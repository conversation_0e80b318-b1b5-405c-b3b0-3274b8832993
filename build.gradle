plugins {
    id 'java'
    id "org.jetbrains.kotlin.jvm" version "1.9.0"
}
allprojects {
    ext.kotlin_version = '1.9.0'
    ext.kotlin_coroutines_version = '1.2.0'
    ext.ktor_version = '2.3.3'
    ext.logback_version = '1.2.9'
    ext.mysql_connector_version = "5.1.49"
    ext.hikari_version = "2.7.8"
    ext.jb_crypt_version = "0.4"
    ext.guava_version = "23.6-jre"
    ext.jmustache_version = "1.14"
    ext.lettuce_version = "5.0.1.RELEASE" //"4.4.2.Final"
    ext.brain_tree_version = "3.25.0"  // worked on "2.74.1"
    ext.apache_commons_version = "3.7"
    ext.google_api_version = "1.25.0"
    ext.google_android_publish_version = "v3-rev129-$google_api_version"
    ext.closure_js_compiler_version = "v20180402"
    ext.okhttp_version = "3.10.0"
    ext.jackson_version = "2.13.4.1"

    ext.aws_library_verion = "1.11.280"

    ext.API_SERVER_JAR_NAME = "kidjoServer"
    ext.BACKGROUND_PROCESS_JAR_NAME = "backgroundProcess"
    ext.exposed_version = "0.35.1"
    ext.flyway_version = "5.2.0"

    repositories {
        mavenCentral()
        jcenter()
    }
    sourceCompatibility = 11
}

group 'kidjo-server'
version '5.0.6-SNAPSHOT'

tasks.register('copyApiServerJarForProductionDeployment', Copy) {
    dependsOn(project(':ApiServer').tasks.getByName('shadowJar'))
    from "${project(':ApiServer').buildDir}/libs/${API_SERVER_JAR_NAME}.jar"
    into "${projectDir}/deploy/production/"
}

tasks.register('copyBackgroundProcessJarForProductionDeployment', Copy) {
    dependsOn(project(':BackgroundProcess').tasks.getByName('shadowJar'))
    from "${project(':BackgroundProcess').buildDir}/libs/${BACKGROUND_PROCESS_JAR_NAME}.jar"
    into "${projectDir}/deploy/production/"
}

tasks.register('copyApiServerJarForStagingDeployment', Copy) {
    dependsOn(project(':ApiServer').tasks.getByName('shadowJar'))
    from "${project(':ApiServer').buildDir}/libs/${API_SERVER_JAR_NAME}.jar"
    into "${projectDir}/deploy/staging/"
}

tasks.register('copyApiServerJarForDevelopDeployment', Copy) {
    dependsOn(project(':ApiServer').tasks.getByName('shadowJar'))
    from "${project(':ApiServer').buildDir}/libs/${API_SERVER_JAR_NAME}.jar"
    into "${projectDir}/deploy/develop/"
}

tasks.register('copyBackgroundProcessJarForStagingDeployment', Copy) {
    dependsOn(project(':BackgroundProcess').tasks.getByName('shadowJar'))
    from "${project(':BackgroundProcess').buildDir}/libs/${BACKGROUND_PROCESS_JAR_NAME}.jar"
    into "${projectDir}/deploy/staging/"
}

tasks.register('copyBackgroundProcessJarForDevelopDeployment', Copy) {
    dependsOn(project(':BackgroundProcess').tasks.getByName('shadowJar'))
    from "${project(':BackgroundProcess').buildDir}/libs/${BACKGROUND_PROCESS_JAR_NAME}.jar"
    into "${projectDir}/deploy/staging/"
}
tasks.register('buildForProduction', Zip) {
    dependsOn(copyApiServerJarForProductionDeployment,
            copyBackgroundProcessJarForProductionDeployment)

    from "${projectDir}/deploy/production/"
    include ".ebextensions/"
    include ".platform/"
    include "${API_SERVER_JAR_NAME}.jar"
    include "${BACKGROUND_PROCESS_JAR_NAME}.jar"
    include "Procfile"
    setArchiveFileName("kidjo-server-prod.zip")
    destinationDirectory = file("${projectDir}/deploy/")
}

tasks.register('buildForStaging', Zip) {
    dependsOn copyApiServerJarForStagingDeployment,copyBackgroundProcessJarForStagingDeployment
    from "${projectDir}/deploy/staging/"
    include ".ebextensions/"
    include ".platform/"
    include "${API_SERVER_JAR_NAME}.jar"
    include "${BACKGROUND_PROCESS_JAR_NAME}.jar"
    include "Procfile"
    setArchiveFileName("kidjo-server-staging.zip")
    destinationDirectory = file("${projectDir}/deploy/")
}

tasks.register('buildForDevelop', Zip) {
    dependsOn copyApiServerJarForDevelopDeployment,copyBackgroundProcessJarForStagingDeployment

    from "${projectDir}/deploy/develop/"
    include ".ebextensions/"
    include ".platform/"
    include "${API_SERVER_JAR_NAME}.jar"
    include "${BACKGROUND_PROCESS_JAR_NAME}.jar"
    include "Procfile"
    setArchiveFileName("kidjo-server-develop.zip")
    destinationDirectory = file("${projectDir}/deploy/")
}

