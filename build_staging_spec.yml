version: 0.2

phases:
  install:
    runtime-versions:
      java: corretto17
    commands:
      - echo Nothing to do in the install phase...
    finally:
      - echo Install Finally

  pre_build:
    commands:
      - echo Nothing to do in the pre_build phase...
    finally:
      - echo Pre BUILD Finally

  build:
    commands:
      - echo Build started on `date`
      - bash ./gradlew clean buildForStaging
    finally:
      - echo BUILD Finally

  post_build:
    commands:
      - echo Build completed on `date`
    finally:
      - echo Post BUILD Finally

artifacts:
  base-directory: deploy/staging/
  files:
    - "**/*"
