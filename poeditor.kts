#! /usr/bin/env kscript

//DEPS com.github.kittinunf.fuel:fuel:2.0.1, com.github.kittinunf.fuel:fuel-coroutines:2.0.1
//DEPS com.fasterxml.jackson.core:jackson-databind:2.9.8, com.fasterxml.jackson.module:jackson-module-kotlin:2.9.8
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.github.kittinunf.fuel.Fuel
import com.github.kittinunf.fuel.core.ResponseDeserializable
import com.github.kittinunf.fuel.coroutines.awaitObject
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.system.exitProcess

val languages = listOf("en", "fr", "es", "pt", "it", "de", "ar")
val poEditorApi = "https://api.poeditor.com/v2/terms/list"
val localPath = "./SharedServer/src/main/resources/languages"
val filePrefix = ""
val apiToken = "90a68bf95ba816a8bf11e7a2a6e156a2"
val projectId = 251627

data class PoEditorResponse(val result: Result)
data class Result(val terms: List<Term>)
data class Term(val term: String, val translation: Translation)
data class Translation(val content: String)

object PoEditorResponseDeserializer : ResponseDeserializable<PoEditorResponse>
{

  override fun deserialize(content: String): PoEditorResponse =
      jacksonObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
          .readValue(content, PoEditorResponse::class.java)

}

suspend fun getTerms(language: String): HashMap<String, String>
{
  val terms = HashMap<String, String>()
  val result = Fuel.post(poEditorApi, listOf("api_token" to apiToken, "id" to projectId, "language" to language))
      .awaitObject(PoEditorResponseDeserializer)
  result.result.terms.forEach { term -> terms[term.term] = term.translation.content }
  return terms
}

if (args.contains("--help") || args.contains("-h"))
{
  println("POEditor string generator: ./poeditor [path] [language-list]")
  println("Examples:")
  println("-- path: './lang/example' or '.' (default: ./SharedServer/src/main/resources/languages)")
  println("-- language-list: 'fr' or 'en,fr,es' (default: en,fr,es,it,de,ar)")

  exitProcess(1)
}

println("Importing POEditor strings for all translations...")

val userPath = args.getOrElse(0) { localPath }
val userLanguages = args.getOrNull(1)?.split(",") ?: languages

runBlocking {
  userLanguages.forEach { language ->
    println("-- Language '$language'")
    val terms = getTerms(language)
    val objectMapper = ObjectMapper()
    objectMapper.writeValue(File("$userPath/$filePrefix$language.json"), terms)
  }
}

println("Done")
