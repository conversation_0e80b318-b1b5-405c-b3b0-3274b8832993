files:
  "/etc/logrotate.d/beanstalk-custom-logs":
    mode: "000644"
    owner: root
    group: root
    content: |
      /var/log/cron
      /var/log/maillog
      /var/log/messages
      /var/log/secure
      /var/log/spooler
      {
          daily
          missingok
          rotate 7
          compress
          dateext
          dateformat -%Y-%m-%d
          sharedscripts
          postrotate
              /usr/bin/systemctl kill -s HUP rsyslog.service >/dev/null 2>&1 || true
              sleep 5
              /usr/bin/aws s3 sync /var/log/ s3://kidjo-internal/logs/eb-accounts-prod/ --exclude "*" --include "*-*.gz" >/dev/null 2>&1 || true
          endscript
      }

container_commands:
  01_install_aws_cli:
    command: "yum install -y aws-cli"