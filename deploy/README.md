# Production deployment setup

You need to add proper kidjo.tv ssl certificate to production/.ebextensions/nginx/ssl folder,
in order for them to be copied into the container.

They are not versioned via git, for security reasons.

## .ebextensions

This folder allows the EC2 instance configuration by Elastic Beanstalk.

* .config: configure several parts of the deployment, we use it to setup the 443 port on the EC2
instance, to enable HTTPS traffic
* nginx/nginx.conf: overwrite the default nginx configuration to enable HTTPS by default
* nginx/ssl/kidjo.tv.crt: the SSL certificate chain
* nginx/ssl/kidjo.tv.key: the SSL private key
