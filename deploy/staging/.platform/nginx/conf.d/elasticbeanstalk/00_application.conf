location / {
     rewrite		          ^/(v.*)$ /api/$1 last;
     proxy_pass          http://127.0.0.1:5000;
     proxy_http_version  1.1;

     proxy_set_header    Connection          $connection_upgrade;
     proxy_set_header    Upgrade             $http_upgrade;
     proxy_set_header    Host                $host;
     proxy_set_header    X-Real-IP           $remote_addr;
     proxy_set_header    X-Forwarded-For     $proxy_add_x_forwarded_for;
}