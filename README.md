# Kidjo

This repository groups up every module used to compute to Kidjo backend servers and the front web application.

## Setup local environment

### Local base setup

A base environment is needed in order for the project to work on a local machine. First you need to install MySQL and Redis:
```
// Install MySQL
brew install mysql@5.7
// Link keg binaries to use this specific version
brew link mysql@5.7 --force
// Start local service right now and with every machine restart
brew services start mysql@5.7

// Install Redis
brew install redis@3.2
// Same commands
brew link redis@3.2 --force
brew services start redis@3.2
```
The default configurations are enough for both tools, the only thing that is recommended is to execute the MySQL configuration:
```
mysql_secure_installation
```
During the setup, you are asked to set a password for the `root` user, and to configure other options.

You need to retrieve the last `kidjo` MySQL database export to run it locally on your machine.

### Docker

The Docker environment is still yet to come...

## Build and start project

### Start locally

To start locally the project, you need to start this command:

```
./gradlew :ApiServer:run_local_mac
or
./gradlew.bat :ApiServer:run_local_mac
```

The `run_local_mac` command can be replaced regarding the `build.gradle` inside the `ApiServer` module. In the `Config.kt` file, you can find the parameters you need to switch the configuration. Beware of the environment configuration specially regarding the `websiteHotReloadDir` that is different from one computer to another.

### Build for production

To build the project, stay at the root of the repository and execute this command:

```
./gradlew clean buildForProduction
or
./gradlew.bat clean buildForProduction
```

This command allows the zip file `kidjo-server-prod.zip` (to upload to AWS ElasticBeanstalk environment) to be created in the `deploy/` folder.
You need to pay attention to specific instructions in the `deploy/production/` folder, specially regarding the current SSL certificates.


### Build for staging

To build the project, stay at the root of the repository and execute this command:

```
./gradlew clean buildForStaging
or
./gradlew.bat clean buildForStaging
```

This command allows the zip file `kidjo-server-staging.zip` (to upload to AWS ElasticBeanstalk environment) to be created in the `deploy/` folder.
You need to pay attention to specific instructions in the `deploy/staging/` folder, specially regarding the current SSL certificates.

