version: "3.8"
services:
  redis:
    container_name: kidjo-tv-server
    image: redis
    restart: unless-stopped
    volumes:
      - cache:/data
    ports:
      - "6379:6379"
  mysql:
    container_name: kidjo
    image: mysql:5.7
    restart: unless-stopped
    volumes:
      - mysql:/var/lib/mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: '`kidjo`'
      MYSQL_USER: 'kidjo'
      MYSQL_PASSWORD: 'root'
      MYSQL_ROOT_PASSWORD: 'root'
    command:
      --sql_mode='' # Added this to match with sql mode in aurora mysql in aws
    networks:
      - mysql

networks:
  mysql:
    driver: bridge

volumes:
  cache:
    driver: local
  mysql:
