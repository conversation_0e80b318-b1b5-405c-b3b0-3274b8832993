image: amazoncorretto:17
pipelines:
  branches:
    master:
      -step:
        name: Build project
        caches:
          -gradle
        script:
          - yum update -y && yum install -y git
          - chmod +x gradlew
          - bash gradlew --version
          - bash gradlew clean build
        after-script:
          - pipe: atlassian/checkstyle-report:0.3.0
  pull-requests:
    '**':
      - step:
          name: Build and Test on Pull Request
          caches:
            - gradle
          script:
            - yum update -y && yum install -y git
            - chmod +x gradlew
            - bash gradlew --version
            - bash gradlew clean build
          after-script:
            - pipe: atlassian/checkstyle-report:0.3.0
