plugins {
    id "org.jetbrains.kotlin.jvm" version "1.9.0" apply true
    id("com.jetbrains.exposed.gradle.plugin") version "0.2.1"
}
group 'SharedServer'
version '1.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlin_coroutines_version"

    implementation "ch.qos.logback:logback-classic:$logback_version"
    implementation "com.samskivert:jmustache:$jmustache_version"
    implementation "com.squareup.okhttp3:okhttp:$okhttp_version"

    implementation group: 'org.mindrot', name: 'jbcrypt', version: jb_crypt_version

    implementation group: 'mysql', name: 'mysql-connector-java', version: mysql_connector_version
    implementation group: 'com.zaxxer', name: 'Hi<PERSON><PERSON>', version: hikari_version
    implementation "io.lettuce:lettuce-core:$lettuce_version"

    implementation "com.braintreepayments.gateway:braintree-java:$brain_tree_version"
    implementation "com.google.api-client:google-api-client:$google_api_version"
    implementation "com.google.apis:google-api-services-androidpublisher:$google_android_publish_version"

    implementation "io.ktor:ktor-server-netty:$ktor_version"
    implementation "io.ktor:ktor-serialization-jackson:$ktor_version"
    implementation "io.ktor:ktor-server-default-headers:$ktor_version"
    implementation "io.ktor:ktor-server-compression:$ktor_version"
    implementation "io.ktor:ktor-server-auth:$ktor_version"
    implementation "io.ktor:ktor-server-auth-jwt:$ktor_version"

    implementation("org.jetbrains.exposed:exposed-core:$exposed_version")
    implementation("org.jetbrains.exposed:exposed-dao:$exposed_version")
    implementation("org.jetbrains.exposed:exposed-jdbc:$exposed_version")
    implementation("org.jetbrains.exposed:exposed-java-time:$exposed_version")

    implementation "com.amazonaws:aws-java-sdk-ses:$aws_library_verion"
    implementation group: 'org.jdom', name: 'jdom2', version: '2.0.6.1'
    implementation group: 'javax.xml.bind', name: 'jaxb-api', version: '2.3.1'
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_version"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_version"
    implementation "com.fasterxml.jackson.module:jackson-module-jaxb-annotations:$jackson_version"
    // Jackson JAXB annotations
    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-xml:$jackson_version"
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.12.37'

    implementation "org.flywaydb:flyway-core:$flyway_version"

    implementation("software.amazon.awssdk:secretsmanager:2.26.18")
    implementation("software.amazon.awssdk:ssm:2.26.18")

}
exposedCodeGeneratorConfig {

    connectionURL = "**************************************************************************"
    user = "root"
    password = "root"
    outputDirectory.set(file("src/main/kotlin/model/entity"))

}

