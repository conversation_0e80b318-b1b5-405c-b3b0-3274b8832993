/*
  migration 27 a table for background tasks log
 */

/*
 UP
 */

CREATE TABLE background_task_logs (
  id SERIAL PRIMARY KEY,
  version VARCHAR(20) NOT NULL ,
  taskType ENUM('subscription') NOT NULL ,
  result ENUM('running','success', 'non_fatal_error', 'fatal_error') NOT NULL DEFAULT 'running',
  shortMessage VARCHAR(255) NOT NULL DEFAULT '',
  longMessage TEXT NOT NULL,

  start DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end DATETIME DEFAULT NULL
);

/*
 DOWN
 */
DROP TABLE background_task_logs;