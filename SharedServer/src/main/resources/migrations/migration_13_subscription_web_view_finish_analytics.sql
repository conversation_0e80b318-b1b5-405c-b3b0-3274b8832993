/*
  migration 12 add a table to hold each time a subscription webview is opened
 */

/*
 UP
 */
CREATE TABLE a_subscription_webview_finish(
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  /*
  User info
   */
  `deviceId` BIGINT(20) UNSIGNED NOT NULL,
  `build` smallint(6) NOT NULL,
  `location` VARCHAR(50) NOT NULL,
  `videoId` BIGINT(20) UNSIGNED NOT NULL,
  `kidId` BIGINT(20) UNSIGNED NOT NULL,
  `storeId` VARCHAR(50) NOT NULL,
  countryId VARCHAR(4) NOT NULL,
  languageId VARCHAR(4) NOT NULL,
  videosWatched INT NOT NULL,
  subscribeViews INT NOT NULL,

  /*
  IAP rule info
   */
  groupIdName varchar(50) NOT NULL,
  `ruleLabel` VARCHAR(30) NOT NULL,
  iap VARCHAR(200) NOT NULL,
  viewId VARCHAR(80) NOT NULL,

  /*
  Purchasing info
   */
  timeSpentInSeconds INT NOT NULL,
  didSubscribe BOOL NOT NULL
);


/*
 DOWN
 */

DROP TABLE a_subscription_webview_finish;