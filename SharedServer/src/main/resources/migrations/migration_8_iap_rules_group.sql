/*
  migration 8 add the rules group plus add the static groups
 */

/*
 UP
 */
CREATE TABLE iap_rules_group(
  id INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
  groupIdName varchar(50) NOT NULL,
  groupName varchar(50) NOT NULL,
  storeId varchar(50) NOT NULL,
  mainCountries BOOL NOT NULL
);

/*
 SET DATA
 */

INSERT INTO iap_rules_group(groupIdName,groupName,storeId,mainCountries) VALUES ('playstore_mc','Android Main Countries','playstore',TRUE); /*Google's Android Main Countries*/
INSERT INTO iap_rules_group(groupIdName,groupName,storeId,mainCountries) VALUES ('playstore_row','Android Rest of World','playstore',FALSE); /*Google's Android Rest of the world*/
INSERT INTO iap_rules_group(groupIdName,groupName,storeId,mainCountries) VALUES ('ios_mc','iOS Main Countries','ios',TRUE); /*iOS Main Countries*/
INSERT INTO iap_rules_group(groupIdName,groupName,storeId,mainCountries) VALUES ('ios_row','iOS Rest of World','ios',FALSE); /*iOS Rest of the world*/

/*
 DOWN
 */

DROP TABLE iap_rules_group;