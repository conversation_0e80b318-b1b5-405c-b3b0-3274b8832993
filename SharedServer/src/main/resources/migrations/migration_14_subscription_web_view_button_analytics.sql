/*
  migration 14 add a table to track whe a user presses the subscribe button
 */

/*
 UP
 */
CREATE TABLE a_subscription_webview_button (
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  /*
  User info
   */
  `deviceId` bigint(20) unsigned NOT NULL,
  `build` smallint(6) NOT NULL,
  `location` VARCHAR(50) NOT NULL,
  `videoId` bigint(20) unsigned NOT NULL,
  `kidId` bigint(20) unsigned NOT NULL,
  `storeId` VARCHAR(50) NOT NULL,
  countryId VARCHAR(4) NOT NULL,
  languageId VARCHAR(4) NOT NULL,
  videosWatched INT NOT NULL,
  subscribeViews INT NOT NULL,

  /*
  IAP rule info
   */
  groupIdName varchar(50) NOT NULL,
  `ruleLabel` VARCHAR(30) NOT NULL,
  iap VARCHAR(200) NOT NULL,
  viewId VARCHAR(80) NOT NULL,

  /*
  Purchasing info
   */
  timeSpentInSeconds INT NOT NULL
);


/*
 DOWN
 */

DROP TABLE a_subscription_webview_button;