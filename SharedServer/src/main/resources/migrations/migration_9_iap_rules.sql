/*
  migration 9 the actual rules for choosing an IAP
 */

/*
 UP
 */
CREATE TABLE iap_rules(
  id INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
  groupId INT UNSIGNED NOT NULL,
  `label` VARCHAR(30) NOT NULL,
  iap VARCHAR(200) NOT NULL,
  viewId VARCHAR(50) NOT NULL DEFAULT 'view_1',
  chance INT UNSIGNED NOT NULL DEFAULT 1,
  allowFreeTrial BOOL NOT NULL DEFAULT TRUE,
  isDefault BOOL NOT NULL DEFAULT FALSE
);

/*
 SET DATA

 adds defaults
 */
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'playstore_mc'),'default_playstore_mc','kidjoplus_1month_7days_5_real',TRUE ,TRUE); /*Google's Android Main Countries*/
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault,chance) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'playstore_mc'),'default_playstore_mc_no_trial','kidjoplus_1month_0days_5_real',FALSE ,TRUE, 2); /*Google's Android Main Countries*/
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'playstore_row'),'default_playstore_row','kidjoplus_1month_7days_2_real',TRUE ,TRUE); /*Google's Android ROW*/
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault,chance) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'playstore_row'),'default_playstore_row_no_trial','kidjoplus_1month_7days_2_real',FALSE ,TRUE, 2); /*Google's Android ROW*/

INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'ios_mc'),'default_ios_mc','ios_monthly_7days_3',TRUE ,TRUE); /*IOS Main Countries*/
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault,chance) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'ios_mc'),'default_ios_mc_no_trial','KidjoPlusMonthSubscription5',FALSE ,TRUE, 2); /*IOS Main Countries*/
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'ios_row'),'default_ios_row','kidjoplus_monthly_family_e',TRUE ,TRUE); /*IOS ROW*/
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault,chance) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'ios_row'),'default_ios_row_no_trial','kidjoplus_monthly_family_e',FALSE ,TRUE, 2); /*IOS ROW*/


/*
 DOWN
 */

DROP TABLE iap_rules;