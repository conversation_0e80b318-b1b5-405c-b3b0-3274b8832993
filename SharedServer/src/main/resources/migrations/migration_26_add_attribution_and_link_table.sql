/*
  migration 26 adds a LinkAttribution table and a Attributed table
  this is to allow us to track attribution on our end
 */

/*
 UP
 */

CREATE TABLE link_attributions (
  id SERIAL PRIMARY KEY,
  name VARCHAR(40) NOT NULL DEFAULT 'NOT SET',
  deepLinkId VARCHAR(40) NOT NULL UNIQUE,
  link VARCHAR(20) NOT NULL UNIQUE,
  isActive BOOLEAN NOT NULL DEFAULT TRUE,
  INDEX (deepLinkId),
  INDEX (link)
);

CREATE TABLE link_attribution_events (
  linkId BIGINT UNSIGNED NOT NULL,
  event ENUM('install','subscribe') NOT NULL ,
  eventValue BIGINT UNSIGNED NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO link_attributions(name, deepLinkId, link) VALUES ('Logicom','logicom1','logicom');
INSERT INTO link_attributions(name, deepLinkId, link) VALUES ('Test','test1','test');
CREATE UNIQUE INDEX event_and_value ON link_attribution_events (event, eventValue);

/*
 DOWN
 */

DROP TABLE link_attributions;
DROP TABLE link_attribution_events;
