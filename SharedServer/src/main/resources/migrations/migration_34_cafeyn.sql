-- Cafeyn incoming request

CREATE TABLE cafeynrequests(
                               id INT NOT NULL AUTO_INCREMENT,
                               requestId varchar(256) NOT NULL,
                               cafeynHubToken varchar(2048) NOT NULL,
                               timeStamp DATETIME,
                               signature varchar(2048),
                               valid boolean,
                               incomingRequestDate DATETIME,
                               PRIMARY KEY(id));

ALTER TABLE cafeynrequests MODIFY COLUMN `timeStamp` varchar(256) NULL;
ALTER TABLE users MODIFY COLUMN oAuthType enum('none','google','facebook','cafeyn') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'none' NOT NULL;
ALTER TABLE subscriptions_root MODIFY COLUMN storeId enum('coupon','ios','playstore','amazon','samsung','kidjo','mondia_media','swisscom','docomo','orange','virgo','huawei','twt','cafeyn') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL;

