/*
  migration 19 add a new subscription table
  a new table for each type of subscription will need to be added
 */

/*
 UP
 */
CREATE TABLE subscriptions_root (
  id SERIAL PRIMARY KEY,
  userId BIGINT UNSIGNED NOT NULL DEFAULT 0,

  isActive BOOL NOT NULL DEFAULT TRUE ,
  isRenewing BOOL NOT NULL DEFAULT TRUE ,
  stillInFreeTrial BOOL NOT NULL,

  paymentType ENUM('native','cc','paypal','google_pay','apple_pay') NOT NULL ,
  paymentId VARCHAR(255) NOT NULL DEFAULT '',
  platformPurchaseId VARCHAR(256) NOT NULL ,
  storeId ENUM('ios','playstore','amazon','samsung','kidjo') NOT NULL ,
  iapId VARCHAR(255) NOT NULL ,
  purchasingSessionId VARCHAR(32) NOT NULL,

  subscriptionToken TEXT NOT NULL ,
  subscriptionTokenHash VARCHAR(64) NOT NULL ,
  accountCouponId BIGINT UNSIGNED NOT NULL DEFAULT 0,

  nextBillDate DATE NOT NULL ,
  lastCheckDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

/*
 DOWN
 */

DROP TABLE subscriptions_root;
