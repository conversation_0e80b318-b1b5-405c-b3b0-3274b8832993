/*
  migration 31 add a table for subscription web view events
  Eventually we will migration all the web view tables to this
  as well as change the open table to a root event table
 */

/*
 UP
 */
CREATE TABLE a_subscription_webview_events(
  sessionId VARCHAR(32) NOT NULL ,
  timeOfEvent TIMESTAMP NOT NULL,
  eventType ENUM('open','went_offline','did_subscribe','not_subscribed') NOT NULL
);

/*
 DOWN
 */

DROP TABLE a_subscription_webview_events;
