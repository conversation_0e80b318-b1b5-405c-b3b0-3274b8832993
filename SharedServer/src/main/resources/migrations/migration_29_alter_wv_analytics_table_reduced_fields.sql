/*
  migration 29 reduces and alter WV analytics fields to where it makes sense

  Also adding bootSession to track a user during an entire boot session

  No longer getting rid of old tables and fields migrating to an event table.

 */

/*
 UP
 */

ALTER TABLE a_subscription_webview_open ADD COLUMN userId BIGINT UNSIGNED NOT NULL DEFAULT 0;
ALTER TABLE a_subscription_webview_open ADD COLUMN bootSession VARCHAR(32) NOT NULL DEFAULT '';
#
# ALTER TABLE a_subscription_webview_finish DROP COLUMN deviceId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN build;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN location;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN videoId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN kidId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN storeId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN countryId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN languageId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN videosWatched;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN subscribeViews;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN groupIdName;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN ruleLabel;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN iap;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN viewId;
# ALTER TABLE a_subscription_webview_finish DROP COLUMN timeSpentInSeconds;
#
#
# ALTER TABLE a_subscription_webview_offline DROP COLUMN deviceId;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN build;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN location;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN videoId;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN kidId;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN storeId;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN countryId;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN languageId;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN videosWatched;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN subscribeViews;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN groupIdName;
# ALTER TABLE a_subscription_webview_offline DROP COLUMN timeSpentInSeconds;
#
#
# DROP TABLE a_subscription_webview_button;



/*
 DOWN
 */

# To drastic of a move no going back here be brave.