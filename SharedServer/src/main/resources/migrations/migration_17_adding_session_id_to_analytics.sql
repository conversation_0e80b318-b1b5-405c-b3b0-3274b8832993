/*
  migration 17 adds a new column to the web view analytics called session id, to track individual sessions
 */

/*
 UP
 */
ALTER TABLE a_subscription_webview_open
  ADD sessionId varchar(32) NOT NULL DEFAULT '';

ALTER TABLE a_subscription_webview_finish
  ADD sessionId varchar(32) NOT NULL DEFAULT '';

ALTER TABLE a_subscription_webview_button
  ADD sessionId varchar(32) NOT NULL DEFAULT '';

ALTER TABLE a_subscription_webview_offline
  ADD sessionId varchar(32) NOT NULL DEFAULT '';

/*
 DOWN
 */

ALTER TABLE a_subscription_webview_open
  DROP COLUMN sessionId;

ALTER TABLE a_subscription_webview_finish
  DROP COLUMN sessionId;

ALTER TABLE a_subscription_webview_button
  DROP COLUMN sessionId;

ALTER TABLE a_subscription_webview_offline
  DROP COLUMN sessionId;