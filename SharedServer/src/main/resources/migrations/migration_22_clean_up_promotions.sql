/*
  migration 23 cleaning up promotions
 */

/*
 UP
 */
UPDATE promotions SET pricePer = 'month' WHERE id = 1;
UPDATE promotions SET price = -1 WHERE price is NULL;
UPDATE promotions SET oldPrice = -1 WHERE oldPrice is NULL;

ALTER TABLE promotions MODIFY COLUMN pricePer VARCHAR(10) DEFAULT 'month' NOT NULL;
ALTER TABLE promotions MODIFY COLUMN price FLOAT NOT NULL DEFAULT -1;
ALTER TABLE promotions MODIFY COLUMN oldPrice FLOAT NOT NULL DEFAULT -1;
/*
 DOWN

 no going back
 */

