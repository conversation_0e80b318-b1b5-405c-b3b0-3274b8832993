/*
  migration 3 creates a relational table between games and folders
 */

/*
 UP
 */
CREATE TABLE game_folders(
  id INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
  folderId INT UNSIGNED NOT NULL,
  gameId INT UNSIGNED NOT NULL,
  `order` smallint(6) NOT NULL DEFAULT '50',
  isPremium tinyint(1) NOT NULL DEFAULT '0',
  difficulty ENUM('easy','medium','hard','veryHard') NOT NULL DEFAULT 'easy',
  isActive SMALLINT NOT NULL DEFAULT '0'
);


/*
 DOWN
 */

DROP TABLE game_folders;