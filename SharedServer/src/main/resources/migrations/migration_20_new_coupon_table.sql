/*
  migration 20 added a new coupon table
 */

/*
 UP
 */
CREATE TABLE account_coupons (
  id SERIAL PRIMARY KEY,
  groupId VARCHAR(50) NOT NULL,
  couponId VARCHAR(30) NOT NULL UNIQUE,
  expireDate DATETIME NOT NULL ,

  duration VARCHAR(6) NOT NULL,
  redeemedTimes MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
  redeemAmount MEDIUMINT UNSIGNED NOT NULL DEFAULT 1,

  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

/*
 DOWN
 */

DROP TABLE account_coupons;

