/*
  migration 30 table to create a one to many relationship between subscription_root and devices
 */

/*
 UP
 */

CREATE TABLE subscription_device_platform_connections (
  rootSubscriptionId BIGINT UNSIGNED NOT NULL ,
  deviceId BIGINT UNSIGNED NOT NULL
);

CREATE UNIQUE INDEX sub_device_index ON subscription_device_platform_connections (rootSubscriptionId, deviceId);
/*
 DOWN
 */

DROP TABLE subscription_device_platform_connections;

