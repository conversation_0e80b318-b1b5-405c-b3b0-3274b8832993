/*
  migration 15 add a rule group to test on
 */

/*
 UP
 */

/*
 SET DATA
 */

INSERT INTO iap_rules_group(groupIdName,groupName,storeId,mainCountries) VALUES ('debug_test','Test rule group','debug_test',TRUE);

INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'debug_test'),'default_debug_test','kidjoplus_1month_7days_5_real',TRUE ,TRUE);
INSERT INTO iap_rules(groupId,`label`,iap,allowFreeTrial,isDefault,chance) VALUES ((SELECT id FROM iap_rules_group WHERE groupIdName = 'debug_test'),'default_debug_test_no_trial','kidjoplus_1month_0days_5_real',FALSE ,TRUE, 2);

/*
 DOWN
 */
