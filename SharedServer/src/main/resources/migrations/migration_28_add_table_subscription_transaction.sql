/*
  migration 29 add table to track individual subscription transactions
 */

/*
 UP
 */

CREATE TABLE subscription_transactions (
  rootSubscriptionId BIGINT UNSIGNED NOT NULL ,
  transactionType ENUM('subscribe','free_trial_end','coupon_end','old_sub_duration_end','renew','cancel_user','cancel_failed_pay', 'cancel_admin'),
  priceValueUSD FLOAT NOT NULL ,
  time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

/*
 DOWN
 */
DROP TABLE subscription_transactions;