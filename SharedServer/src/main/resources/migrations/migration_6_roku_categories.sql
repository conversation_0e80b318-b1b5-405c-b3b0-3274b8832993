/*
  migration 6 data stored to create Roku JSON file, video formats
 */

/*
 UP
 */
CREATE TABLE roku_categories(
  id INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(40) NOT NULL,
  languageId INT UNSIGNED NOT NULL,
  `query` varchar(100) NOT NULL DEFAULT '',
  `orderType` ENUM('manual', 'most_recent', 'chronological', 'most_popular') DEFAULT 'most_popular',
  `jsonOrder` INT UNSIGNED NOT NULL
);

/*
 SET DATA
 */

INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Compilations',1,'compilation',1); /*english*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Compilations',34,'compilation',1); /*french*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Compilaciones',27,'compilation',1); /*spanish*/

INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Nursery Rhymes',1,'rhymes',2); /*english*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Comptines',34,'rhymes',2); /*french*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Canciones infantiles',27,'rhymes',2); /*spanish*/

INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Music',1,'music',3); /*english*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Musique',34,'music',3); /*french*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Música',27,'music',3); /*spanish*/

INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Shows',1,'shows',4); /*english*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Séries',34,'shows',4); /*french*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Muestra TV',27,'shows',4); /*spanish*/

INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Cartoons',1,'cartoons',5); /*english*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Dessins animés',34,'cartoons',5); /*french*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Dibujos animados',27,'cartoons',5); /*spanish*/

INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Education',1,'education',6); /*english*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Éducation',34,'education',6); /*french*/
INSERT INTO roku_categories(`name`,languageId,`query`,`jsonOrder`) VALUES ('Educación',27,'education',6); /*spanish*/

/*
 DOWN
 */

DROP TABLE roku_categories;