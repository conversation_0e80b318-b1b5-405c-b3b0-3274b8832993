/*
  migration 18 add user account table
 */

/*
 UP
 */
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL  DEFAULT '',
  email VARCHAR(336) NOT NULL ,
  email_is_confirmed BOOL NOT NULL DEFAULT FALSE ,
  password VARCHAR(255) NOT NULL DEFAULT 'NOT SET',
  passwordVersion TINYINT UNSIGNED NOT NULL DEFAULT 1,
  authType ENUM('mixed', 'email','oauth') NOT NULL DEFAULT 'email',
  oAuthType ENUM('none','google','facebook') NOT NULL DEFAULT 'none',
  authId VARCHAR(336) NOT NULL DEFAULT 'NOT SET',
  brainTreeId VARCHAR(36) NOT NULL DEFAULT 'NOT SET',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE devices
    ADD COLUMN userId BIGINT UNSIGNED NOT NULL DEFAULT 0;

/*
 DOWN
 */

DROP TABLE users;

ALTER TABLE devices
    DROP COLUMN userId;