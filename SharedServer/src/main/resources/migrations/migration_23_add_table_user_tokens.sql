/*
  migration 23 added a user token table to track login
 */

/*
 UP
 */
CREATE TABLE user_tokens (
  sessionId VARCHAR(60) NOT NULL,
  userId BIGINT UNSIGNED NOT NULL ,
  deviceId BIGINT UNSIGNED NOT NULL DEFAULT 0,
  authToken TEXT NOT NULL ,

  loginType ENUM('direct', 'link') NOT NULL ,
  isValid BOOL NOT NULL DEFAULT TRUE ,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

/*
 DOWN
 */

DROP TABLE user_tokens;
