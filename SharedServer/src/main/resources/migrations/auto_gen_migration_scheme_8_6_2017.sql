-- MySQL dump 10.13  Distrib 5.5.56, for Linux (x86_64)
--
-- Host: kidjo-db-staging-cluster.cluster-cms2z6ssftox.us-east-1.rds.amazonaws.com    Database: kidjo
-- ------------------------------------------------------
-- Server version	5.6.10

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `avatars`
--

DROP TABLE IF EXISTS `avatars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `avatars` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) NOT NULL DEFAULT '',
  `age` tinyint(4) unsigned DEFAULT NULL,
  `ageGroup` enum('0-5','6-10','11-14','14+') DEFAULT NULL,
  `gender` char(1) NOT NULL DEFAULT 'M',
  `imageId` smallint(6) unsigned DEFAULT NULL,
  `parentId` bigint(20) unsigned DEFAULT NULL,
  `deviceId` bigint(20) unsigned DEFAULT NULL,
  `creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `avatars-parentId` (`parentId`),
  KEY `avatar-deviceId` (`deviceId`)
) ENGINE=InnoDB AUTO_INCREMENT=761507 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `avatarsMonthlyStats`
--

DROP TABLE IF EXISTS `avatarsMonthlyStats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `avatarsMonthlyStats` (
  `period` varchar(10) NOT NULL DEFAULT '',
  `avatarId` bigint(20) unsigned NOT NULL,
  `data` text,
  KEY `avatarId` (`avatarId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `avatarsWeeklyStats`
--

DROP TABLE IF EXISTS `avatarsWeeklyStats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `avatarsWeeklyStats` (
  `period` varchar(10) NOT NULL DEFAULT '',
  `avatarId` bigint(20) unsigned NOT NULL,
  `data` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `avatars_folders`
--

DROP TABLE IF EXISTS `avatars_folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `avatars_folders` (
  `avatarId` bigint(20) unsigned NOT NULL,
  `folderId` int(11) unsigned NOT NULL,
  `isActive` tinyint(1) NOT NULL,
  KEY `avatarId` (`avatarId`),
  KEY `folderId` (`folderId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `blogAuthor`
--

DROP TABLE IF EXISTS `blogAuthor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blogAuthor` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `blogPost`
--

DROP TABLE IF EXISTS `blogPost`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blogPost` (
  `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
  `languageId` smallint(6) unsigned NOT NULL,
  `content` text,
  `title` varchar(255) NOT NULL DEFAULT '',
  `teaser` text,
  `slug` varchar(255) NOT NULL DEFAULT '',
  `authorId` smallint(6) unsigned NOT NULL,
  `creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
  `ageMin` smallint(6) unsigned NOT NULL DEFAULT '0',
  `ageMax` smallint(6) unsigned NOT NULL DEFAULT '7',
  `type` enum('entertainement','education') DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1200 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categories_countries`
--

DROP TABLE IF EXISTS `categories_countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories_countries` (
  `categoryId` smallint(6) unsigned NOT NULL,
  `countryId` smallint(6) unsigned NOT NULL,
  `languageId` smallint(6) unsigned DEFAULT NULL,
  KEY `countryId` (`countryId`),
  KEY `categoryId` (`categoryId`,`languageId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categories_languages`
--

DROP TABLE IF EXISTS `categories_languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories_languages` (
  `categoryId` smallint(6) NOT NULL,
  `languageId` smallint(6) NOT NULL,
  `order` smallint(6) NOT NULL DEFAULT '99',
  `name` varchar(50) NOT NULL DEFAULT '',
  `active` tinyint(1) NOT NULL DEFAULT '0',
  `frontEndVisible` tinyint(1) NOT NULL DEFAULT '0',
  `description` text,
  `rating` float DEFAULT NULL,
  `trophy` tinyint(1) NOT NULL DEFAULT '0',
  KEY `categoryId` (`categoryId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categories_skills`
--

DROP TABLE IF EXISTS `categories_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories_skills` (
  `categoryId` smallint(6) unsigned NOT NULL,
  `skillId` smallint(6) unsigned NOT NULL,
  KEY `categorieId` (`categoryId`),
  KEY `skillsId` (`skillId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categories_values`
--

DROP TABLE IF EXISTS `categories_values`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories_values` (
  `categoryId` smallint(6) unsigned NOT NULL,
  `valueId` smallint(6) unsigned NOT NULL,
  KEY `categorieId` (`categoryId`),
  KEY `skillsId` (`valueId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `countries`
--

DROP TABLE IF EXISTS `countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `countries` (
  `id` smallint(6) unsigned NOT NULL,
  `name` varchar(50) NOT NULL DEFAULT '',
  `short` varchar(2) NOT NULL DEFAULT '',
  `active` tinyint(1) NOT NULL DEFAULT '0',
  `backendVisible` tinyint(1) NOT NULL DEFAULT '0',
  `pricingTierMonthly` varchar(5) NOT NULL DEFAULT '5',
  `pricingTierYearly` varchar(5) NOT NULL DEFAULT '45',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupons`
--

DROP TABLE IF EXISTS `coupons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `value` varchar(16) NOT NULL DEFAULT '',
  `deviceId` bigint(20) unsigned DEFAULT NULL,
  `receiver` varchar(50) NOT NULL DEFAULT '',
  `redeemed` tinyint(1) NOT NULL DEFAULT '0',
  `validUntil` timestamp NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT '',
  `validCountLeft` smallint(6) DEFAULT '0',
  `LCValue` varchar(4) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `value` (`value`)
) ENGINE=InnoDB AUTO_INCREMENT=45726 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `coupons_devices`
--

DROP TABLE IF EXISTS `coupons_devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coupons_devices` (
  `couponId` int(11) unsigned NOT NULL,
  `deviceId` bigint(20) unsigned NOT NULL,
  KEY `couponId` (`couponId`),
  KEY `deviceId` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `deviceAttribution`
--

DROP TABLE IF EXISTS `deviceAttribution`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `deviceAttribution` (
  `deviceId` bigint(20) unsigned NOT NULL,
  `trackerToken` varchar(10) NOT NULL DEFAULT '',
  `trackerName` varchar(30) NOT NULL DEFAULT '',
  `adjustId` varchar(40) NOT NULL DEFAULT '',
  PRIMARY KEY (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `manufacturer` varchar(100) DEFAULT NULL,
  `model` varchar(100) DEFAULT NULL,
  `locale` varchar(10) DEFAULT NULL,
  `localTimeZone` smallint(6) DEFAULT NULL COMMENT 'in minutes',
  `languageId` smallint(6) unsigned NOT NULL DEFAULT '1',
  `creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `countryId` smallint(6) unsigned NOT NULL DEFAULT '237',
  `APIVersion` varchar(6) DEFAULT 'v1.0',
  `email` varchar(254) DEFAULT NULL,
  `optIn` tinyint(1) DEFAULT NULL,
  `store` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `APIVersion` (`APIVersion`)
) ENGINE=InnoDB AUTO_INCREMENT=270589 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `favorites`
--

DROP TABLE IF EXISTS `favorites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `favorites` (
  `avatarId` bigint(20) unsigned NOT NULL,
  `videoId` bigint(20) unsigned NOT NULL,
  `favorite` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `favorites-videoId-avatarId` (`videoId`,`avatarId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `folders`
--

DROP TABLE IF EXISTS `folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `languageId` smallint(6) unsigned NOT NULL,
  `title` varchar(50) NOT NULL DEFAULT '',
  `ageMin` smallint(6) NOT NULL DEFAULT '1',
  `ageMax` smallint(6) NOT NULL DEFAULT '7',
  `order` smallint(6) unsigned NOT NULL,
  `isActive` tinyint(1) NOT NULL DEFAULT '0',
  `type` enum('entertainment','education') NOT NULL DEFAULT 'entertainment',
  `description` text,
  `videoCount` smallint(6) unsigned NOT NULL DEFAULT '0',
  `compileCount` smallint(6) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=236 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `folders_countries`
--

DROP TABLE IF EXISTS `folders_countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folders_countries` (
  `folderId` smallint(6) unsigned NOT NULL,
  `countryId` smallint(6) unsigned NOT NULL,
  KEY `folderId` (`folderId`),
  KEY `countryId` (`countryId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `folders_videos`
--

DROP TABLE IF EXISTS `folders_videos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folders_videos` (
  `folderId` smallint(6) unsigned NOT NULL,
  `videoId` int(11) unsigned NOT NULL,
  `order` smallint(6) NOT NULL DEFAULT '50',
  UNIQUE KEY `folderId` (`folderId`,`videoId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `formats`
--

DROP TABLE IF EXISTS `formats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `formats` (
  `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
  `height` int(10) unsigned NOT NULL,
  `codec` varchar(12) NOT NULL DEFAULT '',
  `streamingDelivery` varchar(10) DEFAULT NULL,
  `bitrate` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `iap`
--

DROP TABLE IF EXISTS `iap`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `iap` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `platform` varchar(10) NOT NULL DEFAULT '',
  `name` varchar(40) NOT NULL DEFAULT '',
  `pricing` decimal(10,2) NOT NULL DEFAULT '5.00',
  `freeTrial` int(11) DEFAULT NULL,
  `recurrence` smallint(6) NOT NULL DEFAULT '1',
  `minimunBuild` int(11) NOT NULL DEFAULT '0',
  `family` varchar(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `languages`
--

DROP TABLE IF EXISTS `languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `languages` (
  `id` smallint(6) unsigned NOT NULL,
  `name` varchar(100) NOT NULL DEFAULT '',
  `nativeName` varchar(100) DEFAULT NULL,
  `short` varchar(2) NOT NULL DEFAULT '',
  `active` tinyint(1) NOT NULL DEFAULT '0',
  `backendVisible` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `owners`
--

DROP TABLE IF EXISTS `owners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `owners` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parents`
--

DROP TABLE IF EXISTS `parents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(254) NOT NULL DEFAULT '',
  `hash` varchar(64) NOT NULL DEFAULT '',
  `creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reports` tinyint(1) NOT NULL DEFAULT '1',
  `reportsPeriod` enum('Monthly','Weekly','Daily') NOT NULL DEFAULT 'Weekly',
  `reportsLanguageId` smallint(6) unsigned DEFAULT NULL,
  `ageRestriction` tinyint(1) NOT NULL DEFAULT '0',
  `timeLimit` int(11) NOT NULL DEFAULT '0',
  `isKidjoPlus` tinyint(1) NOT NULL DEFAULT '0',
  `receipt` text,
  `receiptDeviceType` varchar(10) DEFAULT NULL,
  `receiptHash` varchar(64) DEFAULT NULL,
  `unsubscribeToken` varchar(32) NOT NULL DEFAULT '',
  `unsubscribe` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `parents-email` (`email`),
  KEY `receiptHash` (`receiptHash`)
) ENGINE=InnoDB AUTO_INCREMENT=2673 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `parents_languages`
--

DROP TABLE IF EXISTS `parents_languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parents_languages` (
  `languageId` smallint(6) unsigned NOT NULL,
  `parentId` bigint(20) unsigned NOT NULL,
  `selected` tinyint(1) NOT NULL DEFAULT '1',
  UNIQUE KEY `parents_languages-languageId-parentId` (`languageId`,`parentId`),
  KEY `parents_languages-parentId` (`parentId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `promotions`
--

DROP TABLE IF EXISTS `promotions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `promotions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `valid` tinyint(1) NOT NULL DEFAULT '1',
  `deepLinkPromoID` varchar(50) NOT NULL DEFAULT '',
  `iap` varchar(40) NOT NULL DEFAULT 'kidjoplus_monthly_7days_5',
  `iap_ios` varchar(40) DEFAULT NULL,
  `iap_android` varchar(40) DEFAULT NULL,
  `oldest_build_android` smallint(6) NOT NULL DEFAULT '0',
  `oldest_build_ios` smallint(6) NOT NULL DEFAULT '0',
  `freeTrial` int(11) NOT NULL DEFAULT '0',
  `url` varchar(40) NOT NULL DEFAULT '/',
  `title` varchar(40) NOT NULL,
  `adjustTracker` varchar(10) NOT NULL DEFAULT '',
  `price` float DEFAULT NULL,
  `oldPrice` float DEFAULT NULL,
  `pricePer` varchar(10) DEFAULT 'month',
  `redirect` varchar(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `skills`
--

DROP TABLE IF EXISTS `skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `skills` (
  `id` smallint(6) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptionClick`
--

DROP TABLE IF EXISTS `subscriptionClick`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptionClick` (
  `deviceId` bigint(20) unsigned NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `iap` varchar(40) NOT NULL DEFAULT '',
  `pageVersion` smallint(6) NOT NULL DEFAULT '1',
  `location` varchar(40) NOT NULL DEFAULT '',
  `build` smallint(6) DEFAULT NULL,
  KEY `deviceId` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptionReceipts`
--

DROP TABLE IF EXISTS `subscriptionReceipts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptionReceipts` (
  `receiptId` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `deviceId` bigint(20) unsigned NOT NULL,
  `receipt` text NOT NULL,
  `receiptDeviceType` varchar(10) NOT NULL DEFAULT '',
  `iapId` int(11) NOT NULL,
  `promotionId` int(11) DEFAULT NULL,
  `creationDate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `hash` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`receiptId`),
  UNIQUE KEY `deviceId` (`deviceId`,`hash`)
) ENGINE=InnoDB AUTO_INCREMENT=7091 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptionTransactions`
--

DROP TABLE IF EXISTS `subscriptionTransactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptionTransactions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `receiptId` int(11) unsigned NOT NULL,
  `transaction_id` bigint(20) unsigned NOT NULL,
  `date` timestamp NULL DEFAULT NULL,
  `expires` timestamp NULL DEFAULT NULL,
  `is_trial` tinyint(1) NOT NULL DEFAULT '1',
  `iapId` int(11) DEFAULT NULL,
  `transactionNb` smallint(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`)
) ENGINE=InnoDB AUTO_INCREMENT=419299 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptionView`
--

DROP TABLE IF EXISTS `subscriptionView`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptionView` (
  `deviceId` bigint(20) unsigned NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `iap` varchar(40) NOT NULL DEFAULT '',
  `pageVersion` smallint(6) NOT NULL DEFAULT '1',
  `location` varchar(40) NOT NULL DEFAULT '',
  `build` smallint(6) DEFAULT NULL,
  KEY `deviceId` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptions` (
  `deviceId` bigint(20) unsigned NOT NULL,
  `receipt` text NOT NULL,
  `receiptDeviceType` varchar(10) NOT NULL DEFAULT '',
  `iap` varchar(40) DEFAULT NULL,
  `creationDate` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `subscriptionsTransactionAndroid`
--

DROP TABLE IF EXISTS `subscriptionsTransactionAndroid`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptionsTransactionAndroid` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `orderNumber` varchar(50) NOT NULL DEFAULT '',
  `date` timestamp NOT NULL,
  `expires` timestamp NOT NULL,
  `isTrial` tinyint(1) NOT NULL,
  `iapId` int(11) NOT NULL,
  `transactionNb` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `orderNumber` (`orderNumber`)
) ENGINE=InnoDB AUTO_INCREMENT=3137 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `valuess`
--

DROP TABLE IF EXISTS `valuess`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `valuess` (
  `id` smallint(6) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos`
--

DROP TABLE IF EXISTS `videos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(50) NOT NULL DEFAULT '',
  `subtitle` varchar(100) DEFAULT '',
  `isActive` tinyint(1) NOT NULL DEFAULT '0',
  `languageId` smallint(6) unsigned NOT NULL,
  `creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `youtubeId` varchar(11) DEFAULT NULL,
  `ageMin` smallint(6) NOT NULL,
  `ageMax` smallint(6) NOT NULL,
  `contentTypeId` smallint(6) unsigned DEFAULT NULL,
  `contentBrandId` smallint(6) unsigned NOT NULL,
  `thumbnailTime` float NOT NULL DEFAULT '10',
  `ownerId` smallint(6) DEFAULT NULL,
  `isPremium` tinyint(1) NOT NULL DEFAULT '0',
  `multiLanguage` tinyint(1) NOT NULL DEFAULT '0',
  `masterId` int(10) unsigned DEFAULT NULL,
  `quality` smallint(6) unsigned NOT NULL DEFAULT '2',
  `compile` tinyint(1) NOT NULL DEFAULT '0',
  `duration` smallint(6) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `videos-languageId` (`languageId`),
  KEY `videos-contentType` (`contentTypeId`),
  KEY `isActive` (`isActive`),
  KEY `isPremium` (`isPremium`),
  FULLTEXT KEY `videos-title-subtitle` (`title`,`subtitle`)
) ENGINE=InnoDB AUTO_INCREMENT=3063 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videosIncoming`
--

DROP TABLE IF EXISTS `videosIncoming`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videosIncoming` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `source` varchar(20) NOT NULL DEFAULT '',
  `videoId` int(11) unsigned DEFAULT NULL,
  `youtubeId` varchar(11) DEFAULT NULL,
  `creation` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `title` varchar(200) DEFAULT '',
  `formatId` smallint(6) NOT NULL,
  `status` enum('0.ToDownload','0.1.Downloading','1.Downloaded','2.Saved','3.Imported','4.Cut','5.Transcoded','5.1.TranscodedHLS','5.1.1.TranscodingHLS','5.2TranscodedDASH','5.2.1.TranscodingDASH','8.Error','9.Skipped') NOT NULL DEFAULT '1.Downloaded',
  `skipReason` text,
  `youtubeLink` varchar(200) DEFAULT NULL,
  `youtubeRetries` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3257 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videosPlayed`
--

DROP TABLE IF EXISTS `videosPlayed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videosPlayed` (
  `videoId` int(11) unsigned NOT NULL,
  `deviceId` bigint(20) unsigned NOT NULL,
  `avatarId` bigint(20) unsigned DEFAULT NULL,
  `length` smallint(6) DEFAULT NULL,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `deviceId` (`deviceId`),
  KEY `videoId` (`videoId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos_categories`
--

DROP TABLE IF EXISTS `videos_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos_categories` (
  `videoId` int(11) unsigned NOT NULL,
  `categoryId` smallint(6) unsigned NOT NULL,
  KEY `videoId` (`videoId`),
  KEY `categoryId` (`categoryId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos_countries`
--

DROP TABLE IF EXISTS `videos_countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos_countries` (
  `videoId` int(11) unsigned NOT NULL,
  `countryId` smallint(6) unsigned NOT NULL DEFAULT '0',
  KEY `videoId` (`videoId`),
  KEY `countryId` (`countryId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos_formats`
--

DROP TABLE IF EXISTS `videos_formats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos_formats` (
  `videoId` int(11) unsigned NOT NULL,
  `formatId` int(11) unsigned NOT NULL,
  `fileSize` bigint(20) unsigned NOT NULL DEFAULT '0',
  `yuv420p` smallint(1) DEFAULT NULL,
  KEY `videos_formats-videoId` (`videoId`),
  KEY `videos_formats-formatId` (`formatId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos_skills`
--

DROP TABLE IF EXISTS `videos_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos_skills` (
  `videoId` int(11) unsigned NOT NULL,
  `skillId` smallint(6) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `videos_values`
--

DROP TABLE IF EXISTS `videos_values`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `videos_values` (
  `videoId` int(11) unsigned NOT NULL,
  `valueId` smallint(6) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2017-09-06 20:58:57
