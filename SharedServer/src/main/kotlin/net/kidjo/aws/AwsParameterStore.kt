package net.kidjo.aws

import software.amazon.awssdk.services.ssm.SsmClient
import software.amazon.awssdk.services.ssm.model.GetParameterRequest

object AwsParameterStore {

    private val ssmClient = SsmClient.builder().build()

    fun getParameter(name: String): String {
        val request = GetParameterRequest.builder()
            .name(name)
            .withDecryption(true)
            .build()
        val response = ssmClient.getParameter(request)
        return response.parameter().value()
    }

    fun getCafeynLoginUrl(environment: String): String {
        val parameterName = "/kidjo-server/${environment.lowercase()}/cafeyn/login-url"
        return getParameter(parameterName)
    }
}