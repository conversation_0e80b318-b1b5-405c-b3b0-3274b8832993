package net.kidjo.server.shared.tools

data class LogTracker(private val name: String) {
    private var createdTimeStamp = System.currentTimeMillis()
    private var lastLogTimeStamp: Long = 0L
    private var lastLogCounter = 0

    fun log() {
        log("event")
    }

    fun log(eventName: String) {
        val timeStamp = System.currentTimeMillis()
        val timeSinceCreate = createdTimeStamp - timeStamp
        val timeSinceLastLog = lastLogTimeStamp - timeStamp
        lastLogTimeStamp = timeStamp
        lastLogCounter++
        val message =
            "$name - $eventName - Counter: $lastLogCounter - sinceLastLog: $timeSinceLastLog - sinceCreated: $timeSinceCreate"
        println(message)
    }
}