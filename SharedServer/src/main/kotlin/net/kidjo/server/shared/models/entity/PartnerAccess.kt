package net.kidjo.server.shared.models.entity

import net.kidjo.server.shared.models.enums.PartnerAuthType
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object PartnerAccess : Table("partner_access") {
    val id = integer("id").autoIncrement()
    val partnerName=varchar("partner_name",255)
    val apiKey=varchar("api_key",255)
    val isKeyExpired=bool("is_key_expired")
    val isActive = bool("active")
    val authType: Column<PartnerAuthType> = customEnumeration("authType", null, fromDb = {
        PartnerAuthType.fromRaw(it as String)
    }, toDb = {
        it.name
    })
    val username = varchar("username", 255)
    val password = varchar("password", 255)
    val updated_at = datetime("updated_at")
    val created_at = datetime("created_at")

    val additional1 = varchar("additional1", 255)
}
