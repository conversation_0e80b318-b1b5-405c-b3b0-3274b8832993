package net.kidjo.server.shared.tools

import io.ktor.server.application.*
import io.ktor.server.plugins.*
import net.kidjo.common.models.Language
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.cachedatabase.CacheDatabase
import net.kidjo.server.shared.cachedatabase.getCountryInfoByIp
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.user_getCountryIdFromShortName
import net.kidjo.server.shared.extensions.getString
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.TimeUnit

class CountryCodeByGoogle(validator: Validator,
                          languageCache: LanguageCache,
                          databaseController: DatabaseController,
                          cacheDatabase: CacheDatabase) {

    internal val logger = LoggerFactory.getLogger("CountryCodeByGoogle")

    private val validator = validator
    private val databaseController = databaseController
    private val languageCache = languageCache
    private val cacheDatabase = cacheDatabase

    fun getGoogleCountryResponse(call: ApplicationCall): String {
        val query = call.parameters
        val queryIp = query.getString("ip")
        logger.info("MANUALLY ADDED DEVICE IP FROM QUERY PARAMETER: ${queryIp}")

        val ip: String? = when {
            queryIp != "" -> {
                queryIp
            }
            call.request.origin.remoteHost == "0:0:0:0:0:0:0:1" -> {
                logger.info("INFO MOBILE LOCALHOST IP")
                logger.info("mobile IP is: ${call.request.origin.remoteHost}")
                null

            }
            call.request.origin.remoteHost == "127.0.0.1" -> {
                logger.info("INFO BROWSER LOCALHOST IP")
                logger.info("browser IP is: ${call.request.origin.remoteHost}")
                null
            }
            call.request.origin.remoteHost == "*************" -> {
                logger.info("INFO APPLE TV LOCALHOST IP")
                logger.info("apple tv IP is: ${call.request.origin.remoteHost}")
                null
            }
            else -> {
                logger.info("INFO WHAT IS REAL DEVICE IP")
                logger.info("real device IP is: ${call.request.origin.remoteHost}")
                call.request.origin.remoteHost
            }
        }

        // Use cached version with automatic API fallback
        val countryInfo = cacheDatabase.getCountryInfoByIp(ip)
        logger.info("Country info retrieved for IP $ip: $countryInfo")

        return countryInfo
    }

    fun getCountryCodeByResponse(responseBody: String): String {
        var geoPluginJSON: JSONObject? = null

        if (responseBody.isNotEmpty()) {
            val geoPluginResponse = responseBody
            geoPluginJSON = JSONObject(geoPluginResponse)
        }
        return if (geoPluginJSON != null) geoPluginJSON.getString("geoplugin_countryCode") else "en"
    }

    fun getCountryIdByIP(call: ApplicationCall): Int {
        val responseBody = getGoogleCountryResponse(call)
        val countryCode = getCountryCodeByResponse(responseBody)
        val countryId: Int = databaseController.user_getCountryIdFromShortName(countryCode)
        logger.info("GET COUNTRY CODE BY GOOGLE")
        logger.info("countryCode: ${countryCode}")
        return countryId
    }

    fun getMapCountryIdLanguageIdByGoogle(deviceLocate: String, call: ApplicationCall): Map<String, Int> {

        val responseBody = getGoogleCountryResponse(call)
        val countryCode = getCountryCodeByResponse(responseBody)
        logger.info("GET COUNTRY CODE BY GOOGLE")
        logger.info("countryCode: ${countryCode}")

        //locale
        var locale = validator.createLocale(deviceLocate)

        if (locale.country.contains("@")) {
            locale = Locale(locale.language, locale.country.substring(0, locale.country.indexOf("@")))
        }


        var countryId: Int = databaseController.user_getCountryIdFromShortName(if (countryCode != "") countryCode else locale.country)
        if (countryId < 1 || countryId > 249) {
            countryId = 237 // US
            logger.error("ISSUE FINDING COUNTRY CODE BY GOOGLE")
            logger.error("locale country: ${locale.country}")
            logger.error("countryId: $countryId")
            logger.error("countryCode: $countryCode")
        }

        var usingLanguageId: Int = languageCache.getLanguageIdWithShortName(if (countryCode == "TN") "fr" else locale.language)
        logger.error("GET LANGUAGE ID BY LOCALE")
        logger.info("locale.language: ${locale.language}")

        if (usingLanguageId == Language.NO_ID) {
            usingLanguageId = Language.ID_ENGLISH

            logger.error("ISSUE FINDING LANGUAGE ID BY LOCALE")
            logger.error("usingLanguageId: ${usingLanguageId}")
            logger.error("locale.language: ${locale.language}")
        }

        return mapOf("countryId" to countryId, "languageId" to usingLanguageId)
    }

}
