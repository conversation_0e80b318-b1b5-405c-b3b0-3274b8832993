package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.getinfo
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response.DVGetSubscriptionInfoDTO
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.buildRequestHeaders
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.generatedBearerToken
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.DVSubscriptionApi
import okhttp3.Response

suspend fun DVSubscriptionApi.getSubscriptionInfoDVPASSApiDTO(
    subscriptionIdString: String,
    baseUrl: String,
    login: String,
    secret: String,
    claim:  Pair<String, Long>? = null,
    validity: Long
): DVGetSubscriptionInfoDTO? {

    var responseGET: Response? = null
    try {
        logger.info("Getting Subscription Info | getSubscriptionInfoDVPASSAPI ")

        val subInfoURL= "https://$baseUrl/service/subscriptions/$subscriptionIdString?include=user,offer,package"
        logger.info("Getting Subscription Info URL API | subInfoURL ")

        val bearerToken = generatedBearerToken(
            sub = baseUrl,
            iss = login,
            secret = secret,
            claim = claim,
            validity = validity
        )

        val requestGET =
            buildRequestHeaders(
                url = subInfoURL,
                token = bearerToken
            ).
            get().
            build()

        responseGET = withContext(Dispatchers.IO) {
            httpClient.
            newCall(requestGET).
            execute()
        }
        logger.info("Getting Subscription Info URL API | subInfoURL ")

        when (responseGET.code()) {
            HttpStatusCode.OK.value -> {
                logger.info("Subscription Info | main response 200 OK")

                val bodyString = withContext(Dispatchers.IO) { responseGET.body()!!.string() }
                logger.info("DV sub Info Response BODY: $bodyString")
                val responseInfo =
                    bodyString.let {
                        jacksonObjectMapper().
                        configure(
                            DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                            false
                        ).
                        readValue<DVGetSubscriptionInfoDTO>(it)
                    }
                logger.info("DV sub Info Jackson Mapper request: $responseInfo")
                when (responseInfo.code){
                    3400, 4401-> {
                        responseInfo.data?.status = SubscriptionStatusType.CANCELLED.raw
                        logger.info("Subscription already cancelled | response 3400, 4401 : $responseInfo ")
                        responseGET.close()

                        return responseInfo
                    }
                    3401,4402 -> {
                        responseInfo.data?.status = SubscriptionStatusType.EXPIRED.raw
                        logger.info("Subscription already expired | response 3401,4402: $responseInfo ")
                        responseGET.close()

                        return responseInfo
                    }
                    4400 -> {
                        responseInfo.data?.status = SubscriptionStatusType.TERMINATED.raw
                        logger.info("Subscription unknown | response 4400: $responseInfo ")
                        responseGET.close()

                        return responseInfo
                    }
                }
                logger.info("Successful Getting Subscription Info | response OK: ${responseInfo.code} ")

                responseGET.close()
                return responseInfo
            }
            else -> {
                logger.error("Unsuccessful Getting Subscription Info | response: ${responseGET.code()} ")

                responseGET.close()
                return null
            }
        }
    } catch (e: Exception) {
        logger.error("Error DV subscriptionInfoResponse, ${e.localizedMessage}")
        throw e
    } finally {
        responseGET?.close()
    }
}
