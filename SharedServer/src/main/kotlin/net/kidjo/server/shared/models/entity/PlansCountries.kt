package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table

object PlansCountries : Table("plans_country") {
    val id = integer("id").autoIncrement()

    @OptIn(ExperimentalUnsignedTypes::class)
    val countryId = (uinteger("countryId") references Countries.id)
    val price = decimal("price",4,2)
    val plan = (integer("planId") references Plans.id)
    val currency = varchar("currency", 10)
    val currencySymbol = varchar("currencySymbol", 10)
}
