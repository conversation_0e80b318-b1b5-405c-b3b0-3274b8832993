package net.kidjo.server.shared.tools

import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubMethodTypeModel
import net.kidjo.server.shared.models.SubscriptionRoot
import org.apache.http.util.TextUtils
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import java.security.SecureRandom
import java.text.Normalizer
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

class Utility {

    private  val REGEX_UNACCENT = "[\\p{InCombiningDiacriticalMarks}]"
    private  val REGEX_REMOVE_CHARS = "[$&=?@#'<>^*()%!]"
    private  val REGEX_REPLACE_CHARS = "[^\\w\\d\\s\\p{IsCyrillic}]"
    fun getFormattedString(title: String): String {
        val removedChars = Regex(REGEX_REMOVE_CHARS).replace(title.unAccent(), "").trim()
        return Regex(REGEX_REPLACE_CHARS).replace(removedChars, "_").replace(" ", "_")
    }
    private fun CharSequence.unAccent(): String {
        val temp = Normalizer.normalize(this, Normalizer.Form.NFD)
        return Regex(REGEX_UNACCENT).replace(temp, "")
    }
    fun randomString(length: Int, chars: CharArray, random: Random = SecureRandom()): String {
        if (length == 0 || chars.isEmpty()) return ""

        val charLength = chars.size
        val builder = StringBuilder(length)
        for (i in 0 until length) {
            builder.append(chars[random.nextInt(charLength)])
        }
        return builder.toString()
    }


    private val regex = Regex("[0-9]+", RegexOption.IGNORE_CASE)
    //Braintree works only with Subscription.DurationUnit MONTH and DAY
    fun getDayMonthFromFreeTrialISO8601Code(ISO8601Code: String): Long {
        if (ISO8601Code == "0") return 0

        try {
            val value = regex.find(ISO8601Code)?.value?.toLong() ?: return 0
            return when (ISO8601Code.last()) {
                'D' -> value
                'M' -> value
                else -> 0
            }
        } catch (e: NumberFormatException) {
            return 0
        }
    }

    fun createNextBillingDateFromCoupon(duration: String): LocalDateTime {

        var nextBillingDateTime: LocalDateTime = LocalDateTime.now()
        val freeTrialTime = getDayMonthFromFreeTrialISO8601Code(duration)

        nextBillingDateTime = if (duration.contains("D")) {
            nextBillingDateTime.plusDays(freeTrialTime)
        } else {
            nextBillingDateTime.plusMonths(freeTrialTime)
        }
        return nextBillingDateTime
    }

    fun getCountryCodeFromPhoneNumber(phoneNumber: String): String? {
        val phoneNumberUtil = PhoneNumberUtil.getInstance()
        var countryCode: String

        try {
            val phone = phoneNumberUtil.parse(phoneNumber, Phonenumber.PhoneNumber.CountryCodeSource.UNSPECIFIED.name)
            countryCode = phoneNumberUtil.getRegionCodeForNumber(phone)

        } catch (e: NumberParseException) {
            System.err.println("NumberParseException was thrown: $e")
            return null
        }

        return countryCode
    }

    fun isValidMobileNumber(msisdn: String?): Boolean {
        if (TextUtils.isEmpty(msisdn)) return false
        val phoneNumberUtil = PhoneNumberUtil.getInstance()
        try {
            val phoneNumber = phoneNumberUtil.parse(msisdn, Locale.getDefault().country)
            return phoneNumberUtil.isValidNumber(phoneNumber)
        } catch (e: Exception) {
            System.err.println("NumberParseException was thrown: $e")
        }
        return false
    }

    fun getNationalNumber(msisdn: String?): Long {
        if (TextUtils.isEmpty(msisdn)) return -1
        val phoneNumberUtil = PhoneNumberUtil.getInstance()
        try {
            val phoneNumber = phoneNumberUtil.parse(msisdn, Locale.getDefault().country)

            return phoneNumber.nationalNumber
        } catch (e: Exception) {
            System.err.println("NumberParseException was thrown: $e")
        }
        return -1
    }

//    fun sortStringList(inputList: ArrayList<SubMethodTypeModel>): List<String> {
//        return inputList.sorted();
//    }

    fun getSubMethods(stores: String): List<SubMethodTypeModel> {

        val subscriptionMethods = arrayListOf<SubMethodTypeModel>()

        stores.split(",").toTypedArray().forEach { store: String ->
            when (store) {

                Device.StorePlatform.FREE_ACCESS_COUPON.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.FREE_ACCESS_COUPON.label, store))
                }
                Device.StorePlatform.IOS.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.IOS.label, store))
                }
                Device.StorePlatform.PLAYSTORE.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.PLAYSTORE.label, store))
                }
                Device.StorePlatform.AMAZON.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.AMAZON.label, store))
                }
                Device.StorePlatform.DOCOMO.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.DOCOMO.label, store))
                }
                Device.StorePlatform.HUAWEI.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.HUAWEI.label, store))
                }
                Device.StorePlatform.KIDJO_BRAINTREE.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.KIDJO_BRAINTREE.label, store))
                }
                Device.StorePlatform.ORANGE.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.ORANGE.label, store))
                }
                Device.StorePlatform.VIRGO.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.VIRGO.label, store))
                }
                Device.StorePlatform.SAMSUNG.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.SAMSUNG.label, store))
                }
                Device.StorePlatform.CAFEYN.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.CAFEYN.label, store))
                }
                Device.StorePlatform.TWT.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.TWT.label, store))
                }
                Device.StorePlatform.JIO.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.JIO.label, store))
                }
                Device.StorePlatform.SWISSCOM.raw -> {
                    subscriptionMethods.add(SubMethodTypeModel(store, Device.StorePlatform.SWISSCOM.label, store))
                }
                Device.StorePlatform.SWITCH.raw ->{
                    subscriptionMethods.add(SubMethodTypeModel(store,Device.StorePlatform.SWITCH.label,store))
                }
                Device.StorePlatform.MONDIA.raw ->{
                    subscriptionMethods.add(SubMethodTypeModel(store,Device.StorePlatform.MONDIA.label,store))
                }
                Device.StorePlatform.SALT.raw ->{
                    subscriptionMethods.add(SubMethodTypeModel(store,Device.StorePlatform.SALT.label,store))
                }

                else -> { }
            }

        }

        return subscriptionMethods.sortedWith(compareBy({it.name}))
    }
    fun isBillingDateExpired(nextBillDate: LocalDateTime): Boolean = LocalDateTime.now().isAfter(nextBillDate)
    fun stringToLocalDateTime(date: String): LocalDateTime? {
        return ZonedDateTime.parse(date, DateTimeFormatter.ISO_DATE_TIME)
            .toLocalDateTime()
    }

    fun buildUrl(baseUrl: String, path: String, queryParams: Map<String, String>): String {
        val urlBuilder = StringBuilder(baseUrl)

        if (!baseUrl.endsWith("/")) {
            urlBuilder.append('/')
        }

        urlBuilder.append(path)

        if (queryParams.isNotEmpty()) {
            urlBuilder.append('?')
            queryParams.forEach { (key, value) ->
                urlBuilder.append(key).append('=').append(value).append('&')
            }
            urlBuilder.deleteCharAt(urlBuilder.length - 1)
        }

        return urlBuilder.toString()
    }

    //Created method for native users for registration purpose
    fun attachNativeSubscription(checkSubscription: SubscriptionRoot, id: Long): Boolean {
        return transaction {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.
            update({ net.kidjo.server.shared.models.entity.SubscriptionRoot.id eq checkSubscription.id }) {
                it[userId] = id.toInt()
            } > 0 // returns true if at least one row was updated
        }
    }
}
