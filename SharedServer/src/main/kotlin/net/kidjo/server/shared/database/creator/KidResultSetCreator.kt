package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.Kid
import java.sql.ResultSet


fun ResultSetObjectCreator.kid(resultSet: ResultSet): Kid {
    val serverId = resultSet.getLong("id")
    val id = encryptionController.encodeNormalId(serverId)
    val name = resultSet.getString("name")
    val age = resultSet.getInt("age")
    val serverParentAccountId = resultSet.getLong("deviceId") //todo migrate to account

    return Kid(serverId,id,name,age,serverParentAccountId)
}