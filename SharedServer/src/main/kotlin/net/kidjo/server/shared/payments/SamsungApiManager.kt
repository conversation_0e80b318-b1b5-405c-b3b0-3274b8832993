package net.kidjo.server.shared.payments

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule
import io.ktor.server.application.*
import net.kidjo.server.shared.extensions.SamsungSubscriptionErrors
import net.kidjo.server.shared.extensions.respondCustomError
import net.kidjo.server.shared.models.SamsungSubscriptionCheckException
import okhttp3.*
import org.slf4j.LoggerFactory
import java.io.StringReader
import javax.xml.bind.annotation.XmlAccessType
import javax.xml.bind.annotation.XmlAccessorType
import javax.xml.bind.annotation.XmlElement
import javax.xml.bind.annotation.XmlRootElement
import javax.xml.stream.XMLInputFactory
import javax.xml.stream.XMLStreamReader

class SamsungApiManager {
    private val okHttpClient = OkHttpClient()
    private val xmlMediaType = MediaType.parse("text/xml; charset=utf-8")
    private val logger = LoggerFactory.getLogger("SamsungApiManager")
    private val SAMSUNG_STATUS_API = "https://iap.samsungapps.com/iap/ws/RTCService?wsdl"
    private val SECRET = "000000018206"
    private val TARGET_ELEMENT = "output"

    lateinit var subscriptionStatusResponse: SubscriptionStatusResponse
    lateinit var token: Token

    /**
     * Get Auto Recurring Subscription from SAMSUNG API
     */
    suspend fun getSubscriptionStatus(
        token: String,
        call: ApplicationCall? = null
    ): SubscriptionStatusResponse {

        val statusRequest = subscriptionStatusRequest(token)
        val response = soapResponse(statusRequest)
        logger.debug("Samsung verify subscription api Response Code -> ${response.code()}")
        if (response.code() == 10) {
            call?.respondCustomError(SamsungSubscriptionErrors.ERROR_MISSING_PARAMETER, "A required parameter is missing.")
        }
        if (response.code() == 15) {
            call?.respondCustomError(SamsungSubscriptionErrors.ERROR_INVALID_PARAMETER_VALUE, "A parameter value is invalid.")
        }
        if (response.code() == -1) {
            call?.respondCustomError(SamsungSubscriptionErrors.UNKNOWN_ERROR, "The service token is invalid.")
        }
        if (response.code() == 301) {
            call?.respondCustomError(SamsungSubscriptionErrors.ERROR_EXPIRED_SERVICE_TOKEN, "The service token has expired.")
        }
        if (response.code() == 300) {
            call?.respondCustomError(SamsungSubscriptionErrors.ERROR_INVALID_SECRET, "A server error has occurred.")
        }
        if (response.code() == 500) {
            call?.respondCustomError(SamsungSubscriptionErrors.SERVER_ERROR, "Problem finding samsung subscrition.")
        }

        return getXmlStatusParser(response)
    }

    /**
     * Get Auto Recurring Subscription from SAMSUNG API
     */
    fun getSubscriptionStatus(token: String): SubscriptionStatusResponse {

        val statusRequest = subscriptionStatusRequest(token)
        val response = soapResponse(statusRequest)

        if (response.code() == 10) {
            throw SamsungSubscriptionCheckException("10 A required parameter is missing.")
        }
        if (response.code() == 15) {
            throw SamsungSubscriptionCheckException("15 A parameter value is invalid.")
        }
        if (response.code() == -1) {
            throw SamsungSubscriptionCheckException("-1 The service token is invalid.")
        }
        if (response.code() == 301) {
            throw SamsungSubscriptionCheckException("301 The service token has expired.")
        }
        if (response.code() == 300) {
            throw SamsungSubscriptionCheckException("300 A server error has occurred.")
        }
        if (response.code() == 500) {
            throw SamsungSubscriptionCheckException("500 Problem finding samsung subscrition.")
        }

        return getXmlStatusParser(response)
    }

    private fun getXmlStatusParser(response: Response): SubscriptionStatusResponse {
        val xmlMapper = XmlMapper()
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)

        val f = XMLInputFactory.newFactory()
        val sr = f.createXMLStreamReader(response.body()?.string()?.let { StringReader(it) })

        while (sr.hasNext()) {
            val type = sr.next()
            if (type == XMLStreamReader.START_ELEMENT && TARGET_ELEMENT == sr.localName) {
                subscriptionStatusResponse = xmlMapper.readValue(sr, SubscriptionStatusResponse::class.java)

            }
        }
        return subscriptionStatusResponse
    }

    private fun getToken(): String {
        val tokenRequest = subscriptionTokenRequest()
        val response = soapResponse(tokenRequest)
        logger.debug("Samsung serviceToken api response -> ${response.code()}")
        val token=getXmlTokenParser(response);
        return token.output
    }

    private fun getXmlTokenParser(response: Response): Token {
        val xmlMapper = XmlMapper()
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        xmlMapper.registerModule(JaxbAnnotationModule())
        val f = XMLInputFactory.newFactory()
        val responseAsString=response.body()?.string()?.let { StringReader(it) }
        val sr = f.createXMLStreamReader(responseAsString)
        while (sr.hasNext()) {
            val type = sr.next()
            if (type == XMLStreamReader.START_ELEMENT && TARGET_ELEMENT == sr.localName) {
                sr.next()
                token=Token(sr.text)
                break;
            }
        }
        return token
    }

    private fun soapResponse(soapBuilder: StringBuilder): Response {
        val body = RequestBody.create(xmlMediaType, soapBuilder.toString())
        val request: Request = Request.Builder()
            .url(SAMSUNG_STATUS_API)
            .post(body)
            .addHeader("content-type", "text/xml")
            .build()

        return okHttpClient.newCall(request).execute()
    }

    private fun subscriptionTokenRequest(): StringBuilder {
        val soapBuilder = StringBuilder()
        soapBuilder.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ws=\"http://ws.iap.samsung.com/\">\n")
            .append("<soapenv:Header/>\n")
            .append("<soapenv:Body>\n")
            .append("<ws:createServiceToken>\n")
            .append("<secret>$SECRET</secret>\n")
            .append("</ws:createServiceToken>\n")
            .append("</soapenv:Body>\n")
            .append("</soapenv:Envelope>\n")
        return soapBuilder
    }

    private fun subscriptionStatusRequest(token: String): StringBuilder {
        val serviceToken=getToken();
        val soapBuilder = StringBuilder()
        soapBuilder.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ws=\"http://ws.iap.samsung.com/\">\n")
            .append("<soapenv:Header/>\n")
            .append("<soapenv:Body>\n")
            .append("<ws:getSubscriptionStatus>\n")
            .append("<purchaseID>$token</purchaseID>\n")
            .append("<serviceToken>${serviceToken}</serviceToken>\n")
            .append("</ws:getSubscriptionStatus>\n")
            .append("</soapenv:Body>\n")
            .append("</soapenv:Envelope>\n")
        return soapBuilder
    }
}

@XmlRootElement(name = "output", namespace = "http://ws.iap.samsung.com")
@XmlAccessorType(XmlAccessType.FIELD)
data class Token(
    @XmlElement(name = "output", namespace = "http://ws.iap.samsung.com")
    val output: String = ""
)

@XmlRootElement(name = "output", namespace = "http://ws.iap.samsung.com")
@XmlAccessorType(XmlAccessType.FIELD)
data class SubscriptionStatusResponse(
    @XmlElement(name = "supplyPrice", namespace = "http://ws.iap.samsung.com")
    val supplyPrice: String = "",
    @XmlElement(name = "subscriptionPurchaseDate", namespace = "http://ws.iap.samsung.com")
    val subscriptionPurchaseDate: String = "",
    @XmlElement(name = "freeTrial", namespace = "http://ws.iap.samsung.com")
    val freeTrial: String = "",
    @XmlElement(name = "subscriptionEndDate", namespace = "http://ws.iap.samsung.com")
    val subscriptionEndDate: String = "",
    @XmlElement(name = "totalNumberOfRenewalPayment", namespace = "http://ws.iap.samsung.com")
    val totalNumberOfRenewalPayment: String = "",
    @XmlElement(name = "localCurrencyCode", namespace = "http://ws.iap.samsung.com")
    val localCurrencyCode: String = "",
    @XmlElement(name = "totalNumberOfTieredPayment", namespace = "http://ws.iap.samsung.com")
    val totalNumberOfTieredPayment: String = "",
    @XmlElement(name = "currentPaymentPlan", namespace = "http://ws.iap.samsung.com")
    val currentPaymentPlan: String = "",
    @XmlElement(name = "subscriptionFirstPurchaseID", namespace = "http://ws.iap.samsung.com")
    val subscriptionFirstPurchaseID: String = "",
    @XmlElement(name = "itemID", namespace = "http://ws.iap.samsung.com")
    val itemID: String = "",
    @XmlElement(name = "cancelSubscriptionDate", namespace = "http://ws.iap.samsung.com")
    val cancelSubscriptionDate: String = "",
    @XmlElement(name = "cancelSubscriptionReason", namespace = "http://ws.iap.samsung.com")
    val cancelSubscriptionReason: String = "",
    @XmlElement(name = "subscriptionType", namespace = "http://ws.iap.samsung.com")
    val subscriptionType: String = "",
    @XmlElement(name = "subscriptionFirstPaymentDate", namespace = "http://ws.iap.samsung.com")
    val subscriptionFirstPaymentDate: String = "",
    @XmlElement(name = "countryCode", namespace = "http://ws.iap.samsung.com")
    val countryCode: String = "",
    @XmlElement(name = "subscriptionStatus", namespace = "http://ws.iap.samsung.com")
    val subscriptionStatus: String = "",
    @XmlElement(name = "realMode", namespace = "http://ws.iap.samsung.com")
    val realMode: String = "",
    @XmlElement(name = "localPrice", namespace = "http://ws.iap.samsung.com")
    val localPrice: String = "",
    @XmlElement(name = "latestOrderId", namespace = "http://ws.iap.samsung.com")
    val latestOrderId: String = ""
)
