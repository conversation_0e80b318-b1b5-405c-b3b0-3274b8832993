package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table

object FoldersLanguages : Table("folders_languages") {

    val id: Column<Int> = integer("id").autoIncrement()

    val folderId: Column<Int> = integer("folderid")

    val languageId: Column<Int> = integer("languageid")
    override val primaryKey: PrimaryKey?
        get() = PrimaryKey(id)
}
