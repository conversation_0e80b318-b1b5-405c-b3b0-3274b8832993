package net.kidjo.server.shared.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class CafeynUserInfo(
    @JsonProperty("email")
    var email: String? = "",
    @JsonProperty("user_id")
    var userId: Int = 0,
    @JsonProperty("partner_id")
    var partnerId: Int = 0,
    @JsonProperty("partner_name")
    var partnerName: String = "",
    @JsonProperty("service_name")
    var serviceName: String = "",
    @JsonProperty("event_type")
    var eventType: String = ""
)
