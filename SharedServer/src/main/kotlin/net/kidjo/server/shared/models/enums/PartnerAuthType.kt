package net.kidjo.server.shared.models.enums

enum class PartnerAuthType(val raw: String) {

    OAUTH("oauth"),
    BASICAUTH("basicAuth"),
    APIKEY("apiKey")

    ;

    companion object {
        fun fromRaw(raw: String): PartnerAuthType {
            return when (raw) {
                OAUTH.raw -> OAUTH
                APIKEY.raw -> APIKEY
                BASICAUTH.raw -> BASICAUTH
                else -> APIKEY
            }
        }
    }
}
