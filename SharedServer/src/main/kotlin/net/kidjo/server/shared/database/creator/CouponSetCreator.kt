package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.AccountCouponPartnerModel
import net.kidjo.server.shared.models.AccountCouponProductModel
import net.kidjo.server.shared.models.AccountCouponTypeModel
import java.sql.ResultSet


fun ResultSetObjectCreator.couponPartners(resultSet: ResultSet): AccountCouponPartnerModel {
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val description = resultSet.getString("description")

    return AccountCouponPartnerModel(id, name, description)
}

fun ResultSetObjectCreator.couponProducts(resultSet: ResultSet): AccountCouponProductModel{
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val description = resultSet.getString("description")

    return AccountCouponProductModel(id, name, description)
}
fun ResultSetObjectCreator.couponTypes(resultSet: ResultSet): AccountCouponTypeModel {
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val description = resultSet.getString("description")

    return AccountCouponTypeModel(id, name, description)
}