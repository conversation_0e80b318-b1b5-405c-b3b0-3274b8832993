package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.linkAttribution
import net.kidjo.server.shared.models.LinkAttribution
import net.kidjo.server.shared.models.LinkAttributionEvent

fun DatabaseController.attribution_addAdjust(deviceId: Long, trackerToken: String, trackerName: String, adid: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO deviceAttribution (deviceId,trackerToken,trackerName,adjustId) VALUES (?,?,?,?) ON DUPLICATE KEY UPDATE trackerToken = ?,trackerName = ?,adjustId = ?")
    statement.setLong(1,deviceId)
    statement.setString(2,trackerToken)
    statement.setString(3,trackerName)
    statement.setString(4,adid)
    statement.setString(5,trackerToken)
    statement.setString(6,trackerName)
    statement.setString(7,adid)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.attribution_addLinkAttributionEvent(linkId: Long, event: LinkAttributionEvent.EventType, eventValue: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO link_attribution_events(linkId, event, eventValue) VALUES (?,?,?)")
    statement.setLong(1,linkId)
    statement.setString(2,event.raw)
    statement.setLong(3,eventValue)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}
fun DatabaseController.attribution_getLinkAttributionByDeeplink(deepLinkId: String): LinkAttribution? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM link_attributions WHERE deepLinkId = ?")
    statement.setString(1,deepLinkId)

    val results = statement.executeQuery()

    val attribution: LinkAttribution?
    if (results.next()) attribution = objectCreator.linkAttribution(results)
    else attribution = null

    statement.close()
    connection.close()
    return attribution
}
fun DatabaseController.attribution_getLinkAttributionByURLlink(link: String): LinkAttribution? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM link_attributions WHERE link = ?")
    statement.setString(1,link)

    val results = statement.executeQuery()

    val attribution: LinkAttribution?
    if (results.next()) attribution = objectCreator.linkAttribution(results)
    else attribution = null

    statement.close()
    connection.close()
    return attribution
}