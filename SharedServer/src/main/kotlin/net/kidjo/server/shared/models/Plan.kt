package net.kidjo.server.shared.models

import java.math.BigDecimal

data class Plan(
    val status: Boolean,
    val product: String,
    val iap: String,
    val price: BigDecimal,
    val currency: String? = "USD",
    val currencySymbol: String? = "$",
    val productLabel: String,
    val id: Int?,
    val addOns: Set<Addon>? = null,
    val subscribed: Boolean = false,
)

data class Addon(
    val addonName: String,
    val price: BigDecimal,
)
