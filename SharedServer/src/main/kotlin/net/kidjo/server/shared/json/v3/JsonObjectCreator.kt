package net.kidjo.server.shared.json.v3

import net.kidjo.common.models.Language
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.cache.LanguageJsonCreator
import net.kidjo.server.shared.tools.Config
import org.json.JSONObject

class JsonObjectCreatorV3(config: Config,
                          internal val encryptionController: EncryptionController): LanguageJsonCreator {
    val CARD_VIDEO_ID = "id"

    //=========
    //LanguageJsonCreator
    //=========
    override fun language_toJSON(language: Language, isActive: Boolean) : JSONObject {
        val json = JSONObject()
        json.put("id",language.id)
        json.put("name",language.nativeName)
        json.put("selected",isActive)
        return json
    }

}