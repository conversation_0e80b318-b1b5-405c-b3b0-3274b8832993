package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByPaymentStateId
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.AppStoreBillingResponse
import net.kidjo.server.shared.payments.AppleApiManager
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient
import java.time.LocalDateTime

class PaymentAppStoreManager(
        config: Config,
        httpClient: OkHttpClient,
        encryptionController: EncryptionController,
        databaseController: DatabaseController,
        iapManager: IAPManager,
        private val appleApiManager: AppleApiManager,
        emailManager: EmailManager,
        languageCache: LanguageCache,
        userManager: UserManager

) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {


        override suspend fun start(
                userId: Long, inAppPurchase: InAppPurchase,
                token: String, purchasingSession: String,
                subscriptionType: SubscriptionRoot.SubscriptionType,
                forceUpdate: Boolean,
                subscriptionId: String?,
                orderId: String?,
                call: ApplicationCall
        )
        {

        when (inAppPurchase.store) {
            Device.StorePlatform.IOS -> {
                var checkSubscription: SubscriptionRoot? = null
                val appStoreBillingResponse = getAppStoreReceipt(token, inAppPurchase, subscriptionType.raw, call)
                var nextBillDate = LocalDateTime.now()
                if (appStoreBillingResponse != null) {
                    val transactionId = appStoreBillingResponse.latestReceipt[0]!!.transaction_id.toString()
                    checkSubscription = databaseController.subscription_getByPaymentStateId(transactionId)
                    nextBillDate =
                            appStoreBillingResponse.latestReceipt[0]!!.expires_date_ms.epochMilliToLocalDateTime()
                }
                if (!isBillingDateExpired(nextBillDate)) {
                    if (checkSubscription == null) {

                        return createSubscription(
                                inAppPurchase, token,
                                call, userId, purchasingSession, subscriptionType, subscriptionId, orderId
                        )
                    } else {
                        if (checkSubscription.userId != 0L && userId != checkSubscription.userId) {
                            return call.respondConflict(
                                    SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                                    "This subscription already belongs to another account. Please login."
                            )
                        }
                        val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                        if (isExpired) {
                            return updateSubscription(
                                    inAppPurchase, "",
                                    call, userId, checkSubscription
                            )
                        } else {
                            if(checkSubscription.userId==0L && checkSubscription.id.toInt()!=0){
                                var utility= Utility()
                                if(utility.attachNativeSubscription(checkSubscription,userId)){
                                    logger.info("Subscription is attached succesfully !!!")
                                    val userDetails=userManager.getUser(checkSubscription.userId.toString())
                                    emailManager.sendConfirmationEmail(userDetails.email, userDetails.name, languageCache.get(call))
                                    return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${userId} ")

                                }
                            }
                            return call.respondNoContent()
                        }
                    }
                }
            }
            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun updateSubscription(
            inAppPurchase: InAppPurchase,
            token: String, call: ApplicationCall, userId: Long,
            subscriptionRoot: SubscriptionRoot
    ) {
        val appStoreBillingResponse =
                getAppStoreReceipt(token, inAppPurchase, subscriptionRoot.subscriptionType.raw, call)
        if (appStoreBillingResponse != null) {
            val subscriptionRootUpdate: SubscriptionRootUpdate
            val autoRenewingStatus = appStoreBillingResponse.pending_renewal_info[0].auto_renew_status
            var autoRenewing = false
            if (autoRenewingStatus >= 0) {
                autoRenewing = true
            }

            val nextBillDate = appStoreBillingResponse.latestReceipt[0]!!.expires_date_ms.epochMilliToLocalDateTime()
            val orderId = appStoreBillingResponse.latestReceipt[0]!!.web_order_line_item_id.toString()
            val transactionId = appStoreBillingResponse.latestReceipt[0]!!.transaction_id.toString()
            val freeTrial: Boolean
            val priceToLog: Float

            if (appStoreBillingResponse.latestReceipt[0]!!.is_trial_period) {
                freeTrial = true
                priceToLog = 0.0f
            } else {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }

            subscriptionRootUpdate = SubscriptionRootUpdate(
                    userId, autoRenewing, nextBillDate, orderId,
                    freeTrial, priceToLog, subscriptionRoot.id, transactionId
            )

            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)
        } else {
            logger.error("Error update AppStore Subscription")
            return call.respondBadRequest("Not found AppStore Subscription for updating")
        }
    }


    override suspend fun createSubscription(
            inAppPurchase: InAppPurchase, token: String, call: ApplicationCall,
            userId: Long, purchasingSession: String,
            subscriptionType: SubscriptionRoot.SubscriptionType,
            subscriptionId: String?,
            orderId: String?

    ) {
        val appStoreBillingResponse = getAppStoreReceipt(token, inAppPurchase, subscriptionType.raw, call)
        if (appStoreBillingResponse != null) {
            val subscriptionRootInsert: SubscriptionRootInsert
            val autoRenewingStatus = appStoreBillingResponse.pending_renewal_info[0].auto_renew_status
            var autoRenewing = false
            if (autoRenewingStatus >= 0) {
                autoRenewing = true
            }

            val nextBillDate = appStoreBillingResponse.latestReceipt[0]!!.expires_date_ms.epochMilliToLocalDateTime()
            val orderId = appStoreBillingResponse.latestReceipt[0]!!.web_order_line_item_id.toString()
            val freeTrial: Boolean
            val priceToLog: Float
            var isTestReceipt = false

            if (appStoreBillingResponse.environment == "Sandbox") {
                isTestReceipt = true
            }

            if (appStoreBillingResponse.latestReceipt[0]!!.is_trial_period) {
                freeTrial = true
                priceToLog = 0.0f
            } else {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }
            val transactionId = appStoreBillingResponse.latestReceipt[0]!!.transaction_id.toString()

            subscriptionRootInsert = SubscriptionRootInsert(
                    userId,
                    0L,
                    freeTrial,
                    priceToLog,
                    SubscriptionRoot.PaymentType.NATIVE,
                    subscriptionType,
                    inAppPurchase.store.raw,
                    orderId,
                    inAppPurchase.store,
                    transactionId,
                    inAppPurchase.id,
                    purchasingSession,
                    token,
                    encryptionController.sha256Hash(token),
                    0L,
                    autoRenewing,
                    nextBillDate,
                    isTestReceipt
            )

            insert(subscriptionRootInsert, call)
        } else {
            logger.error("Error crate AppStore Subscription")
            return call.respondBadRequest("Not found AppStore Subscription for updating")
        }
    }

    suspend fun getAppStoreReceipt(
            token: String, inAppPurchase: InAppPurchase,
            subscriptionType: String, call: ApplicationCall
    ): AppStoreBillingResponse? {
        return try {
            appleApiManager.getAppStoreReceipt(token, config, subscriptionType, call)
        } catch (e: Exception) {
            logger.error("cant find AppStor subscription of $inAppPurchase, token: ${token.take(10)}")
            return null
        }
    }



}
