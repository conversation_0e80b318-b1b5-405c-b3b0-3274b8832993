package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.AmazonApiManager
import net.kidjo.server.shared.payments.AmazonSubscriptionStatus
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient

class PaymentAmazonStoreManager(
    config: Config,
    httpClient: OkHttpClient,
    encryptionController: EncryptionController,
    databaseController: DatabaseController,
    iapManager: IAPManager,
    private val amazoniApiManager: AmazonApiManager,
    emailManager: EmailManager,
    languageCache: Language<PERSON>ache,
    userManager: UserManager

) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {


    override suspend fun start(
            userId: Long, inAppPurchase: InAppPurchase,
            token: String, purchasingSession: String,
            subscriptionType: SubscriptionRoot.SubscriptionType,
            forceUpdate: Boolean,
            amazonUserId: String?,
            orderId: String?,
            call: ApplicationCall
    ) {

        when (inAppPurchase.store) {
            Device.StorePlatform.AMAZON -> {
                var checkSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(token)
                if (checkSubscription == null) {
                    return createSubscription(
                            inAppPurchase, token,
                            call, userId, purchasingSession, subscriptionType,
                        amazonUserId, orderId)
                } else {
                    if (checkSubscription?.userId != 0L && userId != checkSubscription?.userId) {
                        return call.respondConflict(
                                SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                                "This subscription already belongs to another account. Please login."
                        )
                    }
                    val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                    if (isExpired) {
                        return updateSubscription(
                                inAppPurchase, token,
                                call, userId, checkSubscription
                        )
                    } else {
                        if(checkSubscription.userId==0L && checkSubscription.id.toInt()!=0){
                            var utility= Utility()
                            if(utility.attachNativeSubscription(checkSubscription,userId)){
                                val userDetails=userManager.getUser(checkSubscription.userId.toString())
                                emailManager.sendConfirmationEmail(userDetails.email, userDetails.name, languageCache.get(call))
                                logger.info("Subscription is attached succesfully !!!")
                                return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${userId} ")

                            }
                        }
                        return call.respondNoContent()
                    }
                }
            }
            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun createSubscription(
            inAppPurchase: InAppPurchase,
            token: String, call:
            ApplicationCall,
            userId: Long,
            purchasingSession: String,
            subscriptionType: SubscriptionRoot.SubscriptionType,
            amazonUserId: String?,
            orderId: String?
    ) {
        var isTestReceipt = false

        val amazonSubscriptionPurchase = getAmazonReceipt(
                token, amazonUserId!!, call)
        if (amazonSubscriptionPurchase != null) {

            if(amazonSubscriptionPurchase.testTransaction) {
                isTestReceipt = true
            }
            // platformPurchaseId is amazonUserId
            val subscriptionRootInsert = SubscriptionRootInsert(
                    userId, 0L, true, 0.0f,
                    SubscriptionRoot.PaymentType.NATIVE, subscriptionType, inAppPurchase.store.raw,
                    amazonUserId, inAppPurchase.store, amazonSubscriptionPurchase.productId,
                    inAppPurchase.id, purchasingSession, token, encryptionController.sha256Hash(token),
                    0L, amazonSubscriptionPurchase.autoRenewing,
                    amazonSubscriptionPurchase.renewalDate.epochMilliToLocalDateTime(), isTestReceipt
            )

            insert(subscriptionRootInsert, call)
        }
    }

    override suspend fun updateSubscription(
            inAppPurchase: InAppPurchase,
            token: String,
            call: ApplicationCall,
            userId: Long,
            subscriptionRoot: SubscriptionRoot
    ) {
        // // subscriptionRoot.platformPurchaseId is amazonUserId
        val amazonSubscriptionPurchase = getAmazonReceipt(token, subscriptionRoot.platformPurchaseId, call)

        if (amazonSubscriptionPurchase != null) {

            val subscriptionRootUpdate = SubscriptionRootUpdate(
                    userId, amazonSubscriptionPurchase.autoRenewing,
                    amazonSubscriptionPurchase.renewalDate.epochMilliToLocalDateTime(),
                    subscriptionRoot.platformPurchaseId, true, 0.0f,
                    subscriptionRoot.id, amazonSubscriptionPurchase.productId
            )

            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)
        }
    }

    private suspend fun getAmazonReceipt(receiptId: String, amazonUserId: String,
                                         call: ApplicationCall
    ): AmazonSubscriptionStatus? {
        return try {
            amazoniApiManager.getSubscriptionStatus(receiptId, amazonUserId, call)
        } catch (e: Exception) {
            logger.error("cant find Amazon subscription token: ${receiptId.take(10)}")
            return null
        }
    }
}
