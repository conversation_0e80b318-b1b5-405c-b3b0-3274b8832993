package net.kidjo.server.shared.tools.payments

import com.braintreegateway.*
import com.braintreegateway.Subscription as BrainTreeSubscription
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.*
import net.kidjo.server.shared.payments.publichers.v5.cafeyn.CafeynApiManager
import net.kidjo.server.shared.tools.*
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

private const val MAX_NAME_LENGTH_BRAIN_TREE = 250

class PaymentManager(
    internal val config: Config,
    internal val httpClient: OkHttpClient,
    internal val encryptionController: EncryptionController,
    internal val databaseController: DatabaseController,
    internal val iapManager: IAPManager,
    internal val playStoreApiManager: PlayStoreApiManager,
    internal val appleApiManager: AppleApiManager,
    internal val orangeApiManager: OrangeApiManager,
    internal val samsungApiManager: SamsungApiManager,
    internal val huaweiApiManager: HuaweiApiManager,
    internal val amazonApiManager: AmazonApiManager,
    internal val jioApiManager: JioApiManager,
    internal val cafeynApiManager: CafeynApiManager
) {

    internal val logger = LoggerFactory.getLogger("PaymentManager")

    internal val brainTreeGateway = BraintreeGateway(
        if (config.brainTree_useSandbox) Environment.SANDBOX else Environment.PRODUCTION,
        config.brainTree_merchandId,
        config.brainTree_publicKey,
        config.brainTree_privateKey
    )


    fun setPaymentDefaultMethod(
        user: User,
        nameOnPaymentMethod: String,
        nonce: String,
        deviceData: String
    ): PaymentError {
        val addPaymentRequest = PaymentMethodRequest()
            .paymentMethodNonce(nonce)
            .customerId(user.brainTreeId)
            .options()
            .failOnDuplicatePaymentMethod(false)
            .verifyCard(true)
            .verificationAmount("0.00")
            .makeDefault(true)
            .done()
        if (deviceData != "") addPaymentRequest.deviceData(deviceData)

        if (nameOnPaymentMethod != "") {
            val size = kotlin.math.min(MAX_NAME_LENGTH_BRAIN_TREE, nameOnPaymentMethod.length)
            val name = nameOnPaymentMethod.substring(0, size)
            addPaymentRequest.cardholderName(name)
        }


        val addPayment = brainTreeGateway.paymentMethod().create(addPaymentRequest)

        if (!addPayment.isSuccess) {
            //todo handle errors
            if (addPayment.creditCardVerification != null) {
                println(addPayment.creditCardVerification.processorResponseText)
            } else {
                println(addPayment.message)
            }

            return PaymentError.ISSUE_ADDING_CARD
        }
        return PaymentError.NONE
    }

    fun startSubscription(
        device: Device, user: User, sessionId: String, inAppPurchase: InAppPurchase, forceNoFreeTrial: Boolean,
        subscriptionStartDate: LocalDateTime?, withCoupon: AccountCoupon?
    ): PaymentError {
        if (inAppPurchase.store != Device.StorePlatform.KIDJO_BRAINTREE) {
            logger.error("Error trying to start a subscription with non brain tree")
            return PaymentError.ISSUE_CHARGING_CARD
        }

        val customer = brainTreeGateway.customer().find(user.brainTreeId)
        if (customer == null) {
            logger.error("could not find brain tree customer for user: $user")
            return PaymentError.ISSUE_CHARGING_CARD
        }

        val paymentMethod = customer.defaultPaymentMethod
        val subscriptionRequest = SubscriptionRequest()
            .paymentMethodToken(paymentMethod.token)
            .planId(inAppPurchase.id)

        val usingCouponId = withCoupon?.id ?: 0L
        var willHaveFreeTrial = !inAppPurchase.noFreeTrial
        var priceToBeChargedImmediately: Float = 0.0f //don't track any revenue unless theres no free trial

        if (withCoupon != null) {
            willHaveFreeTrial = true
            subscriptionRequest.trialDuration(withCoupon.freeTrialValue)
            subscriptionRequest.trialDurationUnit(if (withCoupon.freeTrialIsInMonths) BrainTreeSubscription.DurationUnit.MONTH else BrainTreeSubscription.DurationUnit.DAY)
        } else if (forceNoFreeTrial) { //only remove free trial if theres no coupon
            willHaveFreeTrial = false
            priceToBeChargedImmediately = inAppPurchase.price.toFloat()
            subscriptionRequest.trialDuration(0)
        }

        if (subscriptionStartDate != null) {
            subscriptionRequest.firstBillingDate(GregorianCalendar.from(subscriptionStartDate.atZone(ZoneId.systemDefault())))
            priceToBeChargedImmediately = 0.0f
        }

        val results = brainTreeGateway.subscription().create(subscriptionRequest)

        if (!results.isSuccess) {
            //todo handle errors
            logger.error(results.message)
            return PaymentError.ISSUE_CHARGING_CARD
        }

        val subscription = results.target

        val paymentType: SubscriptionRoot.PaymentType
        val paymentId: String
        if (paymentMethod is PayPalAccount) {
            paymentType = SubscriptionRoot.PaymentType.PAY_PAL
            paymentId = paymentMethod.email
        } else if (paymentMethod is CreditCard) {
            paymentType = SubscriptionRoot.PaymentType.CREDIT_CARD
            paymentId = paymentMethod.last4
        } else {
            logger.error("Didn't catch payment method ${paymentMethod} for user $user")
            paymentType = SubscriptionRoot.PaymentType.CREDIT_CARD
            paymentId = "TODO"
        }

        if (withCoupon != null) {
            databaseController.accountCoupon_consumeCoupon(usingCouponId)
        }
        val nextBillingDate =
            LocalDateTime.ofInstant(
                subscription.nextBillingDate.toInstant(),
                subscription.nextBillingDate.timeZone.toZoneId()
            )
        val subscriptionRootInsert = SubscriptionRootInsert(
            user.getLongId(), device.serverId,
            willHaveFreeTrial, priceToBeChargedImmediately, paymentType, SubscriptionRoot.SubscriptionType.KIDJO_TV,
            paymentId, subscription.id, Device.StorePlatform.KIDJO_BRAINTREE, null, inAppPurchase.id,
            sessionId, subscription.id, encryptionController.sha256Hash(subscription.id),
            usingCouponId, true, nextBillingDate, !config.env.isLive
        )

        startTrackingSubscription(subscriptionRootInsert, "")

        return PaymentError.NONE
    }

    fun cancelSubscription(user: User, subscription: SubscriptionRoot, cancelingFromUser: Boolean): Boolean {
        if (user.getLongId() != subscription.userId || !subscription.isRenewing) return false

        var success = false

        logger.info("Unsubscribe subscription from ${subscription.storeId} - ${subscription.subscriptionToken}")

        if (subscription.storeId == Device.StorePlatform.KIDJO_BRAINTREE) {
            val results = brainTreeGateway.subscription().cancel(subscription.subscriptionToken)
            if (results.isSuccess) {
                success = databaseController.subscription_cancel(subscription.id)
                if (!success) {
                    logger.error("Database Error, setting this subscription to stop renewing: $subscription")
                }
                val transactionType =
                    if (cancelingFromUser) SubscriptionTransaction.TransactionType.CANCEL_USER else SubscriptionTransaction.TransactionType.CANCEL_ADMIN
                success = databaseController.subscriptionTransaction_add(subscription.id, transactionType, 0f)
            } else {
                logger.error(results.message)
            }
        } else if (subscription.storeId == Device.StorePlatform.PLAYSTORE) {
            logger.error("Tried canceling subscription, but could not: $subscription")
        } else if (subscription.storeId == Device.StorePlatform.MONDIA_MEDIA) {
            val client = OkHttpClient()

            val operator = MondiaOperators.GetByOpName(subscription.operatorName) ?: MondiaOperators.ORANGE_TUNISIA

            val url: String
            if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) {
                url = operator.productionBackendAPI
            } else if (config.env == Config.Env.STAGING) {
                url = operator.stagingBackendAPI
            } else {
                // Not staging or production
                url = operator.productionBackendAPI
            }

            val request: Request = Request.Builder()
                .url("${url}subservice/unsubscribe?subid=${subscription.subscriptionToken}&operatorId=${operator.id}")
                .header(
                    "username",
                    if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_USERNAME_PRODUCTION else MONDIA_API_USERNAME_STAGING
                )
                .header(
                    "password",
                    if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_PASSWORD_PRODUCTION else MONDIA_API_PASSWORD_STAGING
                )
                .build()

            // Debug
            logger.info("Unsubscribe mondia user ${subscription.subscriptionToken}")
            logger.info(request.toString())

            val response: Response = client.newCall(request).execute()

            if (response.isSuccessful) {
                databaseController.subscription_cancel(subscription.id)
                success = true
            } else {
                logger.error("Failed to cancel subscription for Mondia: ${subscription}")
            }

            response.close()
        } else {
            logger.error("Tried canceling subscription, but could not: $subscription")
        }

        return success
    }

    fun createCustomer(user: User): Boolean {
        val customerRequest = CustomerRequest()
        if (user.email != "") customerRequest.email(user.email)

        val results = brainTreeGateway.customer().create(customerRequest)
        if (results.isSuccess) {
            databaseController.user_setBrainUserId(user.getLongId(), results.target.id)
            return true
        }
        return false
    }

    fun createClientToken(user: User): String {
        val clientTokenRequest = ClientTokenRequest()
            .customerId(user.brainTreeId)
        println("user: " + user)
        println("clientTokenRequest: " + clientTokenRequest)
        return brainTreeGateway.clientToken().generate(clientTokenRequest) ?: "xxToken"
    }

    enum class PaymentError(val noError: Boolean) {
        NONE(true), ISSUE_ADDING_CARD(false), ISSUE_CHARGING_CARD(false)
    }
}
