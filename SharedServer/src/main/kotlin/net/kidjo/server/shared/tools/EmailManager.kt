package net.kidjo.server.shared.tools

import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.regions.Regions
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder
import com.amazonaws.services.simpleemail.model.*
import io.ktor.util.*
import net.kidjo.common.models.Language
import net.kidjo.server.shared.cachedatabase.CacheDatabase
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.User
import org.slf4j.LoggerFactory

class EmailManager(
    config: Config,
    private val utility: Utility,
    val cacheDatabase: CacheDatabase,
    val validator: Validator,
    loader: ClassLoader = Thread.currentThread().contextClassLoader
) {

    private val logger = LoggerFactory.getLogger("EmailManager")

    private val awsSesFromEmail = config.awsSesFromEmail
    private val sesClient: AmazonSimpleEmailService = createSesClient(config)

    private fun createSesClient(config: Config): AmazonSimpleEmailService {
        val builder = AmazonSimpleEmailServiceClientBuilder.standard()
            .withRegion(Regions.fromName(config.awsSesRegion))
        val credentials = BasicAWSCredentials(config.awsSesAccessKey, config.awsSesSecretKey)
        return builder.withCredentials(AWSStaticCredentialsProvider(credentials)).build()
    }

    fun sendCredentialsEmail(email: String, user: User, password: String, language: Language): Boolean {
        val languageTerms = LanguageManager.getLanguageTerms(language)
        val mailObject = languageTerms.mailRedeemCredentialsObject
        val mailContent = languageTerms.format(languageTerms.mailRedeemCredentialsContent, user.email, languageTerms.format(languageTerms.mailRedeemCredentialsPassword, password))

        logger.info("Sending credentials mail to '$email'")
        return sendMail(email, mailObject, mailContent)
    }


    private fun sendMail(to: String, mailSubject: String, mailContent: String): Boolean {
        return try {
            val destination = Destination().withToAddresses(to)

            val subject = Content().withData(mailSubject).withCharset("UTF-8")
            val htmlBody = Content().withData(mailContent).withCharset("UTF-8")
            val body = Body().withHtml(htmlBody)

            val message = Message()
                .withSubject(subject)
                .withBody(body)

            val request = SendEmailRequest()
                .withSource("Kidjo <$awsSesFromEmail>")
                .withDestination(destination)
                .withMessage(message)

            val result = sesClient.sendEmail(request)
            logger.info("Email sent successfully to '$to'. Message ID: ${result.messageId}")
            true
        } catch (e: Exception) {
            logger.error("Failed to send email to '$to': ${e.message}", e)
            false
        }
    }

    fun sendConfirmationEmail(email: String, name: String, language: Language): Boolean {
        val languageTerms = LanguageManager.getLanguageTerms(language)
        val mailObject = languageTerms.getTerm(
            languageTerms.mailRedeemCredentialsObject,
            LanguageManager.getDefaultLanguageTerms().mailRedeemCredentialsObject
        )
        val mailContent = languageTerms.format(
            languageTerms.mailRedeemCredentialsContent, name.takeIf { it!="" }?:email, "",
            defaultTerm = LanguageManager.getDefaultLanguageTerms().mailRedeemCredentialsContent
        )

        logger.info("Sending subscribing conformation email to '$email'")
        return sendMail(email, mailObject, mailContent)
    }

    fun sendResetPasswordEmail(email: String, password: String, language: Language): Boolean {
        val languageTerms = LanguageManager.getLanguageTerms(language)
        val mailObject = languageTerms.getTerm(
            languageTerms.mailSendPasswordObject,
            LanguageManager.getDefaultLanguageTerms().mailSendPasswordObject
        )
        val mailContent = languageTerms.format(
            languageTerms.mailSendPasswordContent,
            password,
            defaultTerm = LanguageManager.getDefaultLanguageTerms().mailSendPasswordContent
        )

        logger.info("Sending reset password mail to '$email'")
        return sendMail(email, mailObject, mailContent)
    }

    fun sendResetPasswordEmailByPartner(
        email: String, password: String, language: Language, userPartner: String
    ): Boolean {
        return if (userPartner == Device.StorePlatform.MONDIA.name.toUpperCasePreservingASCIIRules()) {
            sendResetPasswordEmailMondia(email, password, language)
        } else {
            sendResetPasswordEmail(email, password, language)
        }
    }

    private fun sendResetPasswordEmailMondia(email: String, password: String, language: Language): Boolean {
        val languageTerms = LanguageManager.getLanguageTerms(language)
        val mailObject = languageTerms.getTerm(
            languageTerms.mondiaEmailTitle,
            LanguageManager.getDefaultLanguageTerms().mondiaEmailTitle
        )
        val mailContent = languageTerms.format(
            languageTerms.mondiaEmailApi, email,
            password,
            defaultTerm = LanguageManager.getDefaultLanguageTerms().mondiaEmailApi
        )

        logger.info("Sending reset password mail to '$email'")
        return sendMail(email, mailObject, mailContent)

    }
}
