package net.kidjo.server.shared.models

data class LinkAttribution(val id: Long,
                           val name: String,
                           val deepLinkId: String,
                           val link: String,
                           val isActive: Boolean)

data class LinkAttributionEvent(val linkId: Long) {
    enum class EventType(val raw: String) {
        INSTALL("install"), SUBSCRIBE("subscribe")
    }
}