package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums

/*
ACTIVE The one-shot access period is still valid.
EXPIRED The one-shot access period has ended.
SUBSCRIBED The subscription is on-going and recurring invoicing is enabled.
CANCELLED The subscription is on-going but recurring invoicing is disabled as the end of contract was requested. The subscription will remain in this state until the end of the current period is reached. Access content SHOULD be kept during this state.
Depending on the countries or carriers guidelines, a cancellation request will have an immediate effect and subscription will directly be transitioned into the TERMINATED state.
TERMINATED The subscription is closed and contract expiration has been reached. No further access to the content is expected.
SUSPENDED The subscription is on-going but scheduled invoices for the period failed. Until a retry policy performs a successful invoice, the status will remain. Access content MAY be put on hold during this state. Only available in some countries.
*/
enum class SubscriptionStatusType(val raw: String) {
    ACTIVE("ACTIVE"),
    EXPIRED("EXPIRED"),
    SUBSCRIBED("SUBSCRIBED"),
    CANCELLED("CANCELLED"),
    TERMINATED("TERMINATED"),
    SUSPENDED("SUSPENDED"),
    NONE("NONE")

    ;

    companion object {
        fun fromRaw(raw: String?): SubscriptionStatusType {
            return when (raw) {
                ACTIVE.raw -> ACTIVE
                EXPIRED.raw ->EXPIRED
                SUBSCRIBED.raw -> SUBSCRIBED
                CANCELLED.raw -> CANCELLED
                TERMINATED.raw-> TERMINATED
                SUSPENDED.raw-> SUSPENDED
                else -> NONE
            }
        }
    }
}