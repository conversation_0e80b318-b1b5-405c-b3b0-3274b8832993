package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.sms.send

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.updateSenditoEmailStatus
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.CONTENT_TYPE_URLENCODED
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.buildRequestHeaders
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.generatedBasicToken
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.sms.DVSenditoApi
import okhttp3.FormBody
import okhttp3.Response
import java.net.URLEncoder

private const val MESSAGE_SENDITO_SMS_KEY = "message"
private const val DESTINATION_SENDITO_KEY = "destination"
private const val OPERATION_ID_SENDITO_KEY = "operation_id"
private const val COUNTRY_SENDITO_KEY = "country"
private const val OFFER_ID_SENDITO_KEY = "offer_id"
private const val LANGUAGE_SENDITO_KEY = "lang"
private const val TYPE_SENDITO_KEY = "type"
private const val SESAME_LOGIN = "sesame_login"
private const val SESAME_PASSWORD = "sesame_password"
private const val SITE = "site"
private const val URL_CVG = "url_cvg"
private const val LOGIN = "login"
private const val PASSWORD = "password"
private const val URL = "url"
private const val URL_TOKEN = "url_token"
private const val TEMPLATE_ID="template_id"

suspend fun DVSenditoApi.sendSENDITOApi(
    messageSMSValue: String,
    destinationSMSValue: String,
    operationIdSMSValue: String,
    offerIdSMSValue: String,
    countryCodeSMSValue: String,
    languageCodeSMSValue: String = "fr",
    typeSMSValue: String = "welcome",
    dvSesameLogin: String,
    dvSesamePasswordHashed: String,
    senditoApiUrl: String,
    site: String,
    url_cvg: String,
    databaseController: DatabaseController,
    login: String,
    password: String,
    url: String,
    urlToken: String,
    templateId:Long,
    operatorName:String
): Boolean {
    var responsePOST: Response? = null
    try {
        logger.info("<<<<< ENTER SEND SMS TO VIRGO USER: sendSMSVirgoUser() >>>>>")

        val formBuilder = FormBody.Builder()
            .addEncoded(MESSAGE_SENDITO_SMS_KEY, URLEncoder.encode(messageSMSValue, "UTF-8"))
            .addEncoded(DESTINATION_SENDITO_KEY, URLEncoder.encode(destinationSMSValue, "UTF-8"))
            .addEncoded(OPERATION_ID_SENDITO_KEY, operationIdSMSValue)
            .addEncoded(COUNTRY_SENDITO_KEY, countryCodeSMSValue)
            .addEncoded(LANGUAGE_SENDITO_KEY, languageCodeSMSValue)
            .addEncoded(TYPE_SENDITO_KEY, typeSMSValue)
            .addEncoded(OFFER_ID_SENDITO_KEY, offerIdSMSValue)
            .addEncoded(SESAME_LOGIN, dvSesameLogin)
            .addEncoded(SESAME_PASSWORD, dvSesamePasswordHashed)
            .addEncoded(SITE, site)
            .addEncoded(URL_CVG, url_cvg)
            .addEncoded(LOGIN, login)
            .addEncoded(PASSWORD, password)
            .addEncoded(URL, url)
            .addEncoded(URL_TOKEN, urlToken)

        // Add template_encode ONLY if operatorName is "NONE"
        if (operatorName.equals("NONE", ignoreCase = true) && templateId!=0L) {
            formBuilder.addEncoded(TEMPLATE_ID, templateId.toString()) // or some actual value
        }
        val formBody = formBuilder.build()

        val basicToken =
            generatedBasicToken(
                login = dvSesameLogin,
                password = dvSesamePasswordHashed
            )

        val requestPOST =
            buildRequestHeaders(
                url = senditoApiUrl,
                token = basicToken,
                contentType = CONTENT_TYPE_URLENCODED
            ).post(formBody).build()

        logger.info(
            "[DVSenditoApi.sendSENDITOApi] Calling SENDITO API on ${requestPOST.url()} | " +
                    "Method: ${requestPOST.method()} | Headers: ${requestPOST.headers()} | " +
                    "Body: ${getFormBodyAsString(requestPOST)} "
        )

        responsePOST = withContext(Dispatchers.IO) {
            httpClient.newCall(requestPOST).execute()
        }

        val responseBodyString = withContext(Dispatchers.IO) { responsePOST.body()?.string() }
        return when (responsePOST.code()) {
            202 -> {
                logger.info("SEND SMS TO VIRGO USER: SUCCESS ${responsePOST.code()} destinationSMSValue ($destinationSMSValue) | Response ($responseBodyString) ")
                databaseController.updateSenditoEmailStatus(destinationSMSValue)
                responsePOST.close()
                true
            }

            else -> {
                logger.error("UNSUCCESSFUL SEND SMS: UNSUCCESS ${responsePOST.code()} | Response ($responseBodyString)  ")
                responsePOST.close()
                logger.error("<<<<< EXIT UNSUCCESSFUL SEND SMS : sendSMSVirgoUser() >>>>>")
                false
            }
        }
    } catch (e: Exception) {
        logger.error("Error DV subscriptionInfoResponse, ${e.localizedMessage}")
        throw e

    } finally {
        responsePOST?.close()
    }
}

fun getFormBodyAsString(request: okhttp3.Request): String {
    val body = request.body()
    return if (body is FormBody) {
        (0 until body.size()).joinToString("&") { "${body.name(it)}=${body.value(it)}" }
    } else {
        ""
    }
}
