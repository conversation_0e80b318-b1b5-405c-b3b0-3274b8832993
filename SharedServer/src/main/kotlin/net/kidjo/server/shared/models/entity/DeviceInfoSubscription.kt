package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object DeviceInfoSubscription: Table("device_info_subscription") {
    val id = integer("id").autoIncrement()
    val deviceInfoId = integer("deviceInfoId").autoIncrement()
    val subscriptionId=integer("subscriptionId")
    val updated_at = datetime("updated_at")
    val created_at = datetime("created_at")

}
