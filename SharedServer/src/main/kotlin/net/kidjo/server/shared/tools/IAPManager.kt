package net.kidjo.server.shared.tools

import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.MerchantAccountId
import net.kidjo.server.shared.payments.SwisscomApiManager

class IAPManager(config: Config) {
    val defaultIOSIAP =
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_monthly_7days_5", 27, 4.99, "P1M", "P7D", "Family A")
    val defaultIOSIAPNoFreeTrial =
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_monthly_7days_5", 27, 4.99, "P1M", "P7D", "Family A")

    private val iosIAPs = arrayOf(
        defaultIOSIAP,
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_family_a_yearly", 85, 17.99, "P1Y", "P7D", "Family A"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_12months_0days_38", 27, 29.99, "P1Y", "P90D", "Family B"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_monthly_30days_5", 27, 3.99, "P1M", "P7D", "Family B"),

        InAppPurchase(Device.StorePlatform.IOS, "ios_monthly_7days_3", 27, 4.99, "P1M", "P3D", "Family C"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_3months_0days_9", 27, 12.99, "P3M", "P7D", "Family C"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_6months_0days_20", 27, 19.99, "P6M", "P7D", "Family C"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_12months_0days_30", 27, 34.99, "P1Y", "P7D", "Family C"),

        InAppPurchase(Device.StorePlatform.IOS, "KidjoPlusMonthSubscription5", 28, 4.99, "P1M", "0", "Family D"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_12months_30days_38", 84, 29.99, "P1Y", "P7D", "Family D"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_monthly_family_e", 28, 1.99, "P1M", "P3D", "Family E"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_12months_30days_30", 42, 17.99, "P1Y", "0", "Family E"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_monthly_family_f", 28, 3.99, "P1M", "P7D", "Family F"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjoplus_yearly_family_f", 42, 29.99, "P1Y", "0", "Family F"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjo_family_g_yearly", 84, 9.99, "P1Y", "P3D", "Family G"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_family_g_monthly", 84, 2.99, "P1M", "P3D", "Family G"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjo_plus_monthly_7days_free", 84, 4.99, "P1M", "P7D", "KidjoPlus Subscriptions"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_plus_yearly_7days_free", 84, 29.99, "P1Y", "P7D", "KidjoPlus Subscriptions"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjo_premium_monthly_7days_free", 84, 4.99, "P1M", "P7D", "KidjoPlus Subscriptions"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_premium_yearly_7days_free", 84, 29.99, "P1Y", "P7D", "KidjoPlus Subscriptions"),

        InAppPurchase(Device.StorePlatform.IOS, "kidjo_monthly_7days_free_premium", 84, 4.99, "P1M", "P7D", "KidjoPlus Subscriptions"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_yearly_7days_free_premium", 84, 29.99, "P1Y", "P7D", "KidjoPlus Subscriptions"),

        InAppPurchase(Device.StorePlatform.IOS, "bundle_price_monthly", 84, 4.99, "P1M", "P7D", "KidjoPlus Subscriptions"),
        InAppPurchase(Device.StorePlatform.IOS, "bundle_price_yearly", 84, 29.99, "P1Y", "P7D", "KidjoPlus Subscriptions"),

        //Test purchase
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_books_monthly_7_days", 27, 4.99, "P1M", "P7D", "Family A"),
        InAppPurchase(Device.StorePlatform.IOS, "kidjo_games_monthly_7_days", 27, 4.99, "P1M", "P7D", "Family A")


    )

    val defaultPlayStoreIAP =
        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_month_5_real2", 0, 4.99, "P1M", "P0D", "Family A")
    val defaultPlayStoreIAPNoFreeTrail =
        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_1month_7days_4_real", 0, 4.99, "P1M", "0", "Family A")
    private val playStoreIAPs = arrayOf(
        defaultPlayStoreIAP,
        defaultPlayStoreIAPNoFreeTrail,

        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_1week_0days_1_real", 85, 0.99, "P1W", "0", "Family A"),
        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_1week_0days_2_real", 85, 1.99, "P1W", "0", "Family A"),

        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_month_1_real2", 103, 0.99, "P1M", "P3D", "Family A"),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_1month_7days_2_real",
            37,
            1.99,
            "P1M",
            "P3D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_1month_0days_3_real",
            85,
            2.99,
            "P1M",
            "0",
            "Family A"
        ),

        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_month_2_real2", 103, 2.99, "P1M", "P3D", "Family A"),
        InAppPurchase(Device.StorePlatform.PLAYSTORE, "kidjoplus_month_4_real2", 103, 3.99, "P1M", "P7D", "Family A"),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_1month_0days_5_real",
            62,
            4.99,
            "P1M",
            "0",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_1month_7days_5_real",
            37,
            4.99,
            "P1M",
            "P3D",
            "Family A"
        ),


        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_10_real2",
            103,
            9.99,
            "P1Y",
            "P3D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_0days_15_real",
            85,
            14.99,
            "P1Y",
            "0",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_0days_18_real",
            37,
            17.99,
            "P1Y",
            "0",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_18_real2",
            103,
            17.99,
            "P1Y",
            "P7D",
            "Family A"
        ),

        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_7days_20_real",
            85,
            19.99,
            "P1Y",
            "P3D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_0days_30_real",
            37,
            29.99,
            "P1Y",
            "0",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_30_real2",
            103,
            29.99,
            "P1Y",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_90days_30_real",
            90,
            29.99,
            "P1Y",
            "P90D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_12months_30days_35_real",
            37,
            34.99,
            "P1Y",
            "P7D",
            "Family A"
        ),

        // New IAPs
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_3months_30days_12_new",
            177,
            4.99,
            "P1M",
            "P1M",
            "Family A"
        ),
        // Kidjo Books IAPs
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjo_books_monthly_7_days_trial",
            0,
            4.99,
            "P1M",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjo_books_1year_7days_free_trail",
            0,
            29.90,
            "P1Y",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjo_books_30days_7day_free_trail",
            0,
            4.99,
            "P1M",
            "P7D",
            "Family A"
        ),

        // TCL 30 days
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_month_30free_tcl_mobile",
            0,
            4.99,
            "P1M",
            "P1M",
            "Family A"
        ),
        // Kidjo Games App
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjo_games_monthly_7_days_trial",
            0,
            4.99,
            "P1M",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjo_games_30days_7day_free_trail",
            0,
            4.99,
            "P1M",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjo_games_1year_7day_free_trail",
            0,
            29.90,
            "P1Y",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_30days_7days_free_trial",
            0,
            4.99,
            "P1M",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_1year_7days_free_trial",
            0,
            29.90,
            "P1Y",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_3in1_12months_7days_free_trail",
            0,
            4.99,
            "P1M",
            "P7D",
            "Family A"
        ),
        InAppPurchase(
            Device.StorePlatform.PLAYSTORE,
            "kidjoplus_3in1_1year_7days_free_trail",
            0,
            29.90,
            "P1Y",
            "P7D",
            "Family A"
        )

    )

    val defaultOrangeIAP =
        InAppPurchase(Device.StorePlatform.ORANGE, "kidjo_tv_orange_weekly", 0, 1.99, "P1W", "P0D", "Family A")
    private val orangeIAPs = arrayOf(
        InAppPurchase(
            Device.StorePlatform.ORANGE,
            "kidjo_tv_subscription_weekly_199",
            0,
            1.99,
            "P1W",
            "P0D",
            "Family A"
        ),
        InAppPurchase(Device.StorePlatform.ORANGE, "kidjo_books_orange_weekly", 0, 1.99, "P1W", "0", "Family A"),
        InAppPurchase(Device.StorePlatform.ORANGE, "kidjoplus_1week_0days_1_real", 85, 1.99, "P1W", "0", "Family A"),
        InAppPurchase(Device.StorePlatform.ORANGE, "kidjoplus_1week_0days_2_real", 85, 1.99, "P1W", "0", "Family A"),
        InAppPurchase(Device.StorePlatform.ORANGE, config.orange_staging_iap_id, 85, 1.99, "P1W", "0", "Family A")

    )

    val defaultHuaweiIAPMC =
        InAppPurchase(
            Device.StorePlatform.HUAWEI,
            "kidjo_tv_huawei_monthly_7_days_trial",
            0,
            1.99,
            "P1W",
            "P0D",
            "Family A"
        )
    val defaultHuaweiIAPROW =
        InAppPurchase(
            Device.StorePlatform.HUAWEI,
            "kidjo_tv_huawei_monthly_7_days_trial",
            0,
            1.99,
            "P1M",
            "P30D",
            "Family A"
        )
    val defaultHuaweiIAPs = InAppPurchase(
        Device.StorePlatform.HUAWEI,
        "kidjo_tv_huawei_monthly_7_days_trial",
        0,
        1.99,
        "P1W",
        "0",
        "Family A"
    )
    val defaultGamesIAPs = InAppPurchase(
        Device.StorePlatform.HUAWEI,
        "huawei_kidjo_games_monthly_a_7_days_free_trial",
        0,
        1.99,
        "P1W",
        "P0D",
        "Family A"
    )
    val defaultKidjoStoriesIAPs = InAppPurchase(
        Device.StorePlatform.HUAWEI,
        "huawei_kidjo_stories_monthly_a_7_days_free_trial",
        0,
        1.99,
        "P1W",
        "P0D",
        "Family A"
    )
    private val huaweiIAPs =
        arrayOf(defaultHuaweiIAPMC, defaultHuaweiIAPROW, defaultHuaweiIAPs, defaultGamesIAPs, defaultKidjoStoriesIAPs)
    val defaultJioIAPMC =
        InAppPurchase(Device.StorePlatform.JIO, "kidjo_tv_jio_monthly_7_days_trial", 0, 1.99, "P1W", "P0D", "Family A")
    val defaultJioIAPROW =
        InAppPurchase(Device.StorePlatform.JIO, "kidjo_tv_jio_monthly_7_days_trial", 0, 1.99, "P1M", "P30D", "Family A")
    private val jioIAPs = arrayOf(
        InAppPurchase(Device.StorePlatform.JIO, "kidjo_tv_jio_monthly_7_days_trial", 0, 1.99, "P1W", "0", "Family A")
    )
    val defaultSamsungIAPMC =
        InAppPurchase(Device.StorePlatform.SAMSUNG, "samsung_1month_30days_5", 0, 3.99, "P1M", "P30D", "Family A")
    val defaultSamsungIAPROW =
        InAppPurchase(Device.StorePlatform.SAMSUNG, "samsung_1month_30days_2", 0, 1.99, "P1M", "P30D", "Family A")
    val defaultSamasungIAPGames =
        InAppPurchase(
            Device.StorePlatform.SAMSUNG,
            "samsung_kidjo_games_monthly_7_days_trial",
            0,
            4.99,
            "P1M",
            "P30D",
            "Family A"
        )
    val defaultSamasungIAP =
        InAppPurchase(
            Device.StorePlatform.SAMSUNG,
            "samsung_kidjo_monthly_7_days_trial",
            0,
            4.99,
            "P1M",
            "P30D",
            "Family A"
        )

    private val samsungIAPs =
        arrayOf(defaultSamsungIAPMC, defaultSamsungIAPROW, defaultSamasungIAPGames, defaultSamasungIAP)


    val defaultAmazonIAP =
        InAppPurchase(Device.StorePlatform.AMAZON, "kidjo_premium_amazon_a_5_7days", 0, 4.99, "P1M", "P0D", "Family A")
    val defaultAmazonIAPNoFreeTrail =
        InAppPurchase(Device.StorePlatform.AMAZON, "kidjo_premium_amazon_b_5_0days", 0, 4.99, "P1M", "0", "Family A")
    private val defaultAmazonGamesIAP = InAppPurchase(
        Device.StorePlatform.AMAZON,
        "amazon_kidjo_games_monthly_a_7_days_trial",
        0,
        4.99,
        "P1M",
        "0",
        "Family A"
    )
    private val defaultAmazonStoriesIAP = InAppPurchase(
        Device.StorePlatform.AMAZON,
        "amazon_kidjo_stories_monthly_a_7_days_trial",
        0,
        4.99,
        "P1M",
        "0",
        "Family A"
    )

    private val amazonIAPs =
        arrayOf(defaultAmazonIAP, defaultAmazonIAPNoFreeTrail, defaultAmazonGamesIAP, defaultAmazonStoriesIAP)
    val defaultKidjoBrainTreeIapMC =
        InAppPurchase(Device.StorePlatform.KIDJO_BRAINTREE, "kidjo_server_plan_5_monthly_7f", 0, 4.99, "P1M", "P7D", "")
    val defaultKidjoBrainTreeIapROW =
        InAppPurchase(Device.StorePlatform.KIDJO_BRAINTREE, "kidjo_server_plan_2_monthly_7f", 0, 1.99, "P1M", "P7D", "")
    val defaultKidjoBrainTreeIAPYear = InAppPurchase(
        Device.StorePlatform.KIDJO_BRAINTREE,
        "kidjo_server_plan_3_12months_7f",
        0,
        34.90,
        "P1Y",
        "P7D",
        ""
    )
    private val kidjoIAPs = arrayOf(
        defaultKidjoBrainTreeIapMC,
        defaultKidjoBrainTreeIapROW,
        defaultKidjoBrainTreeIAPYear,
        // Kidjo - TV IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_server_plan_discount_4_monthly_7f",
            0,
            2.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_server_plan_discount_10_12months_7f",
            0,
            20.90,
            "P1Y",
            "P7D",
            ""
        ),

        // Kidjo - Books IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_books_server_plan_6_monthly_7f",
            0,
            4.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_books_server_plan_8_12months_7f",
            0,
            34.90,
            "P1Y",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_books_server_plan_discount_7_monthly_7f",
            0,
            2.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_books_server_plan_discount_9_12months_7f",
            0,
            20.90,
            "P1Y",
            "P7D",
            ""
        ),

        // Kidjo - Games IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_games_server_plan_6_monthly_7f",
            0,
            4.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_games_server_plan_8_12months_7f",
            0,
            34.90,
            "P1Y",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_games_server_plan_discount_7_monthly_7f",
            0,
            2.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_games_server_plan_discount_9_12months_7f",
            0,
            20.90,
            "P1Y",
            "P7D",
            ""
        ),

        // Kidjo - TV AND BOOKS IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_tv_and_books_server_plan_12_monthly_7f",
            0,
            7.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_tv_and_books_server_plan_11_12months_7f",
            0,
            55.90,
            "P1Y",
            "P7D",
            ""
        ),
        // Kidjo - TV AND GAMES IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_tv_and_games_server_plan_12_monthly_7f",
            0,
            7.99,
            "P1M",
            "P7D",
            ""
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "kidjo_tv_and_games_server_plan_11_12months_7f",
            0,
            55.90,
            "P1Y",
            "P7D",
            ""
        ),

        // Kidjo - TV FOR SWISS IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_server_plan_5_monthly_7f",
            0,
            6.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_server_plan_3_12months_7f",
            0,
            49.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_server_plan_2_monthly_7f",
            0,
            6.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_server_plan_discount_4_monthly_7f",
            0,
            2.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_server_plan_discount_10_12months_7f",
            0,
            19.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - Books FOR SWISS IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_books_server_plan_6_monthly_7f",
            0,
            6.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_books_server_plan_8_12months_7f",
            0,
            49.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_books_server_plan_discount_7_monthly_7f",
            0,
            2.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_books_server_plan_discount_9_12months_7f",
            0,
            19.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - GAMES FOR SWISS IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_games_server_plan_6_monthly_7f",
            0,
            6.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_games_server_plan_8_12months_7f",
            0,
            49.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_games_server_plan_discount_7_monthly_7f",
            0,
            2.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_games_server_plan_discount_9_12months_7f",
            0,
            19.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - TV AND BOOKS FOR SWISS IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_tv_and_books_server_plan_12_monthly_7f",
            0,
            9.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_tv_and_books_server_plan_11_12months_7f",
            0,
            69.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        // Kidjo - TV AND Games FOR SWISS IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_tv_and_games_server_plan_12_monthly_7f",
            0,
            9.90,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "swiss_kidjo_tv_and_games_server_plan_11_12months_7f",
            0,
            69.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        // Kidjo - TV FOR EUROPE IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_server_plan_5_monthly_7f",
            0,
            4.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_server_plan_2_monthly_7f",
            0,
            1.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_server_plan_3_12months_7f",
            0,
            34.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_server_plan_discount_4_monthly_7f",
            0,
            2.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_server_plan_discount_10_12months_7f",
            0,
            20.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - Books FOR EUROPE IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_books_server_plan_6_monthly_7f",
            0,
            4.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_books_server_plan_8_12months_7f",
            0,
            34.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_books_server_plan_discount_7_monthly_7f",
            0,
            2.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_books_server_plan_discount_9_12months_7f",
            0,
            20.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - Games FOR EUROPE IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_games_server_plan_6_monthly_7f",
            0,
            4.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_games_server_plan_8_12months_7f",
            0,
            34.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_games_server_plan_discount_7_monthly_7f",
            0,
            2.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_games_server_plan_discount_9_12months_7f",
            0,
            20.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - TV AND GAMES FOR EUROPE IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_tv_and_games_server_plan_12_monthly_7f",
            0,
            7.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_tv_and_games_server_plan_11_12months_7f",
            0,
            55.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),

        // Kidjo - TV AND BOOKS FOR EUROPE IAPs
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_tv_and_books_server_plan_12_monthly_7f",
            0,
            7.99,
            "P1M",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
        InAppPurchase(
            Device.StorePlatform.KIDJO_BRAINTREE,
            "europe_kidjo_tv_and_books_server_plan_11_12months_7f",
            0,
            55.90,
            "P1Y",
            "P7D",
            "",
            MerchantAccountId.USD
        ),
    )
    val defaultAndroidTvIAP =
        InAppPurchase(Device.StorePlatform.ANDROID_TV, "kidjoplus_month_5_real2", 0, 4.99, "P1M", "P0D", "Family A")

    val defaultSwisscomIap = InAppPurchase(
        Device.StorePlatform.SWISSCOM,
        "swisscom",
        0,
        SwisscomApiManager.SWISSCOM_SUBSCRIPTION_PRICE,
        "",
        "",
        ""
    )
    val defaultFreeAccessIap = InAppPurchase(
        Device.StorePlatform.FREE_ACCESS_COUPON,
        "coupon",
        0,
        0.00,
        "",
        "",
        ""
    )
    val defaultMondiaMediaIap = InAppPurchase(
        Device.StorePlatform.MONDIA_MEDIA,
        "mondia_media",
        0,
        0.00,
        "",
        "",
        ""
    )

    fun getIAPWithDefaults(
        platform: Device.StorePlatform,
        iapId: String,
        freeTrial: Boolean = true,
        mainCountry: Boolean = true,
        build: Int = 0
    ): InAppPurchase {
        val iap = getIAP(platform, iapId)
        if (iap != null) return iap
        return getDefaultIAP(platform, freeTrial)
    }

    fun getDefaultIAP(
        platform: Device.StorePlatform,
        freeTrial: Boolean = true,
        mainCountry: Boolean = true,
        build: Int = 0
    ): InAppPurchase {
        return when (platform) {
            Device.StorePlatform.ANDROID_TV -> defaultAndroidTvIAP
            Device.StorePlatform.IOS -> if (freeTrial) defaultIOSIAP else defaultIOSIAPNoFreeTrial
            Device.StorePlatform.PLAYSTORE -> if (freeTrial) defaultPlayStoreIAP else defaultPlayStoreIAPNoFreeTrail
            Device.StorePlatform.ORANGE -> if (freeTrial) defaultOrangeIAP else defaultOrangeIAP
            Device.StorePlatform.AMAZON -> if (freeTrial) defaultAmazonIAP else defaultAmazonIAPNoFreeTrail
            Device.StorePlatform.SAMSUNG -> if (mainCountry) defaultSamsungIAPMC else defaultSamsungIAPROW
            Device.StorePlatform.KIDJO_BRAINTREE -> if (mainCountry) defaultKidjoBrainTreeIapMC else defaultKidjoBrainTreeIapROW
            Device.StorePlatform.HUAWEI -> if (mainCountry) defaultHuaweiIAPMC else defaultHuaweiIAPROW
            Device.StorePlatform.JIO -> if (freeTrial) defaultJioIAPMC else defaultJioIAPROW
            Device.StorePlatform.MONDIA_MEDIA -> defaultMondiaMediaIap
            Device.StorePlatform.DOCOMO -> throw Exception("DOCOMO MEDIA does not have an IAP")
            Device.StorePlatform.VIRGO -> throw Exception("VIRGO MEDIA does not have an IAP")
            Device.StorePlatform.TWT -> throw Exception("TWT MEDIA does not have an IAP")
            Device.StorePlatform.FREE_ACCESS_COUPON -> defaultFreeAccessIap
            Device.StorePlatform.SWISSCOM -> defaultSwisscomIap
            Device.StorePlatform.CAFEYN -> throw Exception("Cafeyn does not have an IAP")
            else -> throw Exception("Device StorePlatform is required ")
        }

    }

    fun getIAP(platform: Device.StorePlatform, iapId: String): InAppPurchase? {
        return when (platform) {
            Device.StorePlatform.KIDJO_BRAINTREE -> getKidjoBrainTreeIAP(iapId)
            Device.StorePlatform.IOS -> getIOSIAP(iapId)
            Device.StorePlatform.PLAYSTORE -> getPlayStoreIAP(iapId)
            Device.StorePlatform.AMAZON -> getAmazonIAP(iapId)
            Device.StorePlatform.SAMSUNG -> getSamsungIAP(iapId)
            Device.StorePlatform.ORANGE -> getOrangeIAP(iapId)
            Device.StorePlatform.HUAWEI -> getHuaweiIAP(iapId)
            Device.StorePlatform.JIO -> getJioIAP(iapId)
            Device.StorePlatform.MONDIA_MEDIA -> defaultMondiaMediaIap
            Device.StorePlatform.DOCOMO -> null
            Device.StorePlatform.VIRGO -> null
            Device.StorePlatform.TWT -> null
            Device.StorePlatform.FREE_ACCESS_COUPON -> defaultFreeAccessIap
            Device.StorePlatform.SWISSCOM -> defaultSwisscomIap
            Device.StorePlatform.CAFEYN -> null
            Device.StorePlatform.ANDROID_TV -> defaultAndroidTvIAP
            else -> null
        }
    }

    fun getIOSIAP(iapId: String): InAppPurchase? {
        iosIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getPlayStoreIAP(iapId: String): InAppPurchase? {
        playStoreIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getOrangeIAP(iapId: String): InAppPurchase? {
        orangeIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getHuaweiIAP(iapId: String): InAppPurchase? {
        huaweiIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getJioIAP(iapId: String): InAppPurchase? {
        jioIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getSamsungIAP(iapId: String): InAppPurchase? {
        samsungIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getAmazonIAP(iapId: String): InAppPurchase? {
        amazonIAPs.forEach { if (it.id == iapId) return it }
        return null
    }

    fun getKidjoBrainTreeIAP(iapId: String): InAppPurchase? {
        kidjoIAPs.forEach { if (it.id == iapId) return it }
        return null
    }
}


