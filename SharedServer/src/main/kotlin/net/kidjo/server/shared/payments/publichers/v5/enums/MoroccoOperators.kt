package net.kidjo.server.shared.payments.publishers.v5.enums

import java.util.*

/**
 * This enum is not updated with the correct MCCMNC for morocco, we need to update it and include all MNCs
 */
enum class MoroccoOperators(
    val id: Int,
    val opName: String,
    val countryShort: String,
    val mccmnc: Int,
    val countryId: Long,
    val currencyCode: String
) {
    IAM(
        1,
        "IAM_MOROCCO",
        "ma",
        60401,
        151,
        "MAD"
    ),
    ORANGE(
        2,
        "ORANGE_MOROCCO",
        "ma",
        60402,
        151,
        "MAD"
    ),
    INWI(
        3,
        "INWI_MOROCCO",
        "ma",
        60405,
        151,
        "MAD"
    ),
    NONE(
        0,
        "NONE",
        "NONE",
        0,
        0,
        "NONE"
    );

    companion object {
        fun getByOpName(opName: String): MoroccoOperators {
            val lower = opName.lowercase(Locale.getDefault())
            return when (lower) {
                IAM.opName -> IAM
                ORANGE.opName -> ORANGE
                INWI.opName -> INWI
                else -> NONE
            }
        }

        fun getByCountryShort(countryShort: String?): MoroccoOperators {
            return when (if (!countryShort.isNullOrBlank()) countryShort.lowercase(Locale.getDefault()) else "") {
                IAM.countryShort -> IAM
                ORANGE.countryShort -> ORANGE
                INWI.countryShort -> INWI
                else -> NONE
            }
        }

        fun getByMCCMNC(mccmnc: Int): MoroccoOperators {
            return when (mccmnc) {
                60401 -> IAM
                60402 -> ORANGE
                60403 -> INWI
                else -> NONE
            }
        }
    }
}
