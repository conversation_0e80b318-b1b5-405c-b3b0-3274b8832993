package net.kidjo.server.shared.database

import net.kidjo.server.shared.models.CouponLink

enum class LogicomSerialCheck {
    UNKNOWN, VALID, UPDATED, ERROR
}

data class LogicomSerialBundle(val check: LogicomSerialCheck, val oldDeviceId: Long? = null)
data class LogicomSerialBundleV4(val check: <PERSON>olean)

fun DatabaseController.logicomGetFirstWithRemaining(): CouponLink? {
    val connection = dataSource.connection
    val statement1 = connection.prepareStatement("SELECT * FROM coupons WHERE type = ? AND validCountLeft > 0 ORDER BY id ASC")
    statement1.setString(1, "Logicom")
    val results = statement1.executeQuery()
    val couponLink = if (results.next()) CouponLink(results) else null
    statement1.close()
    connection.close()
    return couponLink
}

fun DatabaseController.logicomCheckDevice(serialNumber: String? = null, macAddress: String? = null, deviceId: Long? = null): LogicomSerialBundle {
    if (serialNumber == null && macAddress == null)
        return LogicomSerialBundle(LogicomSerialCheck.UNKNOWN)

    val connection = dataSource.connection
    var sql = "SELECT device_id FROM devices_logicom WHERE"
    if (serialNumber != null)
        sql += " serial_number = ?"
    if (macAddress != null)
        sql += if (serialNumber != null) " OR mac_address = ?" else " mac_address = ?"
    val statement = connection.prepareStatement(sql)
    statement.setString(1, if (serialNumber != null) serialNumber else macAddress)
    if (serialNumber != null && macAddress != null)
        statement.setString(2, macAddress)
    val results = statement.executeQuery()
    var logicomSerialCheck = if (results.next()) {
        val oldDeviceId = results.getLong("device_id")
        if (!results.wasNull()) LogicomSerialBundle(LogicomSerialCheck.UPDATED, oldDeviceId)
        else LogicomSerialBundle(LogicomSerialCheck.VALID)
    } else {
        LogicomSerialBundle(LogicomSerialCheck.UNKNOWN)
    }
    statement.close()

    if (deviceId != null
            && (logicomSerialCheck.check == LogicomSerialCheck.VALID
                    || logicomSerialCheck.check == LogicomSerialCheck.UPDATED)) {
        var sql2 = "UPDATE devices_logicom SET device_id = ? WHERE"
        if (serialNumber != null)
            sql2 += " serial_number = ?"
        if (macAddress != null)
            sql2 += if (serialNumber != null) " OR mac_address = ?" else " mac_address = ?"
        val statement2 = connection.prepareStatement(sql2)
        statement2.setLong(1, deviceId)
        statement2.setString(2, if (serialNumber != null) serialNumber else macAddress)
        if (serialNumber != null && macAddress != null)
            statement2.setString(3, macAddress)
        if (!statement2.executeAndCheck())
            logicomSerialCheck = LogicomSerialBundle(LogicomSerialCheck.ERROR)
        statement2.close()
    }

    connection.close()
    return logicomSerialCheck
}

fun DatabaseController.logicomV4Check(serialNumber: String? = null, macAddress: String? = null): LogicomSerialBundleV4 {
    if (serialNumber == null && macAddress == null)
        return LogicomSerialBundleV4(false)

    val connection = dataSource.connection
    var sql = "SELECT device_id FROM devices_logicom WHERE"
    if (serialNumber != null)
        sql += " serial_number = ?"
    if (macAddress != null)
        sql += if (serialNumber != null) " OR mac_address = ?" else " mac_address = ?"
    val statement = connection.prepareStatement(sql)
    statement.setString(1, if (serialNumber != null) serialNumber else macAddress)
    if (serialNumber != null && macAddress != null)
        statement.setString(2, macAddress)
    val results = statement.executeQuery()
    var logicomSerialCheck = if (results.next()) {
        LogicomSerialBundleV4(true)
    } else {
        LogicomSerialBundleV4(false)
    }
    statement.close()

    connection.close()
    return logicomSerialCheck
}
