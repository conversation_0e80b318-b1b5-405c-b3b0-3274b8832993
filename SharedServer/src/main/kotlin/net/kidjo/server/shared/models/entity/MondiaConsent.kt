package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object MondiaConsent : Table("mondia_consent") {
    val id = long("id").autoIncrement()
    val consentId = varchar("consent_id", 255)
    val userUuid = varchar("user_uuid", 255).nullable()
    val flow = varchar("flow", 50).nullable()
    val fields_ = varchar("fields", 2000).nullable()
    val entitlements = varchar("entitlements", 2000).nullable()
    val consentRecordCreated = datetime("consent_record_created").nullable()
    val signature = varchar("signature", 255).nullable()
    val eventId = varchar("event_id", 255).nullable()

    val createdAt = datetime("created_at").default(LocalDateTime.now())
    val updatedAt = datetime("updated_at").nullable()

}