package net.kidjo.server.shared.models

import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.shared.tools.LanguageTerms
import java.sql.ResultSet

class Promotion {
    companion object {
        fun formatPrice(price: Float?, countryShortName: String): String? {
            val country = countryShortName.toLowerCase()
            val currency = if (country == "gb") "£" else if (country == "us") "$" else "€"
            val currencyFirst = country == "gb" || country == "us"
            return price?.let { "${if (currencyFirst) currency else ""}$it${if (!currencyFirst) currency else ""}" }
        }
    }

    val id: Long
    val isValid: Boolean
    val deepLinkId: String

    val title: String

    val ios_iap: String
    val ios_oldestBuild: Int
    val playstore_iap: String
    val playstore_oldestBuild: Int

    val viewId: IAPRule.ViewId = IAPRule.ViewId.VIEW_NEW

    val pricePer: String
    val price: Float
    val oldPrice: Float?
    val freeTrial: Int

    constructor(resultSet: ResultSet) {
        id = resultSet.getLong("id")
        isValid = resultSet.getBoolean("valid")
        deepLinkId = resultSet.getString("deepLinkPromoID")
        title = resultSet.getString("title")
        ios_iap = resultSet.getString("iap_ios")
        ios_oldestBuild = resultSet.getInt("oldest_build_ios")
        playstore_iap = resultSet.getString("iap_android")
        playstore_oldestBuild = resultSet.getInt("oldest_build_android")
        pricePer = resultSet.getString("pricePer")
        price = resultSet.getFloat("price")
        val oldPriceValue = resultSet.getFloat("oldPrice")
        oldPrice = if (resultSet.wasNull()) null else oldPriceValue
        freeTrial = resultSet.getInt("freeTrial")
    }

    fun getPricePer(languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms(), customPricePer: String? = null): String {
        val defaultLanguageTerms = LanguageManager.getDefaultLanguageTerms()
        return when (customPricePer?.toLowerCase() ?: pricePer.toLowerCase()) {
            "day" -> languageTerms.subscriptionViewNewDay.ifBlank { defaultLanguageTerms.subscriptionViewNewDay }
            "year" -> languageTerms.subscriptionViewNewYear.ifBlank { defaultLanguageTerms.subscriptionViewNewYear }
            else -> languageTerms.subscriptionViewNewMonth.ifBlank { defaultLanguageTerms.subscriptionViewNewMonth }
        }
    }

    fun getFreeTrial(languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms()): String {
        val count = freeTrial / 30
        val pricePer = if (count > 0) getPricePer(languageTerms, "month")
        else getPricePer(languageTerms, "day")
        return "${if (count > 0) count else freeTrial} $pricePer"
    }
}
