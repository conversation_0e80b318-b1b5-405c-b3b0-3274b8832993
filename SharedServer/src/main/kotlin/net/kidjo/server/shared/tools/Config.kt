package net.kidjo.server.shared.tools

import net.kidjo.common.models.Language
import java.util.Locale

class Config(
    val appTypeId: AppTypeId,
    val env: Env
) {

    //DEBUG
    val isDebug: Boolean
    val permissiveCORS: Boolean
    var redirectionBaseUrl: String = "localhost:8989"
    var vodacomRedirectionBaseUrl: String = "localhost:8989"

    //server
    val port: Int

    //header and user stuff
    val apiCode = "Tg4TwzUgR8"
    val mainCountriesId = arrayOf(
        "za", "sa", "am", "au", "bs", "bh", "be", "ca", "cn", "kr",
        "ci", "dk", "eg", "ae", "es", "us", "fi", "fr", "hk", "mu", "id", "ir", "ie", "is", "il", "jp", "kw",
        "lb", "li", "lu", "ml", "mt", "no", "nz", "om", "nl", "pl", "pt", "qa", "gb", "ru", "sn", "sg", "se",
        "ch", "tn"
    )

    //val ID things
    val idEncryptionKey = 52854957485963545L
    val idEncryptionPad = 56
    val hashIdSalt = "2PDzvOhtiB"
    val hashIdLength = 10
    val cookies_secureUserSessionCookies: Boolean

    //Image and video URLS
    val defaultLanguageId = Language.ID_ENGLISH
    val defaultCountryId = 237 // US

    val videoThumbnailsUrl: String = "https://d22oud9a7xyyql.cloudfront.net/V2/"
    val videoUrl: String = "https://d23sw6prl9jc74.cloudfront.net/"
    val folderUrl: String = "https://d2dcsm9gsb21yx.cloudfront.net/"
    val prodFolderUrl : String="https://d2ci7g2i49tul4.cloudfront.net/"
    var folderThumbnail = "https://d3b4o7vp07919u.cloudfront.net/"
    var prodFolderThumbnail = "https://d22oud9a7xyyql.cloudfront.net/"
    val gameImages: String = "https://d3aod987c9rl70.cloudfront.net/gameImages/"
    val folderThumbnailImageUrl: String = folderUrl + "folderImage/settings/"

    val user_saltLogRounds = 12

    val locale_default = Locale("en", "US")

    //MYSQL
    val db_properties = "?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false"
    val db_maxFetch = 20
    val db_name = "kidjo" + db_properties
    val db_user: String
    val db_host: String
    val db_password: String
    val db_maxConnections = 90

    // Redis
    val redis_login: String

    //iOS stuff
    val iOS_iTunesKidjoTvSharedSecret = "537f24f7d876492d85fb0e7ca61dd644"
    val iOS_iTunesKidjoBooksSharedSecret = "537f24f7d876492d85fb0e7ca61dd644"
    val iOS_verify_receipt_url = "https://buy.itunes.apple.com/verifyReceipt"
    val iOS_verify_santbox_receipt_url = "https://sandbox.itunes.apple.com/verifyReceipt"

    //PlayStore package name
    val playStoreKidjoTvPackageName = "net.kidjo.app.android"
    val playStoreKidjoBooksPackageName = "net.kidjo.books.android.playstore"
    val playStoreKidjoGamesPackageName: String = "net.kidjo.games.app.playstore"

    //Orange stuff
    val orange_useStaging: Boolean
    var orange_verify_receipt_url: String
    var orange_kidjo_tv_package_name: String
    var orange_kidjo_books_package_name: String
    var orange_staging_iap_id = "fake-sku-subs-subscribed"

    //Orange web verification
    val orange_kidjo_authorization_url = "https://api.hubler.digital/authorization/v1/request"
    val orange_kidjo_authorization_dev_user = "<EMAIL>"
    val orange_kidjo_authorization_dev_password = "Xn[k7S;nV+!zu?-E"

    val orange_user_authentication_url = "https://api.hubler.digital/authentication/v1/request"
    val orange_user_authentication_service_partner = "kidjo.tv"
    val orange_user_authentication_auth_call_back_url = "https://app.kidjo.tv"

    val orange_user_info_url = "https://api.hubler.digital/eligibility/v1/user-profile/%s"

    //Virgo Orange stuff
    var dv_sesame_login: String = ""
    var dv_sesame_password: String = ""
    var dv_sesame_password_hashed: String = ""
    var dv_secret_key: String = ""
    var dv_sendito_message_api_url: String = ""
    var dv_merchants_api_url: String = ""
    var dv_cancel_url = "https://www.mes-abonnements.ma/"
    var dv_kidjo_service_id: Long = 0
    var dv_kidjo_offer_clame_name: String = "offer"
    var dv_kidjo_service_clame_name: String = "service"
    var dv_login_redirect_url: String = ""
    var jwt_token_validity_mls: Long = 60000
    var url_cvg = "https://www.kidjo.tv/terms"
    var site = "kidjo"

    //Old Version Virgo stuff
    var virgo_sesame_login: String = "kidjo"
    var virgo_sesame_password: String = "KiDjOaPmOsEnS21*"
    var virgo_sesame_password_hashed: String = "S2lEak9hUG1Pc0VuUzIxKg=="
    var virgo_sendito_message_prod_api_url: String = "https://sendito.contactdve.com/message/create"
    var virgo_unsubcribe_prod_api_url: String = "http://billing.virgopass.com/api_v1.5.php?unsubscribe=1"
    var virgo_useSandbox: Boolean

    //Amazon stuff
    var amazon_verify_receipt_url: String
    var amazon_operation_version_number: String = ""
    var amazon_shared_secret: String = ""
    var amazon_userId: String = ""
    val amazon_useSandbox: Boolean

    //HUAWEI stuff
    val huaweiSubscriptionUrl = "https://subscr-dre.iap.hicloud.com/sub/applications/v2/purchases/get"
    val huaweiAuthorizationTokenUrl = "https://oauth-login.cloud.huawei.com/oauth2/v3/token"
    val huaweiClientSecretForTv = "9840b94727799d7f1a98a5497aac9551a509825730d587cc172fda404c7d69f0"
    val huaweiClientIdForTv = "104525487"
    val huaweiClientSecretForStories = "0c9db70b90c5227dc2f9828dfdac5bb6ad81856ce5daed19688551af6807754a"
    val huaweiClientIdForStories = "109790605"
    val huaweiClientSecretForGames = "73c2451f7c5f350041098f88bd96167ed2382000df8e193264c2b7d58a6d2607"
    val huaweiClientIdForGames = "109796795"
    val huaweiGrantType = "client_credentials"
    var isDeviceType: String = ""
    var deviceIdForTwt: String = ""


    //JIO stuff
    val jio_useSandbox: Boolean
    var jio_verify_receipt_url: String
    var jio_kidjo_tv_package_name: String
    var jio_shared_secret: String = ""
    var jio_clientId: String = ""

    //Subscription view
    var subscriptionViewAssetRoot: String = ""
    val subscriptionViewDebugCode = "2ERdw2kwStaeBJygy00hJ70WIzjHsFfERRLho4WXOq4tVTvDzjmf"
    val subscriptionViewDebug: Boolean

    //Web site
    val websiteAssetRoot: String
    val websiteHotReload: Boolean
    var websiteHotReloadDir: String
    var clientSideHashV1Salt = "n2iciz509zQV2MRlZDSiI3DFrol"

    //BRAIN tree
    val brainTree_useSandbox: Boolean
    val brainTree_merchandId: String
    val brainTree_publicKey: String
    val brainTree_privateKey: String

    //Google credentials is in resources now

    //Facebook
    val facebook_appId = "1037837526263294"
    val facebook_accessToken = "********************************"

    // Email Configuration
    val mailDomain = "mail.kidjo.tv"
    val mailAuthor = "postmaster@$mailDomain"

    // AWS SES Configuration
    val awsSesRegion = "us-east-1"
    val awsSesFromEmail = "postmaster@$mailDomain"
    val awsSesAccessKey = "********************"
    val awsSesSecretKey = "DpXNWB6dsC1cwXB0GYg0/cysiR+n74TaltFJqDog"

    // Legacy Mailgun Configuration (deprecated)
    val mailApiUrl = "https://api.eu.mailgun.net/v3/$mailDomain/messages"
    val mailApiUser = "api"
    val mailApiKey = "************************************"

    //Deeplonks for Vewd
    val tvAppSite: String

    //User Login Limitation number
    val maxLoginNumber = 3
    val productsNumber = 2

    //Cafeyn
    val cafeynTVSecretKey: String
    val cafeynStoriesSecretKey: String
    val cafeynGamesSecretKey: String
    val cafeynAccountsSecretKey: String
    val cafeynTokenValidationUrl: String
    val cafeynTvPartnerName: String = "kidjo-tv"
    val cafeynStoriesPartnerName: String = "kidjo-stories"
    val cafeynGamesPartnerName: String = "kidjo-games"
    val cafeynAccountsPartnerName: String = "kidjo-accounts"

    //Switch
    val apiKey = "SyFkSjA="
    val apiUrl = "https://api.jazzkidjo.com/kidjoUnSubscribeUser"


    // Job Configuration
    val enableCafeynJob: Boolean
    val enableAmazonJob: Boolean
    val enableHuaweiJob: Boolean
    val enableIosJob: Boolean
    val enableOrangeJob: Boolean
    val enableSamsungJob: Boolean
    val enablePlaystoreJob: Boolean
    val enableBraintreeJob: Boolean
    val enableSwitchJob: Boolean
    val tvSite: String
    val storySite: String
    val gamesSite: String
    val accountsSite: String
    val flywayLocations: Array<String>


    //products to offer partners
    val subscriptionPartnerProduct = "kidjo_tv_books_games" // "kidjo_tv_books"
    var userPassword = ""

    init {
        when (env) {
            Env.LOCAL -> {
                isDebug = true
                permissiveCORS = true

                port = 8989

                db_user = "root"
                db_host = "localhost"
                db_password = "root"

                redis_login = "redis://localhost:6379/0"

                subscriptionViewDebug = true

                websiteHotReload = true

                subscriptionViewAssetRoot = "/assets/subscription/"
                websiteAssetRoot = "/assets/"

                cookies_secureUserSessionCookies = false

                brainTree_useSandbox = true
                orange_useStaging = true
                amazon_useSandbox = false
                jio_useSandbox = true
                virgo_useSandbox = true

                tvAppSite = "http://localhost:8080"

                if (env == Env.LOCAL) {

                    websiteHotReloadDir =
                        "/Users/<USER>/BitBucketGit/kidjo-server/ApiServer/src/main/resources/"
                } else {
                    websiteHotReloadDir =
                        "/home/<USER>/Documents/workspace/Kidjo/server/Server/ApiServer/src/main/resources/"
                }
                // Cafeyn Integration details
                cafeynTVSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynStoriesSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynGamesSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynAccountsSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"

                cafeynTokenValidationUrl = "https://hub-preprod.cafeyn.co/api"
                tvSite = "http://localhost:8080"
                storySite = "http://localhost:8080"
                gamesSite = "http://localhost:8080"
                accountsSite = "http://localhost:8080"
                enableCafeynJob = true
                enableAmazonJob = false
                enableHuaweiJob = false
                enableIosJob = false
                enableOrangeJob = false
                enableSamsungJob = false
                enablePlaystoreJob = false
                enableBraintreeJob = true
                enableSwitchJob = true
                flywayLocations = arrayOf("db/migrations", "db/dev")
            }

            Env.DEVELOP -> {
                redirectionBaseUrl = "devaccounts.kidjo.tv"
                vodacomRedirectionBaseUrl="stage-app.kidjo.tv"
                isDebug = true
                permissiveCORS = true

                port = 80

                db_user = "kidjo"
                db_host = "kidjo-db-dev.cd6mnxzpcgxu.eu-west-3.rds.amazonaws.com"
                db_password = "J0QBFLjHspiDUatzzYyKDevelop"

                redis_login = "redis://kidjo-tv-staging-redis.wqa9dd.ng.0001.euw3.cache.amazonaws.com:6379"

                subscriptionViewDebug = true

                subscriptionViewAssetRoot = "https://d3aod987c9rl70.cloudfront.net/subscription/"
                websiteAssetRoot = "https://d3aod987c9rl70.cloudfront.net/"
                websiteHotReload = false
                websiteHotReloadDir = websiteAssetRoot

                cookies_secureUserSessionCookies = false

                brainTree_useSandbox = true
                orange_useStaging = true
                amazon_useSandbox = true
                jio_useSandbox = true
                virgo_useSandbox = true

                tvAppSite = "https://devtapp.kidjo.tv"
                cafeynTVSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynStoriesSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynGamesSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynAccountsSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"

                cafeynTokenValidationUrl = "https://hub-dev.cafeyn.co/api"
                tvSite = "https://test.kidjo.tv"
                storySite = "http://dev-stories.kidjo.tv"
                gamesSite = "https://dev-games.kidjo.tv"
                accountsSite = "https://devaccounts.kidjo.tv"
                enableCafeynJob = true
                enableAmazonJob = false
                enableHuaweiJob = false
                enableIosJob = false
                enableOrangeJob = false
                enableSamsungJob = false
                enablePlaystoreJob = false
                enableBraintreeJob = false
                enableSwitchJob = true
                flywayLocations = arrayOf("db/migrations", "db/dev")
            }

            Env.STAGING -> {
                redirectionBaseUrl = "testaccount.kidjo.tv"
                vodacomRedirectionBaseUrl="stage-app.kidjo.tv"
                isDebug = true
                permissiveCORS = true

                port = 80

                db_user = "kidjo"
                db_host = "kidjo-db-staging.cd6mnxzpcgxu.eu-west-3.rds.amazonaws.com"
                db_password = "J0QBFLjHspiDUatzzYyKStaging"

                redis_login = "redis://kidjo-tv-staging-redis.wqa9dd.ng.0001.euw3.cache.amazonaws.com:6379"

                subscriptionViewDebug = true

                subscriptionViewAssetRoot = "https://d3aod987c9rl70.cloudfront.net/subscription/"
                websiteAssetRoot = "https://d3aod987c9rl70.cloudfront.net/"
                websiteHotReload = false
                websiteHotReloadDir = websiteAssetRoot

                cookies_secureUserSessionCookies = false

                brainTree_useSandbox = true
                orange_useStaging = true
                amazon_useSandbox = true
                jio_useSandbox = true
                virgo_useSandbox = true

                tvAppSite = "https://testapp.kidjo.tv"

                cafeynTVSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynStoriesSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynGamesSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"
                cafeynAccountsSecretKey = "7m|Vn*d/Z,Eh33r%g%6yChx0^YB&Pd"

                cafeynTokenValidationUrl = "https://hub-preprod.cafeyn.co/api"
                tvSite = "https://test.kidjo.tv"
                storySite = "http://teststories.kidjo.tv"
                gamesSite = "https://staging-games.kidjo.tv"
                accountsSite = "https://testaccount.kidjo.tv"
                enableCafeynJob = true
                enableAmazonJob = false
                enableHuaweiJob = false
                enableIosJob = false
                enableOrangeJob = false
                enableSamsungJob = false
                enablePlaystoreJob = false
                enableBraintreeJob = true
                enableSwitchJob = true
                flywayLocations = arrayOf("db/migrations", "db/stage")
            }

            Env.PROD -> {
                redirectionBaseUrl = "account.kidjo.tv"
                vodacomRedirectionBaseUrl="app.kidjo.tv"
                isDebug = false
                permissiveCORS = false

                port = 80

                db_user = "kidjo"
                db_host = "kidjo-db-prod-cluster.cluster-cd6mnxzpcgxu.eu-west-3.rds.amazonaws.com"
                db_password = "J0QBFLjHspiDUatzzYyK"

                redis_login = "redis://kidjo-redis.wqa9dd.0001.euw3.cache.amazonaws.com:6379"

                subscriptionViewDebug = false

                subscriptionViewAssetRoot = "https://d3aod987c9rl70.cloudfront.net/subscription/"
                websiteAssetRoot = "https://d3aod987c9rl70.cloudfront.net/"

                websiteHotReload = false
                websiteHotReloadDir = websiteAssetRoot

                cookies_secureUserSessionCookies = false

                brainTree_useSandbox = false
                orange_useStaging = false
                amazon_useSandbox = false
                jio_useSandbox = false
                virgo_useSandbox = false

                tvAppSite = "https://tv.kidjo.tv"
                tvSite = "https://www.kidjo.tv"
                storySite = "http://stories.kidjo.tv"
                gamesSite = "https://games.kidjo.tv"
                accountsSite = "https://account.kidjo.tv"

                cafeynTVSecretKey = "pOnVd/U~@QJz%;gQT8>g=ID;0Y?[1Z"
                cafeynStoriesSecretKey = "pOnVd/U~@QJz%;gQT8>g=ID;0Y?[1Z"
                cafeynGamesSecretKey = "pOnVd/U~@QJz%;gQT8>g=ID;0Y?[1Z"
                cafeynAccountsSecretKey = "pOnVd/U~@QJz%;gQT8>g=ID;0Y?[1Z"

                cafeynTokenValidationUrl = "https://hub.cafeyn.co/api"
                enableCafeynJob = true
                enableAmazonJob = true
                enableHuaweiJob = true
                enableIosJob = true
                enableOrangeJob = true
                enableSamsungJob = true
                enablePlaystoreJob = true
                enableBraintreeJob = true
                enableSwitchJob = true
                flywayLocations = arrayOf("db/migrations", "db/prod")

            }
        }

        if (brainTree_useSandbox) {
            brainTree_merchandId = "2cw4y5gwx3j87t9j"
            brainTree_publicKey = "v479kqbgz96rbr6m"//"363cdpbnsdfysrgx"
            brainTree_privateKey = "********************************"//""386c60cef01fa727a6ce055a7642bd3b"
        } else {
            brainTree_merchandId = "w4t7czhbtppbbmpy"
            brainTree_publicKey = "mhpccfv6zmsyfkmn"
            brainTree_privateKey = "********************************"
        }

        if (orange_useStaging) {
            orange_kidjo_tv_package_name = "fake-package-name"
            orange_kidjo_books_package_name = "fake-package-name"
            orange_verify_receipt_url =
                "https://developer-api-stg.wrapper.solutions/android/v1.0/applications/%s/purchases/subscriptions/%s/tokens/%s"
        } else {
            orange_kidjo_tv_package_name = "net.kidjo.app.android.orange"
            orange_kidjo_books_package_name = "net.kidjo.books.android.orange"
            orange_verify_receipt_url =
                "https://developer-api.wrapper.solutions/android/v1.0/applications/%s/purchases/subscriptions/%s/tokens/%s"
        }

        if (amazon_useSandbox) {
            amazon_operation_version_number = "1.0"
            amazon_shared_secret =
                "2:RokCA9xD7ezPdXH-gZpPpc9cMFhi36hn8uN8895_x_Cq5DuA7bO1r3053Kji9q4a:2BKRweEER6zbTiS7hhkoUg=="
            // amazon_userId = "l3HL7XppEMhrOGDnur9-ulvqomrSg6qyODKmah76lJU="
            // amazon_verify_receipt_url = https://appstore-sdk.amazon.com/sandbox/version/
            amazon_verify_receipt_url =
                "https://appstore-sdk.amazon.com/sandbox/version/$amazon_operation_version_number/verifyReceiptId/developer/$amazon_shared_secret/user/%s/receiptId/%s"
        } else {
            amazon_operation_version_number = "1.0"
            amazon_shared_secret =
                "2:RokCA9xD7ezPdXH-gZpPpc9cMFhi36hn8uN8895_x_Cq5DuA7bO1r3053Kji9q4a:2BKRweEER6zbTiS7hhkoUg=="

            amazon_verify_receipt_url =
                "https://appstore-sdk.amazon.com/version/$amazon_operation_version_number/verifyReceiptId/developer/$amazon_shared_secret/user/%s/receiptId/%s"
        }

        if (jio_useSandbox) {
            jio_kidjo_tv_package_name = "jio-package-name"
            jio_verify_receipt_url =
                "https://api-sit.jio.com/cr/v1/jiopay/stbservice/txn/get"
        } else {
            jio_kidjo_tv_package_name = "jio-package-name-prod"
            jio_verify_receipt_url =
                "https://api.jiomoney.com/cr/v1/jiopay/stbservice/txn/get"
        }

        if (virgo_useSandbox) {
            dv_sesame_login = "kidjo_apistg"
            dv_sesame_password = "KiDjOaPiStS11*"
            dv_sesame_password_hashed = "S2lEakR2UER2UzEzKg=="
            dv_secret_key = "437d5a76e85f197167462307074ba1a7"
            dv_sendito_message_api_url = "https://pp-sendito.contactdve.com/messages"
            dv_merchants_api_url = "staging.merchants.dvpass.io"
            dv_kidjo_service_id = 36
            dv_login_redirect_url = "https://testapp.kidjo.tv?key="
        } else {
            dv_sesame_login = "kidjo_api"
            dv_sesame_password = "KiDjDvPDvS13*"
            dv_sesame_password_hashed = "S2lEakR2UER2UzEzKg=="
            dv_secret_key = "9a44b1642930d2a79b797204d27fed60"
            dv_sendito_message_api_url = "https://sendito.dvetech.fr/messages"
            dv_merchants_api_url = "merchants.dvpass.io"
            dv_kidjo_service_id = 36
            dv_login_redirect_url = "https://tv.kidjo.tv?key="
        }
    }

    fun getPairingUrl(languageCode: Int): String {
        return "pair"
    }

    enum class AppTypeId {
        SERVER, BACKGROUND, CRON
    }

    enum class Env(val isLive: Boolean) {
        LOCAL(false),
        STAGING(false),
        DEVELOP(false),
        PROD(true)
        ;

        companion object {
            const val ID_LOCAL = "local"
            const val ID_STAGING = "staging"
            const val ID_DEVELOP = "develop"
            const val ID_PROD = "prod"
            fun FromString(s: String): Env {
                return when (s) {
                    ID_LOCAL -> LOCAL
                    ID_STAGING -> STAGING
                    ID_DEVELOP -> DEVELOP
                    else -> PROD
                }
            }
        }
    }
}
