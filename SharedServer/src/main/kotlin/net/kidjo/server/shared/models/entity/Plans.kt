package net.kidjo.server.shared.models.entity

import net.kidjo.server.shared.database.v5.PlanType
import org.jetbrains.exposed.sql.Table

object Plans : Table("plans") {
    val id = integer("id").autoIncrement()
    val planName = varchar("planName", 256)
    val planType = customEnumeration("planType", null, { value ->
        PlanType.valueOf((value as String).uppercase())
    }, {
        it.planType
    })
    val active = bool("active")
    val bundle = bool("bundle")
    val product = (integer("productId") references Products.id)
}
