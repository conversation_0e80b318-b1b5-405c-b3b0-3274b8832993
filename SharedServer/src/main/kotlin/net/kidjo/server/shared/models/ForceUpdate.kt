package net.kidjo.server.shared.models

data class ForceUpdate (var id: String,
                        var appName: AppName,
                        var appModule: AppModule,
                        var appOS: AppOS,
                        var optionalVersion: Float,
                        var mandatoryVersion: Float) {


    enum class AppName(val raw: String) {
        KIDJO_TV("kidjo-tv"), K<PERSON>JO_BOOKS("kidjo-books");

        companion object {
            fun FromRaw(raw: String): AppName {
                return when (raw) {
                    KIDJO_TV.raw -> KIDJO_TV
                    KIDJO_BOOKS.raw -> KIDJO_BOOKS
                    else -> KIDJO_TV
                }
            }
        }
    }
    enum class AppModule(val raw: String) {
        PLAYSTORE("playstore"), LOGICOM("logicom"), SWISSCOM("swisscom"),
        SAMSUNG("samsung"), IOS("ios"), TVOS("tvos");

        companion object {
            fun FromRaw(raw: String): AppModule {
                return when (raw) {
                    PLAYSTORE.raw -> PLAYSTORE
                    LOGICOM.raw -> LOGICOM
                    SWISSCOM.raw -> SWISSCOM
                    SAMSUNG.raw -> SAMSUNG
                    IOS.raw -> IOS
                    TVOS.raw -> TVOS
                    else -> PLAYSTORE
                }
            }
        }
    }

    enum class AppOS(val raw: String) {
        ANDROID("android"), APPLE("apple");

        companion object {
            fun FromRaw(raw: String): AppOS {
                return when (raw) {
                    ANDROID.raw -> ANDROID
                    APPLE.raw -> APPLE
                    else -> ANDROID
                }
            }
        }
    }
}