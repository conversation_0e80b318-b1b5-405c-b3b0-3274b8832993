package net.kidjo.server.shared.database

import net.kidjo.server.shared.models.AccountCoupon
import java.sql.SQLException


fun DatabaseController.accountCoupon_get(id: Long): AccountCoupon? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT a.*, ap.name as partner_name, apr.name as product_name, at.name as coupon_type_name " +
                "FROM account_coupons a " +
                "LEFT JOIN account_coupon_partner ap ON a.id_partner = ap.id  " +
                "LEFT JOIN account_coupon_product apr ON a.id_product = apr.id " +
                "LEFT JOIN account_coupon_type at ON a.id_type = at.id " +
                "WHERE a.id = ? "
    )
    try {
        statement.setLong(1, id)
        val results = statement.executeQuery()
        val accountCoupon: AccountCoupon?
        if (results.next()) accountCoupon = objectCreator.accountCoupon(results)
        else accountCoupon = null

        return accountCoupon
    } catch (e: SQLException) {
        println("Error getting: accountCoupon_get ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.accountCoupon_getByCouponId(couponId: String): AccountCoupon? {
    if (couponId == "") return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT a.*, ap.name as partner_name, apr.name as product_name , at.name as coupon_type_name " +
                "FROM account_coupons a " +
                "LEFT JOIN account_coupon_partner ap ON a.id_partner = ap.id  " +
                "LEFT JOIN account_coupon_product apr ON a.id_product = apr.id " +
                "LEFT JOIN account_coupon_type at ON a.id_type = at.id " +
                "WHERE a.couponId = ? "
    )
    try {
        statement.setString(1, couponId)
        val results = statement.executeQuery()
        val accountCoupon: AccountCoupon?
        if (results.next()) accountCoupon = objectCreator.accountCoupon(results)
        else accountCoupon = null

        return accountCoupon
    } catch (e: SQLException) {
        println("Error getting: accountCoupon_getByCouponId ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.accountCoupon_consumeCoupon(id: Long): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE account_coupons SET redeemedTimes = redeemedTimes + 1 WHERE id = ?")
    try {
        statement.setLong(1, id)
        val results = statement.executeAndCheck()

        return results
    } catch (e: SQLException) {
        println("Error getting: accountCoupon_consumeCoupon ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.getAccountCouponId(id: Long): AccountCoupon? {
    if (id == -1L) return null
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT a.*, ap.name as partner_name, apr.name as product_name , at.name as coupon_type_name " +
                "FROM account_coupons a " +
                "LEFT JOIN account_coupon_partner ap ON a.id_partner = ap.id  " +
                "LEFT JOIN account_coupon_product apr ON a.id_product = apr.id " +
                "LEFT JOIN account_coupon_type at ON a.id_type = at.id " +
                "WHERE a.id = ? "
    )
    try {
        statement.setLong(1, id)
        val results = statement.executeQuery()
        val accountCoupon: AccountCoupon? = if (results.next()) objectCreator.accountCoupon(results) else null

        return accountCoupon
    } catch (e: SQLException) {
        println("Error getting: getAccountCouponId ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

