package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.ForceUpdate
import java.sql.ResultSet

fun ResultSetObjectCreator.forceUpdate(resultSet: ResultSet): ForceUpdate {
    val id = resultSet.getLong("id").toString()
    val appName = ForceUpdate.AppName.FromRaw(resultSet.getString("app_name"))
    val appModule = ForceUpdate.AppModule.FromRaw(resultSet.getString("app_module"))
    val appOs = ForceUpdate.AppOS.FromRaw(resultSet.getString("app_os"))
    val optionalVersion = resultSet.getFloat("optional_version")
    val mandatoryVersion = resultSet.getFloat("mandatory_version")
    return ForceUpdate(id, appName, appModule, appOs, optionalVersion, mandatoryVersion)
}
