package net.kidjo.server.shared.tools

import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.user_getCountryIdFromShortName
import java.util.*

object RequestUtil {

    fun getCountryFromCode(databaseController: DatabaseController, code: String? = null): Int? {
        // Get country identifier from country code
        return if (code?.isNotBlank() == true) databaseController.user_getCountryIdFromShortName(code) else null
    }

    fun getLocaleFromHeader(acceptLanguageHeader: String? = null): Locale? {
        val headerLocale = acceptLanguageHeader?.split(",")
        if (headerLocale?.isNotEmpty() == true) {
            return Locale.forLanguageTag(headerLocale[0])
        }

        return null
    }

    fun getCountryFromHeader(databaseController: DatabaseController, acceptLanguageHeader: String? = null): Int? {
        val userLocale: Locale? = getLocaleFromHeader(acceptLanguageHeader)
        return if (userLocale != null) databaseController.user_getCountryIdFromShortName(userLocale.country) else null
    }

    fun getLanguageFromHeader(languageCache: LanguageCache, acceptLanguageHeader: String? = null): Int? {
        val headerLocale = acceptLanguageHeader?.split(",")
        if (headerLocale?.isNotEmpty() == true) {
            return languageCache.getLanguageIdFromAcceptLanguageHeader(acceptLanguageHeader)
        }

        return null
    }

}
