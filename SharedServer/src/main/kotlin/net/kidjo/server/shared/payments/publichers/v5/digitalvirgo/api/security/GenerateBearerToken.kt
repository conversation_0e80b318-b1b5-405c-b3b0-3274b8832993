package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security

import net.kidjo.server.shared.security.JwtPartnerManager

fun generatedBearerToken(sub: String,
                         iss: String,
                         secret: String,
                         claim:  Pair<String, Long>? = null,
                         validity: Long): String = "Bearer " +
    if(claim != null)
        JwtPartnerManager.generatePartnerJWTWithClaim(
            subject = sub,
            issuer = iss,
            secret = secret,
            claim = claim,
            validityInMs = validity)
    else
        JwtPartnerManager.generatePartnerJWT(
            subject = sub,
            issuer = iss,
            secret = secret,
            validityInMs = validity)