package net.kidjo.server.shared.database

import net.kidjo.server.shared.models.BackgroundTaskLog
import java.sql.PreparedStatement
import java.time.LocalDateTime


fun DatabaseController.backgroundTask_startLog(backgroundVersion: String, taskType: BackgroundTaskLog.TaskType): Long {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO background_task_logs(version, taskType,longMessage) VALUES (?,?,'')", PreparedStatement.RETURN_GENERATED_KEYS)
    statement.setString(1,backgroundVersion)
    statement.setString(2,taskType.raw)

    val results = statement.executeAndCheck()
    val ids = statement.generatedKeys

    val id: Long
    if (ids.next()) id = ids.getLong(1)
    else id = BackgroundTaskLog.NO_ID

    statement.close()
    connection.close()

    return id
}
fun DatabaseController.backgroundTask_finishLog(id: Long, results: BackgroundTaskLog.Results,
                                                shortMessage: String,
                                                longMessage: String,
                                                updateMessage: String,
                                                cancelMessage: String,
                                                endTime: LocalDateTime): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE background_task_logs SET result = ?, shortMessage = ?, longMessage = ?, updateMessage = ?, cancelMessage = ?, end = ? WHERE id = ?")
    statement.setString(1,results.raw)
    statement.setString(2,shortMessage)
    statement.setString(3,longMessage)
    statement.setString(4,updateMessage)
    statement.setString(5,cancelMessage)
    statement.setString(6,endTime.format(mysqlDateTimeFormatter))
    statement.setLong(7,id)

    val dbResults = statement.executeAndCheck()

    statement.close()
    connection.close()

    return dbResults
}