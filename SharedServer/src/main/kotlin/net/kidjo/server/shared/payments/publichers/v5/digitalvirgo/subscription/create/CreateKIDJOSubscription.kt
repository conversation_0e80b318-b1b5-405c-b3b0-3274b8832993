package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.create
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.tools.EncryptionController
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private const val DV_WEEKLY_SUBSCRIPTION_PRICE = 10.00f
private const val DV_MONTHLY_SUBSCRIPTION_PRICE = 35.00f
private val logger  = LoggerFactory.getLogger("createKIDJOSubscription - ")
@Throws
fun createKIDJOSubscription(
    userId: Long,
    userAlias: String,
    subscriptionIdString: String,
    nextBillingDate: LocalDateTime,
    operatorName: String,
    price: Float= DV_WEEKLY_SUBSCRIPTION_PRICE,
    isTestEnv: <PERSON>olean,
    encryptionController: EncryptionController,
    databaseController: DatabaseController
): Long? = try {
    logger.info("<<<<< ENTER CREATE USER SUBSCRIPTION DV createUserSubscription() >>>>>")

    var insertedSubId: Long? = null
    logger.info("SUBSCRIPTION KIDJO CREATE: nextBillingDate is $nextBillingDate ")
    val subscriptionRootInsert =
            SubscriptionRootInsert(
                userId = userId,
                deviceId = 0L,
                isFreeTrail = false,
                priceToLogUSD = price,
                paymentType = SubscriptionRoot.PaymentType.NATIVE,
                subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV,
                paymentId = Device.StorePlatform.VIRGO.raw,
                platformPurchaseId = Device.StorePlatform.VIRGO.raw,
                storeId = Device.StorePlatform.VIRGO,
                paymentStateId = null,
                iapId = userAlias,
                purchasingSessionId = "NOT SET",
                subscriptionToken = subscriptionIdString,
                subscriptionTokenHash = encryptionController.sha256Hash(subscriptionIdString),
                accountCouponId = 0L,
                isRenewing = true,
                nextBillDate = nextBillingDate,
                isTest = isTestEnv,
                operatorName = operatorName
            )
    logger.info("SUBSCRIPTION KIDJO CREATE: subscriptionRootInsert is $subscriptionRootInsert ")

    insertedSubId = databaseController.subscriptionRoot_create(subscriptionRootInsert)

    if (insertedSubId == SubscriptionRoot.NO_ID) {
        logger.error("SUBSCRIPTION KIDJO ERROR: NOT CREATE: subscribe() : user ($userId) subscription failed to create in the database ")
    }
    logger.info("<<<<< EXIT SUBSCRIPTION CREATE KIDJO createManuallyKidjoSubscription() >>>>>")
    insertedSubId
    } catch (e: Throwable) {
        logger.error("Problems creating dv Account user key: ${e.localizedMessage}")
        throw e
    }