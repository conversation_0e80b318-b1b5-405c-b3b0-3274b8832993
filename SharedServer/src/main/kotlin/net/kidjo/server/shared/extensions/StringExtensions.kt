package net.kidjo.server.shared.extensions

import java.util.regex.Pattern

fun String.shortenTo(maxLength: Int): String {
    return substring(0, kotlin.math.min(maxLength, length))
}

fun String.optDouble(defaultValue: Double = 0.0): Double {
    return try {
        this.toDouble()
    } catch (e: Exception) {
        defaultValue
    }
}

fun String.optFloat(defaultValue: Float = 0f): Float {
    return try {
        this.toFloat()
    } catch (e: Exception) {
        defaultValue
    }
}

fun String.optInt(defaultValue: Int = 0): Int {
    return try {
        this.toInt()
    } catch (e: Exception) {
        defaultValue
    }
}

fun String.optLong(defaultValue: Long = 0): Long {
    return try {
        this.toLong()
    } catch (e: Exception) {
        defaultValue
    }
}

fun String.isEmail(): Boolean {
    val emailRegex = Pattern.compile(
            "[a-zA-Z0-9\\+\\.\\_\\%\\-\\+]{1,256}" +
                    "\\@" +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
                    "(" +
                    "\\." +
                    "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
                    ")+"
    )
    return emailRegex.matcher(this).matches()
}

fun String.getBrowserFromUA(): String {
    val regex = Regex("(MSIE|Trident|(?!Gecko.+)Firefox|(?!AppleWebKit.+Chrome.+)Safari(?!.+Edge)|(?!AppleWebKit.+)Chrome(?!.+Edge)|(?!AppleWebKit.+Chrome.+Safari.+)Edge|AppleWebKit(?!.+Chrome|.+Safari)|Gecko(?!.+Firefox))(?: |\\/)([\\d\\.apre]+)")
    return regex.findAll(this).first().groupValues.takeLast(2).mapIndexed { index, value -> if (index == 1) value.split(".").first() else value }.joinToString("/")
}
