package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object AccountCoupons : Table("account_coupons") {
    val id = long("id").autoIncrement()
    val groupId = varchar("groupId", 50)
    val couponId = varchar("couponId", 30)
    val startDate = datetime("startDate")
    val expireDate = datetime("expireDate")
    val duration = varchar("duration", 6)
    val redeemedTimes = integer("redeemedTimes")
    val redeemAmount = integer("redeemAmount")
    val couponType = (long("id_type") references AccountCouponType.id)
    val product = (long("id_product") references AccountCouponProduct.id)
    val partner = (long("id_partner") references AccountCouponsPartner.id)
    val updated_at = datetime("updated_at")
    val created_at = datetime("created_at")
    override val primaryKey = PrimaryKey(id)
}
