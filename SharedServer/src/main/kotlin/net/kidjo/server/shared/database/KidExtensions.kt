package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.kid
import net.kidjo.server.shared.models.Kid
import java.sql.PreparedStatement
import java.sql.SQLException
import kotlin.collections.ArrayList


fun DatabaseController.kid_doesExist(id: Long): Bo<PERSON>an {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT id FROM avatars WHERE id = ?")
    statement.setLong(1, id)
    val results = statement.executeQuery()
    val exists = results.next()

    statement.close()
    connection.close()
    return exists
}

fun DatabaseController.kid_get(id: Long): Kid? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT id,name,age,deviceId FROM avatars WHERE id = ?")
    try {
        statement.setLong(1, id)
        val results = statement.executeQuery()
        val kid: Kid?
        if (results.next()) kid = objectCreator.kid(results)
        else kid = null

        return kid
    } catch (e: SQLException) {
        println("Error getting: kid_get ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.kid_getByDeviceId(deviceId: Long): List<Kid> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT id,name,age,deviceId FROM avatars WHERE deviceId = ?")
    val kids = ArrayList<Kid>()
    try {
        statement.setLong(1, deviceId)
        val results = statement.executeQuery()
        while (results.next()) kids.add(objectCreator.kid(results))

        return kids
    } catch (e: SQLException) {
        println("Error getting: kid_getByDeviceId ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.kid_createThreeDefaultKids(deviceId: Long): List<Kid> {
    val connection = dataSource.connection
    val kids = ArrayList<Kid>()
    val statement = connection.prepareStatement(
        "INSERT INTO avatars(deviceId) VALUE(?),(?),(?)",
        PreparedStatement.RETURN_GENERATED_KEYS
    )

    try {
        statement.setLong(1, deviceId)
        statement.setLong(2, deviceId)
        statement.setLong(3, deviceId)

        val results = statement.executeAndCheck()
        val keys = statement.generatedKeys


        while (keys.next()) {
            val kid = Kid()
            val newKidId = keys.getLong(1)
            kid.serverId = newKidId
            kid.userId = encryptionController.encodeNormalId(newKidId)
            kids.add(kid)
        }

        return kids
    } catch (e: SQLException) {
        println("Error getting: kid_createThreeDefaultKids ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.kid_createKidWithDevice(deviceId: Long): Kid? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT INTO avatars(deviceId) VALUE(?)", PreparedStatement.RETURN_GENERATED_KEYS)
    try {
        statement.setLong(1, deviceId)

        val results = statement.executeAndCheck()
        val keys = statement.generatedKeys

        val kid: Kid?
        if (keys.next()) {
            kid = Kid()
            val newKidId = keys.getLong(1)
            kid.serverId = newKidId
            kid.userId = encryptionController.encodeNormalId(newKidId)
        } else {
            kid = null
        }

        return kid
    } catch (e: SQLException) {
        println("Error getting: kid_createKidWithDevice ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.kid_update(id: Long, age: Int, name: String): Boolean {
    val ageOk = Kid.CheckAge(age)
    val nameOk = Kid.CheckName(name)
    if (!ageOk && !nameOk) return true
    val bothOk = ageOk && nameOk

    val connection = dataSource.connection
    val sql = "UPDATE avatars SET " +
            (if (ageOk) "age = ? " else "") +
            (if (bothOk) ", " else "") +
            (if (nameOk) "name = ? " else "") +
            "WHERE id = ?"
    val statement = connection.prepareStatement(sql)
    try {
        var index = 1
        if (ageOk) {
            statement.setInt(index, age)
            index++
        }
        if (nameOk) {
            statement.setString(index, name)
            index++
        }
        statement.setLong(index, id)
        val results = statement.executeAndCheck()

        return results
    } catch (e: SQLException) {
        println("Error getting: kid_update ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.kid_deleteAndGetNewOne(kidId: Long, deviceId: Long): Long {
    val connection = dataSource.connection
    val statement = connection.createStatement()
    try {
        statement.addBatch("DELETE FROM avatars_folders WHERE avatarId = $kidId")
        statement.addBatch("DELETE FROM avatars WHERE id = $kidId")
        statement.addBatch("INSERT INTO avatars (deviceId) VALUE($deviceId)")
        statement.executeBatch()
        val keys = statement.generatedKeys

        val id: Long
        if (keys.next()) id = keys.getLong(1)
        else id = Kid.NO_ID_SERVER

        return id
    } catch (e: SQLException) {
        println("Error getting: kid_deleteAndGetNewOne ${e.localizedMessage}")
        return 0L
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.kid_updateFilters(kidId: Long, filters: Map<Long, Boolean>): Boolean {
    if (filters.size == 0) return true

    val connection = dataSource.connection
    val sqlBuilder = StringBuilder("DELETE FROM avatars_folders WHERE avatarId = $kidId AND folderId IN (")
    filters.forEach {
        sqlBuilder.append("${it.key},")
    }
    sqlBuilder.deleteCharAt(sqlBuilder.length - 1)
    sqlBuilder.append(")")
    val sql = sqlBuilder.toString()

    val statement = connection.createStatement()
    try {
        statement.addBatch(sql)

        filters.forEach {
            if (it.value == false) {
                statement.addBatch("INSERT INTO avatars_folders(avatarId, folderId, isActive) VALUE ($kidId, ${it.key}, FALSE)")
            }
        }
        val results = statement.executeBatchAndCheck()

        return results
    } catch (e: SQLException) {
        println("Error getting: kid_updateFilters ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}