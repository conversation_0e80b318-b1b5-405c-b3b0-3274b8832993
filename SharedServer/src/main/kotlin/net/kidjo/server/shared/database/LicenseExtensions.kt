package net.kidjo.server.shared.database

import net.kidjo.common.models.License
import net.kidjo.server.shared.database.creator.license

data class BooleanBox(var value: Boolean = false)

fun DatabaseController.licensesGet(licenseId: Long): License? {
    val connection = dataSource.connection
    val sql = "SELECT * FROM licenses WHERE id = ?"

    val statement = connection.prepareStatement(sql)
    statement.setLong(1, licenseId)

    val results = statement.executeQuery()
    val license = if (results.next()) objectCreator.license(results) else null

    statement.close()
    connection.close()

    return license
}

fun DatabaseController.countryByCode(code: String): Int {
    val connection = dataSource.connection
    val sql = "SELECT * FROM countries WHERE short = ?"

    val statement = connection.prepareStatement(sql)
    statement.setString(1, code)
    val results = statement.executeQuery()

    val id: Int = if (results.next()) results.getInt("id") else -1
    statement.close()
    connection.close()

    return id
}

fun DatabaseController.countryNameByCode(code: String): String? {
    val connection = dataSource.connection
    val sql = "SELECT * FROM countries WHERE short = '$code'"
    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()
    val name = if (results.next()) results.getString("name") else null
    statement.close()
    connection.close()
    return name
}

fun DatabaseController.licensesGetList(count: Int? = null, languageId: Int? = null, countryId: Int? = null,
                                       swisscom: Boolean? = null, website: Boolean? = null,
                                       getCountry: Boolean = false, getLanguage: Boolean = false): List<License> {
    val connection = dataSource.connection

    // Request
    val where = BooleanBox(false)
    val select = "licenses.*, folders.type" +
            (if (getCountry) ", GROUP_CONCAT(DISTINCT countries.short) as countries" else "") +
            (if (getLanguage) ", languages.short as languages" else "")
    var sql = "SELECT $select FROM licenses" +
            " JOIN folders ON licenses.folderId = folders.id"
    if (countryId != null || getCountry) {
        sql += " LEFT JOIN folders_countries ON licenses.folderId = folders_countries.folderId"
    }
    if (getCountry) sql += " LEFT JOIN countries ON folders_countries.countryId = countries.id"
    if (getLanguage) sql += " LEFT JOIN languages ON licenses.languageId = languages.id"
    languageId?.let { sql += " ${whereOrAnd(where)} licenses.languageId IN (0, ?)" }
    swisscom?.let { sql += " ${whereOrAnd(where)} licenses.swisscom = ?" }
    website?.let { sql += " ${whereOrAnd(where)} licenses.website = ?" }
    countryId?.let { sql += " ${whereOrAnd(where)} folders_countries.countryId IN (0, ?)" }
    if (getCountry) sql += " GROUP BY licenses.id"
    sql += " ORDER BY licenses.order ASC"
    count?.let { sql += " LIMIT ?" }

    // Statement parameters
    val statement = connection.prepareStatement(sql)
    var index = 1
    languageId?.let { statement.setInt(index, it); index++ }
    swisscom?.let { statement.setBoolean(index, it); index++ }
    website?.let { statement.setBoolean(index, it); index++ }
    countryId?.let { statement.setInt(index, it); index++ }
    count?.let { statement.setInt(index, it); index++ }

    val results = statement.executeQuery()
    val licenses = ArrayList<License>()
    while (results.next()) {
        licenses.add(objectCreator.license(results))
    }

    statement.close()
    connection.close()

    return licenses
}

internal fun whereOrAnd(where: BooleanBox? = null): String {
    var result = "AND"
    if (where != null ) {
        result = if (!where.value) "WHERE" else result
        where.value = true
    }
    return result
}
