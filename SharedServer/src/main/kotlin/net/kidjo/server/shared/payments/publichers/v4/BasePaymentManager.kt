package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.subscriptionRoot_update
import net.kidjo.server.shared.extensions.Success
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import okhttp3.OkHttpClient
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

abstract class BasePaymentManager(
    internal val config: Config,
    internal val httpClient: OkHttpClient,
    internal val encryptionController: EncryptionController,
    internal val databaseController: DatabaseController,
    internal val iapManager: IAPManager,
    internal val emailManager: EmailManager,
    internal val languageCache: LanguageCache,
    internal val userManager: UserManager
) {

    internal val logger = LoggerFactory.getLogger("BasePaymentManager")

    fun isBillingDateExpired(nextBillDate: LocalDateTime): Boolean = LocalDateTime.now().isAfter(nextBillDate)

    suspend fun insert(subscriptionRootInsert: SubscriptionRootInsert, call: ApplicationCall) {
        val id = databaseController.subscriptionRoot_create(subscriptionRootInsert)
        if (id > 0) {
            return call.respondOK(Success.SUCCESS, "Successful created Subscription id: $id ")
        } else {
            logger.error("Error create Subscription id: $id")
            return call.respondBadRequest("Error create Subscription id: $id")
        }
    }

    suspend fun update(
        subscriptionId: String,
        subscriptionRootUpdate: SubscriptionRootUpdate,
        call: ApplicationCall
    ) {
        val checkUpdate = databaseController.subscriptionRoot_update(subscriptionRootUpdate)
        if (!checkUpdate) {
            logger.error("Error update user's subscription: ${subscriptionId}")
            return call.respondBadRequest("Error update user's subscription: ${subscriptionId}")
        } else {

            return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${subscriptionId} ")
        }
    }

    abstract suspend fun start(
        userId: Long, inAppPurchase: InAppPurchase,
        token: String, purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        forceUpdate: Boolean,
        subscriptionId: String?,
        orderId: String?,
        call: ApplicationCall
    )

    abstract suspend fun createSubscription(
        inAppPurchase: InAppPurchase,
        token: String, call:
        ApplicationCall,
        userId: Long,
        purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        subscriptionId: String?,
        orderId: String?
    )

    abstract suspend fun updateSubscription(
        inAppPurchase: InAppPurchase,
        token: String,
        call: ApplicationCall,
        userId: Long,
        subscriptionRoot: SubscriptionRoot
    )
}
