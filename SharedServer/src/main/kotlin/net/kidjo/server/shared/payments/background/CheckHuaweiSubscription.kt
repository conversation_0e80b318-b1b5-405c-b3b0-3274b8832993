package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.HuaweiSubscriptionStatus
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder

fun PaymentManager.checkHuaweiSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val huaweiSubscriptionStatus: HuaweiSubscriptionStatus?
    try {
        huaweiSubscriptionStatus = huaweiApiManager.getSubscriptionStatus(
                subscriptionRoot.subscriptionToken,
                subscriptionRoot.subscriptionType.raw,
                subscriptionRoot.iapId,
                subscriptionRoot.platformPurchaseId
        )

    } catch (e: Exception) {
        throw e
    }

    if (huaweiSubscriptionStatus == null) {
        throw SubscriptionCheckException("Could not find HUAWEI subscription")
    }
    val iap =
        iapManager.getHuaweiIAP(subscriptionRoot.iapId) ?: throw SubscriptionCheckException("Could not find the iap")
    val status: SubscriptionCheckResult.Status
    val priceInUSD: Float = iap.price.toFloat()

    val nextBillingDate = huaweiSubscriptionStatus.expirationDate.epochMilliToLocalDateTime()

    var purchaseState = huaweiSubscriptionStatus.purchaseState

    if (purchaseState == 0) {
        status = SubscriptionCheckResult.Status.ACTIVE
        updateStringBuilder.appendln(
            "UPDATE = HUAWEI Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )

    } else {
        status = SubscriptionCheckResult.Status.CANCEL_USER
        cancelStringBuilder.appendln(
            "CANCEL = HUAWEI Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    }
    return SubscriptionCheckResult(status, priceInUSD, nextBillingDate, null)
}
