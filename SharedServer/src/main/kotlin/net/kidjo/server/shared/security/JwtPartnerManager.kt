package net.kidjo.server.shared.security

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import java.util.*

object JwtPartnerManager {
    private const val mls: Long = 60000 // one minute
    fun verifierPartnerJWT(algorithm: Algorithm, issuer: String): JWTVerifier =
        JWT.require(algorithm)
            .withIssuer(issuer)
            .build()


    fun generatePartnerJWT(subject: String,
                           issuer: String,
                           secret: String,
                           validityInMs: Long = mls
    ): String =
        JWT.create()
            .withSubject(subject)
            .withIssuer(issuer)
            .withExpiresAt(expiresAt(validityInMs))
            .sign(algorithm(secret))
    fun generatePartnerJWTWithClaim(subject: String,
                           issuer: String,
                           secret: String,
                           claim: Pair<String, Long>,
                           validityInMs: Long = mls // one minute
    ): String =
        JWT.create()
            .withSubject(subject)
            .withIssuer(issuer)
            .withClaim(claim.first, claim.second.toInt())
            .withExpiresAt(expiresAt(validityInMs))
            .sign(algorithm(secret))
    private fun expiresAt(validityInMs: Long) =
        Date(System.currentTimeMillis() + validityInMs)
    private fun algorithm(secret: String):Algorithm =
        Algorithm.HMAC256(secret)

}