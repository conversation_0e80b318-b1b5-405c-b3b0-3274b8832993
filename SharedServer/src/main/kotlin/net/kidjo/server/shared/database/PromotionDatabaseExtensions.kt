package net.kidjo.server.shared.database

import net.kidjo.server.shared.models.Promotion

suspend fun DatabaseController.promotion_getByDeepLinkId(deeplinkId: String): Promotion? {
    if (deeplinkId == "") return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM promotions WHERE deepLinkPromoID = ? AND valid = TRUE LIMIT 1")
    statement.setString(1,deeplinkId)

    val results = statement.executeQuery()
    val promotion: Promotion?
    if (results.next()) promotion = Promotion(results)
    else promotion = null

    statement.close()
    connection.close()

    return promotion
}