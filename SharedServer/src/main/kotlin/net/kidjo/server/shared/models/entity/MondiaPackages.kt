package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table

object MondiaPackages : Table("mondia_packages") {

    val id = long("id").autoIncrement()
    val mondiaProductId = integer("mondia_product_id")
    val mondiaPackageId = integer("mondia_package_id")
    val subscriptionPackage = varchar("subscription_package", 255)
    val description = varchar("description", 255)
    val isActive = bool("isActive")
    val operator = varchar("operator", 255)
    val price = double("price")
    val currency = varchar("currency", 255)
    val freeTrial = varchar("freeTrial", 255)
}