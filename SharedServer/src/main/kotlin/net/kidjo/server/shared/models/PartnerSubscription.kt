package net.kidjo.server.shared.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class PartnerSubscription(
    @JsonProperty("email")
    var email: String? = "",
    @JsonProperty("name")
    val username: String = "",
    @JsonProperty("msisdn")
    val msidn: String? = "",
    @JsonProperty("plan")
    val plan: SubscriptionPlan,
    @JsonProperty("product")
    val product: String? = "",
    @JsonProperty("country_code")
    val countryCode: String? = "",
    @JsonProperty("sub_partner")
    val subPartner:String?="",
    @JsonProperty("package")
    val subscriptionPackage:String?=""
)
