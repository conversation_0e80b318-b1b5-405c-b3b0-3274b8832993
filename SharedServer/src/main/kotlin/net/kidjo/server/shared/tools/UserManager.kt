package net.kidjo.server.shared.tools

import io.ktor.http.Cookie
import io.ktor.http.CookieEncoding
import io.ktor.http.auth.HttpAuthHeader
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.parseAuthorizationHeader
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.RequestCookies
import io.ktor.server.response.ResponseCookies
import io.ktor.util.date.GMTDate
import io.ktor.util.date.plus
import net.kidjo.server.shared.cachedatabase.CacheDatabase
import net.kidjo.server.shared.cachedatabase.UserJWTPairing
import net.kidjo.server.shared.cachedatabase.setUserLoginToBlackList
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.checkExistingDeviceId
import net.kidjo.server.shared.database.deleteUserJWT
import net.kidjo.server.shared.database.getCurrentUserLogins
import net.kidjo.server.shared.database.getDeviceForTwtOnly
import net.kidjo.server.shared.database.getExistingToken
import net.kidjo.server.shared.database.getNextOneLoginFromSignOutList
import net.kidjo.server.shared.database.getPartnerNameByUserId
import net.kidjo.server.shared.database.getUserSubscriptionType
import net.kidjo.server.shared.database.setNextUserLogin
import net.kidjo.server.shared.database.setUserLoginToSignOutList
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.database.user_getById
import net.kidjo.server.shared.database.user_getFromSession
import net.kidjo.server.shared.database.user_setSession
import net.kidjo.server.shared.extensions.isEmail
import net.kidjo.server.shared.extensions.toJSON
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.UserSession
import net.kidjo.server.shared.security.JwtKidjoServerManager
import org.slf4j.LoggerFactory
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder


private const val USER_SESSION_ID_IN_COOKIES = "k_sess"
private const val USER_SESSION_MONDIA_OPERATOR_IN_COOKIES = "k_mond_oprt"
private const val LENGTH_OF_SESSION_ID = 60
private val VALID_COOKIE_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray()
private const val MAX_AGE_OF_SESSION_COOKIE = 60 * 60 * 24 * 30 //30 days before cookie expires

class UserManager(
    private val config: Config,
    private val utility: Utility,
    private val databaseController: DatabaseController,
    private val cacheDatabase: CacheDatabase,
    private val jwtManager: JwtKidjoServerManager
) {
    private val logger = LoggerFactory.getLogger("UserManager")

    fun getUser(cookies: RequestCookies, withUser: User) {
        val session = cookies[USER_SESSION_ID_IN_COOKIES] ?: return
        val user = databaseController.user_getFromSession(session) ?: return
        user.isSignedIn = true
        withUser.copy(user)
    }

    fun getUser(cookies: RequestCookies): User {
        val session = cookies[USER_SESSION_ID_IN_COOKIES] ?: return User.getEmptyUser()
        val user = databaseController.user_getFromSession(session) ?: return User.getEmptyUser()
        user.isSignedIn = true
        return user
    }

    fun getUserByToken(token: String): User {
        val user = databaseController.user_getFromSession(token) ?: return User.getEmptyUser()
        user.isSignedIn = true
        return user
    }

    fun getUser(id: String): User {
        val longId = id.toLongOrNull() ?: return User.getEmptyUser()
        val user = databaseController.user_getById(longId) ?: return User.getEmptyUser()
        user.isSignedIn = true
        return user
    }

    fun getUserByEmail(email: String): User {
        if (email.isBlank() || !email.isEmail()) return User.getEmptyUser()
        val user = databaseController.user_getByEmail(email) ?: return User.getEmptyUser()
        return user
    }

    fun setUserLegacy(user: User): String {
        val sessionId = utility.randomString(LENGTH_OF_SESSION_ID, VALID_COOKIE_CHARS)
        val success = databaseController.user_setSession(sessionId, UserSession.LoginType.LINK, user)
        return if (success) sessionId
        else ""
    }

    fun generateAndSetUpUserAccessToken(
        userId: Long
    ): String {
        try {
            var jwt = jwtManager.generateAccessToken(userId.toString())
            val isUpdated=setUserJWTtoRedis(userId, jwt)
            if(isUpdated==0){
                jwt=databaseController.getExistingToken(userId.toInt(),config.deviceIdForTwt)
            }
            return jwt
        } catch (e: Throwable) {
            throw e
        }
    }

    private fun setUserJWTtoRedis(userId: Long, newToken: String): Int {
        val allowedLogins = getAllowedLoginsByProduct(userId)
        var checkResult=false
        if (allowedLogins == -1) {
            return -1
        }

        val currentUserLogins = databaseController.getCurrentUserLogins(userId)
        if (currentUserLogins <= allowedLogins - 1) {

            if(config.deviceIdForTwt.isNotEmpty() && !databaseController.checkExistingDeviceId(userId.toInt(),config.deviceIdForTwt)){
                databaseController.setNextUserLogin(userId, newToken, config.deviceIdForTwt)
                return 0
            }else if(config.deviceIdForTwt.isEmpty()){
                databaseController.setNextUserLogin(userId, newToken, config.deviceIdForTwt)
                return 1
            }

        } else if (currentUserLogins == allowedLogins) {
            logger.info("Current User Logins $currentUserLogins")
            val nextLoggedUser = databaseController.getNextOneLoginFromSignOutList(userId)
            if (!nextLoggedUser.isNullOrBlank()) {
                cacheDatabase.setUserLoginToBlackList(UserJWTPairing(userId.toString(), nextLoggedUser))
                if (databaseController.getPartnerNameByUserId(userId.toInt()) == "twt") {
                    logger.info("partner is twt and userId is $userId")
                    if (!databaseController.getDeviceForTwtOnly(userId.toInt(), config.deviceIdForTwt)) {
                        checkResult= databaseController.setUserLoginToSignOutList(userId, newToken,config.deviceIdForTwt)
                    }
                } else {
                    checkResult= databaseController.setUserLoginToSignOutList(userId, newToken,"")
                }

            }
        }
        if (checkResult){
            return 1
        }
        return 0
    }

    private fun getAllowedLoginsByProduct(userId: Long): Int {
        val subscriptionType = databaseController.getUserSubscriptionType(userId)
        if (subscriptionType.isEmpty()) {
            return -1
        }

        var maxLoginNumber = config.maxLoginNumber
        val productNumber = config.productsNumber
        val subscriptionNumber = subscriptionType.size

        // when there are kidjo_books AND kidjo_tv AND kidjo_tv_books
        if (subscriptionNumber > productNumber) {
            maxLoginNumber *= productNumber
        }

        // when there are (kidjo_books and kidjo_tv) OR (kidjo_tv and kidjo_tv_books) OR (kidjo_books and kidjo_tv_books)
        if (subscriptionNumber == productNumber) {
            maxLoginNumber *= productNumber
        }

        // when there is only one product - the premium one kidjo_tv_books
        if ((subscriptionNumber == productNumber - 1
                    && subscriptionType.contains(SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS.raw))
        ) {
            maxLoginNumber *= productNumber
        }

        return maxLoginNumber
    }

    fun signOutUser(userId: Long, call: ApplicationCall): Boolean {
        val authHeader = call.request.parseAuthorizationHeader()
        if (!(authHeader == null || authHeader !is HttpAuthHeader.Single || authHeader.authScheme != "Bearer")) {
            return try {
                cacheDatabase.setUserLoginToBlackList(UserJWTPairing(userId.toString(), authHeader.blob))
                databaseController.deleteUserJWT(userId, authHeader.blob)
                return true
            } catch (e: Exception) {
                false
            }
        }
        return false
    }

    fun setUserJwtToBlackList(userId: Long, call: ApplicationCall): Boolean {
        val authHeader = call.request.parseAuthorizationHeader()
        if (!(authHeader == null || authHeader !is HttpAuthHeader.Single || authHeader.authScheme != "Bearer")) {
            return try {
                cacheDatabase.setUserLoginToBlackList(UserJWTPairing(userId.toString(), authHeader.blob))
                return true
            } catch (e: Exception) {
                false
            }
        }
        return false
    }

    fun setUser(user: User, loginType: UserSession.LoginType, responseCookies: ResponseCookies): Boolean {
        val sessionId = utility.randomString(LENGTH_OF_SESSION_ID, VALID_COOKIE_CHARS)

        val success = databaseController.user_setSession(sessionId, loginType, user)
        if (success) {
            val temporal = GMTDate().plus(30 * 24 * 60 * 60 * 1000L) // Add 30 days
            responseCookies.append(
                Cookie(
                    USER_SESSION_ID_IN_COOKIES,
                    sessionId,
                    CookieEncoding.URI_ENCODING,
                    MAX_AGE_OF_SESSION_COOKIE,
                    temporal,
                    "kidjo.tv",
                    "/",
                    config.cookies_secureUserSessionCookies,
                    true
                )
            )
        }

        return success
    }

    fun logUserOut(responseCookies: ResponseCookies) {
        responseCookies.appendExpired(USER_SESSION_ID_IN_COOKIES, null, "/")
        responseCookies.appendExpired(USER_SESSION_ID_IN_COOKIES, "kidjo.tv", "/")
    }

    fun getVerifyAndFacebookUserInfo(user: User): Boolean {
        val encodedValue = URLEncoder.encode(user.authToken, "UTF-8")
        val url = URL("https://graph.facebook.com/me?access_token=$encodedValue")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "GET"
        val status = connection.responseCode
        if (status != 200) {
            connection.disconnect()
            return false
        }
        val input = BufferedReader(InputStreamReader(connection.inputStream))
        var inputLine: String? = input.readLine()
        val content = StringBuffer()
        while (inputLine != null) {
            content.append(inputLine)
            inputLine = input.readLine()
        }
        input.close()
        connection.disconnect()
        val json = content.toString().toJSON()
        if (json == null) return false

        val name = json.optString("name")
        val fbId = json.optString("id")
        user.name = name
        user.authId = fbId
        return true
    }

    fun getUserMondiaOperatorCookie(cookies: RequestCookies) =
        cookies[USER_SESSION_MONDIA_OPERATOR_IN_COOKIES, CookieEncoding.URI_ENCODING]

    fun setUserMondiaOperatorCookie(operator: String, responseCookies: ResponseCookies) {
        val temporal = GMTDate().plus(30 * 24 * 60 * 60 * 1000L) // Add 30 days
        responseCookies.append(
            Cookie(
                USER_SESSION_MONDIA_OPERATOR_IN_COOKIES,
                operator,
                CookieEncoding.URI_ENCODING,
                MAX_AGE_OF_SESSION_COOKIE,
                temporal,
                "kidjo.tv",
                "/",
                config.cookies_secureUserSessionCookies,
                true
            )
        )
    }
}
