package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.virgoLinkedUser
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.VirgoLinkedUser
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.DV_USERS_BASE_EMAIL
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.LENGTH_OF_DV_ACCOUNT_NAME
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction

fun DatabaseController.createDVPairedUser(
    operationId: String,
    virgoAlias: String,
    subscriptionToken: String,
    subscriptionId: Long? = null,
    userId: Long? = null,
    offerId: Long? = null,
    packageId: Long? = null,
    correlationIdString: String? = null,
    subStatus: String? = null
): Bo<PERSON>an {

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT INTO virgo_user_links(ope_id, virgo_alias, subscription_token, sub_status, subscription_id, user_id, offer_id, package_id, correlation_id ) VALUES (?,?,?,?,?,?,?,?,?)")
    statement.setString(1, operationId)
    statement.setString(2, virgoAlias)
    statement.setString(3, subscriptionToken)
    statement.setString(4, subStatus ?: SubscriptionStatusType.NONE.raw)

    if (subscriptionId != null) statement.setLong(5, subscriptionId) else statement.setNull(5, java.sql.Types.NULL)
    if (userId != null) statement.setLong(6, userId) else statement.setNull(6, java.sql.Types.NULL)
    if (offerId != null) statement.setLong(7, offerId) else statement.setNull(7, java.sql.Types.NULL)
    if (packageId != null) statement.setLong(8, packageId) else statement.setNull(8, java.sql.Types.NULL)
    if (correlationIdString != null) statement.setString(9, correlationIdString) else statement.setNull(9, java.sql.Types.NULL)

    val results = statement.executeAndCheck()
    statement.close()
    connection.close()

    return results
}

fun DatabaseController.updateDVPairedSubscriptionStatus(subStatus: String?=null, subToken: String, correlationId: String? = null): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE virgo_user_links SET sub_status = ? , correlation_id =? WHERE subscription_token = ? " )
    statement.setString(1, subStatus ?: SubscriptionStatusType.NONE.raw)
    if(correlationId != null){
        statement.setString(2, correlationId)
    } else statement.setNull(2, java.sql.Types.NULL)
    statement.setString(3, subToken)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}
fun DatabaseController.getDVPairedUserByAlias(alias: String): VirgoLinkedUser? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT *  FROM virgo_user_links WHERE  virgo_alias = ? LIMIT 1")
    statement.setString(1, alias)

    val results = statement.executeQuery()

    val virgoLinkedUser: VirgoLinkedUser? = if (results.next()) objectCreator.virgoLinkedUser(results) else null

    statement.close()
    connection.close()
    return virgoLinkedUser
}
fun DatabaseController.getDVPairedUserBySubToken(subToken: String): VirgoLinkedUser? {
    if (subToken.isBlank()) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM virgo_user_links WHERE subscription_token = ? LIMIT 1")
    statement.setString(1, subToken)

    val results = statement.executeQuery()

    val virgoLinkedUser: VirgoLinkedUser? = if (results.next()) objectCreator.virgoLinkedUser(results) else null

    statement.close()
    connection.close()
    return virgoLinkedUser
}

fun DatabaseController.getDVPairedUserByMsIsdn(email: String?): Long {

    val updatedEmail = email!!.replace("+", "0")?.take(LENGTH_OF_DV_ACCOUNT_NAME) + DV_USERS_BASE_EMAIL

    return transaction {
        Users
            .select { Users.email.eq(updatedEmail) }
            .mapNotNull { it[Users.id] }
            .singleOrNull() ?: 0 // Return 0 if no user is found
    }
}

fun DatabaseController.getDVPairedSubscriptionByIdOrUserId(subId: Long? = null, userId: Long? = null): VirgoLinkedUser? {
    if (subId == SubscriptionRoot.NO_ID && userId == User.NO_SERVER_ID ) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM virgo_user_links WHERE subscription_id = COALESCE(?, subscription_id) AND user_id = COALESCE(?, user_id)  ORDER BY created DESC LIMIT 1 ")
    statement.setObject(1, subId)
    statement.setObject(2, userId)
    val results = statement.executeQuery()

    val virgoLinkedUser: VirgoLinkedUser? = if (results.next()) objectCreator.virgoLinkedUser(results) else null

    statement.close()
    connection.close()
    return virgoLinkedUser
}

fun DatabaseController.getDVPairedSubscriptionToken(subToken: String): String? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT subscription_token FROM virgo_user_links WHERE subscription_token = ? LIMIT 1")
    statement.setString(1, subToken)

    val results = statement.executeQuery()
    val subscriptionId = if (results.next()) results.getString("subscription_token")
    else null

    statement.close()
    connection.close()
    return subscriptionId
}
fun DatabaseController.getSubscriptionIdByOrangeAlias(alias: String): Long? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT s.id FROM virgo_user_links " +
                "JOIN subscriptions_root s ON s.id = virgo_user_links.subscription_id " +
                "WHERE virgo_alias = ? " +
                "AND s.isActive = TRUE " +
                "AND s.isRenewing = TRUE " +
                "ORDER BY s.created_at DESC LIMIT 1 ")
    statement.setString(1, alias)
    val results = statement.executeQuery()
    val subscriptionId = if (results.next()) results.getLong("s.id")
    else null

    statement.close()
    connection.close()
    return subscriptionId
}
fun DatabaseController.getDVPairedCorrelationIdBySubToken(subToken: String): String? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT correlation_id FROM virgo_user_links WHERE subscription_token = ? LIMIT 1")
    statement.setString(1, subToken)

    val results = statement.executeQuery()
    val correlationId = if (results.next()) results.getString("correlation_id") else null

    statement.close()
    connection.close()
    return correlationId
}
fun DatabaseController.getVirgoAliasByUserId(userId: Long): String? {
    if (userId == User.NO_SERVER_ID) return null
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT virgo_alias FROM virgo_user_links WHERE user_id = ? LIMIT 1")
    statement.setLong(1, userId)

    val results = statement.executeQuery()
    val alias = if (results.next()) results.getString("virgo_alias")
    else null

    statement.close()
    connection.close()
    return alias
}

fun DatabaseController.getSenditoEmailStatusBySubToken(subToken: String): Int? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT send_email FROM virgo_user_links WHERE  subscription_token = ? LIMIT 1")
    statement.setString(1, subToken)

    val results = statement.executeQuery()
    val sendEmail = if (results.next()) results.getInt("send_email")
    else null

    statement.close()
    connection.close()
    return sendEmail
}

fun DatabaseController.updateSenditoEmailStatus(alias: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE virgo_user_links SET send_email = 1 WHERE  virgo_alias = ? ")
    statement.setString(1, alias)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.linkUserToVirgoSubscription(userId: Long, alias: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE virgo_user_links SET user_id = ? WHERE virgo_alias = ? ")
    statement.setLong(1, userId)
    statement.setString(2, alias)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.deleteLinkedUserBySubToken(subToken: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("DELETE FROM virgo_user_links WHERE subscription_token = ? ")
    statement.setString(1, subToken)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}
