package net.kidjo.server.shared.cache

import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.common.models.Language
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.models.LanguageLocale
import net.kidjo.server.shared.tools.Config
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

class LanguageCache(private val config: Config,
                    private val databaseController: DatabaseController) {

    private val languages = Language.getArrayOfAllLanguages()
    private val defaultLanguageLocales = List(languages.size) { i ->
        Locale(languages[i].shortName)
    }


    fun getLanguages(): Array<Language> {
        val lang = languages
        return lang.copyOf()
    }

    fun getLanguageJSON(activeLanguageId: Int, jsonCreator: LanguageJsonCreator): JSONArray {
        val json = JSONArray()
        val lang = languages
        lang.forEach { json.put(jsonCreator.language_toJSON(it, it.id == activeLanguageId)) }
        return json
    }

    fun getLanguageIdWithShortName(shortName: String): Int {
        val lang = languages
        lang.forEach { if (it.shortName == shortName) return it.id }
        return Language.NO_ID
    }

    fun languageIsSupported(languageId: Int): Boolean {
        val lang = languages
        lang.forEach { if (it.id == languageId) return true }
        return false
    }

    fun get(fromCall: ApplicationCall): Language {
        return getLanguageFromAcceptLanguageHeader(fromCall.request.acceptLanguage())
    }

    fun getLanguageLocale(fromCall: ApplicationCall): LanguageLocale {
        val acceptLanguage = fromCall.request.acceptLanguage()
        if (acceptLanguage == null) return LanguageLocale(config.locale_default, Language.ENGLISH)

        val locales = Locale.LanguageRange.parse(acceptLanguage)
        val locale = Locale.lookup(locales, defaultLanguageLocales)
        val firstLocale: Locale
        if (locales.size > 0)
            firstLocale = Locale.forLanguageTag(locales[0].range) ?: config.locale_default
        else
            firstLocale = config.locale_default

        languages.forEach { if (locale != null && it.shortName == locale.language) return LanguageLocale(firstLocale, it) }

        return LanguageLocale(firstLocale, Language.ENGLISH)
    }

    fun getLanguageIdFromAcceptLanguageHeader(acceptLanguage: String): Int {
        val locales = Locale.LanguageRange.parse(acceptLanguage)
        val locale = Locale.lookup(locales, defaultLanguageLocales)
        languages.forEach { if (locale != null && it.shortName == locale.language) return it.id }
        return config.defaultLanguageId
    }

    fun getLanguageFromAcceptLanguageHeader(acceptLanguage: String?): Language {
        if (acceptLanguage == null) return languages[0]

        val locales = Locale.LanguageRange.parse(acceptLanguage)
        val locale = Locale.lookup(locales, defaultLanguageLocales)
        languages.forEach { if (locale != null && it.shortName == locale.language) return it }
        return languages[0]
    }

    fun getLanguageCodeFromId(languageId:Int):String?{
        return languages.filter { it.id == languageId }.mapNotNull { it.shortName }.singleOrNull()
    }

}
interface LanguageJsonCreator {
    fun language_toJSON(language: Language, isActive: Boolean): JSONObject
}
