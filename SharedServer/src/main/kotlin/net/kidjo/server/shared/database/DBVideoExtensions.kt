package net.kidjo.server.shared.database

import net.kidjo.common.models.Folder
import net.kidjo.common.models.LanguageSimple
import net.kidjo.server.shared.database.creator.languages
import net.kidjo.server.shared.database.creator.video
import net.kidjo.server.shared.database.creator.videoSimple
import net.kidjo.server.shared.models.Video
import net.kidjo.server.shared.models.VideoSimple
import java.sql.SQLException

fun DatabaseController.videos_getListFromFolder(cardId: Long, limit: Int = 12): List<Video> {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT id,title,isPremium,ageMin,ageMax,duration,compile,GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id JOIN folders_videos ON folders_videos.videoId = videos.id WHERE folderId = ? AND videos.isActive = 1 GROUP BY videos.id ORDER BY folders_videos.order ASC LIMIT ?")
    try {
        statement.setLong(1, cardId)
        statement.setInt(2, limit)

        statement.fetchSize = kotlin.math.min(limit, config.db_maxFetch)

        val results = statement.executeQuery()
        val videos = ArrayList<Video>()
        while (results.next()) videos.add(objectCreator.video(results))

        return videos
    } catch (e: SQLException) {
        println("Error getting: videos_getListFromFolder ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.getVideoListFromFolder(folderId: Long): List<Video> {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT id,title,isPremium,ageMin,ageMax,duration,compile,GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id JOIN folders_videos ON folders_videos.videoId = videos.id WHERE folderId = ? AND videos.isActive = 1 GROUP BY videos.id ")
    statement.setLong(1, folderId)

    val results = statement.executeQuery()
    val videos = ArrayList<Video>()
    while (results.next()) videos.add(objectCreator.video(results))

    statement.close()
    connection.close()
    statement.close()
    connection.close()
    return videos
}

fun DatabaseController.getVideoFromFolder(folderId: Long): Video? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT id,title,isPremium,ageMin,ageMax,duration,compile,creation,GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id JOIN folders_videos ON folders_videos.videoId = videos.id WHERE folderId = ? AND videos.isActive = 1 AND videos.duration <> 0 GROUP BY videos.id LIMIT 1")
    statement.setLong(1, folderId)

    val results = statement.executeQuery()
    val video: Video? = if (results.next()) objectCreator.video(results) else null

    statement.close()
    connection.close()
    return video
}

fun DatabaseController.videos_get(id: Long): Video? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT id,title,isPremium,ageMin,ageMax,duration,compile, GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id WHERE videos.id = ? AND videos.isActive = 1 GROUP BY videos.id  LIMIT 1")
    statement.setLong(1, id)

    val results = statement.executeQuery()
    val video: Video?

    if (results.next()) video = objectCreator.video(results)
    else video = null

    statement.close()
    connection.close()

    return video
}

fun DatabaseController.videos_getAll(): List<VideoSimple> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT id FROM videos order by id DESC")

    val results = statement.executeQuery()
    val videos = ArrayList<VideoSimple>()
    while (results.next()) videos.add(objectCreator.videoSimple(results))

    statement.close()
    connection.close()
    return videos

}

fun DatabaseController.videoSearch(
    search: String, languageId: Int,
    countryId: Int,
    age: Int,
    folderType: Folder.ContentType,
    folderFilter: String,
    version4: Boolean = false
): List<Video> {
    val connection = dataSource.connection

    var folderFilterArray = emptyList<String>()

    val searchTerms = if (search.isEmpty()) emptyList() else search.split(" ")

    // Base SQL request
    var sql =
        "SELECT videos.id, videos.title, videos.isPremium, videos.ageMin, videos.ageMax, videos.duration, videos.compile, GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize" +
                " FROM videos" +
                " JOIN videos_formats ON videos_formats.videoId = videos.id" +
                " JOIN folders_videos ON folders_videos.videoId = videos.id" +
                " JOIN folders ON folders_videos.folderId = folders.id" +
                " JOIN folders_countries ON folders.id = folders_countries.folderId" +
                " WHERE folders.isActive = 1 AND videos.isActive = 1 " +
                " AND folders.languageId IN (0, ?)" +
                " AND folders_countries.countryId IN (0, ?)" +
                " AND videos.ageMin <= ?" +
                " AND videos.ageMax >= ?"

    if (folderFilter.isNotEmpty()) {
        sql += " AND folders_videos.folderId NOT IN ($folderFilter) "
    }

    (if (folderType != Folder.ContentType.MIXED) sql += " AND folders.type = ?")

    if (searchTerms.isNotEmpty()) {
        repeat(searchTerms.size) { sql += " AND videos.title COLLATE UTF8_GENERAL_CI LIKE ? " }
    }

    sql += (if (version4) " GROUP BY videos.id " else "GROUP BY videos.id, folders_videos.order")
    sql += " ORDER BY folders_videos.order ASC"
    // Prepare SQL statement with parameters
    val statement = connection.prepareStatement(sql)
    try {

        statement.setInt(1, languageId)
        statement.setInt(2, countryId)
        statement.setInt(3, age)
        statement.setInt(4, age)
        var index = 5

        if (folderType != Folder.ContentType.MIXED) {
            statement.setString(index, folderType.raw)
            index++
        }

        folderFilterArray.forEach { value ->
            statement.setString(index, value)
            index++
        }

        searchTerms.forEach { term ->
            statement.setString(index, "%$term%")
            index++
        }

        val results = statement.executeQuery()
        val videos = ArrayList<Video>()
        while (results.next()) videos.add(objectCreator.video(results))

        return videos
    } catch (e: SQLException) {
        println("Error getting: videoSearch ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.videosGetFirstFromFolder(cardId: Long, premium: Boolean): Video? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT id, title, isPremium, ageMin, ageMax, duration, compile, GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize" +
                " FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id" +
                " JOIN folders_videos ON folders_videos.videoId = videos.id" +
                " WHERE folderId = ?" +
                " AND videos.isActive = 1" +
                " AND videos.isPremium = ?" +
                " GROUP BY videos.id" +
                " ORDER BY folders_videos.order ASC LIMIT 1"
    )
    statement.setLong(1, cardId)
    statement.setBoolean(2, premium)

    statement.fetchSize = kotlin.math.min(1, config.db_maxFetch)

    val results = statement.executeQuery()
    val video = if (results.next()) objectCreator.video(results) else null

    statement.close()
    connection.close()
    return video
}

fun DatabaseController.getLanguagesByActiveVideos(): List<LanguageSimple> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT l.name, l.short, v.isActive " +
                " FROM videos v " +
                " JOIN languages l on v.languageId = l.id " +
                " WHERE v.isActive = TRUE " +
                " GROUP BY v.languageId "
    )
    try {
        val results = statement.executeQuery()
        val videos = ArrayList<LanguageSimple>()
        while (results.next()) videos.add(objectCreator.languages(results))

        statement.close()
        connection.close()

        return videos
    } catch (e: SQLException) {
        println("Error getting: getLanguagesByActiveVideos ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.videoSearchV5(
    fetchLimit: Int, skip: Int,
    search: String, languageId: Int,
    countryId: Int,
    age: Int,
    folderType: Folder.ContentType,
    folderFilter: String,
    version4: Boolean = false
): List<Video> {
    val connection = dataSource.connection

    var folderFilterArray = emptyList<String>()
    var folderFilterAsQuery = ""
    if (folderFilter.isNotEmpty()) {
        // Prepare folder filters query
        folderFilterArray = folderFilter.split(',')
        folderFilterArray.forEach { value ->
            folderFilterAsQuery += "?,"
        }
        if (folderFilterAsQuery != "") {
            folderFilterAsQuery = " AND folders_videos.folderId NOT IN(" + folderFilterAsQuery.substring(
                0,
                folderFilterAsQuery.length - 1
            ) + ")"
        }
    }
    val searchTerms = if (search.isEmpty()) emptyList() else search.split(" ")

    // Base SQL request
    var sql =
        "SELECT videos.id, videos.title, videos.isPremium, videos.ageMin, videos.ageMax, videos.duration, videos.compile, GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize" +
                " FROM videos" +
                " JOIN videos_formats ON videos_formats.videoId = videos.id" +
                " JOIN folders_videos ON folders_videos.videoId = videos.id" +
                " JOIN folders ON folders_videos.folderId = folders.id" +
                " JOIN folders_countries ON folders.id = folders_countries.folderId" +
                " WHERE folders.isActive = 1 AND videos.isActive = 1 " +
                " AND folders.languageId IN (0, ?)" +
                " AND folders_countries.countryId IN (0, ?)" +
                " AND videos.ageMin <= ?" +
                " AND videos.ageMax >= ?" +
                folderFilterAsQuery

    (if (folderType != Folder.ContentType.MIXED) sql += " AND folders.type = ?")

    if (searchTerms.isNotEmpty()) {
        repeat(searchTerms.size) { sql += " AND videos.title COLLATE UTF8_GENERAL_CI LIKE ? " }
    }

    sql += (if (version4) " GROUP BY videos.id " else "GROUP BY videos.id, folders_videos.order")
    sql += " ORDER BY folders_videos.order ASC"
    // Prepare SQL statement with parameters
    val statement = connection.prepareStatement(sql)
    try {

        statement.setInt(1, languageId)
        statement.setInt(2, countryId)
        statement.setInt(3, age)
        statement.setInt(4, age)
        var index = 5

        if (folderType != Folder.ContentType.MIXED) {
            statement.setString(index, folderType.raw)
            index++
        }

        folderFilterArray.forEach { value ->
            statement.setString(index, value)
            index++
        }

        searchTerms.forEach { term ->
            statement.setString(index, "%$term%")
            index++
        }
        statement.fetchSize = kotlin.math.min(fetchLimit, config.db_maxFetch)
        val results = statement.executeQuery()
        val videos = ArrayList<Video>()
        results.absolute(skip)
        while (results.next()) videos.add(objectCreator.video(results))

        return videos
    } catch (e: SQLException) {
        println("Error getting: videoSearch ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}