package net.kidjo.server.shared.models

import com.fasterxml.jackson.annotation.JsonIgnore

data class FilteredSearchAccountModel(
    var countries: List<CountryTypeModel>? = null,
    var partners: List<PartnerTypeModel>? = null,
    var products: List<ProductTypeModel>? = null,
    var couponTypes: List<CouponTypeModel>? = null,
    var subMethods: List<SubMethodTypeModel>? = null,
    val subStatuses: List<SubStatusModel.Status>? = null,
    var reportTypes: List<ReportTypeModel>? = null
){
    @JsonIgnore
    var countryIds: String? = null
    @JsonIgnore
    var partnerIds: String? = null
    @JsonIgnore
    var couponTypeIds: String? = null
}
data class ReportTypeModel(
    val name: String,
    val display: String
)
data class CountryTypeModel(
    val id: Long,
    val name: String,
    val code: String
)
data class PartnerTypeModel(
    val id: Long,
    val name: String,
    val description: String?=null
)
data class ProductTypeModel(
    val name: String
)

data class SubMethodTypeModel(
    var id: String,
    var name: String,
    val description: String?=null
)
data class SubPaymentTypeModel(
    var id: String,
    var name: String,
    val description: String?=null
)
data class SubTypeModel(
    var id: String,
    var name: String,
    val description: String?=null
)
data class SubStatusModel(
    val id: Long,
    val name: Status,
    val description: String?=null
) {
    enum class Status(val raw: String?) {
        ACTIVE("true"),
        INACTIVE("false"),
        NONE(null)
        ;
        companion object {
            fun fromRaw(raw: String?): Status =
                when (raw) {
                    ACTIVE.raw, "1" -> ACTIVE
                    INACTIVE.raw, "0" -> INACTIVE
                    null -> NONE
                    else -> NONE
                }
            fun fromName(name: String): Boolean? =
                when (name.trim().toUpperCase().capitalize()) {
                    ACTIVE.name -> true
                    INACTIVE.name -> false
                    NONE.name -> null
                    else -> null
                }
        }
    }
}
data class CouponTypeModel(
    val id: Long,
    val name: String,
    val description: String?=null
)

enum class CouponStatus(val raw: String) {
    PENDING_ACTIVATION("Pending"),
    ACTIVE("Active"),
    EXPIRED("Expired"),
    INVALID("Invalid")
    ;

    companion object {
        fun fromRaw(raw: String): CouponStatus {
            return when (raw.trim().toUpperCase().capitalize()) {
                PENDING_ACTIVATION.name -> PENDING_ACTIVATION
                ACTIVE.name -> ACTIVE
                EXPIRED.name -> EXPIRED
                INVALID.name -> INVALID
                else -> INVALID
            }
        }
    }
}