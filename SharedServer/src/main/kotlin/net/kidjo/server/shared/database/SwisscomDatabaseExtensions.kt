package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.subscription
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import java.time.LocalDate

fun DatabaseController.swisscomLinkWithAbid(deviceId: Long, swisscomAbid: String, userId: Long): Boolean {
    if (deviceId == User.NO_SERVER_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT swisscom_links(deviceId, swisscomAbid, userId) VALUES (?,?,?)")
    statement.setLong(1, deviceId)
    statement.setString(2, swisscomAbid)
    statement.setLong(3, userId)

    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}

fun DatabaseController.swisscomGetAbidWithDeviceId(deviceId: Long): String? {
    if (deviceId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT swisscomAbid FROM swisscom_links WHERE deviceId = ? LIMIT 1")
    statement.setLong(1, deviceId)

    val results = statement.executeQuery()
    val abid = if (results.next()) results.getString("swisscomAbid")
    else null

    statement.close()
    connection.close()
    return abid
}

fun DatabaseController.swisscomGetAbidWithUserId(userId: Long): String? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT swisscomAbid FROM swisscom_links WHERE userId = ? LIMIT 1")
    statement.setLong(1, userId)

    val results = statement.executeQuery()
    val abid = if (results.next()) results.getString("swisscomAbid")
    else null

    statement.close()
    connection.close()
    return abid
}

fun DatabaseController.swisscomGetUserIdWithAbid(abid: String): Long? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT userId FROM swisscom_links WHERE swisscomAbid = ? ORDER BY 'timestamp' DESC LIMIT 1")
    statement.setString(1, abid)

    val results = statement.executeQuery()
    val userId = if (results.next()) results.getLong("userId")
    else null

    statement.close()
    connection.close()

    return userId
}



fun DatabaseController.swisscomGetRenewableSubscriptions(nextBillDate: LocalDate): List<SubscriptionRoot> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM subscriptions_root WHERE storeId = ? AND isRenewing = 1 AND nextBillDate <= ? ORDER BY created_at")
    statement.setString(1, Device.StorePlatform.SWISSCOM.raw)
    statement.setString(2, nextBillDate.format(mysqlDateFormatter))

    val results = statement.executeQuery()
    val subscriptions = mutableListOf<SubscriptionRoot>()
    while (results.next()) {
        subscriptions.add(objectCreator.subscription(results))
    }

    statement.close()
    connection.close()
    return subscriptions
}

fun DatabaseController.swisscomGetActiveNonRenewingSubscription(nextBillDate: LocalDate): List<SubscriptionRoot> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM subscriptions_root WHERE storeId = ? AND isActive = 1 AND isRenewing = 0 AND nextBillDate <= ? ORDER BY created_at")
    statement.setString(1, Device.StorePlatform.SWISSCOM.raw)
    statement.setString(2, nextBillDate.format(mysqlDateFormatter))

    val results = statement.executeQuery()
    val subscriptions = mutableListOf<SubscriptionRoot>()
    while (results.next()) {
        subscriptions.add(objectCreator.subscription(results))
    }

    statement.close()
    connection.close()
    return subscriptions
}

fun DatabaseController.swisscomUpdateSubscriptionEnd(subscriptionId: Long, nextBillDate: LocalDate): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET nextBillDate = ?, lastCheckDate = ? WHERE id = ?")
    statement.setString(1, nextBillDate.format(mysqlDateFormatter))
    statement.setString(2, LocalDate.now().format(mysqlDateFormatter))
    statement.setLong(3, subscriptionId)

    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}
