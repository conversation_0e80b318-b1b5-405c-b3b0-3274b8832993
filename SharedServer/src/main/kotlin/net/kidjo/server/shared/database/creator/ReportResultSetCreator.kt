package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.database.AccountDTO
import net.kidjo.server.shared.database.AccountDetailsDTO
import net.kidjo.server.shared.database.AccountSearchDTO
import net.kidjo.server.shared.database.CouponDTO
import net.kidjo.server.shared.database.SubscriptionDTO
import net.kidjo.server.shared.database.SubscriptionListEntryDTO
import net.kidjo.server.shared.models.*
import org.slf4j.LoggerFactory
import java.sql.ResultSet
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

private val logger = LoggerFactory.getLogger("ReportResultSetCreator")

fun ResultSetObjectCreator.userSubscriptionView(resultSet: ResultSet): UserSubscriptionView {
    var iapPrice = ""
    var iapPlan = ""
    val userId = resultSet.getLong("id_user")
    val registeredDate = resultSet.getString("registered_date")
    val name = resultSet.getString("name")
    val email = resultSet.getString("email")
    var country = ""
    if (resultSet.getString("country") != null) {
        country = resultSet.getString("country")
    }
    var storeId: Device.StorePlatform = Device.StorePlatform.KIDJO_BRAINTREE
    if (resultSet.getString("storeId") != null) {
        storeId = Device.StorePlatform.fromRow(resultSet.getString("storeId"))
    }
    var subscriptionType: SubscriptionRoot.SubscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV
    if (resultSet.getString("subscriptionType") != null) {
        subscriptionType = SubscriptionRoot.SubscriptionType.fromRaw(resultSet.getString("subscriptionType"))
    }
    val subId = resultSet.getLong("id_sub")
    val couponId = resultSet.getLong("id_coupon")
    var couponNumber = ""
    if (resultSet.getString("couponId") != null) {
        couponNumber = resultSet.getString("couponId")
    }
    var groupId = ""
    if (resultSet.getString("groupId") != null) {
        groupId = resultSet.getString("groupId")
    }
    var nextBillDate = ""
    if (resultSet.getString("nextBillDate") != null) {
        nextBillDate = resultSet.getString("nextBillDate")
    }
    var iapId = ""
    if (resultSet.getString("iapId") != null) {
        iapId = resultSet.getString("iapId")
    }

    val iap = iapManager.getIAP(storeId, iapId)
    if (iap != null) {
        iapPrice = iap.price.toString()
        iapPlan = iap.id
    }
    var subCreatedDate = ""
    if (resultSet.getString("createdDate") != null) {
        subCreatedDate = resultSet.getString("createdDate")
    }

    var couponType = ""
    if (resultSet.getString("id_type") != null) {
        couponType = resultSet.getString("id_type")
    }

    return UserSubscriptionView(
        userId, registeredDate, name, email, country,
        storeId.name,
        subscriptionType, subId, couponId, couponNumber, groupId,
        nextBillDate, iapPrice, iapPlan, iapId, subCreatedDate, couponType
    )
}

fun ResultSetObjectCreator.toFilteredSearchAccountMapper(
    storeIdsResultSet: ResultSet,
    couponsResultSet: ResultSet,
    countriesResultSet: ResultSet,
    subTypesResultSet: ResultSet,
    subStatusesResultSet: ResultSet,
    partnersResultSet: ResultSet,
): FilteredSearchAccountModel {

    val products = ArrayList<ProductTypeModel>()
    val partners = ArrayList<PartnerTypeModel>()
    val stores = ArrayList<String>()
    val subStatuses = ArrayList<SubStatusModel.Status>()
    val countries = ArrayList<CountryTypeModel>()
    val couponTypes = ArrayList<CouponTypeModel>()

    val results = FilteredSearchAccountModel(
        products = products,
        partners = partners,
        subStatuses =  subStatuses,
        countries = countries,
        couponTypes = couponTypes,
        subMethods = ArrayList()
    )

    while (storeIdsResultSet.next()) {
        val value = storeIdsResultSet.getString("value")
        stores.add(value)
    }

    while (subStatusesResultSet.next()) {
        val value = subStatusesResultSet.getString("value")
        subStatuses.add(SubStatusModel.Status.fromRaw(value))
    }

    while (subTypesResultSet.next()) {
        val value = subTypesResultSet.getString("value")
        products.add(ProductTypeModel(value))
    }

    while (countriesResultSet.next()) {
        val value = countriesResultSet.getString("value")
        if (!value.isNullOrEmpty() && value.contains("|||")){
        val row = value.split("|||")
        countries.add(CountryTypeModel(row[0].toLong(), row[2], row[1]))
            }
    }

    while (partnersResultSet.next()) {
        val value = partnersResultSet.getString("value")
        if (!value.isNullOrEmpty() && value.contains("|||")) {
            val row = value.split("|||")
            partners.add(PartnerTypeModel(row[0].toLong(), row[1], row[2]))
        }
    }

    while (couponsResultSet.next()) {
        val value = couponsResultSet.getString("value")
        if (!value.isNullOrEmpty() && value.contains("|||")) {
            val row = value.split("|||")
            couponTypes.add(CouponTypeModel(row[0].toLong(), row[1], row[2]))
        }
    }

    results.subMethods = utility.getSubMethods(stores.joinToString(","))

    return results
}

fun ResultSetObjectCreator.toCountryTypeMapper(resultSet: ResultSet): CountryTypeModel {
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val code = resultSet.getString("short")
    return CountryTypeModel(id, name, code)
}

fun ResultSetObjectCreator.toProductTypeMapper(resultSet: ResultSet): ProductTypeModel {
    val name = resultSet.getString("name")
    return ProductTypeModel(name)
}

fun ResultSetObjectCreator.toPartnerTypeMapper(resultSet: ResultSet): PartnerTypeModel {
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val description = resultSet.getString("description")
    return PartnerTypeModel(id, name, description)
}

fun ResultSetObjectCreator.toCouponTypeMapper(resultSet: ResultSet): CouponTypeModel {
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val description = resultSet.getString("description")
    return CouponTypeModel(id, name, description)
}

fun ResultSetObjectCreator.toAccountSearchMapper(resultSet: ResultSet): AccountSearchDTO {
    val accountId = resultSet.getLong("id_account")
    val name = resultSet.getString("name")
    val email = resultSet.getString("email")
    val country = resultSet.getString("country")
    val subType = try {
        SubscriptionRoot.SubscriptionType.fromRaw(resultSet.getString("subscriptionType")).name
    } catch (_: Exception) {
        null
    }
    val couponType = try {
        resultSet.getString("type")
    } catch (_: Exception) {
        null
    }
    val couponPartner = try {
        resultSet.getString("partner")
    } catch (_: Exception) {
        null
    }
    val storeId = try {
        Device.StorePlatform.fromRow(resultSet.getString("storeId")).name
    } catch (_: Exception) {
        null
    }
    val subStatus = try {
        SubscriptionRoot.Status.fromRaw(resultSet.getString("subStatus")).name
    } catch (_: Exception) {
        null
    }

    val subCreatedDate = try {
        resultSet.getDate("createdDate").toLocalDate().toString()
    } catch (_: Exception) {
        null
    }

    val isTcAccepted = resultSet.getInt("is_tc_accepted")
    val isPromoAccepted = resultSet.getInt("is_promo_accepted")
    return AccountSearchDTO(
        accountId = accountId,
        name = name,
        email = email,
        country = country,
        subStatus = subStatus,
        subStartDate = subCreatedDate,
        subType = subType,
        subMethod = storeId,
        couponType = couponType,
        couponPartner = couponPartner,
        isTcAccepted,
        isPromoAccepted
    )
}

fun ResultSetObjectCreator.toAccountDetails(resultSet: ResultSet): AccountDetailsDTO {
    //Account
    val userId = resultSet.getLong("id_user")
    val registeredDate = try {
        resultSet.getDate("registered_date").toLocalDate().toString()
    } catch (_: Exception) {
        null
    }
    val name = resultSet.getString("name")
    val email = resultSet.getString("email")
    val country = resultSet.getString("country")
    val isTcAccepted = resultSet.getInt("is_tc_accepted")
    val isPromoAccepted = resultSet.getInt("is_promo_accepted")

    //Subscription
    val subId = resultSet.getLong("id_sub")
    val subscriptionType = try {
        SubscriptionRoot.SubscriptionType.fromRaw(resultSet.getString("subscriptionType")).name
    } catch (_: Exception) {
        null
    }
    val subStatus = try {
        SubscriptionRoot.Status.fromRaw(resultSet.getString("subStatus")).name
    } catch (_: Exception) {
        null
    }

    val subCreatedDate = try {
        resultSet.getDate("createdDate").toLocalDate().toString()
    } catch (_: Exception) {
        null
    }
    val nextBillDate = try {
        resultSet.getDate("nextBillDate").toLocalDate().toString()
    } catch (_: Exception) {
        null
    }
    val subMethod = try {
        Device.StorePlatform.fromRow(resultSet.getString("storeId")).name
    } catch (_: Exception) {
        null
    }
    val iapPrice = try {
        val storeId = Device.StorePlatform.fromRow(resultSet.getString("storeId"))
        val iapId = iapManager.getIAP(storeId, resultSet.getString("iapId"))
        if (iapId != null) String.format("%.2f\$", iapId.price) else "N/A"
    } catch (_: Exception) {
        println("error: getting iap price from acc details: N/A")
        "N/A"
    }
    //Coupon
    val couponId = resultSet.getLong("id_coupon")
    val couponNumber = resultSet.getString("couponId")
    val couponStatus = try {
        AccountCoupon.Status.fromRaw(resultSet.getString("couponStatus")).name
    } catch (_: Exception) {
        null
    }

    val couponType = try {
        resultSet.getString("type")
    } catch (_: Exception) {
        null
    }

    val couponStartDate = try {
        resultSet.getDate("couponStartDate").toLocalDate().toString()
    } catch (_: Exception) {
        null
    }

    return AccountDetailsDTO(
        account = if (userId != 0L) AccountDTO(
            id = userId,
            name = name,
            email = email,
            country = country,
            startDate = registeredDate,
            isPromoAccepted = isPromoAccepted,
            isTcAccepted = isTcAccepted
        ) else null,
        subscription = if (subId != 0L) SubscriptionDTO(
            id = subId,
            status = subStatus,
            type = subscriptionType,
            price = iapPrice,
            startDate = subCreatedDate,
            endDate = nextBillDate,
            method = subMethod
        ) else null,
        coupon = if (couponId != 0L) CouponDTO(
            id = couponId,
            couponId = couponNumber,
            status = couponStatus,
            type = couponType,
            startDate = couponStartDate
        ) else null
    )
}

fun ResultSetObjectCreator.userCouponsView(resultSet: ResultSet): UserCouponsView {
    var name: String? = null
    var email: String? = null
    var userId: String? = null
    val subscriptionType = try {
        userId = resultSet.getString("id_user")
        name = resultSet.getString("name")
        email = resultSet.getString("email")
        SubscriptionRoot.SubscriptionType.fromRaw(resultSet.getString("subscriptionType"))
    } catch (e: Exception) {
        logger.error("Error while mapping UserCouponsView", e)
        null
    }

    var couponType = ""
    if (resultSet.getString("id_type") != null) {
        couponType = resultSet.getString("id_type")
    }
    val groupId = resultSet.getString("groupId")
    val couponId = resultSet.getString("id_coupon")
    val couponNumber = resultSet.getString("couponId")
    val redeemedTimes = resultSet.getInt("redeemedTimes")
    var redeemed = false
    if (redeemedTimes != 0) {
        redeemed = true
    }

    val duration = resultSet.getString("duration")
    val subCreatedDate = resultSet.getTimestamp("createdDate").toLocalDateTime()

    var months = 0
    val freeTrialDate: LocalDateTime
    val freeTrialTime = utility.getDayMonthFromFreeTrialISO8601Code(duration)

    freeTrialDate = if (duration.contains("M")) {
        subCreatedDate.plusMonths(freeTrialTime)
    } else {
        subCreatedDate.plusDays(freeTrialTime)
    }
    val toDay = LocalDateTime.now()

    if (freeTrialDate.isAfter(toDay)) {
        months = ChronoUnit.MONTHS.between(subCreatedDate, freeTrialDate).toInt()
    }
    val partnerId = resultSet.getInt("id_partner").or(0)
    return UserCouponsView(
        userId, name, email, subscriptionType, groupId,
        couponId, couponNumber, months.toString(),
        subCreatedDate.toString(), redeemed, couponType, partnerId
    )
}

fun ResultSetObjectCreator.toSubscriptionListEntry(resultSet: ResultSet): SubscriptionListEntryDTO {
    val userId = resultSet.getLong("userId")
    val startDate = resultSet.getTimestamp("created_at").toLocalDateTime()
    val endDate = resultSet.getTimestamp("nextBillDate").toLocalDateTime()
    val iapId = resultSet.getString("iapId")
    val subscriptionType = resultSet.getString("subscriptionType")
    val isActive = resultSet.getBoolean("isActive")
    val storeId = Device.StorePlatform.fromRow(resultSet.getString("storeId"))
    val iap = iapManager.getIAP(storeId, iapId)
    var paymentPlanPrice = ""
    if (iap != null) {
        paymentPlanPrice = String.format("%.2f", iap.price)
    }
    return SubscriptionListEntryDTO(
        userId,
        startDate.toString(),
        endDate.toString(),
        subscriptionType,
        Device.StorePlatform.fromRow(storeId.raw).name,
        paymentPlanPrice,
        isActive
    )
}