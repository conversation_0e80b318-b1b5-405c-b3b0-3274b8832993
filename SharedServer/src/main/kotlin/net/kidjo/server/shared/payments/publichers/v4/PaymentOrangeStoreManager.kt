package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.OrangeApiManager
import net.kidjo.server.shared.payments.SubscriptionStatus
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import okhttp3.OkHttpClient

class PaymentOrangeStoreManager(
    config: Config,
    httpClient: OkHttpClient,
    encryptionController: EncryptionController,
    databaseController: DatabaseController,
    iapManager: IAPManager,
    private val orangeApiManager: OrangeApiManager,
    emailManager: EmailManager,
    languageCache: LanguageCache,
    userManager: UserManager

) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {


    override suspend fun start(
        userId: Long, inAppPurchase: InAppPurchase,
        token: String, purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        forceUpdate: Boolean,
        subscriptionId: String?,
        orderId: String?,
        call: ApplicationCall
    ) {

        when (inAppPurchase.store) {
            Device.StorePlatform.ORANGE -> {
                var checkSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(token)
                if (checkSubscription == null) {
                    return createSubscription(
                        inAppPurchase, token,
                        call, userId, purchasingSession, subscriptionType, subscriptionId, orderId
                    )
                } else {
                    if (checkSubscription?.userId != 0L && userId != checkSubscription?.userId) {
                        return call.respondConflict(
                            SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                            "This subscription already belongs to another account. Please login."
                        )
                    }
                    val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                    if (isExpired) {
                        return updateSubscription(
                            inAppPurchase, token,
                            call, userId, checkSubscription
                        )
                    } else {
                        return call.respondNoContent()
                    }
                }
            }
            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun createSubscription(
        inAppPurchase: InAppPurchase,
        token: String, call:
        ApplicationCall,
        userId: Long,
        purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        subscriptionId: String?,
        orderId: String?
    ) {
        var freeTrial: Boolean = true
        var priceToLog: Float = 0.0f

        val orangeSubscriptionPurchase = getOrangeReceipt(inAppPurchase, token, subscriptionType.raw, call)
        if (orangeSubscriptionPurchase != null) {
            val paymentStateId = orangeSubscriptionPurchase.paymentState


            if (paymentStateId == 0 || paymentStateId == 1) {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }

            val subscriptionRootInsert = SubscriptionRootInsert(
                userId, 0L, freeTrial, priceToLog,
                SubscriptionRoot.PaymentType.NATIVE, subscriptionType, inAppPurchase.store.raw,
                "orange", inAppPurchase.store, paymentStateId.toString(),
                inAppPurchase.id, purchasingSession, token, encryptionController.sha256Hash(token),
                0L, orangeSubscriptionPurchase.autoRenewing,
                orangeSubscriptionPurchase.expiryTimeMillis.epochMilliToLocalDateTime(), !config.env.isLive
            )

            insert(subscriptionRootInsert, call)
        }
    }

    override suspend fun updateSubscription(
        inAppPurchase: InAppPurchase,
        token: String,
        call: ApplicationCall,
        userId: Long,
        subscriptionRoot: SubscriptionRoot
    ) {

        var freeTrial: Boolean = true
        var priceToLog: Float = 0.0f
        val orangeSubscriptionPurchase = getOrangeReceipt(
            inAppPurchase, token,
            subscriptionRoot.subscriptionType.raw, call
        )

        if (orangeSubscriptionPurchase != null) {

            val paymentStateId = orangeSubscriptionPurchase.paymentState

            if (paymentStateId == 0 || paymentStateId == 1) {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }

            val subscriptionRootUpdate = SubscriptionRootUpdate(
                userId, orangeSubscriptionPurchase.autoRenewing,
                orangeSubscriptionPurchase.expiryTimeMillis.epochMilliToLocalDateTime(),
                "orange",
                freeTrial, priceToLog, subscriptionRoot.id, paymentStateId.toString()
            )

            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)
        }
    }

    suspend fun getOrangeReceipt(
        inAppPurchase: InAppPurchase, token: String, subscriptionType: String,
        call: ApplicationCall
    ): SubscriptionStatus? {
        return try {
            orangeApiManager.getSubscriptionStatus(token, subscriptionType, inAppPurchase.id, call)
        } catch (e: Exception) {
            logger.error("cant find Orange subscription of $inAppPurchase, token: ${token.take(10)}")
            return null
        }
    }
}
