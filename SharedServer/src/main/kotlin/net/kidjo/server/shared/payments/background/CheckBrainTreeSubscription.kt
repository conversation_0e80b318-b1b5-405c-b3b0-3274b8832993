package net.kidjo.server.shared.payments.background

import com.braintreegateway.Subscription
import com.braintreegateway.exceptions.NotFoundException
import com.braintreegateway.exceptions.UnexpectedException
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.text.SimpleDateFormat
import java.time.LocalDateTime


fun PaymentManager.checkBrainTreeSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val braintreeSub: Subscription?
    try {
        braintreeSub = brainTreeGateway.subscription().find(subscriptionRoot.subscriptionToken)
    } catch (e: NotFoundException) {
        throw e
    } catch (ex: UnexpectedException) {
        throw ex
    }

    if (braintreeSub == null) {
        throw SubscriptionCheckException("Subscription was found but was null with brain trees gateway")
    }

    val status: SubscriptionCheckResult.Status
    var priceUSD: Float
    var transactionId: String? = null
    var createdTransaction: String? = null
    var billingPeriodEndDate: String? = null
    var paidThroughDate: String? = null
    val format = SimpleDateFormat("yyyy-MM-dd")

    val nextBillDate: LocalDateTime =
        LocalDateTime.ofInstant(
            braintreeSub.nextBillingDate.toInstant(),
            braintreeSub.nextBillingDate.timeZone.toZoneId()
        )

    when (braintreeSub.status) {
        Subscription.Status.ACTIVE -> {
            status = SubscriptionCheckResult.Status.ACTIVE
            if (braintreeSub.transactions != null && braintreeSub.transactions.size > 0) {
                val lastTransaction = braintreeSub.transactions[0]
                priceUSD = lastTransaction.amount.toFloat()
                transactionId = lastTransaction.id
                if (lastTransaction.createdAt != null) {
                    createdTransaction = format.format(lastTransaction.createdAt.time)
                }
                if (braintreeSub.billingPeriodEndDate != null) {
                    billingPeriodEndDate = format.format(braintreeSub.billingPeriodEndDate.time)
                }
                if (braintreeSub.paidThroughDate != null) {
                    paidThroughDate = format.format(braintreeSub.paidThroughDate.time)
                }

            } else {
                priceUSD = braintreeSub.price.toFloat()
            }
            updateStringBuilder.appendln(
                "UPDATE = Braintree Subscription id: ${subscriptionRoot.id}, status: ${braintreeSub.status}, customer id:${braintreeSub.id}, " +
                        "OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillDate} " +
                        "billingPeriodEndDate: ${billingPeriodEndDate}, paidThroughDate ${paidThroughDate} " +
                        "| Last Transaction id: ${transactionId}, price: ${priceUSD}, created on: ${createdTransaction}"
            )
            //success
        }
        Subscription.Status.PENDING, Subscription.Status.PAST_DUE -> {
            //subscription is pending
            status = SubscriptionCheckResult.Status.PENDING
            priceUSD = braintreeSub.nextBillingPeriodAmount.toFloat()
        }
        else -> {
            //has to have canceled from a failed payment
            status = SubscriptionCheckResult.Status.CANCEL_FAILED_PAYMENT
            priceUSD = 0f
            cancelStringBuilder.appendln(
                "CANCEL = Braintree Subscription id: ${subscriptionRoot.id}, status: ${braintreeSub.status}, customer id:${braintreeSub.id}, " +
                        "OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillDate} " +
                        "billingPeriodEndDate: ${billingPeriodEndDate}, paidThroughDate ${paidThroughDate} " +
                        "| Last Transaction id: ${transactionId}, price: ${priceUSD}, created on: ${createdTransaction}"
            )
        }
    }
    return SubscriptionCheckResult(status, priceUSD, nextBillDate, null)
}


