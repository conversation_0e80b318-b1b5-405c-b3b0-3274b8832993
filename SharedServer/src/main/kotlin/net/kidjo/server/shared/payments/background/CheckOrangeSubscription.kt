package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.SubscriptionStatus
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder
import java.time.LocalDateTime

fun PaymentManager.checkOrangeSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val orangeSubscription: SubscriptionStatus?
    try {
        orangeSubscription = orangeApiManager.getSubscriptionStatus(
            subscriptionRoot.subscriptionToken,
            subscriptionRoot.subscriptionType.raw, subscriptionRoot.iapId
        )
    } catch (e: Exception) {
        throw e
    }

    if (orangeSubscription == null) {
        throw SubscriptionCheckException("Could not find Orange subscription")
    }
    val iap =
        iapManager.getOrangeIAP(subscriptionRoot.iapId) ?: throw SubscriptionCheckException("Could not find the iap")
    val status: SubscriptionCheckResult.Status
    val priceInUSD: Float = iap.price.toFloat()

    val nextBillingDate: LocalDateTime = orangeSubscription.expiryTimeMillis.epochMilliToLocalDateTime()
    val paymentState = orangeSubscription.paymentState
    var paymentStateString: String? = null
    if (paymentState != null) {

        if (paymentState == 0) {
            status = SubscriptionCheckResult.Status.PENDING
        } else {
            status = SubscriptionCheckResult.Status.ACTIVE
            updateStringBuilder.appendln(
                "UPDATE = ORANGE Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
            )
        }
        paymentStateString = paymentState.toString()


    } else if (orangeSubscription.cancelReason != null) {
        val cancelReason = orangeSubscription.cancelReason
        if (cancelReason == 0) status = SubscriptionCheckResult.Status.CANCEL_USER
        else if (cancelReason == 1) status = SubscriptionCheckResult.Status.CANCEL_FAILED_PAYMENT
        else status = SubscriptionCheckResult.Status.CANCEL_USER
        cancelStringBuilder.appendln(
            "CANCEL = ORANGE Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    } else {
        throw SubscriptionCheckException("Could not properly parse if the play store subscription is active")
    }
    return SubscriptionCheckResult(status, priceInUSD, nextBillingDate, paymentStateString)
}
