package net.kidjo.server.shared.cachedatabase

import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.util.concurrent.TimeUnit

private const val IP_COUNTRY_CACHE_LIFETIME_IN_SECONDS = 30L * 24L * 60L * 60L // 30 days (approximately 1 month)
private const val API_KEY = "8e4f9054333d42b29524eb09e0464050"

/**
 * Get full country info from cache or fetch from AbstractAPI if not cached
 * Returns JSON string in geoplugin format for backward compatibility
 * @param ip The IP address to lookup (null for current IP)
 * @return JSON string with geoplugin_countryCode or empty string if not found
 */
fun CacheDatabase.getCountryInfoByIp(ip: String?): String {
    val cacheKey = CacheDatabase.KEY_IP_COUNTRY + "info_" + (ip ?: "current")
    val commands = connection.sync()
    
    // Try to get from cache first
    val cachedInfo = commands.get(cacheKey)
    if (cachedInfo != null) {
        return cachedInfo
    }
    
    // If not in cache, fetch from API
    val countryInfo = fetchCountryInfoFromApi(ip)
    
    // Cache the result if we got one
    if (countryInfo.isNotEmpty()) {
        commands.set(cacheKey, countryInfo)
        commands.expire(cacheKey, IP_COUNTRY_CACHE_LIFETIME_IN_SECONDS)
    }
    
    return countryInfo
}

/**
 * Fetch country info from AbstractAPI and transform to geoplugin format
 * @param ip The IP address to lookup (null for current IP)
 * @return JSON string in geoplugin format or empty string if not found
 */
private fun fetchCountryInfoFromApi(ip: String?): String {
    val client = OkHttpClient()
        .newBuilder()
        .connectTimeout(2000, TimeUnit.MILLISECONDS)
        .readTimeout(2000, TimeUnit.MILLISECONDS)
        .build()

    val url = if (ip != null) {
        "https://ipgeolocation.abstractapi.com/v1/?ip_address=$ip&api_key=${API_KEY}"
    } else {
        "https://ipgeolocation.abstractapi.com/v1/?api_key=${API_KEY}"
    }

    try {
        val request: Request = Request.Builder().url(url).build()
        val response = client.newCall(request).execute()
        val body = response.body()?.string()
        response.close()
        
        if (body != null) {
            val abstractApiJson = JSONObject(body)
            val transformedJson = JSONObject()

            // Map 'country_code' from AbstractAPI to 'geoplugin_countryCode'
            if (abstractApiJson.has("country_code")) {
                transformedJson.put("geoplugin_countryCode", abstractApiJson.getString("country_code"))
            }

            return transformedJson.toString()
        }
    } catch (e: Exception) {
        println("Exception | IpCountryCache.fetchCountryInfoFromApi() | Cannot retrieve country from IP '$ip': ${e.message}")
    }

    return ""
}

/**
 * Clear cached country info for a specific IP
 * @param ip The IP address to clear from cache (null for current IP)
 */
fun CacheDatabase.clearCountryCache(ip: String?) {
    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_IP_COUNTRY + (ip ?: "current")
    val infoCacheKey = CacheDatabase.KEY_IP_COUNTRY + "info_" + (ip ?: "current")
    
    commands.del(cacheKey)
    commands.del(infoCacheKey)
}
