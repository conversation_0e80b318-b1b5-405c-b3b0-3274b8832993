package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.*
import net.kidjo.server.shared.models.*
import org.intellij.lang.annotations.Language
import kotlin.math.min

fun DatabaseController.getUsersSubscriptions(
    fetchLimit: Int,
    skip: Int,
    storeId: String,
    subscriptionType: String,
    withCoupon: String,
    search: String,
    fromDate: String,
    toDate: String,
    country: String,
    registrationDateOrder: String,
    isActive: Boolean?,
    unsubscribed: Boolean?,
    isFreeTrial: Boolean?,
    sortColumns: List<String>,
    sortColumnOrders: List<String>,
): List<UserSubscriptionView> {

    val connection = dataSource.connection

    var sql =
        "SELECT id_user, registered_date, name, email, country, storeId, subscriptionType, id_sub, id_coupon, groupId, couponId, iapId, nextBillDate, createdDate, id_type, is_test, stillInFreeTrial, partnerName, id_partner " +
                "FROM user_subscription_view "
    val where = BooleanBox(false)

    if (isActive != null) {
        sql += if (isActive == true) {
            "WHERE id_sub is not null AND ( stillInFreeTrial = false  OR  id_type = 2 OR id_type = 4 ) "
        } else {
            "WHERE id_sub is null "
        }
        where.value = true
    }

    (if (storeId.isNotEmpty()) sql += "${whereOrAnd(where)} ( storeId = '$storeId' OR storeId IS NULL ) ")
    (if (storeId.isNotEmpty()) sql += "${whereOrAnd(where)} ( storeId = '$storeId' OR storeId IS NULL ) ")
    (if (subscriptionType.isNotEmpty()) sql += "${whereOrAnd(where)} ( subscriptionType = '$subscriptionType' OR subscriptionType IS NULL ) ")
    (if (withCoupon.isNotEmpty() && withCoupon == "1") sql += "${whereOrAnd(where)} ( id_coupon > 0 OR id_coupon IS NULL ) ")
    (if (withCoupon.isNotEmpty() && withCoupon == "0") sql += "${whereOrAnd(where)} ( id_coupon = 0 OR id_coupon IS NULL ) ")
    (if (fromDate.isNotEmpty()) sql += "${whereOrAnd(where)} ( createdDate >= '$fromDate' OR createdDate IS NULL ) ")
    (if (toDate.isNotEmpty()) sql += "${whereOrAnd(where)} ( createdDate <= '$toDate' OR createdDate IS NULL ) ")
    (if (search.isNotEmpty()) sql += "${whereOrAnd(where)} ( email LIKE '%$search%' OR groupId LIKE '%$search%' OR couponId LIKE '%$search%' ) ")
    (if (country.isNotEmpty()) sql += "${whereOrAnd(where)} ( country = '$country' OR country IS NULL ) ")
    (if (country.isNotEmpty()) sql += "${whereOrAnd(where)} ( country = '$country' OR country IS NULL ) ")

    if (unsubscribed != null) {
        if (unsubscribed == true) {
            sql += "${whereOrAnd(where)} ( id_sub IS NOT NULL AND nextBillDate < now() ) "
        }
    }

    if (isFreeTrial != null) {
        sql += if (isFreeTrial == true) {
            "${whereOrAnd(where)} ( id_sub IS NOT NULL AND stillInFreeTrial = true ) "
        } else {
            "${whereOrAnd(where)} ( id_sub IS NOT NULL AND stillInFreeTrial = false ) "
        }
    }

    var order = ""
    if (sortColumns.isNotEmpty() && sortColumns.size == sortColumnOrders.size) {
        for (i in sortColumns.indices) {
            order += if (i != sortColumns.size - 1) {
                "${sortColumns[i]} ${sortColumnOrders[i]}, "
            } else {
                "${sortColumns[i]} ${sortColumnOrders[i]} "
            }
        }

    } else {
        order += "registered_date $registrationDateOrder "
    }
    sql += "${whereOrAnd(where)} ( is_test = ${!config.env.isLive} OR is_test IS NULL ) "
    sql += "${whereOrAnd(where)} ( storeId NOT IN ( 'mondia_media', 'swisscom', 'docomo', 'orange', 'virgo') OR storeId IS NULL ) "

    sql += "GROUP BY id_user ORDER BY $order"

    val statement = connection.prepareStatement(sql)

    //set fetch limit to the number of books retrieved, but only if its below the fetch limit
    statement.fetchSize = min(fetchLimit, config.db_maxFetch)

    val results = statement.executeQuery()
    val users = arrayListOf<UserSubscriptionView>()
    results.absolute(skip)

    var i = 0
    while (results.next() && i < fetchLimit) {
        users.add(objectCreator.userSubscriptionView(results))
        i++
    }

    statement.close()
    connection.close()
    return users
}

fun DatabaseController.getUserSubscriptions(userId: Long): List<SubscriptionRoot> {
    val sql = "Select * from subscriptions_root where userId=? and isActive=true"
    val users = arrayListOf<SubscriptionRoot>()
    dataSource.connection.use {
        val statement = it.prepareStatement(sql)
        statement.setLong(1, userId)
        val results = statement.executeQuery()
        while (results.next()) {
            users.add(objectCreator.subscription(results))
        }
        statement.close()
        results.close()
    }
    return users

}


fun DatabaseController.getQueryUsersSubscriptions(
    storeId: String,
    subscriptionType: String,
    withCoupon: String,
    search: String,
    country: String,
    fromDate: String,
    toDate: String,
    isActive: Boolean?,
    unsubscribed: Boolean?,
    isFreeTrial: Boolean?,
): Int {

    val connection = dataSource.connection

    var sql =
        "SELECT id_user, registered_date, name, email, country, storeId, subscriptionType, id_sub, id_coupon, groupId, couponId, iapId, nextBillDate, createdDate, id_type, is_test, stillInFreeTrial, partnerName, id_partner  " +
                "FROM user_subscription_view "
    val where = BooleanBox(false)

    if (isActive != null) {
        sql += if (isActive == true) {
            "WHERE id_sub is not null AND ( stillInFreeTrial = false  OR  id_type = 2 OR id_type = 4 ) "
        } else {
            "WHERE id_sub is null "
        }
        where.value = true
    }

    (if (storeId.isNotEmpty()) sql += "${whereOrAnd(where)} ( storeId = '$storeId' OR storeId IS NULL ) ")
    (if (subscriptionType.isNotEmpty()) sql += "${whereOrAnd(where)} ( subscriptionType = '$subscriptionType' OR subscriptionType IS NULL ) ")
    (if (withCoupon.isNotEmpty() && withCoupon == "1") sql += "${whereOrAnd(where)} ( id_coupon > 0 OR id_coupon IS NULL ) ")
    (if (withCoupon.isNotEmpty() && withCoupon == "0") sql += "${whereOrAnd(where)} ( id_coupon = 0 OR id_coupon IS NULL ) ")
    (if (fromDate.isNotEmpty()) sql += "${whereOrAnd(where)} ( createdDate >= '$fromDate' OR createdDate IS NULL ) ")
    (if (toDate.isNotEmpty()) sql += "${whereOrAnd(where)} ( createdDate <= '$toDate' OR createdDate IS NULL ) ")
    (if (search.isNotEmpty()) sql += "${whereOrAnd(where)} ( email LIKE '%$search%' OR groupId LIKE '%$search%' OR couponId LIKE '%$search%' ) ")
    (if (country.isNotEmpty()) sql += "${whereOrAnd(where)} ( country = '$country' OR country IS NULL ) ")

    sql += "${whereOrAnd(where)} ( is_test = ${!config.env.isLive} OR is_test IS NULL ) "
    sql += "${whereOrAnd(where)} ( storeId NOT IN ( 'mondia_media', 'swisscom', 'docomo', 'orange', 'virgo') OR storeId IS NULL ) "

    if (unsubscribed != null) {
        if (unsubscribed == true) {
            sql += "${whereOrAnd(where)} ( id_sub IS NOT NULL AND nextBillDate < now() ) "
        }
    }

    if (isFreeTrial != null) {
        sql += if (isFreeTrial == true) {
            "${whereOrAnd(where)} ( id_sub IS NOT NULL AND stillInFreeTrial = true ) "
        } else {
            "${whereOrAnd(where)} ( id_sub IS NOT NULL AND stillInFreeTrial = false ) "
        }
    }

    sql += "GROUP BY id_user "

    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()
    var size = 0
    if (results != null) {
        results.last() // moves cursor to the last row
        size = results.row
    }

    statement.close()
    connection.close()
    return size
}

fun DatabaseController.getSubscriptions(
    fetchLimit: Int,
    skip: Int,
    fromDate: String,
    toDate: String,
    storeId: String,
    subscriptionType: String,
    isActive: Boolean?,
    sortColumns: List<String>,
    sortColumnOrders: List<String>,
    createdDateOrder: String,
): List<SubscriptionListEntryDTO> {
    val connection = dataSource.connection
    var sql =
        "SELECT isActive, userId,  subscriptionType, storeId, created_at, nextBillDate, iapId FROM subscriptions_root "
    val where = BooleanBox(false)
    (if (isActive != null) sql += "${whereOrAnd(where)} isActive = $isActive ")
    (if (storeId.isNotEmpty()) sql += "${whereOrAnd(where)} storeId = '$storeId' ")
    (if (subscriptionType.isNotEmpty()) sql += "${whereOrAnd(where)} subscriptionType = '$subscriptionType' ")
    (if (fromDate.isNotEmpty()) sql += "${whereOrAnd(where)} created_at >= '$fromDate' ")
    (if (toDate.isNotEmpty()) sql += "${whereOrAnd(where)} created_at <= '$toDate' ")

    var order = ""
    if (sortColumns.isNotEmpty() && sortColumns.size == sortColumnOrders.size) {
        for (i in sortColumns.indices) {
            order += if (i != sortColumns.size - 1) {
                "${sortColumns[i]} ${sortColumnOrders[i]}, "
            } else {
                "${sortColumns[i]} ${sortColumnOrders[i]} "
            }
        }
    } else {
        order += "created_at $createdDateOrder "
    }

    sql += "${whereOrAnd(where)} created_at <> nextBillDate AND isTest = ${!config.env.isLive} "
    sql += "${whereOrAnd(where)} storeId NOT IN ( 'mondia_media', 'swisscom', 'docomo', 'orange', 'virgo') "
    sql += "ORDER BY $order"

    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()
    statement.fetchSize = min(fetchLimit, config.db_maxFetch)
    val subscriptions = arrayListOf<SubscriptionListEntryDTO>()
    results.absolute(skip)
    var i = 0
    while (results.next() && i < fetchLimit) {
        subscriptions.add(objectCreator.toSubscriptionListEntry(results))
        i++
    }
    statement.close()
    connection.close()
    return subscriptions
}

fun DatabaseController.getSubscriptionsSize(
    fromDate: String,
    toDate: String,
    storeId: String,
    subscriptionType: String,
    isActive: Boolean?,
): Int {
    val connection = dataSource.connection
    var sql = "SELECT isActive, subscriptionType, storeId, created_at, nextBillDate, iapId FROM subscriptions_root "
    val where = BooleanBox(false)
    (if (isActive != null) sql += "${whereOrAnd(where)} isActive = $isActive ")
    (if (storeId.isNotEmpty()) sql += "${whereOrAnd(where)} storeId = '$storeId' ")
    (if (subscriptionType.isNotEmpty()) sql += "${whereOrAnd(where)} subscriptionType = '$subscriptionType' ")
    (if (fromDate.isNotEmpty()) sql += "${whereOrAnd(where)} created_at >= '$fromDate' ")
    (if (toDate.isNotEmpty()) sql += "${whereOrAnd(where)} nextBillDate <= '$toDate' ")

    sql += "${whereOrAnd(where)} created_at <> nextBillDate AND isTest = ${!config.env.isLive} "
    sql += "${whereOrAnd(where)} storeId NOT IN ( 'mondia_media', 'swisscom', 'docomo', 'orange', 'virgo') "

    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()
    var size = 0

    if (results != null) {
        results.last()
        size = results.row
    }
    statement.close()
    connection.close()
    return size
}

data class SubscriptionListEntryDTO(
    val userId: Long,
    val startDate: String,
    val endDate: String,
    val subscriptionType: String,
    val storeId: String,
    val price: String,
    val isActive: Boolean,
)

data class SubscriptionList(
    val size: Int,
    val subscriptions: List<SubscriptionListEntryDTO>,
)

fun DatabaseController.getUserViewById(userId: Long): UserSubscriptionView? {
    val connection = dataSource.connection

    val sql =
        "SELECT id_user, registered_date, name, email, country, storeId, subscriptionType, id_sub, id_coupon, groupId, couponId, iapId, nextBillDate, createdDate, id_type, is_test, partnerName, id_partner  " +
                "FROM user_subscription_view " +
                "WHERE id_user = ? AND is_test = ${!config.env.isLive} ORDER by nextBillDate DESC LIMIT 1"

    val statement = connection.prepareStatement(sql)
    statement.setLong(1, userId)
    val results = statement.executeQuery()

    val user: UserSubscriptionView? = if (results.next()) objectCreator.userSubscriptionView(results)
    else null

    statement.close()
    connection.close()
    return user
}

fun DatabaseController.getSumRedeemedCoupons(groupId: String): String {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT CONCAT(" +
                "CAST(SUM(redeemedTimes) AS char(20))," +
                "'/'," +
                "CAST(SUM(redeemAmount) AS char(20))" +
                ") as redeemedSum FROM account_coupons where groupId = ?"
    )
    statement.setString(1, groupId)
    val results = statement.executeQuery()

    val redeemedSum: String = if (results.next()) results.getString("redeemedSum") else ""

    statement.close()
    connection.close()
    return redeemedSum

}

fun DatabaseController.searchCoupons(
    limit: Int,
    offset: Int,
    fromDate: String?,
    toDate: String?,
    search: String?,
    searchType: String?,
    country: String?,
    duration: String?,
    product: String?,
    type: String?,
    partner: String?,
    couponId: String?,
    status: String?,
    sortColumns: List<String>?,
    sortColumnOrders: List<String>?,
): Pair<Int, List<CouponSearchListDTO>> {
    val connection = dataSource.connection
    try {
        var sql = """
        SELECT 
    acv.id,
    acv.couponId,
    acv.groupId,
    acv.duration,
    acv.startDate,
    acv.expireDate,
    acv.partner,
    acv.product,
    acv.`type`,
    acv.status,
    acv.userId,
    acv.email,
    acv.name,
    acv.country,
    acv.userStartDate
    """
        if (couponId.isNullOrBlank()) {
            sql += """,
            (CASE 
                WHEN acv.`type` IN ('${AccountCouponType.UNIQUE_COUPON.name}', '${AccountCouponType.UNIQUE_ACCESS_COUPON.name}') THEN 
                    CONCAT(COALESCE(acv.redeemedTimes, 0), '/', acv.redeemAmount)
                ELSE 
                    CONCAT(
                        COALESCE(ca.totalRedeemed, 0), '/', 
                        COALESCE(ca.totalRedeemAmount, 0)
                    )
            END) AS redeemed 
        """
        }else{
            sql+=" , acv.redeemedTimes AS redeemed"
        }

        sql+=" FROM account_coupon_view acv\n" +
                "        LEFT JOIN (\n" +
                "        SELECT \n" +
                "            groupId,\n" +
                "            SUM(redeemedTimes) AS totalRedeemed,\n" +
                "            SUM(redeemAmount) AS totalRedeemAmount\n" +
                "        FROM account_coupons\n" +
                "        GROUP BY groupId\n" +
                "        ) ca ON ca.groupId = acv.groupId\n" +
                "        WHERE acv.id > 0 "

        if (!couponId.isNullOrBlank()) sql += " and acv.groupId IN (SELECT groupId FROM account_coupons WHERE id = ?) "
        if (!product.isNullOrBlank()) sql += " AND acv.product = ? "
        if (!partner.isNullOrBlank()) sql += " AND acv.partner = ? "
        if (!type.isNullOrBlank()) sql += " AND acv.type = ? "
        if (!duration.isNullOrBlank()) sql += " AND acv.duration = ? "
        if (!country.isNullOrBlank()) sql += " AND acv.country = ? "
        if (!status.isNullOrBlank()) sql += " AND acv.status = ? "

        if (!search.isNullOrBlank()) {
            sql += when (searchType) {
                CouponSearchType.USER_EMAIL.name -> " AND (acv.name LIKE ? OR acv.email LIKE ?) "
                CouponSearchType.USER_EMAIL_COUPON_CODE.name -> " AND (acv.name LIKE ? OR acv.email LIKE ? OR acv.couponId LIKE ?) "
                CouponSearchType.COUPON_CODE.name -> " AND acv.couponId LIKE ? "
                CouponSearchType.BATCH_NAME.name -> " AND acv.groupId LIKE ? "
                else -> ""
            }
        }

        if (!fromDate.isNullOrBlank() && !toDate.isNullOrBlank()) {
            sql += """
            AND (
                CAST(acv.startDate AS DATE) BETWEEN '$fromDate' AND '$toDate' 
                OR CAST(acv.startDate AS DATE) < '$fromDate' AND CAST(acv.startDate AS DATE) > '$toDate'
            )
        """
        }

        sql += if (couponId.isNullOrBlank()) " GROUP BY acv.groupId " else ""
        if (!couponId.isNullOrBlank()) sql += " ORDER BY acv.userStartDate DESC "

        val order = appendOrderBy(sortColumns, sortColumnOrders)
        if (!order.isNullOrBlank()) {
            sql += if (!couponId.isNullOrBlank()) {
                ", $order, acv.id"
            } else {
                " ORDER BY $order, acv.id"
            }
        }

        val statement = connection.prepareStatement(sql)
        statement.fetchSize = min(limit, config.db_maxFetch)

        var index = 1
        if (!couponId.isNullOrBlank()) {
            statement.setLong(index++, couponId.trim().toLong())
        }
        if (!product.isNullOrBlank()) {
            statement.setString(index++, product.trim())
        }
        if (!partner.isNullOrBlank()) {
            statement.setString(index++, partner.trim())
        }
        if (!type.isNullOrBlank()) {
            statement.setString(index++, type.trim())
        }
        if (!duration.isNullOrBlank()) {
            statement.setString(index++, duration.trim())
        }
        if (!country.isNullOrBlank()) {
            statement.setString(index++, country.trim())
        }
        if (!status.isNullOrBlank()) {
            statement.setString(index++, status.trim())
        }

        if (!search.isNullOrBlank()) {
            when (searchType) {
                CouponSearchType.USER_EMAIL.name -> {
                    statement.setString(index++, "%$search%")
                    statement.setString(index++, "%$search%")
                }
                CouponSearchType.USER_EMAIL_COUPON_CODE.name -> {
                    statement.setString(index++, "%$search%")
                    statement.setString(index++, "%$search%")
                    statement.setString(index++, "%$search%")
                }
                else -> {
                    statement.setString(index++, "%$search%")
                }
            }
        }

        logger.info("Final SQL -> ${statement}")
        val results = statement.executeQuery()
        var size = 0
        if (results.last()) {
            size = results.row
            results.beforeFirst()
        }

        val coupons = arrayListOf<CouponSearchListDTO>()
        results.absolute(offset)

        var i = 0
        while (results.next() && i < limit) {
            coupons.add(objectCreator.toCouponSearch(results))
            i++
        }

        statement.close()
        return size to coupons
    } catch (e: Throwable) {
        throw e
    } finally {
        connection.close()
    }
}

fun DatabaseController.getCouponBatchDetails(couponId: String): CouponBatchDetails? {
    val connection = dataSource.connection
    val sql =
        "SELECT a.groupId, a.couponId, a.duration, a.expireDate, ap.name as partner, apr.name as product, at.name as type " +
                "FROM account_coupons a  " +
                "JOIN account_coupon_partner ap ON a.id_partner = ap.id  " +
                "JOIN account_coupon_product apr ON a.id_product = apr.id " +
                "JOIN account_coupon_type at ON a.id_type = at.id " +
                "WHERE a.id = '$couponId' "
    val statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    var details: CouponBatchDetails? = null
    if (resultSet.next()) details = objectCreator.couponBatchDetails(resultSet) else null
    statement.close()
    connection.close()
    return details
}

fun DatabaseController.getAccountCoupons(groupId: String): List<AccountCoupon> {
    val connection = dataSource.connection
    val sql =
        "SELECT a.*, ap.name as partner_name, apr.name as product_name , at.name as coupon_type_name " +
                "FROM account_coupons a  " +
                "LEFT JOIN account_coupon_partner ap ON a.id_partner = ap.id  " +
                "LEFT JOIN account_coupon_product apr ON a.id_product = apr.id " +
                "LEFT JOIN account_coupon_type at ON a.id_type = at.id " +
                "WHERE a.id > 0 AND a.groupId = '$groupId' "
    val statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    val coupons = mutableListOf<AccountCoupon>()
    while (resultSet.next()) coupons.add(objectCreator.accountCoupon(resultSet))
    statement?.close()
    connection?.close()
    return coupons
}

fun DatabaseController.getCsvReportAccountCoupons(couponId: String): List<CsvReportAccountCoupon> {
    val connection = dataSource.connection
    val sql =
        "SELECT a.groupId, a.couponId, a.duration, a.expireDate, ap.name as partner_name, apr.name as product_name, " +
                "at.name as coupon_type_name , CONCAT(a.redeemedTimes,'/', a.redeemAmount) as redeemedTimes " +
                "FROM account_coupons a  " +
                "JOIN account_coupon_partner ap ON a.id_partner = ap.id  " +
                "JOIN account_coupon_product apr ON a.id_product = apr.id " +
                "JOIN account_coupon_type at ON a.id_type = at.id " +
                "WHERE a.groupId IN (SELECT groupId FROM account_coupons where id = $couponId ) "
    val statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    val coupons = mutableListOf<CsvReportAccountCoupon>()
    while (resultSet.next()) coupons.add(objectCreator.csvAccountCoupon(resultSet))
    statement?.close()
    connection?.close()
    return coupons
}

fun DatabaseController.insertMultipleAccountCoupon(
    groupId: String, couponId: String, expireDate: String,
    duration: String, typeId: Int, productId: Int, partnerId: Int, startDate: String,
) {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT INTO account_coupons(groupId, couponId, expireDate, duration, id_type, id_product, id_partner, startDate) VALUES (?,?,?,?,?,?,?,?)")
    statement.setString(1, groupId)
    statement.setString(2, couponId)
    statement.setString(3, expireDate)
    statement.setString(4, duration)
    statement.setInt(5, typeId)
    statement.setInt(6, productId)
    statement.setObject(7, partnerId)
    statement.setString(8, startDate)
    statement.executeUpdate()

    statement.close()
    connection.close()
}

fun DatabaseController.insertSingleAccountCoupon(
    groupId: String, couponId: String, expireDate: String,
    duration: String, typeId: Int, productId: Int,
    partnerId: Int, redeemAmount: Int, startDate: String,
) {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT INTO account_coupons(groupId, couponId, expireDate, duration, id_type, id_product, id_partner, redeemAmount, startDate) VALUES (?,?,?,?,?,?,?,?,?)")
    statement.setString(1, groupId)
    statement.setString(2, couponId)
    statement.setString(3, expireDate)
    statement.setString(4, duration)
    statement.setInt(5, typeId)
    statement.setInt(6, productId)
    statement.setInt(7, partnerId)
    statement.setInt(8, redeemAmount)
    statement.setString(9, startDate)
    statement.executeUpdate()

    statement.close()
    connection.close()
}

fun DatabaseController.insertAccountCouponPartner(name: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO account_coupon_partner(name, description) VALUES (?, ?)")
    statement.setString(1, name)
    statement.setString(2, name)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.listAllCouponProducts(): ArrayList<AccountCouponProductModel> {
    val connection = dataSource.connection
    val sql = "SELECT * FROM account_coupon_product"

    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()

    val products = ArrayList<AccountCouponProductModel>()
    while (results.next()) {
        products.add(objectCreator.couponProducts(results))
    }

    statement.close()
    connection.close()

    return products
}

fun DatabaseController.listAllCouponPartner(): ArrayList<AccountCouponPartnerModel> {
    val connection = dataSource.connection
    val sql = "SELECT * FROM account_coupon_partner where id > 0"

    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()

    val partners = ArrayList<AccountCouponPartnerModel>()
    while (results.next()) {
        partners.add(objectCreator.couponPartners(results))
    }

    statement.close()
    connection.close()

    return partners
}

fun DatabaseController.listAllCouponTypes(): ArrayList<AccountCouponTypeModel> {
    val connection = dataSource.connection
    val sql = "SELECT * FROM account_coupon_type"

    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()

    val partners = ArrayList<AccountCouponTypeModel>()
    while (results.next()) {
        partners.add(objectCreator.couponTypes(results))
    }

    statement.close()
    connection.close()

    return partners
}

fun DatabaseController.deleteAccountCouponPartner(partnerId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("DELETE FROM account_coupon_partner WHERE id = ?")
    statement.setLong(1, partnerId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.deleteAccountCouponsForBatch(id: String) {
    val connection = dataSource.connection
    try {
        val statement = connection.prepareStatement(
            "DELETE FROM account_coupons " +
                    "WHERE groupId IN (SELECT * FROM (SELECT c.groupId FROM account_coupons c WHERE c.id = ?) tbl) "
        )
        statement.setString(1, id)
        statement.execute()
        statement.close()
    } catch (e: Throwable) {
        throw e
    } finally {
        if (!connection.isClosed) {
            connection.close()
        }
    }
}

fun DatabaseController.countAccountCoupon(groupId: String): Int {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM account_coupons WHERE groupId = '$groupId' ")

    val results = statement.executeQuery()
    results.last()
    val count = results.row

    statement.close()
    connection.close()

    return count
}

fun DatabaseController.getUsersCouponsByGroupIdSize(
    groupId: String,
    redeemed: String,
    search: String,
): Int {
    val connection = dataSource.connection
    @Language("SQL") var sql =
        "SELECT  name, email, subscriptionType, id_coupon, groupId, couponId, duration, redeemedTimes, redeemAmount, createdDate, id_type, is_test, id_partner  " +
                "FROM user_subscription_view " +
                "WHERE is_test = ${!config.env.isLive} "

    (if (groupId.isNotEmpty()) sql += "AND groupId = '$groupId' ")
    (if (search.isNotEmpty()) sql += "AND ( email LIKE '%$search%' OR groupId LIKE '%$search%' OR couponId LIKE '%$search%' ) ")
    (if (redeemed.isNotEmpty() && redeemed == "0") sql += "AND redeemedTimes =  0 ")
    (if (redeemed.isNotEmpty() && redeemed == "1") sql += "AND redeemedTimes >  0 ")
    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()

    results.last()
    val count = results.row

    statement.close()
    connection.close()
    return count
}

fun DatabaseController.getUsersCouponsByGroupId(
    fetchLimit: Int, skip: Int, groupId: String,
    redeemed: String, search: String,
    groupIdOrder: String,
): List<UserCouponsView> {
    val connection = dataSource.connection
    @Language("SQL") var sql =
        "SELECT id_user, name, email, subscriptionType, id_coupon, groupId, couponId, duration, redeemedTimes, redeemAmount, createdDate, id_type, is_test, id_partner  " +
                "FROM user_subscription_view " +
                "WHERE is_test = ${!config.env.isLive} "
    (if (groupId.isNotEmpty()) sql += "AND groupId = '$groupId' ")
    (if (search.isNotEmpty()) sql += "AND ( email LIKE '%$search%' OR groupId LIKE '%$search%' OR couponId LIKE '%$search%' ) ")
    (if (redeemed.isNotEmpty() && redeemed == "0") sql += "AND redeemedTimes =  0 ")
    (if (redeemed.isNotEmpty() && redeemed == "1") sql += "AND redeemedTimes >  0 ")

    sql += "ORDER BY groupId $groupIdOrder, couponExpireDate DESC "

    val statement = connection.prepareStatement(sql)
    //set fetch limit to the number of books retrieved, but only if its below the fetch limit
    statement.fetchSize = min(fetchLimit, config.db_maxFetch)

    val results = statement.executeQuery()
    val coupons = ArrayList<UserCouponsView>()
    results.absolute(skip)

    var i = 0
    while (results.next() && i < fetchLimit) {
        coupons.add(objectCreator.userCouponsView(results))
        i++
    }

    statement.close()
    connection.close()
    return coupons
}

fun DatabaseController.getCouponsByGroupIdSize(
    groupId: String,
    redeemed: String, search: String,
)
        : Int {
    val connection = dataSource.connection

    var sql =
        "SELECT a.id AS id_coupon, a.couponId, a.groupId, a.duration, a.redeemedTimes, a.redeemAmount, a.created_at AS createdDate, a.expireDate AS couponExpireDate, a.id_type, ap.name AS partnerName  " +
                "FROM account_coupons a " +
                "LEFT JOIN account_coupon_partner ap ON ap.id = a.id_partner " +
                "WHERE a.id > 0 "

    (if (groupId.isNotEmpty()) sql += "AND a.groupId = '$groupId' ")
    (if (search.isNotEmpty()) sql += "AND ( a.groupId LIKE '%$search%' OR a.couponId LIKE '%$search%' ) ")
    (if (redeemed.isNotEmpty() && redeemed == "0") sql += "AND a.redeemedTimes =  0 ")
    (if (redeemed.isNotEmpty() && redeemed == "1") sql += "AND a.redeemedTimes >  0 ")
    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()

    results.last()
    val count = results.row

    statement.close()
    connection.close()
    return count
}

fun DatabaseController.getCouponsByGroupId(
    fetchLimit: Int, skip: Int, groupId: String,
    redeemed: String, search: String,
    groupIdOrder: String,
)
        : List<UserCouponsView> {
    val connection = dataSource.connection

    var sql =
        "SELECT a.id AS id_coupon, a.couponId, a.groupId, a.duration, a.redeemedTimes, a.redeemAmount, a.created_at AS createdDate, a.expireDate AS couponExpireDate, a.id_type, a.id_partner " +
                "FROM account_coupons a " +
                "LEFT JOIN account_coupon_partner ap ON ap.id = a.id_partner " +
                "WHERE a.id > 0 "

    (if (groupId.isNotEmpty()) sql += "AND a.groupId = '$groupId' ")
    (if (search.isNotEmpty()) sql += "AND ( a.groupId LIKE '%$search%' OR a.couponId LIKE '%$search%' ) ")
    (if (redeemed.isNotEmpty() && redeemed == "0") sql += "AND a.redeemedTimes =  0 ")
    (if (redeemed.isNotEmpty() && redeemed == "1") sql += "AND a.redeemedTimes >  0 ")

    sql += "ORDER BY a.groupId $groupIdOrder, couponExpireDate DESC "

    val statement = connection.prepareStatement(sql)
    //set fetch limit to the number of books retrieved, but only if its below the fetch limit
    statement.fetchSize = min(fetchLimit, config.db_maxFetch)

    val results = statement.executeQuery()
    val coupons = ArrayList<UserCouponsView>()
    results.absolute(skip)

    var i = 0
    while (results.next() && i < fetchLimit) {
        coupons.add(objectCreator.userCouponsView(results))
        i++
    }

    statement.close()
    connection.close()
    return coupons
}

fun DatabaseController.listAllOriginCountries(): Set<UserCountryDTO> {
    val connection = dataSource.connection
    val sql = "SELECT DISTINCT c.id, c.name, c.short FROM users u JOIN countries c ON c.id = u.country_id"
    val statement = connection.prepareStatement(sql)
    val results = statement.executeQuery()
    val countries = linkedSetOf<UserCountryDTO>()
    while (results.next()) {
        countries.add(
            UserCountryDTO(
                id = results.getLong("id"),
                name = results.getString("name"),
                code = results.getString("short")
            )
        )
    }
    statement.close()
    connection.close()
    return countries
}

data class UserCountryDTO(
    val id: Long,
    val name: String,
    val code: String,
)

data class CouponSearchListDTO(
    val id: Long?,
    val groupId: String?,
    val serialNumber: String?,
    val startDate: String?,
    val expireDate: String?,
    val durationCode: String?,
    val status: String?,
    val redeemed: String?,
    val type: String?,
    val product: String?,
    val partner: String?,
    val userId: Long?,
    val email: String?,
    val country: String?,
)

enum class CouponSearchType(val raw: String) {

    USER_EMAIL("user_email"),
    BATCH_NAME("batch_name"),
    COUPON_CODE("coupon_code"),
    USER_EMAIL_COUPON_CODE("email_coupon_code")
    ;

    companion object {
        fun fromRaw(raw: String): CouponSearchType {
            return when (raw) {
                USER_EMAIL.name -> USER_EMAIL
                BATCH_NAME.name -> BATCH_NAME
                COUPON_CODE.name -> COUPON_CODE
                USER_EMAIL_COUPON_CODE.name -> USER_EMAIL_COUPON_CODE
                else -> USER_EMAIL_COUPON_CODE
            }
        }
    }
}
