package net.kidjo.server.shared.models.entity

import net.kidjo.common.models.Folder
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table

object Folders : Table("folders") {

    val id: Column<Int> = integer("id").autoIncrement()
    val languageId: Column<Int> = integer("languageid")

    val title: Column<String> = varchar("title", 50)

    val ageMin: Column<Int> = integer("agemin")

    val ageMax: Column<Int> = integer("agemax")

    val order: Column<Int> = integer("order")

    val isActive: Column<Boolean> = bool("isactive")

    val description: Column<String?> = text("description").nullable()

    val videoCount: Column<Int> = integer("videocount")

    val compileCount: Column<Int> = integer("compilecount")

    val parentId: Column<Int> = integer("parentid")

    val mediaType =
        customEnumeration(
            "mediaType",
            "enum('video','game','mixed_games')",
            { value -> Folder.MediaType.FromRaw(value as String) },
            { it.raw })
    val type = customEnumeration(
        "type",
        "enum('entertainment','education')",
        { value -> Folder.ContentType.FromRaw(value as String) },
        { it.raw})

    override val primaryKey: PrimaryKey
        get() = PrimaryKey(id)
}
