package net.kidjo.server.shared.tools

import com.google.common.hash.Hashing
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.Video
import org.hashids.Hashids
import org.mindrot.jbcrypt.BCrypt
import java.math.BigInteger

class EncryptionController(private val config: Config) {
    private val sha1HashFunction = Hashing.sha1()
    private val sha256HashFunction = Hashing.sha256()

    //    private val md5HashFunction = Hashing.md5()
    private val hashId = Hashids(config.hashIdSalt, config.hashIdLength)

    fun sha1Hash(s: String): String = sha1HashFunction.hashString(s, Charsets.UTF_8).toString()
    fun sha256Hash(s: String): String = sha256HashFunction.hashString(s, Charsets.UTF_8).toString()
//    fun md5Hash(s: String): String = md5HashFunction.hashString(s, Charsets.UTF_8).toString()


    fun encodeVideoId(l: Long): String {
        return hashId.encode(l)
    }

    fun decodeVideoId(id: String): Long {
        val ids = hashId.decode(id)
        return if (ids.isNotEmpty()) ids[0] else Video.NO_SERVER_ID
    }

    fun encodeNormalId(l: Long): String {
        val binaryString = java.lang.Long.toBinaryString(l).padStart(config.idEncryptionPad, '0').reversed()
        val first = BigInteger(binaryString, 2).toLong()
        val id = first xor config.idEncryptionKey
        return id.toString()
    }

    fun decodeNormalId(stringId: String): Long {
        val id: Long
        try {
            id = stringId.toLong()
        } catch (e: Exception) {
            return User.NO_SERVER_ID
        }
        val reversedId = id xor config.idEncryptionKey
        val binaryString = java.lang.Long.toBinaryString(reversedId).padStart(config.idEncryptionPad, '0').reversed()
        return BigInteger(binaryString, 2).toLong()
    }

    fun checkHeaderHash(deviceIdString: String, dateString: String, providedHash: String): Boolean {
        val hashValue = sha1Hash(deviceIdString + dateString + config.apiCode)
        return hashValue == providedHash
    }

    fun hashPassword(password: String): String {
        return BCrypt.hashpw(password, BCrypt.gensalt(config.user_saltLogRounds))
    }

    fun checkPassword(password: String, hashedPassword: String): Boolean {
        return BCrypt.checkpw(password, hashedPassword)
    }
}