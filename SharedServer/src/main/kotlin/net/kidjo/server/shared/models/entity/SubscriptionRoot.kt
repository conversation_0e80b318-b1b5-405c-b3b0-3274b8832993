package net.kidjo.server.shared.models.entity

import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object SubscriptionRoot : Table("subscriptions_root") {
    val id = long("id").autoIncrement()
    val userId = integer("userId")
    val isActive = bool("isActive")
    val isRenewing = bool("isRenewing")
    val stillInFreeTrial = bool("stillInFreeTrial")
    val paymentType: Column<SubscriptionRoot.PaymentType> = customEnumeration("paymentType", null, fromDb = {
        SubscriptionRoot.PaymentType.fromRaw(it as String)
    }, toDb = {
        it.name
    })
    val subscriptionType: Column<SubscriptionRoot.SubscriptionType> =
        customEnumeration("subscriptionType", null, fromDb = {
            SubscriptionRoot.SubscriptionType.fromRaw(it as String)
        }, toDb = {
            it.name
        })
    val paymentId = varchar("paymentId", 255)
    val platformPurchaseId = varchar("platformPurchaseId", 255)
    val storeId: Column<Device.StorePlatform> =
        customEnumeration("storeId", null, fromDb = {
            Device.StorePlatform.fromRow(it as String)
        }, toDb = {
            it.name
        })
    val paymentStateId = varchar("paymentStateId", 255)
    val iapId = varchar("iapId", 255)
    val purchasingSessionId = varchar("purchasingSessionId", 32)
    val subscriptionToken = text("subscriptionToken")
    val subscriptionTokenHash = text("subscriptionTokenHash")
    val accountCouponId = integer("accountCouponId")
    val nextBillDate = datetime("nextBillDate")
    val lastCheckDate = datetime("lastCheckDate")
    val updatedAt =datetime("updated_at")
    val createdAt = datetime("created_at")
    val isTest = bool("isTest")
    val operatorName = varchar("operatorName", 100)

}

