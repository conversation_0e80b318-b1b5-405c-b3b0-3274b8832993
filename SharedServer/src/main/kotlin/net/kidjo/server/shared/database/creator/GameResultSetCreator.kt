package net.kidjo.server.shared.database.creator

import net.kidjo.common.models.Game
import java.sql.ResultSet


fun ResultSetObjectCreator.game(resultSet: ResultSet): Game {
    val id = resultSet.getLong("id")
    val isPremium = resultSet.getBoolean("isPremium")
    val type = Game.Type.FromRaw(resultSet.getString("type"))
    val difficulty = Game.Difficulty.FromRaw(resultSet.getString("difficulty"))
    val position = resultSet.getInt("locationIndex")

    return Game(id,isPremium,type,difficulty,position)

}