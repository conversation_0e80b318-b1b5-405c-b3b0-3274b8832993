package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.link
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.createDVPairedUser
import org.slf4j.LoggerFactory

private val logger = LoggerFactory.getLogger("DigitalVirgoApiManager - setDVPairingKIDJOInfo() SET PAIRING INFO -   ")
@Throws
fun setDVPairingKIDJOInfo(
    operationId: String,
    userAlias: String,
    subscriptionToken: String,
    subscriptionId: Long? = null,
    userId: Long? = null,
    offerId: Long? = null,
    packageId: Long? = null,
    correlationIdString: String? = null,
    subStatus: String? = null,
    databaseController: DatabaseController
) = try {
        databaseController.createDVPairedUser(
            operationId = operationId,
            virgoAlias = userAlias,
            subscriptionToken = subscriptionToken,
            subscriptionId = subscriptionId,
            userId = userId,
            offerId = offerId,
            packageId = packageId,
            correlationIdString = correlationIdString,
            subStatus = subStatus
        )
    } catch (e: Throwable) {
        logger.error("Problems setting pairing DV info: ${e.localizedMessage}")
        throw  e
    }
