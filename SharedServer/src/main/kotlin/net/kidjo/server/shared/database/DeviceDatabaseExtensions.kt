package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.device_update
import net.kidjo.server.shared.extensions.shortenTo
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.User
import java.sql.PreparedStatement
import java.sql.SQLException

private const val MAX_LENGTH_OF_USER_AGENT = 512

fun DatabaseController.device_new(
    manufacturer: String,
    userId: Long,
    model: String,
    locale: String,
    countryId: Int,
    localTimeZone: Int,
    languageId: Int,
    apiVersionCode: String,
    store: String,
    userAgent: String
): Long {
    val connection = dataSource.connection
    val sql: String
    if (userId == User.NO_SERVER_ID) {
        if (userAgent != "")
            sql =
                "INSERT INTO devices (manufacturer,model,locale,countryId,localTimeZone,languageId,APIVersion,store,userAgent) VALUE(?,?,?,?,?,?,?,?,?)"
        else
            sql =
                "INSERT INTO devices (manufacturer,model,locale,countryId,localTimeZone,languageId,APIVersion,store) VALUE(?,?,?,?,?,?,?,?)"
    } else {
        if (userAgent != "")
            sql =
                "INSERT INTO devices (manufacturer,model,locale,countryId,localTimeZone,languageId,APIVersion,store,userId,userAgent) VALUE(?,?,?,?,?,?,?,?,?,?)"
        else
            sql =
                "INSERT INTO devices (manufacturer,model,locale,countryId,localTimeZone,languageId,APIVersion,store,userId) VALUE(?,?,?,?,?,?,?,?,?)"
    }
    val statement = connection.prepareStatement(sql, PreparedStatement.RETURN_GENERATED_KEYS)

    try {

        statement.setString(1, manufacturer)
        statement.setString(2, model)
        statement.setString(3, locale)
        statement.setInt(4, countryId)
        statement.setInt(5, localTimeZone)
        statement.setInt(6, languageId)
        statement.setString(7, apiVersionCode)
        statement.setString(8, store)

        var index = 9
        if (userId != User.NO_SERVER_ID) {
            statement.setLong(index, userId)
            index++
        }
        if (userAgent != "") {
            val usingUserAgent = userAgent.shortenTo(MAX_LENGTH_OF_USER_AGENT)
            statement.setString(index, usingUserAgent)
        }

        val results = statement.executeAndCheck()
        val keys = statement.generatedKeys
        val databaseId: Long
        if (keys.next()) databaseId = keys.getLong(1)
        else databaseId = User.NO_SERVER_ID

        return databaseId

    } catch (e: SQLException) {
        println("Error getting: device_new ${e.localizedMessage}")
        return 0L
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.device_checkAndGetDeviceInfo(device: Device) {
    if (device.isDeviceIdValid == false) return
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT countryId,languageId,creation,model,userAgent FROM devices WHERE id = ?")
    try {
        statement.setLong(1, device.serverId)
        val results = statement.executeQuery()
        if (results.next()) objectCreator.device_update(device, results)
        else device.infoFromServer = false
    } catch (e: SQLException) {
        println("Error getting: device_checkAndGetDeviceInfo ${e.localizedMessage}")
        return
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.device_setEmail(deviceId: Long, email: String, optIn: Boolean): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE devices SET email = ?, optIn = ? WHERE id = ?")
    statement.setString(1, email)
    statement.setBoolean(2, optIn)
    statement.setLong(3, deviceId)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.device_isSubscribed(deviceId: Long): Boolean {
    if (deviceId == Device.NO_SERVER_ID) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT id FROM subscriptions_root LEFT JOIN subscription_device_platform_connections ON rootSubscriptionId = subscriptions_root.id WHERE subscription_device_platform_connections.deviceId = ? AND subscriptions_root.isActive = TRUE")
    statement.setLong(1, deviceId)
    val results = statement.executeQuery()

    val success: Boolean = results.next()

    statement.close()
    connection.close()

    return success
}

fun DatabaseController.deviceGetUserId(device: Device): String {
    if (!device.isDeviceIdValid) return User.NO_ID

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT userId FROM devices WHERE id = ?")
    statement.setLong(1, device.serverId)
    val results = statement.executeQuery()

    val userId = if (results.next()) results.getString("userId") else User.NO_ID

    statement.close()
    connection.close()

    return userId
}

fun DatabaseController.deviceSetUserId(device: Device, userId: Long): Boolean {
    if (device.serverId == Device.NO_SERVER_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE devices SET userId = ? WHERE id = ?")
    statement.setLong(1, userId)
    statement.setLong(2, device.serverId)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.deviceUnlinkUserId(device: Device): Boolean =
    deviceSetUserId(device, 0)
