package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table

object UsersPartnerSSO : Table("users_partner_sso") {

    val id = long("id").autoIncrement()
    val userId = long("user_id").uniqueIndex("user_id_unique")
    val partnerId = long("partner_id").references(AccountCouponsPartner.id)
    val isActive = bool("is_active").default(true)

    val partnerUserId = long("partner_user_id")
    val partnerName = varchar("partner_partner_name", 255)
    val partnerDescription = varchar("partner_partner_description", 255)
    val partnerIsActive = bool("partner_is_active").default(true)
    val partnerIsSsoActivated = bool("partner_is_sso_activated").default(false)
    val partnerBundles = varchar("partner_bundles", 1024).default("")
    val partnerSubscribedServices = varchar("partner_subscribed_services", 1024).default("")
    val email = varchar("email", 255)

    override val primaryKey = PrimaryKey(id)
}