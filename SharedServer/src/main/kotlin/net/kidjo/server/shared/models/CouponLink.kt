package net.kidjo.server.shared.models

import java.sql.ResultSet
import java.time.LocalDateTime

class CouponLink(resultSet: ResultSet) {
    val id: Long
    val value: String
    val deviceId: String?

    val receiver: String
    val type: String

    val validUntil: LocalDateTime
    val duration: Int? // days

    val redeemed: Int
    val validCountLeft: Int

    val lcValue: String?

    init {
        id = resultSet.getLong("id")
        value = resultSet.getString("value")
        deviceId = resultSet.getString("deviceId")
        receiver = resultSet.getString("receiver")
        type = resultSet.getString("type")
        val timestamp = resultSet.getTimestamp("validUntil")
        validUntil = timestamp.toLocalDateTime()
        duration = resultSet.getInt("duration")
        redeemed = resultSet.getInt("redeemed")
        validCountLeft = resultSet.getInt("validCountLeft")
        lcValue = resultSet.getString("LCValue")
    }
}
