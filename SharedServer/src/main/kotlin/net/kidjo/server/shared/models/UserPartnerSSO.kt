package net.kidjo.server.shared.models

data class UserPartnerSSO(
    //val id: Int,
    val userId: Long,
    val partnerId: Long,
    val isActive: <PERSON><PERSON>an,

    val partnerUserId: Long,
    val partnerName: String,
    val partnerDescription: String,
    val partnerIsActive: <PERSON>olean,
    val partnerIsSsoActivated: Boolean,
    val partnerBundles: String,
    val partnerSubscribedServices: String,
    val email: String
)