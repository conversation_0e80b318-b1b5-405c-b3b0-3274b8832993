package net.kidjo.server.shared.payments.background

import com.google.api.client.googleapis.json.GoogleJsonResponseException
import com.google.api.services.androidpublisher.model.SubscriptionPurchase
import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder
import java.time.LocalDateTime

fun PaymentManager.checkPlayStoreSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val googlePlaySubscription: SubscriptionPurchase
    try {
        googlePlaySubscription = playStoreApiManager.getSubscription(subscriptionRoot)
    } catch (e: GoogleJsonResponseException) {
        throw e
    }
    val iap =
        iapManager.getPlayStoreIAP(subscriptionRoot.iapId) ?: throw SubscriptionCheckException("Could not find the iap")
    val status: SubscriptionCheckResult.Status
    val priceInUSD: Float = iap.price.toFloat()
    val nextBillingDate: LocalDateTime = googlePlaySubscription.expiryTimeMillis.epochMilliToLocalDateTime()
    val paymentState = googlePlaySubscription.paymentState
    var paymentStateString: String? = null
    if (paymentState != null) {
        if (paymentState == 0) {
            status = SubscriptionCheckResult.Status.PENDING
        } else {
            status = SubscriptionCheckResult.Status.ACTIVE
            updateStringBuilder.appendln(
                "UPDATE = PLAYSTORE Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
            )
        }
        paymentStateString = paymentState.toString()
    } else if (googlePlaySubscription.cancelReason != null) {
        val cancelReason = googlePlaySubscription.cancelReason
        if (cancelReason == 0) status = SubscriptionCheckResult.Status.CANCEL_USER
        else if (cancelReason == 1) status = SubscriptionCheckResult.Status.CANCEL_FAILED_PAYMENT
        else if (cancelReason == 3) status = SubscriptionCheckResult.Status.CANCEL_ADMIN
        else status = SubscriptionCheckResult.Status.CANCEL_USER
        cancelStringBuilder.appendln(
            "CANCEL = PLAYSTORE Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    } else {
        throw SubscriptionCheckException("Could not properly parse if the play store subscription is active")
    }
    return SubscriptionCheckResult(status, priceInUSD, nextBillingDate, paymentStateString)
}
