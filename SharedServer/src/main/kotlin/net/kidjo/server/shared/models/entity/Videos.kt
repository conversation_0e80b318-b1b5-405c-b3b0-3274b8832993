package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime
import java.time.LocalDateTime

object Videos : Table("videos") {
    val id: Column<Int> = integer("id").autoIncrement()
    val title: Column<String> = varchar("title", 50)

    val subtitle: Column<String?> = varchar("subtitle", 100).nullable()

    val isActive: Column<Boolean> = bool("isactive").index("isactive")

    val languageId: Column<Int> = integer("languageid").index("videos-languageid")

    val creation: Column<LocalDateTime> = datetime("creation")

    val youtubeId: Column<String?> = varchar("youtubeid", 11).nullable()

    val ageMin: Column<Int> = integer("agemin")

    val ageMax: Column<Int> = integer("agemax")

    val contentTypeId: Column<Int?> =
        integer("contenttypeid").index("videos-contenttype").nullable()

    val contentBrandId: Column<Int> = integer("contentbrandid")

    val thumbnailTime: Column<Double> = double("thumbnailtime")

    val ownerId: Column<Short?> = short("ownerid").nullable()

    val isPremium: Column<Boolean> = bool("ispremium").index("ispremium")

    val multiLanguage: Column<Boolean> = bool("multilanguage")

    val masterId: Column<Int?> = integer("masterid").nullable()

    val quality: Column<Int> = integer("quality")

    val compile: Column<Boolean> = bool("compile")

    val duration: Column<Int> = integer("duration")
    override val primaryKey: PrimaryKey?
        get() = PrimaryKey(id)
}
