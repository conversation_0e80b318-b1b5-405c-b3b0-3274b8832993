package net.kidjo.server.shared.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class PasswordResetRequest(
    @JsonProperty("email")
    val email: String? = "",
    @JsonProperty("msIsdn")
    val msIsdn: String? = "",
    @JsonProperty("oldPassword")
    val oldPassword: String,
    @JsonProperty("newPassword")
    val newPassword: String

)
