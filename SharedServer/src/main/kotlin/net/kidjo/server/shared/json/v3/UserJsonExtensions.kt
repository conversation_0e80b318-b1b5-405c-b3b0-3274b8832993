package net.kidjo.server.shared.json.v3

import net.kidjo.server.shared.models.User
import org.json.JSONObject


fun JsonObjectCreatorV3.user_toJSON(user: User, fullInfo: Boolean): JSONObject {
    val json = JSONObject()
    json.put("id", user.id)
    json.put("name", user.name)

    if (fullInfo) {
        json.put("email", user.email)
        json.put("emailIsConfirmed", user.emailIsConfirmed)
        json.put("activeSubscription", user.isSubscribed)
    }

    return json
}