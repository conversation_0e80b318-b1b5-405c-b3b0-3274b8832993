package net.kidjo.server.shared.database

import net.kidjo.common.models.SubscriptionEvent
import java.sql.SQLException
import java.sql.Timestamp


fun DatabaseController.wv_analyticsOpen(
    deviceId: Long, build: Int, videoId: Long, kidId: Long, videosWatched: Int, subscribeViews: Int,
    location: String, storeId: String, countryId: String, languageId: String, groupIdName: String,
    ruleLabel: String, iap: String, viewId: String, sessionId: String, bootSession: String
): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT INTO a_subscription_webview_open(deviceId,build,videoId,kidId,videosWatched,subscribeViews,location,storeId,countryId,languageId,groupIdName,ruleLabel,iap,viewId,sessionId,bootSession) VALUE(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)")
    try {
        statement.setLong(1, deviceId)
        statement.setInt(2, build)
        statement.setLong(3, videoId)
        statement.setLong(4, kidId)
        statement.setInt(5, videosWatched)
        statement.setInt(6, subscribeViews)
        statement.setString(7, location)
        statement.setString(8, storeId)
        statement.setString(9, countryId)
        statement.setString(10, languageId)
        statement.setString(11, groupIdName)
        statement.setString(12, ruleLabel)
        statement.setString(13, iap)
        statement.setString(14, viewId)
        statement.setString(15, sessionId)
        statement.setString(16, bootSession)
        val results = statement.executeAndCheck()

        return results
    } catch (e: SQLException) {
        println("Error getting: wv_analyticsOpen ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.wv_analyticsEvent(sessionId: String, event: SubscriptionEvent.Type, timestamp: Long): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT INTO a_subscription_webview_events(sessionId,timeOfEvent,eventType) VALUE(?,?,?)")
    try {
        statement.setString(1, sessionId)
        statement.setTimestamp(2, Timestamp(timestamp))
        statement.setString(3, event.raw)
        val results = statement.executeAndCheck()

        return results
    } catch (e: SQLException) {
        println("Error getting: wv_analyticsEvent ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}