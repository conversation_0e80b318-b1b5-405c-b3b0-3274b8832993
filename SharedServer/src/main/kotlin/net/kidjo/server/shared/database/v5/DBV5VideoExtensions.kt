package net.kidjo.server.shared.database.v5
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.creator.video
import net.kidjo.server.shared.models.Video
import java.sql.SQLException

fun DatabaseController.v5_videos_getListFromFolder(cardId: Long ): List<Video> {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT id,title,isPremium,ageMin,ageMax,duration,compile,GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id JOIN folders_videos ON folders_videos.videoId = videos.id WHERE folderId = ? AND videos.isActive = 1 GROUP BY videos.id ORDER BY folders_videos.order ASC ")
    try {
        statement.setLong(1, cardId)

        val results = statement.executeQuery()
        val videos = ArrayList<Video>()
        while (results.next()) videos.add(objectCreator.video(results))

        return videos
    } catch (e: SQLException) {
        println("Error getting: videos_getListFromFolder ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}
