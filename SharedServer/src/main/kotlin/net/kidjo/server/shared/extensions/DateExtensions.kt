package net.kidjo.server.shared.extensions

import java.time.*
import java.util.*

fun Calendar.toLocalDate(): LocalDate {
    return LocalDate.of(this.get(Calendar.YEAR), this.get(Calendar.MONTH) + 1, this.get(Calendar.DAY_OF_MONTH))
}

fun Long.epochMilliToLocalDateTime(): LocalDateTime {
    return Instant.ofEpochMilli(this).atZone(ZoneId.systemDefault()).toLocalDateTime()
}
fun Long.epochMilliToLocalDate(): LocalDate{
    return Instant.ofEpochMilli(this).atZone(ZoneId.systemDefault()).toLocalDate()
}

fun LocalDateTime.toEpochMilli(): Long {
    return ZonedDateTime.of(this, ZoneId.systemDefault()).toInstant().toEpochMilli()
}