package net.kidjo.server.shared.database

import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import net.kidjo.common.models.UserReportType
import net.kidjo.server.shared.database.creator.toAccountDetails
import net.kidjo.server.shared.database.creator.toAccountSearchMapper
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.SubscriptionRoot
import java.sql.ResultSet
import kotlin.math.min

internal fun appendFilter(where: BooleanBox? = null, sql: String, columnValue: String?, columnName: String) =
    sql + if (!columnValue.isNullOrBlank() && columnValue.toLowerCase().trim() == "none") {
        "${whereOrAnd(where)} ( $columnName IS NULL ) "
    } else if (!columnValue.isNullOrBlank()) {
        "${whereOrAnd(where)} ( $columnName = '$columnValue' ) "
    } else ""

internal fun appendOrderBy(
    sortColumns: List<String>?,
    sortColumnOrders: List<String>?
): String? {
    var order: String? = null
    if ((!sortColumns.isNullOrEmpty() && !sortColumnOrders.isNullOrEmpty())
        && sortColumns.size == sortColumnOrders.size
    ) {
        for (i in sortColumns.indices) {
            order = if (i != sortColumns.size - 1) {
                "${sortColumns[i]} ${sortColumnOrders[i]}, "
            } else {
                "${sortColumns[i]} ${sortColumnOrders[i]} "
            }
        }
    }
    return order
}

fun DatabaseController.searchAccounts(
    limit: Int,
    offset: Int,
    search: String?,
    countryCode: String?,
    fromDate: String?,
    toDate: String?,
    subStatus: String?,
    subMethod: String?,
    subType: String?,
    couponType: String?,
    partnerId: Long?,
    sortColumns: List<String>?,
    sortColumnOrders: List<String>?,
    promoContent: Int?,
    reportType: String?,
    inCountries: List<String>?
): Pair<Int, List<AccountSearchDTO>> {
    val connection = dataSource.connection
    val columnSelection = "SELECT id_account, name, email," +
    "country , subscriptionType, storeId, subStatus, createdDate, " +
            "`type`, partner, id_partner, is_tc_accepted, is_promo_accepted "
    val countSlection = "SELECT COUNT(*) AS total "

    val dateFilter = when(reportType){
        UserReportType.ACTIVE_USERS.value -> "nextBillDate"
        else -> "createdDate"
    }

    val from = "FROM subscriptions_report_materialized_view "
    var where = " "

    if (!fromDate.isNullOrBlank() && !toDate.isNullOrBlank()) where += "AND ( ? <= $dateFilter AND ? >= $dateFilter ) "
    else if (reportType == UserReportType.ACTIVE_USERS.value) where += "AND ( nextBillDate >= NOW() ) "

    if (!search.isNullOrBlank()) where += "AND ( email LIKE ? OR name LIKE ? OR couponId LIKE ? ) "
    if (!countryCode.isNullOrBlank()) where += "AND country_code = ? "

    if (!inCountries.isNullOrEmpty()) where += "AND country_code IN (${inCountries.joinToString(",") { "'$it'" }}) "

    if (!subStatus.isNullOrBlank()) {
        where += if (SubscriptionRoot.Status.fromName(subStatus) == true)
            "AND (subStatus = ${SubscriptionRoot.Status.fromName(subStatus)} OR nextBillDate > NOW()) "
        else if(SubscriptionRoot.Status.fromName(subStatus) == false)
            "AND (subStatus = ${SubscriptionRoot.Status.fromName(subStatus)} ) "
        else ""
    }

    where = appendFilter(sql = where, columnValue = subType, columnName = "subscriptionType")
    where = appendFilter(sql = where, columnValue = subMethod, columnName = "storeId")
    where = appendFilter(
        sql = where, columnValue = if (couponType?.toInt() != 0) {
            AccountCouponType.fromRaw(couponType.toString()).toString()
        } else "", columnName = "type"
    )

    if (partnerId != null && partnerId > 0L) where += "AND id_partner IN($partnerId) "

    if (promoContent != null && promoContent > 0) {
        where += "AND is_promo_accepted =$promoContent "
    }
    if (promoContent != null && promoContent == 0) {
        where += "AND (is_promo_accepted = $promoContent or is_promo_accepted is null) "
    }

    val order = appendOrderBy(sortColumns, sortColumnOrders)
    val orderClause = if (!order.isNullOrBlank()) "ORDER BY $order " else "ORDER BY createdDate DESC "

    val limitClause = " LIMIT $limit OFFSET $offset"

    where = where.replaceFirst("AND", "WHERE", true)
    val selectStatement = connection.prepareStatement(columnSelection + from + where + orderClause + limitClause)
    val countStatement = connection.prepareStatement(countSlection + from + where)
    selectStatement.fetchSize = min(limit, config.db_maxFetch)

    var index = 1

    if (!fromDate.isNullOrBlank() && !toDate.isNullOrBlank()) {
        selectStatement.setString(index, fromDate)
        countStatement.setString(index, fromDate)
        index++
        selectStatement.setString(index, toDate)
        countStatement.setString(index, toDate)
        index++
    }

    if (!search.isNullOrBlank()) {
        selectStatement.setString(index, "%$search%")
        countStatement.setString(index, "%$search%")
        index++
        selectStatement.setString(index, "%$search%")
        countStatement.setString(index, "%$search%")
        index++
        selectStatement.setString(index, "%$search%")
        countStatement.setString(index, "%$search%")
        index++
    }
    if (!countryCode.isNullOrBlank()) {
        selectStatement.setString(index, countryCode)
        countStatement.setString(index, countryCode)
        index++
    }
    val results: ResultSet
    val count: ResultSet

    runBlocking {
        val selectQuery = async { selectStatement.executeQuery()}
        val countQuery = async { countStatement.executeQuery()}
        val (selectQueryResult, countQueryResult) = awaitAll(selectQuery, countQuery)
        results = selectQueryResult
        count = countQueryResult
    }

    val rowcount = when (count.next()) {
        true -> count.getInt("total")
        else -> 0
    }


    val accounts = mutableListOf<AccountSearchDTO>()
    while (results.next()) {
        accounts.add(objectCreator.toAccountSearchMapper(results))
    }
    selectStatement.close()
    countStatement.close()
    connection.close()

    return Pair(rowcount, accounts)
}

fun DatabaseController.getAccountById(accountId: String): AccountDetailsDTO? {

    dataSource.connection.use { connection ->
        val sql =
            "SELECT id_user, registered_date, name, email, country, storeId, subscriptionType, id_sub, " +
                    "id_coupon, couponId, iapId, nextBillDate, createdDate, type, subStatus, couponStartDate, " +
                    "couponStatus, is_tc_accepted, is_promo_accepted " +
                    "FROM subscriptions_report_materialized_view WHERE id_sub =  $accountId "

        connection.prepareStatement(sql).use { statement ->
            val resultSet = statement.executeQuery()
            return if (resultSet.next()) objectCreator.toAccountDetails(resultSet) else null
        }
    }
}

fun DatabaseController.getUserById(userId: String): AccountDetailsDTO? {
    val connection = dataSource.connection
    val sql =
        "SELECT id_user, registered_date, name, email, is_tc_accepted, is_promo_accepted, country, storeId, subscriptionType, id_sub, id_coupon, couponId, " +
                "iapId, nextBillDate, createdDate, type, subStatus, couponStartDate, couponStatus " +
                "FROM account_subscription_view_V5 WHERE id_user =  $userId "

    val statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    val account: AccountDetailsDTO? = if (resultSet.next()) objectCreator.toAccountDetails(resultSet) else null
    statement.close()
    connection.close()
    return account
}

data class AccountSearchDTO(
    var accountId: Long?,
    val name: String?,
    val email: String?,
    val country: String?,
    val subStatus: String?,
    val subStartDate: String?,
    val subType: String?,
    val subMethod: String?,
    val couponType: String?,
    val couponPartner: String?,
    val isTcAccepted: Int?,
    val isPromoAccepted: Int?
)

data class NomIdsDTO(
    val countryIds: Set<Int>,
    val partnerIds: Set<Int>,
    val productIds: Set<Int>,
    val subTypes: List<String>,
    val subMethods: List<String>,
    val subStatuses: List<String>
)

data class FilteredAccountNomsDTO(
    val countryIds: Set<Int>,
    val partnerIds: Set<Int>,
    val productIds: Set<Int>,
    val subTypes: List<String>,
    val subMethods: List<String>,
    val subStatuses: List<String>
)

data class AccountDetailsDTO(
    val account: AccountDTO?,
    val subscription: SubscriptionDTO?,
    val coupon: CouponDTO?
)

data class AccountDTO(
    var id: Long?,
    val name: String?,
    val email: String?,
    val country: String?,
    val startDate: String?,
    val isTcAccepted: Int?,
    val isPromoAccepted: Int?
)

data class SubscriptionDTO(
    var id: Long?,
    val startDate: String?,
    val endDate: String?,
    val type: String?,
    val price: String?,
    val status: String?,
    val method: String?
)

data class CouponDTO(
    var id: Long?,
    val couponId: String?,
    val startDate: String?,
    var type: String?,
    val status: String?
)

