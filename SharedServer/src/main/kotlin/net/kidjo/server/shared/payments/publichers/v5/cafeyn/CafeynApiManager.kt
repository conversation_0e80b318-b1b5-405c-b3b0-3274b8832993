package net.kidjo.server.shared.payments.publichers.v5.cafeyn

import com.fasterxml.jackson.databind.ObjectMapper
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class CafeynApiManager(
    val config: Config,
    val utility: Utility,
    val encryptionController: EncryptionController,
    val databaseController: DatabaseController
) {
    companion object {
        private val logger: Logger = LoggerFactory.getLogger(CafeynApiManager::class.java)
    }

    fun refreshTokenApi(cafeynHubToken: String?): Pair<Int, CafeynResponse?> {
        val httpClient = OkHttpClient()
        val mapper = ObjectMapper()
        val requestBody = mapper.writeValueAsString(mapOf("token" to cafeynHubToken))

        val request = Request.Builder()
            .url(
                utility.buildUrl(
                    config.cafeynTokenValidationUrl,
                    "provider/refresh",
                    emptyMap()
                )
            )
            .post(
                RequestBody.create(
                    MediaType.parse("application/json"),
                    requestBody
                )
            )
            .build()

        logger.info("Refresh Api Request to Cafeyn -{}", request)
        val response = httpClient.newCall(request).execute()
        logger.info("Refresh Api Response From Cafeyn -{}", response)

        if (response.isSuccessful) {
            val responseBody = response.body()?.string()
            val cafeynResponse = mapper.readValue(responseBody, CafeynResponse::class.java)
            return Pair(response.code(), cafeynResponse)
        }
        response.close()
        return Pair(response.code(), null)
    }
}
