package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.models.SamsungSubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.AmazonSubscriptionStatus
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder

fun PaymentManager.checkAmazonSubscriptionStatus(
        subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
        cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val amazonSubscriptionStatus: AmazonSubscriptionStatus?
    try {
        // subscriptionRoot.platformPurchaseId is amazonUserId
        amazonSubscriptionStatus = amazonApiManager.getSubscriptionStatus(
                subscriptionRoot.subscriptionToken, subscriptionRoot.platformPurchaseId)
    } catch (e: SubscriptionCheckException) {
        throw e
    }
    if (amazonSubscriptionStatus == null) {
        throw SubscriptionCheckException("Could not find Amazon subscription")
    }
    val iap =
            iapManager.getAmazonIAP(subscriptionRoot.iapId)
                    ?: throw SubscriptionCheckException("Could not find the iap")
    val status: SubscriptionCheckResult.Status
    val priceInUSD: Float = iap.price.toFloat()

    val nextBillingDate = amazonSubscriptionStatus.renewalDate.epochMilliToLocalDateTime()

    val paymentState = amazonSubscriptionStatus.parentProductId.toString()

    var cancelReason = amazonSubscriptionStatus.cancelReason

    if (cancelReason == null) {
        status = SubscriptionCheckResult.Status.ACTIVE
        updateStringBuilder.appendln(
                "UPDATE = AMAZON Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )

    } else if (cancelReason == 0L || cancelReason == 2L) {
        status = SubscriptionCheckResult.Status.CANCEL_ADMIN
        cancelStringBuilder.appendln(
                "CANCEL = AMAZON Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    } else if (cancelReason == 1L) {
        status = SubscriptionCheckResult.Status.CANCEL_USER
        cancelStringBuilder.appendln(
                "CANCEL = AMAZON Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    } else {
        throw SubscriptionCheckException("Could not properly parse if the AMAZON store subscription is active")
    }
    return SubscriptionCheckResult(status, priceInUSD, nextBillingDate, paymentState)
}
