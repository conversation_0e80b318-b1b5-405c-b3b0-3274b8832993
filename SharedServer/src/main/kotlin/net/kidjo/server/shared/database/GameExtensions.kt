package net.kidjo.server.shared.database

import net.kidjo.common.models.Game
import net.kidjo.server.shared.database.creator.game

fun DatabaseController.game_getByFolder(folderId: Long, limit: Int = 10): ArrayList<Game> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT games.id,game_folders.isPremium,games.type,game_folders.difficulty,game_folders.locationIndex FROM games JOIN game_folders ON game_folders.gameId = games.id WHERE game_folders.folderId = ? AND game_folders.isActive = 1 GROUP BY game_folders.id ORDER BY game_folders.order ASC LIMIT ?")
    statement.setLong(1, folderId)
    statement.setInt(2, limit)

    statement.fetchSize = kotlin.math.min(limit, config.db_maxFetch)

    val results = statement.executeQuery()
    val games = ArrayList<Game>()
    while (results.next()) games.add(objectCreator.game(results))

    statement.close()
    connection.close()
    return games
}
