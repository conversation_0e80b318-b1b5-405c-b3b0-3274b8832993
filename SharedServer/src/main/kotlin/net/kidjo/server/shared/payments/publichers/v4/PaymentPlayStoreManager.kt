package net.kidjo.server.shared.payments.publichers.v4

import com.google.api.services.androidpublisher.model.SubscriptionPurchase
import io.ktor.server.application.ApplicationCall
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.SubscriptionErrors
import net.kidjo.server.shared.extensions.Success
import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondConflict
import net.kidjo.server.shared.extensions.respondNoContent
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import net.kidjo.server.shared.models.entity.DeviceInfo
import net.kidjo.server.shared.models.entity.DeviceInfoSubscription
import net.kidjo.server.shared.payments.PlayStoreApiManager
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction

class PaymentPlayStoreManager(
    config: Config,
    httpClient: OkHttpClient,
    encryptionController: EncryptionController,
    databaseController: DatabaseController,
    iapManager: IAPManager,
    internal val playStoreApiManager: PlayStoreApiManager,
    emailManager: EmailManager,
    languageCache: LanguageCache,
    userManager: UserManager
) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {

    override suspend fun start(
        userId: Long, inAppPurchase: InAppPurchase,
        token: String, purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        forceUpdate: Boolean,
        subscriptionId: String?,
        orderId: String?,
        call: ApplicationCall
    ) {

        when (inAppPurchase.store) {
            Device.StorePlatform.PLAYSTORE, Device.StorePlatform.ANDROID_TV -> {

                val checkSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(token)
                if (checkSubscription == null) {
                    return createSubscription(
                        inAppPurchase, token,
                        call, userId, purchasingSession, subscriptionType, subscriptionId, orderId
                    )
                } else {
                    if (checkSubscription.userId != 0L &&
                        userId != checkSubscription.userId
                    ) {
                        return call.respondConflict(
                            SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                            "This subscription already belongs to another account. Please login."
                        )
                    }
                    val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                    if (isExpired) {
                        return updateSubscription(
                            inAppPurchase, token,
                            call, userId, checkSubscription
                        )
                    }else {
                        if(checkSubscription.userId==0L && checkSubscription.id.toInt()!=0){
                            var utility= Utility()
                            if(utility.attachNativeSubscription(checkSubscription,userId)){
                                val userDetails=userManager.getUser(checkSubscription.userId.toString())
                                emailManager.sendConfirmationEmail(userDetails.email, userDetails.name, languageCache.get(call))
                                logger.info("Subscription is attached succesfully !!!")
                                return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${userId} ")

                            }
                        }
                        return call.respondNoContent()
                    }
                }
            }

            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun createSubscription(
        inAppPurchase: InAppPurchase,
        token: String, call:
        ApplicationCall,
        userId: Long,
        purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        subscriptionId: String?,
        orderId: String?
    ) {
        var freeTrial: Boolean = true
        var priceToLog: Float = 0.0f
        var isTestReceipt = false

        val googleSubscriptionPurchase = getPlayStoreReceipt(inAppPurchase, token, subscriptionType.raw)
        if (googleSubscriptionPurchase != null) {
            val paymentStateId = googleSubscriptionPurchase.paymentState
            var paymentStateIdString: String? = null
            if (paymentStateId != null && paymentStateId == 2) {
                paymentStateIdString = paymentStateId.toString()
            }
            if (paymentStateId != null && (paymentStateId == 0 || paymentStateId == 1)) {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
                paymentStateIdString = paymentStateId.toString()
            }
            if (googleSubscriptionPurchase.purchaseType != null) {
                isTestReceipt = true
            }

            val subscriptionRootInsert = SubscriptionRootInsert(
                userId, 0L, freeTrial, priceToLog,
                SubscriptionRoot.PaymentType.NATIVE, subscriptionType, inAppPurchase.store.raw,
                googleSubscriptionPurchase.orderId, inAppPurchase.store, paymentStateIdString,
                inAppPurchase.id, purchasingSession, token, encryptionController.sha256Hash(token),
                0L, googleSubscriptionPurchase.autoRenewing,
                googleSubscriptionPurchase.expiryTimeMillis.epochMilliToLocalDateTime(), isTestReceipt
            )

            insert(subscriptionRootInsert, call)

        }
    }

    override suspend fun updateSubscription(
        inAppPurchase: InAppPurchase,
        token: String,
        call: ApplicationCall,
        userId: Long,
        subscriptionRoot: SubscriptionRoot
    ) {

        var freeTrial: Boolean = true
        var priceToLog: Float = 0.0f
        val googleSubscriptionPurchase = getPlayStoreReceipt(
            inAppPurchase, token,
            subscriptionRoot.subscriptionType.raw
        )

        if (googleSubscriptionPurchase != null) {

            val paymentStateId = googleSubscriptionPurchase.paymentState
            var paymentStateIdString: String? = null
            if (paymentStateId != null && paymentStateId == 2) {
                paymentStateIdString = paymentStateId.toString()
            }
            if (paymentStateId != null && (paymentStateId == 0 || paymentStateId == 1)) {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
                paymentStateIdString = paymentStateId.toString()
            }

            val subscriptionRootUpdate = SubscriptionRootUpdate(
                userId, googleSubscriptionPurchase.autoRenewing,
                googleSubscriptionPurchase.expiryTimeMillis.epochMilliToLocalDateTime(),
                googleSubscriptionPurchase.orderId,
                freeTrial, priceToLog, subscriptionRoot.id, paymentStateIdString
            )

            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)

        }
    }

    fun insertDeviceInfo(type: String, manufacturer: String, model: String) {
        var deviceInfoId = 0
        var subscriptionId = 0

        transaction {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.selectAll()
                .orderBy(net.kidjo.server.shared.models.entity.SubscriptionRoot.id, SortOrder.DESC).limit(1)
                .mapNotNull { row ->
                    subscriptionId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt()
                }

            DeviceInfo.select { DeviceInfo.manufacturer eq manufacturer }.limit(1)
                .mapNotNull { row -> deviceInfoId = row[DeviceInfo.id] }.singleOrNull()

            if (deviceInfoId == 0 && model.isNotEmpty() && type.isNotEmpty() && manufacturer.isNotEmpty()) {
                DeviceInfo.insert {
                    it[DeviceInfo.manufacturer] = manufacturer
                    it[DeviceInfo.model] = model
                    it[DeviceInfo.type] = type
                }
                DeviceInfo.select { DeviceInfo.manufacturer eq manufacturer }.limit(1)
                    .mapNotNull { row -> deviceInfoId = row[DeviceInfo.id] }.singleOrNull()

            }

            val existingSubsInfo = DeviceInfoSubscription.select {
                (DeviceInfoSubscription.deviceInfoId eq deviceInfoId) and
                        (DeviceInfoSubscription.subscriptionId eq subscriptionId)
            }.count()
            if (existingSubsInfo.toInt() == 0) {
                DeviceInfoSubscription.insert {
                    it[DeviceInfoSubscription.deviceInfoId] = deviceInfoId
                    it[DeviceInfoSubscription.subscriptionId] = subscriptionId
                }
            }

        }
    }

    fun getPlayStoreReceipt(
        inAppPurchase: InAppPurchase, token: String, subscriptionType: String
    ): SubscriptionPurchase? {
        return try {
            playStoreApiManager.getSubscription(inAppPurchase.id, token, subscriptionType)
        } catch (e: Exception) {
            logger.error("cant find PlayStore subscription of $inAppPurchase, token: ${token.take(10)}")
            return null
        }
    }
}
