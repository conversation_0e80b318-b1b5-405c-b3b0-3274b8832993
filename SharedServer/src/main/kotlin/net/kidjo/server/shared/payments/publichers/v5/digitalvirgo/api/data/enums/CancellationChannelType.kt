package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums

/*
SERVICE The user canceled subscription from the «Account» section of the content service.
SMS The user canceled subscription via MO STOP.
LIFECYCLE-DV Subscription was cancelled by the subscription management done by the platform.
LIFECYCLE-PROVIDER Subscription was cancelled by the subscription management of the external platform.
SELFCARE-DV Subscription was cancelled by the user on DV own global self-care.
SELFCARE-PROVIDER Subscription was cancelled by the user on the external platform global self-care.
CUSTOMER-CARE-DV Subscription was cancelled by the CC tool owned by DV.
CUSTOMER-CARE-PROVIDER Subscription was cancelled by the CC tool owned by the external platform.
HOTLINE-DV Subscription was cancelled by the user by calling an automated Hotline owned by DV.
HOTLINE-PROVIDER Subscription was cancelled by the user by calling an automated Hotline owned by an external platform.
*/
enum class CancellationChannelType(val raw: String) {
    SERVICE("SERVICE"),
    SMS("SMS"),
    LIFECYCLE_DV("LIFECYCLE-DV"),
    LIFECYCLE_PROVIDER("LIFECYCLE-PROVIDER"),
    SELFCARE_DV("SELFCARE-DV"),
    SELFCARE_PROVIDER("SELFCARE-PROVIDER"),
    CUSTOMER_CARE_DV("CUSTOMER-CARE-DV"),
    CUSTOMER_CARE_PROVIDER("CUSTOMER-CARE-PROVIDER"),
    HOTLINE_DV("HOTLINE-DV"),
    HOTLINE_PROVIDER("HOTLINE-PROVIDER"),
    NONE("NONE")

    ;

    companion object {
        fun fromRaw(raw: String): CancellationChannelType {
            return when (raw) {
                SERVICE.raw -> SERVICE
                SMS.raw -> SMS
                LIFECYCLE_DV.raw-> LIFECYCLE_DV
                LIFECYCLE_PROVIDER.raw-> LIFECYCLE_PROVIDER
                SELFCARE_DV.raw-> SELFCARE_DV
                SELFCARE_PROVIDER.raw-> SELFCARE_PROVIDER
                CUSTOMER_CARE_DV.raw-> CUSTOMER_CARE_DV
                CUSTOMER_CARE_PROVIDER.raw-> CUSTOMER_CARE_PROVIDER
                HOTLINE_DV.raw-> HOTLINE_DV
                HOTLINE_PROVIDER.raw-> HOTLINE_PROVIDER
                else -> NONE
            }
        }
    }
}