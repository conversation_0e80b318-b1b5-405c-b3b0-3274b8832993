package net.kidjo.server.shared.json.v3

import net.kidjo.server.shared.models.Video
import org.json.JSONArray
import org.json.JSONObject


fun JsonObjectCreatorV3.cards_videoToJSON(video: Video): JSONObject {
    val json = JSONObject()
    json.put("type", "video")
    json.put(CARD_VIDEO_ID, video.userId)
    json.put("title", video.title)
    json.put("ageMin", video.ageMin)
    json.put("ageMax", video.ageMax)
    json.put("duration", video.duration)
    json.put("premium", video.isPremium)
    json.put("isLocked", video.isPremium)
    json.put("compilation", video.compilation)
    json.put("formats", cards_videoFormatsToJSONArray(video))
    return json
}

fun JsonObjectCreatorV3.new_cards_videoToJSON(video: Video): JSONObject {
    val json = JSONObject()
    json.put("type", "video")
    json.put(CARD_VIDEO_ID, video.userId)
    json.put("title", video.title)
    json.put("ageMin", video.ageMin)
    json.put("ageMax", video.ageMax)
    json.put("duration", video.duration)
    json.put("isLocked", video.isPremium)
    json.put("formats", cards_videoFormatsToJSONArray(video))
    return json
}
private fun JsonObjectCreatorV3.cards_videoFormatsToJSONArray(video: Video): JSONArray {
    val formatJSON = Array<JSONObject>(video.formats.size, { i ->
        val format = video.formats[i]
        val size = if (video.formatSizes.size > i) video.formatSizes[i] else 0L
        cards_videoFormatToJSON(format, video.userId, size)
    })

    return JSONArray(formatJSON.toList())
}
private fun JsonObjectCreatorV3.cards_videoFormatToJSON(format: Video.Format, videoId: String, size: Long): JSONObject {
    val json = JSONObject()
    json.put("id", format.id)
    json.put("height", format.height)
    json.put("sd", format.delivery)
    json.put("href",Video.Format.GetVideoUrlWithFormat(videoId,format))

    if (size > 0L)
        json.put("fileSize", size)

    return json
}

//=======
//FROM JSON
///======

fun JsonObjectCreatorV3.cards_JSONToVideo(json: JSONObject): Video {
    val id = json.optString(CARD_VIDEO_ID)
    val title = json.optString("title")
    val ageMin = json.optInt("ageMin")
    val ageMax = json.optInt("ageMax")
    val duration = json.optInt("duration")
    val premium = json.optBoolean("premium")
    val compilation = json.optBoolean("compilation")
    val variant = "video"
    val formats = json.optJSONArray("formats")
    val fPair = cards_JSONToFormatArray(formats)
    return Video(encryptionController.decodeVideoId(id),id,title,ageMin,ageMax,premium,duration,compilation,fPair.first,fPair.second, "", null)
}
private fun JsonObjectCreatorV3.cards_JSONToFormatArray(jsonFormatArray: JSONArray): kotlin.Pair<Array<Video.Format>, LongArray> {
    val size = jsonFormatArray.length()

    val longArray = LongArray(size)
    val formats = Array<Video.Format>(size, {i ->
        val json = jsonFormatArray.getJSONObject(i)
        val id = json.optInt("id")
        val fileSize = json.optLong("size",0L)
        longArray[i] = fileSize
        Video.Format.FromId(id)
    })
    return Pair(formats,longArray)
}
