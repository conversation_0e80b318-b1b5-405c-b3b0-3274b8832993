package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object DeviceInfo : Table("device_info"){
    val id = integer("id").autoIncrement()
    val manufacturer = varchar("manufacturer", 255)
    val model=varchar("model",255)
    val type=varchar("type",255)
    val updated_at = datetime("updated_at")
    val created_at = datetime("created_at")
}


