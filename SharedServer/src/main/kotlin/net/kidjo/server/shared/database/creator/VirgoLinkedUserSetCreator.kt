package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.VirgoLinkedUser
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import java.sql.ResultSet

fun ResultSetObjectCreator.virgoLinkedUser(resultSet: ResultSet): VirgoLinkedUser {
    val id = resultSet.getInt("id")
    val opeId = resultSet.getString("ope_id")
    val alias = resultSet.getString("virgo_alias")
    val subscriptionId = resultSet.getLong("subscription_id")
    val subscriptionToken = resultSet.getString("subscription_token")
    val userId = resultSet.getLong("user_id")
    val offerId = resultSet.getLong("offer_id")
    val packageId = resultSet.getLong("package_id")
    val correlationId = resultSet.getString("correlation_id")
    val isEmailSend = resultSet.getBoolean("send_email")
    val subStatus = SubscriptionStatusType.fromRaw(resultSet.getString("sub_status"))

    return VirgoLinkedUser(
        id,
        opeId,
        alias,
        subscriptionId,
        subscriptionToken,
        userId,
        offerId,
        packageId,
        correlationId,
        isEmailSend,
        subStatus
    )
}
