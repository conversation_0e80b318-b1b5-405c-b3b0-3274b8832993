package net.kidjo.server.shared.payments

import io.ktor.server.application.*
import io.ktor.server.plugins.*
import io.ktor.server.request.receiveText
import net.kidjo.server.shared.cachedatabase.CacheDatabase
import net.kidjo.server.shared.cachedatabase.mondia_setPairingInfo
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.optFloat
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.shared.tools.payments.PaymentManager
import net.kidjo.server.shared.tools.payments.startTrackingSubscription
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.jdom2.Document
import org.jdom2.input.SAXBuilder
import org.slf4j.LoggerFactory
import java.io.StringReader
import java.net.URLEncoder
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.xml.parsers.DocumentBuilderFactory

class MondiaPayManager(
    private val config: Config,
    private val utility: Utility,
    private val encryptionController: EncryptionController,
    private val paymentManager: PaymentManager,
    private val cacheDatabase: CacheDatabase,
    private val databaseController: DatabaseController
) {
    private val logger = LoggerFactory.getLogger("MondiaPayManager - ")
    var client = OkHttpClient()
    val factory = DocumentBuilderFactory.newInstance()

    private val mondiaIps = arrayOf(
        "************",
        "*************",
        "***********",
        "************",
        "***************",
        "0:0:0:0:0:0:0:1",
        "127.0.0.1"
    ) // last 5 are for testing

    init {
        factory.isValidating = true
        factory.isIgnoringElementContentWhitespace = true
    }

    suspend fun handleMondiaNotification(call: ApplicationCall) {
        logger.info("Mondia notification received.")

        if (!mondiaIps.contains(call.request.origin.remoteHost)) {
            logger.error("IP not allowed: ${call.request.origin.remoteHost}")
            return
        }

        val xml: Document?
        try {
            val text = call.receiveText()

            logger.info("Notification payload: ${text}")
            val builder = SAXBuilder()
            xml = builder.build(StringReader(text))
        } catch (e: Exception) {
            logger.error("Error handling Mondia's notification, ${e.localizedMessage}")
            return
        }
        if (xml == null) {
            logger.error("Error parsing Mondia's notification, ${call.receiveText()}")
            return
        }

        val child = xml.rootElement //.getChild("MMNotification")
        if (child == null) {
            logger.error("notification didn't have root element, ${call.receiveText()}")
            return
        }

        val actionString = (child.getChildText("Action") ?: "").toUpperCase()
        val subscriptionIdString = child.getChildText("SubscriptionID") ?: ""
        val customerIdString = child.getChildText("CustomerID") ?: ""
        val subscriptionStatus = child.getChildText("SubscriptionStatus") ?: ""
        val freeTrialUntil = child.getChildText("FreeTrialUntill") ?: ""
        val durationCode = child.getChildText("PackageCode") ?: "D"
        val operatorName = (child.getChildText("OperatorName") ?: "").toUpperCase()
        val nextAction = child.getChildText("NextAction") ?: ""
        val nextActionDateString = child.getChildText("NextActionDate") ?: ""
        val price = child.getChildText("Price") ?: "0"
        val iapId = child.getChildText("ProductCode") ?: "NONE"
        val sessionId = child.getChildText("CampaignID") ?: "NONE"
        if (actionString == "" || subscriptionIdString == "" || customerIdString == "" || operatorName == "") {
            logger.error("notification didn't have right information, ${call.receiveText()}")
            return
        }
        val operator = MondiaOperators.GetByOpName(operatorName) ?: MondiaOperators.ORANGE_TUNISIA
        logger.info("OperatorName : $operatorName")
        logger.info("actionString : $actionString")
        logger.info("subscriptionIdString  : $subscriptionIdString")
        logger.info("customerIdString  : $customerIdString")
        logger.info("subscriptionStatus  : $subscriptionStatus")
        logger.info("freeTrialUntil  : $freeTrialUntil")
        logger.info("durationCode : $durationCode")
        logger.info("operatorName : $operatorName")
        logger.info("nextAction  : $nextAction")
        logger.info("nextActionDateString  : $nextActionDateString ")
        logger.info("price : $price")
        logger.info("iapId : $iapId")
        logger.info("sessionId : $sessionId")

        val freeTrialUntilDate =
            if (freeTrialUntil != "") freeTrialUntil.substring(0, freeTrialUntil.indexOf(" ")) else ""
        var stillInFreeTrial: Boolean = true

        if (freeTrialUntilDate != "") {

            val day = freeTrialUntilDate.split("-")[1].toInt()
            val month = freeTrialUntilDate.split("-")[0].toInt()
            val year = freeTrialUntilDate.split("-")[2].toInt()

            stillInFreeTrial = LocalDate.of(year, month, day) > LocalDate.now()
        }

        val subscriptionId: Long
        val customerId: Long
        try {
            subscriptionId = subscriptionIdString.toLong()
            customerId = customerIdString.toLong()
        } catch (e: Exception) {
            logger.error("Error turning ids into longs, ${e.localizedMessage}")
            return
        }

        val transactionHash = encryptionController.sha256Hash(subscriptionIdString)

        when (actionString) {
            "SUBSCRIBE" -> {
                var logDate = LocalDateTime.now().plusDays(if (durationCode == "D") 1 else 7)
                val priceToLog = price.optFloat()
                val isFreeTrial = priceToLog == 0f
                val dateFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss z") //this is probably accurate
                try {
                    logDate = LocalDateTime.parse(nextActionDateString, dateFormatter)
                } catch (e: Exception) {
                    logger.error("Error in parsing date, datestring: $nextActionDateString")
                }
                //this is kinda a complicated system
                //1 create user
                var fakeEmail = ""
                while (fakeEmail == "") {
                    fakeEmail = utility.randomString(
                        LENGTH_OF_GENERATED_MONDIA_ACCOUNT_NAME,
                        CHARS_TO_USE_FOR_MONDIA_ACCOUNT
                    ) + MONDIA_USERS_BASE_EMAIL
                    val user = databaseController.user_getByEmail(fakeEmail)
                    if (user != null) fakeEmail = ""
                }

                val password =
                    utility.randomString(LENGTH_OF_GENERATED_MONDIA_ACCOUNT_PASSWORD, CHARS_TO_USE_FOR_MONDIA_ACCOUNT)
                val hashedPassword = encryptionController.hashPassword(password)

                val insertingUser = User(User.NO_ID, fakeEmail, false, "", 0, hashedPassword)

                val userId = databaseController.user_register(insertingUser).toString()
                insertingUser.id = userId
                val createCustomer: Boolean = paymentManager.createCustomer(insertingUser)
                if (!createCustomer) {
                    logger.error("Failed to create customer: ${insertingUser.toString()}")
                }

                //2. create subscription root and transaction
                val subscriptionRootInsert = SubscriptionRootInsert(
                    insertingUser.getLongId(),
                    0L,
                    stillInFreeTrial,
                    priceToLog,
                    SubscriptionRoot.PaymentType.NATIVE,
                    SubscriptionRoot.SubscriptionType.KIDJO_TV,
                    "Mondia",
                    customerIdString,
                    Device.StorePlatform.MONDIA_MEDIA,
                    null,
                    iapId,
                    sessionId,
                    subscriptionIdString,
                    transactionHash,
                    0L,
                    true,
                    logDate,
                    !config.env.isLive,
                    operatorName
                )
                val success = paymentManager.startTrackingSubscription(subscriptionRootInsert, "")
                if (!success) {
                    logger.error("Failed to create subscription: ${subscriptionRootInsert.toString()}")
                } else {
                    val rootSub =
                        databaseController.subscription_getByHash(Device.StorePlatform.MONDIA_MEDIA, transactionHash)
                    if (rootSub == null) {
                        logger.error("Error finding subscription for action $actionString and hash of $transactionHash")
                        return
                    }

                    if (nextAction == "RENEW") {
                        var success = databaseController.subscription_renew(rootSub.id)
                        if (!success) logger.error("Error renewing subscription $rootSub")
                    } else {
                        var success = databaseController.subscription_renew_cancel(rootSub.id)
                        if (!success) logger.error("Error renewing subscription $rootSub")
                    }

                    if (subscriptionStatus == "SUSPENDED") {
                        var success = databaseController.subscription_disable(rootSub.id)
                        if (!success) logger.error("Error disabling subscription $rootSub")
                    } else {
                        var success = databaseController.subscription_enable(rootSub.id)
                        if (!success) logger.error("Error enabling subscription $rootSub")
                    }

                    if (!stillInFreeTrial) {
                        var success = databaseController.subscription_stopFreeTrial(rootSub.id)
                        if (!success) logger.error("Error canceling free trial $rootSub")
                    } else {
                        var success = databaseController.subscription_startFreeTrial(rootSub.id)
                        if (!success) logger.error("Error starting free trial $rootSub")
                    }
                }

                //3 send SMS
                if (operator != MondiaOperators.VODAFONE_SPAIN) {
                    val smsRequest: Boolean =
                        sendUserAccountInfoThruSMS(customerId, fakeEmail, password, operator, price, durationCode)
                    if (!smsRequest) {
                        logger.error("Failed to send SMS request: " + customerId.toString() + "/" + fakeEmail + "/" + password + "/" + operator.id.toString())
                    }
                }

                //4 send information to cache database
                val cacheSuccess: Boolean = cacheDatabase.mondia_setPairingInfo(customerIdString, insertingUser.id)
                if (!cacheSuccess) {
                    logger.error("Failed to save user data to cache: " + customerIdString + "/" + insertingUser.id)
                }
            }
            "UNSUBSCRIBE" -> {
                val rootSub =
                    databaseController.subscription_getByHash(Device.StorePlatform.MONDIA_MEDIA, transactionHash)
                if (rootSub == null) {
                    logger.error("Error finding subscription for action $actionString and hash of $transactionHash")
                    return
                }
                var success = databaseController.subscriptionTransaction_add(
                    rootSub.id,
                    SubscriptionTransaction.TransactionType.RENEW,
                    0f
                )
                if (!success) {
                    logger.error("Error logging this cancel transaction $rootSub")
                }
                success = databaseController.subscription_cancelFull(rootSub.id)
                if (!success) {
                    logger.error("Error canceling this subscription $rootSub")
                }
            }
            "RENEW" -> {
                var logDate = LocalDate.now().plusDays(if (durationCode == "D") 1 else 7)
                val priceToLog = price.optFloat()
                val dateFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss z") //this is probably accurate
                try {
                    logDate = LocalDateTime.parse(nextActionDateString, dateFormatter).toLocalDate()
                } catch (e: Exception) {
                    logger.error("Error in parsing date, datestring: $nextActionDateString")
                }

                val rootSub =
                    databaseController.subscription_getByHash(Device.StorePlatform.MONDIA_MEDIA, transactionHash)
                if (rootSub == null) {
                    logger.error("Error finding subscription for action $actionString and hash of $transactionHash")
                    return
                }

                // Set renewing to true
                var success = databaseController.subscription_renew(rootSub.id)
                if (!success) logger.error("Error renewing subscription $rootSub")

                // Check and adjust subscription status
                if (subscriptionStatus == "SUSPENDED") {
                    var success = databaseController.subscription_disable(rootSub.id)
                    if (!success) logger.error("Error disabling subscription $rootSub")
                } else {
                    var success = databaseController.subscription_enable(rootSub.id)
                    if (!success) logger.error("Error enabling subscription $rootSub")
                }

                // Check and adjust free trial
                if (!stillInFreeTrial) {
                    var success = databaseController.subscription_stopFreeTrial(rootSub.id)
                    if (!success) logger.error("Error canceling free trial $rootSub")
                } else {
                    var success = databaseController.subscription_startFreeTrial(rootSub.id)
                    if (!success) logger.error("Error starting free trial $rootSub")
                }

                val cacheSuccess: Boolean =
                    cacheDatabase.mondia_setPairingInfo(customerIdString, rootSub.userId.toString())
                if (!cacheSuccess) {
                    logger.error("Failed to save user data to cache: " + customerIdString + "/" + rootSub.userId.toString())
                }

            }
            "RETRY" -> {
                var logDate = LocalDate.now().plusDays(if (durationCode == "D") 1 else 7)
                val priceToLog = price.optFloat()
                val dateFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss z") //this is probably accurate
                try {
                    logDate = LocalDateTime.parse(nextActionDateString, dateFormatter).toLocalDate()
                } catch (e: Exception) {
                    logger.error("Error in parsing date, datestring: $nextActionDateString")
                }

                val rootSub =
                    databaseController.subscription_getByHash(Device.StorePlatform.MONDIA_MEDIA, transactionHash)
                if (rootSub == null) {
                    logger.error("Error finding subscription for action $actionString and hash of $transactionHash")
                    return
                }

                // Check and adjust subscription status
                if (subscriptionStatus == "SUSPENDED") {
                    var success = databaseController.subscription_disable(rootSub.id)
                    if (!success) logger.error("Error disabling subscription $rootSub")
                } else {
                    var success = databaseController.subscription_enable(rootSub.id)
                    if (!success) logger.error("Error enabling subscription $rootSub")
                }

                // Check and adjust free trial
                if (!stillInFreeTrial) {
                    var success = databaseController.subscription_stopFreeTrial(rootSub.id)
                    if (!success) logger.error("Error canceling free trial $rootSub")
                } else {
                    var success = databaseController.subscription_startFreeTrial(rootSub.id)
                    if (!success) logger.error("Error starting free trial $rootSub")
                }

                val cacheSuccess: Boolean =
                    cacheDatabase.mondia_setPairingInfo(customerIdString, rootSub.userId.toString())
                if (!cacheSuccess) {
                    logger.error("Failed to save user data to cache: " + customerIdString + "/" + rootSub.userId.toString())
                }
            }
            "STATUS_CHANGE" -> {
                var logDate = LocalDate.now().plusDays(if (durationCode == "D") 1 else 7)
                val priceToLog = price.optFloat()
                val dateFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss z") //this is probably accurate
                try {
                    logDate = LocalDateTime.parse(nextActionDateString, dateFormatter).toLocalDate()
                } catch (e: Exception) {
                    logger.error("Error in parsing date, datestring: $nextActionDateString")
                }

                val rootSub =
                    databaseController.subscription_getByHash(Device.StorePlatform.MONDIA_MEDIA, transactionHash)
                if (rootSub == null) {
                    logger.error("Error finding subscription for action $actionString and hash of $transactionHash")
                    return
                }

                // Check and adjust subscription status
                if (subscriptionStatus == "SUSPENDED") {
                    var success = databaseController.subscription_disable(rootSub.id)
                    if (!success) logger.error("Error disabling subscription $rootSub")
                } else {
                    var success = databaseController.subscription_enable(rootSub.id)
                    if (!success) logger.error("Error enabling subscription $rootSub")
                }

                // Check and adjust free trial
                if (!stillInFreeTrial) {
                    var success = databaseController.subscription_stopFreeTrial(rootSub.id)
                    if (!success) logger.error("Error canceling free trial $rootSub")
                } else {
                    var success = databaseController.subscription_startFreeTrial(rootSub.id)
                    if (!success) logger.error("Error starting free trial $rootSub")
                }

            }
            else -> {
                return
            }
        }
    }

    private fun sendUserAccountInfoThruSMS(
        customerId: Long,
        email: String,
        password: String,
        operator: MondiaOperators,
        price: String,
        duration: String
    ): Boolean {
        // val message: String = "Merci pour votre inscription à kidjo, votre nom d'utilisateur et mot de passe sont $email $password. Connectez-vous ici : www.kidjo.tv/login"

        var messageDaily =
            "Félicitations ! Vous êtes abonné avec succès à Kidjo. 1 jour gratuit puis ${price}TND/jour. Pour vous désabonner, connectez-vous à votre compte ici account.kidjo.tv."
        var messageWeekly =
            "Félicitations ! Vous êtes abonné avec succès à Kidjo. 7 jours gratuits puis ${price}TND/sem. Pour vous désabonner, connectez-vous à votre compte ici account.kidjo.tv."
//        var message: String = "Vous vous êtes abonné avec succès à KIDJO, TND ${price}. Pour vous désabonner, connectez-vous à votre compte ici (kidjo.tv/account)."
        var messageCredentials: String = "Votre nom d’utilisateur et votre mot de passe sont : ${email} et ${password}"

        var message = if (duration == "D") messageDaily else messageWeekly

        val url: String
        if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) {
            url = operator.productionBackendAPI
        } else if (config.env == Config.Env.STAGING) {
            url = operator.stagingBackendAPI
        } else {
            logger.info("Not staging or production!")
            logger.info("Message: ${message} ${messageCredentials}")
            return true
        }

        val request = Request.Builder()
            .url("${url}subservice/sendsms?customerId=$customerId&message=$message&operatorId=${operator.id}")
            .header(
                "username",
                if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_USERNAME_PRODUCTION else MONDIA_API_USERNAME_STAGING
            )
            .header(
                "password",
                if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_PASSWORD_PRODUCTION else MONDIA_API_PASSWORD_STAGING
            )
            .build()
        val request2 = Request.Builder()
            .url("${url}subservice/sendsms?customerId=$customerId&message=$messageCredentials&operatorId=${operator.id}")
            .header(
                "username",
                if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_USERNAME_PRODUCTION else MONDIA_API_USERNAME_STAGING
            )
            .header(
                "password",
                if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_PASSWORD_PRODUCTION else MONDIA_API_PASSWORD_STAGING
            )
            .build()

        var response: Response
        var response2: Response

        var responseText: String = ""
        var responseText2: String = ""
        try {
            val client = OkHttpClient()
            val client2 = OkHttpClient()

            response = client.newCall(request).execute()
            response2 = client2.newCall(request2).execute()

            if (response.isSuccessful && response2.isSuccessful) {
                responseText = response.body()!!.string()
                responseText2 = response2.body()!!.string()
            }

            response.close()
            response2.close()
        } catch (e: Exception) {
            logger.error("Failed to send sms request: ${e.localizedMessage}")
        }

        logger.info("Send SMS Response: ${responseText}")

        return responseText == "OK" && responseText2 == "OK"
    }

    fun getRedirectUrlString(countryShort: String, redirectUrl: String, duration: String?): String {
        val subType = duration ?: "D"
        val imagePath = "http://menad2c.mondiamedia.com/tnd/59560001/DMC-97/img/kidjo.png"
        val operator = MondiaOperators.GetByCountryShort(countryShort) ?: MondiaOperators.ORANGE_TUNISIA

        val url: String
        if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) {
            url = operator.productionFrontendAPI
        } else if (config.env == Config.Env.STAGING) {
            url = operator.stagingFrontendAPI
        } else {
            println("Not staging or production!");
            url = "http://0.0.0.0/local"
        }

        return "${url}?method=subscribe&merchantId=${if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_MERCHANT_ID_PRODUCTION else MONDIA_MERCHANT_ID_STAGING}&" +
                "redirect=$redirectUrl&productCd=KIDJO&subPackage=$subType&operatorId=${operator.id}&imgPath=${imagePath}"
    }

    fun getRedirectUrlStringForVodafone(
        sxid: String,
        utmSource: String,
        utmCampaign: String,
        redirectUrl: String
    ): String {
        val subType = "W"
        val imagePath = ""
        val operator = MondiaOperators.VODAFONE_SPAIN
        val url = operator.productionFrontendAPI

        return "$url?method=subscribe&merchantId=$MONDIA_MERCHANT_ID_PRODUCTION&redirect=${
            URLEncoder.encode(
                redirectUrl,
                "UTF-8"
            )
        }&productCd=KIDJOES&" +
                "subPackage=$subType&operatorId=${operator.id}&imgPath=$imagePath&campaignId=VF_ES_Mondia&utm_source=$utmSource&utm_campaign=$utmCampaign"
    }
}

enum class MondiaOperators(
    val id: Int,
    val opName: String,
    val countryShort: String,
    val baseUrl: String,
    val productionFrontendAPI: String = "",
    val stagingFrontendAPI: String = "",
    val productionBackendAPI: String = "",
    val stagingBackendAPI: String = ""
) {
    ORANGE_EGYPT(
        2,
        "orange egypt",
        "eg",
        "http://sso.orange.com"
    ),
    ORANGE_TUNISIA(
        6,
        "orange tunisia",
        "tn",
        "http://sso.orange.tn",
        "http://sso.orange.tn/mondiamedia_subscription/",
        "http://stage-login.mondiamediamena.com/billinggw-lcm/orange",
        "http://payment.mondiamediamena.com/billing-gw/",
        "http://payment-staging.mondiamediamena.com/billing-gw/"
    ),
    VODACOM_SOUTH_AFRICA(
        7,
        "vodacom south africa",
        "za",
        "http://login.mondiamediamena.com"
    ),
    VODAFONE_SPAIN(
        10,
        "vodafone spain",
        "es",
        "http://login.mondiamediamena.com",
        "http://login.mondiamediamena.com/billinggw-lcm/billing",
        "http://stage-login.mondiamediamena.com/billinggw-lcm/billing",
        "http://payment.mondiapay.com/billing-gw/",
        "http://staging-payment.mondiapay.com/billing-gw/"
    ),
    OOREDOO_ALGERIA(
        22,
        "ooredoo algeria",
        "dz",
        "http://login.mondiamediamena.com"
    );

    companion object {
        fun GetByOpName(opName: String): MondiaOperators? {
            val lower = opName.toLowerCase()
            return when (lower) {
                ORANGE_EGYPT.opName -> ORANGE_EGYPT
                ORANGE_TUNISIA.opName -> ORANGE_TUNISIA
                VODAFONE_SPAIN.opName -> VODAFONE_SPAIN
                VODACOM_SOUTH_AFRICA.opName -> VODACOM_SOUTH_AFRICA
                OOREDOO_ALGERIA.opName -> OOREDOO_ALGERIA
                else -> null
            }
        }

        fun GetByCountryShort(countryShort: String): MondiaOperators? {
            val lower = countryShort.toLowerCase()
            return when (lower) {
                ORANGE_EGYPT.countryShort -> ORANGE_EGYPT
                ORANGE_TUNISIA.countryShort -> ORANGE_TUNISIA
                VODAFONE_SPAIN.countryShort -> VODAFONE_SPAIN
                VODACOM_SOUTH_AFRICA.countryShort -> VODACOM_SOUTH_AFRICA
                OOREDOO_ALGERIA.countryShort -> OOREDOO_ALGERIA
                else -> null
            }
        }
    }
}

val CHARS_TO_USE_FOR_MONDIA_ACCOUNT = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()
private const val LENGTH_OF_GENERATED_MONDIA_ACCOUNT_NAME = 6
private const val MONDIA_MERCHANT_ID_PRODUCTION = "142"
private const val MONDIA_MERCHANT_ID_STAGING = "133"
private const val MONDIA_USERS_BASE_EMAIL = "@kidjo.net"
const val LENGTH_OF_GENERATED_MONDIA_ACCOUNT_PASSWORD = 6
const val MONDIA_API_USERNAME_STAGING = "Kidjo"
const val MONDIA_API_PASSWORD_STAGING = "p\$ItZoO!9$0a!2T9"
const val MONDIA_API_USERNAME_PRODUCTION = "@kidjo_inc"
const val MONDIA_API_PASSWORD_PRODUCTION = "m\$1DMB159x*#^Uaw"
