package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.operation

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getDVPairedUserBySubToken
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response.DVGetOperationInfoDTO
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.operation.getinfo.getOperationInfoDVPASSApiDTO
import net.kidjo.server.shared.tools.Config
import okhttp3.OkHttpClient
import org.slf4j.LoggerFactory

class DVOperationApi(
    private val config: Config,
    private val databaseController: DatabaseController
) {

    internal val logger = LoggerFactory.getLogger("DVOperationApi - ")
    val httpClient = OkHttpClient()

    suspend fun getDVPASSInfo(subscriptionId: String?, operationId: String?): DVGetOperationInfoDTO? = try {
        logger.info("GET OPERATION INFO DVPASS. ")
        val oprInfoResponse: DVGetOperationInfoDTO?

        val operationIndentString = getOperationIndent(operationId,subscriptionId)

        if (operationIndentString == null) {
            logger.error("ERROR: THE operationId AND subscriptionId are empty. ")
            throw IllegalArgumentException("Can not take subIndentString of null.")
        }

        val claimId = databaseController.getDVPairedUserBySubToken(operationIndentString)?.offerId

        oprInfoResponse =
            getOperationInfoDVPASSApiDTO(
                operationIdString = operationIndentString,
                baseUrl = config.dv_merchants_api_url,
                login = config.dv_sesame_login,
                secret = config.dv_secret_key,
                claim = Pair(
                    if(claimId == null || claimId == 0L) config.dv_kidjo_service_clame_name else config.dv_kidjo_offer_clame_name,
                    databaseController.getDVPairedUserBySubToken(operationIndentString)?.offerId ?:
                    config.dv_kidjo_service_id
                ),
                validity = config.jwt_token_validity_mls
            )

        logger.info("dv_operationInfo.code : ${oprInfoResponse?.code}")
        logger.info("dv_operationInfoResponse.message  : ${oprInfoResponse?.message}")
        logger.info("dv_operationInfoResponse.data.subscription.status  : ${oprInfoResponse?.data?.subscription?.status}")
        logger.info("dv_operationInfoResponse.data.subscription.expirationDate :${oprInfoResponse?.data?.subscription?.expirationDate}")
        logger.info("dv_operationInfoResponse.data.subscription.cancellationDate :${oprInfoResponse?.data?.subscription?.cancellationDate}")
        logger.info("dv_operationInfoResponse.data.subscription.nextInvoiceDate : ${oprInfoResponse?.data?.subscription?.nextInvoiceDate}")
        logger.info("dv_operationInfoResponse.data.subscription.premiumStartDate : ${oprInfoResponse?.data?.subscription?.premiumStartDate}")
        logger.info("dv_operationInfoResponse.data.subscription.subscriptionDate : ${oprInfoResponse?.data?.subscription?.subscriptionDate}")
        logger.info("dv_operationInfoResponse.data.subscription.subscriptionId : ${oprInfoResponse?.data?.subscription?.subscriptionId}")
        logger.info("dv_operationInfoResponse.data.subscription.country : ${oprInfoResponse?.data?.subscription?.country}")

        oprInfoResponse

    } catch (e: Exception) {
        logger.error("Error DV operationInfoResponse, ${e.localizedMessage}")
        throw e
    }

    private fun getOperationIndent(operationId: String?, subscriptionId: String?): String? {
        if (!subscriptionId.isNullOrBlank()) {
            return subscriptionId
        }

        if (!operationId.isNullOrBlank()) {
            return operationId
        }
        return null
    }
}
