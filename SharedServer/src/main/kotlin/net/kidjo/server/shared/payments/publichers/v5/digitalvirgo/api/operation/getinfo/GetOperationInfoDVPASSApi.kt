package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.operation.getinfo
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response.DVGetOperationInfoDTO
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.operation.DVOperationApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.buildRequestHeaders
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.generatedBearerToken
import okhttp3.Response

suspend fun DVOperationApi.getOperationInfoDVPASSApiDTO(
    operationIdString: String,
    baseUrl: String,
    login: String,
    secret: String,
    claim:  Pair<String, Long>? = null,
    validity: Long
): DVGetOperationInfoDTO? {

    var responseGET: Response? = null
    try {
        logger.info("Getting Operation Info | getOperationInfoDVPASSApiDTO ")

        val operationInfoURL= "https://$baseUrl/service/operations/$operationIdString?include=user,offer,package,subscription"
        logger.info("Getting Operation Info URL API | operationInfoURL ")

        val bearerToken = generatedBearerToken(
            sub = baseUrl,
            iss = login,
            secret = secret,
            claim = claim,
            validity = validity
        )

        val requestGET =
            buildRequestHeaders(
                url = operationInfoURL,
                token = bearerToken
            ).
            get().
            build()

        responseGET = withContext(Dispatchers.IO) {
            httpClient.
            newCall(requestGET).
            execute()
        }
        logger.info("Getting Operation Info URL API | operationInfoURL ")

        when (responseGET.code()) {
            HttpStatusCode.OK.value -> {
                logger.info("Operation Info | main response 200 OK")

                val bodyString = withContext(Dispatchers.IO) { responseGET.body()!!.string() }
                logger.info("DV operation Info Response BODY: $bodyString")
                val responseInfo =
                    bodyString.let {
                        jacksonObjectMapper().
                        configure(
                            DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                            false
                        ).
                        readValue<DVGetOperationInfoDTO>(it)
                    }
                logger.info("DV operation Info Jackson Mapper request: $responseInfo")
                when (responseInfo.code){
                    3400, 4401-> {
                        responseInfo.data?.status = SubscriptionStatusType.CANCELLED.raw
                        logger.info("Operation subsc already cancelled | response 3400, 4401 : $responseInfo ")
                        responseGET.close()

                        return responseInfo
                    }
                    3401,4402 -> {
                        responseInfo.data?.status = SubscriptionStatusType.EXPIRED.raw
                        logger.info("Operation subsc already expired | response 3401,4402: $responseInfo ")
                        responseGET.close()

                        return responseInfo
                    }
                    4400 -> {
                        responseInfo.data?.status = SubscriptionStatusType.TERMINATED.raw
                        logger.info("Operation subsc unknown | response 4400: $responseInfo ")
                        responseGET.close()

                        return responseInfo
                    }
                }
                logger.info("Successful Getting Operation Info | response OK: ${responseInfo.code} ")

                responseGET.close()
                return responseInfo
            }
            else -> {
                logger.error("Unsuccessful Getting Operation Info | response: ${responseGET.code()} ")

                responseGET.close()
                return null
            }
        }
    } catch (e: Exception) {
        logger.error("Error DV getOperationInfoDVPASSApiDTO, ${e.localizedMessage}")
        throw e
    } finally {
        responseGET?.close()
    }
}
