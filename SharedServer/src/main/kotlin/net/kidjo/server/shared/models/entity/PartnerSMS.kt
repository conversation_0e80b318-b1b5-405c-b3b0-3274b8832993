package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object PartnerSMS : Table("partner_sms") {
    val id = integer("id").autoIncrement()
    val partnerName = varchar("partner_name", 255)
    val telecom = varchar("telecom", 255)
    val packageId = integer("package_id")
    val subscription = varchar("subscription", 50).nullable()
    val message = text("message")
    val created = date("created")
    val updated = date("updated")
    val description = text("description").nullable()
    val isActive = bool("isActive")

    override val primaryKey = PrimaryKey(id)
}
