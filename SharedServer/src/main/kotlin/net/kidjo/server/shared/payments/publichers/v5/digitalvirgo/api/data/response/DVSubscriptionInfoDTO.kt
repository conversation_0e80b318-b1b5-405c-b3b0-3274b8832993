package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetSubscriptionInfoDTO (
    var code    : Int?    = null,
    var message : String? = null,
    var detail  : String? = null,
    var data    : DVGetSubscriptionInfoData?   = DVGetSubscriptionInfoData()
){
    fun getUserIndent() =
        Companion.getUserIndent(
            data?.user?.msisdn,
            data?.user?.alias
            //data?.user?.aliasGsm
        )
    fun getNextBillingDate() =
        getNextBillingDate(
            data?.nextInvoiceDate,
            data?.subscriptionDate
        )

    fun getNextExpirationDate() =
        getNextExpirationDate(
            data?.cancellationDate,
            data?.expirationDate,
            data?.suspensionDate
        )
    fun getNextCancelDate() =
        getNextCancelDate(
            data?.cancellationDate,
            data?.expirationDate,
            data?.suspensionDate
        )

    companion object{
        private fun getNextBillingDate(nextInvoiceDate: String?, subscriptionDate: String?): LocalDateTime? {
            if (!nextInvoiceDate.isNullOrBlank()) {
                return stringToLocalDateTime(nextInvoiceDate)
            }
            if (!subscriptionDate.isNullOrBlank()) {
                return stringToLocalDateTime(subscriptionDate)
            }

            return null
        }
        private fun getNextCancelDate(cancellationDate: String?, expirationDate: String?, suspensionDate: String?): LocalDateTime? {
            if (!cancellationDate.isNullOrBlank()) {
                return stringToLocalDateTime(cancellationDate)
            }
            if (!expirationDate.isNullOrBlank()) {
                return stringToLocalDateTime(expirationDate)
            }
            if (!suspensionDate.isNullOrBlank()) {
                return stringToLocalDateTime(suspensionDate)
            }
            return null
        }
        private fun getNextExpirationDate(cancellationDate: String?, expirationDate: String?, suspensionDate: String?): LocalDateTime? {
            if (!cancellationDate.isNullOrBlank()) {
                return stringToLocalDateTime(cancellationDate)
            }
            if (!expirationDate.isNullOrBlank()) {
                return stringToLocalDateTime(expirationDate)
            }
            if (!suspensionDate.isNullOrBlank()) {
                return stringToLocalDateTime(suspensionDate)
            }
            return null
        }
        private fun getUserIndent(msisdn: String?, alias: String?): String? {
            if (!msisdn.isNullOrBlank()) {
                return msisdn
            }
            /*if (!aliasGsm.isNullOrBlank()) {
                return aliasGsm
            }*/
            if (!alias.isNullOrBlank()) {
                return alias
            }
            return null
        }
        fun stringToLocalDateTime(date: String?): LocalDateTime? {
            return ZonedDateTime.parse(date, DateTimeFormatter.ISO_DATE_TIME)
                .toLocalDateTime()
        }
    }
}
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetSubscriptionInfoData (
    var subscriptionId      : String? = null,
    var status              : String? = null,
    var country             : String? = null,
    var subscriptionDate    : String? = null,
    var cancellationDate    : String? = null,
    var cancellationChannel : String? = null,
    var expirationDate      : String? = null,
    var suspensionDate      : String? = null,
    var premiumStartDate    : String? = null,
    var nextCycleDate       : String? = null,
    var lastCycleDate       : String? = null,
    var lastInvoiceDate     : String? = null,
    var nextInvoiceDate     : String? = null,
    var user                : DVGetSubscriptionInfoUser?  = DVGetSubscriptionInfoUser(),
    @JsonProperty("package")
    var dvPackage           : DVGetSubscriptionInfoPackage? = DVGetSubscriptionInfoPackage(),
    var offer               : DVGetSubscriptionInfoOffer? = DVGetSubscriptionInfoOffer()
)

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetSubscriptionInfoUser (
    var msisdn    : String? = null,
    var alias   : String? = null,
    //var aliasGsm  : String? = null,
    var ip        : String? = null,
    var mccmnc    : Int?    = null,
    var userAgent : String? = null,
    var locale    : String? = null
)

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetSubscriptionInfoOffer (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null
){
    fun getLongId() = getId(id)
    companion object{
        private fun getId(id: Int?):  Long? {
            return try {
                id?.toLong()
            } catch (e: Exception) {
                null
            }
        }
    }
}
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetSubscriptionInfoPackage (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null

) {
    fun getLongId() = getId(id)
    companion object{
        private fun getId(id: Int?): Long? {
            return try {
                id?.toLong()
            } catch (e: Exception) {
                null
            }
        }
    }
}
