package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.SevamediaSubscription
import net.kidjo.server.shared.database.creator.sevamedia_subscription_info
import net.kidjo.server.shared.database.creator.subscription
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubMethodTypeModel
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import net.kidjo.server.shared.models.SubscriptionRootUpdateV1
import net.kidjo.server.shared.models.TwtNotification
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.SubscriptionsRoot
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.models.entity.UsersConsent
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import java.sql.PreparedStatement
import java.sql.SQLException
import java.time.LocalDateTime


fun DatabaseController.subscriptionRoot_create(subscriptionRoot: SubscriptionRootInsert): Long {
    return subscriptionRoot_create(
        subscriptionRoot.userId,
        subscriptionRoot.nextBillDate,
        subscriptionRoot.purchasingSessionId,
        subscriptionRoot.paymentId,
        subscriptionRoot.platformPurchaseId,
        subscriptionRoot.paymentStateId,
        subscriptionRoot.storeId,
        subscriptionRoot.iapId,
        subscriptionRoot.isFreeTrail,
        subscriptionRoot.paymentType,
        subscriptionRoot.subscriptionType,
        subscriptionRoot.subscriptionToken,
        subscriptionRoot.subscriptionTokenHash,
        subscriptionRoot.accountCouponId,
        subscriptionRoot.isTest,
        subscriptionRoot.operatorName,
        subscriptionRoot.isRenewing
    )
}

fun DatabaseController.create(subscriptionRoot: SubscriptionRootInsert): Long {
    return subscriptionRoot_create(
        subscriptionRoot.userId,
        subscriptionRoot.nextBillDate,
        subscriptionRoot.purchasingSessionId,
        subscriptionRoot.paymentId,
        subscriptionRoot.platformPurchaseId,
        subscriptionRoot.paymentStateId,
        subscriptionRoot.storeId,
        subscriptionRoot.iapId,
        subscriptionRoot.isFreeTrail,
        subscriptionRoot.paymentType,
        subscriptionRoot.subscriptionType,
        subscriptionRoot.subscriptionToken,
        subscriptionRoot.subscriptionTokenHash,
        subscriptionRoot.accountCouponId,
        subscriptionRoot.isTest,
        subscriptionRoot.operatorName,
        subscriptionRoot.isRenewing
    )
}

fun DatabaseController.subscriptionRoot_create(
    userId: Long,
    nextBillDate: LocalDateTime,
    purchasingSessionId: String,
    paymentId: String,
    platformPurchaseId: String,
    paymentStateId: String?,
    store: Device.StorePlatform,
    inAppPurchaseId: String,
    willHaveFreeTrial: Boolean,
    paymentType: SubscriptionRoot.PaymentType,
    subscriptionType: SubscriptionRoot.SubscriptionType,
    subscriptionId: String,
    subscriptionIdHash: String,
    couponId: Long,
    isTest: Boolean,
    operatorName: String = "",
    isRenewing: Boolean = true
): Long {
    val connection = dataSource.connection

    val usingUserId = if (userId < 0) 0L else userId
    val usingCouponId = if (couponId < 0) 0L else couponId

    val statement = connection.prepareStatement(
        "INSERT INTO subscriptions_root(userId, nextBillDate, paymentType, paymentId, platformPurchaseId, storeId, iapId, purchasingSessionId, subscriptionToken,subscriptionTokenHash, accountCouponId, stillInFreeTrial, isTest, operatorName, paymentStateId, subscriptionType, isRenewing) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
        PreparedStatement.RETURN_GENERATED_KEYS
    )
    statement.setLong(1, usingUserId)
    statement.setString(2, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(3, paymentType.raw)
    statement.setString(4, paymentId)
    statement.setString(5, platformPurchaseId)
    statement.setString(6, store.raw)
    statement.setString(7, inAppPurchaseId)
    statement.setString(8, purchasingSessionId)
    statement.setString(9, subscriptionId)
    statement.setString(10, subscriptionIdHash)
    statement.setLong(11, usingCouponId)
    statement.setBoolean(12, willHaveFreeTrial)
    statement.setBoolean(13, isTest)
    statement.setString(14, operatorName)
    statement.setString(15, paymentStateId)
    statement.setString(16, subscriptionType.raw)
    statement.setBoolean(17, isRenewing)

    val results = statement.executeAndCheck()
    val keys = statement.generatedKeys

    val id: Long

    if (keys.next()) {
        id = keys.getLong(1)
    } else {
        id = SubscriptionRoot.NO_ID
        println("Couldn't insert subscription ${statement}")
    }

    statement.close()
    connection.close()
    return id
}

fun DatabaseController.subscriptionRoot_update(subscriptionRoot: SubscriptionRootUpdate): Boolean {
    return subscription_update(
        subscriptionRoot.userId,
        subscriptionRoot.isRenewing,
        subscriptionRoot.nextBillDate,
        subscriptionRoot.platformPurchaseId,
        subscriptionRoot.isFreeTrail,
        subscriptionRoot.subId,
        subscriptionRoot.paymentStateId
    )
}

fun DatabaseController.subscription_update(
    userId: Long, isRenewing: Boolean,
    nextBillDate: LocalDateTime, platformPurchaseId: String,
    isFreeTrail: Boolean, subId: Long, paymentStateId: String?,
): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET userId = ?, isRenewing = ?, nextBillDate = ?, platformPurchaseId = ?, stillInFreeTrial = ? , paymentStateId = ?, updated_at = ? WHERE id = ?")
    statement.setLong(1, userId)
    statement.setBoolean(2, isRenewing)
    statement.setString(3, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(4, platformPurchaseId)
    statement.setBoolean(5, isFreeTrail)
    statement.setString(6, paymentStateId)
    statement.setString(7,  mysqlDateTimeFormatter.format(LocalDateTime.now()))
    statement.setLong(8, subId)


    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_update_nextBillingDate(nextBillDate: LocalDateTime, subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET nextBillDate = ? WHERE id = ?")
    statement.setString(1, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setLong(2, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_update_nextBillingDateBySubToken(
    nextBillDate: LocalDateTime,
    subToken: String,
): Boolean {
    if (subToken.isBlank()) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET nextBillDate = ? WHERE subscriptionToken = ?")
    statement.setString(1, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(2, subToken)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_type_getList(userId: Long): List<SubscriptionRoot> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT * FROM subscriptions_root " + "WHERE subscriptions_root.created_at IN (" + "SELECT max(subscriptions_root.created_at) FROM subscriptions_root WHERE userId = ? GROUP BY iapId) ORDER BY created_at DESC"
    )
    statement.setLong(1, userId)

    val results = statement.executeQuery()
    val subs = arrayListOf<SubscriptionRoot>()
    while (results.next()) {
        subs.add(objectCreator.subscription(results))
    }
    statement.close()
    connection.close()

    return subs
}

fun DatabaseController.subscription_getRecentActive(userId: Long): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? AND isActive = 1 ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getRecentActiveBraintreeSubscriptionBySubIdOrUserId(
    subId: Long?,
    userId: Long?,
): SubscriptionRoot? {
    if (subId == SubscriptionRoot.NO_ID && userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE id = COALESCE(?, id) AND userId = COALESCE(?, userId) AND isActive = TRUE AND storeId = ? ORDER BY nextBillDate DESC LIMIT 1")
    statement.setObject(1, subId)
    statement.setObject(2, userId)
    statement.setString(3, Device.StorePlatform.KIDJO_BRAINTREE.raw)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscriptionDocomo_getRecentActive(userId: Long): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE ( userId = ? OR subscriptionToken = ? ) AND isActive = 1 AND storeId='docomo' ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getLogicomSubscriptionRecentActive(userId: Long): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? AND isActive = 1 AND paymentId='Logicom' ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscriptionTWT_getRecentActive(userId: Long, subscriptionToken: String): SubscriptionRoot? {
    if (userId == 0L && subscriptionToken.isBlank()) return null
    val connection = dataSource.connection

    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE ( userId = ? AND subscriptionToken = ?  AND isActive = 1)")
    statement.setLong(1, userId)
    statement.setString(2, subscriptionToken)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscription_getRecent(userId: Long): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getRecentTVSubscription(userId: Long, skip: Boolean = false ): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? ${if(skip) "" else " AND isActive = 1 AND nextBillDate > NOW() "}  AND ( subscriptionType = ? OR subscriptionType = ? OR subscriptionType = ? OR subscriptionType = ? ) ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)
    statement.setString(2, SubscriptionRoot.SubscriptionType.KIDJO_TV.raw)
    statement.setString(3, SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS.raw)
    statement.setString(4, SubscriptionRoot.SubscriptionType.KIDJO_TV_GAMES.raw)
    statement.setString(5, SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS_GAMES.raw)
    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getRecentBOOKSubscription(userId: Long, skip: Boolean = false): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? ${if(skip) " " else " AND isActive = 1 AND nextBillDate > NOW() "} AND ( subscriptionType = ? OR subscriptionType = ? OR subscriptionType = ? OR subscriptionType = ?) ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)
    statement.setString(2, SubscriptionRoot.SubscriptionType.KIDJO_BOOKS.raw)
    statement.setString(3, SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS.raw)
    statement.setString(4, SubscriptionRoot.SubscriptionType.KIDJO_BOOKS_GAMES.raw)
    statement.setString(5, SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS_GAMES.raw)
    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getRecentGAMESubscription(userId: Long, skip: Boolean = false): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? ${if(skip) "" else " AND isActive = 1 AND nextBillDate > NOW() "} AND ( subscriptionType = ? OR subscriptionType = ? OR subscriptionType = ? OR subscriptionType = ?) ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, userId)
    statement.setString(2, SubscriptionRoot.SubscriptionType.KIDJO_GAMES.raw)
    statement.setString(3, SubscriptionRoot.SubscriptionType.KIDJO_TV_GAMES.raw)
    statement.setString(4, SubscriptionRoot.SubscriptionType.KIDJO_BOOKS_GAMES.raw)
    statement.setString(5, SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS_GAMES.raw)
    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscription_getById(userId: Long, id: String): SubscriptionRoot? {
    if (userId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? AND id = ? ")
    statement.setLong(1, userId)
    statement.setString(2, id)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.virgo_subscription_getUserById(userId: Long): String? {

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT subscription_id FROM virgo_user_links WHERE user_id = ? ")
    statement.setLong(1, userId)

    val results = statement.executeQuery()

    val virgoSubscription: String?
    if (results.next()) virgoSubscription = results.getString("subscription_id")
    else virgoSubscription = null

    statement.close()
    connection.close()

    return virgoSubscription
}

fun DatabaseController.getDVSubscriptionListByIdOrUserAlias(
    subIndent: String? = null,
    userAlias: String? = null,
): List<SubscriptionRoot> {

    val connection = dataSource.connection
    if (subIndent.isNullOrBlank() && userAlias.isNullOrBlank()) return emptyList()

    val targetValue = if (subIndent.isNullOrBlank()) userAlias else subIndent
    val targetColumnName = if (subIndent.isNullOrBlank()) "virgo_alias" else "subscription_token"

    val statement = connection.prepareStatement(
        "SELECT * FROM subscriptions_root " + "WHERE id IN(SELECT subscription_id FROM virgo_user_links WHERE $targetColumnName = ? ) " + "AND subscriptions_root.created_at IN (SELECT max(subscriptions_root.created_at) " + "FROM subscriptions_root GROUP BY iapId) ORDER BY created_at DESC"
    )
    statement.setString(1, targetValue)

    val results = statement.executeQuery()
    val subs = arrayListOf<SubscriptionRoot>()
    while (results.next()) {
        subs.add(objectCreator.subscription(results))
    }
    statement.close()
    connection.close()

    return subs
}

fun DatabaseController.virgo_subscription_geAllByAlias(alias: String): List<SubscriptionRoot> {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT * FROM subscriptions_root " + "WHERE id IN(SELECT subscription_id FROM virgo_user_links WHERE virgo_alias = ?) " + "ORDER BY created_at DESC"
    )
    statement.setString(1, alias)

    val results = statement.executeQuery()
    val subs = arrayListOf<SubscriptionRoot>()
    while (results.next()) {
        subs.add(objectCreator.subscription(results))
    }
    statement.close()
    connection.close()

    return subs
}

fun DatabaseController.subscriptionGetRecentByCustomerId(customerId: Long): SubscriptionRoot? {
    if (customerId == User.NO_SERVER_ID) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE platformPurchaseId = ? ORDER BY created_at DESC LIMIT 1")
    statement.setLong(1, customerId)

    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getSubscriptionRecentActiveById(subId: Long): SubscriptionRoot? {
    if (subId == SubscriptionRoot.NO_ID) return null


    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE id = ? AND isActive = TRUE ORDER BY nextBillDate DESC LIMIT 1")
    statement.setLong(1, subId)

    val results = statement.executeQuery()
    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}


fun DatabaseController.subscription_getByHash(store: Device.StorePlatform, tokenHash: String): SubscriptionRoot? {
    if (tokenHash.isEmpty()) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE storeId = ? AND subscriptionTokenHash = ? LIMIT 1")
    statement.setString(1, store.raw)
    statement.setString(2, tokenHash)

    val results = statement.executeQuery()
    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscription_getByToken(token: String): SubscriptionRoot? {
    if (token.isEmpty()) return null


    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE subscriptionToken = ? ORDER BY nextBillDate DESC LIMIT 1")
    statement.setString(1, token)

    val results = statement.executeQuery()
    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.getSubscriptionIdByEmail(email: String): SubscriptionRoot? {

    if (email.isEmpty()) return null
    var userID = 0
    val email1 = (email).replace("+", "0") + "@kidjo.tv"

    transaction {
        userID = (Users.select { Users.email.eq(email1) }
            .mapNotNull { it[Users.id] } // Use mapNotNull to avoid null values
            .singleOrNull() ?: 0).toInt() // Return 0 if no record is found

        logger.info("User ID: $userID")
    }


    var subscription: SubscriptionRoot? = null
    if (userID != 0) {
        val connection = dataSource.connection
        var statement =
            connection.prepareStatement("SELECT * FROM subscriptions_root WHERE userId = ? and isActive = 1 and nextBillDate > NOW() and storeId = ? ORDER BY nextBillDate DESC LIMIT 1")
        statement.setInt(1, userID)
        statement.setString(2, "virgo")

        val results = statement.executeQuery()
        if (results.next()) subscription = objectCreator.subscription(results)

        statement.close()
        connection.close()

    }

    return subscription
}


fun DatabaseController.getHuaweiSubscriptionByOrderId(orderId: String): SubscriptionRoot? {
    if (orderId.isEmpty()) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE purchasingSessionId = ? ORDER BY nextBillDate DESC LIMIT 1")
    statement.setString(1, orderId)

    val results = statement.executeQuery()
    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscription_getByPaymentStateId(paymentStateId: String): SubscriptionRoot? {
    if (paymentStateId.isEmpty()) return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM subscriptions_root WHERE paymentStateId = ? LIMIT 1")
    statement.setString(1, paymentStateId)

    val results = statement.executeQuery()
    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscription_getListSortByDateWithProcess(
    batchSize: Int,
    process: (batch: ArrayList<SubscriptionRoot>) -> Unit,
) {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE isActive = TRUE AND CURRENT_TIMESTAMP() > nextBillDate ORDER BY created_at")
    val fetchSize = kotlin.math.min(batchSize, config.db_maxFetch)
    statement.fetchSize = fetchSize

    val results = statement.executeQuery()
    val batch = ArrayList<SubscriptionRoot>()
    while (results.next()) {
        batch.add(objectCreator.subscription(results))

        if (batch.size >= fetchSize) {
            process(batch)
            batch.clear()
        }
    }

    if (batch.size > 0) process(batch)

    statement.close()
    connection.close()

}

fun DatabaseController.subscription_updateNextBillingDateAndRemoveFreeTrial(
    subId: Long,
    nextBillDate: LocalDateTime,
    paymentStateId: String?,
): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET stillInFreeTrial = FALSE, nextBillDate = ?, paymentStateId = ? WHERE id = ?")
    statement.setString(1, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(2, paymentStateId)
    statement.setLong(3, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_cancel(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET isRenewing = FALSE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_disable(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET isActive = FALSE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_stopFreeTrial(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET stillInFreeTrial = FALSE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_startFreeTrial(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET stillInFreeTrial = TRUE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_enable(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET isActive = TRUE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_cancelFull(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET isRenewing = FALSE, isActive = FALSE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_cancelFull_virgo(subToken: String?, nextBillDate: LocalDateTime): Boolean {
    if (subToken.isNullOrBlank()) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET isRenewing = FALSE, isActive = FALSE, nextBillDate = ? WHERE subscriptionToken = ?")
    statement.setString(1, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(2, subToken)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_cancel_vigro(subToken: String): Boolean {
    if (subToken.isBlank()) return false
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET isRenewing = FALSE WHERE subscriptionToken = ?")
    statement.setString(1, subToken)
    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}


fun DatabaseController.subscription_disable_virgo(subToken: String?, nextBillDate: LocalDateTime): Boolean {
    if (subToken.isNullOrBlank()) return false

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE subscriptions_root SET isActive = FALSE, nextBillDate = ? WHERE subscriptionToken = ?")
    statement.setString(1, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(2, subToken)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_renew(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET isRenewing = TRUE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_renew_cancel(subId: Long): Boolean {
    if (subId == SubscriptionRoot.NO_ID) return false

    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET isRenewing = FALSE WHERE id = ?")
    statement.setLong(1, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}


fun DatabaseController.subscription_createDeviceConnection(subscriptionId: Long, deviceId: Long): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("INSERT subscription_device_platform_connections(rootSubscriptionId, deviceId) VALUES (?,?)")
    statement.setLong(1, subscriptionId)
    statement.setLong(2, deviceId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.subscription_linkDeviceWithUserSubscription(userId: Long, deviceId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE devices SET userId = ? WHERE id = ?")
    statement.setLong(1, userId)
    statement.setLong(2, deviceId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.getActiveDeviceSubscription(deviceId: Long): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT subscriptions_root.isActive AS isActive FROM devices JOIN subscriptions_root ON devices.userId = subscriptions_root.userId WHERE devices.id = ? AND subscriptions_root.isActive = 1 AND subscriptions_root.userId <> 0 ORDER BY subscriptions_root.created_at DESC LIMIT 1;")
    try {
        statement.setLong(1, deviceId)

        val results = statement.executeQuery()
        val isSubscribed = if (results.next()) results.getBoolean("isActive") else false

        return isSubscribed
    } catch (e: SQLException) {
        println("Error getting: getActiveDeviceSubscription ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.getActiveSubscriptionExpirationDateByToken(subToken: String?): LocalDateTime? {
    if (subToken == null) return null

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT nextBillDate FROM subscriptions_root WHERE subscriptionToken = ? AND isActive = true AND isRenewing = true ORDER BY nextBillDate DESC LIMIT 1")
    statement.setString(1, subToken)

    val results = statement.executeQuery()
    val nextBillDate = if (results.next()) results.getTimestamp("nextBillDate").toLocalDateTime() else null

    statement.close()
    connection.close()
    return nextBillDate
}

fun DatabaseController.getActiveSwisscomSubscriptionByAbid(abid: String): SubscriptionRoot? {

    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * FROM subscriptions_root WHERE isActive = 1 AND storeId = ? AND platformPurchaseId = ? ORDER BY created_at DESC LIMIT 1")
    statement.setString(1, Device.StorePlatform.SWISSCOM.raw)
    statement.setString(2, abid)
    val results = statement.executeQuery()

    val subscription: SubscriptionRoot?
    if (results.next()) subscription = objectCreator.subscription(results)
    else subscription = null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.subscription_getStores(): List<SubMethodTypeModel> {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT REPLACE(REPLACE(REPLACE(SUBSTRING(COLUMN_TYPE,5),'(',''),')',''),'''','' ) as stores " + "FROM information_schema.COLUMNS " + "WHERE TABLE_SCHEMA='kidjo' " + "AND TABLE_NAME='subscriptions_root' " + "AND COLUMN_NAME='storeId'"
    )

    val results = statement.executeQuery()

    val stores: List<SubMethodTypeModel> =
        if (results.next()) utility.getSubMethods(results.getString("stores")) else arrayListOf()

    statement.close()
    connection.close()
    return stores
}

fun DatabaseController.subscription_getByCouponId(couponId: String): SevamediaSubscription? {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT u.email, s.nextBillDate, ac.couponId, ac.duration, c.name, c.short, s.operatorName FROM subscriptions_root s " + "JOIN account_coupons ac ON s.accountCouponId = ac.id " + "JOIN users u ON s.userId = u.id " + "LEFT JOIN countries c ON c.id = u.country_id " + "WHERE ac.couponId = ?  "
    )
    statement.setString(1, couponId)
    val results = statement.executeQuery()

    val subscription: SevamediaSubscription? = if (results.next()) objectCreator.sevamedia_subscription_info(results)
    else null

    statement.close()
    connection.close()
    return subscription
}

fun DatabaseController.linkUserToKidjoSubscription(userId: Long, subId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE subscriptions_root SET userId = ? WHERE id = ?")
    statement.setLong(1, userId)
    statement.setLong(2, subId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}


fun DatabaseController.updateCafeynSubscription(token: String?, nextBillDate: LocalDateTime, id: Long): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("update subscriptions_root set paymentStateId= ?,nextBillDate = ? where id =?")
    statement.setString(1, token)
    statement.setString(2, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setLong(3, id)

    val result = statement.executeAndCheck()

    statement.close()
    connection.close()
    return result
}

fun DatabaseController.updateCafeynSubscriptionType(
    token: String?,
    nextBillDate: LocalDateTime,
    id: Long,
    subType: String
): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("update subscriptions_root set paymentStateId= ?,nextBillDate = ? , subscriptionType = ? where id =?")
    statement.setString(1, token)
    statement.setString(2, mysqlDateTimeFormatter.format(nextBillDate))
    statement.setString(3, subType)
    statement.setLong(4, id)

    val result = statement.executeAndCheck()

    statement.close()
    connection.close()
    return result
}


fun DatabaseController.getLatestUserSubscriptions(id: Long): SubscriptionRoot? {
    dataSource.connection.use {
        val statement =
            it.prepareStatement("Select * from subscriptions_root where userId=? AND (isActive = true OR storeId='coupon') and nextBillDate>NOW() order by created_at desc LIMIT 1")
        statement.setLong(1, id)
        val results = statement.executeQuery()
        val subscription: SubscriptionRoot? = if (results.next()) objectCreator.subscription(results)
        else null
        statement.close()
        return subscription
    }
}

fun DatabaseController.updateSubscription(
    subscription: SubscriptionRootUpdateV1,
    subscriptionType: SubscriptionRoot.SubscriptionType,
): Boolean {
    var results = false
    dataSource.connection.use {
        val statement = it.prepareStatement(
            "Update subscriptions_root " + "set subscriptionType=?," + "isRenewing=?," + "nextBillDate=?," + "platformPurchaseId=?," + "stillInFreeTrial=?," + "paymentStateId=?," + "iapId =?," + "accountCouponId=?  " + "where id=?"
        )
        statement.setString(1, subscriptionType.raw)
        statement.setBoolean(2, subscription.isRenewing)
        statement.setString(3, mysqlDateTimeFormatter.format(subscription.nextBillDate))
        statement.setString(4, subscription.platformPurchaseId)
        statement.setBoolean(5, subscription.isRenewing)
        statement.setString(6, subscription.platformPurchaseId)
        statement.setString(7, subscription.iapId)
        statement.setLong(8, subscription.accountCouponId)
        statement.setLong(9, subscription.subId)

        results = statement.executeAndCheck();
        statement.close()
    }
    return results
}

fun DatabaseController.getExistingSubscriptionId(token: String): Long? {
    return transaction {
       val result =  net.kidjo.server.shared.models.entity.SubscriptionRoot.select {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionToken.eq(
                token
            )
        }.singleOrNull()
        result?.let { it[SubscriptionsRoot.id] }
    }
}

fun DatabaseController.getActiveSubscription(token: String): Boolean {
    return transaction {
        net.kidjo.server.shared.models.entity.SubscriptionRoot.select {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionToken.eq(
                token
            ) and (net.kidjo.server.shared.models.entity.SubscriptionRoot.isActive.eq(true)) and (net.kidjo.server.shared.models.entity.SubscriptionRoot.isRenewing.eq(
                true
            ))
        }.count() > 0
    }
}

fun DatabaseController.updateOldSubscription(subscriptionRoot: SubscriptionRootInsert): Int {
    return transaction {
        net.kidjo.server.shared.models.entity.SubscriptionRoot.update({
            net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionToken.eq(
                subscriptionRoot.subscriptionToken
            )
        }) {
            it[isActive] = true
            it[isRenewing] = true
            it[nextBillDate] = subscriptionRoot.nextBillDate
        }
    }
}

fun DatabaseController.insert_country_customer_details(twtNotification: TwtNotification) {

    var subscriptionId = 0
    transaction {

        net.kidjo.server.shared.models.entity.SubscriptionRoot.selectAll()
            .orderBy(net.kidjo.server.shared.models.entity.SubscriptionRoot.id, SortOrder.DESC).limit(1)
            .mapNotNull { row ->
                subscriptionId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt()
            }

        UserPartnerSubscription.insert {
            it[UserPartnerSubscription.subscriptionId] = subscriptionId
            it[userUuid] = twtNotification.customerId.toString()
            it[country] = twtNotification.countryISO.toString()
            it[eventType] = twtNotification.notifType.toString()
            it[subscriptionPackage] = twtNotification.packageDetails.toString()
        }
    }
}

fun updateUserPartnerSubsDetails(subId: Long, twtNotification: TwtNotification) {

    transaction {

        UserPartnerSubscription.update({
            UserPartnerSubscription.subscriptionId.eq(
                subId.toInt()
            )
        }) {
            it[subscriptionId] = subscriptionId
            it[country] = twtNotification.countryISO.toString()
            it[subscriptionPackage] = twtNotification.packageDetails.toString()
        }
    }
}

fun DatabaseController.getTCForUser(userId: Long): Int {
    return transaction {
        UsersConsent.select { UsersConsent.userId.eq(userId) }.limit(1).map { it[UsersConsent.isTCAccepted] }
            .map { isTCAccepted -> if (isTCAccepted) 1 else 0 }.firstOrNull() ?: 0
    }
}

fun DatabaseController.getPromoForUser(userId: Long): Int {
    return transaction {
        UsersConsent.select { UsersConsent.userId.eq(userId) }.limit(1).map { it[UsersConsent.isPromoAccepted] }
            .map { isPromoAccepted -> if (isPromoAccepted) 1 else 0 }.firstOrNull() ?: 0
    }
}

fun DatabaseController.getLastSubscriptionTokenByUserId(userId: Int): String {
    return transaction {
        net.kidjo.server.shared.models.entity.SubscriptionRoot
            .select {
                net.kidjo.server.shared.models.entity.SubscriptionRoot.userId eq userId
            }
            .orderBy(net.kidjo.server.shared.models.entity.SubscriptionRoot.createdAt, SortOrder.DESC)
            .limit(1)
            .map { it[net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionToken] }
            .firstOrNull() ?: "0|0"
    }
}