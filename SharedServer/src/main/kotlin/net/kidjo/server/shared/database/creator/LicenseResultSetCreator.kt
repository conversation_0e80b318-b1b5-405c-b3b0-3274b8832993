package net.kidjo.server.shared.database.creator

import net.kidjo.common.models.License
import java.sql.ResultSet
import java.sql.SQLException

fun ResultSetObjectCreator.license(resultSet: ResultSet): License {
    val id = resultSet.getLong("id")
    val title = resultSet.getString("title")
    val description = resultSet.getString("description")
    val image = resultSet.getString("image")
    val genre = resultSet.getString("type")
    val languageId = resultSet.getLong("languageId")
    val folderId = resultSet.getLong("folderId")
    val swisscom = resultSet.getBoolean("swisscom")
    val website = resultSet.getBoolean("website")

    val countries = try {
        val csv = resultSet.getString("countries")
        csv?.split(",") ?: emptyList()
    } catch (exception: SQLException) {
        emptyList<String>()
    }
    val language = try {
        resultSet.getString("languages")
    } catch (exception: SQLException) {
        ""
    }
    return License(id, title, description, image, genre, languageId, folderId, swisscom, website, countries = countries, language = language)
}
