package net.kidjo.server.shared.models

import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType

data class VirgoLinkedUser(
    var id: Int,
    var opeId: String,
    var virgoAlias: String,
    var subscriptionId: Long?,
    var subscriptionToken: String?,
    var userId: Long,
    val offerId: Long?,
    val packageId: Long?,
    val correlationId: String?,
    var sendEmail: Boolean,
    var subStatus: SubscriptionStatusType
)