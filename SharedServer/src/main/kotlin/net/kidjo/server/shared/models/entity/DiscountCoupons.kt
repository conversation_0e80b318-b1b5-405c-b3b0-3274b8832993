package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table

object DiscountCoupons : Table("discounts_coupons") {
    val id = integer("id").autoIncrement()
    val discountId = integer("discountId")
    val couponId = integer("couponId")
    val price = decimal("price", 10, 2)
    val applyForBundle=bool("applyForBundle")
    override val primaryKey: PrimaryKey
        get() = PrimaryKey(id)
}
