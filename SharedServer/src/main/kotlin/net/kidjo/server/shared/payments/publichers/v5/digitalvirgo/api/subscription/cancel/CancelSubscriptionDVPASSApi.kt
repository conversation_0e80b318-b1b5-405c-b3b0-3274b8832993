package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.cancel
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response.DVCancelSubscriptionDTO
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.buildRequestHeaders
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security.generatedBearerToken
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.DVSubscriptionApi
import okhttp3.MediaType
import okhttp3.RequestBody
import okhttp3.Response
import org.json.JSONObject
suspend fun DVSubscriptionApi.cancelSubscriptionDVPASSApi(
    subscriptionIdString: String,
    correlationId: String,
    baseUrl: String,
    login: String,
    secret: String
): DVCancelSubscriptionDTO? {
    var responsePATCH: Response? = null
    try {
        logger.info("CANCELING DV Subscription | cancelSubscriptionDVPASSApi ")

        val jsonMediaType = MediaType.parse("application/json; charset=utf-8")
        val jsonBody = JSONObject()

        val subscriptionCancelURL = "https://$baseUrl/service/subscriptions/$subscriptionIdString/cancel"
        logger.info("CANCELING DV Subscription URL API | subscriptionCancelURL ")

        val bearerToken = generatedBearerToken(
            sub = baseUrl,
            iss = login,
            secret = secret,
            validity = 60000)

        val requestPATCH =
            buildRequestHeaders(
                url = subscriptionCancelURL,
                token = bearerToken).
            patch(
                RequestBody.create(
                    jsonMediaType,
                    jsonBody.
                    put("correlationId", correlationId).
                    toString()
                )
            ).
            build()

            responsePATCH = withContext(Dispatchers.IO) {
                httpClient.
                newCall(requestPATCH).
                execute()
            }


        when (responsePATCH.code()) {
            HttpStatusCode.OK.value -> {
                logger.info("Successful Cancellation | main response 200 OK")

                val bodyString = withContext(Dispatchers.IO) { responsePATCH.body()!!.string() }
                val responseCancel =
                    bodyString.let {
                        jacksonObjectMapper().
                        configure(
                            DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                            false
                        ).
                        readValue<DVCancelSubscriptionDTO>(it)
                    }
                logger.info("DV CANCELING DV Subscription Response BODY: $bodyString")

                when (responseCancel.code){
                    3400, 4401-> {
                        responseCancel.data?.status = SubscriptionStatusType.CANCELLED.raw
                        logger.info("Subscription already cancelled | response 3400, 4401 : $responseCancel ")
                        responsePATCH.close()

                        return responseCancel
                    }
                    3401,4402 -> {
                        responseCancel.data?.status = SubscriptionStatusType.EXPIRED.raw
                        logger.info("Subscription already expired | response 3401,4402: $responseCancel ")
                        responsePATCH.close()

                        return responseCancel
                    }
                    4400 -> {
                        responseCancel.data?.status = SubscriptionStatusType.TERMINATED.raw
                        logger.info("Subscription unknown | response 4400: $responseCancel ")
                        responsePATCH.close()

                        return responseCancel
                    }
                }
                responsePATCH.close()

                return responseCancel
            }
            else -> {
                logger.error("Unsuccessful Cancellation | response: ${responsePATCH.code()} ")
                responsePATCH.close()
                return null
            }
        }
    } catch (e: Exception) {
        logger.error("Error DV subscriptionInfoResponse, ${e.localizedMessage}")
        throw e
    } finally {
        responsePATCH?.close()
    }
}
