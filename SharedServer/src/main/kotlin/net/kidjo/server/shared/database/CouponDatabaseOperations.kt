package net.kidjo.server.shared.database

import java.sql.Timestamp
import java.time.LocalDateTime

fun DatabaseController.couponSet(couponId: Long, deviceId: Long): Boolean {
    val connection = dataSource.connection
    val statement1 = connection.prepareStatement("UPDATE coupons SET redeemed = 1, validCountLeft = validCountLeft - 1 WHERE id= ?")
    statement1.setLong(1, couponId)
    var results = statement1.executeAndCheck()
    statement1.close()

    if (results) {
        val statement2 = connection.prepareStatement("INSERT INTO coupons_devices(couponId, deviceId, activation) VALUES (?, ?, ?)")
        statement2.setLong(1, couponId)
        statement2.setLong(2, deviceId)
        statement2.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()))
        results = statement2.executeAndCheck()
        statement2.close()
    }

    connection.close()
    return results
}

fun DatabaseController.couponV4Set(couponId: Long): Boolean {
    val connection = dataSource.connection
    val statement1 = connection.prepareStatement("UPDATE coupons SET redeemed = 1, validCountLeft = validCountLeft - 1 WHERE id= ?")
    statement1.setLong(1, couponId)
    var results = statement1.executeAndCheck()
    statement1.close()

    connection.close()
    return results
}

fun DatabaseController.couponUpdateDevice(couponId: Long, deviceId: Long, oldDeviceId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE coupons_devices SET deviceId = ? WHERE couponId = ? AND deviceId = ?")
    statement.setLong(1, deviceId)
    statement.setLong(2, couponId)
    statement.setLong(3, oldDeviceId)
    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}
