package net.kidjo.server.shared.models

import java.time.LocalDateTime

data class BackgroundTaskLog(val id: Long,
                             val version: String,
                             val type: TaskType,
                             val shortMessage: String,
                             val longMessage: String,
                             val start: LocalDateTime,
                             val end: LocalDateTime?) {
    enum class TaskType(val raw: String) {
        PROCESS_SUBSCRIPTION("subscription")
    }
    enum class Results(val levelToLog: Int, val raw: String, val isError: <PERSON>olean) {
        RUNNING(0, "running", false),
        SUCCESS(1, "success", false),
        NON_FATAL_ERROR(2, "non_fatal_error", true),
        FATAL_ERROR(3,"fatal_error", true)
    }
    companion object {
        const val NO_ID = 0L
        const val MAX_LENGTH_OF_SHORT_MESSAGE = 255
    }
}