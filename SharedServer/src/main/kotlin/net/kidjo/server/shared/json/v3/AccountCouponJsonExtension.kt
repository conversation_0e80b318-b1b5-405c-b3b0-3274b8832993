package net.kidjo.server.shared.json.v3

import net.kidjo.server.shared.models.AccountCoupon
import org.json.JSONObject


fun JsonObjectCreatorV3.accountCoupon_toJson(accountCoupon: AccountCoupon): JSONObject {
    val json = JSONObject()
    json.put("id",accountCoupon.couponId)
    json.put("duration",accountCoupon.freeTrialIsInMonths)
    json.put("durationType",accountCoupon.freeTrialSymbol)
    return json
}