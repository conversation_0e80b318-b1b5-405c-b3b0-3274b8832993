package net.kidjo.server.shared.database

import net.kidjo.common.models.Folder
import net.kidjo.server.shared.database.creator.folder
import net.kidjo.server.shared.database.creator.folderSimple
import net.kidjo.server.shared.database.creator.subscription
import net.kidjo.server.shared.models.SubscriptionRoot
import java.sql.SQLException

fun DatabaseController.folders_get(folderId: Long): Folder? {
    val connection = dataSource.connection
    val sql = "SELECT id,title,type,videoCount,compileCount,mediaType FROM folders WHERE id = ?"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setLong(1, folderId)

        val results = statement.executeQuery()
        val folder: Folder?
        if (results.next()) folder = objectCreator.folder(results, false)
        else folder = null

        return folder
    } catch (e: SQLException) {
        println("Error getting: folders_get ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.getFolderById(folderId: Long): Folder? {
    val connection = dataSource.connection
    val sql = "SELECT id, title, type, description, mediaType FROM folders WHERE id = ?"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setLong(1, folderId)

        val results = statement.executeQuery()
        val folder: Folder?
        if (results.next()) folder = objectCreator.folderSimple(results)
        else folder = null

        return folder
    } catch (e: SQLException) {
        println("Error getting: videoSearch ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.getFolderMixedGameId(): Folder? {
    val connection = dataSource.connection
    val sql = "SELECT id, title, type, description, mediaType FROM folders WHERE mediaType = ?"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setString(1, Folder.MediaType.MIXED_GAMES.raw)

        val results = statement.executeQuery()
        val folder: Folder?
        if (results.next()) folder = objectCreator.folderSimple(results)
        else folder = null

        return folder
    } catch (e: SQLException) {
        println("Error getting: getFolderMixedGameId ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.folderSearch(
    search: String,
    languageId: Int,
    countryId: Int,
    age: Int,
    folderType: Folder.ContentType,
    folderFilter: String,
    includeGames: Boolean = true,
    excludeFolderIds: String? = null
): List<Folder> {
    val connection = dataSource.connection

    // Prepare folder filters query
    var folderFilterAsQuery = ""
    val folderFilterArray = folderFilter.split(',')
    folderFilterArray.forEach { value ->
        folderFilterAsQuery += "?,"
    }
    if (folderFilterAsQuery != "") {
        folderFilterAsQuery =
            "AND folderId NOT IN(" + folderFilterAsQuery.substring(0, folderFilterAsQuery.length - 1) + ") "
    }

    val searchTerms = search.split(" ")

    // Base SQL request
    var sql = "SELECT id, title, type, videoCount, compileCount, mediaType " +
            "FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "WHERE folders.languageId IN (0, ?) " +
            "AND folders_countries.countryId IN (0, ?) " +
            (if (folderType != Folder.ContentType.MIXED) "AND folders.type = ? " else "") +
            (if (!includeGames) "AND mediaType = 'video' " else "") +
            (if (!excludeFolderIds.isNullOrBlank()) "AND folders.id NOT IN ($excludeFolderIds) " else "") +
            folderFilterAsQuery +
            "AND isActive = 1 " +
            "AND ageMin <= ? " +
            "AND ageMax >= ? "
    repeat(searchTerms.size) { sql += "AND folders. title COLLATE UTF8_GENERAL_CI LIKE ? " }
    sql += "ORDER BY folders.order ASC"

    // Prepare SQL statement with parameters
    val statement = connection.prepareStatement(sql)
    try {
        statement.setInt(1, languageId)
        statement.setInt(2, countryId)
        var index = 3
        if (folderType != Folder.ContentType.MIXED) {
            statement.setString(index, folderType.raw)
            index++
        }
        folderFilterArray.forEach { value ->
            statement.setString(index, value)
            index++
        }
        statement.setInt(index, age)
        statement.setInt(index + 1, age)
        searchTerms.forEachIndexed { i, term ->
            statement.setString(index + 2 + i, "%$term%")
        }

        // Execute final query
        val results = statement.executeQuery()

        val folders = ArrayList<Folder>()
        var i = 0
        while (results.next()) {
            folders.add(objectCreator.folder(results, false))
            i++
        }

        return folders
    } catch (e: SQLException) {
        println("Error getting: getFolderMixedGameId ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.folders_getList(
    fetchLimit: Int, skip: Int, languageId: Int, countryId: Int, age: Int,
    folderType: Folder.ContentType, folderFilter: String,
    filterOutGames: Boolean
): List<Folder> {

    val connection = dataSource.connection

    var folderFilterAsQuery = ""
    val folderFilterArray = folderFilter.split(',')
    folderFilterArray.forEach { _ ->
        folderFilterAsQuery += "?,"
    }
    if (folderFilterAsQuery != "") {
        folderFilterAsQuery =
            "AND folderId NOT IN(" + folderFilterAsQuery.substring(0, folderFilterAsQuery.length - 1) + ") "
    }

    val sql = "SELECT id,title,type,videoCount,compileCount,mediaType " +
            "FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "WHERE folders.languageId IN (0, ? ) " +
            "AND folders_countries.countryId IN (0, ? ) " +
            (if (folderType != Folder.ContentType.MIXED) "AND folders.type = ? " else "") +
            (if (filterOutGames) "AND mediaType = '${Folder.MediaType.VIDEO.raw}' " else "") +
            folderFilterAsQuery +
            "AND isActive = 1 " +
            "AND ageMin <= ? " +
            "AND ageMax >= ? " +
            "ORDER BY folders.order ASC"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setInt(1, languageId)
        statement.setInt(2, countryId)
        var index = 3
        if (folderType != Folder.ContentType.MIXED) {
            statement.setString(index, folderType.raw)
            index++
        }
        folderFilterArray.forEach { value ->
            statement.setString(index, value)
            index++
        }
        statement.setInt(index, age)
        statement.setInt(index + 1, age)

        //set fetch limit to the number of cards retrieved, but only if its below the fetch limit
        statement.fetchSize = kotlin.math.min(fetchLimit, config.db_maxFetch)

        val results = statement.executeQuery()

        val folders = ArrayList<Folder>()
        results.absolute(skip)
        var i = 0
        while (results.next() && i < fetchLimit) {
            folders.add(objectCreator.folder(results, false))
            i++
        }

        return folders
    } catch (e: SQLException) {
        println("Error getting: folders_getList ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}
fun DatabaseController.folder_videos_getListWithProcess(
    batchSize: Int,
    languageId: Int, countryId: Int, age: Int,
    process: (batch: List<Folder>) -> Unit
) {
    val connection = dataSource.connection
    val sql = "SELECT id,title,type,videoCount,compileCount,mediaType " +
            "FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "WHERE folders.languageId IN (0, ? ) " +
            "AND folders_countries.countryId IN (0, ? ) " +
            "AND mediaType = 'video' " +
            "AND isActive = 1 " +
            "AND ageMin <= ? " +
            "AND ageMax >= ? " +
            "ORDER BY folders.order ASC"
    val statement = connection.prepareStatement(sql)

    val fetchSize = kotlin.math.min(batchSize, config.db_maxFetch)
    statement.fetchSize = fetchSize

    statement.setInt(1, languageId)
    statement.setInt(2, countryId)
    statement.setInt(3, age)
    statement.setInt(4, age)

    val results = statement.executeQuery()
    val batch = ArrayList<Folder>()
    while (results.next()) {
        batch.add(objectCreator.folder(results, false))

        if (batch.size >= fetchSize) {
            process(batch)
            batch.clear()
        }
    }

    if (batch.size > 0) process(batch)

    statement.close()
    connection.close()

}
fun DatabaseController.folder_videos_getList(
    languageId: Int, countryId: Int, age: Int,
    folderType: Folder.ContentType
): List<Folder> {

    val connection = dataSource.connection


    val sql = "SELECT id,title,type,videoCount,compileCount,mediaType " +
            "FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "WHERE folders.languageId IN (0, ? ) " +
            "AND folders_countries.countryId IN (0, ? ) " +
            (if (folderType != Folder.ContentType.MIXED) "AND folders.type = ? " else "") +
            "AND mediaType = 'video' " +
            "AND isActive = 1 " +
            "AND ageMin <= ? " +
            "AND ageMax >= ? " +
            "ORDER BY folders.order ASC"
    val statement = connection.prepareStatement(sql)
    try {
        statement.setInt(1, languageId)
        statement.setInt(2, countryId)

        var index = 3

        if (folderType != Folder.ContentType.MIXED) {
            statement.setString(index, folderType.raw)
            index++
        }

        statement.setInt(index, age)
        statement.setInt(index + 1, age)

        val results = statement.executeQuery()
        val folders = ArrayList<Folder>()
        var i = 0

        while (results.next()) {
            folders.add(objectCreator.folder(results, false))
            i++
        }

        return folders
    } catch (e: SQLException) {
        println("Error getting: folder_videos_getList ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.folders_getFilterList(kidId: Long, age: Int, languageId: Int, countryId: Int): List<Folder> {
    val connection = dataSource.connection
    val sql = "SELECT id,title,type,avatars_folders.isActive as filterIsNotActive,mediaType FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "LEFT JOIN avatars_folders on folders.id = avatars_folders.folderId AND avatars_folders.avatarId = ? " +
            "WHERE folders.languageId IN (0,?) " +
            "AND folders_countries.countryId IN (0,?) " +
            "AND folders.isActive = 1 " +
            "AND folders.ageMin <= ? " +
            "AND folders.ageMax >= ? " +
            "order by folders.type,folders.title"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setLong(1, kidId)
        statement.setInt(2, languageId)
        statement.setInt(3, countryId)
        statement.setInt(4, age)
        statement.setInt(5, age)

        statement.fetchSize = config.db_maxFetch

        val results = statement.executeQuery()
        val folders = ArrayList<Folder>()
        while (results.next()) {
            folders.add(objectCreator.folder(results, true))
        }

        return folders
    } catch (e: SQLException) {
        println("Error getting: folders_getFilterList ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.getFilterList(
    age: Int,
    languageId: Int,
    countryId: Int,
    folderType: Folder.ContentType
): List<Folder> {
    val connection = dataSource.connection
    var sql = "SELECT id,title,type,mediaType FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "WHERE folders.languageId IN (0,?) " +
            "AND folders_countries.countryId IN (0,?) " +
            "AND folders.isActive = 1 " +
            "AND folders.ageMin <= ? " +
            "AND folders.ageMax >= ? "

    (if (folderType != Folder.ContentType.MIXED) sql += " AND folders.type = ?")

    sql += " ORDER BY folders.type,folders.title"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setInt(1, languageId)
        statement.setInt(2, countryId)
        statement.setInt(3, age)
        statement.setInt(4, age)

        if (folderType != Folder.ContentType.MIXED) {
            statement.setString(5, folderType.raw)
        }

        statement.fetchSize = config.db_maxFetch

        val results = statement.executeQuery()
        val folders = ArrayList<Folder>()
        while (results.next()) {
            folders.add(objectCreator.folder(results, false))
        }

        return folders
    } catch (e: SQLException) {
        println("Error getting: getFilterList ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.foldersByCountryAndLanguage(limit: Int, languageId: Int, countryId: Int): List<Folder> {
    val connection = dataSource.connection
    val sql = "SELECT id, title, type, mediaType, description FROM folders " +
            "JOIN folders_countries ON folders.id = folders_countries.folderId " +
            "WHERE folders.languageId IN (0, ?) " +
            "AND folders_countries.countryId IN (0, ?) " +
            "AND isActive = 1 " +
            "ORDER BY folders.order ASC " +
            "LIMIT ?"

    val statement = connection.prepareStatement(sql)
    try {
        statement.setInt(1, languageId)
        statement.setInt(2, countryId)
        statement.setInt(3, limit)

        statement.fetchSize = config.db_maxFetch

        val results = statement.executeQuery()
        val folders = ArrayList<Folder>()
        while (results.next()) {
            folders.add(objectCreator.folderSimple(results))
        }

        return folders
    } catch (e: SQLException) {
        println("Error getting: foldersByCountryAndLanguage ${e.localizedMessage}")
        return emptyList()
    } finally {
        statement.close()
        connection.close()
    }
}