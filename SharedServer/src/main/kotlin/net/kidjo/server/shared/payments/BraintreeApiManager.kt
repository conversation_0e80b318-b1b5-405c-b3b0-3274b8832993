package net.kidjo.server.shared.payments

import com.braintreegateway.BraintreeGateway
import com.braintreegateway.ClientTokenRequest
import com.braintreegateway.CreditCard
import com.braintreegateway.CustomerRequest
import com.braintreegateway.Environment
import com.braintreegateway.PayPalAccount
import com.braintreegateway.PaymentMethodRequest
import com.braintreegateway.ResourceCollection
import com.braintreegateway.Subscription
import com.braintreegateway.SubscriptionRequest
import com.braintreegateway.Transaction
import com.braintreegateway.TransactionSearchRequest
import com.braintreegateway.exceptions.NotFoundException
import io.ktor.server.application.ApplicationCall
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.accountCoupon_consumeCoupon
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.subscriptionRoot_update
import net.kidjo.server.shared.database.subscriptionTransaction_add
import net.kidjo.server.shared.database.subscription_cancel
import net.kidjo.server.shared.database.subscription_getByHash
import net.kidjo.server.shared.database.subscription_update
import net.kidjo.server.shared.database.user_setBrainUserId
import net.kidjo.server.shared.extensions.Success
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import net.kidjo.server.shared.models.SubscriptionTransaction
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.GregorianCalendar


private const val MAX_NAME_LENGTH_BRAIN_TREE = 250

class BraintreeApiManager(
    internal val config: Config,
    internal val databaseController: DatabaseController,
    internal val encryptionController: EncryptionController,
    internal val utility: Utility
) {

    internal val logger = LoggerFactory.getLogger("BraintreeApiManager")

    internal val brainTreeGateway = BraintreeGateway(
        if (config.brainTree_useSandbox) Environment.SANDBOX else Environment.PRODUCTION,
        config.brainTree_merchandId,
        config.brainTree_publicKey,
        config.brainTree_privateKey
    )

    suspend fun startBraintreeSubscription(
        device: Device, user: User, sessionId: String, inAppPurchase: InAppPurchase, forceNoFreeTrial: Boolean,
        subscriptionStartDate: LocalDateTime?, subscriptionType: SubscriptionRoot.SubscriptionType,
        withCoupon: AccountCoupon?, call: ApplicationCall
    ) {
        if (inAppPurchase.store != Device.StorePlatform.KIDJO_BRAINTREE) {
            logger.error("Error trying to start a subscription with non brain tree")
            return call.respondBadRequest("Error trying to start a subscription with non brain tree.")
        }

        val customer = brainTreeGateway.customer().find(user.brainTreeId)
        if (customer == null) {
            logger.error("could not find brain tree customer for user: $user")
            return call.respondBadRequest("Could not find brain tree customer for user: $user")
        }

        logger.info("[BraintreeApiManager.startBraintreeSubscription] Finding customer: $customer for the User $user")

        val paymentMethod = customer.defaultPaymentMethod
        val subscriptionRequest = SubscriptionRequest()
            .merchantAccountId(inAppPurchase.merchantAccountId.raw)
            .paymentMethodToken(paymentMethod.token)
            .planId(inAppPurchase.id)

        val usingCouponId = withCoupon?.id ?: 0L
        var willHaveFreeTrial = !inAppPurchase.noFreeTrial
        var priceToBeChargedImmediately: Float = 0.0f //don't track any revenue unless there is no free trial

        //Braintree works only with Subscription.DurationUnit MONTH and DAY
        if (withCoupon != null) {
            willHaveFreeTrial = true
            val freeTrialTime = utility.getDayMonthFromFreeTrialISO8601Code(withCoupon.durationCode)
            subscriptionRequest.trialDuration(freeTrialTime.toInt())
            subscriptionRequest.trialDurationUnit(if (withCoupon.durationCode.contains("M")) Subscription.DurationUnit.MONTH else Subscription.DurationUnit.DAY)

        } else if (forceNoFreeTrial) { //only remove free trial if there is no coupon
            willHaveFreeTrial = false
            priceToBeChargedImmediately = inAppPurchase.price.toFloat()
            subscriptionRequest.trialDuration(0)
        }

        if (subscriptionStartDate != null) {
            subscriptionRequest.firstBillingDate(GregorianCalendar.from(subscriptionStartDate.atZone(ZoneId.systemDefault())))
            priceToBeChargedImmediately = 0.0f
        }

        val results = brainTreeGateway.subscription().create(subscriptionRequest)

        if (!results.isSuccess) {
            //todo handle errors
            logger.error(results.message)
            return call.respondBadRequest("Could not create braintree subscription.")
        }

        val subscription = results.target

        val paymentType: SubscriptionRoot.PaymentType
        val paymentId: String
        if (paymentMethod is PayPalAccount) {
            paymentType = SubscriptionRoot.PaymentType.PAY_PAL
            paymentId = paymentMethod.email
        } else if (paymentMethod is CreditCard) {
            paymentType = SubscriptionRoot.PaymentType.CREDIT_CARD
            paymentId = paymentMethod.last4
        } else {
            logger.error("Didn't catch payment method ${paymentMethod} for user $user")
            paymentType = SubscriptionRoot.PaymentType.CREDIT_CARD
            paymentId = "TODO"
        }

        if (withCoupon != null) {
            databaseController.accountCoupon_consumeCoupon(usingCouponId)
        }

        val subscriptionToString = subscriptionToString(subscription)
        logger.info("[BraintreeApiManager.startBraintreeSubscription] Customer ${customer.email} || Subscription: $subscriptionToString")

        val subscriptionNextBillingDate = subscription.nextBillingDate.toInstant()
        val zonedDateTime: ZonedDateTime = ZonedDateTime.ofInstant(subscriptionNextBillingDate,
            subscription.nextBillingDate.timeZone.toZoneId())
            .withHour(23)
            .withMinute(59)
            .withSecond(0)

        val endOfTheDayInstant: Instant = zonedDateTime.toInstant()
        logger.debug("[BraintreeApiManager.startBraintreeSubscription] Customer ${customer.email} || End of the day Instant: $endOfTheDayInstant")

        val nextBillingDate =
            LocalDateTime.ofInstant(
                endOfTheDayInstant,
                subscription.nextBillingDate.timeZone.toZoneId()
            )

        logger.info("[BraintreeApiManager.startBraintreeSubscription] Customer ${customer.email} || NextBillingDate: $nextBillingDate")

        val subscriptionRootInsert = SubscriptionRootInsert(
            user.getLongId(), device.serverId, willHaveFreeTrial,
            priceToBeChargedImmediately, paymentType, subscriptionType,
            paymentId, subscription.id, Device.StorePlatform.KIDJO_BRAINTREE, null, inAppPurchase.id,
            sessionId, subscription.id, encryptionController.sha256Hash(subscription.id), usingCouponId,
            true, nextBillingDate, !config.env.isLive
        )

        return startTrackingSubscription(subscriptionRootInsert, call)

    }

    suspend fun updateBraintreeSubscription(
        user: User,
        subscription: SubscriptionRoot,
        inAppPurchase: InAppPurchase,
        call: ApplicationCall
    ) {
        if (inAppPurchase.store != Device.StorePlatform.KIDJO_BRAINTREE) {
            logger.error("Error trying to update a subscription with non brain tree")
            return call.respondBadRequest("Error trying to update a subscription with non brain tree.")
        }

        val customer = brainTreeGateway.customer().find(user.brainTreeId)
        if (customer == null) {
            logger.error("could not find brain tree customer for user: $user")
            return call.respondBadRequest("Could not find brain tree customer for user: $user")
        }

        val paymentMethod = customer.defaultPaymentMethod
        val subscriptionRequest = SubscriptionRequest()
            .paymentMethodToken(paymentMethod.token)
            .price(inAppPurchase.price.toBigDecimal())
            .planId(inAppPurchase.id)

        val results = brainTreeGateway.subscription().update(subscription.subscriptionToken, subscriptionRequest)

        if (!results.isSuccess) {
            //todo handle errors
            logger.error(results.message)
            return call.respondBadRequest("Could not update braintree subscription.")
        }

        val updatedSubscription = results.target

        val nextBillingDate =
            LocalDateTime.ofInstant(
                updatedSubscription.nextBillingDate.toInstant(),
                updatedSubscription.nextBillingDate.timeZone.toZoneId()
            )

        val subscriptionRootUpdate = SubscriptionRootUpdate(
            user.getLongId(), subscription.isRenewing, nextBillingDate, subscription.platformPurchaseId,
            subscription.stillInFreeTrial, 0.0F, subscription.id, null
        )

        updateSubscription(subscription.id.toString(), subscriptionRootUpdate, call)

    }

    fun cancelSubscription(user: User, subscription: SubscriptionRoot, cancelingFromUser: Boolean): Boolean {
        if (user.getLongId() != subscription.userId || !subscription.isRenewing) return false

        var success = false

        logger.info("Unsubscribe subscription from ${subscription.storeId} - ${subscription.subscriptionToken}")

        if (subscription.storeId == Device.StorePlatform.KIDJO_BRAINTREE) {
            val results = brainTreeGateway.subscription().cancel(subscription.subscriptionToken)
            if (results.isSuccess) {
                success = databaseController.subscription_cancel(subscription.id)
                if (!success) {
                    logger.error("Database Error, setting this subscription to stop renewing: $subscription")
                }
                val transactionType =
                    if (cancelingFromUser) SubscriptionTransaction.TransactionType.CANCEL_USER else SubscriptionTransaction.TransactionType.CANCEL_ADMIN
                success = databaseController.subscriptionTransaction_add(subscription.id, transactionType, 0f)
            } else {
                logger.error(results.message)
            }
        } else if (subscription.storeId == Device.StorePlatform.PLAYSTORE) {
            logger.error("Tried canceling subscription, but could not: $subscription")
        } else if (subscription.storeId == Device.StorePlatform.MONDIA_MEDIA) {
            val client = OkHttpClient()

            val operator = MondiaOperators.GetByOpName(subscription.operatorName) ?: MondiaOperators.ORANGE_TUNISIA

            val url: String
            if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) {
                url = operator.productionBackendAPI
            } else if (config.env == Config.Env.STAGING) {
                url = operator.stagingBackendAPI
            } else {
                // Not staging or production
                url = operator.productionBackendAPI
            }

            val request: Request = Request.Builder()
                .url("${url}subservice/unsubscribe?subid=${subscription.subscriptionToken}&operatorId=${operator.id}")
                .header(
                    "username",
                    if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_USERNAME_PRODUCTION else MONDIA_API_USERNAME_STAGING
                )
                .header(
                    "password",
                    if (config.env == Config.Env.PROD || config.env == Config.Env.DEVELOP) MONDIA_API_PASSWORD_PRODUCTION else MONDIA_API_PASSWORD_STAGING
                )
                .build()

            // Debug
            logger.info("Unsubscribe mondia user ${subscription.subscriptionToken}")
            logger.info(request.toString())

            val response: Response = client.newCall(request).execute()

            if (response.isSuccessful) {
                databaseController.subscription_cancel(subscription.id)
                success = true
            } else {
                logger.error("Failed to cancel subscription for Mondia: ${subscription}")
            }

            response.close()
        } else {
            logger.error("Tried canceling subscription, but could not: $subscription")
        }

        return success
    }

    suspend fun startTrackingSubscription(subscriptionRoot: SubscriptionRootInsert, call: ApplicationCall) {
        val id: Long
        val checkSubscription =
            databaseController.subscription_getByHash(subscriptionRoot.storeId, subscriptionRoot.subscriptionTokenHash)

        if (checkSubscription != null) {

            if (subscriptionRoot.userId != checkSubscription.userId) {
                return call.respondBadRequest("This subscription already belongs to another account. Please login.")
            }

            val checkUpdate = databaseController.subscription_update(
                subscriptionRoot.userId,
                subscriptionRoot.isRenewing,
                subscriptionRoot.nextBillDate,
                subscriptionRoot.platformPurchaseId,
                subscriptionRoot.isFreeTrail,
                checkSubscription.id,
                subscriptionRoot.paymentStateId
            );

            if (!checkUpdate) {
                logger.error("Error update user: ${subscriptionRoot.userId}")
                return call.respondBadRequest("Error update user: ${subscriptionRoot.userId}")
            } else {
                return call.respondOK("Successful updated subscription to user id: ${subscriptionRoot.userId} ")
            }
        } else {
            id = databaseController.subscriptionRoot_create(subscriptionRoot)
            if (id > 0) {
                return call.respondOK("Successful created Subscription to User id: ${subscriptionRoot.userId} ")
            }
        }

        if (id == SubscriptionRoot.NO_ID) {
            logger.error("Error logging users subscription- $subscriptionRoot")
            return call.respondBadRequest("Error logging users subscription- $subscriptionRoot")
        }

        return call.respondOK("Successful  create subscription")
    }

    fun createCustomer(user: User): Boolean {
        val customerRequest = CustomerRequest()
        if (user.email != "") customerRequest.email(user.email)

        val results = brainTreeGateway.customer().create(customerRequest)
        if (results.isSuccess) {
            databaseController.user_setBrainUserId(user.getLongId(), results.target.id)
            return true
        }
        return false
    }

    fun createClientToken(user: User): String {
        val clientTokenRequest = ClientTokenRequest()
            .customerId(user.brainTreeId)
        println("user: " + user)
        println("clientTokenRequest: " + clientTokenRequest)
        return brainTreeGateway.clientToken().generate(clientTokenRequest) ?: "xxToken"
    }

    fun getLastTransactionBraintreeCustomer(customerId: String): Transaction? {
        try {
            val customer = brainTreeGateway.customer().find(customerId)
            if (customer != null) {
                val request = TransactionSearchRequest().customerId().`is`(customer.id)
                val collection: ResourceCollection<Transaction> = brainTreeGateway.transaction().search(request)
                return collection.lastOrNull()
            }
        } catch (e: NotFoundException) {
            println(e.message)
        }
        return null
    }

    fun getBraintreeSub(token: String): com.braintreegateway.Subscription? {
        var braintreeSub: com.braintreegateway.Subscription? = null
        try {
            braintreeSub = brainTreeGateway.subscription().find(token)
        } catch (e: NotFoundException) {
            println(e.message)
        }
        return braintreeSub
    }

    fun setPaymentDefaultMethod(
        user: User,
        nameOnPaymentMethod: String,
        nonce: String,
        deviceData: String
    ): PaymentError {

        val addPaymentRequest = PaymentMethodRequest()
            .paymentMethodNonce(nonce)
            .customerId(user.brainTreeId)
            .options()
            .failOnDuplicatePaymentMethod(false)
            .verifyCard(true)
            .verificationAmount("0.00")
            .makeDefault(true)
            .done()
        if (deviceData != "") addPaymentRequest.deviceData(deviceData)

        if (nameOnPaymentMethod != "") {
            val size = kotlin.math.min(MAX_NAME_LENGTH_BRAIN_TREE, nameOnPaymentMethod.length)
            val name = nameOnPaymentMethod.substring(0, size)
            addPaymentRequest.cardholderName(name)
        }

        val addPayment = brainTreeGateway.paymentMethod().create(addPaymentRequest)

        if (!addPayment.isSuccess) {
            //todo handle errors
            if (addPayment.creditCardVerification != null) {
                println(addPayment.creditCardVerification.processorResponseText)
            } else {
                println(addPayment.message)
            }

            return PaymentError.ISSUE_ADDING_CARD
        }
        return PaymentError.NONE
    }

    suspend fun updateSubscription(
        subscriptionId: String,
        subscriptionRootUpdate: SubscriptionRootUpdate,
        call: ApplicationCall
    ) {
        val checkUpdate = databaseController.subscriptionRoot_update(subscriptionRootUpdate);
        if (!checkUpdate) {
            logger.error("Error update user's subscription: ${subscriptionId}")
            return call.respondBadRequest("Error update user's subscription: ${subscriptionId}")
        } else {
            return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${subscriptionId} ")
        }
    }

    enum class PaymentError(val noError: Boolean) {
        NONE(true), ISSUE_ADDING_CARD(false), ISSUE_CHARGING_CARD(false)
    }


    private fun subscriptionToString(subscription: Subscription): String {
        return "Subscription{" +
                "addOns=" + subscription.addOns +
                ", balance=" + subscription.balance +
                ", billingDayOfMonth=" + subscription.billingDayOfMonth +
                ", billingPeriodEndDate=" + subscription.billingPeriodEndDate +
                ", billingPeriodStartDate=" + subscription.billingPeriodStartDate +
                ", currentBillingCycle=" + subscription.currentBillingCycle +
                ", daysPastDue=" + subscription.daysPastDue +
                ", descriptor=" + subscription.descriptor +
                ", description='" + subscription.description + '\'' +
                ", discounts=" + subscription.discounts +
                ", failureCount=" + subscription.failureCount +
                ", createdAt=" + subscription.createdAt +
                ", updatedAt=" + subscription.updatedAt +
                ", firstBillingDate=" + subscription.firstBillingDate +
                ", hasTrialPeriod=" + subscription.hasTrialPeriod() +
                ", id='" + subscription.id + '\'' +
                ", merchantAccountId='" + subscription.merchantAccountId + '\'' +
                ", neverExpires=" + subscription.neverExpires() +
                //", nextBillAmount=" + subscription.nextBillAmount +
                ", nextBillingDate=" + subscription.nextBillingDate +
                ", nextBillingPeriodAmount=" + subscription.nextBillingPeriodAmount +
                ", numberOfBillingCycles=" + subscription.numberOfBillingCycles +
                ", paidThroughDate=" + subscription.paidThroughDate +
                ", paymentMethodToken='" + subscription.paymentMethodToken + '\'' +
                ", planId='" + subscription.planId + '\'' +
                ", price=" + subscription.price +
                ", status=" + subscription.status +
                ", statusHistory=" + subscription.statusHistory +
                ", transactions=" + subscription.transactions +
                ", trialDuration=" + subscription.trialDuration +
                ", trialDurationUnit=" + subscription.trialDurationUnit +
                '}'
    }
}


