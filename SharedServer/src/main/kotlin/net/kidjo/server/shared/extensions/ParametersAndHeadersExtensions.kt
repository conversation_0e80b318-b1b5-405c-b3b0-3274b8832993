package net.kidjo.server.shared.extensions

import io.ktor.http.Headers
import io.ktor.http.Parameters
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.tools.EncryptionController
import org.json.JSONObject


fun JSONObject.getDecodedNormalId(key: String, encryptionController: EncryptionController): Long {
    val encodedId = optString(key)
    if (encodedId == "") return User.NO_SERVER_ID
    return encryptionController.decodeNormalId(encodedId)

}
fun Parameters.getDecodedNormalId(key: String, encryptionController: EncryptionController): Long {
    val encodedId = getString(key, "")
    if (encodedId == "") return User.NO_SERVER_ID
    return encryptionController.decodeNormalId(encodedId)
}
fun Parameters.getString(id: String, fallback: String = ""): String {
    return this[id] ?: return fallback
}
fun Parameters.getStringToLongId(id: String, fallback: Long = User.NO_SERVER_ID): Long {
    return try {
        this[id]?.toLong() ?: return fallback
    } catch (e: Exception) {
        return fallback
    }
}
fun Parameters.getInt(id: String, fallback: Int = 0): Int {
    val valueString = this[id] ?: return fallback
    return try {
        valueString.toInt()
    } catch (e: Exception) {
        fallback
    }
}
fun Parameters.getLong(id: String, fallback: Long = 0): Long {
    val valueString = this[id] ?: return fallback
    return try {
        valueString.toLong()
    } catch (e: Exception) {
        fallback
    }
}

fun Headers.getDecodedNormalId(key: String, encryptionController: EncryptionController): Long {
    val encodedId = getString(key, "")
    if (encodedId == "") return User.NO_SERVER_ID
    return encryptionController.decodeNormalId(encodedId)
}
fun Headers.getString(id: String, fallback: String = ""): String {
    return this[id] ?: return fallback
}
fun Headers.getInt(id: String, fallback: Int = 0): Int {
    val valueString = this[id] ?: return fallback
    return try {
        valueString.toInt()
    } catch (e: Exception) {
        fallback
    }
}
fun Headers.getLong(id: String, fallback: Long = 0): Long {
    val valueString = this[id] ?: return fallback
    return try {
        valueString.toLong()
    } catch (e: Exception) {
        fallback
    }
}