package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getHuaweiSubscriptionByOrderId
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.HuaweiApiManager
import net.kidjo.server.shared.payments.HuaweiSubscriptionStatus
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient

class PaymentHuaweiStoreManager(
    config: Config,
    httpClient: OkHttpClient,
    encryptionController: EncryptionController,
    databaseController: DatabaseController,
    iapManager: IAPManager,
    private val huaweiApiManager: <PERSON>aweiA<PERSON><PERSON>anager,
    emailManager: EmailManager,
    languageCache: LanguageCache,
    userManager: UserManager

) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {


    override suspend fun start(
        userId: Long, inAppPurchase: InAppPurchase,
        token: String, purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        forceUpdate: Boolean,
        subscriptionId: String?,
        orderId: String?,
        call: ApplicationCall
    ) {

        when (inAppPurchase.store) {
            Device.StorePlatform.HUAWEI -> {
                // token is unique but orderId will be the identificator.
                val checkSubscription: SubscriptionRoot? = databaseController.getHuaweiSubscriptionByOrderId(orderId!!)
                if (checkSubscription == null) {
                    return createSubscription(
                        inAppPurchase, token,
                        call, userId, purchasingSession, subscriptionType,
                        subscriptionId!!, orderId!!
                    )
                } else {
                    if (checkSubscription?.userId != 0L && userId != checkSubscription?.userId) {
                        return call.respondConflict(
                            SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                            "This subscription already belongs to another account. Please login."
                        )
                    }
                    // token is unique but subscriptionId will be the identificator.
                    val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                    if (isExpired) {
                        return updateSubscription(
                            inAppPurchase, token,
                            call, userId, checkSubscription
                        )
                    } else {
                        if(checkSubscription.userId==0L && checkSubscription.id.toInt()!=0){
                            var utility= Utility()
                            if(utility.attachNativeSubscription(checkSubscription,userId)){
                                val userDetails=userManager.getUser(checkSubscription.userId.toString())
                                emailManager.sendConfirmationEmail(userDetails.email, userDetails.name, languageCache.get(call))
                                logger.info("Subscription is attached succesfully !!!")
                                return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${userId} ")

                            }
                        }
                        return call.respondNoContent()
                    }
                }
            }

            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun createSubscription(
        inAppPurchase: InAppPurchase,
        token: String, call:
        ApplicationCall,
        userId: Long,
        purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        subscriptionId: String?,
        orderId: String?
    ) {
        var freeTrial: Boolean = true
        var priceToLog: Float = 0.0f
        var isTestReceipt = false


        val huaweiSubscriptionPurchase =
            getHuaweiReceipt(inAppPurchase, token, subscriptionType.raw, subscriptionId!!, call)
        logger.debug("[PaymentHuaweiStoreManager.createSubscription] huaweiSubscriptionPurchase: $huaweiSubscriptionPurchase")
        if (huaweiSubscriptionPurchase != null) {
            val paymentStateId = huaweiSubscriptionPurchase.purchaseState

            if (paymentStateId != 0) {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }
            if (huaweiSubscriptionPurchase.purchaseType != null) {
                isTestReceipt = false
            }

            // token(susbcriptionToken) and subscriptionId(platformPurchaseId) are not unique but orderId (purchaseSessionId) will be the identificator.
            val subscriptionRootInsert = SubscriptionRootInsert(
                userId, 0L, freeTrial, priceToLog,
                SubscriptionRoot.PaymentType.NATIVE, subscriptionType, inAppPurchase.store.raw,
                huaweiSubscriptionPurchase.subscriptionId, inAppPurchase.store, null,
                inAppPurchase.id, orderId!!, token,
                encryptionController.sha256Hash(token),
                0L, huaweiSubscriptionPurchase.autoRenewing,
                huaweiSubscriptionPurchase.expirationDate.epochMilliToLocalDateTime(),
                isTestReceipt
            )
            insert(subscriptionRootInsert, call)
        }
    }

    override suspend fun updateSubscription(
        inAppPurchase: InAppPurchase,
        token: String,
        call: ApplicationCall,
        userId: Long,
        subscriptionRoot: SubscriptionRoot
    ) {

        var freeTrial: Boolean = true
        var priceToLog: Float = 0.0f


        val huaweiSubscriptionPurchase = getHuaweiReceipt(
            inAppPurchase, token,
            subscriptionRoot.subscriptionType.raw,
            subscriptionRoot.platformPurchaseId, call
        )

        if (huaweiSubscriptionPurchase != null) {
            // subscriptionId(platformPurchaseId) are not unique but orderId (purchaseSessionId) will be the identificator.
            val subscriptionRootUpdate = SubscriptionRootUpdate(
                userId, subscriptionRoot.isRenewing,
                huaweiSubscriptionPurchase.expirationDate.epochMilliToLocalDateTime(),
                huaweiSubscriptionPurchase.subscriptionId,
                freeTrial, priceToLog, subscriptionRoot.id, null
            )

            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)
        }
    }

    suspend fun getHuaweiReceipt(
        inAppPurchase: InAppPurchase, purchaseToken: String, subscriptionType: String,
        subscriptionId: String?,
        call: ApplicationCall
    ): HuaweiSubscriptionStatus? {
        return try {
            huaweiApiManager.getSubscriptionStatusSuspend(
                purchaseToken,
                subscriptionType,
                inAppPurchase.id,
                subscriptionId!!
            )
        } catch (e: Exception) {
            logger.error(
                "[PaymentHuaweiStoreManager.createSubscription] cant find Huawei subscription of $inAppPurchase, " +
                        "token: ${purchaseToken.take(10)} , Exception: $e"
            )
            return null
        }
    }
}
