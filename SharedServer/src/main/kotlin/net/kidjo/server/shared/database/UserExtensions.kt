package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.userFull
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.UserConsent
import net.kidjo.server.shared.models.UserPartnerSSO
import net.kidjo.server.shared.models.UserSession
import net.kidjo.server.shared.models.entity.*
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import java.sql.PreparedStatement
import java.sql.SQLException
import java.sql.Timestamp
import java.time.LocalDateTime

fun DatabaseController.user_getCountryIdFromShortName(shortName: String): Int {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT id from countries WHERE short = ?")
    try {
        statement.setString(1, shortName)
        val results = statement.executeQuery()

        val id = if (results.next()) {
            results.getInt("id")
        } else {
            -1
        }
        return id
    } catch (e: SQLException) {
        println("Error getting: user_getCountryIdFromShortName ${e.localizedMessage}")
        return -1
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.get_email(email: String, userId: Long): String? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT email from users WHERE email = ? AND id <> ?")
    statement.setString(1, email)
    statement.setLong(2, userId)
    val results = statement.executeQuery()

    val email: String? = if (results.next()) results.getString("email") else null

    statement.close()
    connection.close()
    return email
}

fun DatabaseController.getUserEmailById(userId: Long): String {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT email from users WHERE id = ?")
    statement.setLong(1, userId)
    val results = statement.executeQuery()

    val email: String = if (results.next()) results.getString("email") else ""

    statement.close()
    connection.close()
    return email
}

fun DatabaseController.user_getByEmail(email: String): User? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT *, IFNULL((SELECT isActive FROM subscriptions_root WHERE subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 LIMIT 1),0) as subscriptionIsActive from users WHERE email = ? LIMIT 1")
    statement.setString(1, email)
    val results = statement.executeQuery()

    val user: User? = if (results.next()) objectCreator.userFull(results) else null

    statement.close()
    connection.close()
    return user
}

fun DatabaseController.user_getById(id: Long): User? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT *, IFNULL((SELECT isActive FROM subscriptions_root WHERE subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 LIMIT 1),0) as subscriptionIsActive from users WHERE id = ? LIMIT 1")
    statement.setLong(1, id)
    val results = statement.executeQuery()

    val user: User? = if (results.next()) objectCreator.userFull(results) else null

    statement.close()
    connection.close()
    return user
}

//fun DatabaseController.user_getPlusRecentSubscription(userId: Long): Pair<User, Subscription?>? {
//    val connection = dataSource.connection
//    val statement = connection.prepareStatement("SELECT users.*, subscriptions_root.id AS s_id, subscriptions_root.userId AS s_userId, " +
//            "subscriptions_root.firstDeviceId AS s_firstDeviceId, subscriptions_root.isActive AS s_isActive, subscriptions_root.isRenewing AS s_isRenewing, " +
//            "subscriptions_root.stillInFreeTrial AS s_stillInFreeTrial, subscriptions_root.paymentType AS s_paymentType, subscriptions_root.checkDate AS s_checkDate," +
//            "subscriptions_root.storeId AS s_storeId, subscriptions_root.iapId AS s_iapId, subscriptions_root.purchasingSessionId AS s_purchasingSessionId," +
//            "subscriptions_root.subscriptionToken AS s_subscriptionToken, subscriptions_accountCouponId.iapId AS s_accountCouponId, subscriptions_root.created_at AS s_created_at," +
//            "subscriptions_root.updated_at AS s_updated_at " +
//            "FROM users LEFT JOIN subscriptions_root ON subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 WHERE users.id = ? ORDER BY subscriptions_root.created_at LIMIT 1")
//    statement.setLong(1,userId)
//    val results = statement.executeQuery()
//
//    val pair: Pair<User, Subscription?>?
//    if (results.next()) {
//        pair = objectCreator.userAndSubscription(results)
//    } else {
//        pair = null
//    }
//
//    statement.close()
//    connection.close()
//    return pair
//}

fun DatabaseController.user_updateWithOauth(updateUser: User): Boolean {
    val id = User.getId(updateUser.id)
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE users SET authId = ?, name = ?, authType = ?, oAuthType = ? WHERE id = ?")
    statement.setString(1, updateUser.authId)
    statement.setString(2, updateUser.name)
    statement.setString(3, updateUser.authType.raw)
    statement.setString(4, updateUser.oAuthType.raw)
    statement.setLong(5, id)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.updateUserPasswordAndName(id: Long, name: String, hashedPassword: String): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE users SET name = ?, email = ?,   password = ? WHERE id = ?")
    statement.setString(1, name)
    statement.setString(2, name)
    statement.setString(3, hashedPassword)
    statement.setLong(4, id)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.user_getByOauthId(oAuthType: User.OAuthType, oauthId: String): User? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT *, IFNULL((SELECT isActive FROM subscriptions_root WHERE subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 LIMIT 1),0) as subscriptionIsActive from users WHERE authId = ? AND oAuthType = ? LIMIT 1")
    statement.setString(1, oauthId)
    statement.setString(2, oAuthType.raw)
    val results = statement.executeQuery()

    val user: User?
    if (results.next()) user = objectCreator.userFull(results)
    else user = null

    statement.close()
    connection.close()
    return user
}

fun DatabaseController.user_register(user: User): Long {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "INSERT INTO users(name, email, password, country_id, authType, authId, oAuthType) VALUES (?,?,?,?,?,?,?)",
        PreparedStatement.RETURN_GENERATED_KEYS
    )
    statement.setString(1, user.name)
    statement.setString(2, user.email)
    statement.setString(3, user.hashedPassword)
    statement.setInt(4, user.countryId)
    statement.setString(5, user.authType.raw)
    statement.setString(6, user.authId)
    statement.setString(7, user.oAuthType.raw)
    statement.executeAndCheck()
    val keys = statement.generatedKeys
    val id = if (keys.next()) keys.getLong(1)
    else User.NO_SERVER_ID

    statement.close()
    connection.close()

    return id
}

fun DatabaseController.user_setBrainUserId(userId: Long, brainTreeToken: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE users SET brainTreeId = ? WHERE id = ?")
    statement.setString(1, brainTreeToken)
    statement.setLong(2, userId)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.user_refresh(user: User) {
    val id = User.getId(user.id)
    if (id == User.NO_SERVER_ID) return

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT *, IFNULL((SELECT isActive " +
                "FROM subscriptions_root " +
                "WHERE subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 LIMIT 1),0) as subscriptionIsActive from users WHERE id = ?"
    )
    statement.setLong(1, id)

    val results = statement.executeQuery()
//    if (results.next()) id = keys.getLong(1)

    statement.close()
    connection.close()
}

fun DatabaseController.user_getSubscriptionFromSession(sessionId: String): Boolean {
    if (sessionId.isEmpty()) return false
    var isSubscribed = false
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT IFNULL((SELECT isActive " +
                "FROM subscriptions_root " +
                "WHERE subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 LIMIT 1),0) as subscriptionIsActive from users " +
                "JOIN user_tokens ON user_tokens.userId = users.id " +
                "WHERE user_tokens.sessionId = ? LIMIT 1;"
    )
    try {
        statement.setString(1, sessionId)
        val results = statement.executeQuery()
        isSubscribed = if (results.next()) results.getBoolean("subscriptionIsActive") else false

        return isSubscribed
    } catch (e: SQLException) {
        println("Error getting: user_getSubscriptionFromSession ${e.localizedMessage}")
        return false
    } finally {
        statement.close()
        connection.close()
    }
}

fun DatabaseController.user_getFromSession(sessionId: String): User? {
    if (sessionId == "") return null

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT users.*, IFNULL((SELECT isActive " +
                "FROM subscriptions_root " +
                "WHERE subscriptions_root.userId = users.id AND subscriptions_root.isActive = 1 LIMIT 1),0) as subscriptionIsActive from users " +
                "JOIN user_tokens ON user_tokens.userId = users.id " +
                "WHERE user_tokens.sessionId = ? LIMIT 1;"
    )
    statement.setString(1, sessionId)
    val results = statement.executeQuery()

    val user: User? = if (results.next()) objectCreator.userFull(results) else null

    statement.close()
    connection.close()
    return user
}

fun DatabaseController.getUserJWT(user: User, authToken: String): Boolean {
    val userId = User.getId(user.id)

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT * FROM user_jwt WHERE userId = ? AND authToken = ? "
    )
    statement.setLong(1, userId)
    statement.setString(2, authToken)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}


fun DatabaseController.user_setSession(sessionId: String, loginType: UserSession.LoginType, user: User): Boolean {
    val userId = User.getId(user.id)

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "INSERT INTO user_tokens(sessionId, userId, loginType, authToken) VALUES (?,?,?,?)"
    )
    statement.setString(1, sessionId)
    statement.setLong(2, userId)
    statement.setString(3, loginType.raw)
    statement.setString(4, user.authToken)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.setNextUserLogin(userId: Long, authToken: String, deviceId: String): Boolean {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "INSERT INTO user_account_jwt (userId, authToken, deviceId) VALUES (?,?,?)"
    )
    statement.setLong(1, userId)
    statement.setString(2, authToken)
    statement.setString(3, deviceId)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.deleteUserJWT(userId: Long, authToken: String): Boolean {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "DELETE FROM user_account_jwt WHERE userId = ? AND authToken = ? "
    )
    statement.setLong(1, userId)
    statement.setString(2, authToken)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.getCurrentUserLogins(userId: Long): Int {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT COUNT(userId) as user_count FROM user_account_jwt WHERE userId = ? "
    )
    statement.setLong(1, userId)
    val results = statement.executeQuery()

    val userCount: Int = if (results.next()) results.getInt("user_count") else 0

    statement.close()
    connection.close()
    return userCount
}

fun DatabaseController.getUserSubscriptionType(userId: Long): List<String> {

    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "SELECT s.subscriptionType FROM subscriptions_root s WHERE s.userId = ? AND s.isActive = true group by s.subscriptionType "
    )
    statement.setLong(1, userId)
    val results = statement.executeQuery()

    val types = arrayListOf<String>()

    while (results.next()) {
        types.add(results.getString("s.subscriptionType"))
    }

    statement.close()
    connection.close()
    return types
}

fun DatabaseController.getNextOneLoginFromSignOutList(userId: Long): String? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT authToken FROM user_account_jwt WHERE userId = ? ORDER BY updated_at ASC LIMIT 1")
    statement.setLong(1, userId)
    val results = statement.executeQuery()

    var token: String? = null

    while (results.next()) {
        token = results.getString("authToken")
    }

    statement.close()
    connection.close()
    return token
}


fun DatabaseController.setUserLoginToSignOutList(userId: Long, token: String, deviceId: String): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE user_account_jwt SET authToken = ?, deviceId=CONCAT(?,deviceId) WHERE userId = ? ORDER BY updated_at ASC LIMIT 1")
    statement.setString(1, token)
    if (getExistingDeviceId(userId.toInt(), deviceId).isNotEmpty()) {
        logger.info("Device Id : $deviceId is appended with ,")
        statement.setString(2, "$deviceId,")
    } else {
        statement.setString(2, deviceId)
        logger.info("Device Id : $deviceId is added ")
    }

    statement.setLong(3, userId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.getDeviceForTwtOnly(userId: Int, deviceId: String): Boolean {
    val connection = dataSource.connection
    var count = 0
    val statement =
        connection.prepareStatement("select deviceId,authToken  from user_account_jwt where userId = ? and deviceId like ?")
    statement.setInt(1, userId)
    statement.setString(2, "%$deviceId%")
    val results = statement.executeQuery()
    if (results.next()) {
        val devices = results.getString("deviceId")
        val token = results.getString("authToken")
        logger.info("Devices : $devices received from database ")
        if (devices.contains(",")) {
            val arrayOfDevices = devices.split(",")
            if (arrayOfDevices[0] == deviceId) {
                logger.info("Requested device is the same db device-> index value is equal")
                count++;
            } else {
                updatePrevousDeviceWithCurrentOne(token, arrayOfDevices[0])
                count++
            }
        } else {
            count++
        }
    }
    statement.close()
    connection.close()
    return count > 0
}

fun DatabaseController.updatePrevousDeviceWithCurrentOne(token: String, deviceId: String) {

    val connection = dataSource.connection
    config.deviceIdForTwt = deviceId
    val statement =
        connection.prepareStatement("UPDATE user_account_jwt SET deviceId=? WHERE authToken=?")
    statement.setString(1, deviceId)
    statement.setString(2, token)
    val i = statement.executeAndCheck()
    if (i) {
        logger.info("Old DeviceId is updated with accessed deviceId")
    } else {
        logger.info("Old DeviceId is not updated with accessed deviceId")
    }
    statement.close()
    connection.close()
}

fun DatabaseController.getExistingToken(userId: Int, deviceId: String): String {
    val connection = dataSource.connection
    var token = "";
    val statement =
        connection.prepareStatement("select authToken from user_account_jwt where userId = ? and deviceId like  ?")
    statement.setInt(1, userId)
    statement.setString(2, "%$deviceId%")
    val results = statement.executeQuery()
    if (results.next()) {
        token = results.getString("authToken")
    }
    statement.close()
    connection.close()
    return token
}

fun DatabaseController.getExistingDeviceId(userId: Int, deviceId: String): String {
    val connection = dataSource.connection
    var device = "";
    val statement =
        connection.prepareStatement("select deviceId from user_account_jwt where userId = ? ORDER BY updated_at ASC LIMIT 1")
    statement.setInt(1, userId)
    val results = statement.executeQuery()
    if (results.next()) {
        device = results.getString("deviceId")
    }
    statement.close()
    connection.close()
    return device
}

fun DatabaseController.checkExistingDeviceId(userId: Int, deviceId: String): Boolean {
    val connection = dataSource.connection
    var result = 0;
    val statement =
        connection.prepareStatement("select count(deviceId) as number from user_account_jwt where userId = ? and deviceId=?")
    statement.setInt(1, userId)
    statement.setString(2, deviceId)
    val results = statement.executeQuery()
    if (results.next()) {
        result = results.getInt("number")
    }
    statement.close()
    connection.close()
    return result > 0
}

fun DatabaseController.getPartnerNameByUserId(userId: Int): String {
    val connection = dataSource.connection
    var storeId = ""
    val statement =
        connection.prepareStatement("select storeId from subscriptions_root where userId =? and isActive is true")
    statement.setInt(1, userId)
    val results = statement.executeQuery()
    if (results.next()) {
        storeId = results.getString("storeId")
    }
    statement.close()
    connection.close()
    return storeId
}

fun DatabaseController.user_emailConfirmed(userId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE users SET email_is_confirmed = TRUE WHERE id = ?")
    statement.setLong(1, userId)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.userUpdatePassword(userId: Long, password: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE users SET password = ? WHERE id = ?")
    statement.setString(1, password)
    statement.setLong(2, userId)

    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}

fun DatabaseController.userUpdateLastChangedPassword(userId: Long, lastPassword: String) {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE users SET last_changed_password = ? WHERE id = ?")
    statement.setString(1, lastPassword)
    statement.setLong(2, userId)
    statement?.executeUpdate()
    statement?.close()
    connection?.close()
}

fun DatabaseController.getUserLastChangedPassword(userId: Long): String? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT last_changed_password FROM users WHERE id = ?")
    statement.setLong(1, userId)
    val resultSet = statement.executeQuery()
    val userLastChangedPassword = if (resultSet.next()) resultSet.getString("last_changed_password") else null
    statement?.close()
    connection?.close()
    return userLastChangedPassword
}

fun DatabaseController.userUpdateName(userId: Long, name: String): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE users SET name = ? WHERE id = ?")
    statement.setString(1, name)
    statement.setLong(2, userId)

    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}

fun DatabaseController.userUpdateEmail(userId: Long, email: String): Boolean {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("UPDATE users SET email = ?, email_is_confirmed = TRUE, authType = ? WHERE id = ?")
    statement.setString(1, email)
    statement.setString(2, User.AuthType.EMAIL.raw)
    statement.setLong(3, userId)

    val results = statement.executeAndCheck()
    statement.close()
    connection.close()
    return results
}

fun DatabaseController.userUpdateCountry(userId: Long, countryId: Int): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE users SET country_id = $countryId WHERE id = $userId")
    val success = statement.executeAndCheck()
    statement.close()
    connection.close()
    return success
}

fun DatabaseController.insert_user_redirect_key(userId: Long, redirectKey: String): String {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO users_redirect_key(userId, redirect_key ) VALUES (?,?)")
    statement.setLong(1, userId)
    statement.setString(2, redirectKey)
    statement.executeAndCheck()
    statement.close()
    connection.close()
    return redirectKey
}

fun DatabaseController.delete_user_redirect_key(userId: Long): Boolean {

    val connection = dataSource.connection
    val statement = connection.prepareStatement("DELETE FROM users_redirect_key WHERE userId = ? ")
    statement.setLong(1, userId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.getUserRedirectKey(redirectKey: String): Long {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT userId FROM users_redirect_key WHERE redirect_key = ?")
    statement.setString(1, redirectKey)
    val results = statement.executeQuery()

    val userId: Long = if (results.next()) results.getLong("userId") else -1

    statement.close()
    connection.close()
    return userId
}

fun DatabaseController.incrementWrongPasswordCount(userId: Long) {
    val connection = dataSource.connection
    var sql = "SELECT wrong_password_count FROM users WHERE id = $userId "
    var statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    val wrongPasswordCount = if (resultSet.next()) resultSet.getInt("wrong_password_count") else -1
    statement?.close()
    sql = "UPDATE users SET wrong_password_count = ${wrongPasswordCount + 1} WHERE id = $userId "
    statement = connection.prepareStatement(sql)
    statement?.executeUpdate()
    statement?.close()
    connection?.close()
}

fun DatabaseController.checkLimitLoginThreshold(userId: Long) {
    val connection = dataSource.connection
    var sql = "SELECT wrong_password_count FROM users WHERE id = $userId "
    var statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    val wrongPasswordCount = if (resultSet.next()) resultSet.getInt("wrong_password_count") else -1
    statement?.close()
    if (wrongPasswordCount >= 5) {
        sql = "UPDATE users SET limit_login_at = '${Timestamp.valueOf(LocalDateTime.now())}' WHERE id = $userId "
        statement = connection.prepareStatement(sql)
        statement?.executeUpdate()
        statement?.close()
    }
    connection?.close()
}

fun DatabaseController.isLoginLimited(userId: Long): Boolean {
    val connection = dataSource.connection
    val sql = "SELECT limit_login_at FROM users WHERE id = $userId "
    val statement = connection.prepareStatement(sql)
    val resultSet = statement.executeQuery()
    val limitLoginAt = if (resultSet.next()) resultSet.getTimestamp("limit_login_at")?.toLocalDateTime() else null
    statement?.close()
    connection?.close()
    return limitLoginAt?.plusMinutes(2)?.isAfter(LocalDateTime.now()) ?: false
}

fun DatabaseController.resetWrongPasswordCountAndLimitLoginAt(userId: Long) {
    val connection = dataSource.connection
    val sql = "UPDATE users SET wrong_password_count = 0, limit_login_at = null WHERE id = $userId "
    val statement = connection.prepareStatement(sql)
    statement?.executeUpdate()
    statement?.close()
    connection?.close()
}

fun DatabaseController.getUserActiveSubscriptionsType(userId: Long): ArrayList<String> {
    val types = arrayListOf<String>()
    dataSource.connection.use {
        val statement = it.prepareStatement(
            "SELECT s.subscriptionType FROM subscriptions_root s WHERE s.userId = ? AND s.isActive = true  and nextBillDate>NOW() group by s.subscriptionType "
        )
        statement.setLong(1, userId)
        val results = statement.executeQuery()
        while (results.next()) {
            types.add(results.getString("s.subscriptionType"))
        }
        statement.close()
    }
    return types
}

fun DatabaseController.registerUserConsent(userId: String, tcAccepted: Int, promoAccepted: Int): Long {

    dataSource.connection.use { connection ->
        val sql = "INSERT INTO users_consent (is_tc_accepted, is_promo_accepted, user_id) VALUES (?, ?, ?);"
        val statement = connection.prepareStatement(sql, PreparedStatement.RETURN_GENERATED_KEYS).apply {
            setInt(1, tcAccepted)
            setInt(2, promoAccepted)
            setString(3, userId)
            executeAndCheck()
        }

        val id = statement.generatedKeys.use { keys ->
            if (keys.next()) keys.getLong(1)
            else User.NO_SERVER_ID
        }

        return id
    }
}


fun DatabaseController.getMondiaUserPackageType(productId: Int, packageId: Int): String {
    dataSource.connection.use {
        val statement = it.prepareStatement(
            "SELECT subscription_package FROM mondia_packages " +
                    "WHERE isActive = 1 " +
                    "AND mondia_product_id = ? " +
                    "AND mondia_package_id = ?;"
        )
        statement.setInt(1, productId)
        statement.setInt(2, packageId)
        val results = statement.executeQuery()

        val subPackage = if (results.next()) {
            results.getString("subscription_package")
        } else {
            "UNDEFINED"
        }
        statement.close()
        return subPackage
    }
}

fun DatabaseController.createOrUpdateUserCosnent(userConsent: UserConsent): String {
    return transaction {
        val existingId = UsersConsent
            .select { UsersConsent.userId eq userConsent.userId }
            .map { it[UsersConsent.id] }
            .singleOrNull()

        if (existingId != null) {
            UsersConsent.update({ UsersConsent.id eq existingId }) {
                it[isTCAccepted] = userConsent.isTCAccepted
                it[isPromoAccepted] = userConsent.isPromoAccepted
            }
            "Updated"
        } else {
            UsersConsent.insert {
                it[userId] = userConsent.userId
                it[isTCAccepted] = userConsent.isTCAccepted
                it[isPromoAccepted] = userConsent.isPromoAccepted
            }
            "Inserted"
        }
    }
}

fun DatabaseController.getUserConsent(userId: Long): UserConsent {
    return transaction {
        UsersConsent.select { UsersConsent.userId.eq(userId) }
            .limit(1)
            .map {
                UserConsent(
                    id = it[UsersConsent.id],
                    userId = it[UsersConsent.userId],
                    isTCAccepted = it[UsersConsent.isTCAccepted],
                    isPromoAccepted = it[UsersConsent.isPromoAccepted]
                )
            }.singleOrNull() ?: UserConsent(
            id = 0,
            userId = userId,
            isTCAccepted = false,
            isPromoAccepted = false
        )
    }
}

fun DatabaseController.getPartnerByUserId(userId: Long): String {
    return transaction {
        SubscriptionsRoot
            .select { (SubscriptionsRoot.isActive eq true) and (SubscriptionsRoot.userId eq userId) }
            .orderBy(SubscriptionsRoot.createdAt, SortOrder.DESC)
            .limit(1)
            .map { it[SubscriptionsRoot.storeId] }
            .singleOrNull() ?: ""
    }
}


fun DatabaseController.createOrUpdateUserPartnerSSO(userPartnerSSO: UserPartnerSSO): String {
    return transaction {
        val existingId = UsersPartnerSSO
            .select { (UsersPartnerSSO.userId eq userPartnerSSO.userId) and (UsersPartnerSSO.partnerId eq userPartnerSSO.partnerId) }
            .map { it[UsersPartnerSSO.id] }
            .singleOrNull()

        if (existingId != null) {
            UsersPartnerSSO.update({ UsersPartnerSSO.id eq existingId }) {
                it[isActive] = userPartnerSSO.isActive
            }
            "Updated"
        } else {
            UsersPartnerSSO.insert {
                it[userId] = userPartnerSSO.userId
                it[partnerId] = userPartnerSSO.partnerId
                it[isActive] = userPartnerSSO.isActive
            }
            "Inserted"
        }
    }
}

fun DatabaseController.getUserPartnerSSO(userId: Long): UserPartnerSSO {
    return transaction {
        (UsersPartnerSSO innerJoin AccountCouponsPartner)
            .select {
                (UsersPartnerSSO.userId eq userId) and
                        (AccountCouponsPartner.id eq UsersPartnerSSO.partnerId) and
                        (AccountCouponsPartner.name.upperCase() eq "CAFEYN")
            }
            .limit(1)
            .map {
                UserPartnerSSO(
                    userId = it[UsersPartnerSSO.userId],
                    partnerId = it[UsersPartnerSSO.partnerId],
                    isActive = it[UsersPartnerSSO.isActive],

                    partnerUserId = it[UsersPartnerSSO.partnerUserId],
                    partnerName = it[UsersPartnerSSO.partnerName],
                    partnerDescription = it[UsersPartnerSSO.partnerDescription],
                    partnerIsActive = it[UsersPartnerSSO.partnerIsActive],
                    partnerIsSsoActivated = it[UsersPartnerSSO.partnerIsSsoActivated],
                    partnerBundles = it[UsersPartnerSSO.partnerBundles],
                    partnerSubscribedServices = it[UsersPartnerSSO.partnerSubscribedServices],
                    email = it[UsersPartnerSSO.email]
                )
            }.singleOrNull() ?: UserPartnerSSO(
            userId = userId,
            partnerId = 0,
            isActive = false,

            partnerUserId = 0,
            partnerName = "",
            partnerDescription = "",
            partnerIsActive = false,
            partnerIsSsoActivated = false,
            partnerBundles = "",
            partnerSubscribedServices = "",
            email = ""
        )
    }
}

fun DatabaseController.belongsToCafeynPartner(user: User?): Boolean {
    if (user == null || user.id.toLong() == 0L) {
        return false
    }
    val userPartnerSSO = getUserPartnerSSO(user.id.toLong())
    return userPartnerSSO.partnerId != 0L
}


fun DatabaseController.saveUsersPartnerSSO(usersPartnerSSO: UserPartnerSSO): Long? {
    return transaction {
        UsersPartnerSSO.insert {
            it[userId] = usersPartnerSSO.userId
            it[partnerId] = usersPartnerSSO.partnerId
            it[isActive] = usersPartnerSSO.isActive
            it[partnerUserId] = usersPartnerSSO.partnerUserId
            it[partnerName] = usersPartnerSSO.partnerName
            it[partnerDescription] = usersPartnerSSO.partnerDescription
            it[partnerIsActive] = usersPartnerSSO.partnerIsActive
            it[partnerIsSsoActivated] = usersPartnerSSO.partnerIsSsoActivated
            it[partnerBundles] = usersPartnerSSO.partnerBundles
            it[partnerSubscribedServices] = usersPartnerSSO.partnerSubscribedServices
            it[email] = usersPartnerSSO.email
        } get UsersPartnerSSO.id
    }
}

fun DatabaseController.saveUsersPartnerSSOIfNotExists(usersPartnerSSO: UserPartnerSSO): Long? {
    return transaction {
        val existingRecord = UsersPartnerSSO.select {
            (UsersPartnerSSO.userId eq usersPartnerSSO.userId) and
                    (UsersPartnerSSO.partnerId eq usersPartnerSSO.partnerId)
        }.singleOrNull()

        if (existingRecord == null) {
            saveUsersPartnerSSO(usersPartnerSSO)
        } else {
            null
        }
    }
}



fun DatabaseController.updateUsersPartnerSSO(usersPartnerSSO: UserPartnerSSO): Int {
    return transaction {
        UsersPartnerSSO.update(
            where = {
                (UsersPartnerSSO.userId eq usersPartnerSSO.userId) and
                        (UsersPartnerSSO.partnerId eq usersPartnerSSO.partnerId)
            }
        ) {
            it[isActive] = usersPartnerSSO.isActive
            it[partnerUserId] = usersPartnerSSO.partnerUserId
            it[partnerName] = usersPartnerSSO.partnerName
            it[partnerDescription] = usersPartnerSSO.partnerDescription
            it[partnerIsActive] = usersPartnerSSO.partnerIsActive
            it[partnerIsSsoActivated] = usersPartnerSSO.partnerIsSsoActivated
            it[partnerBundles] = usersPartnerSSO.partnerBundles
            it[partnerSubscribedServices] = usersPartnerSSO.partnerSubscribedServices
            it[email] = usersPartnerSSO.email
        }
    }
}


fun DatabaseController.getAccountCouponPartnerId(partnerName: String): Long {
    return transaction {
        AccountCouponsPartner.select { AccountCouponsPartner.name.upperCase().eq(partnerName) }
            .limit(1)
            .map { it[AccountCouponsPartner.id] }
            .singleOrNull() ?: 0L
    }
}

fun DatabaseController.getLastRedirectKeyByUserId(userId: Long): String {
    return transaction {
        UsersRedirectKey
            .select { UsersRedirectKey.userId eq userId }
            .orderBy(UsersRedirectKey.createTime, SortOrder.DESC)
            .limit(1)
            .map { it[UsersRedirectKey.redirectKey] }
            .firstOrNull() ?: ""
    }
}