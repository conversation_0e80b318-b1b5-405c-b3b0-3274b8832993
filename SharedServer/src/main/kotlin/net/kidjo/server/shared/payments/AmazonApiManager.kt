package net.kidjo.server.shared.payments

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.ktor.http.*
import io.ktor.server.application.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.tools.Config
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.slf4j.LoggerFactory

class AmazonApiManager(internal val config: Config) {

    private val okHttpClient = OkHttpClient()
    private val logger = LoggerFactory.getLogger("AmazonApiManager")

    /**
     * Verify and return purchase receipt from AMAZON API
     */
    suspend fun getSubscriptionStatus(
        receiptId: String,
        amazonUserId: String,
        call: ApplicationCall? = null,
    ): AmazonSubscriptionStatus? {

        var response = getResponse(receiptId, amazonUserId)

        if (response.code().toString() == "400") {
            logger.error("Amazon RVS Error: Invalid receiptID")
            call?.respondError(HttpStatusCode.fromValue(response.code()), "Amazon RVS Error: Invalid receiptID")
            return null
        }
        if (response.code().toString() == "496") {
            logger.error("Amazon RVS Error: Invalid developerSecret")
            call?.respondError(HttpStatusCode.fromValue(response.code()), "Amazon RVS Error: Invalid developerSecret")
            return null
        }
        if (response.code().toString() == "497") {
            logger.error("Amazon RVS Error: Invalid userId")
            call?.respondError(HttpStatusCode.fromValue(response.code()), "Amazon RVS Error: Invalid userId")
            return null
        }
        if (response.code().toString() == "500") {
            logger.error("Amazon RVS Error: Internal Server Error")
            call?.respondError(HttpStatusCode.fromValue(response.code()), "Amazon RVS Error: Internal Server Error")
            return null
        }

        if (response.code() != HttpStatusCode.OK.value) {
            logger.error("Response from the Amazon receipt verify API was not OK")
            call?.respondError(HttpStatusCode.fromValue(response.code()))
            return null
        }

        return getSubscriptionResponse(receiptId, amazonUserId)
    }

    /**
     * Verify and return purchase receipt from AMAZON API for the JOB
     */
    fun getSubscriptionStatus(receiptId: String, amazonUserId: String): AmazonSubscriptionStatus? {
        var billingResponse: AmazonSubscriptionStatus? = null

        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                billingResponse = getSubscriptionResponse(receiptId, amazonUserId)
            }
        }

        return billingResponse
    }

    private suspend fun getSubscriptionResponse(
        receiptId: String,
        amazonUserId: String,
    ): AmazonSubscriptionStatus? {

        var response = getResponse(receiptId, amazonUserId)

        var billingResponseString = withContext(Dispatchers.IO) { response.body()?.string() }

        val subRes = billingResponseString?.let {
            val mapper = jacksonObjectMapper()
            try {
                mapper.readValue(it, AmazonSubscriptionStatus::class.java)
            } catch (e: Exception) {
                logger.error("Error on processing Amazon subscription response", e)
                return null
            }
        }
        response.close()
        return subRes
    }

    private suspend fun getResponse(receiptId: String, amazonUserId: String): Response {
        val validationUrl = getAmazonReceiptUrl(receiptId, amazonUserId)
        var request = Request.Builder().url(validationUrl).get().build()
        var response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
        return response
    }

    private fun getAmazonReceiptUrl(receiptId: String, amazonUserId: String): String {
        val validationUrl = String.format(
            config.amazon_verify_receipt_url, amazonUserId, receiptId
        )
        return validationUrl
    }

    private fun getAmazonPackage(subscriptionType: String): String {
        var orangePackage = config.orange_kidjo_tv_package_name
        if (subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_BOOKS.raw) {
            orangePackage = config.orange_kidjo_books_package_name
        }
        return orangePackage
    }
}
@JsonIgnoreProperties(ignoreUnknown = true)
data class AmazonSubscriptionStatus(
    val autoRenewing: Boolean = false,
    val betaProduct: Boolean = false,
    val cancelDate: Long? = 0,
    val cancelReason: Long? = 0,
    val deferredDate: Long? = 0,
    val deferredSku: String? = "",
    val freeTrialEndDate: Long = 0,
    val gracePeriodEndDate: Long = 0,
    val parentProductId: String? = "",
    val productId: String = "",
    val productType: String = "",
    val purchaseDate: Long = 0,
    val quantity: Int = 0,
    val receiptId: String = "",
    val renewalDate: Long = 0,
    val term: String = "",
    val termSku: String = "",
    val testTransaction: Boolean = false,
)
