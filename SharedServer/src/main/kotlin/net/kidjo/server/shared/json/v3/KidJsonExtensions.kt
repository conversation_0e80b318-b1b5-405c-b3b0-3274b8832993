package net.kidjo.server.shared.json.v3

import net.kidjo.server.shared.models.Kid
import org.json.JSONObject

fun JsonObjectCreatorV3.kid_toJSON(kid: Kid): JSONObject {
    val json = JSONObject()
    json.put("id",kid.userId)
    println("kidName-" + kid.name)
    println("kidAge-" + kid.age)

    try {
        json.put("name", kid.name)
        json.put("age", kid.age)
    } catch (e: Error) {
        println(e)
    }
    return json
}