package net.kidjo.server.shared.tools

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import net.kidjo.common.models.Language
import org.slf4j.LoggerFactory
import java.net.URL

data class LanguageTerms(
    val accountCancelActuallyCancel: String = "",
    val accountCancelGoBack: String = "",
    val accountCancelMessage1: String = "", // With arguments
    val accountCancelMessage2: String = "", // With arguments
    val accountCancelSubtitle: String = "",
    val accountCancelTitle: String = "",
    val accountEmailPlaceholder: String = "",
    val accountFacebookLogin: String = "",
    val accountGoogleLogin: String = "",
    val accountInfoAppSectionTitle: String = "",
    val accountInfoCancelSubscription: String = "",
    val accountInfoCancelledPaymentTitle: String = "",
    val accountInfoCouponTitle: String = "",
    val accountInfoEmailTitle: String = "",
    val accountInfoInfoSectionTitle: String = "",
    val accountInfoLinkSubButton: String = "",
    val accountInfoLinkSubText: String = "",
    val accountInfoLinkWebappButton: String = "",
    val accountInfoLinkWebappText: String = "",
    val accountInfoLinkPinButton: String = "",
    val accountInfoLinkPinText: String = "",
    val accountInfoNextBillDate: String = "",
    val accountInfoPaymentTitle: String = "",
    val accountInfoPaymentTypeCc: String = "", // With arguments
    val accountInfoPlanTitle: String = "",
    val accountInfoResubscribeButtonTitle: String = "",
    val accountInfoSubscriptionActiveUntil: String = "",
    val accountInfoSubscriptionTitle: String = "",
    val accountInfoTitle: String = "",
    val accountLinkPinTitle: String = "",
    val accountLinkPinDescription: String = "",
    val accountLoadingMessage: String = "",
    @JsonProperty("account_login_button")
    val accountLoginLoginButton: String = "",
    val accountLoginRegister: String = "",
    val accountLoginSubtitle1: String = "",
    val accountLoginTitle1: String = "",
    val accountLoginTitle2: String = "",
    val accountLoginTitle3: String = "",
    val accountNavigationLogin: String = "",
    val accountNavigationLogout: String = "",
    val accountPasswordPlaceholder: String = "",
    val accountPaymentCcComplete: String = "",
    val accountPaymentCcName: String = "",
    val accountPaymentCcTitle: String = "",
    val accountPaymentSelectCard: String = "",
    val accountPaymentSelectCoupon: String = "",
    val accountPaymentSelectPaypal: String = "",
    val accountPaymentSelectSubtitle1: String = "",
    val accountPaymentSelectSubtitle2: String = "",
    val accountPaymentSelectTitle: String = "",
    @JsonProperty("account_placeholder_email")
    val accountPlaceholdersEmail: String = "",
    @JsonProperty("account_placeholder_password")
    val accountPlaceholdersPassword: String = "",
    val accountPlanNote: String = "",
    val accountPlanTitle: String = "",
    @JsonProperty("account_register_button")
    val accountRegisterRegisterButton: String = "",
    val accountRegisterSubtitle1: String = "",
    val accountRegisterTitle1: String = "",
    val accountRegisterTitle2: String = "",
    val accountRegisterTitle3: String = "",
    val accountForgottenPassword: String = "",
    val accountForgottenPasswordTitle: String = "",
    val accountForgottenPasswordSubtitle: String = "",
    val accountForgottenPasswordInput: String = "",
    val accountForgottenPasswordConfirm: String = "",
    val accountForgottenPasswordValidate: String = "",
    val accountSubscriptionTermsApproval: String = "",
    val accountVewdUnsubscribe: String = "",
    val couponBadgeGenericBottom: String = "", // With arguments
    val couponBadgeGenericBottomMany: String = "", // With arguments
    val couponBadgeGenericTop: String = "", // With arguments
    val couponGenericCancel: String = "",
    val couponGenericEnter: String = "",
    val couponGenericPlaceholder: String = "",
    val couponGenericSubtitle: String = "",
    val couponGenericTitle: String = "",
    val couponLoginSubtitle: String = "",
    val couponLoginTitle: String = "",
    val couponPaymentSelectSubtitle1: String = "",
    val couponPaymentSelectSubtitle2: String = "",
    val couponPaymentSelectTitle: String = "",
    val couponRegisterSubtitle: String = "",
    val couponRegisterTitle: String = "",
    val emailSupport: String = "",
    val mondiaSubscriptionButton: String = "",
    val mondiaSubscriptionButtonDay: String = "",
    val mondiaSubscriptionButtonWeek: String = "",
    val mondiaSubscriptionHaveAccount: String = "",
    val mondiaSubscriptionSubtitle: String = "",
    val mondiaSubscriptionTitle: String = "",
    val paymentErrorIssueAddingCc: String = "",
    val paymentErrorIssueChargingCc: String = "",
    val planMonthlyTitle: String = "",
    val planMonthlyPriceShort: String = "", // With arguments
    val mondiaRedeemCredentialsTitle: String = "",
    val mondiaRedeemCredentialsSubtitle: String = "",
    val mondiaRedeemCredentialsInput: String = "",
    val mondiaRedeemCredentialsInputError: String = "",
    val mondiaRedeemCredentialsValidate: String = "",
    val footerTerms: String = "",
    val footerPrivacy: String = "",
    val footerCookies: String = "",
    val footerDevices: String = "",
    val footerUnsubscribe: String = "",
    val mailRedeemCredentialsObject: String = "",
    val mailRedeemCredentialsContent: String = "",
    val mailRedeemCredentialsPassword: String = "",
    val mailSendPasswordObject: String = "",
    val mailSendPasswordContent: String = "",
    val errorGeneric: String = "",
    val errorWrongCredentials: String = "",
    val accountCancelSuccessTitle: String = "",
    val accountCancelSuccessBack: String = "",
    val subscriptionViewTitle: String = "",
    val subscriptionViewSubtitle: String = "",
    val subscriptionViewButtonText: String = "",
    val subscriptionViewRestore: String = "",
    val subscriptionViewAccountLogin: String = "",
    val subscriptionViewNewFact1: String = "",
    val subscriptionViewNewFact2: String = "",
    val subscriptionViewNewFact3: String = "",
    val subscriptionViewNewButton: String = "",
    val subscriptionViewNewButtonRestore: String = "",
    val subscriptionViewNewDescription: String = "",
    val subscriptionViewNewDay: String = "",
    val subscriptionViewNewMonth: String = "",
    val subscriptionViewNewYear: String = "",
    val subscriptionViewNewOffer: String = "",
    val subscriptionViewNewOfferFree: String = "",
    val subscriptionViewNewTerms: String = "",
    val subscriptionViewNewPrivacy: String = "",
    val cmpText: String = "",
    val cmpMore: String = "",
    val cmpAllow: String = "",
    val cmpDeny: String = "",
    val mondiaEmailApi: String = "",
    val mondiaEmailTitle: String = ""
) {

    fun format(term: String, vararg arguments: String, defaultTerm: String? = null): String {
        var formatted = getTerm(term, defaultTerm)
        var index = 1
        while (index > 0) {
            val placeholder = "%$index\$s"
            if (term.contains("%$index\$s") && index - 1 < arguments.size) {
                formatted = formatted.replace(placeholder, arguments[index - 1], true)
                index++
            } else {
                index = 0
            }
        }

        return formatted
    }

    fun getTerm(term: String, defaultTerm: String?): String =
        if (term.isBlank() && defaultTerm != null) defaultTerm
        else term

}

object LanguageManager {
    private val logger = LoggerFactory.getLogger("LanguageManager")

    private val languageTermsMap = mutableMapOf<String, LanguageTerms>()

    init {
        Language.values().forEach { language ->
            val langShort = language.shortName
            getLanguageResourceFile(langShort)?.let {
                try {
                    val terms = getTermsFromLanguageFile(it)
                    languageTermsMap[langShort] = terms
                } catch (exception: Exception) {
                    logger.error("An error has occurred loading ${language.nativeName} language file")
                }
            }
        }
    }

    fun getLanguageTerms(language: Language): LanguageTerms =
        languageTermsMap[language.shortName] ?: getDefaultLanguageTerms()

    fun getDefaultLanguageTerms(): LanguageTerms =
        languageTermsMap[Language.ENGLISH.shortName] ?: LanguageTerms()

    private fun getTermsFromLanguageFile(languageFile: URL): LanguageTerms {
        val languageJson = languageFile.readText()
        val objectMapper = jacksonObjectMapper()
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE)
        return objectMapper.readValue(languageJson)
    }

    private fun getLanguageResourceFile(language: String): URL? =
        LanguageManager.javaClass.classLoader.getResource("languages/$language.json")
}
