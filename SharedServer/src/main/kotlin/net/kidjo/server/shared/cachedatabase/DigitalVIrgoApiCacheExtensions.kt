package net.kidjo.server.shared.cachedatabase

import org.slf4j.LoggerFactory


private const val DV_PAIRING_DURATION = 5L * 60L // 5 min
private val logger = LoggerFactory.getLogger("DVApiCacheExtensions - ")

fun CacheDatabase.dv_deletePairing(customerId: String): <PERSON><PERSON><PERSON> {
    val commands = connection.sync()
    commands.del(CacheDatabase.KEY_DV + "pair" + customerId)
    return true
}
fun CacheDatabase.dv_setPairingInfo(customerId: String, userId: String): <PERSON><PERSON>an {
    try {
        val commands = connection.sync()
        val key = CacheDatabase.KEY_DV + "pair" + customerId
        commands.set(key, userId)
        commands.expire(key, DV_PAIRING_DURATION)
    } catch (e: Exception) {
        logger.error("Failed to set pairing info: ${e.localizedMessage}")
        return false
    }
    return true
}
fun CacheDatabase.dv_getPairingUserId(customerId: String): String? {
    if (customerId == "") return null

    val commands = connection.sync()
    val userId = commands.get(CacheDatabase.KEY_DV + "pair" + customerId)
    return userId
}