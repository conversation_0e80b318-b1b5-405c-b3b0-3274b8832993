package net.kidjo.server.shared.payments

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.tools.Config
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.apache.commons.codec.binary.Base64
import org.slf4j.LoggerFactory
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

class HuaweiApiManager(internal val config: Config) {

    private val okHttpClient = OkHttpClient()
    private val logger = LoggerFactory.getLogger("HuaweiApiManager")

    /**
     * Obtain the app-level access token.
     *
     * @return Return the app-level access token.
     * @throws Exception Exception thrown during method execution.
     */
    private suspend fun getAuthenticationToken(): String? {
        // Request Access token
        var clientSecretId = ""
        var clientId = ""
        if (config.isDeviceType.contains("kidjo_tv")) {
            clientSecretId = config.huaweiClientSecretForTv
            clientId = config.huaweiClientIdForTv
        } else if (config.isDeviceType.contains("kidjo_stories")) {
            clientSecretId = config.huaweiClientSecretForStories
            clientId = config.huaweiClientIdForStories
        }else if(config.isDeviceType.contains("kidjo_games")){
            clientSecretId = config.huaweiClientSecretForGames
            clientId = config.huaweiClientIdForGames
        }

        val body = FormBody.Builder()
            .add("grant_type", config.huaweiGrantType)
            .add(
                "client_secret",
                URLEncoder.encode(clientSecretId, "UTF-8")
            )
            .add("client_id", clientId)
            .build()
        val request = getATRequestBuilder(config.huaweiAuthorizationTokenUrl).post(body).build()
        val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
        if (response.code() != HttpStatusCode.OK.value) return ""

        // Retrieve auth object
        val authResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
        val authResponse = authResponseString?.let {
            jacksonObjectMapper().configure(
                DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false
            ).readValue<AuthResponse>(it)
        }

        response.close()
        // Return access token if any
        return authResponse?.access_token
    }

    private fun getATRequestBuilder(url: String): Request.Builder {
        return Request.Builder().url(url)
            .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
    }

    private suspend fun getSubscriptionRequestBuilder(): Request.Builder {
        val authToken = getAuthenticationToken()
        val oriString = "APPAT:$authToken"
        val basicAuthorization = Base64.encodeBase64String(oriString.toByteArray(StandardCharsets.UTF_8))
        return Request.Builder().url(config.huaweiSubscriptionUrl)
            .header("Content-Type", "application/json; charset=UTF-8")
            .header("Authorization", "Basic $basicAuthorization")
            .header("Accept", "application/json")
            .header("Content-Length", "171")
    }

    /**
     * Verify and return purchase receipt response from Huawei API
     */
    private suspend fun getResponse(
        purchaseToken: String, subscriptionType: String, inAppId: String,
        subscriptionId: String, call: ApplicationCall? = null
    ): Response? {

        val body = FormBody.Builder()
            .add(
                "purchaseToken",
                purchaseToken
            )
            .add("subscriptionId", subscriptionId)
            .build()

        val request =
            getSubscriptionRequestBuilder().post(
                body
            ).build()
        val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }

        logger.info("[HuaweiApiManager.getResonse] Response: $response")
        if (response.code() != HttpStatusCode.OK.value) {
            logger.error("[HuaweiApiManager.getResonse] Response from the Huawei receipt verify API was not OK")
            call?.respondError(HttpStatusCode.fromValue(response.code()))
            return null
        }

        return response
    }

    /**
     * Verify and return purchase receipt from Huawei API
     */
    suspend fun getSubscriptionResponse(
        purchaseToken: String,
        subscriptionType: String,
        inAppId: String,
        subscriptionId: String,
        call: ApplicationCall? = null
    ): HuaweiSubscriptionResponse? {

        var response = getResponse(purchaseToken, subscriptionType, inAppId, subscriptionId)
        // Retrieve auth object
        if (response != null) {
            val subscriptionResponseString = withContext(Dispatchers.IO) { response.body()?.string() }

            return subscriptionResponseString?.let {
                jacksonObjectMapper()
                    .readValue<HuaweiSubscriptionResponse>(it)
            }
        }
        return null
    }

    private fun getSubscriptionPurchaseDate(huaweiResponse: HuaweiSubscriptionResponse): HuaweiSubscriptionStatus? {

        if (huaweiResponse.inappPurchaseData != null) {

            return huaweiResponse.inappPurchaseData?.let {
                jacksonObjectMapper().configure(
                    DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false
                ).readValue<HuaweiSubscriptionStatus>(it)
            }
        }
        return null
    }

    /**
     * Verify and return purchase receipt from Huawei API
     */
    suspend fun getSubscriptionStatusSuspend(
        token: String,
        subscriptionType: String,
        inAppId: String,
        subscriptionId: String
    ): HuaweiSubscriptionStatus? {
        val subscriptionResponse = getSubscriptionResponse(token, subscriptionType, inAppId, subscriptionId)

        return getSubscriptionPurchaseDate(subscriptionResponse!!)
    }

    /**
     * Verify and return purchase receipt from Huawei API for JOB
     */
    fun getSubscriptionStatus(token: String, subscriptionType: String, inAppId: String, subscriptionId: String):
            HuaweiSubscriptionStatus? {
        var billingResponse: HuaweiSubscriptionStatus? = null

        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                billingResponse = getSubscriptionStatusSuspend(token, subscriptionType, inAppId, subscriptionId)
            }
        }
        return billingResponse
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class HuaweiSubscriptionResponse(
    val responseCode: Int,
    val inappPurchaseData: String
)

data class HuaweiSubscriptionStatus(
    val autoRenewing: Boolean,
    val subIsvalid: Boolean,
    val orderId: String = "",
    val lastOrderId: String = "",
    val packageName: String = "",
    val applicationId: String = "",
    val productId: String = "",
    val kind: Int,
    val productName: String = "",
    val productGroup: String = "",
    val purchaseTime: Long = 0,
    val oriPurchaseTime: Long = 0,
    val purchaseState: Int,
    val developerPayload: String = "",
    val purchaseToken: String = "",
    val purchaseType: Int,
    val currency: String = "",
    val price: Long = 0,
    val country: String = "",
    val subscriptionId: String = "",
    val quantity: Int = 0,
    val daysLasted: Long = 0,
    val numOfPeriods: Long = 0,
    val numOfDiscount: Long = 0,
    val expirationDate: Long = 0,
    val retryFlag: Int,
    val introductoryFlag: Int,
    val trialFlag: Int,
    val renewStatus: Int,
    val renewPrice: Int,
    val cancelledSubKeepDays: Int = 0,
    val payOrderId: String = "",
    val payType: String = "",
    val confirmed: Int,
    val message: String = "",
    val cancelReason: Int
)

data class AuthResponse(val access_token: String)
