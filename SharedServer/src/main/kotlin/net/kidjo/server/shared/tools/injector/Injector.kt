package net.kidjo.server.shared.tools.injector

import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.cachedatabase.CacheDatabase
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.payments.*
import net.kidjo.server.shared.payments.publichers.v4.*
import net.kidjo.server.shared.payments.publichers.v5.cafeyn.CafeynApiManager
import net.kidjo.server.shared.security.JwtKidjoServerManager
import net.kidjo.server.shared.tools.*
import net.kidjo.server.shared.tools.payments.PaymentManager
import okhttp3.OkHttpClient

class SharedInjector(
    val config: Config,
    val utility: Utility,
    val httpClient: OkHttpClient,
    val validator: Validator,
    val encryptionController: EncryptionController,
    val databaseController: DatabaseController,
    val cacheDatabase: CacheDatabase,
    val playStoreApiManager: PlayStoreApiManager,
    val digitalVirgoApiManager: DigitalVirgoApiManager,
    val orangeApiManager: OrangeApiManager,
    val appleApiManager: AppleApiManager,
    val mondiaPayManager: MondiaPayManager,
    val braintreeApiManager: BraintreeApiManager,
    val paymentManager: PaymentManager,
    val paymentPlayStoreManager: PaymentPlayStoreManager,
    val paymentAppStoreManager: PaymentAppStoreManager,
    val paymentSamsungStoreManager: PaymentSamsungStoreManager,
    val paymentOrangeStoreManager: PaymentOrangeStoreManager,
    val paymentHuaweiStoreManager: PaymentHuaweiStoreManager,
    val paymentAmazonStoreManager: PaymentAmazonStoreManager,
    val paymentJioStoreManager: PaymentJioStoreManager,
    val iapManager: IAPManager,
    val emailManager: EmailManager,
    val userManager: UserManager,
    val rulesManager: RulesManager,
    val couponManager: CouponManager,
    val languageCache: LanguageCache,
    val countryCodeByGoogle: CountryCodeByGoogle,
    val jwtManager: JwtKidjoServerManager,
    val cafeynApiManager: CafeynApiManager
) {

    companion object {

        fun Build(config: Config): SharedInjector {
            val validator = Validator(config)
            val utility = Utility()
            val httpClient = OkHttpClient()
            val encryptionController = EncryptionController(config)
            val iapManager = IAPManager(config)
            val database = DatabaseController(config, validator, encryptionController, iapManager, utility)
            val languageCache = LanguageCache(config, database)
            val cacheDatabase = CacheDatabase(config, encryptionController, database)
            val countryCodeByGoogle = CountryCodeByGoogle(validator, languageCache, database, cacheDatabase)
            val googleApiManager = PlayStoreApiManager(config)
            val orangeApiManager = OrangeApiManager(config)

            val huaweiApiManager = HuaweiApiManager(config)
            val samsungApiManager = SamsungApiManager()
            val appleApiManager = AppleApiManager()
            val amazonApiManager = AmazonApiManager(config)
            val jioApiManager = JioApiManager(config)
            val braintreeApiManager = BraintreeApiManager(config, database, encryptionController, utility)
            val couponManager = CouponManager(config, database, cacheDatabase)
            val emailManager = EmailManager(config, utility, cacheDatabase, validator)
            val jwtManager = JwtKidjoServerManager
            val userManager = UserManager(config, utility, database, cacheDatabase, jwtManager)

            val cafeynApiManager = CafeynApiManager(
                config,
                utility,
                encryptionController,
                database
            )

            val paymentManager = PaymentManager(
                config,
                httpClient,
                encryptionController,
                database,
                iapManager,
                googleApiManager,
                appleApiManager,
                orangeApiManager,
                samsungApiManager,
                huaweiApiManager,
                amazonApiManager,
                jioApiManager,
                cafeynApiManager
            )

            val paymentPlayStoreManager = PaymentPlayStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, googleApiManager,emailManager,languageCache,userManager
            )

            val paymentAppStoreManager = PaymentAppStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, appleApiManager,emailManager,languageCache,userManager
            )

            val paymentSamsungStoreManager = PaymentSamsungStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, samsungApiManager,emailManager,languageCache,userManager
            )

            val paymentOrangeStoreManager = PaymentOrangeStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, orangeApiManager,emailManager,languageCache,userManager
            )

            val paymentHuaweiStoreManager = PaymentHuaweiStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, huaweiApiManager,emailManager,languageCache,userManager
            )

            val paymentAmazonStoreManager = PaymentAmazonStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, amazonApiManager,emailManager,languageCache,userManager
            )

            val paymentJioStoreManager = PaymentJioStoreManager(
                config, httpClient, encryptionController,
                database, iapManager, jioApiManager,emailManager,languageCache,userManager
            )

            val mondiaPayManager =
                MondiaPayManager(config, utility, encryptionController, paymentManager, cacheDatabase, database)


            val ruleManager = RulesManager(config, iapManager, database)

            val digitalVirgoApiManager =
                DigitalVirgoApiManager(config, utility, validator, encryptionController, database)

            return SharedInjector(
                config,
                utility,
                httpClient,
                validator,
                encryptionController,
                database,
                cacheDatabase,
                googleApiManager,
                digitalVirgoApiManager,
                orangeApiManager,
                appleApiManager,
                mondiaPayManager,
                braintreeApiManager,
                paymentManager,
                paymentPlayStoreManager,
                paymentAppStoreManager,
                paymentSamsungStoreManager,
                paymentOrangeStoreManager,
                paymentHuaweiStoreManager,
                paymentAmazonStoreManager,
                paymentJioStoreManager,
                iapManager,
                emailManager,
                userManager,
                ruleManager,
                couponManager,
                languageCache,
                countryCodeByGoogle,
                jwtManager,
                cafeynApiManager
            )
        }

    }
}
