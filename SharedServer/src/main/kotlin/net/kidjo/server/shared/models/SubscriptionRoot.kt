package net.kidjo.server.shared.models

import java.time.LocalDateTime

class SubscriptionRootInsert(
    val userId: Long,
    val deviceId: Long,
    val isFreeTrail: Boolean,
    val priceToLogUSD: Float,
    val paymentType: SubscriptionRoot.PaymentType,
    val subscriptionType: SubscriptionRoot.SubscriptionType,
    val paymentId: String,
    val platformPurchaseId: String,
    val storeId: Device.StorePlatform,
    val paymentStateId: String?,
    val iapId: String,
    val purchasingSessionId: String,
    val subscriptionToken: String,
    val subscriptionTokenHash: String,
    val accountCouponId: Long,
    val isRenewing: Boolean,
    val nextBillDate: LocalDateTime,
    val isTest: Boolean,
    val operatorName: String = "",
) {

    override fun toString() = "$userId/$deviceId/$platformPurchaseId/$storeId/$isTest"
}

open class SubscriptionRootUpdate(
    open val userId: Long,
    open val isRenewing: Boolean,
    open val nextBillDate: LocalDateTime,
    open val platformPurchaseId: String,
    open val isFreeTrail: Boolean,
    open val priceToLogUSD: Float,
    open val subId: Long,
    open val paymentStateId: String?,
) {
    override fun toString() = "$subId/$userId/$nextBillDate/$platformPurchaseId"
}

data class SubscriptionRoot(
    val id: Long,
    val userId: Long,
    val isActive: Boolean,
    val isRenewing: Boolean,
    val stillInFreeTrial: Boolean,
    val paymentType: PaymentType,
    val subscriptionType: SubscriptionType,
    val paymentId: String,
    val platformPurchaseId: String,
    val storeId: Device.StorePlatform,
    val paymentStateId: String?,
    val iapId: String,
    val purchasingSessionId: String,
    val subscriptionToken: String,
    val subscriptionTokenHash: String,
    val accountCouponId: Long,
    val nextBillDate: LocalDateTime,
    val lastCheckDate: LocalDateTime,
    val updatedAt: LocalDateTime,
    val createdAt: LocalDateTime,
    val isTest: Boolean,
    val operatorName: String = "",
) {

    companion object {
        const val NO_ID = -1L
        const val OPERATOR_WEB = "web"
    }

    enum class PaymentType(val raw: String) {
        FREE("free"),
        COUPON("coupon"),
        NATIVE("native"),
        PAY_PAL("paypal"),
        CREDIT_CARD("cc"),
        MONDIA("mondia"),
        GOOGLE_PAY("google_pay");

        companion object {
            fun fromRaw(raw: String): PaymentType {
                return when (raw) {
                    FREE.raw -> FREE
                    NATIVE.raw -> NATIVE
                    PAY_PAL.raw -> PAY_PAL
                    CREDIT_CARD.raw -> CREDIT_CARD
                    COUPON.raw -> COUPON
                    MONDIA.raw -> MONDIA
                    GOOGLE_PAY.raw -> GOOGLE_PAY
                    else -> NATIVE
                }
            }
        }
    }

    enum class SubscriptionType(val raw: String) {

        KIDJO_TV("kidjo_tv"),
        KIDJO_BOOKS("kidjo_books"),
        KIDJO_GAMES("kidjo_games"),
        KIDJO_TV_BOOKS("kidjo_tv_books"),
        KIDJO_TV_GAMES("kidjo_tv_games"),
        KIDJO_BOOKS_GAMES("kidjo_books_games"),
        KIDJO_TV_BOOKS_GAMES("kidjo_tv_books_games")
        ;

        companion object {
            fun fromRaw(raw: String): SubscriptionType {
                return when (raw) {
                    KIDJO_TV.raw -> KIDJO_TV
                    KIDJO_BOOKS.raw -> KIDJO_BOOKS
                    KIDJO_GAMES.raw -> KIDJO_GAMES
                    KIDJO_TV_BOOKS.raw -> KIDJO_TV_BOOKS
                    KIDJO_TV_GAMES.raw -> KIDJO_TV_GAMES
                    KIDJO_BOOKS_GAMES.raw -> KIDJO_BOOKS_GAMES
                    KIDJO_TV_BOOKS_GAMES.raw -> KIDJO_TV_BOOKS_GAMES
                    "kidjo_books_tv" -> KIDJO_TV_BOOKS
                    "kidjo_games_tv" -> KIDJO_TV_GAMES
                    "kidjo_games_books" -> KIDJO_BOOKS_GAMES
                    "kidjo_books_tv_games" -> KIDJO_TV_BOOKS_GAMES
                    "kidjo_books_games_tv" -> KIDJO_TV_BOOKS_GAMES
                    "kidjo_tv_games_books" -> KIDJO_TV_BOOKS_GAMES
                    "kidjo_games_tv_books" -> KIDJO_TV_BOOKS_GAMES
                    "kidjo_games_books_tv" -> KIDJO_TV_BOOKS_GAMES
                    else -> KIDJO_TV
                }
            }
        }
    }

    enum class Status(val raw: String?) {
        ACTIVE("true"),
        INACTIVE("false"),
        NONE(null)
        ;

        companion object {
            fun fromRaw(raw: String?): Status =
                when (raw) {
                    ACTIVE.raw, "1" -> ACTIVE
                    INACTIVE.raw, "0" -> INACTIVE
                    null -> NONE
                    else -> NONE
                }

            fun fromName(name: String): Boolean? =
                when (name.trim().toUpperCase().capitalize()) {
                    ACTIVE.name -> true
                    INACTIVE.name -> false
                    NONE.name -> null
                    else -> null
                }
        }
    }
}
