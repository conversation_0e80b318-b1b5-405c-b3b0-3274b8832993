package net.kidjo.server.shared.models

class UserSubscriptionView(
    val userId: Long,
    val registeredDate: String,
    val name: String,
    val email: String,
    val country: String,
    val storeId: String,
    val subscriptionType: SubscriptionRoot.SubscriptionType,
    val subId: Long,
    val couponId: Long,
    val couponNumber: String,
    val groupId: String,
    val nextBillDate: String,
    val price: String,
    val plan: String,
    val status: String,
    val subCreatedDate: String,
    var couponType: String
)

class UserCouponsView(
    val userId: String?,
    val name: String?,
    val email: String?,
    val subscriptionType: SubscriptionRoot.SubscriptionType?,
    val groupId: String,
    val couponId: String,
    val couponNumber: String,
    var timeLeft: String,
    val redeemedDate: String,
    val redeemed: Boolean,
    var couponType: String,
    var partnerId: Int
)
