package net.kidjo.server.shared.models

import java.time.LocalDateTime


data class SubscriptionTransaction(val rootSubscriptionId: Long,
                                   val transactionType: TransactionType,
                                   val priceInUSDs: Float,
                                   val time: LocalDateTime) {
    enum class TransactionType(val raw: String, val canceled: Boolean) {
        SUBSCRIPTION("subscribe",false),

        FREE_TRIAL_END("free_trial_end",false),
        COUPON_END("coupon_end",false),
        OLD_SUBSCRIPTION_DURATION_END("old_sub_duration_end",false),

        RENEW("renew",false),

        CANCEL_USER("cancel_user",true),
        CANCEL_FAILED_PAYMENT("cancel_failed_pay",true),
        CANCEL_ADMIN("cancel_admin",true),
        CANCEL_EXPIRED("cancel_expired_subscription",true),

        COUPON_DURATION_EXTENSION("coupon_duration_extension",false)

    }
}
