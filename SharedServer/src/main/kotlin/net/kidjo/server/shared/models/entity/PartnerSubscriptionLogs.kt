package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object PartnerSubscriptionLogs : Table("partner_subscription_logs") {
    val requestId=integer("request_id")
    val request = varchar("request",2000)
    val partnerName = varchar("partner_name", 255)
    val requestTimestamp = datetime("request_timestamp")
    val subscriptionId = integer("subscription_id")
}