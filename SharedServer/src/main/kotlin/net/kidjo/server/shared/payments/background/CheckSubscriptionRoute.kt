package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder

fun PaymentManager.checkSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot,
    updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    try {
        return when (subscriptionRoot.storeId) {
            Device.StorePlatform.KIDJO_BRAINTREE -> checkBrainTreeSubscriptionStatus(
                subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder
            )
            Device.StorePlatform.PLAYSTORE -> checkPlayStoreSubscriptionStatus(
                subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder
            )
            Device.StorePlatform.IOS -> checkIOSSubscriptionStatus(subscriptionRoot, cancelStringBuilder)
            Device.StorePlatform.SAMSUNG -> checkSamsungStoreSubscriptionStatus(
                subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder
            )
            Device.StorePlatform.ORANGE -> checkOrangeSubscriptionStatus(
                subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder
            )
            Device.StorePlatform.HUAWEI -> checkHuaweiSubscriptionStatus(
                subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder
            )
          Device.StorePlatform.AMAZON -> checkAmazonSubscriptionStatus(
                subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder
            )
            Device.StorePlatform.CAFEYN-> checkCafeynSubscription(subscriptionRoot,
                updateStringBuilder,
                cancelStringBuilder)
            else -> {
                SubscriptionCheckResult(SubscriptionCheckResult.Status.CANCEL_EXPIRED, 0f, subscriptionRoot.nextBillDate, "expired" )
            }
        }
    } catch (e: Exception) {
        throw e
    }
}
