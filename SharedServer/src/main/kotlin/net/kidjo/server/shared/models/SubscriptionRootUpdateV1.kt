package net.kidjo.server.shared.models

import java.time.LocalDateTime

data class SubscriptionRootUpdateV1(
    override val userId: Long,
    override val isRenewing: Boolean,
    override val nextBillDate: LocalDateTime,
    override val platformPurchaseId: String,
    override val isFreeTrail: Boolean,
    override val priceToLogUSD: Float,
    override val subId: Long,
    override val paymentStateId: String?,
    val iapId: String,
    val accountCouponId: Long,
) : SubscriptionRootUpdate(
    userId,
    isRenewing,
    nextBillDate,
    platformPurchaseId,
    isFreeTrail,
    priceToLogUSD,
    subId,
    paymentStateId
)


