package net.kidjo.server.shared.payments.background


import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.publichers.v5.cafeyn.CafeynResponse
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.time.LocalDateTime

fun PaymentManager.checkCafeynSubscription(
    subscriptionRoot: SubscriptionRoot,
    updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    var cafeynResponsePair: Pair<Int, CafeynResponse?>? = null
    runBlocking<Unit> {
        launch(Dispatchers.IO) {
            cafeynResponsePair = cafeynApiManager.refreshTokenApi(subscriptionRoot.paymentStateId)
        }
    }
    logger.debug(
        "Response from cafeyn for subscription id- {} - {}",
        subscriptionRoot.id,
        cafeynResponsePair?.second.toString()
    )
    if (cafeynResponsePair?.first == 200 && cafeynResponsePair?.second?.isActive == true) {
        updateStringBuilder.append("Subscription is still active for cafeyn user -${subscriptionRoot.id}")
        return SubscriptionCheckResult(
            SubscriptionCheckResult.Status.ACTIVE,
            0.0f,
            LocalDateTime.now().plusDays(1),
            cafeynResponsePair?.second?.token
        )
    } else {
        cancelStringBuilder.append("Subscription is cancelled from cafeyn-${subscriptionRoot.id}")
        return SubscriptionCheckResult(
            SubscriptionCheckResult.Status.CANCEL_USER,
            0.0f,
            LocalDateTime.now(),
            cafeynResponsePair?.second?.token
        )
    }
}
