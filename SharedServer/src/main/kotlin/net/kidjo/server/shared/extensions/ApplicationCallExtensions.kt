package net.kidjo.server.shared.extensions

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.common.models.ApiError
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.tools.EncryptionController
import org.json.JSONObject
import java.util.*


fun ApplicationCall.getDeviceFromHeader(encryptionController: EncryptionController): Device {
    val headers = this.request.headers
    val deviceId = headers.getString("X-Kidjo-DeviceId", Device.NO_ID)
    val dateString = headers.getString("X-Kidjo-Date")
    val build = headers.getInt("X-Kidjo-Build", -1)
    val authorisationSHA1Hash = headers.getString("Authorization", headers.getString("Authorisation"))
    val store = Device.StorePlatform.fromRow(headers.getString("X-Kidjo-Store"))
    val model = headers.getString("X-Kidjo-DeviceType", "NONE")
    val userAgent = headers.getString("User-Agent")

    val device = Device(store, build)
    device.model = model

    val headerWasValid = encryptionController.checkHeaderHash(deviceId, dateString, authorisationSHA1Hash)
    if (headerWasValid) device.deviceFromHeaderWasValid()

    device.setUserId(deviceId, encryptionController)
    device.userAgent = userAgent
    return device
}

suspend fun ApplicationCall.receiveJSON(): JSONObject? {
    return try {
        JSONObject(receiveText())
    } catch (e: Exception) {
        println(e.localizedMessage)
        null
    }
}

fun ApplicationCall.validateParameter(paramName: String) =
    this.parameters[paramName].getParameterValue()

fun ApplicationCall.getParameterOrDefaultValue(paramName: String, default: Any? = null) =
    this.parameters[paramName].getParameterValue(default)

private fun String?.getParameterValue(value: Any? = null): String? =
    when {
        !this.isNullOrBlank() -> this
        this == null && value != null -> value.toString()
        else -> null
    }

fun ApplicationCall.getLocaleFromAcceptLanguage(): Locale? {
    val acceptLanguage = this.request.acceptLanguage() ?: return null

    val locales = Locale.LanguageRange.parse(acceptLanguage)
    if (locales.size > 0) return Locale.forLanguageTag(locales[0].range)

    return null
}

//OK responses
suspend fun ApplicationCall.respondJSON(jsonObject: JSONObject) {
    respondText(jsonObject.toString(), ContentType.Application.Json)
}

suspend fun ApplicationCall.respondHTML(html: String) {
    respondText(html, ContentType.Text.Html)
}

suspend fun ApplicationCall.respondOK() {
    respond(HttpStatusCode.OK, "")
}

suspend fun ApplicationCall.respondCreated(data: Any) {
    respond(HttpStatusCode.Created, data)
}

suspend fun ApplicationCall.respondOK(data: Any) {
    respond(HttpStatusCode.OK, data)
}

suspend fun ApplicationCall.respondOK(successType: Success, message: Any = "") {
    respondOk(HttpStatusCode.OK, successType.code, message)
}

fun ApplicationCall.getFullQueryString(): String {
    val query = this.request.queryString()
    if (query != "") return "?$query"
    return ""
}

//success
suspend fun ApplicationCall.respondOk(statusCode: HttpStatusCode, code: Int, message: Any = "") {
    respondText(
        "{\"code\" : $code${if (message != "") ",\"successMessage\" : \"$message\"" else ""}}",
        ContentType.Application.Json,
        statusCode
    )
}

suspend fun ApplicationCall.respondOk(data: Any, statusCode: HttpStatusCode, code: Int, message: String = "") {
    respondText(
        "{\"code\" : $code${if (message != "") ",\"successMessage\" : \"$message\"" else ""}}",
        ContentType.Application.Json,
        statusCode
    )
}

//errors
//gen
//doesn't escape strings
suspend fun ApplicationCall.respondError(statusCode: HttpStatusCode) {
    respond(statusCode, "")
}

suspend fun ApplicationCall.respondError(
    statusCode: HttpStatusCode,
    code: Int,
    messageCode: String = "",
    errorMessage: String = "",
) {
    respondText(
        "{\"code\" : $code ${if (messageCode != "") ",\"messageCode\" : \"$messageCode\"" else ""} ${if (errorMessage != "") ",\"errorMessage\" : \"$errorMessage\"" else ""} }",
        ContentType.Application.Json,
        statusCode
    )
}

suspend fun ApplicationCall.respondError(statusCode: HttpStatusCode, errorMessage: String) {
    respondText("{\"errorMessage\" : \"$errorMessage\"}", ContentType.Application.Json, statusCode)
}

suspend fun ApplicationCall.respondErrorWithLocalMessage(statusCode: HttpStatusCode, localMessage: String?) {
    respondText("{\"localErrorMessage\" : \"$localMessage\"}", ContentType.Application.Json, statusCode)
}

suspend fun ApplicationCall.respondCustomError(statusCode: HttpStatusCode, code: Any, errorMessage: String = "") {
    respondText(
        "{\"code\" : $code ${if (errorMessage != "") ",\"errorMessage\" : \"$errorMessage\"" else ""}}",
        ContentType.Application.Json,
        statusCode
    )
}

suspend fun ApplicationCall.respondBadDevice() {
    respondError(HttpStatusCode.BadRequest, "device Id does not exist")
}

suspend fun ApplicationCall.respondBadUser() {
    respondForbidden("You need to be signed in.")
}

suspend fun ApplicationCall.respondBadParameters() {
    respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Bad params")
}

suspend fun ApplicationCall.respondBadJSON() {
    respondBadRequest(ServerErrors.ERROR_BAD_JSON, "Bad JSON")
}

suspend fun ApplicationCall.respondDatabaseIssue() {
    respondError(HttpStatusCode.BadRequest, 1, "Issue with database")
}

suspend fun ApplicationCall.respondBadRequest(errorMessage: String = "") {
    respondError(HttpStatusCode.BadRequest, errorMessage)
}

suspend fun ApplicationCall.respondBadRequest(
    errorType: ServerErrors,
    messageCode: String = "",
    errorMessage: String = "",
) {
    respondError(HttpStatusCode.BadRequest, errorType.code, messageCode, errorMessage)
}

suspend fun ApplicationCall.respondBadRequest(
    errorType: UsersErrors,
    messageCode: String = "",
    errorMessage: String = "",
) {
    respondError(HttpStatusCode.BadRequest, errorType.code, messageCode, errorMessage)
}

suspend fun ApplicationCall.respondBadRequest(
    errorType: SubscriptionErrors,
    messageCode: String = "",
    errorMessage: String = "",
) {
    respondError(HttpStatusCode.BadRequest, errorType.code, messageCode, errorMessage)
}

suspend fun ApplicationCall.respondCustomError(error: Any, errorMessage: String = "") {
    respondCustomError(HttpStatusCode.BadRequest, error, errorMessage)
}

suspend fun ApplicationCall.respondConflict(errorType: SubscriptionErrors, errorMessage: String = "") {
    respondError(HttpStatusCode.Conflict, errorType.code, errorMessage)
}

suspend fun ApplicationCall.respondNoContent() {
    respondError(HttpStatusCode.NoContent)
}

suspend fun ApplicationCall.respondForbidden(errorMessage: String = "") {
    respondError(HttpStatusCode.Forbidden, errorMessage)
}

suspend fun ApplicationCall.respond404(errorMessage: String = "") {
    respondError(HttpStatusCode.NotFound, errorMessage)
}

suspend fun ApplicationCall.respondNotFound(errorType: UsersErrors, errorMessage: String = "") {
    respondError(HttpStatusCode.NotFound, errorType.code, errorMessage)
}

suspend fun ApplicationCall.respondGone(errorMessage: String = "") {
    respondError(HttpStatusCode.Gone, errorMessage)
}

suspend fun ApplicationCall.respondAppError(statusCode: HttpStatusCode, code: String, message: String?) {
    respond(statusCode, ApiError(code, message))
}

suspend fun ApplicationCall.getCorrelationId(): String? {
    /*
     get the CORRELATION ID for Mondia
     Note: for new partners integration, make sure to include other partners CORRELATION Ids
      as well to be able to trace the logs.
     */
    return request.headers["X-MM-CORRELATION-ID"]
}

suspend fun ApplicationCall.getTrxUuid(): String? {
    return request.headers["transaction-id"]
}

enum class ServerErrors(val code: Int) {
    INTERNAL_ERROR(-1),
    ERROR_BAD_PARAMS(1),
    ERROR_BAD_JSON(2),
    ERROR_SUBSCRIPTION_IS_NOT_RENEWING(3)
}

enum class UsersErrors(val code: Int) {
    ERROR_BAD_PASS(1),
    ERROR_BAD_EMAIL(2),
    ERROR_INVALID_COUPON(3),
    ERROR_NOT_EXIST_USER(4),
    ERROR_EXIST_USER(5),
    ERROR_LOGIN_LIMITED(6),
    ERROR_COUPON_ALL_READY_USED(7)
}

enum class SubscriptionErrors(val code: Int) {
    ERROR_CONFLICT_SUBSCRIPTION(1),
    ERROR_NOT_EXIST_SUBSCRIPTION(2),
    ERROR_EXIST_FOR_KIDJO_TV(4),
    ERROR_EXIST_FOR_KIDJO_BOOKS(5),
    ERROR_EXIST_FOR_KIDJO_TV_BOOKS(6),
    ERROR_PROBLEM_CREATE_SUBSCRIPTION(7),
    ERROR_PROBLEM_UPDATE_SUBSCRIPTION(8)
    ;
}

enum class Success(val code: Int) {
    SUCCESS(1)
}

enum class SamsungSubscriptionErrors(val code: Int) {
    ERROR_MISSING_PARAMETER(10),
    ERROR_INVALID_PARAMETER_VALUE(15),
    ERROR_INVALID_SECRET(300),
    ERROR_EXPIRED_SERVICE_TOKEN(301),
    UNKNOWN_ERROR(-1),
    SERVER_ERROR(500)
}
