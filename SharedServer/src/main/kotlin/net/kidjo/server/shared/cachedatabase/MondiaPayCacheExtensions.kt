package net.kidjo.server.shared.cachedatabase

import org.json.JSONObject
import org.slf4j.LoggerFactory


private const val MONDIA_PAIRING_DURATION = 5L * 60L // 5 min
private val logger = LoggerFactory.getLogger("MondiaPayCacheExtensions - ")

fun CacheDatabase.mondia_deletePairing(customerId: String): <PERSON><PERSON><PERSON> {
    val commands = connection.sync()
    commands.del(CacheDatabase.KEY_MONDIA + "pair" + customerId)
    return true
}
fun CacheDatabase.mondia_setPairingInfo(customerId: String, userId: String): Boolean {
    try {
        val commands = connection.sync()
        val key = CacheDatabase.KEY_MONDIA + "pair" + customerId
        commands.set(key, userId)
        commands.expire(key, MONDIA_PAIRING_DURATION)
    } catch (e: Exception) {
        logger.error("Failed to set pairing info: ${e.localizedMessage}")
        return false
    }
    return true
}
fun CacheDatabase.mondia_getPairingUserId(customerId: String): String? {
    if (customerId == "") return null

    val commands = connection.sync()
    val userId = commands.get(CacheDatabase.KEY_MONDIA + "pair" + customerId)
    return userId
}