package net.kidjo.server.shared.cachedatabase

private const val EMAIL_KEY_LIFE = 24L * 60L * 60L //1 day
fun CacheDatabase.email_confirmationSet(keyString: String, userId: Long, confirmationCode: String): Boolean {
    val commands = connection.sync()
    val key = CacheDatabase.KEY_EMAIL_CONFIRMATION + keyString
    commands.set(key, "$userId,$confirmationCode")
    commands.expire(key, EMAIL_KEY_LIFE)
    return true
}
fun CacheDatabase.email_getConfirmationCode(keyString: String): Pair<Long, String>? {
    val commands = connection.sync()
    val key = CacheDatabase.KEY_EMAIL_CONFIRMATION + keyString
    val str = commands.get(key) ?: return null
    val slice = str.split(",")
    if (slice.size < 1) return null
    val idString = slice[0]
    val confirmationCode = slice[1]
    try {
        return Pair(idString.toLong(),confirmationCode)
    } catch (e: Exception) {
        return null
    }
}
fun CacheDatabase.email_removeConfirmationCode(keyString: String) {
    val commands = connection.sync()
    val key = CacheDatabase.KEY_EMAIL_CONFIRMATION + keyString
    commands.del(key)
}