package net.kidjo.server.shared.models

import net.kidjo.server.shared.tools.EncryptionController
import org.json.JSONObject

data class DeviceUserPairing(val code: String,
                             val serverDeviceId: Long,
                             val userId: Long,
                             val step: PairingStep) {

    val isRequestingDevicePair = serverDeviceId == OPEN_REQUEST
    val isRequestingUserPair = userId == OPEN_REQUEST

    fun toJSON(encryptionController: EncryptionController): JSONObject {
        val json = JSONObject()
        json.put("code", code)
        json.put("deviceId", encryptionController.encodeNormalId(serverDeviceId))
        json.put("step", step.raw)
        return json
    }
    fun toServerJSON():JSONObject {
        val json = JSONObject()
        json.put("serverDeviceId", serverDeviceId)
        json.put("userId", userId)
        json.put("step", step.raw)
        return json
    }
    fun toServerJSONString():String {
        return toServerJSON().toString()
    }
    fun getWithNewPairing(newStep: PairingStep): DeviceUserPairing {
        return DeviceUserPairing(code, serverDeviceId, userId, newStep)
    }


    companion object {
        const val OPEN_REQUEST = -1L
        const val NO_ID = 0L

        const val API_2_PAIRING_TOKEN_KEY_LENGTH = 10

        fun FromServerJSON(code: String, jsonObject: JSONObject): DeviceUserPairing {
            val deviceId = jsonObject.optLong("serverDeviceId", NO_ID)
            val userId = jsonObject.optLong("userId",NO_ID)
            val step = PairingStep.FromRaw(jsonObject.optInt("step"))

            return DeviceUserPairing(code, deviceId, userId, step)
        }
    }
    enum class PairingStep(val raw: Int) {
        FAILED(0), SET_UP(1), ACCOUNT_CREATED(2), SUBSCRIBED_AND_IS_COMPLETE(3);

        companion object {
            fun FromRaw(raw: Int): PairingStep {
                return when (raw) {
                    FAILED.raw -> FAILED
                    SET_UP.raw -> SET_UP
                    ACCOUNT_CREATED.raw -> ACCOUNT_CREATED
                    SUBSCRIBED_AND_IS_COMPLETE.raw -> SUBSCRIBED_AND_IS_COMPLETE
                    else -> FAILED
                }
            }
        }
    }
}