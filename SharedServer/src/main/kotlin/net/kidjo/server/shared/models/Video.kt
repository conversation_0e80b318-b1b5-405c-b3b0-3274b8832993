package net.kidjo.server.shared.models

import java.time.LocalDate


data class Video(val serverId: Long, //never leaves server
                 val userId: String,
                 val title: String,
                 val ageMin: Int,
                 val ageMax: Int,
                 val isPremium: Boolean,
                 val duration: Int,
                 val compilation: Boolean,

                 val formats: Array<Format>,
                 val formatSizes: LongArray,
                 val creation: String,
                 val creationDate: LocalDate?) {

    companion object {
        const val NO_SERVER_ID = 0L
    }
    enum class Format(val id: Int,
                      val height: Int,
                      val codec: String,
                      val delivery: String,
                      val extension: String,
                      val bitRate: Int) {
        NONE(0,0,"","","",0),
        HLS_720(1,720,"h264", "hls","mp4",0),
        DASH_720(2,720,"h264", "dash","mpd",0),
        MP4_720(3,720,"h264", "none","mp4",2000),
        HLS_480(4,480,"h264", "hls","mp4",0),
        DASH_480(5,480,"h264", "dash","mpd",0),
        MP4_480(6,480,"h264", "none","mp4",1000),
        MP4_360(7,360,"h264", "none","mp4",500),
        MP4_240(8,240,"h264", "none","mp4",192);

        companion object {
            const val STREAMING_TYPE_MP4 = "none"

            fun GetVideoUrlWithFormat(videoId: String, format: Format): String {
                return if (format.delivery == Format.STREAMING_TYPE_MP4)
                    "${format.id}/$videoId.${format.extension}"
                else
                    "${format.id}/$videoId/$videoId.${format.extension}"
            }
            fun FromId(id: Int): Format {
                return when (id) {
                    HLS_720.id -> HLS_720
                    DASH_720.id -> DASH_720
                    MP4_720.id -> MP4_720
                    HLS_480.id -> HLS_480
                    DASH_480.id -> DASH_480
                    MP4_480.id -> MP4_480
                    MP4_360.id -> MP4_360
                    MP4_240.id -> MP4_240
                    else -> NONE
                }
            }

        }
    }
}

data class VideoSimple(val videoPath: String)
