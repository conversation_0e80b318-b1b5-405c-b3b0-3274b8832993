package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.update
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_update_nextBillingDateBySubToken
import net.kidjo.server.shared.database.updateDVPairedSubscriptionStatus
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private val logger  = LoggerFactory.getLogger("updateKIDJOSubscription - ")
@Throws
fun updateKIDJOSubscription(
    nextBillDate: LocalDateTime,
    subToken: String,
    subStatus: String?= null,
    correlationId: String? = null,
    databaseController: DatabaseController
): Boolean = try {

    if(!databaseController.subscription_update_nextBillingDateBySubToken(nextBillDate, subToken)){
        logger.error("UPDATE NEXT BILLING DATE ERROR: A problem occurred while update sub status: $subStatus, nextBillDate:  $nextBillDate")
        throw IllegalArgumentException("Can not take subscription_update_nextBillingDateBySubToken of false.")
    }
    if(!databaseController.updateDVPairedSubscriptionStatus(subStatus, subToken, correlationId)){
        logger.error("UPDATE Subscription status ERROR: A problem occurred while update sub status: $subStatus")
        throw IllegalArgumentException("Can not take updateDVPairedSubscriptionStatus of false.")
    }

    if(SubscriptionStatusType.SUSPENDED.raw.equals(subStatus)){
        logger.info("Suspending the subscription for {}",subToken)
        val result=databaseController.subscription_update_nextBillingDateBySubToken(LocalDateTime.now().plusDays(15),subToken)
        if(!result){
            logger.error("Unable to change the status to suspended- for $subToken");
        }
    }

    logger.info("UPDATE SUCCESS: Successfully update sub token: $subToken, status: $subStatus, nextBillDate:  $nextBillDate"  )
    true

} catch (e: Exception){
    logger.error("Error Kidjo updating subscription, ${e.localizedMessage}")
    throw e
}
