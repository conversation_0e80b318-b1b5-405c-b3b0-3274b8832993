package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.sms

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.VirgoTemplates
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.sms.send.sendSENDITOApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.sms.createSMSText
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.create.GENERATED_CHARACTERS_TO_USE
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.create.createKIDJORedirectURL
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.upperCase
import org.slf4j.LoggerFactory
class DVSenditoApi(
    private val config: Config,
    private val utility: Utility,
    private val databaseController: DatabaseController
) {
    internal val logger = LoggerFactory.getLogger("DVSenditoApi - ")
    val httpClient = OkHttpClient()
    suspend fun sendSMS(
        user: User,
        subIdString: String,
        userAlias: String,
        subCountryCode: String,
        operatorName: String,
        offerId: String,
        packageId: Long
    ): Boolean = try {
        var alias=""
        val redirectURL =
            createKIDJORedirectURL(
                userId = user.getLongId(),
                redirectURL = config.dv_login_redirect_url,
                generatedKey = utility.randomString(4, GENERATED_CHARACTERS_TO_USE),
                databaseController = databaseController
            )

        if (redirectURL.isNullOrBlank()) {
            logger.error("Failed creating redirect URL: $redirectURL")
        }

        val smsMessage =
            createSMSText(
                userLogin = user.email,
                userPass = user.password,
                operatorName = operatorName,
                redirectURL = redirectURL,
                packageId=packageId
            )

        if (smsMessage.isBlank()) {
            logger.error("Failed creating SMS message: $smsMessage")
        }
        logger.info("SEND SENDITO SMS: smsMessage'${smsMessage}' ")
        if (user.email.isNotEmpty() && operatorName=="NONE"){
            alias=user.email
            logger.info("SEND SENDITO SMS: email'${user.email}' ")
        }else{
            alias=userAlias
            logger.info("SEND SENDITO SMS: userAlias'${userAlias}' ")
        }
        logger.info("SEND SENDITO SMS: operationIdString'${subIdString}' ")
        logger.info("SEND SENDITO SMS: subscriptionCountry'${subCountryCode}' ")
        logger.info("SEND SENDITO SMS: userId'${user.getLongId()}' ")
        logger.info("SEND SENDITO SMS: pass'${user.password}' ")

        val resultSend =
            sendSENDITOApi(
                messageSMSValue = smsMessage,
                destinationSMSValue= alias,
                operationIdSMSValue = subIdString,
                offerIdSMSValue = offerId,
                countryCodeSMSValue = subCountryCode,
                dvSesameLogin = config.dv_sesame_login,
                dvSesamePasswordHashed = config.dv_sesame_password_hashed,
                senditoApiUrl = config.dv_sendito_message_api_url,
                url_cvg = config.url_cvg,
                site=config.site,
                databaseController = databaseController,
                login = user.email,
                password = user.password,
                url = redirectURL!!,
                urlToken = redirectURL,
                templateId= getTemplateIdByCountryCode(subCountryCode)!!,
                operatorName = operatorName
            )

        if (!resultSend) {
            logger.error("Failed to send SMS to the user: $userAlias ")
        }

        true
    } catch (e: Exception) {
        logger.error("Error Sendito Send SMS, ${e.localizedMessage}")
        throw e
    }
}

private fun getTemplateIdByCountryCode(countryCode: String): Long? {
    return transaction {
        VirgoTemplates
            .select {
                (VirgoTemplates.isActive eq true) and
                        (VirgoTemplates.countryCode.upperCase() eq countryCode)
            }
            .limit(1) // Fetch only the first match
            .mapNotNull { it[VirgoTemplates.templateId] }
            .firstOrNull() ?: 0L // Return the first result or null
    }
}


