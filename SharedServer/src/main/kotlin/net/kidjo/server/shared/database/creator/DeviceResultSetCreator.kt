package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.Device
import java.sql.ResultSet


fun ResultSetObjectCreator.device_update(device:Device, resultSet: ResultSet) {
    device.model = resultSet.getString("model")
    device.countryId = resultSet.getInt("countryId")
    device.languageId = resultSet.getInt("languageId")
    device.createdAt = resultSet.getDate("creation").time

    if (device.userAgent == "" && resultSet.getObject("userAgent") != null) {
        device.userAgent = resultSet.getString("userAgent")
    }
    device.infoFromServer = true
}