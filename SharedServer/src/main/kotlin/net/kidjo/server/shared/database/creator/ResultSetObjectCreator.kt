package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.database.CouponSearchListDTO
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.CouponBatchDetails
import net.kidjo.server.shared.models.CsvReportAccountCoupon
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.Utility
import java.sql.ResultSet

class ResultSetObjectCreator(
    val encryptionController: EncryptionController,
    val iapManager: IAPManager,
    val utility: Utility
) {

    fun toCouponSearch(resultSet: ResultSet): CouponSearchListDTO {
        val id = resultSet.getLong("id")
        val groupId = resultSet.getString("groupId")

        val startDate = try {
            resultSet.getDate("startDate").toLocalDate().toString()
        } catch (_: Exception) {
            null
        }
        val userStartDate = try {
                resultSet.getDate("userStartDate").toLocalDate().toString()
        } catch (_: Exception) {
            null
        }
        val expireDate = try {
            resultSet.getDate("expireDate").toLocalDate().toString()
        } catch (_: Exception) {
            null
        }
        val status = try {
            resultSet.getString("status")
        } catch (_: Exception) {
            null
        }
        val userId = try {
            resultSet.getLong("userId")
        } catch (_: Exception) {
            null
        }
        val email = try {
            resultSet.getString("email")
        } catch (_: Exception) {
            null
        }
        val country = try {
            resultSet.getString("country")
        } catch (_: Exception) {
            null
        }
        val serialNumber = try {
            resultSet.getString("couponId")
        } catch (_: Exception) {
            null
        }
        val redeemed = try {
            resultSet.getString("redeemed")
        } catch (_: Exception) {
            null
        }
        val durationCode = resultSet.getString("duration")
        val type = resultSet.getString("type")
        val product = resultSet.getString("product")
        val partner = resultSet.getString("partner")

        return CouponSearchListDTO(
            id = id,
            groupId = groupId,
            serialNumber = serialNumber,
            startDate = userStartDate ?: startDate,
            expireDate = expireDate,
            durationCode = durationCode,
            status = status,
            redeemed = redeemed,
            type = type,
            product = product,
            partner = partner,
            userId = if(userId == 0L) null else userId,
            email = email,
            country = country
        )
    }

    fun csvAccountCoupon(resultSet: ResultSet): CsvReportAccountCoupon {
        val groupId = resultSet.getString("groupId")
        val couponId = resultSet.getString("couponId")
        val expireDate = resultSet.getTimestamp("expireDate").toLocalDateTime()
        val durationCode = resultSet.getString("duration")
        val redeemedTimes = resultSet.getString("redeemedTimes")
        val couponType = resultSet.getString("coupon_type_name")
        val productType = resultSet.getString("product_name")
        val partnerName = resultSet.getString("partner_name")
        return CsvReportAccountCoupon(
            groupId,
            couponId,
            expireDate,
            durationCode,
            redeemedTimes,
            couponType,
            productType,
            partnerName
        )
    }

    fun couponBatchDetails(resultSet: ResultSet): CouponBatchDetails {
        val groupId = resultSet.getString("groupId")
        val expireDate = resultSet.getDate("expireDate").toLocalDate().toString()
        val durationCode = resultSet.getString("duration")
        val couponType = resultSet.getString("type")
        val productType = resultSet.getString("product")
        val partnerName = resultSet.getString("partner")
        return CouponBatchDetails(
            groupId,
            expireDate,
            durationCode,
            couponType,
            productType,
            partnerName
        )
    }

    fun accountCoupon(resultSet: ResultSet): AccountCoupon {
        val id = resultSet.getLong("id")
        val groupId = resultSet.getString("groupId")
        val couponId = resultSet.getString("couponId")
        val startDate = resultSet.getTimestamp("startDate").toLocalDateTime()
        val expireDate = resultSet.getTimestamp("expireDate").toLocalDateTime()
        val durationCode = resultSet.getString("duration")
        val redeemedTimes = resultSet.getInt("redeemedTimes")
        val redeemAmount = resultSet.getInt("redeemAmount")
        val couponType = resultSet.getString("coupon_type_name")
        val productType = resultSet.getString("product_name")
        val partnerName = resultSet.getString("partner_name")
        val creationDate = resultSet.getTimestamp("created_at").toLocalDateTime()

        return AccountCoupon(
            id,
            groupId,
            couponId,
            startDate,
            expireDate,
            durationCode,
            redeemedTimes,
            redeemAmount,
            couponType,
            productType,
            partnerName,
            creationDate
        )
    }
}