package net.kidjo.server.shared.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class Subscription(

    @JsonProperty("receipt")
    var receipt: String? = "",
    @JsonProperty("iap")
    var iapId: String? = "",
    @JsonProperty("deviceType")
    var deviceType: String? = "",
    @JsonProperty("sessionId")
    var sessionId: String? = "",
    @JsonProperty("forceUpdate")
    var forceUpdate: Boolean,
    @JsonProperty("subscriptionId")
    var subscriptionId: String? = "",
    @JsonProperty("orderId")
    var orderId: String? = "",
    @JsonProperty("amazonUserId")
    var amazonUserId: String? = "",
    @JsonProperty("deviceInfo")
    var deviceInfo: DeviceInfo,
    @JsonProperty("country")
    var country: String = ""
)