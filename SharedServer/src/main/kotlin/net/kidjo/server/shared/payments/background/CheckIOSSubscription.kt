package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.tools.payments.PaymentManager
import okhttp3.MediaType
import java.lang.StringBuilder

private val OK_HTTP_JSON_CONTENT_TYPE = MediaType.parse("application/json; charset=utf-8")

const val APP_STORE_VERIFY_RECEIPT = "https://buy.itunes.apple.com/verifyReceipt"
const val APP_STORE_VERIFY_RECEIPT_SANDBOX = "https://sandbox.itunes.apple.com/verifyReceipt"

private const val RESPONSE_STATUS_OK = 0

fun PaymentManager.checkIOSSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    var cancelStringBuilder = StringBuilder()
    if (subscriptionRoot.storeId != Device.StorePlatform.IOS) throw SubscriptionCheckException("Error: Not an iOS App Store receipt")
    val iap = iapManager.getIOSIAP(subscriptionRoot.iapId) ?: throw SubscriptionCheckException("Could not find the iap")
    cancelStringBuilder.appendln(
        "CANCEL = APPSTORE SUBSCRIPTION id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}"
    )
    return SubscriptionCheckResult(
        SubscriptionCheckResult.Status.CANCEL_USER,
        iap.price.toFloat(), subscriptionRoot.nextBillDate, subscriptionRoot.paymentStateId
    )
}