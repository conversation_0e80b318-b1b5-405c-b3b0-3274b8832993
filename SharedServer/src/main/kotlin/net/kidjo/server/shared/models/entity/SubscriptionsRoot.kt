package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.CurrentTimestamp
import org.jetbrains.exposed.sql.javatime.datetime

object SubscriptionsRoot : Table("subscriptions_root") {
    val id = long("id").autoIncrement()
    val userId = long("userId").default(0)
    val isActive = bool("isActive").default(true)
    val isRenewing = bool("isRenewing").default(true)
    val stillInFreeTrial = bool("stillInFreeTrial")
    val paymentType = enumerationByName("paymentType", 255, PaymentType::class).nullable()
    val subscriptionType =
        enumerationByName("subscriptionType", 255, SubscriptionType::class).default(SubscriptionType.KIDJO_TV)
    val paymentId = varchar("paymentId", 255).default("")
    val platformPurchaseId = varchar("platformPurchaseId", 256)
    val storeId = varchar("storeId", 255).nullable()
    val paymentStateId = varchar("paymentStateId", 255).nullable()
    val iapId = varchar("iapId", 255)
    val purchasingSessionId = varchar("purchasingSessionId", 32)
    val subscriptionToken = text("subscriptionToken")
    val subscriptionTokenHash = text("subscriptionTokenHash")
    val accountCouponId = long("accountCouponId").default(0)
    val nextBillDate = datetime("nextBillDate")
    val lastCheckDate = datetime("lastCheckDate").defaultExpression(CurrentTimestamp())

    val createdAt = datetime("created_at").nullable()

    /*val updatedAt =
        datetime("updated_at").nullable().defaultExpression(CurrentTimestamp()).onUpdateExpression(CurrentTimestamp())*/
    val isTest = bool("isTest").default(false)
    val operatorName = varchar("operatorName", 100).nullable()

    override val primaryKey: PrimaryKey?
        get() = PrimaryKey(id)
}

// Enums for the enumeration columns
enum class PaymentType {
    FREE, COUPON, NATIVE, CC, PAYPAL, GOOGLE_PAY, APPLE_PAY, MONDIA
}

enum class SubscriptionType {
    KIDJO_BOOKS, KIDJO_TV, KIDJO_TV_BOOKS, KIDJO_GAMES, KIDJO_TV_BOOKS_GAMES, KIDJO_TV_GAMES, KIDJO_BOOKS_GAMES
}