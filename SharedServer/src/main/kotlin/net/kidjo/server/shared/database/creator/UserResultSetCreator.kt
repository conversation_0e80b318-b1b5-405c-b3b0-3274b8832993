package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import java.sql.ResultSet

fun ResultSetObjectCreator.userFull(resultSet: ResultSet): User {
    val id = resultSet.getLong("id")
    val email = resultSet.getString("email")
    val emailIsConfirmed = resultSet.getBoolean("email_is_confirmed")
    val name = resultSet.getString("name")
    val countryId = resultSet.getInt("country_id")
    val hashedPassword = resultSet.getString("password")
    val brainTreeId = resultSet.getString("brainTreeId")
    val authId = resultSet.getString("authId")
    val authType = User.AuthType.fromRaw(resultSet.getString("authType"))
    val oAuthType = User.OAuthType.fromRaw(resultSet.getString("oAuthType"))
    var isSubscriber = false
    try {
        isSubscriber = resultSet.getBoolean("subscriptionIsActive")
    } catch (e: Exception) {
    }
    val user = User(id.toString(), email, emailIsConfirmed, name, countryId, hashedPassword)
    user.authId = authId
    user.authType = authType
    user.oAuthType = oAuthType
    user.isSubscribed = isSubscriber
    user.brainTreeId = brainTreeId
    return user
}

fun ResultSetObjectCreator.userAndSubscription(resultSet: ResultSet): Pair<User, SubscriptionRoot?> {
    val user = userFull(resultSet)
    val subscription: SubscriptionRoot?
    if (resultSet.getObject("s_id") != null) {
        subscription = this.subscription(resultSet, "s_")
        user.isSubscribed = true
    } else {
        subscription = null
    }
    return Pair(user, subscription)
}

fun ResultSetObjectCreator.userAccountJWT(resultSet: ResultSet): UserJWT {
    val id = resultSet.getLong("id")
    val userId = resultSet.getLong("userId")
    val authToken = resultSet.getString("authToken")
    return UserJWT(id, userId, authToken)
}

data class UserJWT(val id: Long, val userId: Long, val authToken: String)