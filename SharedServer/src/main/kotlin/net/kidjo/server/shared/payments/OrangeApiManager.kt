package net.kidjo.server.shared.payments

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.http.*
import io.ktor.server.application.*
import kotlinx.coroutines.*
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.tools.Config
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.slf4j.LoggerFactory


class OrangeApiManager(internal val config: Config) {
    private val okHttpClient = OkHttpClient()
    private val logger = LoggerFactory.getLogger("OrangeApiManager")

    /**
     * Verify and return purchase receipt from ORANGE API
     */
    suspend fun getSubscriptionStatus(
        token: String,
        subscriptionType: String,
        inAppId: String,
        call: ApplicationCall? = null
    ):
            SubscriptionStatus? {
        var response = getResponse(token, subscriptionType, inAppId)

        if (response.code() != HttpStatusCode.OK.value) {
            logger.error("Response from the Orange receipt verify API was not OK")
            call?.respondError(HttpStatusCode.fromValue(response.code()))
        }

        return getSubscriptionResponse(token, subscriptionType, inAppId)
    }

    /**
     * Verify and return purchase receipt from ORANGE API for JOB
     */
    fun getSubscriptionStatus(token: String, subscriptionType: String, inAppId: String):
            SubscriptionStatus? {
        var billingResponse: SubscriptionStatus? = null

        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                billingResponse = getSubscriptionResponse(token, subscriptionType, inAppId)
            }
        }

        return billingResponse
    }

    private suspend fun getSubscriptionResponse(
        token: String,
        subscriptionType: String,
        inAppId: String
    ): SubscriptionStatus? {
        var response = getResponse(token, subscriptionType, inAppId)
        var billingResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
        var billingResponse: SubscriptionStatus? = null
        try {
            billingResponse = billingResponseString?.let {
                jacksonObjectMapper().readValue<SubscriptionStatus>(it)
            }
        } catch (ex: Exception) {
            return null
        }
        if (billingResponse == null || billingResponse.startTimeMillis == billingResponse.expiryTimeMillis) {
            return null
        }

        return billingResponse
    }

    private suspend fun getResponse(token: String, subscriptionType: String, inAppId: String): Response {
        val validationUrl = getOrangeUrl(token, subscriptionType, inAppId)
        var request = Request.Builder().url(validationUrl).get().build()
        var response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
        return response
    }

    private fun getOrangeUrl(token: String, subscriptionType: String, inAppId: String): String {
        val orangePackageName = getOrangePackage(subscriptionType)
        val validationUrl = String.format(
            config.orange_verify_receipt_url, orangePackageName,
            inAppId, token
        )
        return validationUrl
    }

    private fun getOrangePackage(subscriptionType: String): String {
        var orangePackage = config.orange_kidjo_tv_package_name
        if (subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_BOOKS.raw) {
            orangePackage = config.orange_kidjo_books_package_name
        }
        return orangePackage
    }
}

data class SubscriptionStatus(
    val kind: String = "",
    val startTimeMillis: Long = 0,
    val expiryTimeMillis: Long = 0,
    val autoRenewing: Boolean = false,
    val priceCurrencyCode: String = "",
    val priceAmountMicros: Long = 0,
    val countryCode: String? = "",
    val developerPayload: String? = "",
    val paymentState: Int = 0,
    val cancelReason: Int? = 0
)
