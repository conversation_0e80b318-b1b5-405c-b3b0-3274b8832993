package net.kidjo.server.shared.database

import java.sql.Timestamp

/**
 * Persist cafeyn incoming request
 */
fun DatabaseController.persistCafeynRequest(
    requestId: String?,
    cafeynHubToken: String?,
    timestamp: String?,
    signature: String?
) {
    val connection = dataSource.connection
    val statement = connection.prepareStatement(
        "insert into cafeynrequests (requestId," +
                "cafeynHubToken," +
                "timeStamp," +
                "signature," +
                "incomingRequestDate) values(?,?,?,?,?)"
    );
    statement.setString(1, requestId)
    statement.setString(2, cafeynHubToken)
    statement.setString(3, timestamp)
    statement.setString(4, signature)
    statement.setTimestamp(5, Timestamp(System.currentTimeMillis()))
    statement.execute()
    statement.close()
    connection.close()
}

fun DatabaseController.updateAsValidRequest(requestId: String?, valid: Boolean) {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE cafeynrequests set valid=? where requestId=?")
    statement.setBoolean(1, valid)
    statement.setString(2, requestId)
    statement.executeUpdate()
    statement.close()
    connection.close()
}


