package net.kidjo.server.shared.models

import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.shared.tools.LanguageTerms

class InAppPurchase(
    val store: Device.StorePlatform,
    val id: String,
    val buildRequired: Int,
    val price: Double,
    val durationCode: String,
    val freeTrialCode: String,
    val familyId: String,
    val merchantAccountId: MerchantAccountId = MerchantAccountId.DEFAULT
) {

    val freeTrialInDays: Int = GetDayFromFreeTrialISO8601Code(freeTrialCode)
    val noFreeTrial: Boolean
        get() = freeTrialInDays == 0

    fun getInAppPriceDisplay(countryShortName: String, languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms()): String {
        val country = countryShortName.toLowerCase()
        val currency = if (country == "gb") "£" else if (country == "us") "$" else "€"
        val currencyFirst = country == "gb" || country == "us"
        val count = if (durationCode.isNotBlank()) durationCode[1].toString().toIntOrNull() ?: 0 else 0
        val type = getDurationType(languageTerms, count)
        val duration = "${if (count != 1) count.toString() else ""}$type"
        return "${if (currencyFirst) currency else ""}$price${if (!currencyFirst) currency else ""}/$duration"
    }

    fun getDurationCount(): Int {
        return if (durationCode.isNotBlank()) durationCode[1].toString().toIntOrNull() ?: 0 else 0
    }

    fun getDurationType(languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms(), count: Int = 1): String {
        val defaultLanguageTerms = LanguageManager.getDefaultLanguageTerms()
        val type = when(if (durationCode.isNotBlank()) durationCode[2].toString().toLowerCase() else null) {
            "d" -> languageTerms.subscriptionViewNewDay.ifBlank { defaultLanguageTerms.subscriptionViewNewDay }
            "y" -> languageTerms.subscriptionViewNewYear.ifBlank { defaultLanguageTerms.subscriptionViewNewYear }
            else -> languageTerms.subscriptionViewNewMonth.ifBlank { defaultLanguageTerms.subscriptionViewNewMonth }
        }
        return if (count > 1 && type.last() != 's') "${type}s" else type
    }

    fun getPricePer(languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms(), pricePer: String): String {
        val defaultLanguageTerms = LanguageManager.getDefaultLanguageTerms()
        return when (pricePer.toLowerCase()) {
            "day" -> languageTerms.subscriptionViewNewDay.ifBlank { defaultLanguageTerms.subscriptionViewNewDay }
            "year" -> languageTerms.subscriptionViewNewYear.ifBlank { defaultLanguageTerms.subscriptionViewNewYear }
            else -> languageTerms.subscriptionViewNewMonth.ifBlank { defaultLanguageTerms.subscriptionViewNewMonth }
        }
    }

    fun getFreeTrialCount(): String? {
        return if (freeTrialCode.length == 3) freeTrialCode.substring(1, freeTrialCode.length - 1) else null
    }

    fun getFreeTrialPer(languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms()): String? {
        if (freeTrialCode.length != 3) return null
        val last = freeTrialCode.last().toString()
        return when (last.toLowerCase()) {
            "d" -> languageTerms.subscriptionViewNewDay.ifBlank { LanguageManager.getDefaultLanguageTerms().subscriptionViewNewDay }
            "y" -> languageTerms.subscriptionViewNewYear.ifBlank { LanguageManager.getDefaultLanguageTerms().subscriptionViewNewYear }
            else -> languageTerms.subscriptionViewNewMonth.ifBlank { LanguageManager.getDefaultLanguageTerms().subscriptionViewNewMonth }
        }
    }

    fun getFreeTrial(languageTerms: LanguageTerms = LanguageManager.getDefaultLanguageTerms()): String? =
        getFreeTrialCount()?.let { count -> getFreeTrialPer(languageTerms)?.let { per -> "$count $per" } }
}

private val regex = Regex("[0-9]{1,}", RegexOption.IGNORE_CASE)
private fun GetDayFromFreeTrialISO8601Code(ISO8601Code: String): Int {
    if (ISO8601Code == "0") return 0

    try {
        val value = regex.find(ISO8601Code)?.value?.toInt() ?: return 0
        val last = ISO8601Code.last()
        if (last == 'W') return 7 * value
        else if (last == 'D') return value
        return 0
    } catch (e: NumberFormatException) {
        return 0
    }
}

enum class MerchantAccountId(val raw: String) {
    DEFAULT(""),
    EUR("EUR"),
    USD("KidjoInc_instant"),
    CHF("CHF");

    companion object {
        fun FromRaw(raw: String): MerchantAccountId {
            return when(raw) {
                DEFAULT.raw -> DEFAULT
                EUR.raw -> EUR
                USD.raw ->  USD
                CHF.raw -> CHF
                else -> DEFAULT
            }
        }
    }
}

enum class InAppPurchaseType(val raw: String) {
    KIDJO_TV_MONTHLY("kidjo_server_plan_5_monthly_7f"),
    KIDJO_TV_YEARLY("kidjo_server_plan_3_12months_7f"),
    KIDJO_TV_MONTHLY_DISCOUNT("kidjo_server_plan_discount_4_monthly_7f"),
    KIDJO_TV_YEARLY_DISCOUNT("kidjo_server_plan_discount_10_12months_7f"),

    KIDJO_BOOKS_MONTHLY("kidjo_books_server_plan_6_monthly_7f"),
    KIDJO_BOOKS_YEARLY("kidjo_books_server_plan_8_12months_7f"),
    KIDJO_BOOKS_MONTHLY_DISCOUNT("kidjo_books_server_plan_discount_7_monthly_7f"),
    KIDJO_BOOKS_YEARLY_DISCOUNT("kidjo_books_server_plan_discount_9_12months_7f"),

    KIDJO_GAMES_MONTHLY("kidjo_games_server_plan_6_monthly_7f"),
    KIDJO_GAMES_YEARLY("kidjo_games_server_plan_8_12months_7f"),
    KIDJO_GAMES_MONTHLY_DISCOUNT("kidjo_games_server_plan_discount_7_monthly_7f"),
    KIDJO_GAMES_YEARLY_DISCOUNT("kidjo_games_server_plan_discount_9_12months_7f"),

    SWISS_KIDJO_TV_MONTHLY("swiss_kidjo_server_plan_5_monthly_7f"),
    SWISS_KIDJO_TV_YEARLY("swiss_kidjo_server_plan_3_12months_7f"),
    SWISS_KIDJO_TV_MONTHLY_DISCOUNT("swiss_kidjo_server_plan_discount_4_monthly_7f"),
    SWISS_KIDJO_TV_YEARLY_DISCOUNT("swiss_kidjo_server_plan_discount_10_12months_7f"),

    SWISS_KIDJO_BOOKS_MONTHLY("swiss_kidjo_books_server_plan_6_monthly_7f"),
    SWISS_KIDJO_BOOKS_YEARLY("swiss_kidjo_books_server_plan_8_12months_7f"),
    SWISS_KIDJO_BOOKS_MONTHLY_DISCOUNT("swiss_kidjo_books_server_plan_discount_7_monthly_7f"),
    SWISS_KIDJO_BOOKS_YEARLY_DISCOUNT("swiss_kidjo_books_server_plan_discount_9_12months_7f"),

    SWISS_KIDJO_GAMES_MONTHLY("swiss_kidjo_games_server_plan_6_monthly_7f"),
    SWISS_KIDJO_GAMES_YEARLY("swiss_kidjo_games_server_plan_8_12months_7f"),
    SWISS_KIDJO_GAMES_MONTHLY_DISCOUNT("swiss_kidjo_games_server_plan_discount_7_monthly_7f"),
    SWISS_KIDJO_GAMES_YEARLY_DISCOUNT("swiss_kidjo_games_server_plan_discount_9_12months_7f"),

    EUROPE_KIDJO_TV_MONTHLY("europe_kidjo_server_plan_5_monthly_7f"),
    EUROPE_KIDJO_TV_YEARLY("europe_kidjo_server_plan_3_12months_7f"),
    EUROPE_KIDJO_TV_MONTHLY_DISCOUNT("europe_kidjo_server_plan_discount_4_monthly_7f"),
    EUROPE_KIDJO_TV_YEARLY_DISCOUNT("europe_kidjo_server_plan_discount_10_12months_7f"),

    EUROPE_KIDJO_BOOKS_MONTHLY("europe_kidjo_books_server_plan_6_monthly_7f"),
    EUROPE_KIDJO_BOOKS_YEARLY("europe_kidjo_books_server_plan_8_12months_7f"),
    EUROPE_KIDJO_BOOKS_MONTHLY_DISCOUNT("europe_kidjo_books_server_plan_discount_7_monthly_7f"),
    EUROPE_KIDJO_BOOKS_YEARLY_DISCOUNT("europe_kidjo_books_server_plan_discount_9_12months_7f"),

    EUROPE_KIDJO_GAMES_MONTHLY("europe_kidjo_games_server_plan_6_monthly_7f"),
    EUROPE_KIDJO_GAMES_YEARLY("europe_kidjo_games_server_plan_8_12months_7f"),
    EUROPE_KIDJO_GAMES_MONTHLY_DISCOUNT("europe_kidjo_games_server_plan_discount_7_monthly_7f"),
    EUROPE_KIDJO_GAMES_YEARLY_DISCOUNT("europe_kidjo_games_server_plan_discount_9_12months_7f");
    companion object {
        fun FromRaw(raw: String): InAppPurchaseType {
            return when(raw) {
                KIDJO_TV_MONTHLY.raw -> KIDJO_TV_MONTHLY
                KIDJO_TV_YEARLY.raw -> KIDJO_TV_YEARLY
                KIDJO_TV_MONTHLY_DISCOUNT.raw -> KIDJO_TV_MONTHLY_DISCOUNT
                KIDJO_TV_YEARLY_DISCOUNT.raw -> KIDJO_TV_YEARLY_DISCOUNT

                KIDJO_BOOKS_MONTHLY.raw -> KIDJO_BOOKS_MONTHLY
                KIDJO_BOOKS_YEARLY.raw -> KIDJO_BOOKS_YEARLY
                KIDJO_BOOKS_MONTHLY_DISCOUNT.raw -> KIDJO_BOOKS_MONTHLY_DISCOUNT
                KIDJO_BOOKS_YEARLY_DISCOUNT.raw -> KIDJO_BOOKS_YEARLY_DISCOUNT

                KIDJO_GAMES_MONTHLY.raw -> KIDJO_GAMES_MONTHLY
                KIDJO_GAMES_YEARLY.raw -> KIDJO_GAMES_YEARLY
                KIDJO_GAMES_MONTHLY_DISCOUNT.raw -> KIDJO_GAMES_MONTHLY_DISCOUNT
                KIDJO_GAMES_YEARLY_DISCOUNT.raw -> KIDJO_GAMES_YEARLY_DISCOUNT

                SWISS_KIDJO_TV_MONTHLY.raw -> SWISS_KIDJO_TV_MONTHLY
                SWISS_KIDJO_TV_YEARLY.raw -> SWISS_KIDJO_TV_YEARLY
                SWISS_KIDJO_TV_MONTHLY_DISCOUNT.raw -> SWISS_KIDJO_TV_MONTHLY_DISCOUNT
                SWISS_KIDJO_TV_YEARLY_DISCOUNT.raw -> SWISS_KIDJO_TV_YEARLY_DISCOUNT

                SWISS_KIDJO_BOOKS_MONTHLY.raw -> SWISS_KIDJO_BOOKS_MONTHLY
                SWISS_KIDJO_BOOKS_YEARLY.raw -> SWISS_KIDJO_BOOKS_YEARLY
                SWISS_KIDJO_BOOKS_MONTHLY_DISCOUNT.raw -> SWISS_KIDJO_BOOKS_MONTHLY_DISCOUNT
                SWISS_KIDJO_BOOKS_YEARLY_DISCOUNT.raw -> SWISS_KIDJO_BOOKS_YEARLY_DISCOUNT

                SWISS_KIDJO_GAMES_MONTHLY.raw -> SWISS_KIDJO_GAMES_MONTHLY
                SWISS_KIDJO_GAMES_YEARLY.raw -> SWISS_KIDJO_GAMES_YEARLY
                SWISS_KIDJO_GAMES_MONTHLY_DISCOUNT.raw -> SWISS_KIDJO_GAMES_MONTHLY_DISCOUNT
                SWISS_KIDJO_GAMES_YEARLY_DISCOUNT.raw -> SWISS_KIDJO_GAMES_YEARLY_DISCOUNT

                EUROPE_KIDJO_TV_MONTHLY.raw -> EUROPE_KIDJO_TV_MONTHLY
                EUROPE_KIDJO_TV_YEARLY.raw -> EUROPE_KIDJO_TV_YEARLY
                EUROPE_KIDJO_TV_MONTHLY_DISCOUNT.raw -> EUROPE_KIDJO_TV_MONTHLY_DISCOUNT
                EUROPE_KIDJO_TV_YEARLY_DISCOUNT.raw -> EUROPE_KIDJO_TV_YEARLY_DISCOUNT

                EUROPE_KIDJO_BOOKS_MONTHLY.raw -> EUROPE_KIDJO_BOOKS_MONTHLY
                EUROPE_KIDJO_BOOKS_YEARLY.raw -> EUROPE_KIDJO_BOOKS_YEARLY
                EUROPE_KIDJO_BOOKS_MONTHLY_DISCOUNT.raw -> EUROPE_KIDJO_BOOKS_MONTHLY_DISCOUNT
                EUROPE_KIDJO_BOOKS_YEARLY_DISCOUNT.raw -> EUROPE_KIDJO_BOOKS_YEARLY_DISCOUNT

                EUROPE_KIDJO_GAMES_MONTHLY.raw -> EUROPE_KIDJO_GAMES_MONTHLY
                EUROPE_KIDJO_GAMES_YEARLY.raw -> EUROPE_KIDJO_GAMES_YEARLY
                EUROPE_KIDJO_GAMES_MONTHLY_DISCOUNT.raw -> EUROPE_KIDJO_GAMES_MONTHLY_DISCOUNT
                EUROPE_KIDJO_GAMES_YEARLY_DISCOUNT.raw -> EUROPE_KIDJO_GAMES_YEARLY_DISCOUNT

                else -> KIDJO_TV_MONTHLY
            }
        }
    }
}
