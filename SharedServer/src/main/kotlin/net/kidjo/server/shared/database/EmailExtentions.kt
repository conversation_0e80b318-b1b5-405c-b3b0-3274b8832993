package net.kidjo.server.shared.database
import java.sql.PreparedStatement

fun DatabaseController.newsletterSetEmail(email: String): <PERSON><PERSON>an {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO newsletters (email) VALUES (?)", PreparedStatement.RETURN_GENERATED_KEYS)
    statement.setString(1, email)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()

    return results
}
fun DatabaseController.newsletterGetByEmail(email: String): String? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT email FROM newsletters where email = ?")
    statement.setString(1, email)
    val results = statement.executeQuery()

    var email: String? = null
    if (results.next()) email = results.getString("email")
    else email

    statement.close()
    connection.close()
    return email
}