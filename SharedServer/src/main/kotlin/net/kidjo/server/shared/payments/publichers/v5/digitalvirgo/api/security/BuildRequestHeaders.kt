package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.security

import okhttp3.Request

private const val CONTENT_TYPE_JSON = "json"
const val CONTENT_TYPE_URLENCODED = "x-www-form-urlencoded"
fun buildRequestHeaders(url: String, token: String, contentType: String = CONTENT_TYPE_JSON ): Request.Builder =
    Request.Builder().url(url)
        .header("Authorization", "$token")
        .header("Accept", "*/*")
        .header("Content-Type", "application/$contentType; charset=UTF-8")
        .header("UserAgent", "Kidjo-Api")