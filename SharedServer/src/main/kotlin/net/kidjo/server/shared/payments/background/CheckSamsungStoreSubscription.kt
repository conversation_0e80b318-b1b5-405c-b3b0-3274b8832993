package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.models.SamsungSubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.SubscriptionStatusResponse
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

fun PaymentManager.checkSamsungStoreSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val subscriptionStatusResponse: SubscriptionStatusResponse
    try {
        subscriptionStatusResponse = samsungApiManager.getSubscriptionStatus(subscriptionRoot.subscriptionToken)
    } catch (e: SamsungSubscriptionCheckException) {
        throw e
    }
    if (subscriptionStatusResponse == null) {
        throw SamsungSubscriptionCheckException("Could not find SAMSUNG subscription")
    }
    val iap =
        iapManager.getSamsungIAP(subscriptionRoot.iapId) ?: throw SubscriptionCheckException("Could not find the iap")
    val status: SubscriptionCheckResult.Status
    val priceInUSD: Float = iap.price.toFloat()

    val nextBillingDate =
        LocalDateTime.parse(
            subscriptionStatusResponse.subscriptionEndDate,
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        )
    val paymentState = subscriptionStatusResponse.currentPaymentPlan
    var subscriptionStatus = subscriptionStatusResponse.subscriptionStatus
    if (subscriptionStatus == "ACTIVE") {
        status = SubscriptionCheckResult.Status.ACTIVE
        updateStringBuilder.appendln(
            "UPDATE = SAMSUNG Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )

    } else if (subscriptionStatus == "CANCEL") {
        val cancelReason = subscriptionStatusResponse.cancelSubscriptionReason
        if (cancelReason == "1") status = SubscriptionCheckResult.Status.CANCEL_USER
        else if (cancelReason == "2") status = SubscriptionCheckResult.Status.CANCEL_ADMIN
        else if (cancelReason == "3") status = SubscriptionCheckResult.Status.CANCEL_FAILED_PAYMENT
        else status = SubscriptionCheckResult.Status.CANCEL_USER
        cancelStringBuilder.appendln(
            "CANCEL = SAMSUNG Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    } else {
        throw SamsungSubscriptionCheckException("Could not properly parse if the SAMSUNG store subscription is active")
    }
    return SubscriptionCheckResult(status, priceInUSD, nextBillingDate, paymentState)
}
