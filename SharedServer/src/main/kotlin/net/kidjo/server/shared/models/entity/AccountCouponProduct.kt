package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object AccountCouponProduct : Table("account_coupon_product") {
    val id = long("id").autoIncrement()
    val name = varchar("name", 50)
    val description = varchar("description", 30)
    val updated_at = datetime("updated_at")
    val created_at = datetime("created_at")
    override val primaryKey = PrimaryKey(id)
}
