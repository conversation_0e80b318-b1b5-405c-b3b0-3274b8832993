package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.request.*
import net.kidjo.server.shared.payments.publichers.v5.enums.OrangeOperators
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetOperationInfoDTO (
    var code    : Int?    = null,
    var message : String? = null,
    var data    : DVGetOperationInfoDTOData?   = DVGetOperationInfoDTOData()
){
    fun getMCCMNC() = data?.user?.mccmnc
    fun getOperatorName() = getOperatorName(data?.user?.mccmnc, data?.country)
    fun getSubIndent() = getSubIndent(
        data?.operationId,
        data?.subscription?.subscriptionId
    )
    fun getUserIndent() =
        Companion.getUserIndent(
            data?.user?.msisdn,
            data?.user?.alias
            //data?.user?.aliasGsm
        )
    fun  getNextBillingDate() = getNextDate(
        data?.subscription?.nextInvoiceDate,
        data?.subscription?.nextCycleDate,
        data?.subscription?.suspensionDate,
        data?.subscription?.expirationDate,
        data?.subscription?.cancellationDate
    )
    companion object{
        private fun getNextDate(nextInvoiceDate: String?,
                                nextCycleDate: String?,
                                suspensionDate: String?,
                                expirationDate: String?,
                                cancellationDate : String?
        ): LocalDateTime {
            if (!nextInvoiceDate.isNullOrBlank()) {
                return stringToLocalDateTime(nextInvoiceDate)
            }
            if (!nextCycleDate.isNullOrBlank()) {
                return stringToLocalDateTime(nextCycleDate)
            }
            if (!suspensionDate.isNullOrBlank()) {
                return stringToLocalDateTime(suspensionDate)
            }
            if (!expirationDate.isNullOrBlank()) {
                return stringToLocalDateTime(expirationDate)
            }
            if (!cancellationDate.isNullOrBlank()) {
                return stringToLocalDateTime(cancellationDate)
            }
            return LocalDateTime.now()
        }
        private fun getOperatorName(mccmnc: Int?, countryCode: String?): String {
            if (mccmnc != null && mccmnc != 0) {
                return OrangeOperators.getByMCCMNC(mccmnc).name
            }
            if (!countryCode.isNullOrBlank()) {
                return OrangeOperators.getByCountryShort(countryCode).name
            }
            return OrangeOperators.NONE.name
        }
        private fun stringToLocalDateTime(date: String?): LocalDateTime {
            return ZonedDateTime.parse(date, DateTimeFormatter.ISO_DATE_TIME)
                .toLocalDateTime()
        }
        private fun getSubIndent(operationId: String?, subscriptionId: String? ): String? {
            if (!operationId.isNullOrBlank()) {
                return operationId
            }
            if (!subscriptionId.isNullOrBlank()) {
                return subscriptionId
            }
            return null
        }
        private fun getUserIndent(msisdn: String?, alias: String?): String? {
            if (!msisdn.isNullOrBlank()) {
                return msisdn
            }
            if (!alias.isNullOrBlank()) {
                return alias
            }
            /*if (!aliasGsm.isNullOrBlank()) {
                return aliasGsm
            }*/
            return null
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetOperationInfoDTOData (
    var status              : String? = null,
    var type                : String? = null,
    var country             : String? = null,
    var operationId         : String? = null,
    var correlationId       : String? = null,
    var creationDate        : String? = null,
    var date                : String? = null,
    var subscription        : DVGetOperationInfoSubscription? = DVGetOperationInfoSubscription(),
    var user                : DVGetOperationInfoUser?  = DVGetOperationInfoUser(),
    @JsonProperty("package")
    var dvPackage           : DVGetOperationInfoPackage? = DVGetOperationInfoPackage(),
    var offer               : DVGetOperationInfoOffer? = DVGetOperationInfoOffer()
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetOperationInfoSubscription (
    var subscriptionId      : String? = null,
    var status              : String? = null,
    var country             : String? = null,
    var subscriptionDate    : String? = null,
    var cancellationDate    : String? = null,
    var cancellationChannel : String? = null,
    var expirationDate      : String? = null,
    var suspensionDate      : String? = null,
    var terminationDate     : String? = null,
    var premiumStartDate    : String? = null,
    var nextCycleDate       : String? = null,
    var lastCycleDate       : String? = null,
    var lastInvoiceDate     : String? = null,
    var nextInvoiceDate     : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetOperationInfoUser (
    var msisdn    : String? = null,
    var alias   : String? = null,
    //var aliasGsm  : String? = null,
    var ip        : String? = null,
    var mccmnc    : Int?    = null,
    var userAgent : String? = null,
    var locale    : String? = null
)

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetOperationInfoOffer (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null
){
    fun getLongId() = getId(id)
    companion object{
        private fun getId(id: Int?):  Long? {
            return try {
                id?.toLong()
            } catch (e: Exception) {
                null
            }
        }
    }
}
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVGetOperationInfoPackage (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null

) {
    fun getLongId() = getId(id)
    companion object{
        private fun getId(id: Int?): Long? {
            return try {
                id?.toLong()
            } catch (e: Exception) {
                null
            }
        }
    }
}
