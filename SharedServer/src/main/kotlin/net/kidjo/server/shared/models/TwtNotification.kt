package net.kidjo.server.shared.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import kotlinx.serialization.Serializable

@Serializable
@JsonIgnoreProperties(ignoreUnknown = true)
data class TwtNotification(
    @JsonProperty("customerId")
    val customerId: Int = 0,

    @JsonProperty("countryISO")
    val countryISO: String? = "",

    @JsonProperty("notifType")
    var notifType: String? = "",

    @JsonProperty("package_details")
    var packageDetails: String? = ""
)
