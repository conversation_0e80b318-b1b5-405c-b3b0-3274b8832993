package net.kidjo.server.shared.tools

import io.ktor.http.*
import io.ktor.server.application.ApplicationCall
import io.ktor.util.date.GMTDate
import io.ktor.util.date.plus
import net.kidjo.server.shared.cachedatabase.CacheDatabase
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.accountCoupon_get
import net.kidjo.server.shared.database.accountCoupon_getByCouponId
import net.kidjo.server.shared.models.AccountCoupon

private const val COOKIE_KEY_FOR_ID = "kCID"
private const val COOKIE_DURATION = 60 * 60 * 12 // 12 hours

class CouponManager(
    private val config: Config,
    private val databaseController: DatabaseController,
    cacheDatabase: CacheDatabase
) {

    fun getFromDatabase(couponId: String): AccountCoupon? {
        if (couponId == "") return null
        return databaseController.accountCoupon_getByCouponId(couponId)
    }

    fun getFromDatabase(id: Long): AccountCoupon? {
        if (id < 1L) return null
        return databaseController.accountCoupon_get(id)
    }

    fun removeFromCall(call: ApplicationCall) {
        call.response.cookies.appendExpired(COOKIE_KEY_FOR_ID, null, "/")
        call.response.cookies.appendExpired(COOKIE_KEY_FOR_ID, "kidjo.tv", "/")
    }

    fun setToCall(accountCoupon: AccountCoupon, call: ApplicationCall) {
        val temporal = GMTDate().plus(COOKIE_DURATION.toLong() * 1000)
        call.response.cookies.append(
            Cookie(
            COOKIE_KEY_FOR_ID,
            accountCoupon.couponId,
            CookieEncoding.URI_ENCODING,
            COOKIE_DURATION,
            temporal,
            "kidjo.tv",
            "/",
            config.cookies_secureUserSessionCookies,
            true
        ))
    }

    fun getFromCall(call: ApplicationCall): AccountCoupon? {
        val stringId = call.request.cookies[COOKIE_KEY_FOR_ID] ?: return null
        val coupon = getFromDatabase(stringId)
        if (coupon != null && !coupon.isValid()) {
            removeFromCall(call)
            return null
        }
        return coupon
    }

    fun validateCoupon(couponId: String): Boolean {
        val coupon = getFromDatabase(couponId)
        if (coupon != null) {
            if (!coupon.isValid())
                return false
        } else {
            return false
        }
        return true
    }

    fun idIsInCall(call: ApplicationCall): Boolean {
        return call.request.cookies[COOKIE_KEY_FOR_ID] != null
    }
}
