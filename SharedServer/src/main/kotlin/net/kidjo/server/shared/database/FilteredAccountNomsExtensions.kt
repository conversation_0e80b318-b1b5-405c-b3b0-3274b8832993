package net.kidjo.server.shared.database

import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import net.kidjo.common.models.UserReportType
import net.kidjo.server.shared.database.creator.*
import net.kidjo.server.shared.models.*
import java.sql.ResultSet

fun DatabaseController.filteredSearchAccount(
    fromDate: String? = null,
    toDate: String? = null,
    countryCode: String? = null,
    subMethod: String? = null,
    subStatus: String? = null,
    couponType: String? = null,
    productType: String? = null,
    partnerId: Long? = null,
    promoContent: Int? = null,
    reportType: String? = null
): FilteredSearchAccountModel? {



    return dataSource.connection.use { connection ->
        val filters = mutableListOf<String>()
        val params = mutableListOf<Any>()

        val dateFilter = when(reportType){
            UserReportType.ACTIVE_USERS.value -> "nextBillDate"
            else -> "createdDate"
        }

        if (!fromDate.isNullOrBlank() && !toDate.isNullOrBlank()) {
            filters.add("AND (? <= $dateFilter AND ? >= $dateFilter)")
            params.add(fromDate)
            params.add(toDate)
        }

//        if (!countryCode.isNullOrBlank()) {
//            filters.add("AND ((s.storeId = 'mondia') AND c.id = (SELECT id FROM countries WHERE short = ? LIMIT 1))")
//            filters.add("OR u.country_id = (SELECT id FROM countries WHERE short = ? LIMIT 1)")
//            params.add(countryCode)
//            params.add(countryCode)
//        }

        if (!subMethod.isNullOrBlank()) {
            filters.add("AND storeId = ?")
            params.add(subMethod)
        }

        if (!subStatus.isNullOrBlank()) {
            val statusCondition = when (SubscriptionRoot.Status.fromName(subStatus)) {
                true -> "AND ( nextBillDate >= CURRENT_DATE() AND subStatus = TRUE)"
                false -> "AND ( nextBillDate < CURRENT_DATE() OR subStatus = FALSE)"
                else -> ""
            }
            if (statusCondition.isNotEmpty()) filters.add(statusCondition)
        }

        if (!productType.isNullOrBlank()) {
            filters.add("AND subscriptionType = ?")
            params.add(productType)
        }

        if (!couponType.isNullOrBlank()) {
            filters.add("AND coupon_type_id = ?")
            params.add(couponType)
        }

        if (partnerId != null && partnerId > 0) {
            filters.add("AND id_partner = ?")
            params.add(partnerId)
        }

        if (promoContent != null && promoContent > 0) {
            filters.add("AND is_promo_accepted = ?")
            params.add(promoContent)
        }
        if (!filters.isNullOrEmpty())
            filters[0] = filters[0].replaceFirst("AND", "WHERE", true)

        val querySubStatus = """
            SELECT DISTINCT
                subStatus,
                'subStatus' AS `key`,
                subStatus AS value
            FROM kidjo.subscriptions_report_materialized_view
            ${filters.joinToString(" ")}
        """.trimIndent()

                val queryStoreId = """
            SELECT DISTINCT
                storeId,
                'storeId' AS `key`, 
                storeId AS value
            FROM kidjo.subscriptions_report_materialized_view
            ${filters.joinToString(" ")}
        """.trimIndent()

                val querySubscriptionType = """
            SELECT DISTINCT
                subscriptionType,
                'subscriptionType' AS `key`,
                subscriptionType AS value
            FROM kidjo.subscriptions_report_materialized_view
            ${filters.joinToString(" ")}
        """.trimIndent()

                val queryCountry = """
            SELECT DISTINCT
                country_id,
                'country' AS `key`,
                CONCAT(country_id, '|||', country_code, '|||', country) AS value
            FROM kidjo.subscriptions_report_materialized_view
            ${filters.joinToString(" ")}
        """.trimIndent()

                val queryPartner = """
            SELECT DISTINCT
                id_partner,
                'partner' AS `key`,
                CONCAT(id_partner, '|||', partner, '|||', partner_description COLLATE utf8_general_ci) AS value
            FROM kidjo.subscriptions_report_materialized_view
            ${filters.joinToString(" ")}
        """.trimIndent()

                val queryCoupon = """
            SELECT DISTINCT
                coupon_type_id,
                'coupon' AS `key`,
                CONCAT(coupon_type_id, '|||', type, '|||', coupon_type_description) AS value
            FROM kidjo.subscriptions_report_materialized_view
            ${filters.joinToString(" ")}
        """.trimIndent()

        val storeIdStmt = connection.prepareStatement(queryStoreId)
        val couponStmt = connection.prepareStatement(queryCoupon)
        val countryStmt = connection.prepareStatement(queryCountry)
        val subStatusStmt = connection.prepareStatement(querySubStatus)
        val subTypeStmt = connection.prepareStatement(querySubscriptionType)
        val partnerStmt = connection.prepareStatement(queryPartner)

        for (it in 0..5) {
            params.forEachIndexed { index, param ->
                storeIdStmt.setObject(index + 1, param)
                couponStmt.setObject(index + 1, param)
                countryStmt.setObject(index + 1, param)
                subTypeStmt.setObject(index + 1, param)
                subStatusStmt.setObject(index + 1, param)
                partnerStmt.setObject(index + 1, param)
            }
        }
        var storeIds: ResultSet
        var coupons: ResultSet
        var countries: ResultSet
        var subTypes: ResultSet
        var subStatuses: ResultSet
        var partners: ResultSet
        runBlocking {
            val getStoreIds = async { storeIdStmt.executeQuery() }
            val getCoupons = async { couponStmt.executeQuery() }
            val getCountries = async { countryStmt.executeQuery() }
            val getSubTypes = async { subTypeStmt.executeQuery() }
            val getSubStatuses = async { subStatusStmt.executeQuery() }
            val getPartners = async { partnerStmt.executeQuery() }
            val list = awaitAll(getStoreIds, getCoupons, getCountries, getSubTypes, getSubStatuses, getPartners)

            storeIds = list[0]
            coupons = list[1]
            countries = list[2]
            subTypes = list[3]
            subStatuses = list[4]
            partners = list[5]
        }
        var accountNomIds: FilteredSearchAccountModel?
        accountNomIds = objectCreator.toFilteredSearchAccountMapper(storeIds, coupons, countries, subTypes, subStatuses, partners)
        accountNomIds.reportTypes = UserReportType.entries.map { ReportTypeModel(it.value, it.display) }.toList()
        return accountNomIds
    }
}
