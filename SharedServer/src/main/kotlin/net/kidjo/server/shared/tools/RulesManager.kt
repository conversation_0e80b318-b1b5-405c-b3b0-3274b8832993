package net.kidjo.server.shared.tools

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.IAPRule
import net.kidjo.server.shared.models.Promotion

class RulesManager(
    config: Config,
    iapManager: IAPManager,
    databaseController: DatabaseController
) {


    //wrap with getter method incase this changes in the future
    private val iosReviewRule: IAPRule
    private val backUpIOSRule: IAPRule
    private val backUpPlayStoreRule: IAPRule
    private val backUpOrangeRule: IAPRule

    private val amazonDefaultRule: IAPRule
    private val amazonNoFreeTrialRule: IAPRule

    private val samsungDefaultMCRule: IAPRule
    private val samsungDefaultROWRule: IAPRule

    private val huaweiDefaultMCRule: IAPRule
    private val huaweiDefaultROWRule: IAPRule

    private val jioDefaultMCRule: IAPRule
    private val jioDefaultROWRule: IAPRule

    private val kidjoDefaultRuleMC: IAPRule
    private val kidjoDefaultRuleROW: IAPRule

    private val swisscomDefaultRule: IAPRule

    private val backUpAndroidTvRule:IAPRule


    init {
        val reviewIAP = iapManager.defaultIOSIAP
        iosReviewRule =
            IAPRule(-1, "ios_review", "ios_review", IAPRule.ViewId.VIEW_NEW, reviewIAP.id, 0, true, true, false)

        val backupIOSRuleIap = iapManager.defaultIOSIAP
        backUpIOSRule =
            IAPRule(-1, "ios_backup", "ios_backup", IAPRule.ViewId.VIEW_NEW, backupIOSRuleIap.id, 0, true, true, false)

        val backUpPlayStoreRuleIap = iapManager.defaultOrangeIAP
        backUpPlayStoreRule = IAPRule(
            -1,
            "playstore_backup",
            "playstore_backup",
            IAPRule.ViewId.VIEW_NEW,
            backUpPlayStoreRuleIap.id,
            0,
            true,
            true,
            false
        )

        val backUpOrangeRuleIap = iapManager.defaultPlayStoreIAP
        backUpOrangeRule = IAPRule(
            -1,
            "orange_backup",
            "orange_backup",
            IAPRule.ViewId.VIEW_NEW,
            backUpOrangeRuleIap.id,
            0,
            true,
            true,
            false
        )

        val amazonDefaultIAP = iapManager.defaultAmazonIAP
        val defaultAmazonIAPNoFreeTrail = iapManager.defaultAmazonIAPNoFreeTrail
        amazonDefaultRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.AMAZON, true),
            "amazon_month_5_7",
            IAPRule.ViewId.VIEW_NEW,
            amazonDefaultIAP.id,
            0,
            true,
            true,
            false
        )
        amazonNoFreeTrialRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.AMAZON, true),
            "amazon_month_5_0",
            IAPRule.ViewId.VIEW_NEW,
            defaultAmazonIAPNoFreeTrail.id,
            0,
            false,
            true,
            false
        )

        val defaultHuaweitMCRule = iapManager.defaultHuaweiIAPMC
        val defaultHuaweiROWRule = iapManager.defaultHuaweiIAPROW
        huaweiDefaultMCRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.HUAWEI, true),
            "huawei_month_30_4",
            IAPRule.ViewId.VIEW_NEW,
            defaultHuaweitMCRule.id,
            0,
            true,
            true,
            false
        )
        huaweiDefaultROWRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.HUAWEI, false),
            "huawei_month_30_2",
            IAPRule.ViewId.VIEW_NEW,
            defaultHuaweiROWRule.id,
            0,
            true,
            true,
            false
        )


        val defaultSamsungIAPMC = iapManager.defaultSamsungIAPMC
        val defaultSamsungIAPROW = iapManager.defaultSamsungIAPROW
        samsungDefaultMCRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.SAMSUNG, true),
            "samsung_month_30_4",
            IAPRule.ViewId.VIEW_NEW,
            defaultSamsungIAPMC.id,
            0,
            true,
            true,
            false
        )
        samsungDefaultROWRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.SAMSUNG, false),
            "samsung_month_30_2",
            IAPRule.ViewId.VIEW_NEW,
            defaultSamsungIAPROW.id,
            0,
            true,
            true,
            false
        )


        val defaultKidjoIAPMC = iapManager.defaultKidjoBrainTreeIapMC
        val defaultKidjoIAPROW = iapManager.defaultKidjoBrainTreeIapMC
        kidjoDefaultRuleMC = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.KIDJO_BRAINTREE, true),
            "test",
            IAPRule.ViewId.VIEW_NEW,
            defaultKidjoIAPMC.id,
            0,
            true,
            true,
            false
        )
        kidjoDefaultRuleROW = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.KIDJO_BRAINTREE, false),
            "test",
            IAPRule.ViewId.VIEW_NEW,
            defaultKidjoIAPROW.id,
            0,
            true,
            true,
            false
        )

        swisscomDefaultRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.SWISSCOM, true),
            "swisscom",
            IAPRule.ViewId.VIEW_NEW,
            iapManager.defaultSwisscomIap.id,
            0,
            false,
            true,
            false
        )

        val defaultJioMCRule = iapManager.defaultJioIAPMC
        val defaultJioROWRule = iapManager.defaultJioIAPROW
        jioDefaultMCRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.JIO, true),
            "jio_month_30_4",
            IAPRule.ViewId.VIEW_NEW,
            defaultJioMCRule.id,
            0,
            true,
            true,
            false
        )
        jioDefaultROWRule = IAPRule(
            -1,
            getGroupId(Device.StorePlatform.JIO, false),
            "jio_month_30_2",
            IAPRule.ViewId.VIEW_NEW,
            defaultJioROWRule.id,
            0,
            true,
            true,
            false
        )

        val backUpAndroidTvRuleIap = iapManager.defaultAndroidTvIAP
        backUpAndroidTvRule = IAPRule(
            -1,
            "android_tv_backup",
            "android_tv_backup",
            IAPRule.ViewId.VIEW_NEW,
            backUpAndroidTvRuleIap.id,
            0,
            true,
            true,
            false
        )
    }



    fun getIOSReviewRule(inAppId: String? = null): IAPRule = inAppId?.let {
        IAPRule(-1, "ios_review", "ios_review", IAPRule.ViewId.VIEW_NEW, inAppId, 0, true, true, false)
    } ?: iosReviewRule

    fun getDefaultAmazonRule(shouldAllowFreeTrial: Boolean): IAPRule {
        return if (shouldAllowFreeTrial) amazonDefaultRule
        else amazonNoFreeTrialRule
    }

    fun getDefaultSamsungRule(isMainCountry: Boolean): IAPRule {
        return if (isMainCountry) samsungDefaultMCRule
        else samsungDefaultROWRule
    }

    fun getDefaultHuaweiRule(isMainCountry: Boolean): IAPRule {
        return if (isMainCountry) huaweiDefaultMCRule
        else huaweiDefaultROWRule
    }

    fun getDefaultJioRule(isMainCountry: Boolean): IAPRule {
        return if (isMainCountry) jioDefaultMCRule
        else jioDefaultROWRule
    }

    fun getDefaultKidjoRule(isMainCountry: Boolean): IAPRule {
        return if (isMainCountry) kidjoDefaultRuleMC else kidjoDefaultRuleROW
    }

    fun getGroupId(platform: Device.StorePlatform, isMainCountry: Boolean): String {
        if (platform == Device.StorePlatform.AMAZON) {
            return "amazon"
        } else if (platform == Device.StorePlatform.SAMSUNG) {
            return if (isMainCountry) "samsung_mc" else "samsung_row"
        }

        return platform.raw + if (isMainCountry) "_mc" else "_row"
    }

    fun getPromoRule(platform: Device.StorePlatform, promotion: Promotion): IAPRule? {
        val isPlayStore = platform == Device.StorePlatform.PLAYSTORE

        // Promos are only for the playstore or ios
        if (!(platform == Device.StorePlatform.IOS || isPlayStore)) return null

        val iapIdToUse = if (isPlayStore) promotion.playstore_iap else promotion.ios_iap
        return IAPRule(
            -1,
            "promo_${platform.raw}",
            "promo_${promotion.deepLinkId}",
            promotion.viewId,
            iapIdToUse,
            0,
            true,
            true,
            false
        )
    }

    fun getGenericRule(
        platform: Device.StorePlatform,
        build: Int,
        allowFreeTrial: Boolean,
        isMainCountry: Boolean
    ): IAPRule {
        return when (platform) {
            Device.StorePlatform.ANDROID_TV -> backUpAndroidTvRule
            Device.StorePlatform.IOS -> backUpIOSRule
            Device.StorePlatform.PLAYSTORE -> backUpPlayStoreRule
            Device.StorePlatform.ORANGE -> backUpOrangeRule
            Device.StorePlatform.SAMSUNG -> getDefaultSamsungRule(isMainCountry)
            Device.StorePlatform.AMAZON -> getDefaultAmazonRule(allowFreeTrial)
            Device.StorePlatform.HUAWEI -> getDefaultHuaweiRule(isMainCountry)
            Device.StorePlatform.KIDJO_BRAINTREE -> getDefaultKidjoRule(isMainCountry)
            Device.StorePlatform.JIO -> getDefaultAmazonRule(allowFreeTrial)
            Device.StorePlatform.MONDIA_MEDIA -> throw Exception("MONDIA MEDIA does not have a Rule")
            Device.StorePlatform.DOCOMO -> throw Exception("DOCOMO MEDIA does not have a Rule")
            Device.StorePlatform.VIRGO -> throw Exception("VIRGO MEDIA does not have a Rule")
            Device.StorePlatform.TWT -> throw Exception("TWT MEDIA does not have a Rule")
            Device.StorePlatform.FREE_ACCESS_COUPON -> throw Exception("FREE_ACCSSES_COUPON does not have a Rule")
            Device.StorePlatform.SWISSCOM -> swisscomDefaultRule
            Device.StorePlatform.CAFEYN -> throw Exception("CAFEYN MEDIA does not have a Rule")
            else -> throw Exception("Device StorePlatform is required ")
        }
    }


}
