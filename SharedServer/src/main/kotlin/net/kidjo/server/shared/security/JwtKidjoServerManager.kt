package net.kidjo.server.shared.security

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import java.util.*

object JwtKidjoServerManager {

    private val jwtIssuer = "kidjoServer"
    private val jwtSecret = "zAP5MBA4B4Ijz0MZaS48"
    private val algorithm = Algorithm.HMAC512(jwtSecret)
    private val validityInMs = 31556952000 // one year //3_600_000 * 24 // 24 hours

    val verifier: JWTVerifier = JWT
            .require(algorithm)
            .withIssuer(jwtIssuer)
            .build()

    fun generateAccessToken(id: String): String = JWT.create()
            .withSubject("Authentication")
            .withIssuer(jwtIssuer)
            .withClaim("id", id.toInt())
            .withExpiresAt(expiresAt())
            .sign(algorithm)

    private fun expiresAt() = Date(System.currentTimeMillis() + validityInMs)

}