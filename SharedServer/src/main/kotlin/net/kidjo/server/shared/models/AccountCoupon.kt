package net.kidjo.server.shared.models

import java.time.LocalDateTime

data class AccountCoupon(
    val id: Long,
    val groupId: String,
    val couponId: String,
    var startDate: LocalDateTime,
    var expireDate: LocalDateTime,
    val durationCode: String,
    val redeemedTimes: Int,
    val redeemAmount: Int,
    val couponType: String,
    var productType: String,
    val partnerType: String?,
    val creationDate: LocalDateTime,
) {

    companion object {
        const val NO_SERVER_ID = 0L
    }

    val freeTrialValue: Int = "^P(\\d)M\$".toRegex().find(durationCode)?.groups?.get(1)?.value?.toIntOrNull() ?: 6
    val freeTrialIsInMonths: Boolean = true //todo
    val freeTrialSymbol: String = "M"

    fun isValid(): Boolean {
        if (redeemedTimes >= redeemAmount) return false
        if (LocalDateTime.now().isBefore(startDate)) return false
        if (LocalDateTime.now().isAfter(expireDate)) return false
        return true
    }

    enum class Status(val raw: String) {
        PENDING_ACTIVATION("Pending"),
        ACTIVE("Active"),
        EXPIRED("Expired"),
        INVALID("Invalid")
        ;

        companion object {
            fun fromRaw(raw: String): Status {
                return when (raw.trim().uppercase().capitalize()) {
                    PENDING_ACTIVATION.name -> PENDING_ACTIVATION
                    ACTIVE.name -> ACTIVE
                    EXPIRED.name -> EXPIRED
                    INVALID.name -> INVALID
                    else -> INVALID
                }
            }
        }
    }
}


data class AccountCouponPartnerModel(
    val id: Long,
    val name: String,
    val description: String,
)

data class AccountCouponProductModel(
    val id: Long,
    val name: String,
    val description: String,
)

data class AccountCouponTypeModel(
    val id: Long,
    val name: String,
    val description: String,
)

enum class AccountCouponType(val raw: String) {
    STANDARD_COUPON("1"),
    FREE_ACCESS_COUPON("2"),
    UNIQUE_COUPON("3"),
    UNIQUE_ACCESS_COUPON("4"),
    DISCOUNT_COUPON("5"),
    UNIQUE_DISCOUNT_COUPON("6");


    companion object {
        fun fromRaw(raw: String): AccountCouponType {
            return when (raw) {
                STANDARD_COUPON.raw -> STANDARD_COUPON
                FREE_ACCESS_COUPON.raw -> FREE_ACCESS_COUPON
                UNIQUE_COUPON.raw -> UNIQUE_COUPON
                UNIQUE_ACCESS_COUPON.raw -> UNIQUE_ACCESS_COUPON
                DISCOUNT_COUPON.raw -> DISCOUNT_COUPON
                UNIQUE_DISCOUNT_COUPON.raw -> UNIQUE_DISCOUNT_COUPON
                else -> STANDARD_COUPON
            }
        }

        fun fromName(name: String): AccountCouponType {
            return when (name) {
                STANDARD_COUPON.name -> STANDARD_COUPON
                FREE_ACCESS_COUPON.name -> FREE_ACCESS_COUPON
                UNIQUE_COUPON.name -> UNIQUE_COUPON
                UNIQUE_ACCESS_COUPON.name -> UNIQUE_ACCESS_COUPON
                DISCOUNT_COUPON.name -> DISCOUNT_COUPON
                UNIQUE_DISCOUNT_COUPON.name -> UNIQUE_DISCOUNT_COUPON
                else -> STANDARD_COUPON
            }
        }
    }
}

enum class AccountCouponProduct(val raw: String) {
    KIDJO_TV("1"),
    KIDJO_BOOKS("2"),
    KIDJO_TV_BOOKS("3"),
    KIDJO_GAMES("4"),
    KIDJO_TV_GAMES("5"),
    KIDJO_BOOKS_GAMES("6"),
    KIDJO_TV_BOOKS_GAMES("7")
    ;

    companion object {
        fun fromRaw(raw: String?): AccountCouponProduct {
            return when (raw) {
                KIDJO_TV.raw -> KIDJO_TV
                KIDJO_BOOKS.raw -> KIDJO_BOOKS
                KIDJO_TV_BOOKS.raw -> KIDJO_TV_BOOKS
                KIDJO_GAMES.raw -> KIDJO_GAMES
                KIDJO_TV_GAMES.raw -> KIDJO_TV_GAMES
                KIDJO_BOOKS_GAMES.raw -> KIDJO_BOOKS_GAMES
                KIDJO_TV_BOOKS_GAMES.raw -> KIDJO_TV_BOOKS_GAMES
                else -> KIDJO_TV
            }
        }

        fun fromName(name: String): AccountCouponProduct {
            return when (name) {
                KIDJO_TV.name -> KIDJO_TV
                KIDJO_BOOKS.name -> KIDJO_BOOKS
                KIDJO_TV_BOOKS.name -> KIDJO_TV_BOOKS
                KIDJO_GAMES.name -> KIDJO_GAMES
                KIDJO_TV_GAMES.name -> KIDJO_TV_GAMES
                KIDJO_BOOKS_GAMES.name -> KIDJO_BOOKS_GAMES
                KIDJO_TV_BOOKS_GAMES.name -> KIDJO_TV_BOOKS_GAMES
                else -> KIDJO_TV
            }
        }
    }
}

data class CsvReportAccountCoupon(
    val groupId: String,
    val couponId: String,
    var expireDate: LocalDateTime,
    val durationCode: String,
    val redeemedTimes: String,
    val couponType: String,
    var productType: String,
    val partnerType: String,
)

data class CouponBatchDetails(
    val name: String,
    var expireDate: String,
    val duration: String,
    val type: String,
    var product: String,
    val partner: String,
)
