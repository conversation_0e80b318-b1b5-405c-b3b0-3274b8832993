package net.kidjo.server.shared.models

import java.sql.ResultSet

class IAPRule {
    val id: Long
    val groupId: String
    val label: String
    val viewId: IAPRule.ViewId
    val iapKey: String
    val chance: Int
    val allowsFreeTrial: Boolean
    val isDefault: Boolean
    val ruleIsDebug: Boolean

                constructor(resultSet: ResultSet, isRuleDebug: Boolean = false) {
            id = resultSet.getLong("id")
            groupId = resultSet.getString("groupId")
            label = resultSet.getString("label")
            val viewKey = resultSet.getString("viewId")
            viewId = IAPRule.ViewId.FromRaw(viewKey)
            iapKey = resultSet.getString("iap")
            chance = resultSet.getInt("chance")
            allowsFreeTrial = resultSet.getBoolean("allowFreeTrial")
        isDefault = resultSet.getBoolean("isDefault")

        this.ruleIsDebug = isRuleDebug
    }
    constructor(id: Long, groupId: String, label: String, viewId: IAPRule.ViewId, iapKey: String, chance: Int, allowsFreeTrial: Boolean, isDefault: <PERSON>ole<PERSON>, ruleIsDebug: Boolean) {
        this.id = id
        this.groupId = groupId
        this.label = label
        this.viewId = viewId
        this.iapKey = iapKey
        this.chance = chance
        this.allowsFreeTrial = allowsFreeTrial
        this.isDefault = isDefault
        this.ruleIsDebug = ruleIsDebug
    }

    enum class ViewId(val raw: String) {
        IOS_REVIEW("ios_review"), VIEW_1("view_1"),  VIEW_2("view_2"),  VIEW_NEW("view_new"), VIEW_DISCOUNT_A_5("view_discount_a_5"),
        XMAS_DISCOUNT_A_50("xmas_discount_a_60"), PROMOTION("promotion");

        companion object {
            fun FromRaw(raw: String): ViewId {
                return when(raw) {
                    "ios_review" -> IOS_REVIEW
                    "view_1" -> VIEW_1
                    "view_2" -> VIEW_2
                    "view_discount_a_5" -> VIEW_DISCOUNT_A_5
                    "xmas_discount_a_60" -> XMAS_DISCOUNT_A_50
                    "promotion" -> PROMOTION
                    "view_new" -> VIEW_NEW
                    else -> VIEW_1
                }
            }
            fun GetCSSName(viewId: ViewId): String {
                return when (viewId) {
                    PROMOTION -> "promotion_style"
                    VIEW_DISCOUNT_A_5, XMAS_DISCOUNT_A_50 -> "discount_style"
                    VIEW_2 -> "style2"
                    VIEW_NEW -> "styleNew"
                    else -> "style"
                }
            }
        }
    }
}
