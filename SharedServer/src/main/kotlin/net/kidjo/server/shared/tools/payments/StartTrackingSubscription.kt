package net.kidjo.server.shared.tools.payments

import com.google.api.services.androidpublisher.model.SubscriptionPurchase
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.models.*
import java.time.LocalDateTime

fun PaymentManager.LEGACY_startTrackingPlatformSubscription(
    deviceId: Long, inAppPurchase: InAppPurchase,
    token: String, orderId: String, purchasingSession: String, attributionId: String
): Boolean {
    when (inAppPurchase.store) {
        Device.StorePlatform.PLAYSTORE -> return startTrackingPlatformSubscription(
            deviceId,
            inAppPurchase,
            token,
            orderId,
            purchasingSession,
            attributionId
        )
        else -> {
            val subscriptionRootInsert = SubscriptionRootInsert(
                User.NO_SERVER_ID,
                deviceId,
                inAppPurchase.noFreeTrial,
                inAppPurchase.price.toFloat(),
                SubscriptionRoot.PaymentType.NATIVE,
                SubscriptionRoot.SubscriptionType.KIDJO_TV,
                inAppPurchase.store.raw,
                orderId,
                inAppPurchase.store,
                null,
                inAppPurchase.id,
                purchasingSession,
                token,
                encryptionController.sha256Hash(token),
                0L,
                true,
                LocalDateTime.now(),
                !config.env.isLive
            )
            return startTrackingSubscription(subscriptionRootInsert, attributionId)
        }
    }

}

fun PaymentManager.startTrackingPlatformSubscription(
    deviceId: Long, inAppPurchase: InAppPurchase,
    token: String, orderId: String, purchasingSession: String, attributionId: String
): Boolean {
    val subscriptionRootInsert: SubscriptionRootInsert
    when (inAppPurchase.store) {
        Device.StorePlatform.PLAYSTORE -> {
            val subscriptionPurchase: SubscriptionPurchase
            try {
                subscriptionPurchase = playStoreApiManager.getSubscription(inAppPurchase.id, token, "'")
            } catch (e: Exception) {
                logger.error("cant find subscription of $inAppPurchase, token: $token")
                return false
            }
            val paymentStateId = subscriptionPurchase.paymentState
            if (!subscriptionPurchase.autoRenewing) return false

            val nextBillDate = subscriptionPurchase.expiryTimeMillis.epochMilliToLocalDateTime()
            val freeTrial: Boolean
            val priceToLog: Float

            if (paymentStateId != null && paymentStateId == 2) {
                freeTrial = true
                priceToLog = 0.0f
            } else {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }
            subscriptionRootInsert = SubscriptionRootInsert(
                User.NO_SERVER_ID, deviceId, freeTrial, priceToLog,
                SubscriptionRoot.PaymentType.NATIVE, SubscriptionRoot.SubscriptionType.KIDJO_TV,
                inAppPurchase.store.raw, orderId, inAppPurchase.store, paymentStateId.toString(),
                inAppPurchase.id, purchasingSession, token, encryptionController.sha256Hash(token), 0L,
                subscriptionPurchase.autoRenewing, nextBillDate, !config.env.isLive
            )
        }
        else -> {
            logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            return false
        }
    }

    return startTrackingSubscription(subscriptionRootInsert, attributionId)
}

internal fun PaymentManager.startTrackingSubscription(
    subscriptionRoot: SubscriptionRootInsert,
    attributionId: String
): Boolean {
    val id: Long
    val newSubscription: Boolean
    val checkSubscription =
        databaseController.subscription_getByHash(subscriptionRoot.storeId, subscriptionRoot.subscriptionTokenHash)
    if (checkSubscription != null) {
        id = checkSubscription.id
        newSubscription = false
    } else {
        id = databaseController.subscriptionRoot_create(subscriptionRoot)
        newSubscription = true
    }

    if (id == SubscriptionRoot.NO_ID) {
        logger.error("Error logging users subscription- $subscriptionRoot")
        return false
    }
    if (newSubscription) {
        val success = databaseController.subscriptionTransaction_add(
            id,
            SubscriptionTransaction.TransactionType.SUBSCRIPTION,
            subscriptionRoot.priceToLogUSD
        )
        if (!success) {
            logger.error("Error logging users subscription transaction with subscription - $id")
            return false
        }
    }

    //platform doesn't require a user to be created so we should track this via
    //subscription_device_platform_connections
    if (subscriptionRoot.storeId.platformDoesNotRequireUserId()) {
        if (subscriptionRoot.deviceId == Device.NO_SERVER_ID) {
            logger.error("Error device id was not right")
            return false
        }
        val success = databaseController.subscription_createDeviceConnection(id, subscriptionRoot.deviceId)
        if (!success) {
            logger.error("Error associating device: ${subscriptionRoot.deviceId} with $id")
            return false
        }
    }

    if (attributionId != "" && newSubscription) {
        val attribution = databaseController.attribution_getLinkAttributionByDeeplink(attributionId)
        if (attribution != null) {
            databaseController.attribution_addLinkAttributionEvent(
                attribution.id,
                LinkAttributionEvent.EventType.SUBSCRIBE,
                id
            )
        }
    }
    return true
}