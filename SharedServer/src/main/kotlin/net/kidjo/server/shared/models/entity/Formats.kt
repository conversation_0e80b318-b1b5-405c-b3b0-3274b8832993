package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table

object Formats : Table("formats") {
    val id: Column<Int> = integer("id").autoIncrement()

    val height: Column<Int> = integer("height")

    val codec: Column<String> = varchar("codec", 12)

    val streamingDelivery: Column<String?> = varchar("streamingdelivery", 10).nullable()

    val bitrate: Column<Int> = integer("bitrate")
    override val primaryKey: PrimaryKey
        get() = PrimaryKey(id)
}
