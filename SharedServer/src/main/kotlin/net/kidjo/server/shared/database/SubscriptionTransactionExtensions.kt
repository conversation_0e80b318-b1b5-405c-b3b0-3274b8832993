package net.kidjo.server.shared.database

import net.kidjo.server.shared.models.SubscriptionTransaction


fun DatabaseController.subscriptionTransaction_add(rootSubscriptionId: Long, transactionType: SubscriptionTransaction.TransactionType, priceValueUSD: Float): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO subscription_transactions(rootSubscriptionId, transactionType, priceValueUSD) VALUES (?,?,?)")

    statement.setLong(1,rootSubscriptionId)
    statement.setString(2,transactionType.raw)
    statement.setFloat(3,priceValueUSD)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}
