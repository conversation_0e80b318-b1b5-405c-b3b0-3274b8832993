package net.kidjo.server.shared.database.v5

import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.models.entity.*
import net.kidjo.server.shared.models.entity.AccountCouponProduct
import net.kidjo.server.shared.models.entity.AccountCouponType
import net.kidjo.server.shared.models.entity.Plans
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.innerJoin
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction

fun getPlans(product: List<Product>, countryId: Int?): List<net.kidjo.server.shared.models.Plans> {
    val plans = mutableListOf<net.kidjo.server.shared.models.Plans>()
    val plansMap = mutableMapOf<PlanType, List<Plan>>()
    transaction {
        (PlansCountries innerJoin Plans)
            .innerJoin(Countries)
            .innerJoin(Products)
            .slice(
                Plans.id,
                Plans.planType,
                Plans.planName,
                Plans.bundle,
                PlansCountries.price,
                PlansCountries.currency,
                PlansCountries.currencySymbol,
                Plans.active,
                Products.product
            ).select {
                Plans.active.eq(true) and
                        Countries.id.eq(countryId?.toUInt() ?: 32.toUInt()) and
                        Products.product.inList(product.map { it.name.lowercase() }
                            .toList())
            }.map {
                val planType = it[Plans.planType]
                plansMap.put(
                    planType, plansMap.getOrDefault(planType, mutableListOf()).plus(
                        Plan(
                            it[Plans.active],
                            Product.valueOf(it[Products.product]).product,
                            it[Plans.planName],
                            it[PlansCountries.price],
                            it[PlansCountries.currency],
                            it[PlansCountries.currencySymbol],
                            Product.valueOf(it[Products.product]).productLabel,
                            it[Plans.id],
                            getAddOns(it[Plans.id])
                        ),
                    )
                )
            }
        plans.add(
            net.kidjo.server.shared.models.Plans(
                "monthly_plan",
                "P1M",
                plansMap[PlanType.MONTHLY] ?: emptyList()
            )
        )
        plans.add(
            net.kidjo.server.shared.models.Plans(
                "annual_plan",
                "P1Y",
                plansMap[PlanType.YEARLY] ?: emptyList()
            )
        )
    }
    return plans
}


fun getAddOns(planId: Int): Set<Addon> {
    val addOns = mutableSetOf<Addon>()
    transaction {
        AddonPlans.innerJoin(Addons).select {
            AddonPlans.plan.eq(planId)
        }.map {
            addOns.add(
                Addon(
                    it[Addons.addOnId],
                    it[Addons.price]
                )
            )
        }
    }
    return addOns
}


fun getCouponDetails(couponCode: String): AccountCoupon? {
    return transaction {
        val coupon = (AccountCoupons leftJoin AccountCouponType)
            .leftJoin(AccountCouponsPartner)
            .leftJoin(AccountCouponProduct)
            .select {
                AccountCoupons.couponId.eq(couponCode)
            }.singleOrNull()
        coupon?.let {
            AccountCoupon(
                it[AccountCoupons.id],
                it[AccountCoupons.groupId],
                it[AccountCoupons.couponId],
                it[AccountCoupons.startDate],
                it[AccountCoupons.expireDate],
                it[AccountCoupons.duration],
                it[AccountCoupons.redeemedTimes],
                it[AccountCoupons.redeemAmount],
                it[AccountCouponType.name],
                it[AccountCouponProduct.name],
                it[AccountCouponsPartner.name],
                it[AccountCoupons.created_at]
            )
        }
    }
}


fun getDiscountDetails(couponCode: String): DiscountDetails? {
    return transaction {
        val coupon = DiscountCoupons.innerJoin(AccountCoupons, { couponId }, { id })
            .innerJoin(Discount, { DiscountCoupons.discountId }, { id })
            .innerJoin(AccountCouponsPartner)
            .innerJoin(AccountCouponProduct)
            .select {
                AccountCoupons.couponId.eq(couponCode)
            }.singleOrNull()
        coupon?.let {
            DiscountDetails(
                it[Discount.id],
                it[Discount.discountName],
                it[DiscountCoupons.price],
                it[AccountCoupons.couponId],
                it[AccountCouponProduct.name],
                it[DiscountCoupons.applyForBundle],
                getPlanTypeFromDuration(it[AccountCoupons.duration])
            )
        }
    }
}


fun getUserCurrentPlans(
    product: List<Product>,
    countryId: Int?,
    subscriptions: List<String>,
): MutableList<net.kidjo.server.shared.models.Plans> {
    val plans = mutableListOf<net.kidjo.server.shared.models.Plans>()
    val plansMap = mutableMapOf<PlanType, List<Plan>>()
    val subscribedProduct = getProducts(subscriptions)
    transaction {
        (PlansCountries innerJoin Plans)
            .innerJoin(Countries)
            .innerJoin(Products)
            .slice(
                Plans.id,
                Plans.planType,
                Plans.planName,
                Plans.bundle,
                PlansCountries.price,
                PlansCountries.currency,
                PlansCountries.currencySymbol,
                Plans.active,
                Products.product
            ).select {
                Plans.active.eq(true) and
                        Countries.id.eq(countryId?.toUInt() ?: 32.toUInt()) and
                        Products.product.inList(product.map { it.name.lowercase() }
                            .toList())
            }.map {
                val planType = it[Plans.planType]
                plansMap.put(
                    planType, plansMap.getOrDefault(planType, mutableListOf()).plus(
                        Plan(
                            it[Plans.active],
                            Product.valueOf(it[Products.product]).product,
                            it[Plans.planName],
                            it[PlansCountries.price],
                            it[PlansCountries.currency],
                            it[PlansCountries.currencySymbol],
                            Product.valueOf(it[Products.product]).productLabel,
                            it[Plans.id],
                            getAddOns(it[Plans.id]),
                            subscribedProduct.contains(Product.valueOf(it[Products.product]).name.lowercase())
                        ),
                    )
                )
            }
        plans.add(
            net.kidjo.server.shared.models.Plans(
                "monthly_plan",
                "P1M",
                plansMap[PlanType.MONTHLY] ?: emptyList()
            )
        )
        plans.add(
            net.kidjo.server.shared.models.Plans(
                "annual_plan",
                "P1Y",
                plansMap[PlanType.YEARLY] ?: emptyList()
            )
        )
    }
    return plans
}


fun getPlanType(planId: String): PlanType {
    return transaction {
        val plan = Plans.select {
            Plans.planName.eq(planId)
        }.limit(1).singleOrNull()
        plan.let {
            it?.get(Plans.planType) ?: PlanType.MONTHLY
        }
    }
}


private fun getProducts(subscriptions: List<String>): MutableSet<String> {
    val finalProducts = mutableSetOf<String>()
    subscriptions.forEach {
        when (it) {
            "kidjo_tv_books" -> {
                finalProducts.add("kidjo_tv")
                finalProducts.add("kidjo_books")
            }

            "kidjo_tv_games" -> {
                finalProducts.add("kidjo_tv")
                finalProducts.add("kidjo_games")
            }

            "kidjo_books_games" -> {
                finalProducts.add("kidjo_books")
                finalProducts.add("kidjo_games")
            }

            "kidjo_tv_books_games" -> {
                finalProducts.add("kidjo_tv")
                finalProducts.add("kidjo_books")
                finalProducts.add("kidjo_games")
            }

            else -> {
                finalProducts.add(it)
            }

        }
    }
    return finalProducts
}

private fun getPlanTypeFromDuration(durationCode: String): String {

    val planType = durationCode.substring(0, durationCode.length - 1);
    return when (planType.uppercase()) {
        "Y" -> PlanType.YEARLY.planType
        "D" -> PlanType.DAILY.planType
        else -> PlanType.MONTHLY.planType
    }
}
