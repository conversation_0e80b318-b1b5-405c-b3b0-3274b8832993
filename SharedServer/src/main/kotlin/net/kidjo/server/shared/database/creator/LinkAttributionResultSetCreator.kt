package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.LinkAttribution
import java.sql.ResultSet


fun ResultSetObjectCreator.linkAttribution(resultSet: ResultSet): LinkAttribution {
    val id = resultSet.getLong("id")
    val name = resultSet.getString("name")
    val deepLinkId = resultSet.getString("deepLinkId")
    val link = resultSet.getString("link")
    val isActive = resultSet.getBoolean("isActive")

    return LinkAttribution(id, name, deepLinkId, link, isActive)
}