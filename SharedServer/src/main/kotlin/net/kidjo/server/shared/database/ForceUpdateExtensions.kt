package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.forceUpdate
import net.kidjo.server.shared.models.ForceUpdate
import java.sql.SQLException


fun DatabaseController.getForceUpdateVersion(
    appName: String, appModule: String,
    appOS: String
): ForceUpdate? {
    val connection = dataSource.connection
    val statement =
        connection.prepareStatement("SELECT * from mobile_app_force_update WHERE app_name = ? and app_module = ? and app_os = ?")
    try {
        statement.setString(1, appName)
        statement.setString(2, appModule)
        statement.setString(3, appOS)

        val results = statement.executeQuery()

        val forceUpdate: ForceUpdate?
        if (results.next()) forceUpdate = objectCreator.forceUpdate(results)
        else forceUpdate = null

        statement.close()
        connection.close()

        return forceUpdate
    } catch (e: SQLException) {
        println("Error getting: getForceUpdateVersion ${e.localizedMessage}")
        return null
    } finally {
        statement.close()
        connection.close()
    }
}