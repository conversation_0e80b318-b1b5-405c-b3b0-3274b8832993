package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getUserEmailById
import net.kidjo.server.shared.database.updateUserPasswordAndName
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.database.user_register
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import org.slf4j.LoggerFactory
import java.security.MessageDigest

private val logger = LoggerFactory.getLogger("DVNotificationApi - MODIFY KIDJO USER ACCOUNT - ")
internal val GENERATED_ACCOUNT_CHARACTERS_TO_USE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()
private const val LENGTH_OF_GENERATED_DV_ACCOUNT_NAME = 6
const val LENGTH_OF_DV_ACCOUNT_NAME = 12
const val DV_USERS_BASE_EMAIL = "@kidjo.tv"
fun modifyKIDJOAccount(
    userId: Long? = null,
    msisdn: String? = null,
    countryId: Long,
    email : String?,
    operatorName: String,
    utility: Utility,
    config: Config,
    encryptionController: EncryptionController,
    databaseController: DatabaseController
): User = try {

    val userHolder = User.getEmptyUser()

    val (password, hashedPassword) =
        generatePassword(
            randomString = utility.randomString(
                LENGTH_OF_GENERATED_DV_ACCOUNT_NAME,
                GENERATED_ACCOUNT_CHARACTERS_TO_USE
            ),
            encryptionController = encryptionController,
            salt = config.clientSideHashV1Salt
        )

    if (userId?.toInt() == 0) {
        val modifiedMsisdn = msisdn?.replace("+", "0")?.take(LENGTH_OF_DV_ACCOUNT_NAME)
        logger.info(" CREATE DV USER ACCOUNT ")
        var generatedEmail =
            if (modifiedMsisdn != null) modifiedMsisdn + DV_USERS_BASE_EMAIL
            else generateEmail(
                randomString = utility.randomString(
                    LENGTH_OF_GENERATED_DV_ACCOUNT_NAME,
                    GENERATED_ACCOUNT_CHARACTERS_TO_USE
                ) + DV_USERS_BASE_EMAIL,
                databaseController = databaseController
            )
        if (operatorName=="NONE"){
            generatedEmail=email!!
        }
        logger.info("msisdn: $msisdn")
        logger.info("generatedEmail: $generatedEmail")

        if (generatedEmail.isBlank()) {
            logger.error("ERROR: Creating DV USER EMAIL: NOT CREATED EMAIL: generateEmail ($generatedEmail) ")
        }

        userHolder.email = generatedEmail
        userHolder.name = modifiedMsisdn ?: generatedEmail
        userHolder.countryId = countryId.toInt()
        userHolder.password = password
        userHolder.hashedPassword = hashedPassword
        userHolder.authType = User.AuthType.FAKE_EMAIL
        if (operatorName=="NONE"){
            userHolder.authType = User.AuthType.EMAIL
        }


        val user = databaseController.user_getByEmail(userHolder.email);
        if (user != null) {
            userHolder.id = user.id
        } else {
            val insertedUserId = databaseController.user_register(userHolder)
            if (insertedUserId == User.NO_SERVER_ID) {
                logger.error("ERROR: Creating DV USER ACCOUNT: ${userHolder.id}")
            }
            userHolder.id = insertedUserId.toString()
        }
        userHolder
    } else {
        logger.info("UPDATE DV USER ACCOUNT PASSWORD")
        logger.info("USER ACCOUNT ID $userId")
        userHolder.id = userId.toString()
        userHolder.email = databaseController.getUserEmailById(userId!!)
        userHolder.password = password
        if (email != null) {
            if(email.isNotEmpty() && operatorName=="NONE"){
                userHolder.email=email
            }
        }

        databaseController.updateUserPasswordAndName(
            id = userId!!,
            name = userHolder.email,
            hashedPassword = hashedPassword
        )
        logger.info("SUCCESS: UPDATE DV USER ACCOUNT PASSWORD failed to updated the user : '${userId}'")
        userHolder
    }
} catch (e: Throwable) {
    logger.error("Problems modifying VG User Account: ${e.localizedMessage}")
    throw e
}

fun generateEmail(randomString: String, databaseController: DatabaseController): String {
    var generatedEmail = ""
    while (generatedEmail == "") {
        try {
            generatedEmail = randomString
            val user = databaseController.user_getByEmail(generatedEmail)
            if (user != null) generatedEmail = ""
        } catch (e: Throwable) {
            logger.error("Problems generation VG fake email: ${e.localizedMessage}")
        }
    }
    return generatedEmail
}

fun generatePassword(
    randomString: String,
    encryptionController: EncryptionController,
    salt: String
): Pair<String, String> {
    val preHashedPassword = sha256(randomString + salt)
    val hashedPassword = encryptionController.hashPassword(preHashedPassword)

    return Pair(randomString, hashedPassword)
}

fun sha256(input: String): String {
    val messageDigest = MessageDigest.getInstance("SHA-256")
    val hashBytes = messageDigest.digest(input.toByteArray())

    val hexString = StringBuilder()
    for (byte in hashBytes) {
        hexString.append(String.format("%02x", byte))
    }

    return hexString.toString()
}
