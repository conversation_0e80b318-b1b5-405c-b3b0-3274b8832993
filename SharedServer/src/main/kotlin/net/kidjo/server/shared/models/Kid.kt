package net.kidjo.server.shared.models

data class Kid(var serverId: Long,
               var userId: String,
               var name: String,
               var age: Int,
               var serverParentAccountId: Long) {

    constructor(): this(Kid.NO_ID_SERVER, Kid.NO_ID, Kid.NO_NAME, Kid.DEFAULT_AGE, NO_ID_SERVER)

    fun getLongUserId(): Long {
        return try {
            userId.toLong()
        }catch (e: Exception) {
            return Kid.NO_ID_SERVER
        }
    }

    //================
    //STATIC
    //================
    companion object {
        const val NO_ID_SERVER = User.NO_SERVER_ID
        const val NO_ID = "0"
        const val DEFAULT_AGE = 4
        const val NO_NAME = ""
        const val NO_PROFILE_IMAGE = "NONE"
        const val AGE_MAX =  7
        const val AGE_MIN = 1
        const val NUMBER_OF_AGE_OPTIONS = 7
        const val NO_AGE = -1
        const val MAX_NAME_LENGTH = 12

        private val NAME_REGEX = Regex.fromLiteral("[a-zA-Z]{1,12}")
        fun CreateApp(id: String, age: Int): Kid {
            return Kid(NO_ID_SERVER,id, NO_NAME,age,NO_ID_SERVER)
        }
        fun CheckName(name: String): Boolean {
            if (name.isEmpty() || name == NO_NAME || name.length > MAX_NAME_LENGTH) return false
            if (NAME_REGEX.matches(name)) return false
            return true
        }
        fun CheckAge(age: Int): Boolean {
            return (AGE_MIN <= age && age <= AGE_MAX)
        }
    }
}