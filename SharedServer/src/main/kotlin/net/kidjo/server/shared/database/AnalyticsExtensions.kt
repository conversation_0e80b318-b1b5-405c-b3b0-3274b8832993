package net.kidjo.server.shared.database

fun DatabaseController.video_playedAnalytics(deviceId: Long, kidId: Long, videoId: Long, endAtSeconds: Int): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO videosPlayed (deviceId,avatarId,videoId,length) VALUE(?,?,?,?)")
    statement.setLong(1,deviceId)
    statement.setLong(2,kidId)
    statement.setLong(3,videoId)
    statement.setInt(4,endAtSeconds)
    val results: Boolean = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}