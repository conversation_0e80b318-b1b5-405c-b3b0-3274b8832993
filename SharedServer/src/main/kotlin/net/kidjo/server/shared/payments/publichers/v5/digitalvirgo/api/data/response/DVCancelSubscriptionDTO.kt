package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVCancelSubscriptionDTO (
    var code    : Int?    = null,
    var message : String? = null,
    var detail  : String? = null,
    var data    : DVCancelSubscriptionData?   = DVCancelSubscriptionData()

)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVCancelSubscriptionUser (
    var alias   : String? = null,
    var ip        : String? = null,
    var mccmnc    : Int?    = null,
    var userAgent : String? = null,
    var locale    : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVCancelSubscriptionOffer (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVCancelSubscriptionData (
    var status        : String?     = null,
    var type          : String?     = null,
    var operationId   : String?     = null,
    var correlationId : String?     = null,
    var creationDate  : String?     = null,
    var date          : String?     = null,
    var user          : DVCancelSubscriptionUser?       = DVCancelSubscriptionUser(),
    var data          : DVData?       = DVData(),
    var customization : String?     = null,
    var dimensions    : DVCancelSubscriptionDimensions? = DVCancelSubscriptionDimensions(),
    @JsonProperty("package") var dvPackage: String?     = null,
    var offer         : DVCancelSubscriptionOffer?      = DVCancelSubscriptionOffer()
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVData (
    var clickId        : String?     = null,
    var clientId       : String?     = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVCancelSubscriptionDimensions (
    var billingMode    : String? = null,
    var billingChannel : String? = null,
    var orderChannel   : String? = null,
    var networkChannel : String? = null
)