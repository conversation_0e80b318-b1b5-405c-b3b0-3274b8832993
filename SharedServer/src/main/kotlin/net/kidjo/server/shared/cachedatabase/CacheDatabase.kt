package net.kidjo.server.shared.cachedatabase

import io.lettuce.core.RedisClient
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController

class CacheDatabase(config: Config,
                    internal val encryptionController: EncryptionController,
                    internal val databaseController: DatabaseController) {
    private val redisClient: RedisClient = RedisClient.create(config.redis_login)
    internal val connection = redisClient.connect()

    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)

    companion object {

        const val KEY_MONDIA = "mondiapay_"
        const val KEY_DV = "digital_virgo_api_"
        const val KEY_USER_DEVICE_PAIRING_FOR_REGISTRATION = "user_device_reg_"
        const val KEY_JWT_BLAKCLIST = "jwt_blacklist_"
        const val KEY_RECENT_VIDEOS = "recent_videos_"
        const val KEY_EMAIL_CONFIRMATION = "confirm_email_"
        const val KEY_IP_COUNTRY = "ip_country_"

        const val KEY_LINK_PIN = "link_pin_"

    }
}
