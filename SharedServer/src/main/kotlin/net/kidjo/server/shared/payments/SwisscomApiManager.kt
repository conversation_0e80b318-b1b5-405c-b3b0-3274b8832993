package net.kidjo.server.shared.payments

import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.*
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.Device
import okhttp3.*
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

object SwisscomApiManager {

    data class SwisscomIdentificationJson(val requestEndpoint: String? = null, val sessionId: String? = null)

    data class SwisscomIdentification(val requestEndpoint: String, val sessionId: String)

    data class SwisscomRegisterResponse(val isPremium: Boolean? = false)

    data class SwisscomBillingResponse(
        val status: String,
        val code: String? = null,
        val message: String? = null,
        val detail: String? = null
    )

    data class SwisscomSubscriptionData(val requestId: String, val startDate: LocalDateTime, val endDate: LocalDateTime)

    data class AuthResponse(val accessToken: String, val scope: String, val tokenType: String, val expiresIn: Long)

    const val SWISSCOM_DATE_FORMAT = "yyyy-MM-dd"

    const val SWISSCOM_KIDJO_PAYMENT_ID = "Swisscom"
    const val SWISSCOM_KIDJO_IAP_ID = "KIDJO-SWISSCOM"
    const val SWISSCOM_KIDJO_PURCHASE_SESSION_ID = "NONE"
    const val SWISSCOM_KIDJO_REQUEST_ID_PREFIX = "Kidjo-"

    const val SWISSCOM_SUBSCRIPTION_PRICE = 6.90 // CHF
    const val SWISSCOM_SUBSCRIPTION_DESCRIPTION = "Kidjo premium subscription"
    const val SWISSCOM_SUBSCRIPTION_DURATION_DAYS = 30L

    private const val MERCHANT_ID = "KJT01"

    private const val REQUEST_TIMEOUT_MS = 20000L // 20s

    private const val SWISSCOM_BILLING_API_ENDPOINT = "https://api.swisscom.com"
    private const val SWISSCOM_BILLING_API_CUSTOMER_KEY = "9TKmjKOZwi2eC98VHAx2GAf3W5gUZ3oW"
    private const val SWISSCOM_BILLING_API_CUSTOMER_SECRET = "wxQ8Xjsj6PTpn0xL"

    private const val SWISSCOM_API_PATH_AVAILABILITY = "/portfolio/carrierbilling/availability"
    private const val SWISSCOM_API_PATH_REGISTER = "/portfolio/carrierbilling/register"

    private const val SWISSCOM_BILLING_API_PATH_OAUTH = "/oauth2/token"
    private const val SWISSCOM_BILLING_API_PATH_PAYMENTS = "/integrations/tv/payments"

    private const val SWISSCOM_HEADER_VERSION = "SCS-Version"
    private const val SWISSCOM_HEADER_REQUEST_ID = "SCS-Request-ID"

    private const val SWISSCOM_KEY_GRANT_TYPE = "grant_type"
    private const val SWISSCOM_KEY_ABID = "abid"
    private const val SWISSCOM_KEY_AMOUNT = "amount"
    private const val SWISSCOM_KEY_DESCRIPTION = "description"
    private const val SWISSCOM_KEY_BILLING_START_DATE = "billingStartDate"
    private const val SWISSCOM_KEY_BILLING_END_DATE = "billingEndDate"
    private const val SWISSCOM_KEY_IDEMPOTENCY_KEY = "idempotencyKey"

    private const val ERROR_REQUEST_TIMEOUT = "The request took too long to respond"
    private const val ERROR_ABID_REQUEST = "A problem occurred while requesting the ABID"
    private const val ERROR_BILLING_NOT_ALLOWED = "Billing was not allowed"

    private val logger = LoggerFactory.getLogger("SwisscomApiManager")

    private val dateFormatter = DateTimeFormatter.ofPattern(SWISSCOM_DATE_FORMAT)
    private val okHttpClient = OkHttpClient()

    private val jsonMediaType = MediaType.parse("application/json; charset=utf-8")

    //region Main requests

    /**
     * Check if the billing system is available for user on Swisscom API
     */
    suspend fun sendAvailabilityRequest(identification: SwisscomIdentification): Boolean {
        val url = "${getProperEndpoint(identification.requestEndpoint)}$SWISSCOM_API_PATH_AVAILABILITY/$MERCHANT_ID"
        val request = getBaseRequestBuilder(url, identification.sessionId).get().build()
        val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
        return response.code() == HttpStatusCode.NoContent.value
    }

    /**
     * Request new anonymous billing identifier (ABID) to Swisscom API
     */
    suspend fun sendAbidRequest(
            device: Device,
            identification: SwisscomIdentification,
            call: ApplicationCall? = null
    ): String? {
        val url = "${getProperEndpoint(identification.requestEndpoint)}$SWISSCOM_API_PATH_REGISTER/$MERCHANT_ID"
        val request = getBaseRequestBuilder(url, identification.sessionId).get().build()
        return try {
            withTimeout(REQUEST_TIMEOUT_MS) { checkRegistration(request, call) }
        } catch (exception: TimeoutCancellationException) {
            logger.error("sendAbidRequest() | server took too long the respond the requested ABID for deviceId: '${device.serverId}', sessionId: '${identification.sessionId}'")
            call?.respondError(HttpStatusCode.RequestTimeout, ERROR_REQUEST_TIMEOUT)
            null
        }
    }

    /**
     * Request a new subscription to the Swisscom billing API
     */
    suspend fun sendSubscriptionRequest(deviceAbid: String, call: ApplicationCall? = null): SwisscomSubscriptionData? {
        // Retrieve OAuth token to communicate with the Swisscom billing API
//        val oAuthToken = getBillingAuthenticationToken()
//        if (oAuthToken == null) {
//            call?.respondError(HttpStatusCode.Forbidden, SwisscomApiManager.ERROR_BILLING_NOT_ALLOWED)
//            return null
//        }

        // Generate base data for request
        val requestId = "${getSwisscomRequestId()}-${Random().nextInt(Int.MAX_VALUE)}"
        val startDate = LocalDateTime.now()
        val endDate = startDate.plusDays(SWISSCOM_SUBSCRIPTION_DURATION_DAYS)

        // Request the billing API for a new subscription
//        val url = "${SwisscomApiManager.SWISSCOM_BILLING_API_ENDPOINT}${SwisscomApiManager.SWISSCOM_BILLING_API_PATH_PAYMENTS}"
//        val jsonBody = JSONObject()
//                .put(SwisscomApiManager.SWISSCOM_KEY_ABID, deviceAbid)
//                .put(SwisscomApiManager.SWISSCOM_KEY_AMOUNT, SwisscomApiManager.SWISSCOM_SUBSCRIPTION_PRICE)
//                .put(SwisscomApiManager.SWISSCOM_KEY_DESCRIPTION, SwisscomApiManager.SWISSCOM_SUBSCRIPTION_DESCRIPTION)
//                .put(SwisscomApiManager.SWISSCOM_KEY_BILLING_START_DATE, startDate.format(dateFormatter))
//                .put(SwisscomApiManager.SWISSCOM_KEY_BILLING_END_DATE, endDate.format(dateFormatter))
//                .put(SwisscomApiManager.SWISSCOM_KEY_IDEMPOTENCY_KEY, requestId)
//        val request = getBaseRequestBuilder(url, oAuthToken)
//                .header(SwisscomApiManager.SWISSCOM_HEADER_REQUEST_ID, requestId)
//                .header(SwisscomApiManager.SWISSCOM_HEADER_VERSION, "1")
//                .post(RequestBody.create(jsonMediaType, jsonBody.toString()))
//                .build()
//        val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
//        if (response.code() == HttpStatusCode.Conflict.value) {
//            logger.warn("sendSubscriptionRequest() | user already subscribed with the same request identifier (idempotency key)")
//        } else if (response.code() != HttpStatusCode.OK.value) {
//            val billingResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
//            val billingResponse = billingResponseString?.let { jacksonObjectMapper().readValue<SwisscomBillingResponse>(it) }
//            logger.error("sendSubscriptionRequest() | response from the Swisscom billing API was not OK: '${billingResponse?.message ?: ""}' (${billingResponse?.status?.toIntOrNull()})")
//            call?.respondError(HttpStatusCode.fromValue(billingResponse?.status?.toIntOrNull() ?: 0), billingResponse?.message ?: "")
//            return null
//        }

        return SwisscomSubscriptionData(requestId, startDate, endDate)
    }

    //endregion

    //region Extra methods for requests

    /**
     * Swisscom registration check process with request loop and ABID retrieval
     */
    private suspend fun checkRegistration(request: Request, call: ApplicationCall? = null): String? {
        var looping = true
        while (looping) {
            // Stop the loop by default
            looping = false

            // Make request
            val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
            val body = response.body()
            when (response.code()) {
                HttpStatusCode.OK.value -> {
                    logger.info("checkRegistration() | response OK")
                    if (body != null) return withContext(Dispatchers.IO) { body.string().removeSurrounding("\"") }
                    else call?.respondError(HttpStatusCode.InternalServerError, ERROR_ABID_REQUEST)
                }
                HttpStatusCode.Accepted.value -> {
                    logger.info("checkRegistration() | response Accepted: delaying next call...")
                    // Delay next call and override loop
                    delay(2000)
                    looping = true
                }
                HttpStatusCode.Conflict.value -> {
                    logger.info("checkRegistration() | response Conflict")
                    call?.respondError(HttpStatusCode.Conflict, ERROR_BILLING_NOT_ALLOWED)
                }
                else -> {
                    logger.info("checkRegistration() | error in response: code ${response.code()}")
                    call?.respondError(HttpStatusCode.InternalServerError, ERROR_ABID_REQUEST)
                }
            }
        }

        return null
    }

    /**
     * Retrieve an OAuth token for the Swisscom billing API
     */
    private suspend fun getBillingAuthenticationToken(): String? {
        // Request OAuth token
        val url = "$SWISSCOM_BILLING_API_ENDPOINT$SWISSCOM_BILLING_API_PATH_OAUTH"
        val body = FormBody.Builder()
            .add(SWISSCOM_KEY_GRANT_TYPE, "client_credentials")
            .build()
        val request = getOAuthBaseRequestBuilder(url).post(body).build()
        val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
        if (response.code() != HttpStatusCode.OK.value) return null

        // Retrieve auth object
        val authResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
        val authResponse = authResponseString?.let {
            jacksonObjectMapper()
                .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE)
                .readValue<AuthResponse>(it)
        }

        // Return access token if any
        return authResponse?.accessToken
    }

    //endregion

    //region Utils

    private fun getProperEndpoint(url: String) =
        if (url.contains("http")) url else "https://".plus(url)

    private fun getSwisscomRequestId(): String =
        "$SWISSCOM_KIDJO_REQUEST_ID_PREFIX${System.currentTimeMillis()}"

    private fun getBaseRequestBuilder(url: String, bearerToken: String): Request.Builder =
        Request.Builder().url(url)
            .header("Authorization", "Bearer $bearerToken")
            .header("Accept", "application/json")
            .header("Content-Type", "application/json")
            .header("UserAgent", "Kidjo API")

    private fun getOAuthBaseRequestBuilder(url: String): Request.Builder {
        val authKey = Base64.getEncoder()
            .encodeToString("$SWISSCOM_BILLING_API_CUSTOMER_KEY:$SWISSCOM_BILLING_API_CUSTOMER_SECRET".toByteArray())
        return Request.Builder().url(url)
            .header("Authorization", "Basic $authKey")
            .header("Accept", "application/json")
            .header("Content-Type", "x-www-form-urlencoded")
            .header("UserAgent", "Kidjo API")
    }

    //endregion

}
