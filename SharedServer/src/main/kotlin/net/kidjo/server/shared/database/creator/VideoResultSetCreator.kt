package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.Video
import net.kidjo.server.shared.models.VideoSimple
import java.sql.ResultSet
import java.time.LocalDate

fun ResultSetObjectCreator.video(resultSet: ResultSet): Video {
    val serverId = resultSet.getLong("id")
    val id = encryptionController.encodeVideoId(serverId)
    val title = resultSet.getString("title")
    var creation = ""
    var creationDate = LocalDate.now()
    try {
        creation = resultSet.getDate("creation").toString()
        creationDate = resultSet.getDate("creation").toLocalDate()
    } catch (e: Exception) {
    }

    val ageMin = resultSet.getInt("ageMin")
    val ageMax = resultSet.getInt("ageMax")
    val isPremium = resultSet.getBoolean("isPremium")
    val duration = resultSet.getInt("duration")
    val compilation = resultSet.getBoolean("compile")
    val formatIdArrayString = resultSet.getString("videoFormatIds")
    val formatIdArray = formatIdArrayString.split(",")
    val sizeArrayString = resultSet.getString("fileSize")
    val sizeArray = sizeArrayString.split(",")

    val formats = Array(formatIdArray.size,{ i ->
        try {
            return@Array Video.Format.FromId(formatIdArray[i].toInt())
        } catch (e: Exception) {
            return@Array Video.Format.NONE
        }
    })

    val formatSizes = LongArray(sizeArray.size,{ i ->
        try {
            sizeArray[i].toLong()
        } catch (e: Exception) {
            0L
        }
    })

    return Video(serverId,id,title,ageMin,ageMax,isPremium,duration,compilation,formats,formatSizes, creation, creationDate)
}

fun ResultSetObjectCreator.videoSimple(resultSet: ResultSet): VideoSimple {
    val serverId = resultSet.getLong("id")
    val id = encryptionController.encodeVideoId(serverId)
    val videoPath = "https://kidjo.s3.us-east-1.amazonaws.com/videos/8/$id.mp4"

    return VideoSimple(videoPath)
}