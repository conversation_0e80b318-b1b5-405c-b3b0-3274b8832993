package net.kidjo.server.shared.payments

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.server.application.*
import io.ktor.http.*
import kotlinx.coroutines.*
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.tools.Config
import okhttp3.*
import org.json.JSONObject
import org.slf4j.LoggerFactory
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import javax.xml.bind.DatatypeConverter

class JioApiManager(internal val config: Config) {

    private val okHttpClient = OkHttpClient()
    private val logger = LoggerFactory.getLogger("JioApiManager")

    /**
     * Verify and return purchase receipt from JIO API
     */
    suspend fun getSubscriptionStatus(
        receiptId: String,
        call: ApplicationCall? = null
    ): JioSubscriptionStatus? {

        var response = getResponse(receiptId)

        if (response.code().toString() != "200") {
            logger.error("JIO RVS Error: Invalid receiptID")
            call?.respondError(HttpStatusCode.fromValue(response.code()), "JIO RVS Error: Invalid receiptID")
            return null
        }
        return getSubscriptionResponse(receiptId)
    }

    /**
     * Verify and return purchase receipt from JIO API for the JOB
     */
    fun getSubscriptionStatus(receiptId: String): JioSubscriptionStatus? {
        var billingResponse: JioSubscriptionStatus? = null

        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                billingResponse = getSubscriptionResponse(receiptId)
            }
        }

        return billingResponse
    }

    private suspend fun getSubscriptionResponse(
        receiptId: String
    ): JioSubscriptionStatus? {

        var response = getResponse(receiptId)

        var billingResponseString = withContext(Dispatchers.IO) { response.body()?.string() }

        val subRes = billingResponseString?.let {
            jacksonObjectMapper().readValue<JioSubscriptionStatus>(it)
        }

        response.close()
        return subRes
    }

    private suspend fun getResponse(receipt: String): Response {
        val jsonBody = JSONObject()
            .put("orderId", receipt)
            .put("appPackageName", config.jio_kidjo_tv_package_name)
            .put("checksum", generatePayloadCheckSum(receipt))

        var request = Request.Builder().url(config.jio_verify_receipt_url)
            .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonBody.toString())).build()
        return withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
    }

    private fun generatePayloadCheckSum(orderId: String): String {
        val hasher = Mac.getInstance("HmacSHA256")
        hasher.init(SecretKeySpec(config.jio_clientId.toByteArray(), "HmacSHA256"))
        val hash = hasher.doFinal(orderId.toByteArray())
        // to lowercase hexits
        return DatatypeConverter.printHexBinary(hash)
    }
}

data class JioSubscriptionStatus(
    val txnId: String,
    val transactionStatus: String,
    val timestamp: String,
    val amount: String,
    val skuId: String,
    val orderId: String,
    val startDate: String,
    val endDate: String,
    val checksum: String,
    val currency: String,
    val obfUsrId: String? = null,
    val udf1: String? = null,
    val udf2: String? = null,
    val code: String,
    val message: String
)
