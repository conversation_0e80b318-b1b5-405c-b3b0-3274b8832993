package net.kidjo.server.shared.models

enum class Product(
    val product: String,
    val productLabel: String,
) {
    KIDJO_TV("tv", "Kidjo Tv"),
    KIDJO_BOOKS("books", "Kidjo Stories"),
    KIDJO_GAMES("games", "Kidjo Games");

    companion object {
        fun getProduct(subscriptionType: List<String>): List<Product> {
            val product = mutableSetOf<Product>()
            subscriptionType.forEach {
                when (it) {
                    "kidjo_tv_books" -> {
                        product.add(KIDJO_TV)
                        product.add(KIDJO_BOOKS)
                    }

                    "kidjo_tv_games" -> {
                        product.add(KIDJO_TV)
                        product.add(KIDJO_GAMES)
                    }

                    "kidjo_books_games" -> {
                        product.add(KIDJO_BOOKS)
                        product.add(KIDJO_GAMES)
                    }

                    "kidjo_tv_books_games" -> {
                        product.clear()
                    }

                    else -> product.add(Product.valueOf(it.uppercase()))
                }

            }

            return product.toList()
        }
    }


}

