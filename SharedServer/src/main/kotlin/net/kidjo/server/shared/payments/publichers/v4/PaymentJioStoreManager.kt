package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.*
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import okhttp3.OkHttpClient
import java.time.LocalDateTime

class PaymentJioStoreManager(
    config: Config,
    httpClient: OkHttpClient,
    encryptionController: EncryptionController,
    databaseController: DatabaseController,
    iapManager: IAPManager,
    private val jioApiManager: JioApiManager,
    emailManager: <PERSON>ailManager,
    languageCache: LanguageCache,
    userManager: UserManager
) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {


    override suspend fun start(
        userId: Long, inAppPurchase: InAppPurchase,
        token: String, purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        forceUpdate: Boolean,
        subscriptionId: String?,
        orderId: String?,
        call: ApplicationCall
    ) {

        when (inAppPurchase.store) {
            Device.StorePlatform.JIO -> {
                var checkSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(token)
                if (checkSubscription == null) {
                    return createSubscription(
                        inAppPurchase, token,
                        call, userId, purchasingSession, subscriptionType,
                        subscriptionId, orderId
                    )
                } else {
                    if (checkSubscription?.userId != 0L && userId != checkSubscription?.userId) {
                        return call.respondConflict(
                            SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                            "This subscription already belongs to another account. Please login."
                        )
                    }
                    val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                    if (isExpired) {
                        return updateSubscription(
                            inAppPurchase, token,
                            call, userId, checkSubscription
                        )
                    } else {
                        return call.respondNoContent()
                    }
                }
            }
            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun createSubscription(
        inAppPurchase: InAppPurchase,
        token: String, call:
        ApplicationCall,
        userId: Long,
        purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        subscriptionId: String?,
        orderId: String?
    ) {
        val jioSubscriptionPurchase = getJioReceipt(
            token, call
        )
        if (jioSubscriptionPurchase != null) {
            val subscriptionRootInsert = SubscriptionRootInsert(
                userId, 0L, false, jioSubscriptionPurchase.amount.toFloat(),
                SubscriptionRoot.PaymentType.NATIVE, subscriptionType, inAppPurchase.store.raw,
                jioSubscriptionPurchase.txnId, inAppPurchase.store, jioSubscriptionPurchase.txnId,
                inAppPurchase.id, purchasingSession, token, encryptionController.sha256Hash(token),
                0L, true,
                LocalDateTime.parse(jioSubscriptionPurchase.endDate), !config.env.isLive
            )

            insert(subscriptionRootInsert, call)
        }
    }

    override suspend fun updateSubscription(
        inAppPurchase: InAppPurchase,
        token: String,
        call: ApplicationCall,
        userId: Long,
        subscriptionRoot: SubscriptionRoot
    ) {
        val jioSubscriptionPurchase = getJioReceipt(token, call)

        if (jioSubscriptionPurchase != null) {
            val subscriptionRootUpdate = SubscriptionRootUpdate(
                userId, true,
                LocalDateTime.parse(jioSubscriptionPurchase.endDate),
                subscriptionRoot.platformPurchaseId, false, jioSubscriptionPurchase.amount.toFloat(),
                subscriptionRoot.id, jioSubscriptionPurchase.txnId
            )
            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)
        }
    }

    private suspend fun getJioReceipt(
        receiptId: String,
        call: ApplicationCall
    ): JioSubscriptionStatus? {
        return try {
            jioApiManager.getSubscriptionStatus(receiptId, call)
        } catch (e: Exception) {
            logger.error("cant find Jio subscription token: ${receiptId.take(10)}")
            null
        }
    }
}
