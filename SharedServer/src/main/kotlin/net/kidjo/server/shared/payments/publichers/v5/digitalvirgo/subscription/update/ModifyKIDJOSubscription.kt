package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.update
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.DVSubscriptionApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.cancel.cancelKIDJOSubscription
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private val logger  = LoggerFactory.getLogger("MODIFY: KIDJO Subscription BY DV Status - ")
suspend fun modifyKIDJOSubscriptionByDVStatus(
    subIndent: String,
    userIndent: String,
    msisdn: String?,
    subCountryCode: String,
    operatorName: String,
    userLocale: String,
    dvNextBillingDate: LocalDateTime,
    offerId: Int,
    packageId: Int?,
    correlationId: String? = null,
    subStatus: String,
    dvSubscription: DVSubscriptionApi,
    databaseController: DatabaseController): Long? {
    try {
        var subscription: SubscriptionRoot? = null
        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                subscription = databaseController.subscription_getByToken(subIndent)
            }
        }
        if(subscription != null){
            when (subStatus) {
                SubscriptionStatusType.TERMINATED.name -> {
                    logger.info("MODIFY KIDJO SUBSCRIPTION id: $subIndent, status:$subStatus ")
                    val resultCancel =
                        cancelKIDJOSubscription(
                            nextBillDate = LocalDateTime.now(),
                            subToken = subIndent,
                            subStatus = SubscriptionStatusType.fromRaw(subStatus).raw,
                            correlationId = correlationId,
                            databaseController = databaseController
                        )

                    if (!resultCancel) {
                        logger.error("ERROR: TERMINATE SUBSCRIPTION subscription id: $subIndent, status:$subStatus, dvNextBillingDate: $dvNextBillingDate ")
                        return null
                    }
                    logger.info("SUCCESS: CANCELING KIDJO subscription id: $subIndent, status:$subStatus, dvNextBillingDate: $dvNextBillingDate ")
                    return subscription?.userId
                }
                else -> {
                    logger.info("MODIFY KIDJO SUBSCRIPTION id: $subIndent, status:$subStatus ")
                        val resultUpdate =
                            updateKIDJOSubscription(
                                nextBillDate = if(subscription!!.nextBillDate >= dvNextBillingDate ) dvNextBillingDate else subscription!!.nextBillDate,
                                subToken = subIndent,
                                subStatus = SubscriptionStatusType.fromRaw(subStatus).raw,
                                correlationId = correlationId,
                                databaseController = databaseController
                            )

                        if (!resultUpdate) {
                            logger.error("ERROR: UPDATING KIDJO SUBSCRIPTION id: $subIndent, status: $subStatus, dvNextBillingDate: $dvNextBillingDate ")
                            return null
                        }
                        logger.info("SUCCESS: UPDATING SUBSCRIPTION id: $subIndent, status:$subStatus, dvNextBillingDate: $dvNextBillingDate ")
                        return subscription?.userId
                }
            }
        } else {
            when (subStatus) {
                SubscriptionStatusType.SUBSCRIBED.raw,
                SubscriptionStatusType.EXPIRED.raw,
                SubscriptionStatusType.SUSPENDED.raw,
                SubscriptionStatusType.CANCELLED.raw,
                SubscriptionStatusType.ACTIVE.raw -> {
                    logger.info("MODIFY KIDJO SUBSCRIPTION id: $subIndent, status:$subStatus ")
                    val userId = dvSubscription.createDVPASSAndKIDJOAccount(
                        subIndent = subIndent,
                        userIndent = userIndent,
                        msisdn = msisdn,
                        subCountryCode = subCountryCode,
                        operatorName = operatorName,
                        userLocale = userLocale,
                        nextBillingDate = if(dvNextBillingDate <= LocalDateTime.now()) LocalDateTime.now().plusDays(3) else dvNextBillingDate,
                        offerId = offerId,
                        packageId = packageId?:0,
                        correlationId = correlationId,
                        subStatus = subStatus,
                        ""
                    )
                    if (userId == null) {
                        logger.error("ERROR: CREATING SUBSCRIPTION subType: $subStatus: Disable KIDJO Subscription: $dvNextBillingDate ")
                        return null
                    }
                    logger.info("SUCCESS:CREATING SUBSCRIPTION subType: $subStatus: Disable KIDJO Subscription: $dvNextBillingDate, userId: $userId ")
                    return userId
                }
                else -> {
                    logger.info("INFO: NOTHING id: $subIndent, status:$subStatus ")
                    return null
                }
            }
        }
    } catch (e: Exception) {
        logger.error("ERROR: MODIFY KIDJO SUBSCRIPTION, ${e.localizedMessage}")
        return null
    }
}
