package net.kidjo.server.shared.database.v5

import net.kidjo.common.models.Game
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.creator.game

fun DatabaseController.v5_game_getByFolder(folderId: Long): ArrayList<Game> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT games.id,game_folders.isPremium,games.type,game_folders.difficulty,game_folders.locationIndex FROM games JOIN game_folders ON game_folders.gameId = games.id WHERE game_folders.folderId = ? AND game_folders.isActive = 1 GROUP BY game_folders.id ORDER BY game_folders.order ASC ")
    statement.setLong(1, folderId)

    val results = statement.executeQuery()
    val games = ArrayList<Game>()
    while (results.next()) games.add(objectCreator.game(results))

    statement.close()
    connection.close()
    return games
}
