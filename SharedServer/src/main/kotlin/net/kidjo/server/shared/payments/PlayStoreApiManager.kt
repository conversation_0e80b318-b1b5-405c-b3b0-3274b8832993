package net.kidjo.server.shared.payments

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
import com.google.api.client.googleapis.auth.oauth2.GooglePublicKeysManager
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport
import com.google.api.client.json.jackson2.JacksonFactory
import com.google.api.services.androidpublisher.AndroidPublisher
import com.google.api.services.androidpublisher.AndroidPublisherScopes
import com.google.api.services.androidpublisher.model.SubscriptionPurchase
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.tools.Config


class PlayStoreApiManager(internal val config: Config) {
    var credential = GoogleCredential()
    val googleKeys: GooglePublicKeysManager
    val playstorePublisher: AndroidPublisher

    init {
        val loader = Thread.currentThread().contextClassLoader
        val identityJSONFile = loader.getResourceAsStream("credentials/Kidjo-ea29673c0f0b.json")
        credential = GoogleCredential.fromStream(identityJSONFile)
            .createScoped(arrayListOf(AndroidPublisherScopes.ANDROIDPUBLISHER))

        val transport = GoogleNetHttpTransport.newTrustedTransport()
        val json = JacksonFactory.getDefaultInstance()
        googleKeys = GooglePublicKeysManager(transport, json)
        playstorePublisher = AndroidPublisher.Builder(transport, json, credential)
            .setApplicationName("net.kidjo.app.android")
            .build()
    }

    fun getVerifyAndUserInfo(user: User): Boolean {
        val verifier = GoogleIdTokenVerifier(googleKeys)
        val token = verifier.verify(user.authToken)
        if (token != null) {
            val payload = token.payload
            user.email = payload.email
            user.authId = payload.get("sub") as? String ?: payload.email
            user.emailIsConfirmed = payload.emailVerified
            user.name = payload.get("name") as? String ?: ""
            return true
        }
        return false
    }

    fun getSubscription(iapId: String, token: String, subscriptionType: String): SubscriptionPurchase {
        var playStorePackage = getPlayStorePackage(subscriptionType)
        try {
            return playstorePublisher.purchases().subscriptions().get(
                playStorePackage,
                iapId, token
            ).execute()
        } catch (e: Exception) {
            throw e
        }
    }

    fun getSubscription(subscriptionRoot: SubscriptionRoot): SubscriptionPurchase {
        var playStorePackage = getPlayStorePackage(subscriptionRoot.subscriptionType.raw)
        try {
            return playstorePublisher.purchases().subscriptions().get(
                playStorePackage,
                subscriptionRoot.iapId, subscriptionRoot.subscriptionToken
            ).execute()
        } catch (e: Exception) {
            throw e
        }
    }

    private fun getPlayStorePackage(subscriptionType: String): String {
        return when (subscriptionType) {
            SubscriptionRoot.SubscriptionType.KIDJO_TV.raw -> config.playStoreKidjoTvPackageName
            SubscriptionRoot.SubscriptionType.KIDJO_BOOKS.raw -> config.playStoreKidjoBooksPackageName
            SubscriptionRoot.SubscriptionType.KIDJO_GAMES.raw -> config.playStoreKidjoGamesPackageName
            else -> ""
        }
    }
}
