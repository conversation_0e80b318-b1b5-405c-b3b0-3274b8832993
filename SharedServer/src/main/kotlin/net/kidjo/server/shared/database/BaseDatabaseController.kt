package net.kidjo.server.shared.database

import com.mysql.jdbc.jdbc2.optional.MysqlDataSource
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import net.kidjo.server.shared.database.creator.ResultSetObjectCreator
import net.kidjo.server.shared.tools.*
import org.intellij.lang.annotations.Language
import org.jetbrains.exposed.sql.Database
import org.slf4j.LoggerFactory
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.sql.Statement
import java.time.format.DateTimeFormatter
import java.util.*

class DatabaseController(val config: Config,
                              internal val validator: Validator,
                              val encryptionController: EncryptionController,
                              val iapManager: IAPManager,
                              val utility: Utility) {
    internal val logger=LoggerFactory.getLogger(this::class.java);
    internal val mysqlDateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    internal val mysqlDateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    val dataSource: HikariDataSource
    internal val objectCreator = ResultSetObjectCreator(encryptionController, iapManager, utility)
    init {
        val mysqlDataSource = MysqlDataSource()
        mysqlDataSource.databaseName = config.db_name
        mysqlDataSource.serverName = config.db_host
        mysqlDataSource.user = config.db_user
        mysqlDataSource.setPassword(config.db_password)
        mysqlDataSource.useSSL=false

        val hikariConfig = HikariConfig()
        hikariConfig.dataSource = mysqlDataSource
        hikariConfig.maximumPoolSize = config.db_maxConnections
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true")
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "500")
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048")
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true")
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true")
        hikariConfig.addDataSourceProperty("useLocalTransactionState", "true")
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true")
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true")
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true")
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true")
        hikariConfig.addDataSourceProperty("maintainTimeStats", "true")
        hikariConfig.addDataSourceProperty("useSSL","false")

        dataSource = HikariDataSource(hikariConfig)
        Database.connect(dataSource)
    }

    inline fun generic_singleRowQuery(@Language("SQL") query: String, crossinline setArguments: (statement: PreparedStatement) -> Unit): HashMap<String, Any>? {
        val connection = dataSource.connection
        val statement = connection.prepareStatement(query)
        setArguments(statement)
        val results = statement.executeQuery()
        val map: HashMap<String, Any>? = if (results.next()) results.createHashMap() else null

        statement.close()
        connection.close()
        return map
    }
    inline fun generic_insert(@Language("SQL") insert: String, crossinline setArguments: (statement: PreparedStatement) -> Unit): Boolean{
        val connection = dataSource.connection
        val statement = connection.prepareStatement(insert)
        setArguments(statement)
        val success = statement.executeAndCheck()
        statement.close()
        connection.close()
        return success
    }

}

fun ResultSet.createHashMap(): HashMap<String, Any> {
    val map = HashMap<String, Any>()
    val metadata = metaData
    val columns = metadata.columnCount
    for (i in 1..columns) {
        map.put(metadata.getColumnName(i), this.getObject(i))
    }
    return map
}

fun PreparedStatement.executeAndCheck(): Boolean {
    try {
        this.execute()
        return true
    } catch (e: Exception) {
        println(e.localizedMessage)
        return false
    }
}
fun Statement.executeBatchAndCheck(): Boolean {
    try {
        this.executeBatch()
        return true
    } catch (e: Exception) {
        println(e.localizedMessage)
        return false
    }
}
