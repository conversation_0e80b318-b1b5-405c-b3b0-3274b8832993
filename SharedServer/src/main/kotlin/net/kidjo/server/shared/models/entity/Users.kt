package net.kidjo.server.shared.models.entity

import net.kidjo.server.shared.models.User
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Table

object Users : Table("users") {
    val id = long("id").autoIncrement()
    val name = varchar("name", 255)
    val email = varchar("email", 336)
    val emailIsConfimed = bool("email_is_confirmed")
    val password = varchar("password", 255)
    val lastChangedPassword = varchar("last_changed_password", 255)
    val wrongPasswordCount = integer("wrong_password_count")
    val authType: Column<User.AuthType> = customEnumeration("authType", null, fromDb = {
        User.AuthType.fromRaw(it as String)
    }, toDb = {
        it.name
    })
    val authId = varchar("authId", 336)
    val countryId = integer("country_id")

}
