package net.kidjo.server.shared.cachedatabase

import net.kidjo.server.shared.database.videos_get
import net.kidjo.server.shared.extensions.toJSON
import net.kidjo.server.shared.json.v3.cards_JSONToVideo
import net.kidjo.server.shared.json.v3.cards_videoToJSON
import net.kidjo.server.shared.models.Video

private const val RECENT_VIDEOS_MAX = 10L
private const val RECENT_VIDEOS_EXPIRATION =  7 * 24L * 60L * 60L //1 week

fun CacheDatabase.recentVideos_add(kidId: Long, videoId: String) {
    val commands = connection.sync()
    val key = CacheDatabase.KEY_RECENT_VIDEOS + kidId.toString()
    val jsonList = commands.lrange(key,0,-1)
    var found = false

    if (jsonList == null || jsonList.size == 0) {
    } else {
        for (i in jsonList.indices) {
            val jsonString = jsonList[i]
            val json = jsonString.toJSON() ?: continue

            val encodedId = json.optString(jsonCreator.CARD_VIDEO_ID)
            if (encodedId == videoId) {
                found = true
                commands.lrem(key,1,jsonString)
                commands.lpush(key,jsonString)
                break
            }
        }
    }


    if (found == false) {
        val id = encryptionController.decodeVideoId(videoId)
        val video = databaseController.videos_get(id)
        if (video != null) commands.lpush(key,jsonCreator.cards_videoToJSON(video).toString())
    }
    commands.ltrim(key,0, RECENT_VIDEOS_MAX)
    commands.expire(key,RECENT_VIDEOS_EXPIRATION)
}

fun CacheDatabase.recentVideos_get(kidId: Long): ArrayList<Video>? {
    val commands = connection.sync()
    val key = CacheDatabase.KEY_RECENT_VIDEOS + kidId.toString()
    val jsonList = commands.lrange(key,0,-1)
    if (jsonList == null || jsonList.size == 0) return null

    val list = ArrayList<Video>()

    jsonList.forEach {
        val json = it.toJSON()
        if (json != null) list.add(jsonCreator.cards_JSONToVideo(json))
    }
    return list
}