package net.kidjo.server.shared.payments

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.notification.DVNotificationApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.operation.DVOperationApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.sms.DVSenditoApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.DVSubscriptionApi
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.shared.tools.Validator

class DigitalVirgoApiManager(
    config: Config,
    utility: Utility,
    validator: Validator,
    encryptionController: EncryptionController,
    databaseController: DatabaseController
) {

    val sendito = DVSenditoApi(
        config = config,
        utility = utility,
        databaseController = databaseController
    )
    val operation = DVOperationApi(
        config = config,
        databaseController = databaseController
    )
    val subscription = DVSubscriptionApi(
        config = config,
        validator = validator,
        utility = utility,
        encryptionController = encryptionController,
        databaseController = databaseController,
        dvSendito = sendito
    )
    val notification = DVNotificationApi(
        dvSubscription = subscription,
        databaseController = databaseController
    )
}