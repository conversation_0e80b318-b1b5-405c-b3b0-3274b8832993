package net.kidjo.server.shared.models

import io.ktor.server.auth.Principal
import java.io.Serializable

data class User(
    var id: String,
    var email: String,
    var emailIsConfirmed: <PERSON><PERSON><PERSON>,
    var name: String,
    var countryId: Int = 0,
    var hashedPassword: String = "NONE",
    var password: String = "NONE"
) : Serializable, Principal {

    var isSubscribed = false
    var brainTreeId: String = "" //private

    var isSignedIn: Boolean = false
    var authId: String = ""
    var authToken: String = ""

    var authType: AuthType = AuthType.EMAIL
    var oAuthType: OAuthType = OAuthType.NONE

    fun copy(user: User) {
        this.id = user.id
        this.isSubscribed = user.isSubscribed
        this.email = user.email
        this.emailIsConfirmed = user.emailIsConfirmed
        this.name = user.name
        this.countryId = user.countryId
        this.hashedPassword = user.hashedPassword
        this.brainTreeId = user.brainTreeId
        this.isSignedIn = user.isSignedIn
        this.authId = user.authId
        this.authToken = user.authToken
        this.authType = user.authType
        this.oAuthType = user.oAuthType
    }

    fun getLongId() = getId(id)

    override fun toString() = "$id/$name/$email"

    enum class AuthType(val raw: String) {

        MIXED("mixed"),
        EMAIL("email"),
        FAKE_EMAIL("fake_email"),
        OAUTH("OAUTH")

        ;

        companion object {
            fun fromRaw(raw: String): AuthType {
                return when (raw) {
                    MIXED.raw -> MIXED
                    EMAIL.raw -> EMAIL
                    OAUTH.raw -> OAUTH
                    FAKE_EMAIL.raw -> FAKE_EMAIL
                    else -> EMAIL
                }
            }
        }
    }

    enum class OAuthType(val raw: String) {

        NONE("none"),
        GOOGLE("google"),
        FACEBOOK("facebook"),
        CAFEYN("cafeyn")
        ;

        companion object {
            fun fromRaw(raw: String): OAuthType {
                return when (raw) {
                    NONE.raw -> NONE
                    GOOGLE.raw -> GOOGLE
                    FACEBOOK.raw -> FACEBOOK
                    CAFEYN.raw -> CAFEYN
                    else -> NONE
                }
            }
        }
    }

    companion object {
        const val NO_ID = "0"
        const val NO_SERVER_ID = 0L

        fun isNameValid(name: String): Boolean {
            return true
        }

        fun getEmptyUser() = User(
            id = NO_ID,
            email = "",
            emailIsConfirmed = false,
            name = "",
            countryId = 0
        )

        fun getId(id: String): Long {
            return try {
                id.toLong()
            } catch (e: Exception) {
                NO_SERVER_ID
            }
        }
    }
}
