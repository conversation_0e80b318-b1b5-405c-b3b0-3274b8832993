package net.kidjo.server.shared.payments.background

import net.kidjo.server.shared.models.SubscriptionCheckException
import net.kidjo.server.shared.models.SubscriptionCheckResult
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.JioSubscriptionStatus
import net.kidjo.server.shared.tools.payments.PaymentManager
import java.lang.StringBuilder
import java.time.LocalDateTime

fun PaymentManager.checkJioSubscriptionStatus(
    subscriptionRoot: SubscriptionRoot, updateStringBuilder: StringBuilder,
    cancelStringBuilder: StringBuilder
): SubscriptionCheckResult {
    val jioSubscriptionStatus: JioSubscriptionStatus?
    try {
        jioSubscriptionStatus = jioApiManager.getSubscriptionStatus(
            subscriptionRoot.subscriptionToken
        )
    } catch (e: SubscriptionCheckException) {
        throw e
    }
    if (jioSubscriptionStatus == null) {
        throw SubscriptionCheckException("Could not find JIO subscription")
    }
    val status: SubscriptionCheckResult.Status
    val priceInUSD: Float = jioSubscriptionStatus.amount.toFloat()

    val nextBillingDate = LocalDateTime.parse(jioSubscriptionStatus.endDate)

    if (jioSubscriptionStatus.transactionStatus == "SUCCESS") {
        status = SubscriptionCheckResult.Status.ACTIVE
        updateStringBuilder.appendln(
            "UPDATE = JIO Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    } else {
        status = SubscriptionCheckResult.Status.CANCEL_USER
        cancelStringBuilder.appendln(
            "CANCEL = JIO Subscription id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}, NEW next billing date:${nextBillingDate} "
        )
    }
    return SubscriptionCheckResult(status, priceInUSD, nextBillingDate, null)
}
