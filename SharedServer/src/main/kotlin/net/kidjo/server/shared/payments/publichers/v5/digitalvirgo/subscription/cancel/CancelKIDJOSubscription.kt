package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.cancel

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_cancelFull_virgo
import net.kidjo.server.shared.database.subscription_cancel_vigro
import net.kidjo.server.shared.database.updateDVPairedSubscriptionStatus
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private val logger = LoggerFactory.getLogger("cancelKIDJOSubscription - ")

@Throws
fun cancelKIDJOSubscription(
    nextBillDate: LocalDateTime,
    subToken: String,
    subStatus: String? = null,
    correlationId: String? = null,
    databaseController: DatabaseController,
): Boolean = try {

    if (!databaseController.subscription_cancelFull_virgo(subToken, nextBillDate)) {
        logger.error("CANCEL ERROR: A problem occurred while unsubscribe Kidjo-TV sub token: $subToken")
        throw IllegalArgumentException("Can not take subscription_cancelFull_virgo of false.")
    }

    if (!databaseController.updateDVPairedSubscriptionStatus(subStatus, subToken, correlationId)) {
        logger.error("CANCEL ERROR: A problem occurred while update sub status: $subStatus")
        throw IllegalArgumentException("Can not take updateDVPairedSubscriptionStatus of false.")
    }

    logger.info("CANCEL SUCCESS: Successfully unsubscribe kidjo-tv sub token: $subToken")
    true

} catch (e: Exception) {
    logger.error("Error DV canceling subscription, ${e.localizedMessage}")
    throw e
}


fun cancelSubscription(
    subToken: String,
    databaseController: DatabaseController,
    subStatus: String?,
    correlationId: String?,
): Boolean = try {
    if (!databaseController.subscription_cancel_vigro(subToken)) {
        logger.error("CANCEL ERROR: A problem occurred while unsubscribe Kidjo-TV sub token: $subToken")
        throw IllegalArgumentException("Can not take subscription_cancelFull_virgo of false.")
    }
    if (!databaseController.updateDVPairedSubscriptionStatus(subStatus, subToken, correlationId)) {
        logger.error("CANCEL ERROR: A problem occurred while update sub status: $subStatus")
        throw IllegalArgumentException("Can not take updateDVPairedSubscriptionStatus of false.")
    }
    logger.info("Cancel is successfully done for sub-token$subToken");
    true
} catch (e: Exception) {
    logger.error("Error DV canceling subscription, ${e.localizedMessage}")
    throw e
}
