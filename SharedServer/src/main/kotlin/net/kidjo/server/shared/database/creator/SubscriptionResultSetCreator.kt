package net.kidjo.server.shared.database.creator

import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import java.sql.ResultSet

fun ResultSetObjectCreator.subscription(resultSet: ResultSet, prefix: String = ""): SubscriptionRoot {
    val id = resultSet.getLong(prefix + "id")
    val userId = resultSet.getLong(prefix + "userId")
    val isActive = resultSet.getBoolean(prefix + "isActive")
    val isRenewing = resultSet.getBoolean(prefix + "isRenewing")
    val stillInFreeTrial = resultSet.getBoolean(prefix + "stillInFreeTrial")
    val paymentType = SubscriptionRoot.PaymentType.fromRaw(resultSet.getString(prefix + "paymentType"))
    val subscriptionType = SubscriptionRoot.SubscriptionType.fromRaw(resultSet.getString(prefix + "subscriptionType"))
    val paymentId = resultSet.getString(prefix + "paymentId")
    val platformPurchaseId = resultSet.getString(prefix + "platformPurchaseId")
    val storeId = Device.StorePlatform.fromRow(resultSet.getString(prefix + "storeId"))
    val paymentStateId= resultSet.getString(prefix + "paymentStateId")
    val iapId = resultSet.getString(prefix + "iapId")
    val purchasingSessionId = resultSet.getString(prefix + "purchasingSessionId")
    val subscriptionToken = resultSet.getString(prefix + "subscriptionToken") ?: ""
    val subscriptionTokenHash = resultSet.getString(prefix + "subscriptionTokenHash")
    val accountCouponId = resultSet.getLong(prefix + "accountCouponId")
    val nextBillDate = resultSet.getTimestamp(prefix + "nextBillDate").toLocalDateTime()
    val lastCheckDate = resultSet.getTimestamp(prefix + "lastCheckDate").toLocalDateTime()
    val updatedAt = resultSet.getTimestamp(prefix + "updated_at").toLocalDateTime()
    val createdAt = resultSet.getTimestamp(prefix + "created_at").toLocalDateTime()
    val operatorName = resultSet.getString(prefix + "operatorName")
    val isTest = resultSet.getBoolean(prefix + "isTest")

    return SubscriptionRoot(id, userId, isActive, isRenewing, stillInFreeTrial, paymentType, subscriptionType, paymentId,
            platformPurchaseId, storeId, paymentStateId, iapId, purchasingSessionId, subscriptionToken, subscriptionTokenHash,
            accountCouponId, nextBillDate, lastCheckDate, updatedAt, createdAt, isTest, operatorName ?: "")
}
fun ResultSetObjectCreator.sevamedia_subscription_info(resultSet: ResultSet): SevamediaSubscription {

    val email = resultSet.getString("email")
    val endDate = resultSet.getString("nextBillDate")
    val couponId = resultSet.getString("couponId")
    val couponDuration = resultSet.getString("duration")
    val countryName = resultSet.getString("name")
    val countryCode = resultSet.getString("short")
    val mobileProvider = resultSet.getString("operatorName")

    return SevamediaSubscription(email, endDate, couponId, couponDuration, countryName, countryCode, mobileProvider)
}

data class SevamediaSubscription(
    val email: String,
    val endDate: String,
    val couponId: String,
    val couponDuration: String,
    val countryName: String?,
    val countryCode: String?,
    val mobileProvider:String
)