package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object CafeynUsersInfo : Table("cafeyn_users_info") {

    val id = integer("id").autoIncrement()
    val email = varchar("email", 255)
    val userId = integer("user_id")
    val partnerName = varchar("partner_name", 255)
    val partnerId = integer("partner_id")
    val serviceName = varchar("service_name", 255)
    val eventType = varchar("event_type", 255)
    val requestId = varchar("request_id",255)
    val signature = varchar("signature", 255)
    val timestamp = datetime("timestamp")

}
