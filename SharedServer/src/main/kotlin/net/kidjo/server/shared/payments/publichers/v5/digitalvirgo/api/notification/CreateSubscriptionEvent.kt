package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.notification
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getSubscriptionIdByEmail
import net.kidjo.server.shared.database.subscription_update_nextBillingDateBySubToken
import net.kidjo.server.shared.database.updateDVPairedSubscriptionStatus
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.entity.MncMcc
import net.kidjo.server.shared.models.entity.PartnerSubscriptionLogs
import net.kidjo.server.shared.models.entity.SubscriptionsRoot
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.DVSubscriptionApi
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private val logger = LoggerFactory.getLogger("createUnsubscriptionEvent - ")
suspend fun createSubscriptionEvent(
    subIndent: String,
    userIndent: String,
    msisdn: String?,
    subCountryCode: String,
    operatorName: String,
    userLocale: String,
    dvNextBillingDate: LocalDateTime,
    offerId: Int,
    packageId: Int?,
    correlationId: String? = null,
    subStatus: String? = null,
    dvSubscription: DVSubscriptionApi,
    databaseController: DatabaseController,
    alias: String?,
    email: String?
): Long? {
    try {
        val identifier = msisdn ?: alias // Use alias if msisdn is null
        var subscription: SubscriptionRoot? = null
        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                subscription = databaseController.getSubscriptionIdByEmail(identifier!!)
                if (subscription != null && LocalDateTime.now().plusDays(1) > subscription!!.nextBillDate) {
                    SubscriptionsRoot.update({
                        SubscriptionsRoot.id.eq(subscription!!.id)
                    }) {
                        it[isActive] = true
                        it[nextBillDate] = if (dvNextBillingDate <= LocalDateTime.now()) LocalDateTime.now()
                            .plusDays(3) else dvNextBillingDate
                        it[lastCheckDate] = LocalDateTime.now()
                    }
                }
            }
        }

        if (subStatus == null || subStatus == SubscriptionStatusType.TERMINATED.raw || subStatus == SubscriptionStatusType.NONE.raw) {
            logger.error("ERROR: CREATING SUBSCRIPTION subStatus: $subStatus: Disable KIDJO Subscription: $dvNextBillingDate ")
            return null
        }

        if (SubscriptionStatusType.SUSPENDED.raw.equals(subStatus)) {
            logger.info("Suspending the subscription for {}", subIndent)
            databaseController.updateDVPairedSubscriptionStatus(
                subStatus,
                subIndent,
                correlationId
            )
            val result = databaseController.subscription_update_nextBillingDateBySubToken(
                LocalDateTime.now().plusDays(15),
                subIndent
            )
            if (!result) {
                logger.error("Unable to change the status to suspended- for $subIndent");
                return null
            }
        }

        if (subscription == null) {
            val userId = dvSubscription.createDVPASSAndKIDJOAccount(
                subIndent = subIndent,
                userIndent = userIndent,
                msisdn = identifier,
                subCountryCode = subCountryCode,
                operatorName = operatorName,
                userLocale = userLocale,
                nextBillingDate = if (dvNextBillingDate <= LocalDateTime.now()) LocalDateTime.now()
                    .plusDays(3) else dvNextBillingDate,
                offerId = offerId,
                packageId = packageId ?: 0,
                correlationId = correlationId,
                subStatus = subStatus,
                email=email
            )
            if (userId == null) {
                logger.error("ERROR: CREATING SUBSCRIPTION subType: $subStatus: Disable KIDJO Subscription: $dvNextBillingDate ")
                return null
            }
            logger.info("SUCCESS:CREATING SUBSCRIPTION subType: $subStatus: Disable KIDJO Subscription: $dvNextBillingDate, userId: $userId ")
            return userId
        }

        logger.info("EXIT: CREATE SUBSCRIPTION EVENT ")
        return null
    } catch (e: Exception) {
        logger.error("ERROR: CREATE SUBSCRIPTION EVENT, ${e.localizedMessage}")
        return null
    }
}

fun insertCountryAndTelco(mncmcc: String, userId: Int): Boolean {

    var country = "";
    var telco = ""
    var subscriptionId = 0
    var result = false
    transaction {
        logger.info("MCCMNC : $mncmcc")
        MncMcc.select { concat(MncMcc.mcc, MncMcc.mnc) eq "6040" }
            .mapNotNull { country = it[MncMcc.country]; telco = it[MncMcc.network] }
        logger.info("Country : $country and Telco : $telco")
        net.kidjo.server.shared.models.entity.SubscriptionRoot.selectAll()
            .adjustWhere { net.kidjo.server.shared.models.entity.SubscriptionRoot.userId.eq(userId) }
            .mapNotNull { subscriptionId = it[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt() }
        logger.info("SubscriptionId : $subscriptionId exits for userId : $userId ")

        if (telco.isNotEmpty() && country.isNotEmpty()) {

            val isExist = UserPartnerSubscription.select {
                (UserPartnerSubscription.subscriptionId eq subscriptionId) and
                        (UserPartnerSubscription.userId eq userId)
            }.count() > 0
            if (!isExist) {
                result = UserPartnerSubscription.insert {
                    it[UserPartnerSubscription.country] = country
                    it[subPartner] = telco
                    it[UserPartnerSubscription.userId] = userId
                    it[UserPartnerSubscription.subscriptionId] = subscriptionId
                }.insertedCount > 0
            } else {
                logger.info("Subscription already exists in user_partner_subscription with id $subscriptionId")
            }

            if (result) {
                logger.info("Inserted values into user_partner_subscription")
            }
        }
    }
    return result
}
fun insertLogs(logs: String, partnerName: String): Boolean {
    return transaction {
        PartnerSubscriptionLogs.insert {
            it[request] = logs
            it[PartnerSubscriptionLogs.partnerName] = partnerName
        }.insertedCount > 0
    }
}