package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table

object Discount : Table("discounts") {
    val id = integer("id").autoIncrement()
    val discountName = varchar("discountName", 256)
    val active = bool("active").default(false)
    val freeTrial = bool("freeTrial").default(false)
    val isPriceDiscount = bool("isPriceDiscount").default(false)
    override val primaryKey: PrimaryKey
        get() = PrimaryKey(id)
}
