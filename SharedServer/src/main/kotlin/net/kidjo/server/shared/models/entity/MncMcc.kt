package net.kidjo.server.shared.models.entity

import org.jetbrains.exposed.sql.Table


object MncMcc : Table("mccmnc") {
    val id = long("id").autoIncrement()
    val mcc = integer("mcc")
    val mnc = integer("mnc")
    val iso = varchar("iso", 50)
    val country = varchar("country", 50)
    val countryCode = integer("country_code")
    val network = varchar("network", 50)
    override val primaryKey = PrimaryKey(id)
}