package net.kidjo.server.shared.json.v3

import net.kidjo.common.models.Folder
import net.kidjo.common.models.Game
import net.kidjo.common.models.Language
import net.kidjo.server.shared.models.InternalData
import net.kidjo.server.shared.models.Video
import org.json.JSONArray
import org.json.JSONObject

private fun SetBaseFolderJSON(folder: Folder, json: JSONObject) {
    json.put("type", "folder")
    json.put("id", folder.id)
    if (folder.namedId != "") json.put("namedId", folder.namedId)
    json.put("contentType", folder.contentType.raw)
    json.put("mediaType", folder.mediaType.raw)
}

fun JsonObjectCreatorV3.new_cards_folderNormalToJSON(folder: Folder, videos: List<Video>): JSONObject {
    val json = JSONObject()
    SetBaseFolderJSON(folder, json)
    val subcards = JSONArray()

    videos.forEach { subcards.put(new_cards_videoToJSON(it)) }
    json.put("subcards", subcards)

    return json
}

fun JsonObjectCreatorV3.cards_folderNormalToJSON(folder: Folder, videos: List<Video>): JSONObject {
    val json = JSONObject()
    SetBaseFolderJSON(folder, json)
    val subcards = JSONArray()

    videos.forEach { subcards.put(cards_videoToJSON(it)) }
    json.put("subcards", subcards)

    return json
}

fun JsonObjectCreatorV3.cards_folderNormalToJSON(videos: List<Video>): JSONObject {
    val json = JSONObject()
    val subcards = JSONArray()
    videos.forEach { subcards.put(cards_videoToJSON(it)) }
    json.put("videos", subcards)

    return json
}
fun JsonObjectCreatorV3.game_toJson(game: Game): JSONObject {
    val json = JSONObject()
    json.put("variant","game")
    json.put("type","game")
    json.put("id",game.id.toString())
    json.put("isLocked",game.isLocked)
    json.put("gameType",game.type.raw)
    json.put("dif",game.difficulty.raw)
    return json
}

fun JsonObjectCreatorV3.new_cards_gameFolderToJSON(folder: Folder, games: List<Game>): JSONObject {
    val json = JSONObject()
    SetBaseFolderJSON(folder, json)
    val subcards = JSONArray()
    games.forEach { subcards.put(new_game_toJson(it)) }
    json.put("subcards", subcards)

    return json
}
fun JsonObjectCreatorV3.new_game_toJson(game: Game): JSONObject {
    val json = JSONObject()
    json.put("type","game")
    json.put("id",game.id.toString())
    json.put("isLocked",game.isLocked)
    json.put("gameType",game.type.raw)
    json.put("dif",game.difficulty.raw)
    json.put("position", game.position)
    return json
}

fun JsonObjectCreatorV3.cards_gameFolderToJSON(folder: Folder, games: List<Game>): JSONObject {
    val json = JSONObject()
    SetBaseFolderJSON(folder, json)
    val subcards = JSONArray()
    games.forEach { subcards.put(game_toJson(it)) }
    json.put("subcards", subcards)

    return json
}
fun JsonObjectCreatorV3.cards_folderHistoryToJSON(videos: List<Video>): JSONObject {
    return cards_folderNormalToJSON(Folder.GetHistoryFolder(),videos)
}
fun JsonObjectCreatorV3.filterFolderToJSON(folder: Folder): JSONObject {
    val json = org.json.JSONObject()
    json.put("id", folder.getLongId())
    if (folder.namedId == "")
        json.put("image", "${folder.id}.png")
    else
        json.put("image", "${folder.namedId}.png")
    json.put("title", folder.title)
    json.put("type", "folder")
    json.put("contentType", folder.contentType.raw)
    json.put("variant", "folder")
    json.put("isActive", folder.folderIsNotFiltered)

    return json
}

fun JsonObjectCreatorV3.new_filterFolderToJSON(folder: Folder): JSONObject {
    val json = org.json.JSONObject()
    json.put("id", folder.getLongId())
    if (folder.namedId == "")
        json.put("image", "${folder.id}.png")
    else
        json.put("image", "${folder.namedId}.png")
    json.put("title", folder.title)
    json.put("type", "folder")
    json.put("mediaType", folder.mediaType.raw)

    return json
}
fun JsonObjectCreatorV3.cards_soundToJSON(cardIndex: Int): JSONObject {
    val json = JSONObject()
    json.put("type", "sound")
    val soundId = when(cardIndex) {
        0 -> "cow"
        1 -> "frogs"
        2 -> "sea"
        3 -> "rooster"
        4 -> "phone"
        5 -> "car"

        else -> "cow" //same as 0
    }
    json.put("image","$soundId.png")
    json.put("sound",soundId)
    return json
}

fun JsonObjectCreatorV3.new_cards_soundToJSON(cardIndex: Int): JSONObject {
    val json = JSONObject()
    json.put("type", "sound")
    val soundId = when(cardIndex) {
        0 -> "cow"
        1 -> "frogs"
        2 -> "rooster"
        3 -> "plane"
        4 -> "cow"
        5 -> "frogs"
        6 -> "rooster"
        7 -> "plane"
        8 -> "cow"
        9 -> "frogs"
        10 -> "rooster"
        11 -> "plane"
        12 -> "cow"
        13 -> "frogs"
        14 -> "rooster"
        15 -> "plane"
        16 -> "cow"
        17 -> "frogs"
        18 -> "rooster"
        19 -> "plane"
        20 -> "cow"
        21 -> "frogs"
        22 -> "rooster"
        23 -> "plane"
        24 -> "cow"
        25 -> "frogs"
        26 -> "rooster"
        27 -> "plane"
        28 -> "cow"
        29 -> "frogs"
        30 -> "rooster"
        31 -> "plane"

        else -> "cow" //same as 0
    }
    json.put("image","$soundId.png")
    json.put("sound",soundId)
    return json
}
fun JsonObjectCreatorV3.cards_benefitsToJSON(cardIndex: Int, isPremium: Boolean, languageId: Int): JSONObject {
    val json = JSONObject()
    json.put("type", "benefits")
    json.put("title", GetBenefitsString(cardIndex, isPremium, languageId))
    return json
}
private fun GetBenefitsString(cardIndex: Int, isPremium: Boolean, languageId: Int): String {
    if (isPremium) {
        when (cardIndex) {
            0 -> when (languageId) {
                Language.ID_FRENCH -> return "Découvrez plus de vidéos dans d'autres langues dans Parents > Réglages."
                Language.ID_SPANISH -> return "Decubre más videos en otros idiomas en Padres> Configuración."
                Language.ID_PORTUGUESE -> return "Descubra mais vídeos nas outras linguas em Pais> Configurações."
                Language.ID_RUSSIAN -> return "Хотите сменить язык? Перейдите в раздел «Родители» > «Настройки»."
                Language.ID_ITALIAN -> return "Devi cambiare la lingua? Vai su \"Genitori\"> Impostazioni."
                Language.ID_GERMAN -> return "Müssen Sie die Sprache wechseln? Gehen Sie zu \"Eltern\" > Einstellungen."
                Language.ID_ARABIC -> return "تريد أن تغير اللغة؟ اذهب ل\"الآباء\"ثم الإعدادات."
                else /* Language.ID_ENGLISH */ -> return "Need to change the language? Go to \"Parents\" > Settings."
            }
            1 -> when (languageId) {
                Language.ID_FRENCH -> return "Pour accéder aux vidéos hors ligne, cliquez sur le bouton Sac à dos."
                Language.ID_SPANISH -> return "Par acceder a los videos sin conexión, pulse el botón Mochila"
                Language.ID_PORTUGUESE -> return "Para acessar vídeos off-line, carregue no botão Mochila."
                Language.ID_RUSSIAN -> return "Щёлкните по значку Рюкзака слева, чтобы получить доступ ко всем скачанным видео"
                Language.ID_ITALIAN -> return "Fai clic sull'icona Zaino a sinistra per vedere tutti i video offline."
                Language.ID_GERMAN -> return "Tippen Sie auf das Rucksacksymbol links, um Zugang zu allen Offline-Videos zu erhalten."
                Language.ID_ARABIC -> return "اضغط على أيقونة الحقيبة في اليسار لتفتح جمع مقاطع الفيديو عندم عدم وجود إنترنت."
                else /* Language.ID_ENGLISH */ -> return "Click on the Backpack icon on the left to get access to all offline videos."
            }

            //same as 0
            else -> when (languageId) {
                Language.ID_FRENCH -> return "Découvrez plus de vidéos dans d'autres langues dans Parents > Réglages."
                Language.ID_SPANISH -> return "Decubre más videos en otros idiomas en Padres> Configuración."
                Language.ID_PORTUGUESE -> return "Descubra mais vídeos nas outras linguas em Pais> Configurações."
                Language.ID_RUSSIAN -> return "Хотите сменить язык? Перейдите в раздел «Родители» > «Настройки»."
                Language.ID_ITALIAN -> return "Devi cambiare la lingua? Vai su \"Genitori\"> Impostazioni."
                Language.ID_GERMAN -> return "Müssen Sie die Sprache wechseln? Gehen Sie zu \"Eltern\" > Einstellungen."
                Language.ID_ARABIC -> return "تريد أن تغير اللغة؟ اذهب ل\"الآباء\"ثم الإعدادات."
                else /* Language.ID_ENGLISH */ -> return "Need to change the language? Go to \"Parents\" > Settings."
            }
        }
    } else {
        when (cardIndex) {
            0 -> when (languageId) {
                Language.ID_FRENCH -> return "Découvrez plus de vidéos dans d'autres langues dans Parents > Réglages."
                Language.ID_SPANISH -> return "Decubre más videos en otros idiomas en Padres> Configuración."
                Language.ID_PORTUGUESE -> return "Descubra mais vídeos nas outras linguas em Pais> Configurações."
                Language.ID_RUSSIAN -> return "Хотите сменить язык? Перейдите в раздел «Родители» > «Настройки»."
                Language.ID_ITALIAN -> return "Devi cambiare la lingua? Vai su \"Genitori\"> Impostazioni."
                Language.ID_GERMAN -> return "Müssen Sie die Sprache wechseln? Gehen Sie zu \"Eltern\" > Einstellungen."
                Language.ID_ARABIC -> return "تريد أن تغير اللغة؟ اذهب ل\"الآباء\"ثم الإعدادات."
                else /* Language.ID_ENGLISH */ -> return "Need to change the language? Go to \"Parents\" > Settings."
            }
            1 -> when (languageId) {
                Language.ID_FRENCH -> return "Pour accéder aux vidéos hors ligne, cliquez sur le bouton Sac à dos."
                Language.ID_SPANISH -> return "Par acceder a los videos sin conexión, pulse el botón Mochila"
                Language.ID_PORTUGUESE -> return "Para acessar vídeos off-line, carregue no botão Mochila."
                Language.ID_RUSSIAN -> return "Щёлкните по значку Рюкзака слева, чтобы получить доступ ко всем скачанным видео"
                Language.ID_ITALIAN -> return "Fai clic sull'icona Zaino a sinistra per vedere tutti i video offline."
                Language.ID_GERMAN -> return "Tippen Sie auf das Rucksacksymbol links, um Zugang zu allen Offline-Videos zu erhalten."
                Language.ID_ARABIC -> return "اضغط على أيقونة الحقيبة في اليسار لتفتح جمع مقاطع الفيديو عندم عدم وجود إنترنت."
                else /* Language.ID_ENGLISH */ -> return "Click on the Backpack icon on the left to get access to all offline videos."
            }
            2 -> when (languageId) {
                Language.ID_FRENCH -> return "Réglez le temps de visionnage quotidien maximum pour votre enfant dans Parents > Réglages."
                Language.ID_SPANISH -> return "Establezca el tiempo máximo de visualización diario  para su hijo en Padres> Configuración."
                Language.ID_PORTUGUESE -> return "Configure o tempo máximo de visualização diária da sua criança em Pais> Configurações."
                Language.ID_RUSSIAN -> return "Перейдите в раздел «Родители» > «Настройки», чтобы установить ограничение по времени для своих детей."
                Language.ID_ITALIAN -> return "Vai su \"Genitori\"> \"Impostazioni\" per impostare un limite di tempo giornaliero per i tuoi figli."
                Language.ID_GERMAN -> return "Gehen Sie zu \"Eltern\" > \"Einstellungen\", um die tägliche Zeitbegrenzung für Ihre Kinder festzulegen."
                Language.ID_ARABIC -> return "اذهب إلى\"الآباء\" ثم \"الإعدادات\"لتضع حد للفترة الزمنية لأطفالك."
                else /* Language.ID_ENGLISH */ -> return "Go to \"Parents\" > \"Settings\" in order to set up the daily time limit for your kids."
            }
            3 -> when (languageId) {
                Language.ID_FRENCH -> return "Découvrez des compilations toutes prêtes en cliquant sur les différentes cartes."
                Language.ID_SPANISH -> return "Descubre compilaciones listas pulsando en las diferentes tarjetas."
                Language.ID_PORTUGUESE -> return "Descubra as compilações prontas carregando nos diferentes cartões."
                Language.ID_RUSSIAN -> return "Щёлкните по карточке, чтобы посмотреть готовые сборники."
                Language.ID_ITALIAN -> return "Scopri compilation pronte da vedere toccando una carta."
                Language.ID_GERMAN -> return "Entdecke die \"noch anzuschauen\" Zusammenstellung wenn Sie auf eine Karte tippen."
                Language.ID_ARABIC -> return "اكتشف مجموعات مشاهدة جاهزة عندما تضغط على بطاقة."
                else /* Language.ID_ENGLISH */ -> return "Discover ready to watch compilations when you tap on a card."
            }
            4 -> when (languageId) {
                Language.ID_FRENCH -> return "Créez un profil pour chacun de vos enfants dans Parents."
                Language.ID_SPANISH -> return "Para crear une perful para cada uno de sus hijos, pulse en Padres."
                Language.ID_PORTUGUESE -> return "Para criar um perfil para cada uma das suas crianças, carregue em Pais."
                Language.ID_RUSSIAN -> return "Перейдите в раздел «Родители», чтобы создать профиль для каждого ребёнка."
                Language.ID_ITALIAN -> return "Vai su \"Genitori\" per creare un profilo per ciascuno dei tuoi figli."
                Language.ID_GERMAN -> return "Gehen Sie zu \"Eltern\", um ein Profil für jedes Ihrer Kinder zu erstellen."
                Language.ID_ARABIC -> return "اذهب ل\"الآباء\"لتُنشئ حسابًا لكل من أطفالك."
                else /* Language.ID_ENGLISH */ -> return "Go to \"Parents\" to create a profile for each of your kids."
            }
            5 -> when (languageId) {
                Language.ID_FRENCH -> return "S'amuser et apprendre, c'est facile sur Kidjo ! Avec plus de 1500 vidéos !"
                Language.ID_SPANISH -> return "¡Divertirse y aprender es fácil en Kidjo con más de 1500 videos!"
                Language.ID_PORTUGUESE -> return "Divertir-se e aprender é fácil no Kidjo! Com mais de 1500 vídeos!"
                Language.ID_RUSSIAN -> return "Веселись и учись с Kidjo! Более 1500 видео и игр"
                Language.ID_ITALIAN -> return "Divertiti e impara con Kidjo! Divertiti con oltre 1500 video e giochi"
                Language.ID_GERMAN -> return "Viel Spaß beim lernen mit Kidjo! Genießen Sie mehr als 1500 Videos und Spiele."
                Language.ID_ARABIC -> return "استمتع وتعلم مع كيدجو! استمتع بأكثر من 1500 فيديو ولعبة."
                else /* Language.ID_ENGLISH */ -> return "Have fun and learn with Kidjo! Enjoy more than 1500 videos & games"
            }
            6 -> when (languageId) {
                Language.ID_FRENCH -> return "S'amuser, apprendre, méli-mélo, c'est vous qui choisissez le type de programme."
                Language.ID_SPANISH -> return "Divertirse, aprender, mezcolanza, usted elige el tipo de programa."
                Language.ID_PORTUGUESE -> return "Divertir-se, aprender, troca-troca, é você quem escolhe o tipo de programa."
                Language.ID_RUSSIAN -> return "Развлекательные, обучающие или их комбинация, вы выбираете тип видео для своих детей!"
                Language.ID_ITALIAN -> return "Divertimento, formazione o un mix, scegli gli spettacoli che i tuoi bambini possono guardare!"
                Language.ID_GERMAN -> return "Ob Unterhaltung, Lernen oder beides, Sie entscheiden was Ihr Kind sehen darf."
                Language.ID_ARABIC -> return "المُتعة، الإثارة أو كليهما، انت تختار نوع العروض التي يشاهدها أطفالك!"
                else /* Language.ID_ENGLISH */ -> return "Entertainement, education or a mix, you chose the type of shows your kids can watch!"
            }
            7 -> when (languageId) {
                Language.ID_FRENCH -> return "Sélectionnez une à une les vidéos que votre enfant pourra regarder dans son profil."
                Language.ID_SPANISH -> return "Seleccione uno por uno los videos que su niño podrá ver en su perfil."
                Language.ID_PORTUGUESE -> return "Selecione um por um os vídeos que a sua criança poderá ver no seu perfil."
                Language.ID_RUSSIAN -> return "Перейдите в раздел «Родители» > «Профиль», чтобы вручную выбрать видео для своих детей."
                Language.ID_ITALIAN -> return "Vai su \"Genitori\" > profilo per scegliere gli show che i tuoi bambini possono guardare."
                Language.ID_GERMAN -> return "Gehen Sie zu \"Eltern\" > Profil zu ausgewählten Shows, die Ihre Kinder sehen dürfen."
                Language.ID_ARABIC -> return "اذهب إلى\"الآباء\"ثم الملف الشخصي لتختار العروض التي يمكن لأطفالك مشاهدتها."
                else /* Language.ID_ENGLISH */ -> return "Go to \"Parents\" > profile to Handpick shows your kids can watch."
            }

            //same as 0
            else -> when (languageId) {
                Language.ID_FRENCH -> return "Découvrez plus de vidéos dans d'autres langues dans Parents > Réglages."
                Language.ID_SPANISH -> return "Decubre más videos en otros idiomas en Padres> Configuración."
                Language.ID_PORTUGUESE -> return "Descubra mais vídeos nas outras linguas em Pais> Configurações."
                Language.ID_RUSSIAN -> return "Хотите сменить язык? Перейдите в раздел «Родители» > «Настройки»."
                Language.ID_ITALIAN -> return "Devi cambiare la lingua? Vai su \"Genitori\"> Impostazioni."
                Language.ID_GERMAN -> return "Müssen Sie die Sprache wechseln? Gehen Sie zu \"Eltern\" > Einstellungen."
                Language.ID_ARABIC -> return "تريد أن تغير اللغة؟ اذهب ل\"الآباء\"ثم الإعدادات."
                else /* Language.ID_ENGLISH */ -> return "Need to change the language? Go to \"Parents\" > Settings."
            }
        }
    }
}


fun JsonObjectCreatorV3.internal_data_toJson(internalData: InternalData): JSONObject {
    val json = JSONObject()
    json.put("countryId", internalData.countryId)
    json.put("languageId", internalData.languageId)
    return json
}