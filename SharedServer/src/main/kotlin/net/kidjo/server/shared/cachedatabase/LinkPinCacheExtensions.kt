package net.kidjo.server.shared.cachedatabase

import org.slf4j.LoggerFactory

private const val LINK_PIN_DURATION = 5L * 60L // 5 min
private val logger = LoggerFactory.getLogger("LinkPinCacheExtensions")

fun CacheDatabase.linkPinGetUserId(pin: String): String? {
    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_LINK_PIN + pin
    val value = commands.get(cacheKey)
    logger.debug("linkPinGetUserId(pin: $pin) | userId: $value")
    return value
}

fun CacheDatabase.linkPinSet(pin: String, userId: String): Boolean = try {
    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_LINK_PIN + pin
    val value = commands.set(cacheKey, userId)
    commands.expire(cacheKey, LINK_PIN_DURATION)
    logger.debug("linkPinSet(pin: $pin, userId: $userId) | result: ${value == "OK"}")
    value == "OK"
} catch (exception: Exception) {
    false
}

fun CacheDatabase.linkPinDelete(pin: String): Boolean {
    if (pin == "") return false

    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_LINK_PIN + pin
    val value = commands.del(cacheKey)
    logger.debug("linkPinDelete(pin: $pin) | result: ${value > 0}")
    return value > 0
}
