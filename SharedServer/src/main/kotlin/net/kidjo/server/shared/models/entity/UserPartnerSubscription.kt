package net.kidjo.server.shared.models.entity


import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object UserPartnerSubscription : Table("user_partner_subscription") {
    val id = integer("id").autoIncrement()
    val msidn = varchar("msidn", 255).uniqueIndex()
    val subPartner = varchar("sub_partner", 255)
    var subscriptionId = integer("subscription_id")
    val userId = integer("user_id")
    val isActive = bool("active")
    val userUuid = varchar("userUuid", 255).uniqueIndex()
    val subscriptionPackage = varchar("subscription_package", 255)
    val country = varchar("country", 255)
    val eventType = varchar("event_type", 255)
    val updated_at = datetime("updated_at")
    val created_at = datetime("created_at")

    val partnerSubscriptionId = long("partner_subscription_id")
    val partnerUserId = long("partner_user_id")
    val partnerExternalIdentifier = varchar("partner_external_identifier", 255).uniqueIndex()
    val partnerLastModifiedDate = datetime("partner_last_modified_date")

}
