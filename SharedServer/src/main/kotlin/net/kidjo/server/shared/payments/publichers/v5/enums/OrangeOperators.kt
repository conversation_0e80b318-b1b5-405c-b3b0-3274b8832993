package net.kidjo.server.shared.payments.publichers.v5.enums

enum class OrangeOperators(
    val id: Int,
    val opName: String,
    val countryShort: String,
    val mccmnc: Int,
    val countryId: Long,
    val currencyCode: String
) {
    ORANGE_MOROCCO(
        1,
        "orange_morocco",
        "ma",
        60400,
        151,
        "MAD"
    ),
    ORANGE_FRANCE(
            2,
            "orange_france",
            "fr",
        20801,
        76,
            "FE"
    ),
    ORANGE_SENEGAL(
        2,
        "orange_senegal",
        "sn",
        2,
        176,
        "XOFß"
    ),
    ORANGE_IVORY_COAST(
        2,
        "orange_ivory_coast",
        "ci",
        3,
        55,
        "XOF"
    ),
    NONE(0,
        "NONE",
        "NONE",
        0,
        0,
        "NONE");

    companion object {
        fun getByOpName(opName: String): OrangeOperators {
            val lower = opName.toLowerCase()
            return when (lower) {
                ORANGE_MOROCCO.opName -> ORANGE_MOROCCO
                ORANGE_FRANCE.opName -> ORANGE_FRANCE
                ORANGE_SENEGAL.opName -> ORANGE_SENEGAL
                ORANGE_IVORY_COAST.opName -> ORANGE_IVORY_COAST
                else -> NONE
            }
        }

        fun getByCountryShort(countryShort: String?): OrangeOperators {
            return when (if(!countryShort.isNullOrBlank()) countryShort.toLowerCase() else "") {
                ORANGE_MOROCCO.countryShort -> ORANGE_MOROCCO
                ORANGE_FRANCE.countryShort -> ORANGE_FRANCE
                ORANGE_SENEGAL.countryShort -> ORANGE_SENEGAL
                ORANGE_IVORY_COAST.countryShort -> ORANGE_IVORY_COAST
                else -> NONE
            }
        }

        fun getByMCCMNC(mccmnc: Int): OrangeOperators {
            return when (mccmnc) {
                60400 -> ORANGE_MOROCCO
                1 -> ORANGE_FRANCE
                2 -> ORANGE_SENEGAL
                3 -> ORANGE_IVORY_COAST
                else -> NONE
            }
        }
    }
}