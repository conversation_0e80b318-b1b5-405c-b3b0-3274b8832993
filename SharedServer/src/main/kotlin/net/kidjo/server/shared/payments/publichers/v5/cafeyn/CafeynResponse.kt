package net.kidjo.server.shared.payments.publichers.v5.cafeyn

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty


@JsonIgnoreProperties(ignoreUnknown = true)
data class CafeynResponse(
    @JsonProperty("is_active") val isActive: <PERSON><PERSON><PERSON>,
    @JsonProperty("token") val token: String? = null,
    @JsonProperty("email") val email: String? = null,
    @JsonProperty("user_id") val userId: Int? = null,
    @JsonProperty("partner_name") val partnerName: String? = null,
    @JsonProperty("partner_id") val partnerId: Int? = null,
    @JsonProperty("subscribed_services") val subscribedServices: List<String>? = null
)
