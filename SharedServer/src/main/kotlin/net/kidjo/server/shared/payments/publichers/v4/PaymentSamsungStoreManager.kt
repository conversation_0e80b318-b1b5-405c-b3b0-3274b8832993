package net.kidjo.server.shared.payments.publichers.v4

import io.ktor.server.application.*
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.SubscriptionErrors
import net.kidjo.server.shared.extensions.Success
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondConflict
import net.kidjo.server.shared.extensions.respondNoContent
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.payments.SamsungApiManager
import net.kidjo.server.shared.payments.SubscriptionStatusResponse
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.UserManager
import net.kidjo.server.shared.tools.Utility
import okhttp3.OkHttpClient
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class PaymentSamsungStoreManager(
    config: Config,
    httpClient: OkHttpClient,
    encryptionController: EncryptionController,
    databaseController: DatabaseController,
    iapManager: IAPManager,
    private val samsungApiManager: SamsungApiManager,
    emailManager: EmailManager,
    languageCache: LanguageCache,
    userManager: UserManager
) : BasePaymentManager(config, httpClient, encryptionController, databaseController, iapManager,emailManager,languageCache,userManager) {

    override suspend fun start(
        userId: Long, inAppPurchase: InAppPurchase,
        token: String, purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        forceUpdate: Boolean,
        subscriptionId: String?,
        orderId: String?,
        call: ApplicationCall
    ) {

        when (inAppPurchase.store) {
            Device.StorePlatform.SAMSUNG -> {
                val checkSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(token)
                if (checkSubscription == null) {
                    return createSubscription(
                        inAppPurchase, token,
                        call, userId, purchasingSession, subscriptionType, subscriptionId, orderId
                    )
                } else {
                    if (checkSubscription.userId != 0L && userId != checkSubscription.userId) {
                        return call.respondConflict(
                            SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION,
                            "This subscription already belongs to another account. Please login."
                        )
                    }
                    val isExpired = isBillingDateExpired(checkSubscription.nextBillDate)
                    if (isExpired || forceUpdate) {
                        return updateSubscription(
                            inAppPurchase, token,
                            call, userId, checkSubscription
                        )
                    } else {
                        if(checkSubscription.userId==0L && checkSubscription.id.toInt()!=0){
                            var utility= Utility()
                            if(utility.attachNativeSubscription(checkSubscription,userId)){
                                val userDetails=userManager.getUser(checkSubscription.userId.toString())
                                emailManager.sendConfirmationEmail(userDetails.email, userDetails.name, languageCache.get(call))
                                logger.info("Subscription is attached succesfully !!!")
                                return call.respondOK(Success.SUCCESS, "Successful updated subscription to user id: ${userId} ")

                            }
                        }
                        return call.respondNoContent()
                    }

                }
            }
            else -> {
                logger.error("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
                return call.respondBadRequest("Store: ${inAppPurchase.store.raw} Not supported. Why is this being called.")
            }
        }
    }

    override suspend fun createSubscription(
        inAppPurchase: InAppPurchase,
        token: String, call:
        ApplicationCall,
        userId: Long,
        purchasingSession: String,
        subscriptionType: SubscriptionRoot.SubscriptionType,
        subscriptionId: String?,
        orderId: String?
    ) {

        val samsungSubscriptionStatus = getSamsungSubscriptionStatus(
            inAppPurchase, token,
            call
        )

        var freeTrial = true
        var priceToLog = 0.0f
        var isTestReceipt = false

        samsungSubscriptionStatus?.subscriptionType

        if (samsungSubscriptionStatus != null) {
            val samsungFreeTrial = samsungSubscriptionStatus.freeTrial

            if (samsungFreeTrial == "N") {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }
            if (samsungSubscriptionStatus.realMode == "Y") {
                isTestReceipt = true
            }

            val subscriptionEndDate =
                LocalDateTime.parse(
                    samsungSubscriptionStatus.subscriptionEndDate,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                )

            val subscriptionRootInsert = SubscriptionRootInsert(
                userId,
                0L,
                freeTrial,
                priceToLog,
                SubscriptionRoot.PaymentType.NATIVE,
                subscriptionType,
                inAppPurchase.store.raw,
                samsungSubscriptionStatus.latestOrderId,
                inAppPurchase.store,
                samsungSubscriptionStatus.currentPaymentPlan,
                inAppPurchase.id,
                purchasingSession,
                token,
                encryptionController.sha256Hash(token),
                0L,
                true,
                subscriptionEndDate,
                isTestReceipt
            )

            insert(subscriptionRootInsert, call)
        }
    }

    override suspend fun updateSubscription(
        inAppPurchase: InAppPurchase,
        token: String,
        call: ApplicationCall,
        userId: Long,
        subscriptionRoot: SubscriptionRoot
    ) {

        var freeTrial = true
        var priceToLog = 0.0f
        val samsungSubscriptionStatus = getSamsungSubscriptionStatus(
            inAppPurchase, token,
            call
        )

        if (samsungSubscriptionStatus != null) {

            val samsungFreeTrial = samsungSubscriptionStatus.freeTrial
            var autoRenewing = true

            if (samsungFreeTrial == "N") {
                freeTrial = false
                priceToLog = inAppPurchase.price.toFloat()
            }

            if (samsungSubscriptionStatus.subscriptionStatus == "CANCEL") {
                autoRenewing = false
            }

            val subscriptionEndDate =
                LocalDateTime.parse(
                    samsungSubscriptionStatus.subscriptionEndDate,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                )

            val subscriptionRootUpdate = SubscriptionRootUpdate(
                userId, autoRenewing,
                subscriptionEndDate,
                samsungSubscriptionStatus.latestOrderId,
                freeTrial, priceToLog, subscriptionRoot.id, samsungSubscriptionStatus.currentPaymentPlan
            )

            update(subscriptionRoot.id.toString(), subscriptionRootUpdate, call)
        }
    }

    suspend fun getSamsungSubscriptionStatus(
        inAppPurchase: InAppPurchase, token: String, call: ApplicationCall
    ): SubscriptionStatusResponse? {
        return try {
            samsungApiManager.getSubscriptionStatus(token, call)
        } catch (e: Exception) {
            logger.error("cant find subscription Samsung of $inAppPurchase, token: ${token.take(10)}")
            return null
        }
    }
}
