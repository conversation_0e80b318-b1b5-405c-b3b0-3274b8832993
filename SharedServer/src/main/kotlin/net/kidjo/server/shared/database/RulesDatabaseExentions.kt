package net.kidjo.server.shared.database

import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.IAPRule

suspend fun DatabaseController.rules_getList(platform: Device.StorePlatform, isMainCountry: Boolean): ArrayList<IAPRule> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT iap_rules.*, iap_rules_group.groupIdName FROM iap_rules INNER JOIN iap_rules_group on iap_rules.groupId = iap_rules_group.id WHERE storeId = ? AND mainCountries = ? ORDER BY chance")
    statement.setString(1,platform.raw)
    statement.setBoolean(2,isMainCountry)


    val results = statement.executeQuery()

    val rules = ArrayList<IAPRule>()
    while (results.next()) rules.add(IAPRule(results))

    statement.close()
    connection.close()
    return rules
}
suspend fun DatabaseController.rules_getById(id: Long): IAPRule? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT * FROM iap_rules WHERE id = ? LIMIT 1")
    statement.setLong(1,id)

    val results = statement.executeQuery()

    val rule: IAPRule?
    if (results.next()) rule = IAPRule(results)
    else rule = null

    statement.close()
    connection.close()
    return rule
}
suspend fun DatabaseController.rules_getDefault(platform: Device.StorePlatform, isMainCountry: Boolean, shouldAllowFreeTrial: Boolean): IAPRule? {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT iap_rules.*, iap_rules_group.groupIdName FROM iap_rules INNER JOIN iap_rules_group on iap_rules.groupId = iap_rules_group.id WHERE storeId = ? AND mainCountries = ? and iap_rules.allowFreeTrial = ? AND isDefault = TRUE LIMIT 1")
    statement.setString(1,platform.raw)
    statement.setBoolean(2,isMainCountry)
    statement.setBoolean(2,shouldAllowFreeTrial)


    val results = statement.executeQuery()

    val rule: IAPRule?
    if (results.next()) rule = IAPRule(results)
    else rule = null

    statement.close()
    connection.close()
    return rule
}


suspend fun DatabaseController.rules_getGroupId(platform: Device.StorePlatform, isMainCountry: Boolean): String {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT groupIdName FROM iap_rules_group WHERE storeId = ? AND mainCountries = ? LIMIT 1")
    statement.setString(1,platform.raw)
    statement.setBoolean(2,isMainCountry)
    val results = statement.executeQuery()

    val groupId: String
    if (results.next()) {
        groupId = results.getString("groupIdName")
    } else {
        groupId = platform.raw + if (isMainCountry) "_mc" else "_row"
    }
    statement.close()
    connection.close()
    return groupId
}