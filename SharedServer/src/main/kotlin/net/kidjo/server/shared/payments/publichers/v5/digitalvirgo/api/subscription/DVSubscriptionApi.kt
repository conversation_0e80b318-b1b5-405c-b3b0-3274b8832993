package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getDVPairedUserByMsIsdn
import net.kidjo.server.shared.database.getDVPairedUserBySubToken
import net.kidjo.server.shared.database.user_getCountryIdFromShortName
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.response.DVGetSubscriptionInfoDTO
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.sms.DVSenditoApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.cancel.cancelSubscriptionDVPASSApi
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.getinfo.getSubscriptionInfoDVPASSApiDTO
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.cancel.cancelKIDJOSubscription
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.create.createKIDJOSubscription
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.link.setDVPairingKIDJOInfo
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.modifyKIDJOAccount
import net.kidjo.server.shared.payments.publichers.v5.enums.OrangeOperators
import net.kidjo.server.shared.payments.publishers.v5.enums.MoroccoOperators
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.shared.tools.Validator
import okhttp3.OkHttpClient
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

class DVSubscriptionApi(
    private val config: Config,
    private val validator: Validator,
    private val utility: Utility,
    private val encryptionController: EncryptionController,
    private val databaseController: DatabaseController,
    private val dvSendito: DVSenditoApi
) {

    internal val logger = LoggerFactory.getLogger("DVSubscriptionApi - ")
    val httpClient = OkHttpClient()
    suspend fun getDVPASSInfo(subscriptionId: String?, operationId: String?): DVGetSubscriptionInfoDTO? = try {
        logger.info("GET SUBSCRIPTION INFO DVPASS. ")
        val subInfoResponse: DVGetSubscriptionInfoDTO?

        val subIndentString = getSubscriptionIndent(operationId, subscriptionId)

        if (subIndentString == null) {
            logger.error("ERROR: THE operationId AND subscriptionId are empty. ")
            throw IllegalArgumentException("Can not take subIndentString of null.")
        }

        val claimId = databaseController.getDVPairedUserBySubToken(subIndentString)?.offerId
        subInfoResponse =
            getSubscriptionInfoDVPASSApiDTO(
                subscriptionIdString = subIndentString,
                baseUrl = config.dv_merchants_api_url,
                login = config.dv_sesame_login,
                secret = config.dv_secret_key,
                claim = Pair(
                    if (claimId == null || claimId == 0L) config.dv_kidjo_service_clame_name else config.dv_kidjo_offer_clame_name,
                    databaseController.getDVPairedUserBySubToken(subIndentString)?.offerId ?: config.dv_kidjo_service_id
                ),
                validity = config.jwt_token_validity_mls
            )

        val subStatus = subInfoResponse?.data?.status
        val subId = subInfoResponse?.data?.subscriptionId

        if (subStatus.isNullOrBlank() || subId.isNullOrBlank()) {
            logger.error(
                "get subscription info didn't have right information" +
                        "(subscription, data.status, data.subscriptionId )," +
                        " $subInfoResponse"
            )
            throw IllegalArgumentException("Can not take subStatus, subId  of null.")
        }

        logger.info("dv_subscriptionInfo.code : ${subInfoResponse.code}")
        logger.info("dv_subscriptionInfoResponse.message  : ${subInfoResponse.message}")
        logger.info("dv_subscriptionInfoResponse.data?.status  : $subStatus")
        logger.info("dv_subscriptionInfoResponse.data?.expirationDate :${subInfoResponse.data?.expirationDate}")
        logger.info("dv_subscriptionInfoResponse.data?.cancellationDate :${subInfoResponse.data?.cancellationDate}")
        logger.info("dv_subscriptionInfoResponse.data?.nextInvoiceDate : ${subInfoResponse.data?.nextInvoiceDate}")
        logger.info("dv_subscriptionInfoResponse.data?.premiumStartDate : ${subInfoResponse.data?.premiumStartDate}")
        logger.info("dv_subscriptionInfoResponse.data?.subscriptionDate : ${subInfoResponse.data?.subscriptionDate}")
        logger.info("dv_subscriptionInfoResponse.data?.subscriptionId : ${subInfoResponse.data?.subscriptionId}")
        logger.info("dv_subscriptionInfoResponse.data?.country : ${subInfoResponse.data?.country}")

        subInfoResponse

    } catch (e: Exception) {
        logger.error("Error DV subscriptionInfoResponse, ${e.localizedMessage}")
        throw e
    }

    suspend fun createDVPASSAndKIDJOAccount(
        subIndent: String,
        userIndent: String,
        msisdn: String?,
        subCountryCode: String,
        operatorName: String,
        userLocale: String,
        nextBillingDate: LocalDateTime,
        offerId: Int,
        packageId: Int,
        correlationId: String? = null,
        subStatus: String,
        email: String?
    ): Long? {

        logger.info("CREATE DVPASS AND KIDJO SUBSCRIPTION. ")
        logger.info("dv_operatorName : $operatorName ")
        var userId: Long? = null
        val dvUserCountryId =
            databaseController.user_getCountryIdFromShortName(validator.createLocale(userLocale).country)
        val dvPairedUserAccount = databaseController.getDVPairedUserBySubToken(subIndent)
        logger.info("dvUserCountryId : $dvUserCountryId ")
        logger.info("dvPairedUserAccount.subscriptionToken : ${dvPairedUserAccount?.subscriptionToken} ")
        logger.info("subIndent : $subIndent ")

        val idUser = databaseController.getDVPairedUserByMsIsdn(msisdn)
        logger.info("user id $idUser")


        if (operatorName == OrangeOperators.ORANGE_MOROCCO.name || operatorName == OrangeOperators.ORANGE_SENEGAL.name
            || operatorName == OrangeOperators.ORANGE_IVORY_COAST.name || operatorName == MoroccoOperators.IAM.name
            || operatorName == "SFR" || operatorName == OrangeOperators.ORANGE_FRANCE.name || operatorName == "BOUYGUES"
            || operatorName == "INWI"
            || operatorName == OrangeOperators.NONE.name // Handle card payment, the code must be cleaned by using DVBillingType.PAYMENT_CARD
        ) {
            //01. Create or Update Kidjo User Account
            val modifiedUser =
                modifyKIDJOAccount(
                    userId = idUser,
                    msisdn = msisdn,
                    countryId = dvUserCountryId.toLong(),
                    email = email,
                    operatorName = operatorName,
                    utility = utility,
                    config = config,
                    encryptionController = encryptionController,
                    databaseController = databaseController
                )

            //02. Create Kidjo User Subscription
            val insertedSubId =
                createKIDJOSubscription(
                    userId = modifiedUser.getLongId(),
                    userAlias = userIndent,
                    subscriptionIdString = subIndent,
                    nextBillingDate = nextBillingDate,
                    operatorName = operatorName,
                    isTestEnv = !config.env.isLive,
                    encryptionController = encryptionController,
                    databaseController = databaseController
                )

            if (insertedSubId == SubscriptionRoot.NO_ID) {
                logger.error("ERROR: CREATE KIDJO SUBSCRIPTION id: $subIndent, user id: $userIndent ")
            }

            //03. Set DV pairing info to the Kidjo database
            val resultInfo: Boolean =
                setDVPairingKIDJOInfo(
                    operationId = subIndent,
                    userAlias = userIndent,
                    subscriptionToken = subIndent,
                    subscriptionId = insertedSubId,
                    userId = modifiedUser.getLongId(),
                    offerId = offerId.toLong(),
                    packageId = packageId.toLong(),
                    correlationIdString = correlationId,
                    subStatus = SubscriptionStatusType.fromRaw(subStatus).raw,
                    databaseController = databaseController
                )
            if (!resultInfo) {
                logger.error("ERROR: SET PAIRING INFO to KIDJO Database: subscription id: $subIndent, user id: $userIndent ")
            }
//            04. Send Sendito API SMS for ORANGE-MOROCCO Operator
            val resultSMS =
                dvSendito.sendSMS(
                    user = modifiedUser,
                    subIdString = subIndent,
                    userAlias = userIndent,
                    subCountryCode = subCountryCode,
                    operatorName = operatorName,
                    offerId = offerId.toString(),
                    packageId = packageId.toLong()
                )

            if (!resultSMS) {
                logger.error("ERROR: SENT SMS to the user: $userIndent ")
            }

            userId = modifiedUser.getLongId()
        } else {
            logger.error("ERROR: CREATING Orange Subscription or Sending Sendito SMS. ")
            logger.error("The Orange Operator IS NOT ORANGE_MOROCCO: $operatorName OR the subscriptionId exists: $subIndent ")
        }


        return userId
    }

    suspend fun cancelDVPASS(
        subscriptionIdString: String,
        correlationIdString: String? = null,
        expirationDate: LocalDateTime
    ): Boolean = try {

        logger.info("CANCEL SUBSCRIPTION DVPASS. ")

        val responseCansel = if (correlationIdString != null)
            cancelSubscriptionDVPASSApi(
                subscriptionIdString = subscriptionIdString,
                correlationId = correlationIdString,
                baseUrl = config.dv_merchants_api_url,
                login = config.dv_sesame_login,
                secret = config.dv_secret_key
            ) else null

        if (responseCansel == null && correlationIdString != null) {
            logger.error("EXPIRATION ERROR: A problem occurred while getting responseCansel ")
            throw IllegalArgumentException("Can not take resultCancel of false.")
        }
        logger.info("CANCEL: SUB_DTO: responseCansel.data.status: '${responseCansel?.data?.status}' ")
        logger.info("CANCEL: SUB_DTO: responseCansel.data.type: '${responseCansel?.data?.type}' ")
        logger.info("CANCEL: KIDJO Subscription: '${subscriptionIdString}' ")

        val resultCancel =
            cancelKIDJOSubscription(
                nextBillDate = expirationDate,
                subToken = subscriptionIdString,
                subStatus = SubscriptionStatusType.fromRaw(responseCansel?.data?.status).raw,
                databaseController = databaseController
            )

        logger.info("EXPIRATION: nextBillDate '${expirationDate}' ")

        if (!resultCancel) {
            logger.error("ERROR: CANCEL: A problem occurred while unsubscribe Kidho-Tv subscription ID: $subscriptionIdString")
            logger.info(" - EXIT CANCEL: TYPE: ${responseCansel?.data?.type} - STATUS: ${responseCansel?.data?.status} ")
            throw IllegalArgumentException("Can not take resultCancel of false.")
        }

        logger.info("CANCEL SUCCESS: Successfully unsubscribe kidjo-tv subscription: $subscriptionIdString")
        logger.info(" - EXIT CANCEL: TYPE: ${responseCansel?.data?.type} - STATUS: ${responseCansel?.data?.status} ")

        true

    } catch (e: Exception) {
        logger.error("Error DV canceling subscription, ${e.localizedMessage}")
        throw e
    }

    private fun getSubscriptionIndent(operationId: String?, subscriptionId: String?): String? {
        if (!operationId.isNullOrBlank()) {
            return operationId
        }
        if (!subscriptionId.isNullOrBlank()) {
            return subscriptionId
        }
        return null
    }

}
