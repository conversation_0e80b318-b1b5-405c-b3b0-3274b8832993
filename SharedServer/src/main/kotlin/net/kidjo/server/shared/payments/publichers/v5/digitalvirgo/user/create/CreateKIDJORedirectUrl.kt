package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.create
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.insert_user_redirect_key
import org.slf4j.LoggerFactory
import java.net.URLEncoder

private val logger = LoggerFactory.getLogger("DigitalVirgoApiManager - CREATE USER REDIRECT KEY - ")
val GENERATED_CHARACTERS_TO_USE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()
fun createKIDJORedirectURL(
    userId: Long,
    redirectURL: String,
    generatedKey: String,
    databaseController: DatabaseController
): String? = try {
        databaseController.insert_user_redirect_key(
            userId = userId,
            redirectKey = generatedKey
        )
        val key: String = URLEncoder.encode(generatedKey, Charsets.UTF_8.name())

        "$redirectURL$key"
    } catch (e: Throwable) {
        logger.error("Problems creating redirect key: ${e.localizedMessage}")
        null
    }


