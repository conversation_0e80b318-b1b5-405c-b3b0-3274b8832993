package net.kidjo.server.shared.models

import com.samskivert.mustache.Mustache
import com.samskivert.mustache.Template
import java.io.File
import java.io.Writer


class HotReloadTemplate(private val location: String,
                        private val hotReloadDir: String,
                        private val shouldHotReload: <PERSON><PERSON><PERSON>,
                        private val compiler: Mustache.Compiler) {
    private val _template: Template
    val template: Template
        get() {
            if (shouldHotReload) {
                return compiler.compile(File(hotReloadDir + location).reader())
            }
            return _template
        }

    init {
        //could make lazy to increase boot time
        val usingLoader = Thread.currentThread().contextClassLoader
        val source = usingLoader.getResourceAsStream(location)
        val reader = source.reader()
        _template = compiler.compile(reader)
        reader.close()
        source.close()
    }

    fun execute(o: Any): String = template.execute(o)
    fun execute(o: Any, out: Writer) = template.execute(o,out)
    fun execute(o: Any, parentContext: Any, out: Writer) = template.execute(o,parentContext,out)

}