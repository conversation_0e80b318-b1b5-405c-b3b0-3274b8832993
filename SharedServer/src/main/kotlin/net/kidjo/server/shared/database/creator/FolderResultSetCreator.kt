package net.kidjo.server.shared.database.creator

import net.kidjo.common.models.Folder
import java.sql.ResultSet

fun ResultSetObjectCreator.folder(resultSet: ResultSet, isFromFilterSQL: Boolean): Folder {
    val id = resultSet.getLong("id").toString()
    val contentType = Folder.ContentType.FromRaw(resultSet.getString("type"))
    val title: String
    val folderIsNotFiltered: Boolean
    if (isFromFilterSQL) {
        title = resultSet.getString("title")
        val notFilteredValue = resultSet.getBoolean("filterIsNotActive")
        folderIsNotFiltered = if (resultSet.wasNull()) true else notFilteredValue
    } else {
        folderIsNotFiltered = true
        title = resultSet.getString("title")
    }

    val mediaType = Folder.MediaType.FromRaw(resultSet.getString("mediaType"))

    return Folder(id, contentType, "", title, mediaType, folderIsNotFiltered)
}

fun ResultSetObjectCreator.folderSimple(resultSet: ResultSet): Folder {
    val id = resultSet.getLong("id").toString()
    val contentType = Folder.ContentType.FromRaw(resultSet.getString("type"))
    val mediaType = Folder.MediaType.FromRaw(resultSet.getString("mediaType"))
    val title = resultSet.getString("title")
    var description = resultSet.getString("description")
    if(description == null) {
        description = ""
    }
    return Folder(id, contentType, description, title, mediaType, false)
}
