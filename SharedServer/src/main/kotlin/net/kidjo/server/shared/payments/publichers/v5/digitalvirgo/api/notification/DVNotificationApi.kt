package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.notification

import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_update_nextBillingDateBySubToken
import net.kidjo.server.shared.database.updateDVPairedSubscriptionStatus
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.EventType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.request.DVNotificationSubscriptionRequest
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.subscription.DVSubscriptionApi
import net.kidjo.server.shared.payments.publishers.v5.enums.MoroccoOperators
import okhttp3.OkHttpClient
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class DVNotificationApi(
    private var dvSubscription: DVSubscriptionApi,
    private var databaseController: DatabaseController
) {
    internal val logger = LoggerFactory.getLogger("DVNotificationApi - ")
    var httpClient = OkHttpClient()
    suspend fun handleDVPASSEvent(subDto: DVNotificationSubscriptionRequest) {
        logger.info("START DV Notification received: handleDigitalVirgoNotification()")

        logger.info("dv_eventType : ${subDto.type}")
        logger.info("dv_subscriptionIdString  : ${subDto.operationId}")
        logger.info("dv_subscriptionStatus  : ${subDto.subscription?.status}")
        logger.info("dv_subscriptionCountry :${subDto.subscription?.country}")
        logger.info("dv_expirationDateString : ${subDto.subscription?.expirationDate}")
        logger.info("dv_cancellationDateString : ${subDto.subscription?.cancellationDate}")
        logger.info("dv_suspensionDateString : ${subDto.subscription?.suspensionDate}")
        logger.info("dv_terminationDateString : ${subDto.subscription?.terminationDate}")
        logger.info("dv_subscriptionDateString : ${subDto.subscription?.subscriptionDate}")
        logger.info("dv_user_msisdn : ${subDto.user?.msisdn}")
        logger.info("dv_user_alias : ${subDto.user?.alias}")
        logger.info("dv_user_email : ${subDto.user?.email}")
        //logger.info("dv_user_aliasGsm : ${subDto.user?.aliasGsm}")
        logger.info("dv_userLocale : ${subDto.user?.locale}")
        logger.info("dv_subscriptionId :${subDto.subscription?.subscriptionId}")

        val subIndent = subDto.getSubIndent()
        logger.info("subIndent : $subIndent ")

        val userIndent = subDto.getUserIndent()
        logger.info("userIndent : $userIndent ")

        val msisdn = subDto.getMSISDN()
        logger.info("msisdn: $msisdn ")

        val subCountry = subDto.subscription?.country
        logger.info("subCountry: $subCountry ")

        val userLocale = subDto.user?.locale
        logger.info("userLocale: $userLocale ")

        val userEmail = subDto.user?.email
        logger.info("userEmail: $userEmail ")

        val offerId = subDto.offer?.id
        logger.info("offerId: $offerId ")

        val packageId = subDto.dvPackage?.id
        logger.info("packageId: $packageId ")

        val correlationIdString = subDto.correlationId
        logger.info("correlationIdString: $correlationIdString ")

        val subStatus = subDto.subscription?.status
        logger.info("subStatus: $subStatus ")

        val subType = subDto.type
        logger.info("subType: $subType ")

        val mccmnc = subDto.getMCCMNC()
        logger.info("mccmnc: $mccmnc ")

        val nextBillingDate = subDto.getNextBillingDate()
        logger.info("nextBillingDate: $nextBillingDate ")

        var operatorName = when (mccmnc.toString()) {
            "60801" -> "ORANGE_SENEGAL"
            "61203" -> "ORANGE_IVORY_COAST"
            "60401" -> MoroccoOperators.IAM.name
            "20810" -> "SFR"
            "20888", "20821", "20820" -> "BOUYGUES"
            "60405", "60402" -> "INWI"
            else -> subDto.getOperatorName()
        }
        logger.info("operatorName: $operatorName ")

        logger.info("billingChannel ${subDto.dimensions?.billingChannel}")

        if (subType.isNullOrBlank() ||
            subIndent.isNullOrBlank() ||
            subCountry.isNullOrBlank() ||
            userIndent.isNullOrBlank()
        ) {
            logger.error(
                "notification didn't have right information" +
                        "(type, operationId, user:msisdn,alias,aliasGsm, subscription: subsCountry )," +
                        " $subDto"
            )
            return
        }


//        if (operatorName != OrangeOperators.ORANGE_MOROCCO.name) {
//            logger.info("EXIT UNSUPPORTED OPERATOR: $operatorName ")
//            return
//        }
        when (subType) {
            EventType.SUBSCRIPTION.raw -> {
                logger.info("ENTER SUBSCRIPTION NOTIFICATION EVENT TYPE: ${subType}: and Subscription id: $subIndent ")

                val userId = createSubscriptionEvent(
                    subIndent = subIndent,
                    userIndent = userIndent,
                    msisdn = msisdn,
                    subCountryCode = subCountry,
                    operatorName = operatorName,
                    userLocale = userLocale ?: "MA",
                    dvNextBillingDate = subDto.subscription?.let {
                        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX")
                        val zonedDateTime = ZonedDateTime.parse(it.nextCycleDate, formatter)
                        val localDateTime = zonedDateTime.toLocalDateTime()
                        localDateTime
                    } ?: nextBillingDate,
                    offerId = offerId ?: 0,
                    packageId = packageId ?: 0,
                    correlationId = correlationIdString,
                    subStatus = subStatus ?: SubscriptionStatusType.NONE.raw,
                    dvSubscription = dvSubscription,
                    databaseController = databaseController,
                    alias = subDto.user?.alias,
                    email=userEmail
                )

                if (userId == null) {
                    logger.error("ERROR: NOTIFICATION EVENT TYPE: $subType: CREATED KIDJO Subscription id: $subIndent ")
                    return
                }
                if (!insertCountryAndTelco(mccmnc.toString(), userId.toInt())) {
                    logger.error("Unable to insert country and telco into user_partner_subscription")
                }
                logger.info("SUCCESS: Successfully CREATE kidjo subscription id: $subIndent, status:$subStatus, dvNextBillingDate: $nextBillingDate, userId: $userId ")
                logger.info("EXIT SUBSCRIPTION NOTIFICATION EVENT TYPE: $subType ")
                return
            }

            EventType.TERMINATION.raw,
            EventType.EXPIRATION.raw,
            -> {
                logger.info("ENTER UNSUSBCRIPTION NOTIFICATION EVENT TYPE: $subType: and Subscription id: $subIndent ")
                val userId = updateUnsubscriptionEvent(
                    subIndent = subIndent,
                    dvNextBillingDate = nextBillingDate,
                    subStatus = subStatus ?: SubscriptionStatusType.NONE.raw,
                    correlationId = correlationIdString,
                    databaseController = databaseController
                )

                if (userId == null) {
                    logger.error("ERROR: NOTIFICATION EVENT TYPE: $subType:  CREATED KIDJO Subscription id: $subIndent ")
                    return
                }

                logger.info("SUCCESS: Successfully Modify kidjo subscription id: $subIndent, status:$subStatus, dvNextBillingDate: $nextBillingDate, userId: $userIndent ")
                logger.info("EXIT UNSUSBCRIPTION NOTIFICATION EVENT TYPE: $subType ")
                return
            }

            EventType.CANCELLATION.raw -> {
                logger.info("Event Type: Cancellation Until we receive termination user will be active")
                databaseController.updateDVPairedSubscriptionStatus(
                    EventType.CANCELLATION.name,
                    subIndent,
                    correlationIdString
                )
            }

            EventType.SUSPENSION.raw -> {
                logger.info("Event Type: Suspension Until we receive termination user will be active")
                databaseController.updateDVPairedSubscriptionStatus(
                    subStatus,
                    subIndent,
                    correlationIdString
                )
                databaseController.subscription_update_nextBillingDateBySubToken(
                    LocalDateTime.now().plusDays(15),
                    subIndent
                )
                logger.info("Event type:Subscriptions is disabled for subscriptionId {} ", subIndent)
            }

            EventType.INVOICE.raw,
            -> {
                logger.info("EVENT TYPE: INVOICE ")
                return
            }

            EventType.REFUND.raw -> {
                logger.info("EVENT TYPE: REFUND ")
                return
            }

            EventType.RELOAD.raw -> {
                logger.info("EVENT TYPE: RELOAD ")
                return
            }

            else -> {
                logger.error("NOT COVERT EVENT TYPE: $subType subscriptionId: $subIndent ")
                return
            }
        }
    }
}
