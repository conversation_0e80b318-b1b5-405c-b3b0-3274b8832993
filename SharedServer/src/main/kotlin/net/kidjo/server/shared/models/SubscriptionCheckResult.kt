package net.kidjo.server.shared.models

import java.time.LocalDateTime

data class SubscriptionCheckResult(val status: Status,
                                   val priceOfLastSubUSD: Float,
                                   val nextBillDate: LocalDateTime,
                                   val paymentStateId: String?) {
    enum class Status {
        ACTIVE, PENDING, <PERSON><PERSON><PERSON>_USER, CANCEL_FAILED_PAYMENT, CA<PERSON>EL_ADMIN, CANCEL_EXPIRED;
        companion object {
            fun GetCancelReason(status: Status): SubscriptionTransaction.TransactionType {
                return when (status) {
                    CANCEL_USER -> SubscriptionTransaction.TransactionType.CANCEL_USER
                    CANCEL_FAILED_PAYMENT -> SubscriptionTransaction.TransactionType.CA<PERSON>EL_FAILED_PAYMENT
                    CANCEL_ADMIN -> SubscriptionTransaction.TransactionType.CANCEL_ADMIN
                    CANCEL_EXPIRED -> SubscriptionTransaction.TransactionType.CANCEL_EXPIRED
                    else -> {
                        throw SubscriptionCheckException("Not a cancel reason")
                    }
                }
            }
        }
    }
}

class SubscriptionCheckException(reason: String): Throwable(reason)
class SamsungSubscriptionCheckException(reason: String): Throwable(reason)