package net.kidjo.server.shared.cachedatabase
import org.json.JSONObject
import org.slf4j.LoggerFactory

private const val USER_JWT_BLACKLIST_DURATION = 31556952000L //one year
private val logger = LoggerFactory.getLogger("UserJwtPairingCacheExtensions.kt")

fun CacheDatabase.getUserJwtFromBlackList(token: String): Boolean {
    if (token == "") return false
    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_JWT_BLAKCLIST + token
    val jsonString = commands.get(cacheKey) ?: return false

    return try {
        val json = JSONObject(jsonString)
        val serverObject = UserJWTPairing.FromServerJSON(json)
        if(serverObject.token == token){
            logger.info("GET User Jwt From BlackList: (token: ${serverObject.token} , userId: ${serverObject.userId} )")
            return true
        }
        false
    } catch (exception: Exception) {
        logger.error( "GET User Jwt From BlackList: ERROR: " + exception.message)
        false
    }
}

fun CacheDatabase.setUserLoginToBlackList(userJWTPairing : UserJWTPairing): Boolean = try {
    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_JWT_BLAKCLIST + userJWTPairing.token
    val value = commands.set(cacheKey, userJWTPairing.toServerJSONString())
    commands.expire(cacheKey, USER_JWT_BLACKLIST_DURATION)
    logger.debug("SET User Jwt To BlackList: (token: ${userJWTPairing.token} , userId: ${userJWTPairing.userId} ) | result: ${value == "OK"}")
    value == "OK"
} catch (exception: Exception) {
    logger.error( "SET User Jwt To BlackList: ERROR: " + exception.message)
    false
}

fun CacheDatabase.deleteUserPairingToken(token: String): Boolean {
    if (token == "") return false

    val commands = connection.sync()
    val cacheKey = CacheDatabase.KEY_JWT_BLAKCLIST + token
    val value = commands.del(cacheKey)
    logger.debug("deleteUserPairingToken(token: $token) | result: ${value > 0}")
    return value > 0
}

data class UserJWTPairing( val userId: String,
                             val token: String) {


    fun toServerJSON(): JSONObject {
        val json = JSONObject()
        json.put("userId", userId)
        json.put("token", token)
        return json
    }

    fun toServerJSONString():String {
        return toServerJSON().toString()
    }

    companion object {
        const val NO_ID = ""
        const val NO_TOKEN = ""
        fun FromServerJSON(jsonObject: JSONObject): UserJWTPairing {
            val userId = jsonObject.optString("userId", NO_ID)
            val token = jsonObject.optString("token", NO_TOKEN)
            return UserJWTPairing(userId, token)
        }
    }
}