package net.kidjo.server.shared.tools

import java.util.*

private val emailRegex = Regex(
    "^(([\\w-]+\\.)+[\\w-]+|([a-zA-Z]|[\\w-]{2,}))@"
            + "((([0-1]?[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\.([0-1]?"
            + "[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\."
            + "([0-1]?[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\.([0-1]?"
            + "[0-9]{1,2}|25[0-5]|2[0-4][0-9]))|"
            + "([a-zA-Z0-9]+[\\w-]+\\.)+[a-zA-Z]{2,4})$"
)

class Validator(config: Config) {
    private val mainCountries = config.mainCountriesId

    fun isEmailValid(email: String) = emailRegex.find(email) != null

    fun isCountryMainCountry(id: String): Boolean = mainCountries.contains(id.toLowerCase())

    fun isCountryMainCountry(locale: Locale): Boolean = mainCountries.contains(locale.country.toLowerCase())

    fun createLocale(localeString: String): Locale {
        if (localeString.contains("-")) return Locale.forLanguageTag(localeString)
        val split = localeString.split("_")
        return when (split.size) {
            0 -> Locale("", "", "")
            1 -> Locale(split[0])
            2 -> Locale(split[0], split[1].toUpperCase())
            else -> Locale(split[0], split[1].toUpperCase(), split[2])
        }
    }
}
