package net.kidjo.server.shared.payments

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.tools.Config
import okhttp3.*
import org.json.JSONObject
import org.slf4j.LoggerFactory

class AppleApiManager {
    private val okHttpClient = OkHttpClient()
    private val jsonMediaType = MediaType.parse("application/json; charset=utf-8")
    private val logger = LoggerFactory.getLogger("AppleApiManager")

    lateinit var appStoreResponse: AppStoreBillingResponse

    /**
     * Verify and return purchase receipt from AppStore API
     */
    suspend fun getAppStoreReceipt(
        token: String,
        config: Config,
        subscriptionType: String,
        call: ApplicationCall? = null
    ): AppStoreBillingResponse {
        var iosSharedSecret = getIosSharedSecret(config, subscriptionType)
        var response = getResponse(token, iosSharedSecret, config.iOS_verify_receipt_url)
        var billingResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
        if (billingResponseString != null && billingResponseString.contains("21007")) {
            response = getResponse(token, iosSharedSecret, config.iOS_verify_santbox_receipt_url)
            billingResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
        }
        appStoreResponse =
            billingResponseString?.let {
                jacksonObjectMapper().configure(
                    DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false
                ).readValue<AppStoreBillingResponse>(it)
            }!!

        if (response.code() != HttpStatusCode.OK.value) {
            logger.error("Response from the Appstore receipt verify API was not OK")
            call?.respondError(HttpStatusCode.fromValue(appStoreResponse?.status ?: 0))
        }

        return appStoreResponse
    }

    private suspend fun getResponse(token: String, iosSharedSecret: String, iosUrl: String): Response {
        val jsonBody = JSONObject()
            .put("receipt-data", token)
            .put("password", iosSharedSecret)
        var request = Request.Builder().url(iosUrl).post(RequestBody.create(jsonMediaType, jsonBody.toString())).build()
        var response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
        return response
    }

    private fun getIosSharedSecret(config: Config, subscriptionType: String): String {
        var iosSharedSecret = config.iOS_iTunesKidjoTvSharedSecret
        if (subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_BOOKS.raw) {
            iosSharedSecret = config.iOS_iTunesKidjoBooksSharedSecret
        }
        return iosSharedSecret
    }

}

data class In_app(
    val product_id: String,
    val purchase_date: String,
    val purchase_date_ms: Long,
    val purchase_date_pst: String,
    val original_purchase_date: String,
    val original_purchase_date_ms: Long,
    val original_purchase_date_pst: String,
    val expires_date: String,
    val expires_date_ms: Long,
    val expires_date_pst: String,
    val web_order_line_item_id: Long,
    val is_trial_period: Boolean,
    val is_in_intro_offer_period: Boolean
)

data class AppStoreBillingResponse(
    val environment: String,
    val receipt: Receipt,
    val latest_receipt_info: List<Latest_receipt_info>,
    val latest_receipt: String,
    val pending_renewal_info: List<Pending_renewal_info>,
    val status: Int
) {
    val latestReceipt = listOf(latest_receipt_info.maxBy { it -> it.expires_date_ms })
}

data class Latest_receipt_info(
    val product_id: String,
    val transaction_id: Long,
    val original_transaction_id: Long,
    val purchase_date: String,
    val purchase_date_ms: Long,
    val purchase_date_pst: String,
    val original_purchase_date: String,
    val original_purchase_date_ms: Long,
    val original_purchase_date_pst: String,
    val expires_date: String,
    val expires_date_ms: Long,
    val expires_date_pst: String,
    val web_order_line_item_id: Long,
    val is_trial_period: Boolean,
    val is_in_intro_offer_period: Boolean,
    val subscription_group_identifier: Long
)

data class Pending_renewal_info(
    val original_transaction_id: Long,
    val is_in_billing_retry_period: Int,
    val auto_renew_status: Int
)

data class Receipt(
    val app_item_id: Long,
    val receipt_creation_date: String,
    val receipt_creation_date_ms: Long,
    val receipt_creation_date_pst: String,
    val original_purchase_date: String,
    val original_purchase_date_ms: Long,
    val original_purchase_date_pst: String,
    val in_app: List<In_app>
)
