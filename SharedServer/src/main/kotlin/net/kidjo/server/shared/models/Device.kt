package net.kidjo.server.shared.models

import net.kidjo.server.shared.tools.EncryptionController

data class Device(val store: StorePlatform,
             val build: Int) {

    var serverId: Long = NO_SERVER_ID
        private set
    var userId: String = NO_ID
        private set

    var headerWasValid = false
        private set
    var infoFromServer = false

    var languageId: Int = 0
    var countryId: Int = 0
    var model: String = ""
    var createdAt = 0L
    var userAgent: String = ""

    fun setUserId(userId: String, encryptionController: EncryptionController) {
        if (userId == Device.NO_ID || userId.isEmpty()) {
            this.userId = Device.NO_ID
            this.serverId = Device.NO_SERVER_ID
        } else {
            this.userId = userId
            this.serverId = encryptionController.decodeNormalId(userId)
        }
    }
    fun setServerId(serverId: Long, encryptionController: EncryptionController) {
        if (serverId <= 0L) {
            this.serverId = Device.NO_SERVER_ID
            this.userId = Device.NO_ID
        } else {
            this.serverId = serverId
            this.userId = encryptionController.encodeNormalId(serverId)
        }
    }
    fun getLongUserId(): Long {
        return try {
            userId.toLong()
        } catch (e: Exception) {
            return Device.NO_SERVER_ID
        }
    }
    fun deviceFromHeaderWasValid() {
        headerWasValid = true
    }

    val isDeviceIdValid: Boolean
        get() = serverId > 0L
    val isDeviceIdRegistered: Boolean
        get() = infoFromServer


    companion object {
        const val NO_ID = "0"
        const val NO_SERVER_ID = 0L
        const val NO_BUILD_SELECTED = -2
        const val _NO_STORE = "None"
        const val _FREE_ACCESS_COUPON = "Free access by coupon"
        const val _KIDJO_BRAINTREE = "Kidjo account"
        const val _IOS = "Applestore"
        const val _PLAYSTORE = "Playstore"
        const val _SAMSUNG = "Samsungstore"
        const val _ORANGE = "Orange"
        const val _AMAZON = "Amazonstore"
        const val _MONDIA_MEDIA = "Mondia Media"
        const val _SWISSCOM = "Swisscom"
        const val _DOCOMO = "Docomo"
        const val _VIRGO = "Digital Virgo"
        const val _HUAWEI = "Huawei"
        const val _JIO = "Jio"
        const val _TWT = "Twt"
    }

    enum class StorePlatform(val raw: String,val label:String) {
        FREE_ACCESS_COUPON("coupon","Free Access Coupon"),
        KIDJO_BRAINTREE("kidjo","Braintree"),
        IOS("ios","Apple Store"),
        PLAYSTORE("playstore","Play store"),
        SAMSUNG("samsung","Samsung"),
        ORANGE("orange","Orange"),
        AMAZON("amazon","Amazon store"),
        MONDIA_MEDIA("mondia_media","Mondia media"),
        SWISSCOM("swisscom","Swisscom"),
        DOCOMO("docomo","Docomo"),
        VIRGO("virgo","Digital Virgo"),
        HUAWEI("huawei","Huawei"),
        JIO("jio","Jio"),
        TWT("twt","TimWe"),
        CAFEYN("cafeyn","Cafeyn"),
        ANDROID_TV("android_tv","Android TV"),
        SWITCH("switch","Switch"),
        MONDIA("mondia","Mondia"),
        SALT("salt","salt")
        ;

        fun platformDoesNotRequireUserId(): Boolean {
            return when (this) {
                IOS, PLAYSTORE, SAMSUNG, ORANGE, AMAZON, HUAWEI, JIO, TWT,CAFEYN, ANDROID_TV, SWITCH, MONDIA, SALT -> true
                KIDJO_BRAINTREE, MONDIA_MEDIA, SWISSCOM, DOCOMO, VIRGO, FREE_ACCESS_COUPON -> false
            }
        }
        companion object {
            fun fromRow(raw: String): StorePlatform {
                return when(raw.toLowerCase()) {
                    "coupon" -> FREE_ACCESS_COUPON
                    "apple", "ios" -> IOS
                    "android", "playstore" -> PLAYSTORE
                    "kidjo" -> KIDJO_BRAINTREE
                    "samsung" -> SAMSUNG
                    "orange" -> ORANGE
                    "amazon" -> AMAZON
                    "swisscom" -> SWISSCOM
                    "docomo" -> DOCOMO
                    "virgo" -> VIRGO
                    "huawei" -> HUAWEI
                    "jio" -> JIO
                    "twt" -> TWT
                    "mondia_media" -> MONDIA_MEDIA
                    "cafeyn" ->CAFEYN
                    "android_tv" -> ANDROID_TV
                    "switch"-> SWITCH
                    "mondia"->MONDIA
                    "salt" -> SALT
                    else -> KIDJO_BRAINTREE
                }
            }
        }
    }
}
