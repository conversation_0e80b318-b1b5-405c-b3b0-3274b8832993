package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.request

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import net.kidjo.server.shared.payments.publichers.v5.enums.OrangeOperators
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationSubscriptionRequest (
    var type          : String?       = null,
    var status        : String?       = null,
    var operationId   : String?       = null,
    var correlationId : String?       = null,
    var creationDate  : String?       = null,
    var date          : String?       = null,
    var subscription  : DVNotificationSubscription? = DVNotificationSubscription(),
    var data          : DVNotificationData?         = DVNotificationData(),
    var user          : DVNotificationUser?         = DVNotificationUser(),
    var customization : DVNotificationCustomization?= DVNotificationCustomization(),
    var dimensions    : DVNotificationDimensions?   = DVNotificationDimensions(),
    @JsonProperty("package") var dvPackage: DVNotificationPackage?      = DVNotificationPackage(),
    var offer         : DVNotificationOffer?        = DVNotificationOffer(),
    var iat           : Int?          = null
){
    fun getSubIndent() = getSubIndent(operationId, subscription?.subscriptionId)
    fun getUserIndent() = getUserIndent(user?.msisdn, user?.alias)
    fun getMSISDN() = user?.msisdn

    fun getMCCMNC() = user?.mccmnc

    fun  getNextBillingDate() = getNextDate(
        subscription?.nextInvoiceDate,
        subscription?.nextCycleDate,
        subscription?.suspensionDate,
        subscription?.expirationDate,
        subscription?.cancellationDate
    )
    fun getOperatorName() = getOperatorName(user?.mccmnc, subscription?.country)
    companion object{
        private fun getNextDate(nextInvoiceDate: String?,
                                nextCycleDate: String?,
                                suspensionDate: String?,
                                expirationDate: String?,
                                cancellationDate : String?
        ): LocalDateTime {
            if (!nextInvoiceDate.isNullOrBlank()) {
                return stringToLocalDateTime(nextInvoiceDate)
            }
            if (!nextCycleDate.isNullOrBlank()) {
                return stringToLocalDateTime(nextCycleDate)
            }
            if (!suspensionDate.isNullOrBlank()) {
                return stringToLocalDateTime(suspensionDate)
            }
            if (!expirationDate.isNullOrBlank()) {
                return stringToLocalDateTime(expirationDate)
            }
            if (!cancellationDate.isNullOrBlank()) {
                return stringToLocalDateTime(cancellationDate)
            }
            return LocalDateTime.now()
        }

        fun stringToLocalDateTime(date: String?): LocalDateTime {
            return ZonedDateTime.parse(date, DateTimeFormatter.ISO_DATE_TIME)
                .toLocalDateTime()
        }
        private fun getSubIndent(operationId:String?, subscriptionId: String? ): String? {
            if (!subscriptionId.isNullOrBlank()) {
                return subscriptionId
            }
            if (!operationId.isNullOrBlank()) {
                return operationId
            }
            return null
        }
        private fun getUserIndent(msisdn: String?, alias: String?): String? {
            if (!msisdn.isNullOrBlank()) {
                return msisdn
            }
            if (!alias.isNullOrBlank()) {
                return alias
            }
            /*if (!aliasGsm.isNullOrBlank()) {
                return aliasGsm
            }*/
            return null
        }
        private fun getOperatorName(mccmnc: Int?, countryCode: String?): String {
            if (mccmnc != null && mccmnc != 0) {
                return OrangeOperators.getByMCCMNC(mccmnc).name
            }
            if (!countryCode.isNullOrBlank()) {
                return OrangeOperators.NONE.name // OrangeOperators.getByCountryShort(countryCode).name
            }
            return OrangeOperators.NONE.name
        }
    }
}
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationSubscription (
    var subscriptionId      : String? = null,
    var status              : String? = null,
    var country             : String? = null,
    var subscriptionDate    : String? = null,
    var cancellationDate    : String? = null,
    var cancellationChannel : String? = null,
    var expirationDate      : String? = null,
    var suspensionDate      : String? = null,
    var terminationDate     : String? = null,
    var premiumStartDate    : String? = null,
    var nextCycleDate       : String? = null,
    var lastCycleDate       : String? = null,
    var lastInvoiceDate     : String? = null,
    var nextInvoiceDate     : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationCustomization(
    var layoutId    : String? = null,
    var commercialName: String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationDimensions (
    var billingMode    : String? = null,
    var billingChannel : String? = null,
    var orderChannel   : String? = null,
    var networkChannel : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationUser (
    var msisdn    : String? = null,
    var alias   : String? = null,
    //var aliasGsm  : String? = null,
    var ip        : String? = null,
    var mccmnc    : Int?    = null,
    var userAgent : String? = null,
    var locale    : String? = null,
    var email     : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationData (
    var clickId  : String? = null,
    var clientId : String? = null,
    var snowPlow : DVNotificationSnowPlow? = DVNotificationSnowPlow()
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationSnowPlow (
    var networkUserIdId  : String? = null,
    var clientSessionId : String? = null,
    var appId : String? = null,
    var sessionId : String? = null
)
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationPackage (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null
) {
    fun getLongId() = getId(id)
    companion object{
        private fun getId(id: Int?): Long? {
            return try {
                id?.toLong()
            } catch (e: Exception) {
                null
            }
        }
    }
}
@JsonIgnoreProperties(ignoreUnknown=true)
data class DVNotificationOffer (
    var id             : Int?    = null,
    var commercialName : String? = null,
    var owner          : String? = null,
    var legal          : String? = null
){
    fun getLongId() = getId(id)
    companion object{
        private fun getId(id: Int?):  Long? {
            return try {
                id?.toLong()
            } catch (e: Exception) {
                null
            }
        }
    }
}
