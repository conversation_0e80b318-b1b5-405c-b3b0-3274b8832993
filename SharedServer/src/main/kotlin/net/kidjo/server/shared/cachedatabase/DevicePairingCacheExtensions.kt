package net.kidjo.server.shared.cachedatabase

import net.kidjo.server.shared.models.DeviceUserPairing
import org.json.JSONObject

private const val USER_DEVICE_PAIRING_FOR_REGISTRATION_LIFETIME_IN_SECONDS = 3L * 60L * 60L //3 hours

fun CacheDatabase.deleteDeivceUserPairingToken(token: String): Bo<PERSON>an {
    val commands = connection.sync()
    commands.del(CacheDatabase.KEY_USER_DEVICE_PAIRING_FOR_REGISTRATION + token)

    return true
}
fun CacheDatabase.setDeivceUserPairingToken(token: String, deviceUserPairing: DeviceUserPairing): Boolean {
    val commands = connection.sync()
    val key = CacheDatabase.KEY_USER_DEVICE_PAIRING_FOR_REGISTRATION + token
    commands.set(key, deviceUserPairing.toServerJSONString())
    commands.expire(key,USER_DEVICE_PAIRING_FOR_REGISTRATION_LIFETIME_IN_SECONDS)

    return true
}
fun CacheDatabase.getDeivceUserPairingToken(token: String): DeviceUserPairing? {
    if (token == "") return null

    val commands = connection.sync()
    val jsonString = commands.get(CacheDatabase.KEY_USER_DEVICE_PAIRING_FOR_REGISTRATION + token)
    if (jsonString == null) return null

    try {
        val json = JSONObject(jsonString)
        return DeviceUserPairing.FromServerJSON(token, json)
    } catch (e: Exception) {
        return null
    }
}