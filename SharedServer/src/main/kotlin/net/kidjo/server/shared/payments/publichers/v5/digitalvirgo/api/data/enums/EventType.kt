package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums

/*
ONE-SHOT One-shot purchase is completed.
SUBSCRIPTION Subscription is created.
CANCELLATION Subscription renewal is cancelled.
TERMINATION Subscription or One-shot expired.
SUSPENSION Subscription renewal process is suspended, as the current cycle is not paid for.
INVOICE Subscription or One-shot generated an invoice.
REFUND Invoice was
*/
enum class EventType(val raw: String) {
    ONE_SHOT("ONE-SHOT"),
    SUBSCRIPTION("SUBSCRIPTION"),
    CANCELLATION("CANCELLATION"),
    TERMINATION("TERMINATION"),
    SUSPENSION("SUSPENSION"),
    EXPIRATION("EXPIRATION"),
    INVOICE("INVOICE"),
    REFUND("REFUND"),
    RELOAD("RELOAD"),
    NONE("NONE")

    ;

    companion object {
        fun fromRaw(raw: String?): EventType {
            return when (raw) {
                ONE_SHOT.raw -> ONE_SHOT
                SUBSCRIPTION.raw -> SUBSCRIPTION
                CANCELLATION.raw -> CANCELLATION
                TERMINATION.raw -> TERMINATION
                SUSPENSION.raw -> SUSPENSION
                EXPIRATION.raw -> EXPIRATION
                INVOICE.raw -> INVOICE
                REFUND.raw -> REFUND
                else -> NONE
            }
        }
    }
}