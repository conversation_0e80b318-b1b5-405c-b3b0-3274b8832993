package net.kidjo.server.shared.database

import net.kidjo.server.shared.database.creator.video
import net.kidjo.server.shared.models.Video


fun DatabaseController.favorites_getList(kidId: Long, offset: Int, limit: Int): List<Video> {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("SELECT id,title,isPremium,ageMin,ageMax,duration,compile,GROUP_CONCAT(formatId) as videoFormatIds, GROUP_CONCAT(fileSize) as fileSize FROM videos JOIN videos_formats ON videos_formats.videoId = videos.id JOIN kid_favorites ON kid_favorites.videoId = videos.id WHERE kid_favorites.kidId = ? AND videos.isActive = 1 GROUP BY videos.id ORDER BY kid_favorites.created_at ASC")
    statement.setLong(1,kidId)
    statement.fetchSize = kotlin.math.min(limit, config.db_maxFetch)

    val results = statement.executeQuery()
    results.absolute(offset)

    var i = 0
    val videos = ArrayList<Video>()

    while (results.next() && i < limit) {
        videos.add(objectCreator.video(results))
        i++
    }

    statement.close()
    connection.close()
    return videos
}

fun DatabaseController.favorites_add(kidId: Long, videoId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("INSERT INTO kid_favorites(kidId, videoId) VALUES (?,?) ON DUPLICATE KEY UPDATE created_at = CURRENT_TIMESTAMP")
    statement.setLong(1,kidId)
    statement.setLong(2,videoId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}

fun DatabaseController.favorites_remove(kidId: Long, videoId: Long): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("DELETE FROM kid_favorites WHERE kidId = ? AND videoId = ?")
    statement.setLong(1,kidId)
    statement.setLong(2,videoId)

    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}