package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums

/*
SUCCESS The request has succeeded.
ERROR The request was interrupted due to an error (see detail in Error table).
Depending on the countries or carriers guidelines, a cancellation request will have an immediate effect and subscription will directly be transitioned into the TERMINATED state.
PENDING The request is in an intermediary state and the final result is not known yet. A request can remain in pending indefinitely if no final state is reached.
*/
enum class RequestStatusType(val raw: String) {
    SUCCESS("SUCCESS"),
    ERROR("ERROR"),
    PENDING("PENDING"),
    NONE("NONE")

    ;

    companion object {
        fun fromRaw(raw: String?): RequestStatusType {
            return when (raw) {
                SUCCESS.raw -> SUCCESS
                ERROR.raw -> ERROR
                PENDING.raw-> PENDING
                else -> NONE
            }
        }
    }
}