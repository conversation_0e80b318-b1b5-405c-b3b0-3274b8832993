package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.notification

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.cancel.cancelKIDJOSubscription
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.cancel.cancelSubscription
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.update.updateKIDJOSubscription
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private val logger = LoggerFactory.getLogger("updateUnsubscriptionEvent - ")
fun updateUnsubscriptionEvent(
    subIndent: String,
    dvNextBillingDate: LocalDateTime,
    subStatus: String,
    correlationId: String? = null,
    databaseController: DatabaseController,
): Long? {
    try {
        var subscription: SubscriptionRoot? = null
        runBlocking<Unit> {
            launch(Dispatchers.IO) {
                subscription = databaseController.subscription_getByToken(subIndent)
            }
        }

        if (subscription != null) {
            if (subStatus == SubscriptionStatusType.TERMINATED.name || subStatus == SubscriptionStatusType.EXPIRED.name) {
                logger.info("UPDATE UNSUBSCRIPTION EVENT: $subIndent, status:$subStatus ")
                val resultCancel =
                    cancelKIDJOSubscription(
                        nextBillDate = LocalDateTime.now(),
                        subToken = subIndent,
                        subStatus = SubscriptionStatusType.fromRaw(subStatus).raw,
                        correlationId = correlationId,
                        databaseController = databaseController
                    )

                if (!resultCancel) {
                    logger.error("ERROR: TERMINATE OR EXPIRED SUBSCRIPTION subscription id: $subIndent, status:$subStatus, dvNextBillingDate: $dvNextBillingDate ")
                    return null
                }
                logger.info("SUCCESS: CANCELING KIDJO subscription id: $subIndent, status:$subStatus, dvNextBillingDate: $dvNextBillingDate ")
                return subscription?.userId
            } else if ((subStatus == SubscriptionStatusType.CANCELLED.name) || (subStatus == SubscriptionStatusType.CANCELLED.name)) {
                logger.info("Keep the subscription until the existing bill cycle exist")
                cancelSubscription(
                    subToken = subIndent,
                    subStatus = SubscriptionStatusType.fromRaw(subStatus).raw,
                    correlationId = correlationId,
                    databaseController = databaseController
                )
            } else {
                val resultUpdate =
                    updateKIDJOSubscription(
                        nextBillDate = if(subscription!!.nextBillDate <= dvNextBillingDate ) LocalDateTime.now().plusDays(3) else dvNextBillingDate,
                        subToken = subIndent,
                        subStatus = SubscriptionStatusType.fromRaw(subStatus).raw,
                        correlationId = correlationId,
                        databaseController = databaseController
                    )

                if (!resultUpdate) {
                    logger.error("ERROR: UPDATING KIDJO SUBSCRIPTION id: $subIndent, status: $subStatus, dvNextBillingDate: $dvNextBillingDate ")
                    return null
                }
                logger.info("SUCCESS: UPDATING SUBSCRIPTION id: $subIndent, status:$subStatus, dvNextBillingDate: $dvNextBillingDate ")
                return subscription?.userId
            }
        }

        logger.info("EXIT: UPDATE UNSUBSCRIPTION EVENT ")
        return null
    } catch (e: Exception) {
        logger.error("ERROR: MODIFY KIDJO SUBSCRIPTION, ${e.localizedMessage}")
        return null
    }
}
