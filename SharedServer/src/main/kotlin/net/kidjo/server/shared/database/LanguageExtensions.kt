package net.kidjo.server.shared.database


fun DatabaseController.language_setActiveForDevice(deviceId: Long, languageId: Int): Boolean {
    val connection = dataSource.connection
    val statement = connection.prepareStatement("UPDATE devices SET languageId = ? WHERE id = ?")
    statement.setInt(1,languageId)
    statement.setLong(2,deviceId)
    val results = statement.executeAndCheck()

    statement.close()
    connection.close()
    return results
}