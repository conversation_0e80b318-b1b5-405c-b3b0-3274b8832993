package net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.sms

import net.kidjo.server.shared.models.entity.PartnerSMS
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.LoggerFactory

private val logger = LoggerFactory.getLogger("DVSenditoApi - CREATE SENDITO SMS TEXT - ")

fun createSMSText(
    userLogin: String,
    userPass: String,
    operatorName: String,
    redirectURL: String?,
    packageId: Long
): String {
    var messageTemplate: String? = null

    transaction {
        addLogger(StdOutSqlLogger)
        // Fetch the message for the given telecom and packageId
        messageTemplate = PartnerSMS.select {
            (PartnerSMS.telecom eq operatorName) and (PartnerSMS.packageId eq packageId.toInt())
        }.map { it[PartnerSMS.message] }.firstOrNull()

        // If not found, fetch the default message (packageId = 0)
        if (messageTemplate == null) {
            messageTemplate = PartnerSMS.select {
                (PartnerSMS.telecom eq operatorName) and (PartnerSMS.packageId eq 0)
            }.map { it[PartnerSMS.message] }.firstOrNull()
        }
    }

    return if (messageTemplate != null) {
        messageTemplate!!
            .replace("_{UserLogin}", userLogin)
            .replace("_{UserPwd}", userPass)
            .replace("_{UserUrl}", redirectURL ?: "")
            .also { logger.info("CREATED SENDITO MESSAGE: $it") }
    } else {
        logger.error("No message template found for operator: $operatorName and packageId: $packageId")
        ""
    }
}
