package net.kidjo.plugins

import com.mysql.jdbc.jdbc2.optional.MysqlDataSource
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import io.ktor.server.application.*
import io.ktor.server.config.*
import io.ktor.util.*
import org.flywaydb.core.Flyway
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import javax.sql.DataSource

private val flyWayFeatureLogger: Logger = LoggerFactory.getLogger("net.kidjo.plugins.FlywayFeature")

class FlywayFeature(configuration: Configuration) {
    val dataSource: HikariDataSource

    init {
        val mysqlDataSource = MysqlDataSource()
        mysqlDataSource.databaseName = configuration.dbName
        mysqlDataSource.serverName = configuration.host
        mysqlDataSource.user = configuration.user
        mysqlDataSource.setPassword(configuration.password)
        mysqlDataSource.useSSL=configuration.useSSl

        val hikariConfig = HikariConfig()
        hikariConfig.dataSource = mysqlDataSource
        hikariConfig.maximumPoolSize = 10
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true")
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "500")
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048")
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true")
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true")
        hikariConfig.addDataSourceProperty("useLocalTransactionState", "true")
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true")
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true")
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true")
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true")
        hikariConfig.addDataSourceProperty("maintainTimeStats", "true")

        dataSource = HikariDataSource(hikariConfig)
    }

    private val locations = configuration.locations
    private val schemas = configuration.schemas
    private val commands: Set<FlywayCommand> = configuration.commands

    class Configuration {
        var outOfOrderMigration: Boolean?=null
        val useSSl: Boolean=false
        var dataSource: DataSource? = null
        var locations: Array<String>? = null
        var schemas: Array<String>? = null
        var host: String? = null
        var dbName: String? = null
        var user: String? = null
        var password: String? = null
        var baselineOnMigrate: Boolean? = null
        internal var commands: Set<FlywayCommand> = setOf(Info, Migrate)
        fun commands(vararg commandsToExecute: FlywayCommand) {
            commands = commandsToExecute.toSet()
        }
    }

    companion object Feature : BaseApplicationPlugin<Application, Configuration, FlywayFeature> {
        override val key = AttributeKey<FlywayFeature>("FlywayFeature")

        override fun install(pipeline: Application, configure: Configuration.() -> Unit): FlywayFeature {
            val configuration = Configuration().apply(configure)
            val flywayFeature = FlywayFeature(configuration)
            if (configuration.host == null || configuration.dbName == null || configuration.user == null || configuration.password == null)
                throw ApplicationConfigurationException(
                    "DataSource is not configured"
                )
            flyWayFeatureLogger.info("Flyway migration has started")

            val flyway = Flyway
                .configure(pipeline.environment.classLoader)
                .dataSource(flywayFeature.dataSource)
                .also { config -> flywayFeature.locations?.let { config.locations(*it) } }
                .also { config -> flywayFeature.schemas?.let { config.schemas(*it) } }
                .baselineOnMigrate(configuration.baselineOnMigrate ?: false)
                .outOfOrder(configuration.outOfOrderMigration?:false)
                .load()

            flywayFeature.commands.map { command ->
                flyWayFeatureLogger.info("Running command: ${command.javaClass.simpleName}")
                command.run(flyway)
            }

            flyWayFeatureLogger.info("Flyway migration has finished")
            return flywayFeature
        }
    }

}

sealed class FlywayCommand {
    abstract fun run(flyway: Flyway)
}

data object Migrate : FlywayCommand() {
    override fun run(flyway: Flyway) {
        flyway.migrate()
    }
}

data object Clean : FlywayCommand() {
    override fun run(flyway: Flyway) {
        flyway.clean()
    }
}

data object Info : FlywayCommand() {
    override fun run(flyway: Flyway) {
        flyway.info()
    }
}

data object Validate : FlywayCommand() {
    override fun run(flyway: Flyway) {
        flyway.validate()
    }
}

data object Baseline : FlywayCommand() {
    override fun run(flyway: Flyway) {
        flyway.baseline()
    }
}

data object Repair : FlywayCommand() {
    override fun run(flyway: Flyway) {
        flyway.repair()
    }
}
