package net.kidjo.bean


import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import kotlinx.serialization.Required
import kotlinx.serialization.Serializable
import java.math.BigDecimal


data class Coupon(
    @JsonProperty("productId")
    @Required
    val productId: Int,
    @Required
    @JsonProperty("typeId")
    val typeId: Int,
    @Required
    @JsonProperty("expirationDate")
    val expirationDate: String,
    @JsonIgnore
    @JsonProperty("duration")
    val duration: String,
    @Required
    @JsonProperty("groupId")
    val groupId: String,
    @Required
    @JsonProperty("startDate")
    val startDate: String,
    @JsonIgnore
    @JsonProperty("partnerId")
    val partnerId: Int,
    @JsonIgnore
    @JsonProperty("redeemAmount")
    val redeemAmount: String?,
    @JsonProperty("numberToGenerate")
    val numberToGenerate: String?,
    @JsonIgnore
    @JsonProperty("serialNumber")
    val serialNumber: String="",
    @JsonIgnore
    @JsonProperty("discountPrice")
    val discountPrice: BigDecimal?,
    @JsonIgnore
    @JsonProperty("braintreeId")
    val braintreeId: String=""
)