package net.kidjo.common.models

enum class Language(
    val id: Int,
    val shortName: String,
    val nativeName: String
) {

    ENGLISH(1, "en", "English"),
    FRENCH(34, "fr", "Français"),
    SPANISH(27, "es", "Español"),
    PORTUGUESE(89, "pt", "Português"),
    ITALIAN(51, "it", "Italiano"),
    GERMAN(23, "de", "Deutsch"),
    ARABIC(6, "ar", "عربى"),
    RUSSIAN(94, "ru", "Ру́сский");

    companion object {


        const val NO_ID = -1

        const val ID_ENGLISH = 1
        const val SHORT_ENGLISH = "en"
        const val NATIVE_ENGLISH = "English"

        const val ID_FRENCH = 34
        const val SHORT_FRENCH = "fr"
        const val NATIVE_FRENCH = "Français"

        const val ID_SPANISH = 27
        const val SHORT_SPANISH = "es"
        const val NATIVE_SPANISH = "Español"

        const val ID_PORTUGUESE = 89
        const val SHORT_PORTUGUESE = "pt"
        const val NATIVE_PORTUGUESE = "Português"

        const val ID_ITALIAN = 51
        const val SHORT_ITALIAN = "it"
        const val NATIVE_ITALIAN = "Italiano"

        const val ID_GERMAN = 23
        const val SHORT_GERMAN = "de"
        const val NATIVE_GERMAN = "Deutsch"

        const val ID_ARABIC = 6
        const val SHORT_ARABIC = "ar"
        const val NATIVE_ARABIC = "عربى"

        const val ID_RUSSIAN = 94
        const val SHORT_RUSSIAN = "ru"
        const val NATIVE_RUSSIAN = "Ру́сский"

        fun fromId(id: Int): Language {
            return when (id) {
                ENGLISH.id -> ENGLISH
                FRENCH.id -> FRENCH
                SPANISH.id -> SPANISH
                PORTUGUESE.id -> PORTUGUESE
                ITALIAN.id -> ITALIAN
                GERMAN.id -> GERMAN
                ARABIC.id -> ARABIC
                RUSSIAN.id -> RUSSIAN
                else -> ENGLISH
            }
        }

        fun fromShortName(shortName: String): Language? {
            return when (shortName) {
                SHORT_ENGLISH -> ENGLISH
                SHORT_FRENCH -> FRENCH
                SHORT_SPANISH -> SPANISH
                SHORT_PORTUGUESE -> PORTUGUESE
                SHORT_ITALIAN -> ITALIAN
                SHORT_GERMAN -> GERMAN
                SHORT_ARABIC -> ARABIC
                SHORT_RUSSIAN -> RUSSIAN
                else -> null
            }
        }

        fun getArrayOfAllLanguages(): Array<Language> = arrayOf(
            ENGLISH, FRENCH, SPANISH,
            PORTUGUESE, ITALIAN, GERMAN, ARABIC, RUSSIAN
        )

    }
}

data class LanguageSimple(
    val name: String,
    val code: String,
    val isActive: Boolean
)
