package net.kidjo.common.models

data class SubscriptionEvent(val type: Type) {
    enum class Type(val raw: String) {
        OPEN("open"), WENT_OFFLINE("went_offline"),DID_SUBSCRIBE("did_subscribe"),NOT_SUBSCRIBE("not_subscribed");

        companion object {
            fun FromRawOptional(raw: String): Type? {
                return when (raw) {
                    OPEN.raw -> OPEN
                    WENT_OFFLINE.raw -> WENT_OFFLINE
                    DID_SUBSCRIBE.raw -> DID_SUBSCRIBE
                    NOT_SUBSCRIBE.raw -> NOT_SUBSCRIBE
                    else -> null
                }
            }
        }
    }
}