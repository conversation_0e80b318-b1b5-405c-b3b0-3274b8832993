package net.kidjo.common.models

data class UserConfig(val imageBucket: String,
                      val apiSecret: String,
                      val platform: Platform) {

    companion object {
        fun Create(scale: Int, width: Int, height: Int, apiSecret: String, platform: Platform): UserConfig {
            val actualWidth = width * scale
            val actualHeight = height * scale

            val aspectRatio: Double
            val shortestLength: Int
            if (width > height) {
                aspectRatio = width.toDouble() / height.toDouble()
                shortestLength = actualHeight
            } else {
                aspectRatio = height.toDouble() / width.toDouble()
                shortestLength = actualWidth
            }
            val useTablet: Boolean = aspectRatio < 1.6

            val device: String
            val size: String

            if (useTablet) {
                device = "tablet"
                if (shortestLength < 768) size = "s"
                else if (shortestLength < 1536) size = "m"
                else size = "l"
            } else {
                device = "phone"
                if (shortestLength < 640) size = "s"
                else if (shortestLength < 1440) size = "m"
                else size = "l"
            }

            return UserConfig("$device-$size",apiSecret,platform)
        }
    }
    enum class Platform(val raw: String) {
        KIDJO_BRAINTREE("kidjo"), IOS("ios"), PLAYSTORE("playstore"), SAMSUNG("samsung"), AMAZON("amazon");

        fun platformDoesNotRequireUserId(): Boolean {
            return when (this) {
                IOS, PLAYSTORE, SAMSUNG, AMAZON -> true
                KIDJO_BRAINTREE -> false
            }
        }
        companion object {
            //returns playstore if nothing else
            fun FromString(raw: String): Platform {
                val lowerCaseRaw = raw.toLowerCase()
                return when(lowerCaseRaw) {
                    "apple", "ios" -> IOS
                    "android", "playstore" -> PLAYSTORE
                    "kidjo" -> KIDJO_BRAINTREE
                    "samsung" -> SAMSUNG
                    "amazon" -> AMAZON

                    else -> KIDJO_BRAINTREE
                }
            }
        }
    }

}