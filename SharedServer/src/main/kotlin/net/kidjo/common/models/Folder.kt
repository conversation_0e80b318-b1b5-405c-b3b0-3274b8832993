package net.kidjo.common.models

data class Folder(val id: String,
                  val contentType: ContentType,
                  val namedId: String,
                  val title: String,
                  val mediaType: MediaType,
                  val folderIsNotFiltered: Boolean) {

    constructor(id: String, contentType: ContentType,mediaType: MediaType, namedId: String): this(id,contentType,namedId,"",mediaType,false)

    fun getLongId(): Long {
        return try {
            id.toLong()
        }catch (e: Exception) {
            return NO_ID_LONG
        }
    }

    companion object {
        const val NO_ID = "0"
        const val NO_ID_LONG = 0L
        const val ID_HISTORY = "history"
        const val NAMED_ID_HISTORY = "history"
        fun GetHistoryFolder(): Folder {
            return Folder(ID_HISTORY, ContentType.MIXED, MediaType.VIDEO, NAMED_ID_HISTORY)
        }
    }
    enum class MediaType(val raw: String) {
        VIDEO("video"), GAME("game"), MIXED_GAMES("mixed_games");
        companion object {

            fun FromRaw(raw: String): MediaType {
                return when(raw) {
                    VIDEO.raw -> VIDEO
                    GAME.raw -> GAME
                    MIXED_GAMES.raw -> MIXED_GAMES
                    else -> VIDEO
                }
            }
        }
    }
    enum class ContentType(val raw: String) {
        MIXED("mix"), ENTERTAINMENT("entertainment"), EDUCATION("education");
        companion object {
            const val NUMBER_OF_TYPES = 3

            const val ROW_MIXED = 0
            const val ROW_ENTERTAINMENT = 1
            const val ROW_EDUCATION = 2


            fun FromRaw(raw: String): ContentType {
                when(raw) {
                    MIXED.raw -> return MIXED
                    ENTERTAINMENT.raw -> return ENTERTAINMENT
                    EDUCATION.raw -> return EDUCATION
                    else -> return MIXED
                }
            }
        }
    }
}