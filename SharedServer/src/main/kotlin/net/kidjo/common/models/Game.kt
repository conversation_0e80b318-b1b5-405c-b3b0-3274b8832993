package net.kidjo.common.models

data class Game(val id: Long,
                val isLocked: Boolean,
                val type: Game.Type,
                val difficulty: Difficulty,
                val position: Int ) {

    enum class Type(val raw: String) {
        PUZZLE("puzzle"),
        MEMORY("memory"),
        <PERSON><PERSON><PERSON>("music"),
        COLOR("color"),
        <PERSON><PERSON><PERSON>("hide"),
        FIND_THE_WAY("find_the_way")
        ;

        companion object {
            fun FromRaw(raw: String): Type {
                return when (raw) {
                    PUZZLE.raw -> PUZZLE
                    MEMORY.raw -> MEMORY
                    MUSIC.raw -> MUSIC
                    COLOR.raw -> COLOR
                    HIDE.raw -> HIDE
                    FIND_THE_WAY.raw -> FIND_THE_WAY
                    else -> MEMORY
                }
            }
        }
    }

    enum class Difficulty(val raw: String) {
        EASY("easy"), MEDIUM("medium"), HARD("hard"), EXTREME("veryHard");

        companion object  {
            fun FromRaw(raw: String): Difficulty {
                return when (raw) {
                    EASY.raw -> EASY
                    MEDIUM.raw -> MEDIUM
                    HARD.raw -> HARD
                    EXTREME.raw -> EXTREME
                    else -> MEDIUM
                }
            }
        }
    }
}