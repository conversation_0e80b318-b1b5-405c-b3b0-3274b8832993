#!/usr/bin/env bash

# Connect to AWS via CLI
eval "$(aws ecr get-login --no-include-email --region us-east-1)"
# Tag old latest builds as a backup on AWS
docker tag 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:backup-1 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:backup-2
docker push 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:backup-2
docker tag 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:latest 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:backup-1
docker push 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:backup-1
# Build new production image
docker build -t kidjo-api-prod ./production/
# Tag new production image (latest in local) as latest on AWS
docker tag kidjo-api-prod:latest 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:latest
docker push 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-api-prod:latest

echo "Done pushed production"
