#!/usr/bin/env bash
function uploadIntoS3
{
	aws s3api put-object --bucket kidjo --acl public-read --key $2 --body $1 --content-type $3 --content-encoding $4
}

gzip -9 -k account_coupon.js > account_coupon.js.gz -f
gzip -9 -k main.css > main.css.gz -f

uploadIntoS3 ./account_coupon.js.gz public/js/account_coupon.js.gz application/javascript gzip
uploadIntoS3 ./main.css.gz public/css/main.css.gz text/css gzip
