package net.kidjo.server.plugins.monitoring

import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.testing.*
import net.kidjo.server.plugins.configureApiMonitoring
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ApiMonitoringTest {

    @Test
    fun testApiMonitoringPlugin() = testApplication {
        application {
            configureApiMonitoring()
            
            // Add a simple test route
            routing {
                get("/test") {
                    call.respondText("Test response")
                }
                get("/slow-test") {
                    // Simulate slow response
                    Thread.sleep(1500)
                    call.respondText("Slow response")
                }
            }
        }

        // Make some test requests
        client.get("/test")
        client.get("/slow-test")
        
        // Check monitoring stats
        val statsResponse = client.get("/monitoring/stats")
        assertEquals(HttpStatusCode.OK, statsResponse.status)
        
        val statsBody = statsResponse.bodyAsText()
        assertTrue(statsBody.contains("totalRequests"))
        assertTrue(statsBody.contains("averageResponseTimeMs"))
    }

    @Test
    fun testMonitoringDashboard() = testApplication {
        application {
            configureApiMonitoring()
        }

        val dashboardResponse = client.get("/monitoring/dashboard")
        assertEquals(HttpStatusCode.OK, dashboardResponse.status)
        assertEquals(ContentType.Text.Html.withCharset(Charsets.UTF_8), dashboardResponse.contentType())
        
        val dashboardBody = dashboardResponse.bodyAsText()
        assertTrue(dashboardBody.contains("Kidjo API Monitoring Dashboard"))
    }

    @Test
    fun testPerformanceSummary() = testApplication {
        application {
            configureApiMonitoring()
            
            routing {
                get("/test-endpoint") {
                    call.respondText("Test")
                }
            }
        }

        // Make a test request
        client.get("/test-endpoint")
        
        // Check performance summary
        val summaryResponse = client.get("/monitoring/performance-summary")
        assertEquals(HttpStatusCode.OK, summaryResponse.status)
        
        val summaryBody = summaryResponse.bodyAsText()
        assertTrue(summaryBody.contains("overview"))
        assertTrue(summaryBody.contains("totalRequests"))
    }

    @Test
    fun testSlowEndpointsDetection() = testApplication {
        application {
            configureApiMonitoring()
            
            routing {
                get("/fast") {
                    call.respondText("Fast response")
                }
                get("/slow") {
                    Thread.sleep(1200) // Simulate slow response > 1 second
                    call.respondText("Slow response")
                }
            }
        }

        // Make requests
        client.get("/fast")
        client.get("/slow")
        
        // Check slow endpoints
        val slowResponse = client.get("/monitoring/slow-endpoints")
        assertEquals(HttpStatusCode.OK, slowResponse.status)
        
        val slowBody = slowResponse.bodyAsText()
        assertTrue(slowBody.contains("slowEndpoints"))
    }

    @Test
    fun testMonitoringHealth() = testApplication {
        application {
            configureApiMonitoring()
        }

        val healthResponse = client.get("/monitoring/health")
        assertEquals(HttpStatusCode.OK, healthResponse.status)
        
        val healthBody = healthResponse.bodyAsText()
        assertTrue(healthBody.contains("status"))
        assertTrue(healthBody.contains("healthy"))
    }
}
