$ASSET_ROOT: "https://d3aod987c9rl70.cloudfront.net/";
$FONT_ASSET_ROOT: "https://d3aod987c9rl70.cloudfront.net/fonts/";

$TEST_GREEN: #27ae60;
$TEST_GREEN_FADED: rgba(39, 174, 96, 0.85);

$_MAIN_COLOR: #3dc6b4;
$_MAIN_COLOR_FADE: rgba(61, 198, 180, 0.85);

$KIDJO_RED: #f2575a;
$KIDJO_PURPLE: #6b4d86;
$MAIN_COLOR: $TEST_GREEN;
$MAIN_COLOR_FADE: $TEST_GREEN_FADED;

$BACKGROUND_COLOR: #effffd;

$FACEBOOK_BLUE: #2553b4;
$FACEBOOK_BLUE_FADE: #3161c7;
$GOOGLE_RED: #dc4e41;
$GOOGLE_RED_FADE: #f06356;

$TEXT_COLOR_DARK: #383838;
$TEXT_COLOR_LITE_DARK: #474747;
$TEXT_COLOR_EXTRA_LITE_DARK: #5e5e5e;

$BUTTON_COLOR_MAIN: $MAIN_COLOR; //#1abc9c; //#16a085; //#3dc6b4;
$BUTTON_COLOR_MAIN_FADED: $MAIN_COLOR_FADE; //#1abc9c; //#16a085; //#3dc6b4;
$BUTTON_COLOR_ALT: rgb(157, 88, 171);

$PHONE_PX_WIDTH: 520px;
$TABLET_PX_WIDTH: 768px;
$DESKTOP_PX_WIDTH: 1024px;

.bold-font {
  font-family: "BreeBold", Arial, sans-serif;
}
.regular-font {
  font-family: "BreeRegular", Arial, sans-serif;
}
.thin-font {
  font-family: "BreeThin", Arial, sans-serif;
}
.light-font {
  font-family: "BreeLight", Arial, sans-serif;
}
.bree-oblique-font {
  font-family: "BreeOblique", Arial, sans-serif;
}

$BASE_MARGIN: 24px;

//mondia

$MODNIA_GENERAL_BORDER_WIDTH: 6px;
$MODNIA_TEXT_DARK: #515151;
$MODNIA_RED_GRADIENT: #d6575b;
