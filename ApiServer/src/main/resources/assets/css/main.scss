@import 'constants';

html, body {
  background: $BACKGROUND_COLOR;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;

  font-family: 'BreeRegular', Arial, sans-serif;
  color: $TEXT_COLOR_DARK;
}
input:focus {
  outline: none;
}
.page {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.card-width {
  //width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.header {
  height: 50px;
  padding: 10px $BASE_MARGIN 6px $BASE_MARGIN;
  display: flex;
  flex-direction: row;
  .left-box {
    flex: 1;
  }
  .kidjo-logo {
    width: 155px;
    height: 100%;
  }
  .right-box {
    flex: 1;
    display: flex;
    .right-box-text {
      @extend .regular-font;
      margin: auto 0 auto auto;
      font-size: 20px;
      color: $TEXT_COLOR_DARK;
      &:lang(ar) {
        margin: auto auto auto 0;
      }
    }
    Button.right-box-text {
      background: transparent;
      border: none;
      border-radius: 6px;
    }
  }
}

//general
.hidden {
  display: none !important;
}

.g-recaptcha > div {
  margin: 0 auto;
}

////===========
//FONTS
@font-face {
  font-family: 'BreeBold';
  src: url($FONT_ASSET_ROOT + 'BreeBold.woff2') format('woff2'),
  url($FONT_ASSET_ROOT + 'BreeBold.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'BreeRegular';
  src: url($FONT_ASSET_ROOT + 'BreeRegular.woff2') format('woff2'),
  url($FONT_ASSET_ROOT + 'BreeRegular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'BreeThin';
  src: url($FONT_ASSET_ROOT + 'BreeThin.woff2') format('woff2'),
  url($FONT_ASSET_ROOT + 'BreeThin.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'BreeLight';
  src: url($FONT_ASSET_ROOT + 'BreeLight.woff2') format('woff2'),
  url($FONT_ASSET_ROOT + 'BreeLight.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
//@font-face {
//  font-family: 'BreeOblique';
//  src: url($FONT_ASSET_ROOT + 'BreeOblique.woff2') format('woff2'),
//  url($FONT_ASSET_ROOT + 'BreeOblique.woff') format('woff');
//  font-weight: normal;
//  font-style: normal;
//}


//=====
//LOADING SPINNER
//======
.loading-card-container {
  width: 100%;
  height: 100%;
  position: fixed;
  display: none;
  align-items: center;
  z-index: 10;
  background: rgba(0, 0, 0, 0.49);
  &.show {
    opacity: 1.0;
    display: flex;
  }
  .loading-card {
    height: 120px;
    width: 120px;
    background: white;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    padding: 16px;
    margin: auto;
    .loading-text {
      @extend .regular-font;
      font-size: 20px;
      text-align: center;
      color: $TEXT_COLOR_DARK;
    }
  }

}
.loading-spinner,
.loading-spinner:before,
.loading-spinner:after {
  background: $BUTTON_COLOR_MAIN_FADED;
  -webkit-animation: load1 1s infinite ease-in-out;
  animation: load1 1s infinite ease-in-out;
  width: 1em;
  height: 4em;
}
.loading-spinner {
  color: $BUTTON_COLOR_MAIN_FADED;
  text-indent: -9999em;
  margin: 32px auto;
  position: relative;
  font-size: 11px;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loading-spinner:before,
.loading-spinner:after {
  position: absolute;
  top: 0;
  content: '';
}
.loading-spinner:before {
  left: -1.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loading-spinner:after {
  left: 1.5em;
}
@-webkit-keyframes load1 {
  0%,
  80%,
  100% {
    box-shadow: 0 0;
    height: 4em;
  }
  40% {
    box-shadow: 0 -2em;
    height: 5em;
  }
}
@keyframes load1 {
  0%,
  80%,
  100% {
    box-shadow: 0 0;
    height: 4em;
  }
  40% {
    box-shadow: 0 -2em;
    height: 5em;
  }
}


@media screen and (min-width: $PHONE_PX_WIDTH) {
  .page {
    max-width: 520px;
  }
  .card-width {
    max-width: 520px;
  }
}
@media screen and (min-width: $TABLET_PX_WIDTH) {
  .page {
    max-width: 720px;
  }
  .card-width {
    max-width: 520px;
  }
}

@import "account_coupon";
