@import "constants";
@import "colors";

//images

.kidjo-logo {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/logo.svg");
  background-size: contain;
}
.google-icon {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/google_plus.svg");
  background-size: contain;
}
.facebook-icon {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/facebook_icon.svg");
  background-size: contain;
}
.paypal-icon {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/paypal.svg");
  background-size: contain;
}
.coupon-icon {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/coupon_white.svg");
  background-size: contain;
}
.credit-card-icon {
  background: no-repeat center
    url($ASSET_ROOT+"images/svgs/credit_card_white.svg");
  background-size: contain;
}
.paypal-icon-black {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/paypal_black.svg");
  background-size: contain;
}
.coupon-icon-black {
  background: no-repeat center url($ASSET_ROOT+"images/svgs/coupon_black.svg");
  background-size: contain;
}
.credit-card-icon-black {
  background: no-repeat center
    url($ASSET_ROOT+"images/svgs/credit_card_black.svg");
  background-size: contain;
}

//default account button
.kidjo-green {
  color: $_MAIN_COLOR;
}
.account-button {
  @extend .regular-font;
  background: $BUTTON_COLOR_MAIN;
  height: 52px;
  color: #fff;
  font-size: 22px;
  border-radius: 4px;
  border: none;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
  transition: background 0.2s ease;
  &:active,
  &:hover {
    background: $BUTTON_COLOR_MAIN_FADED;
  }
  &.do-not-cancel {
    height: 64px;
  }
  &.cancel-button {
    margin-left: auto;
    margin-right: auto;
    padding-left: 16px;
    padding-right: 16px;
    height: 44px;
    background: #e1e1e1;
    color: $TEXT_COLOR_LITE_DARK;
  }
}
.small-button {
  @extend .regular-font;
  background: $BUTTON_COLOR_ALT;
  height: 48px;
  width: 144px;
  color: #fff;
  font-size: 18px;
  border-radius: 4px;
  border: none;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
}
.account-button-icon {
  @extend .regular-font;
  background: $BUTTON_COLOR_MAIN;
  height: 52px;
  color: #fff;
  font-size: 22px;
  border-radius: 4px;
  border: none;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;

  .icon {
    margin-left: 4px;
  }
  .text {
    margin: auto auto auto 12px;
    &:lang(ar) {
      margin: auto 12px auto auto;
    }
  }
}

//base container
.sign-up-card-container {
  display: flex;
  align-items: center;
  animation-fill-mode: forwards;
  justify-content: center;
  align-content: center;
  &.animation-timing {
    -webkit-transition: 250ms ease-in-out;
    -moz-transition: 250ms ease-in-out;
    -ms-transition: 250ms ease-in-out;
    -o-transition: 250ms ease-in-out;
    transition: 250ms ease-in-out;
  }
  .sign-up-card {
    background: white;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    margin: 16px $BASE_MARGIN 16px $BASE_MARGIN;
    flex: 1;

    > .sign-up-card-inner {
      padding: 22px 28px 22px 28px;
      display: flex;
      flex-direction: row;

      .large-spacer {
        margin-top: 32px;
      }
      .main-spacer {
        margin-top: 16px;
      }
      .minor-spacer {
        margin-top: 10px;
      }
      .sign-up-card-normal {
        display: flex;
        flex-direction: column;
        flex: 1;
        .section-header {
          @extend .bold-font;
          color: $TEXT_COLOR_DARK;
          text-align: center;
          font-size: 24px;
        }
        .section-header-1 {
          @extend .bold-font;
          color: $TEXT_COLOR_DARK;
          text-align: center;
          font-size: 32px;
        }
        .section-header-2 {
          @extend .bold-font;
          color: $TEXT_COLOR_DARK;
          text-align: center;
          font-size: 28px;
        }
        .section-header-3 {
          @extend .bold-font;
          color: $TEXT_COLOR_DARK;
          text-align: center;
          font-size: 24px;
        }
        .section-subheader {
          @extend .regular-font;
          color: $TEXT_COLOR_LITE_DARK;
          text-align: center;
          font-size: 16px;
        }
        .section-subheader-1 {
          @extend .regular-font;
          color: $TEXT_COLOR_LITE_DARK;
          text-align: center;
          font-size: 22px;
        }
      }
      .sign-up-card-extra {
        flex: 1;
      }
    }
  }
}
.sign-up-card-container.card-focused {
  //animation: slideFromLeft 400ms ease-in-out;
}
.sign-up-card-container.done {
  transform: translateX(-100%) scale(0.9);
}
.sign-up-card-container.entering {
  transform: translateX(100%) scale(0.9);
  //animation: slideFromLeft 250ms ease-in-out;
  //display: none;
}
.sign-up-card-container.scroll-in {
  //transform: translateX(100%) scale(.9);
  animation: slideFromLeft 250ms ease-in-out;
  //display: none;
}

.sign-up-card-inner.account-card {
  .line-spacer {
    background: $TEXT_COLOR_DARK;
    height: 2px;
    width: 8px;
    align-self: center;
  }

  .fb-sign-in,
  .google-sign-in {
    height: 48px;
    .text {
      font-size: 20px;
    }
    .google-icon {
      height: 32px;
      width: 32px;
      margin-left: 4px;
      &:lang(ar) {
        margin-left: 0;
        margin-right: 4px;
      }
    }
    .facebook-icon {
      height: 27px;
      width: 32px;
      margin-left: 4px;
      &:lang(ar) {
        margin-left: 0;
        margin-right: 4px;
      }
    }
  }

  .fb-sign-in {
    background: $FACEBOOK_BLUE;
    transition: background 0.2s ease;
    &:active,
    &:hover {
      background: $FACEBOOK_BLUE_FADE;
    }
  }
  .google-sign-in {
    background: $GOOGLE_RED;
    transition: background 0.2s ease;
    &:active,
    &:hover {
      background: $GOOGLE_RED_FADE;
    }
  }
  .create-account {
    margin-top: 16px;
  }
}

.error-message {
  color: #d40200;
  padding: 16px 0;
  text-align: center;
}

.forgotten-password {
  color: #383838;
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
}

//======
//END
//=====

//======
//LOGIN
//======
.email-button-row {
  display: flex;
  button {
    flex: 1;
  }
  :first-child {
    margin-right: 5px;
    &:lang(ar) {
      margin-left: 5px;
      margin-right: 0;
    }
  }
  :last-child {
    background: $TEXT_BOX_BACKGROUND_COLOR_HIGHLIGHT_ALT;
    margin-left: 5px;
    &:active,
    &:hover {
      background: #9e58ad;
    }
    &:lang(ar) {
      margin-left: 0;
      margin-right: 5px;
    }
  }
}
.email-pass-input-box {
  border: solid 3px $BUTTON_COLOR_MAIN;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  width: 98%;
  margin-left: auto;
  margin-right: auto;
  .input-line-spacer {
    height: 1px;
    background: $BUTTON_COLOR_MAIN_FADED;
  }
  input {
    @extend .regular-font;
    border: none;
    background: transparent;
    height: 32px;
    padding: 8px;
    text-align: center;
    color: $BUTTON_COLOR_MAIN;
    font-size: 20px;
  }
  input::placeholder {
    color: $BUTTON_COLOR_MAIN_FADED;
  }
}
.already-have-account-title {
  text-align: center;
  font-size: 16px;
  color: $TEXT_COLOR_LITE_DARK;
  margin-top: 16px;
}
.already-have-button {
  text-align: center;
  font-size: 16px;
  color: #2980b9;
}

.sign-up-card-inner.plan-card {
  .line-spacer {
    background: $TEXT_COLOR_DARK;
    height: 2px;
    width: 8px;
    align-self: center;
  }
}

//======
//Coupon
//======
.coupon-input {
  @extend .bold-font;
  background: transparent;
  font-size: 20px;
  color: rgb(72, 72, 72);
  height: 32px;
  padding: 8px;
  border: solid 3px $BUTTON_COLOR_ALT;
  border-radius: 6px;
}
.coupon-enter-container,
.coupon-payment-container {
  display: flex;
  flex-direction: column;

  .coupon-warning {
    @extend .bold-font;
    text-align: center;
    color: $TEXT_COLOR_RED_ERROR_MESSAGE;
    font-size: 24px;
    margin: 16px 0 16px 0;
  }
  &.hidden {
    display: none;
  }
}

.logicom {
  .right-box-text {
    display: none !important;
  }
  .section-header-1 {
    font-family: "BreeRegular", Arial, sans-serif !important;
  }
  .section-header-3 {
    font-size: 20px !important;
    font-family: "BreeRegular", Arial, sans-serif !important;
  }
}
.coupon-message {
  padding: 0px 16px 0px 16px;
  text-align: center;
  font-size: 20px;
  color: $TEXT_COLOR_RED_ERROR_MESSAGE;
  &.hidden {
    display: none;
  }
}
.coupon-info-container {
  padding: 8px;
  background: $TEXT_BOX_BACKGROUND_COLOR_HIGHLIGHT_ALT;
  border: solid 3px $TEXT_BOX_BACKGROUND_COLOR_HIGHLIGHT_ALT;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  width: 250px;
  margin-left: auto;
  margin-right: auto;
  .icon {
    margin-left: 8px;
    width: 34px;
  }
  .coupon-info-text-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-left: 16px;
    .top-info-text {
      @extend .bold-font;
      flex: 1;
      font-size: 21px;
      color: white;
    }
    .bottom-info-text {
      flex: 1;
      font-size: 19px;
      color: white;
      margin-left: 8px;
    }
  }
}
//======
//Payment
//======
.subscript-footer {
  text-align: center;
  font-size: 12px;
  color: $TEXT_COLOR_LITE_DARK;
}
.cc-container {
  border: solid 3px $BUTTON_COLOR_MAIN;
  border-radius: 6px;
  display: flex;
  flex-direction: column;

  .line-spacer {
    height: 1px;
    background: $BUTTON_COLOR_MAIN_FADED;
  }
  .input-div {
    background: transparent;
    border: none;
    font-size: 18px;
    font-family: sans-serif;
    color: rgb(72, 72, 72);
    height: 32px;
    padding: 8px;
  }
  .expire-and-cvv {
    display: flex;
    flex-direction: row;

    .width-spacer {
      width: 1px;
      background: $BUTTON_COLOR_MAIN_FADED;
    }
    .cc-ccv-input {
      flex: 3;
    }
    .cc-expire {
      flex: 2;
    }
  }
}
.paypal {
  background: #003087;
}
.paypal-button {
}
.coupon {
  background: $BUTTON_COLOR_ALT;
}
.payment-button {
  text-align: left;
  .icon {
    height: 26px;
    width: 26px;
  }
}
#error-box {
  padding: 0px 16px 0px 16px;
  text-align: center;
  font-size: 20px;
  color: $TEXT_COLOR_RED_ERROR_MESSAGE;
}
//======
//Info
//======
.account-info-header {
  @extend .bold-font;
  font-size: 18px;
  color: $TEXT_COLOR_DARK;
}
.account-info-box {
  padding: 8px;
  border-top: solid 3px rgba(197, 197, 197, 0.2);
  //border-radius: 4px;
  .account-info-box-text {
    margin-left: 16px;
    margin-right: 16px;
    display: flex;
    padding: 8px 0 8px 0;
    align-content: center;
    align-items: center;
    border-bottom: solid 2px rgba(197, 197, 197, 0.2);
    .item-header {
      @extend .bold-font;
      font-size: 16px;
      color: $TEXT_COLOR_DARK;
      width: 100px;
    }
    .item-text {
      @extend .regular-font;
      font-size: 18px;
      color: $TEXT_COLOR_DARK;
    }
    .item-icon {
      margin-right: 8px;
      height: 32px;
      width: 32px;
      &:lang(ar) {
        margin-right: 0;
        margin-left: 8px;
      }
    }
  }
  .account-info-request {
    margin-left: 16px;
    margin-right: 16px;
    display: flex;
    padding: 8px 0 8px 0;
    align-content: center;
    align-items: center;
    border-bottom: solid 2px rgba(197, 197, 197, 0.2);
    .account-info-request-text {
      @extend .regular-font;
      font-size: 16px;
      color: $TEXT_COLOR_DARK;
      text-align: left;
      margin: auto 8px auto 0;
    }
    .account-info-request-button {
      @extend .bold-font;
      margin: auto 0 auto auto;
      background: $BUTTON_COLOR_MAIN;
      font-size: 18px;
      color: white;
      border-radius: 4px;
      border: none;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
      padding: 8px 12px 8px 12px;
      &:lang(ar) {
        margin: auto auto auto 0;
      }
    }
  }
  .account-info-box-button {
    @extend .bold-font;
    height: 40px;
    background: $BUTTON_COLOR_MAIN;
    margin-left: 16px;
    margin-right: 16px;
    font-size: 18px;
    color: white;
    border-radius: 4px;
    border: none;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
    width: 144px;
    display: flex;
    align-content: center;
    justify-content: center;
    align-items: center;
    &.cancel-button {
      font-family: "BreeRegular", Arial, sans-serif;
      margin-left: auto;
      background: none;
      box-shadow: none;
      color: $TEXT_COLOR_LITE_DARK;
      &:lang(ar) {
        margin-left: 16px;
        margin-right: auto;
      }
    }
    &.resubscribe-button {
      margin-left: auto;
      &:lang(ar) {
        margin-left: 16px;
        margin-right: auto;
      }
    }
  }
}

//======
//Cancel
//======
.cancel-message {
  line-height: 28px;
  a {
    color: white;
    background: $BUTTON_COLOR_ALT;
    padding: 6px;
    border-radius: 4px;
    text-decoration: none;
  }
}

//SCALING SECTION

.sign-up-card-normal.account-card {
  width: 275px;
  //max-width: 500px;
}
.sign-up-card-normal.plan-card {
  width: 275px;
  //max-width: 500px;
}
.sign-up-card-extra {
  display: none;
}
.sign-up-card.payment-card {
  width: 100%;
}
.sign-up-card-normal.payment-card {
  width: 100%;
}

//SHARED
.plan-container {
  display: flex;
  flex-direction: row;
  height: 64px;
  padding: 8px 0px 8px 0px;

  .plan-label {
    color: $TEXT_COLOR_LITE_DARK;
    font-size: 16px;
    margin: auto 6px auto 6px;
  }
  .plan-description-container {
    margin: auto 0 auto auto;
    border: solid 3px rgba(197, 197, 197, 0.2);
    border-radius: 4px;
    padding: 8px 24px 8px 24px;
    &:lang(ar) {
      margin: auto auto auto 0;
    }
    .plan-time {
      @extend .bold-font;
      color: $TEXT_COLOR_EXTRA_LITE_DARK;
      font-size: 20px;
      text-align: center;
    }
    .plan-price {
      color: $TEXT_COLOR_EXTRA_LITE_DARK;
      font-size: 16px;
      text-align: center;
    }
  }
}

//MONDIA stuff

.text-border {
  display: block;
  text-shadow: rgb(255, 255, 255) 4px 0px 0px,
    rgb(255, 255, 255) 3.87565px 0.989616px 0px,
    rgb(255, 255, 255) 3.51033px 1.9177px 0px,
    rgb(255, 255, 255) 2.92676px 2.72656px 0px,
    rgb(255, 255, 255) 2.16121px 3.36588px 0px,
    rgb(255, 255, 255) 1.26129px 3.79594px 0px,
    rgb(255, 255, 255) 0.282949px 3.98998px 0px,
    rgb(255, 255, 255) -0.712984px 3.93594px 0px,
    rgb(255, 255, 255) -1.66459px 3.63719px 0px,
    rgb(255, 255, 255) -2.51269px 3.11229px 0px,
    rgb(255, 255, 255) -3.20457px 2.39389px 0px,
    rgb(255, 255, 255) -3.69721px 1.52664px 0px,
    rgb(255, 255, 255) -3.95997px 0.56448px 0px,
    rgb(255, 255, 255) -3.97652px -0.432781px 0px,
    rgb(255, 255, 255) -3.74583px -1.40313px 0px,
    rgb(255, 255, 255) -3.28224px -2.28625px 0px,
    rgb(255, 255, 255) -2.61457px -3.02721px 0px,
    rgb(255, 255, 255) -1.78435px -3.57996px 0px,
    rgb(255, 255, 255) -0.843183px -3.91012px 0px,
    rgb(255, 255, 255) 0.150409px -3.99717px 0px,
    rgb(255, 255, 255) 1.13465px -3.8357px 0px,
    rgb(255, 255, 255) 2.04834px -3.43574px 0px,
    rgb(255, 255, 255) 2.83468px -2.82216px 0px,
    rgb(255, 255, 255) 3.44477px -2.03312px 0px,
    rgb(255, 255, 255) 3.84068px -1.11766px 0px,
    rgb(255, 255, 255) 3.9978px -0.132717px 0px;
}
.mondia-background-container {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: url($ASSET_ROOT+"images/background2_row.jpg") no-repeat scroll
    center top;
  background-size: cover;
}
.mondia-main-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .logo {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left center;
    height: 13vh;
    width: 100%;
    margin: 1vh 0 0vw 1vw;
    z-index: 10;
  }
  .content {
    margin: auto 0 auto 0;
    width: 100%;
    text-decoration: none;
    font-size: 5vh;
    display: flex;
    flex-direction: column;
    padding-left: constant(safe-area-inset-left);
    z-index: 10;
    .title-text {
      @extend .text-border;
      font-family: "BreeBold", Arial, sans-serif;
      text-decoration: none;
      font-size: 50px;
      color: $MODNIA_TEXT_DARK;
      margin: 0 0 0 3vw;
      z-index: 10;
      .red-highlight-text {
        font-size: 58px;
        color: $KIDJO_RED;
      }
    }
    .subtitle-text {
      @extend .text-border;
      font-family: "BreeRegular", Arial, sans-serif;
      color: $MODNIA_TEXT_DARK;
      font-size: 32px;
      margin: 0 0 0 3vw;
      z-index: 10;
    }
    .subscribe-button {
      font-family: "BreeBold", Arial, sans-serif;
      height: 48px;
      width: 300px;
      text-decoration: none;
      background: linear-gradient(
        -90deg,
        $MODNIA_RED_GRADIENT,
        $KIDJO_RED,
        $MODNIA_RED_GRADIENT
      );
      margin: 8px 0 0 3vw;
      display: flex;
      border: #fff solid 4px;
      border-radius: 8px;
      box-shadow: rgba(0, 0, 0, 0.3) 0 1px 10px 0;
      animation: shake 6s infinite forwards;
      .text {
        margin: auto;
        text-align: center;
        color: #fff;
        font-size: 32px;
      }
    }
    .restore-button {
      color: $MODNIA_TEXT_DARK;
      font-family: "BreeRegular", Arial, sans-serif;
      width: 300px;
      text-align: center;
      font-size: 16px;
      margin: 8px 0 0 3vw;
      z-index: 10;
    }
  }
}

//Horrible animation stuff
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "BreeRegular", Arial, sans-serif;
}
.mondia-background-container {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
  animation-delay: 0.5s;
  -webkit-animation-delay: 0.5s;
  -webkit-animation-duration: 3.5s;
  animation-duration: 3.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
@-webkit-keyframes zoomIn {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.18);
  }
}
@keyframes zoomIn {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.18);
  }
}
@-webkit-keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
  }

  71%,
  75%,
  79%,
  83%,
  87% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  73%,
  77%,
  81%,
  85%,
  89% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

//ANIMATIONS
@keyframes slideFromLeft {
  0% {
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
    display: none;
  }
  100% {
    -webkit-transform: translateX(0%);
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%);
    display: flex;
  }
}
@keyframes slideToRight {
  0% {
    -webkit-transform: translateX(0%);
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%);
    display: flex;
  }
  100% {
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    -o-transform: translateX(-100%);
    transform: translateX(-100%);
    display: none;
  }
}
