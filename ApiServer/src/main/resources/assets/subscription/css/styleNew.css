@import url("https://p.typekit.net/p.css?s=1&k=lov8yik&ht=tk&f=70&a=657509&app=typekit&e=css");

@font-face {
    font-family: "bree";
    src: url("https://use.typekit.net/af/b5463b/00000000000000000001338b/27/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff2"), url("https://use.typekit.net/af/b5463b/00000000000000000001338b/27/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff"), url("https://use.typekit.net/af/b5463b/00000000000000000001338b/27/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("opentype");
    font-display: auto;
    font-style: normal;
    font-weight: 700;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    width: 100%;
}

body {
    background-color: #12b3ff;
    overflow: hidden;
    padding-top: 20vh;
    position: relative;
}

/* ================================================================ *\
    #Reset
\* ================================================================ */

* {
    padding: 0;
    margin: 0;
    outline: 0;
    box-sizing: border-box;
}

#image_2, #image_3, #image_4, #image_5, #image_6 {
    opacity: 0;
}

#logo {
    left: 6%;
    max-height: 80px;
    position: absolute;
    top: 4%;
    width: 10vw;
}

#trees {
    background-position: -20px 25px;
    background-repeat: no-repeat;
    background-size: contain;
    bottom: 0;
    position: absolute;
    width: 50%;
    height: 30%;
}

.background {
    position: relative;
}

.background img {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}

.catchphrases {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}

.catchphrases .phrase {
    align-items: center;
    display: flex;
    position: absolute;
}

.catchphrases .first {
    left: 22%;
    top: 4%;
}

.catchphrases .second {
    left: 22%;
    top: 15%;
}

.circle {
    padding: 10px;
    height: 2.6vw;
    width: 2.6vw;
}

.circle img {
    display: block;
    height: 100%;
    width: 100%;
}

.text {
    color: white;
    display: flex;
    align-items: center;
    font-family: "bree", sans-serif;
    font-size: 24px;
    font-size: 2.4vw;
}

.text img {
    height: 32px;
    height: 7.2vh;
    margin-right: 8px;
}

.box {
    align-items: flex-end;
    display: flex;
    flex-flow: column wrap;
    justify-content: center;
    height: 100%;
    top: 0;
    right: 0;
    padding-right: 1vw;
    position: absolute;
    width: 100%;
}

.box.has-description {
    justify-content: flex-end;
}

.close {
    border: 3px solid white;
    border-radius: 4px;
    cursor: pointer;
    position: absolute;
    top: 30px;
    right: 30px;
    background-color: #fe4c13;
    height: 40px;
    width: 40px;
    padding: 7px;
    z-index: 10;
}

.close:before {
    border-radius: 2px;
    content: " ";
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #714d4d;
}

.close img {
    width: 100%;
    height: 100%;
}

.button {
    background-color: #ffa325;
    border: 6px solid white;
    border-radius: 24px;
    color: white;
    cursor: pointer;
    display: block;
    font-family: "bree", sans-serif;
    font-size: 32px;
    font-size: 3.2vw;
    font-weight: bold;
    margin: 0;
    padding: 10px;
    position: relative;
    width: 36vw;
}

.button:before {
    border-radius: 18px;
    content: " ";
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 4px solid #714d4d;
}

.links {
    display: none;
    font-weight: bold;
    margin: 0;
    padding: 20px 0;
    padding: 2vh 0;
    position: relative;
    text-align: center;
    width: 36vw;
}

.has-description .links {
    display: block;
}

/*
.promo-icon {
    display: none;
}
*/

img.promo-icon {
    height: 26px;
    height: 5.2vh;
    margin-left: 4px;
}

.promo-icon.has-promo {
    display: inline-block;
}

.restore {
    color: #3a260f;
    display: block;
    font-family: "bree", sans-serif;
    font-size: 24px;
    font-size: 2.4vw;
    text-decoration: none;
}

.link {
    color: #3a260f;
    font-family: "bree", sans-serif;
    font-size: 16px;
    font-size: 1.6vw;
    margin: 0 10px;
}

.description {
    color: #3a260f;
    font-family: "bree", sans-serif;
    font-size: 1.3vw;
    padding: 5px;
    width: 100%;
}

.description-vertical {
    padding: 20px;
    text-align: left;
}

.offer, .offer-vertical {
    color: #fff;
    font-family: "bree", sans-serif;
    font-size: 28px;
}

.offer {
    bottom: 4%;
    font-size: 2.8vw;
    margin-top: 10px;
    position: absolute;
    right: 4%;
    text-align: right;
    width: 40%;
}

.offer-vertical {
    padding: 20px;
    text-align: center;
}

.strikethrough {
    text-decoration: line-through;
}

button.close {
    display:none !important;
}

@media (min-width: 1000px) {
    .close {
        height: 60px;
        width: 60px;
        padding: 10px;
    }
}

@media (min-width: 800px) and (max-height: 400px) {
    .button {
        width: 45vw;
    }
    .links {
        width: 45vw;
    }
}

@media (min-width: 700px) and (max-height: 350px) {
    .button {
        width: 50vw;
    }
    .links {
        width: 50vw;
    }
}

@media (min-width: 1000px) and (min-height: 700px) {
    /* iPad format */
    #trees {
        width: 800px;
        height: 350px;
        background-position: -20px 110px;
    }
}
