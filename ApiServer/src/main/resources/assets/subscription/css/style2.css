.text-border, .main-container .content .title-text, .main-container .content .subtitle-text {
  display: block;
  text-shadow: white 4px 0px 0px, white 3.87565px 0.989616px 0px, white 3.51033px 1.9177px 0px, white 2.92676px 2.72656px 0px, white 2.16121px 3.36588px 0px, white 1.26129px 3.79594px 0px, white 0.282949px 3.98998px 0px, white -0.712984px 3.93594px 0px, white -1.66459px 3.63719px 0px, white -2.51269px 3.11229px 0px, white -3.20457px 2.39389px 0px, white -3.69721px 1.52664px 0px, white -3.95997px 0.56448px 0px, white -3.97652px -0.432781px 0px, white -3.74583px -1.40313px 0px, white -3.28224px -2.28625px 0px, white -2.61457px -3.02721px 0px, white -1.78435px -3.57996px 0px, white -0.843183px -3.91012px 0px, white 0.150409px -3.99717px 0px, white 1.13465px -3.8357px 0px, white 2.04834px -3.43574px 0px, white 2.83468px -2.82216px 0px, white 3.44477px -2.03312px 0px, white 3.84068px -1.11766px 0px, white 3.9978px -0.132717px 0px; }

.background-container {
  position: absolute;
  top: 0;
  left: 0; }

.main-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%; }
  .main-container .logo {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left center;
    height: 13vh;
    width: 100%;
    margin: 1vh 0 0vw 1vw; }
  .main-container .content {
    margin: auto 0 auto 0;
    width: 100%;
    text-decoration: none;
    font-size: 5vh;
    display: flex;
    flex-direction: column;
    padding-left: constant(safe-area-inset-left); }
    .main-container .content .title-text {
      font-family: 'BreeBold',Arial,sans-serif;
      text-decoration: none;
      font-size: 50px;
      color: #515151;
      margin: 0 0 0 3vw; }
      .main-container .content .title-text .red-highlight-text {
        font-size: 58px; }
    .main-container .content .subtitle-text {
      font-family: 'BreeRegular',Arial,sans-serif;
      color: #515151;
      font-size: 32px;
      margin: 0 0 0 3vw; }
    .main-container .content .subscribe-button {
      font-family: 'BreeBold',Arial,sans-serif;
      height: 48px;
      width: 300px;
      text-decoration: none;
      background: linear-gradient(-90deg, #d6575b, #f2575a, #d6575b);
      margin: 8px 0 0 3vw;
      display: flex;
      border: #fff solid 4px;
      border-radius: 8px;
      box-shadow: rgba(0, 0, 0, 0.3) 0 1px 10px 0;
      animation: shake 6s infinite forwards; }
      .main-container .content .subscribe-button .text {
        margin: auto;
        text-align: center;
        color: #fff;
        font-size: 32px; }
    .main-container .content .restore-button {
      color: #515151;
      font-family: 'BreeRegular',Arial,sans-serif;
      width: 300px;
      text-align: center;
      font-size: 16px;
      margin: 8px 0 0 3vw; }

.red-highlight-text {
  color: #f2575a; }

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'BreeRegular', Arial, sans-serif; }

.background-container {
  z-index: -1;
  overflow: hidden;
  width: 100%;
  height: 100%;
  display: block;
  position: fixed; }

.background-container {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
  animation-delay: 0.5s;
  -webkit-animation-delay: 0.5s;
  -webkit-animation-duration: 3.5s;
  animation-duration: 3.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both; }

@-webkit-keyframes zoomIn {
  0% {
    transform: scale(1); }
  100% {
    transform: scale(1.18); } }
@keyframes zoomIn {
  0% {
    transform: scale(1); }
  100% {
    transform: scale(1.18); } }
@-webkit-keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0); }
  71%, 75%, 79%, 83%, 87% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0); }
  73%, 77%, 81%, 85%, 89% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0); } }

/*# sourceMappingURL=style2.css.map */
