/* LAYOUT
================================================== */
html, body {width: 100%;height: 100%;margin: 0;padding: 0;font-family: 'BreeRegular', Arial, sans-serif;}

#landing-yearly-subscription{  overflow: hidden; width: 100%; height: 100%; display: block;position: fixed; background: #fff;}

.euro{
    font-family:Arial;
    font-size: 3.5vw;
}

#yearly-subscription-top{
    max-height: 210px;
    max-height:28vh;
    height: 210px;
    height: 28vh;
    margin: 0;
    margin: 0;
    width: 100%;
    width: 100vw;
    position: relative;
}

#yearly-subscription-middle{
    max-height: 358px;
    max-height:45vh;
    height: 358px;
    height: 45vh;
    margin: 0;
    margin: 0;
    width: 100%;
    width: 100vw;
    position: relative;
    text-align: center;
}

#yearly-subscription-bottom{
    max-height: 200px;
    max-height:27vh;
    height: 199px;
    height: 27vh;
    margin: 0;
    margin: 0;
    width: 100%;
    width: 100vw;
    position: relative;
}

img#logo-img{
    max-height: 120px;
    max-height:20vh;
    height: 80px;
    height: 12vh;
    margin: 30px 0 0 20px;
    margin: 4vh 0 0 1.5vw;
    width: 200px;
    width: 18vw;
    position: absolute;
}


#yearly-subscription-middle h1{
    font-family: 'BreeRegular', Arial, sans-serif;
    font-size: 4.5vw;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: 1;
    text-align: center;
    color: #515151;
    padding: 0px 10px;
    margin: 3vh 0 3vh 0;
}
#yearly-subscription-middle h1 span.number{
    color: #f284bc;
}
#yearly-subscription-middle h1 span.text {
    color: #fd74b8;
}

#yearly-subscription-middle h3 {
    font-family: 'BreeRegular', Arial, sans-serif;
    font-size: 2.5vw;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1;
    text-align: center;
    color: #636363;
    margin: 2vh 0 2vh 0;
}

a#yearly-sub{
    text-decoration: none;
    border-radius: 20px;
    background-color: #50beb0;
    font-family: 'BreeRegular', Arial, sans-serif;
    letter-spacing: 0.01em;
    font-size: 20px;
    font-size: 3vw;
    margin: 60px auto 0px;
    margin: 5vh auto 0vh;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: 0.925;
    color: #ffffff;
    padding: 19px 25px 16px;
    display: inline-block;
    position: relative;
}

/*a#yearly-sub #button-icon{ position: absolute; right: 20px; top: 21px; height: 3.5vh}*/

.prices {
    font-family: 'BreeRegular', Arial, sans-serif;
    font-size: 2.2vw;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1;
    text-align: center;
    color: #919191;
    margin: 2vh 0 3vh 0;
}

.prices .price{
    font-family: 'BreeRegular', Arial, sans-serif;
    font-size: 20px;
    font-size: 2vw;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 0.6;
    color: #292929;
    display: inline-block;
    margin: 15px 5px 0 0;
    margin: 1.5vh 5px 0 0;
    position: relative;
    padding: 5px;
}

.prices .price.striked{ font-size: 16px; font-size: 2vw; color: #919191; }
.prices .price.striked span.strike-span{
    -ms-transform: rotate(-19deg); /* IE 9 */
    -webkit-transform: rotate(-19deg); /* Chrome, Safari, Opera */
    transform: rotate(-19deg);
    background-color: #5a5a5a;
    height: 2px;
    left: 0;
    position: absolute;
    top: 44%;
    width: 100%;
}

.striked{
    text-decoration: line-through;
    color:#919191;
}
.actual-price {
    font-size: 120%;
    line-height: 150%;
}
.pink{
    color:#f284bc;
}

.smaller {
    font-size: 75%;
}

#oval {
    background-color: #9d58ab;
    border: 18px solid rgba(157, 87, 171, 0.25);
    border-radius: 100%;
    -webkit-border-radius: 100%; border-radius: 100%;
    height: 38vw;
    left: -16vw;
    position: absolute;
    -ms-transform: rotate(-46deg); /* IE 9 */
    -webkit-transform: rotate(-46deg); /* Chrome, Safari, Opera */
    transform: rotate(-46deg);
    width: 36vw;
    bottom: -19vw;

    box-shadow: 9px 1px 2px rgba(157, 87, 171, 0.05);
}


#oval h4 {
    font-family: 'BreeRegular', Arial, sans-serif;
    font-size: 2.5vw;
    font-weight: 300;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.4;
    text-align: center;
    color: #ffffff;
    float: right;
    margin: 12vw 3vw 0 0;
    -ms-transform: rotate(26deg); /* IE 9 */
    -webkit-transform: rotate(26deg); /* Chrome, Safari, Opera */
    transform: rotate(26deg);
    height: 60%;
    display: inline-block;
    vertical-align: bottom;
}

#oval h4 span{
    font-weight: bold;
}

#oval h4 span.twoline{
    font-size: 2.8vw;
}



a#restore-subscription{
    text-decoration: none;
    color: #ffffff;
    letter-spacing: 0.01em;
    padding: 11px 20px 10px;
    display: inline-block;
    position: absolute;
    right: 3vw;
    bottom: 4vw;
    font-family: 'BreeRegular', Arial, sans-serif;
    font-size: 24px;
    font-size: 2vw;
    font-weight: normal;
    font-style: normal;
    font-stretch: normal;
    line-height: 1;
    color: #50beb0;
}

img#gift-packs{
    height: auto;
    margin: auto;
    width: 210px;
    width: 21vw;
    position: absolute;
    bottom: 1.2vh;
    left: 0px;
    right: 0px;
    display: block;

}


#shapes .trex:nth-child(n+2){

    animation: kaboom 5s infinite forwards;
}
#shapes .trex:nth-child(n+1){

    animation: kaboom 11s infinite forwards;
}
@keyframes kaboom {

    10% {
        transform: scale(1);
    }
    80% {
        transform: scale(1);
    }
    90% {
        transform: scale(3.2);
    }
    100% {
        transform: scale(1);
    }
}


@-webkit-keyframes bounce {
    from, 20%, 53%, 80%, to {
        -webkit-animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0);
    }

    40%, 43% {
        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        -webkit-transform: translate3d(0, -30px, 0);
        transform: translate3d(0, -30px, 0);
    }

    70% {
        -webkit-animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        -webkit-transform: translate3d(0, -15px, 0);
        transform: translate3d(0, -15px, 0);
    }

    90% {
        -webkit-transform: translate3d(0,-4px,0);
        transform: translate3d(0,-4px,0);
    }
}



#shapes{ position: absolute; width: 100%;height: 100%}



@keyframes kaboom {

    10% {
        transform: scale(1);
    }
    50% {
        transform: scale(3.2);
    }
    100% {
        transform: scale(1);
    }
}


#shapes .fadeInRightBig:nth-child(n+2){

    animation: fadeInRightBig 6s infinite forwards;
}
#shapes .fadeInRightBig:nth-child(n+1){

    animation: fadeInRightBig 12s infinite forwards;
}


@-webkit-keyframes fadeInRightBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(2000px, 0, 0);
        transform: translate3d(2000px, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}


#shapes .hinge:nth-child(n+2){

    animation: hinge 7s infinite forwards;
}
#shapes .hinge:nth-child(n+1){

    animation: hinge 14s infinite forwards;
}

#shapes .zoomOutDown:nth-child(n+2){

    animation: zoomOutDown 6s infinite forwards;
}
#shapes .zoomOutDown:nth-child(n+1){

    animation: zoomOutDown 13s infinite forwards;
}


@-webkit-keyframes zoomOutDown {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}

@keyframes zoomOutDown {
    40% {
        opacity: 1;
        -webkit-transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        transform: scale3d(.475, .475, .475) translate3d(0, -60px, 0);
        -webkit-animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
        animation-timing-function: cubic-bezier(0.550, 0.055, 0.675, 0.190);
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        transform: scale3d(.1, .1, .1) translate3d(0, 2000px, 0);
        -webkit-transform-origin: center bottom;
        transform-origin: center bottom;
        -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1);
    }
}


@-webkit-keyframes hinge {
    81% {
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    82%, 86% {
        -webkit-transform: rotate3d(0, 0, 1, 80deg);
        transform: rotate3d(0, 0, 1, 80deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    84%, 90% {
        -webkit-transform: rotate3d(0, 0, 1, 60deg);
        transform: rotate3d(0, 0, 1, 60deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
        opacity: 1;
    }

    to {
        -webkit-transform: translate3d(0, 700px, 0);
        transform: translate3d(0, 700px, 0);
        opacity: 0;
    }
}

@keyframes hinge {
    80% {
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    82%, 86% {
        -webkit-transform: rotate3d(0, 0, 1, 80deg);
        transform: rotate3d(0, 0, 1, 80deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
    }

    84%, 90% {
        -webkit-transform: rotate3d(0, 0, 1, 60deg);
        transform: rotate3d(0, 0, 1, 60deg);
        -webkit-transform-origin: top left;
        transform-origin: top left;
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out;
        opacity: 1;
    }

    to {
        -webkit-transform: translate3d(0, 700px, 0);
        transform: translate3d(0, 700px, 0);
        opacity: 0;
    }
}



#shapes .fadeInLeftBig:nth-child(n+2){

    animation: fadeInLeftBig 8s infinite forwards;
}
#shapes .fadeInLeftBig:nth-child(n+1){

    animation: fadeInLeftBig 15s infinite forwards;
}

@-webkit-keyframes fadeInLeftBig {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-2000px, 0, 0);
        transform: translate3d(-2000px, 0, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}


#shapes .fadeInLeftBig:nth-child(n+2){

    animation: rotateOut 6s infinite;
}
#shapes .fadeInLeftBig:nth-child(n+1){

    animation: rotateOut 12s infinite;
}


@keyframes rotateOut {
    0% {     -ms-transform: rotate(0deg); /* IE 9 */
        -webkit-transform: rotate(0deg); /* Chrome, Safari, Opera */
        transform: rotate(0deg);}
    50% {     -ms-transform: rotate(180deg); /* IE 9 */
        -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
        transform: rotate(180deg);}
    100% {    -ms-transform: rotate(360deg); /* IE 9 */
        -webkit-transform: rotate(360deg); /* Chrome, Safari, Opera */
        transform: rotate(360deg);}
}
@-webkit-keyframes rotateOut {
    0% {     -ms-transform: rotate(0deg); /* IE 9 */
        -webkit-transform: rotate(0deg); /* Chrome, Safari, Opera */
        transform: rotate(0deg);}
    50% {     -ms-transform: rotate(180deg); /* IE 9 */
        -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
        transform: rotate(180deg);}
    100% {    -ms-transform: rotate(360deg); /* IE 9 */
        -webkit-transform: rotate(360deg); /* Chrome, Safari, Opera */
        transform: rotate(360deg);}

}

@-webkit-keyframes shake {
    from, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translate3d(0px, 0, 0);
        transform: translate3d(0px, 0, 0);
    }

    71%, 75%, 79%, 83%, 87% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    73%, 77%, 81%, 85%, 89%{
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }
}

#shapes .blinking_shape {
    -webkit-animation: blinking_shape 6s linear infinite;
    -moz-animation: blinking_shape 6s linear infinite;
    animation: blinking_shape 6s linear infinite;
}
@keyframes blinking_shape {
    0% { background-color: #50beb0;}
    50% { background-color: #9d58ab}
    100% { background-color: #50beb0;}
}
@-webkit-keyframes blinking_shape {
    0% { background-color: #50beb0;}
    50% { background-color: #9d58ab}
    100% { background-color: #50beb0;}

}

#yearly-sub.blinking_background {
    /*    -webkit-animation: blink_bg 6s linear infinite;
        -moz-animation: blink_bg 6s linear infinite;*/
    animation: shake 6s infinite forwards;
    /*    animation: blink_bg 6s linear infinite;*/
}
@keyframes blink_bg {
    0% { background-color: #50beb0;}
    50% { background-color: #9d58ab}
    100% { background-color: #50beb0;}
}
@-webkit-keyframes blink_bg {
    0% { background-color: #50beb0;}
    50% { background-color: #9d58ab}
    100% { background-color: #50beb0;}

}

#oval.blinking_background {
    -webkit-animation: blink_bg_other 8s linear infinite;
    -moz-animation: blink_bg_other 8s linear infinite;
    animation: blink_bg_other 8s linear infinite;
}
@keyframes blink_bg_other {
    0% { background-color: #9d58ab;}
    50% { background-color: #50beb0}
    100% { background-color: #9d58ab;}
}
@-webkit-keyframes blink_bg_other {
    0% { background-color: #9d58ab;}
    50% { background-color: #50beb0}
    100% { background-color: #9d58ab;}

}

#button-icon{
    display: inline-block;
    vertical-align: middle;
    padding-bottom:2px;
    margin-left: 12px;
}


/* Portrait */
@media screen and (max-width : 1460px) and (orientation: portrait) {


    img#logo-img{
        max-height: 120px;
        max-height:20vh;
        height: 80px;
        height: 12vh;
        margin: 30px 0 0 20px;
        margin: 4vw 0 0 1.5vw;
        width: 200px;
        width: 24vw;
        position: absolute;
    }



    a#restore-subscription{
        text-decoration: none;
        color: #ffffff;
        letter-spacing: 0.01em;
        padding: 11px 20px 10px;
        display: inline-block;
        position: absolute;
        right: 3vw;
        bottom: 4vw;
        font-family: 'BreeRegular', Arial, sans-serif;
        font-size: 24px;
        font-size: 2vh;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 0.875;
        color: #50beb0;
    }

}