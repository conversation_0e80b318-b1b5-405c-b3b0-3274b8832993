html, body {width: 100%;height: 100%;margin: 0;padding: 0;font-family: 'Bree<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;}


#webview{ overflow: hidden; width: 100%; height: 100%; display: block;position: fixed; z-index: 2}

#webview-bg{z-index: 1; overflow: hidden; width: 100%; height: 100%; display: block;position: fixed;}
#webview-bg{-webkit-animation-name: zoomIn;animation-name: zoomIn;animation-delay: 0.5s; -webkit-animation-delay: 0.5s; -webkit-animation-duration: 3.5s;animation-duration: 3.5s;-webkit-animation-fill-mode: both;animation-fill-mode: both;  }
@-webkit-keyframes zoomIn {
    0% { transform: scale(1);}
    100% { transform: scale(1.18); }
}

@keyframes zoomIn {
    0% { transform: scale(1);}
    100% { transform: scale(1.18); }
}


@-webkit-keyframes shake {
    from, to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    10%, 30%, 50%, 70%, 90% {
        -webkit-transform: translate3d(0px, 0, 0);
        transform: translate3d(0px, 0, 0);
    }

    71%, 75%, 79%, 83%, 87% {
        -webkit-transform: translate3d(-10px, 0, 0);
        transform: translate3d(-10px, 0, 0);
    }

    73%, 77%, 81%, 85%, 89%{
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }
}


#logo-img{
    background-size:     contain;
    background-repeat:   no-repeat;
    background-position: left center;
    height: 80px;
    height: 16vh;
    margin: 40px 0 0 20px;
    margin: 5vh 0 0vw 2vw;
}

a#subscription-link{
    max-height: 80px;
    max-height:12vh;
    height: 60px;
    height: 8vh;
    width: 100%;
    width: 100vw;
    text-decoration: none;
    font-size: 14px;
    font-size: 4vh;
    color: #6b4d86;
    display: block;
    position: fixed;
    bottom: 0;
    left: 3vw;
}

#middle-content{
    max-height: calc(100% - 200px);
    max-height:67vh;
    height: calc(100% - 200px);
    height: 67vh;
    margin: 0;
    margin: 0;
    width: 100%;
    width: 100vw;
    text-decoration: none;
    font-size: 16px;
    font-size: 5vh;
    color: #515151;
    display: table-cell;
    vertical-align: middle;
    padding-left: constant(safe-area-inset-left);
}

h1#page-title{
    font-family: 'BreeBold',Arial,sans-serif;
    text-decoration: none;
    font-size: 24px;
    font-size: 9vh;
    color: #515151;
    margin: 90px 0 30px 50px;
    margin: 10vh 0 2.1vh 3vw;
    letter-spacing: 0.01em;
    display: block;
}
h2#page-description{
    font-family: 'BreeRegular',Arial,sans-serif;
    text-decoration: none;
    font-size: 18px;
    font-size: 5.8vh;
    color: #515151;
    margin: 40px 0 30px 40px;
    margin: 0vh 0 5vh 3vw;
    letter-spacing: -0.02em;
    display: block;
}

a#button-subscription{
    text-decoration: none;
    font-size: 16px;
    font-size: 5vh;
    color: #ffffff;
    letter-spacing: 0.01em;
    margin: 0px 0 0px 40px;
    margin: 0vh 0 0vh 3vw;
    border-radius: 5px;
    padding: 10px 15px 10px;
    background-color: #f2575a;
    display: inline-block;
    animation: shake 6s infinite forwards;
}

div#page-price{
    font-family: 'BreeRegular',Arial,sans-serif;
    text-decoration: none;
    font-size: 12px;
    font-size: 3.5vh;
    color: #515151;
    margin: 50px 0 0 50px;
    margin: 3vh 0 0vh 4vw;
    letter-spacing: -0.02em;
    display: block;
}

.strike{text-decoration: line-through;}
.red{color:red}
.red-text{color: #f2575a;}

#button-icon{
    display: inline-block;
    vertical-align: middle;
    padding-bottom:2px;
}

/* Portrait */
@media screen and (max-width : 1460px) and (orientation: portrait) {
    img#logo-img{
        max-height: 120px;
        max-height:20vh;
        height: 80px;
        height: 12vh;
        margin: 40px 0 0 20px;
        margin: 2vh 0 0 1.5vh;
        max-width: 45vw;
        width: auto;
    }

    a#subscription-link{
        max-height: 80px;
        max-height:12vh;
        height: 80px;
        height: 12vh;
        margin: 80px 0 0 40px;
        margin: 8vh 0 0 3vh;
        width: 100%;
        width: 100vw;
        text-decoration: none;
        font-size: 14px;
        font-size: 5vw;
        color: #6b4d86;
        display: block;

    }

    #middle-content{
        max-height: calc(100% - 200px);
        max-height:72vh;
        height: calc(100% - 200px);
        height: 72vh;
        margin: 0;
        margin: 0;
        width: 100%;
        width: 100vw;
        text-decoration: none;
        font-size: 16px;
        font-size: 5vw;
        color: #515151;
        display: table-cell;
        vertical-align: middle;
        padding-left: constant(safe-area-inset-left);
    }

    h1#page-title{
        font-family: 'BreeBold',Arial,sans-serif;
        text-decoration: none;
        font-size: 24px;
        font-size: 9vw;
        color: #515151;
        margin: 100px 0 30px 40px;
        margin: 15vh 0 3.1vh 3vh;
        letter-spacing: 0.01em;
        display: block;
    }
    h2#page-description{
        font-family: 'BreeRegular',Arial,sans-serif;
        text-decoration: none;
        font-size: 18px;
        font-size: 6vw;
        color: #515151;
        margin: 40px 0 30px 40px;
        margin: 0vh 0 7vh 3vh;
        letter-spacing: -0.02em;
        display: block;
    }

    div#page-price{
        font-family: 'BreeRegular',Arial,sans-serif;
        text-decoration: none;
        font-size: 12px;
        font-size: 3.5vh;
        color: #515151;
        margin: 50px 0 30px 50px;
        margin: 3vh 0 9.5vh 4vh;
        letter-spacing: -0.02em;
        display: block;
    }

    a#button-subscription{
        text-decoration: none;
        font-size: 16px;
        font-size: 6vw;
        color: #ffffff;
        letter-spacing: 0.01em;
        margin: 0px 0 0px 40px;
        margin: 0vh 0 0vw 3vh;
        border-radius: 5px;
        padding: 11px 20px 10px;
        background-color: #6b4d86;
        display: inline-block;
    }

}
