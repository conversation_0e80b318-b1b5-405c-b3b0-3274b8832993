var clientSideHashInfo = { 'v1Salt': 'n2iciz509zQV2MRlZDSiI3DFrol' };
var URL_ROOT = "/app/api/3/";
var hasLoaded_google = false;
var hasLoaded_fb = false;
var hasLoaded_brainTree = false;
var requested_loginWithFB = false;
var requested_loginWithGoogle = false;
var logoutIsLoading = false;
var googleAuth2 = null;
if (isLogoutPage === undefined) var isLogoutPage = false;
if (isCouponPage === undefined) var isCouponPage = false;

function SHA256(r){function n(r,n){var t=(65535&r)+(65535&n),o=(r>>16)+(n>>16)+(t>>16)
    return o<<16|65535&t}function t(r,n){return r>>>n|r<<32-n}function o(r,n){return r>>>n}function e(r,n,t){return r&n^~r&t}function u(r,n,t){return r&n^r&t^n&t}function f(r){return t(r,2)^t(r,13)^t(r,22)}function a(r){return t(r,6)^t(r,11)^t(r,25)}function c(r){return t(r,7)^t(r,18)^o(r,3)}function i(r){return t(r,17)^t(r,19)^o(r,10)}function h(r,t){var o,h,C,g,d,v,A,S,l,m,b,p,y=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],B=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],D=Array(64)
    r[t>>5]|=128<<24-t%32,r[(t+64>>9<<4)+15]=t
    for(var l=0;l<r.length;l+=16){o=B[0],h=B[1],C=B[2],g=B[3],d=B[4],v=B[5],A=B[6],S=B[7]
        for(var m=0;64>m;m++)16>m?D[m]=r[m+l]:D[m]=n(n(n(i(D[m-2]),D[m-7]),c(D[m-15])),D[m-16]),b=n(n(n(n(S,a(d)),e(d,v,A)),y[m]),D[m]),p=n(f(o),u(o,h,C)),S=A,A=v,v=d,d=n(g,b),g=C,C=h,h=o,o=n(b,p)
        B[0]=n(o,B[0]),B[1]=n(h,B[1]),B[2]=n(C,B[2]),B[3]=n(g,B[3]),B[4]=n(d,B[4]),B[5]=n(v,B[5]),B[6]=n(A,B[6]),B[7]=n(S,B[7])}return B}function C(r){for(var n=[],t=(1<<v)-1,o=0;o<r.length*v;o+=v)n[o>>5]|=(r.charCodeAt(o/v)&t)<<24-o%32
    return n}function g(r){r=r.replace(/\r\n/g,"\n")
    for(var n="",t=0;t<r.length;t++){var o=r.charCodeAt(t)
        128>o?n+=String.fromCharCode(o):o>127&&2048>o?(n+=String.fromCharCode(o>>6|192),n+=String.fromCharCode(63&o|128)):(n+=String.fromCharCode(o>>12|224),n+=String.fromCharCode(o>>6&63|128),n+=String.fromCharCode(63&o|128))}return n}function d(r){for(var n=A?"0123456789ABCDEF":"0123456789abcdef",t="",o=0;o<4*r.length;o++)t+=n.charAt(r[o>>2]>>8*(3-o%4)+4&15)+n.charAt(r[o>>2]>>8*(3-o%4)&15)
    return t}var v=8,A=0
    return r=g(r),d(h(C(r),r.length*v))};
class ServerApi {
    registerEmailAccount (email, clientHashedPassword, callback) {
        this._sendRequest(URL_ROOT + "user/register", true, {type: "email", email: email, password: clientHashedPassword}, callback);
    }
    oauthLogin (type, token, callback) {
        this._sendRequest(URL_ROOT + "user/login", true, {type: type, token: token}, callback);
    }
    loginEmail (email, clientHashedPassword, callback) {
        this._sendRequest(URL_ROOT + "user/login", true, {type: "email", email: email, password: clientHashedPassword}, callback);
    }
    logOut (callback) {
        this._sendRequest(URL_ROOT + "user/logout", true, null, callback);
    }
    setCoupon (couponId, callback) {
        var json = {couponId: couponId};
        this._sendRequest(URL_ROOT + "accountCoupon/set", true, json, callback);
    }
    removeCoupon (couponId, callback) {
        this._sendRequest(URL_ROOT + "accountCoupon/remove", true, null, callback);
    }
    cancelSubscription (callback) {
        this._sendRequest(URL_ROOT + "subscription/cancel", true, null, callback);
    }
    purchase (nonce, name, deviceData, recaptcha, callback) {
        var json = {nonce: nonce};
        if (recaptcha != null || recaptcha != "") json["recaptcha"] = recaptcha;
        if (name != null || name != "") json["name"] = name;
        if (deviceData != null || deviceData != "") json["deviceData"] = deviceData;

        this._sendRequest(URL_ROOT + "subscription/buy", true, json, callback);
    }
    _sendRequest (url, isPost, data, callback) {
        var request = new XMLHttpRequest();
        if (isPost) {
            request.open("POST", url);
            if (data != null) request.setRequestHeader("Content-type", "application/json")
        } else {
            request.open("GET", url);
        }
        request.onreadystatechange = function () {
            if (request.readyState === XMLHttpRequest.DONE) {
                var responseJSON = null;
                if (request.responseText != null && request.responseText != "") {
                    responseJSON = JSON.parse(request.responseText);
                }
                callback(request.status, responseJSON);
            }
        };
        if (data != null) request.send(JSON.stringify(data));
        else request.send();
    };
}

var api = new ServerApi();
var loadingView = document.querySelector("#loading-view");

function changeLoading(show) {
    if (show) {
        loadingView.classList.add("show")
    } else {
        loadingView.classList.remove("show")
    }
}
function createAccount(type, token, errorMessage = "") {
    changeLoading(true);
    if (type === "email") {
        var inputError = document.getElementById("input-error");
        var emailField = document.querySelector(".email");
        var passwordField = document.querySelector(".password");
        var email = emailField.value;
        var password = passwordField.value;
        var clientSideHashedPassword = SHA256(password + clientSideHashInfo.v1Salt);
        api.registerEmailAccount(email,clientSideHashedPassword, function(code, response) {
            if (code === 200) {
                openLoginSuccess();
                inputError.innerText = "";
            } else {
                changeLoading(false);
                inputError.innerText = errorMessage;
            }
        });
    } else {
        oauthLogin(type, token)
    }
}
function oauthLogin(type, token) {
    loginAccount(type, token);
}
function loginAccount(type, token, errorMessage = "", wrongCredentialsErrorMessage = "") {
    changeLoading(true);
    if (type === "email") {
        var inputError = document.getElementById("input-error");
        var emailField = document.querySelector(".email");
        var passwordField = document.querySelector(".password");
        var email = emailField.value;
        var password = passwordField.value;
        var clientSideHashedPassword = email.includes("kidjo.net") ? password : SHA256(password + clientSideHashInfo.v1Salt);
        api.loginEmail(email,clientSideHashedPassword, function(code, response) {
            if (code === 200) {
                openLoginSuccess();
                inputError.innerText = "";
            } else {
                changeLoading(false);
                inputError.innerText = wrongCredentialsErrorMessage;
            }
        });
    } else {
        api.oauthLogin(type,token, function(code, response) {
            if (code === 200) {
                openLoginSuccess();
                inputError.innerText = "";
            } else {
                changeLoading(false);
                inputError.innerText = errorMessage;
            }
        });
    }
}
function openAccountInfo() {
    location.pathname = "/account/info";
}
function openAccountCancel() {
    location.pathname = "/account/cancel";
}
function openAccountCancelSuccess() {
    location.pathname = "/account/cancel/success";
}
function openAccount() {
    location.pathname = "/account";
}
function openAccountSuccess() {
    location.pathname = "/account/success";
}
function openCoupon() {
    location.pathname = "/account/coupon";
}
function openLogin() {
    location.pathname = "/login";
}
function openLoginSuccess() {
    location.reload();
}
function openLogout() {
    location.pathname = "/account/logout";
}
function openPayments() {
    if (!isCouponPage)
        location.pathname = "/account/payment";
    else
        location.pathname = "/account/coupon/valid";
}
function openCreditCard() {
    if (!isCouponPage)
        location.pathname = "/account/payment/cc";
    else
        location.pathname = "/account/coupon/valid/cc";
}
function openAccountLink() {
    location.pathname = "/account/pair/native";
}

function openWebAppLink() {
    location.pathname = "/account/pair/webapp";
}

function openPinLink() {
    location.pathname = "/link/pin";
}

function openWebApp() {
    location.pathname = "https://app.kidjo.tv";
}
function useCoupon() {
    var couponId = document.querySelector(".coupon-input").value;
    if (couponId === undefined || couponId === null) couponId = "";
    changeLoading(true);
    api.setCoupon(couponId, function(code, json) {
        if (code === 200) {
            if (user.id < 1) openLogin();
            else openPayments();
        } else {
            changeLoading(false);
            var message = json["localErrorMessage"];
            var messageBox = document.querySelector(".coupon-message");
            messageBox.innerHTML = message;
            messageBox.classList.remove("hidden");
        }
    });
}
function purchase(nonce, name, deviceData, recaptcha = '') {
    changeLoading(true);
    api.purchase(nonce, name, deviceData, recaptcha,function(status, json){
        changeLoading(false);
        if (status === 200) openAccountSuccess();
        else if (json != null) {
            var errorBox = document.querySelector("#error-box");
            if (errorBox != null) {
                var localizedMessage = json["localErrorMessage"] || json["error"] || "An error happened";
                errorBox.innerHTML = localizedMessage;
                errorBox.classList.remove("hidden");
            }
        }
    });
}
function cancelSubscription() {
    changeLoading(true);
    api.cancelSubscription(function (code,json) {
        if (code === 200) openAccountCancelSuccess();
        else {
            changeLoading(false);
            alert("Uh oh something went wrong. Please contact <NAME_EMAIL>")
        }
    })
}
function fbLogin() {
    changeLoading(true);
    if (hasLoaded_fb) {
        FB.login(function(response) {
            if (response.authResponse) {
                oauthLogin("facebook",response.authResponse.accessToken);
            } else {
                changeLoading(false);
            }
        });
    } else {
        requested_loginWithFB = true;
    }
}

function gLoaded() {
    gapi.load('auth2', function(){
        googleAuth2 = gapi.auth2.init({
            client_id: '************-3g649ec8bnsfth7rb21dh0dakukbsi95.apps.googleusercontent.com',
            cookiepolicy: 'single_host_origin'
        });
        googleAuth2.then(function () {
            hasLoaded_google = true;
            if (requested_loginWithGoogle) gLogin();

            if (isLogoutPage) handleLogout();
        });
    }, function(){});
}
function gLogin() {
    changeLoading(true);
    if (hasLoaded_google) {
        googleAuth2.signIn({}).then(function(){
            if (googleAuth2.isSignedIn.get()) {
                changeLoading(true);
                createAccount("google",googleAuth2.currentUser.get().getAuthResponse().id_token);
            }  else {
                changeLoading(false);
            }
        });
    } else {
        requested_loginWithGoogle = true;
    }
}
function handleLogout() {
    if (logoutIsLoading) return;
    if (hasLoaded_google && isLoggedInWithGoogle) {
        logoutIsLoading = true;
        googleAuth2.signOut().then(function(){
            api.logOut(function(code) {
                openLogin();
            });
        });
    }
    if (hasLoaded_fb && isLoggedInWithFB) {
        logoutIsLoading = true;
        FB.logout(function(response) {
            api.logOut(function(code) {
                openLogin();
            });
        });
    }
    if (!isLoggedInWithGoogle && !isLoggedInWithFB) {
        logoutIsLoading = true;
        api.logOut(function(code) {
            openLogin();
        });
    }
}
function setUpOauth() {
    window.fbAsyncInit = function() {
        FB.init({
            appId      : '1037837526263294',
            cookie     : true,
            xfbml      : true,
            status      : true,
            version    : 'v2.12'
        });

        // FB.AppEvents.logPageView();

        if (isLogoutPage) {
            FB.getLoginStatus(function(response) {
                hasLoaded_fb = true;
                if (response && response.status === 'connected') {}
                else {
                    isLoggedInWithFB = false;
                }
                handleLogout();
            });
        } else {
            hasLoaded_fb = true;
            if (requested_loginWithFB) {
                requested_loginWithFB = false;
                fbLogin();
            }
        }
    };

    (function(d, s, id){
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {return;}
        js = d.createElement(s); js.id = id;
        js.src = "https://connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
}
var myDeviceData = "";
function setupPaypal() {
    // Create a client.
    braintree.client.create({
        authorization: clientToken
    }, function (clientErr, clientInstance) {

        // Stop if there was a problem creating the client.
        // This could happen if there is a network error or if the authorization
        // is invalid.
        if (clientErr) {
            console.error('Error creating client:', clientErr);
            return;
        }

        // Create a PayPal Checkout component.
        braintree.paypalCheckout.create({
            client: clientInstance
        }, function (paypalCheckoutErr, paypalCheckoutInstance) {
            braintree.dataCollector.create({
                client: clientInstance,
                paypal: true
            }, function (err, dataCollectorInstance) {
                if (err) {
                    // Handle error
                    return;
                }
                // At this point, you should access the dataCollectorInstance.deviceData value and provide it
                // to your server, e.g. by injecting it into your form as a hidden input.
                myDeviceData = dataCollectorInstance.deviceData;
            });
            // Stop if there was a problem creating PayPal Checkout.
            // This could happen if there was a network error or if it's incorrectly
            // configured.
            if (paypalCheckoutErr) {
                console.error('Error creating PayPal Checkout:', paypalCheckoutErr);
                return;
            }

            // Set up PayPal with the checkout.js library
            paypal.Button.render({
                env: 'production', // 'production' or 'sandbox'
                style: {
                    size: 'responsive',
                    color: 'blue',
                    shape: 'rect',
                },
                payment: function () {
                    return paypalCheckoutInstance.createPayment({
                        flow: 'vault',
                        // Your PayPal options here. For available options, see
                        // http://braintree.github.io/braintree-web/current/PayPalCheckout.html#createPayment
                    });
                },

                onAuthorize: function (data, actions) {
                    return paypalCheckoutInstance.tokenizePayment(data)
                        .then(function (payload) {
                            purchase(payload.nonce,"",myDeviceData)
                            // Submit `payload.nonce` to your server.
                        });
                },

                onCancel: function (data) {
                    console.log('checkout.js payment cancelled', JSON.stringify(data, 0, 2));
                },

                onError: function (err) {
                    console.error('checkout.js error', err);
                }
            }, '#paypal-button').then(function () {
                // The PayPal button will be rendered in an html element with the id
                // `paypal-button`. This function will be called when the PayPal button
                // is set up and ready to be used.
            });

        });

    });
}
function setupCC() {
    braintree.client.create({
        authorization: clientToken
    }, function(err, clientInstance) {
        if (err) {
            console.error(err);
            return;
        }
        braintree.dataCollector.create({
            client: clientInstance,
            kount: true
        }, function (err, dataCollectorInstance) {
            if (err) {
                // Handle error
                return;
            }
            // At this point, you should access the dataCollectorInstance.deviceData value and provide it
            // to your server, e.g. by injecting it into your form as a hidden input.
            myDeviceData = dataCollectorInstance.deviceData;
        });
        braintree.hostedFields.create({
            client: clientInstance,
            styles: {
                'input': {
                    'font-size': '18px',
                    'font-family': 'sans-serif',
                    'color': '#484848'
                },
                ':focus': {
                    'color': '#383838'
                },
                '.valid': {
                    'color': 'black'
                },
                '.invalid': {
                    'color': 'red'
                }
            },
            fields: {
                number: {
                    selector: '.cc-num-input',
                    placeholder: 'Card number'
                },
                cvv: {
                    selector: '.cc-ccv-input',
                    placeholder: 'CCV'
                },
                expirationDate: {
                    selector: '.cc-expire',
                    placeholder: "MM/YYYY",
                }
            }
        }, function (err, hostedFieldsInstance) {
            var submit = function (event) {
                event.preventDefault();
                changeLoading(true);

                button.removeEventListener('click', submit, false);
                // hostedFieldsInstance.teardown(function () {
                // });
                hostedFieldsInstance.tokenize(function (tokenizeErr, payload) {
                    if (tokenizeErr) {
                        console.error(tokenizeErr);
                        return;
                    }

                    const recaptcha = grecaptcha.getResponse();

                    // If this was a real integration, this is where you would
                    // send the nonce to your server.
                    var name = document.querySelector(".cc-name-input").value;
                    purchase(payload.nonce,name,myDeviceData,recaptcha);
                });
            };

            var button = document.querySelector("#cc-button");
            button.addEventListener('click', submit, false);
        });

    });
}
