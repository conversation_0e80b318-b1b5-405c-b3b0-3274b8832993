if (typeof kotlin === 'undefined') {
  throw new Error("Error loading module 'app'. Its dependency 'kotlin' was not found. Please, check whether 'kotlin' is loaded prior to 'app'.");
}
var app = function (_, Kotlin) {
  'use strict';
  var $$importsForInline$$ = _.$$importsForInline$$ || (_.$$importsForInline$$ = {});
  var ArrayList_init = Kotlin.kotlin.collections.ArrayList_init_ww73n8$;
  var Unit = Kotlin.kotlin.Unit;
  var toString = Kotlin.toString;
  var println = Kotlin.kotlin.io.println_s8jyv4$;
  var ensureNotNull = Kotlin.ensureNotNull;
  var equals = Kotlin.equals;
  var Kind_CLASS = Kotlin.Kind.CLASS;
  var Enum = Kotlin.kotlin.Enum;
  var throwISE = Kotlin.throwISE;
  var ******** = Kotlin.Long.fromInt(8640000);
  var addAll = Kotlin.kotlin.collections.addAll_ye1y7v$;
  var L300 = Kotlin.Long.fromInt(300);
  var Kind_INTERFACE = Kotlin.Kind.INTERFACE;
  var ArrayList_init_0 = Kotlin.kotlin.collections.ArrayList_init_mqih57$;
  var L60000 = Kotlin.Long.fromInt(60000);
  var round = Kotlin.kotlin.math.round_14dthe$;
  var numberToInt = Kotlin.numberToInt;
  var Kind_OBJECT = Kotlin.Kind.OBJECT;
  var throwCCE = Kotlin.throwCCE;
  var toBoolean = Kotlin.kotlin.text.toBoolean_pdl1vz$;
  var L0 = Kotlin.Long.ZERO;
  var Regex = Kotlin.kotlin.text.Regex;
  var toLong = Kotlin.kotlin.text.toLong_pdl1vz$;
  var Exception = Kotlin.kotlin.Exception;
  var abs = Kotlin.kotlin.math.abs_za3lpa$;
  var copyOf = Kotlin.kotlin.collections.copyOf_se6h4x$;
  var L1000 = Kotlin.Long.fromInt(1000);
  var lastOrNull = Kotlin.kotlin.collections.lastOrNull_2p1efm$;
  var toInt = Kotlin.kotlin.text.toInt_pdl1vz$;
  var getOrNull = Kotlin.kotlin.collections.getOrNull_yzln2o$;
  var split = Kotlin.kotlin.text.split_ip8yn$;
  var last = Kotlin.kotlin.collections.last_2p1efm$;
  var addClass = Kotlin.kotlin.dom.addClass_hhb33f$;
  var toList = Kotlin.kotlin.collections.toList_us0mfu$;
  var List = Kotlin.kotlin.collections.List;
  var wrapFunction = Kotlin.wrapFunction;
  var contains = Kotlin.kotlin.text.contains_li3zpu$;
  var removeClass = Kotlin.kotlin.dom.removeClass_hhb33f$;
  var defineInlineFunction = Kotlin.defineInlineFunction;
  var toDouble = Kotlin.kotlin.text.toDouble_pdl1vz$;
  AudioController$SoundEffects.prototype = Object.create(Enum.prototype);
  AudioController$SoundEffects.prototype.constructor = AudioController$SoundEffects;
  BootController$State.prototype = Object.create(Enum.prototype);
  BootController$State.prototype.constructor = BootController$State;
  SubscriptionEvent$Type.prototype = Object.create(Enum.prototype);
  SubscriptionEvent$Type.prototype.constructor = SubscriptionEvent$Type;
  BackpackItem$Type.prototype = Object.create(Enum.prototype);
  BackpackItem$Type.prototype.constructor = BackpackItem$Type;
  Folder$BackgroundColor.prototype = Object.create(Enum.prototype);
  Folder$BackgroundColor.prototype.constructor = Folder$BackgroundColor;
  Folder$ContentType.prototype = Object.create(Enum.prototype);
  Folder$ContentType.prototype.constructor = Folder$ContentType;
  Language.prototype = Object.create(Enum.prototype);
  Language.prototype.constructor = Language;
  User$AuthType.prototype = Object.create(Enum.prototype);
  User$AuthType.prototype.constructor = User$AuthType;
  User$OAuthType.prototype = Object.create(Enum.prototype);
  User$OAuthType.prototype.constructor = User$OAuthType;
  UserConfig$Platform.prototype = Object.create(Enum.prototype);
  UserConfig$Platform.prototype.constructor = UserConfig$Platform;
  Video$Format.prototype = Object.create(Enum.prototype);
  Video$Format.prototype.constructor = Video$Format;
  HttpRequestType.prototype = Object.create(Enum.prototype);
  HttpRequestType.prototype.constructor = HttpRequestType;
  Route$Path.prototype = Object.create(Enum.prototype);
  Route$Path.prototype.constructor = Route$Path;
  AgeGateViewController.prototype = Object.create(BaseViewController.prototype);
  AgeGateViewController.prototype.constructor = AgeGateViewController;
  ModalResult.prototype = Object.create(Enum.prototype);
  ModalResult.prototype.constructor = ModalResult;
  BaseViewController$ViewState.prototype = Object.create(Enum.prototype);
  BaseViewController$ViewState.prototype.constructor = BaseViewController$ViewState;
  CardSelectionViewController.prototype = Object.create(BaseViewController.prototype);
  CardSelectionViewController.prototype.constructor = CardSelectionViewController;
  FavoritesViewController.prototype = Object.create(BaseViewController.prototype);
  FavoritesViewController.prototype.constructor = FavoritesViewController;
  FolderViewController.prototype = Object.create(BaseViewController.prototype);
  FolderViewController.prototype.constructor = FolderViewController;
  GameViewController.prototype = Object.create(BaseViewController.prototype);
  GameViewController.prototype.constructor = GameViewController;
  LockoutViewController.prototype = Object.create(BaseViewController.prototype);
  LockoutViewController.prototype.constructor = LockoutViewController;
  SettingsViewController.prototype = Object.create(BaseViewController.prototype);
  SettingsViewController.prototype.constructor = SettingsViewController;
  SubscriptionViewController.prototype = Object.create(BaseViewController.prototype);
  SubscriptionViewController.prototype.constructor = SubscriptionViewController;
  VideoPlayerViewController.prototype = Object.create(BaseViewController.prototype);
  VideoPlayerViewController.prototype.constructor = VideoPlayerViewController;
  SettingsPanelViewCoordinator$PanelType.prototype = Object.create(Enum.prototype);
  SettingsPanelViewCoordinator$PanelType.prototype.constructor = SettingsPanelViewCoordinator$PanelType;
  function Api(deviceData, config, hashFunctions, jsonReader, jsonFactoryV3) {
    this.deviceData_0 = deviceData;
    this.config_0 = config;
    this.hashFunctions_0 = hashFunctions;
    this.jsonReader_0 = jsonReader;
    this.jsonFactoryV3_0 = jsonFactoryV3;
    this.requestReader_0 = new HttpRequestReaderJs();
  }
  var JsArray = Array;
  function Api$refreshWeb$lambda(this$Api, closure$user, closure$complete) {
    return function (responseCode, json) {
      var tmp$;
      var kids;
      if (responseCode === 200 && json != null) {
        kids = ArrayList_init();
        deviceData(this$Api.jsonFactoryV3_0, this$Api.deviceData_0, json);
        var tmp$_0;
        var userJson = Kotlin.isType(tmp$_0 = json['user'], Object) ? tmp$_0 : null;
        if (userJson != null)
          user(this$Api.jsonFactoryV3_0, closure$user, json);
        var tmp$_1;
        var kidsJsonArray = Kotlin.isType(tmp$_1 = json['kids'], JsArray) ? tmp$_1 : null;
        if (kidsJsonArray != null) {
          var size = kidsJsonArray.length;
          for (var i = 0; i < size; i++) {
            var tmp$_2;
            tmp$ = Kotlin.isType(tmp$_2 = kidsJsonArray.get(i), Object) ? tmp$_2 : throwCCE();
            if (tmp$ == null) {
              continue;
            }
            var j = tmp$;
            kids.add_11rb$(kid(this$Api.jsonFactoryV3_0, j));
          }
        }
      }
       else {
        kids = null;
      }
      closure$complete(responseCode, kids);
      return Unit;
    };
  }
  Api.prototype.refreshWeb_scx1cm$ = function (timeZoneOffsetInMinutes, user, complete) {
    var requestJson = {};
    requestJson['timeZoneOffsetInMinutes'] = timeZoneOffsetInMinutes;
    var url = (new UrlBuilder(PATH_REFRESH_WEB)).build();
    this.sendRequest_0(HttpRequestType$POST_getInstance(), url, requestJson, Api$refreshWeb$lambda(this, user, complete));
  };
  function Api$unsubscribeUser$lambda(responseCode, json) {
    println('Unsubscribe user:' + toString(responseCode));
    println('Unsubscribe user:' + toString(json));
    return Unit;
  }
  Api.prototype.unsubscribeUser_bepr4$ = function (user) {
    var url = (new UrlBuilder('https://api.kidjo.tv/v3/subscription/cancel')).build();
    var requestJson = {};
    requestJson['userId'] = user.id;
    this.sendRequest_0(HttpRequestType$POST_getInstance(), url, requestJson, Api$unsubscribeUser$lambda);
  };
  function Api$cardsList$lambda(this$Api, closure$complete) {
    return function (responseCode, json) {
      var folders = null;
      if (responseCode === 200 && json != null) {
        var tmp$;
        var cardsJsonArray = Kotlin.isType(tmp$ = json['cards'], JsArray) ? tmp$ : null;
        if (cardsJsonArray != null) {
          var size = cardsJsonArray.length;
          var array = JsArray(size);
          var tmp$_0;
          tmp$_0 = array.length - 1 | 0;
          for (var i = 0; i <= tmp$_0; i++) {
            var this$Api_0 = this$Api;
            var tmp$_1;
            var json_0 = ensureNotNull(Kotlin.isType(tmp$_1 = cardsJsonArray.get(i), Object) ? tmp$_1 : throwCCE());
            array[i] = folder(this$Api_0.jsonFactoryV3_0, json_0);
          }
          folders = array;
        }
      }
      closure$complete(responseCode, folders);
      return Unit;
    };
  }
  Api.prototype.cardsList_9vj10k$ = function (kidId, contentType, premiumActive, showOnlyFolders, showGames, offset, limit, complete) {
    var urlBuilder = new UrlBuilder(BASE_API_URL + PATH_CARDS_LIST);
    urlBuilder.addQuery_puj7f4$('kidId', kidId);
    urlBuilder.addQuery_puj7f4$('contentType', contentType.raw);
    urlBuilder.addQuery_puj7f4$('cardOffset', offset.toString());
    urlBuilder.addQuery_puj7f4$('cardLimit', limit.toString());
    var filterMask = 0;
    filterMask = setBit(filterMask, 0, showOnlyFolders);
    filterMask = setBit(filterMask, 1, showGames);
    filterMask = setBit(filterMask, 2, premiumActive);
    urlBuilder.addQuery_puj7f4$('filterMask', filterMask.toString());
    this.sendRequest_0(HttpRequestType$GET_getInstance(), urlBuilder.build(), null, Api$cardsList$lambda(this, complete));
  };
  function Api$folderGet$lambda(this$Api, closure$complete) {
    return function (responseCode, json) {
      var folder_0;
      if (responseCode === 200 && json != null) {
        folder_0 = folder(this$Api.jsonFactoryV3_0, json);
      }
       else {
        folder_0 = null;
      }
      closure$complete(responseCode, folder_0);
      return Unit;
    };
  }
  Api.prototype.folderGet_p7zhzt$ = function (folderId, complete) {
    var url = (new UrlBuilder(BASE_API_URL + PATH_CARD_FOLDER_GET + folderId)).build();
    this.sendRequest_0(HttpRequestType$GET_getInstance(), url, null, Api$folderGet$lambda(this, complete));
  };
  function Api$videoGet$lambda(this$Api, closure$complete) {
    return function (responseCode, json) {
      var video_0;
      if (responseCode === 200 && json != null) {
        video_0 = video(this$Api.jsonFactoryV3_0, json);
      }
       else {
        video_0 = null;
      }
      closure$complete(responseCode, video_0);
      return Unit;
    };
  }
  Api.prototype.videoGet_bm8j90$ = function (videoId, complete) {
    var url = (new UrlBuilder(BASE_API_URL + PATH_CARD_VIDEO_GET + videoId)).build();
    this.sendRequest_0(HttpRequestType$GET_getInstance(), url, null, Api$videoGet$lambda(this, complete));
  };
  function Api$setLanguage$lambda(closure$complete) {
    return function (responseCode, f) {
      closure$complete != null ? closure$complete(responseCode) : null;
      return Unit;
    };
  }
  Api.prototype.setLanguage_pljp91$ = function (language, complete) {
    if (complete === void 0)
      complete = null;
    var url = (new UrlBuilder(BASE_API_URL + ('/devices/' + this.deviceData_0.deviceId + '/setLanguage'))).build();
    var json = {};
    json['languageId'] = language.id;
    this.sendRequest_0(HttpRequestType$POST_getInstance(), url, json, Api$setLanguage$lambda(complete));
  };
  function Api$updateKid$lambda(closure$complete) {
    return function (responseCode, f) {
      closure$complete != null ? closure$complete(responseCode) : null;
      return Unit;
    };
  }
  Api.prototype.updateKid_zblc1k$ = function (kidId, newAge, complete) {
    if (complete === void 0)
      complete = null;
    var url = (new UrlBuilder(BASE_API_URL + PATH_KID_UPDATE_1 + kidId + PATH_KID_UPDATE_2)).build();
    var json = {};
    json['age'] = newAge;
    this.sendRequest_0(HttpRequestType$POST_getInstance(), url, json, Api$updateKid$lambda(complete));
  };
  var JSON_0 = JSON;
  function Api$sendRequest$lambda(this$Api, closure$complete) {
    return function (responseCode, responseBody) {
      var tmp$;
      if (responseBody != null && !equals(responseBody, '')) {
        var tmp$_0;
        try {
          tmp$_0 = JSON_0.parse(responseBody);
        }
         catch (e) {
          if (Kotlin.isType(e, Exception)) {
            tmp$_0 = null;
          }
           else
            throw e;
        }
        tmp$ = tmp$_0;
      }
       else
        tmp$ = null;
      var json = tmp$;
      closure$complete(responseCode, json);
      return Unit;
    };
  }
  Api.prototype.sendRequest_0 = function (requestType, url, requestJson, complete) {
    if (requestJson === void 0)
      requestJson = null;
    if (complete === void 0)
      complete = null;
    var onComplete;
    if (complete != null) {
      onComplete = Api$sendRequest$lambda(this, complete);
    }
     else {
      onComplete = null;
    }
    var request = this.requestReader_0.createGet_t3ddc1$(requestType, url, onComplete);
    var dateString = UTCAPITimeStamp();
    this.requestReader_0.setHeader_fn1ioh$(request, 'X-Kidjo-DeviceId', this.deviceData_0.deviceId);
    this.requestReader_0.setHeader_fn1ioh$(request, 'X-Kidjo-Date', dateString);
    this.requestReader_0.setHeader_fn1ioh$(request, 'X-Kidjo-Build', this.deviceData_0.build.toString());
    this.requestReader_0.setHeader_fn1ioh$(request, 'X-Kidjo-Store', this.config_0.platform.raw);
    this.requestReader_0.setHeader_fn1ioh$(request, 'Authorization', this.hashFunctions_0.sha1Hash_61zpoe$(this.deviceData_0.deviceId + dateString + this.config_0.apiSecret));
    if (requestJson != null)
      this.requestReader_0.setHeader_fn1ioh$(request, 'Content-Type', 'application/json; charset=UTF-8');
    var tmp$ = this.requestReader_0;
    var jsonToString_t1yf6p$result;
    jsonToString_t1yf6p$break: do {
      if (requestJson == null) {
        jsonToString_t1yf6p$result = null;
        break jsonToString_t1yf6p$break;
      }
      jsonToString_t1yf6p$result = JSON_0.stringify(requestJson);
    }
     while (false);
    tmp$.send_28on4w$(request, jsonToString_t1yf6p$result);
  };
  Api.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Api',
    interfaces: []
  };
  var BASE_API_URL;
  var PATH_VERSION;
  var PATH_REFRESH_WEB;
  var PATH_REFRESH;
  var PATH_CARDS_LIST;
  var PATH_CARD_FOLDER_GET;
  var PATH_CARD_VIDEO_GET;
  var PATH_LANGUAGE_SET;
  var PATH_KID_UPDATE_1;
  var PATH_KID_UPDATE_2;
  function AudioController(broadcaster, audioLoader, settingsController) {
    this.audioLoader_0 = audioLoader;
    this.settingsController_0 = settingsController;
    this.broadcastReceiver_0 = new BroadcastReceiverJs(BroadcastReceiverListener$Companion_getInstance().EVENT_ID_AUDIO_MUTE_SETTING_CHANGED, this);
    this.musicShouldBePlaying_0 = true;
    this.broadcastReceiver_0.register_5oryg$(broadcaster);
  }
  AudioController.prototype.changeMusicState_6taknv$ = function (shouldPlay) {
    this.musicShouldBePlaying_0 = shouldPlay;
    if (!this.audioLoader_0.musicIsLoaded)
      return;
    if (!this.settingsController_0.allowSound) {
      this.audioLoader_0.setMusicPlayState_6taknv$(false);
      return;
    }
    this.audioLoader_0.setMusicPlayState_6taknv$(shouldPlay);
  };
  AudioController.prototype.playSoundEffect_8htko7$ = function (effects, force) {
    if (force === void 0)
      force = false;
    if (!this.settingsController_0.allowSound && !force)
      return;
    this.audioLoader_0.playSound_jn3wr6$(effects);
  };
  AudioController.prototype.musicLoadComplete_6taknv$ = function (success) {
    if (this.musicShouldBePlaying_0)
      this.changeMusicState_6taknv$(this.musicShouldBePlaying_0);
  };
  AudioController.prototype.broadcastReceiverGotBroadcast_61zpoe$ = function (id) {
    if (this.musicShouldBePlaying_0 && this.settingsController_0.allowSound)
      this.changeMusicState_6taknv$(this.musicShouldBePlaying_0);
    else
      this.changeMusicState_6taknv$(false);
  };
  function AudioController$SoundEffects(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function AudioController$SoundEffects_initFields() {
    AudioController$SoundEffects_initFields = function () {
    };
    AudioController$SoundEffects$GENERAL_BUTTON_instance = new AudioController$SoundEffects('GENERAL_BUTTON', 0);
    AudioController$SoundEffects$LOCK_instance = new AudioController$SoundEffects('LOCK', 1);
    AudioController$SoundEffects$SWIPE_LEFT_instance = new AudioController$SoundEffects('SWIPE_LEFT', 2);
    AudioController$SoundEffects$SWIPE_RIGHT_instance = new AudioController$SoundEffects('SWIPE_RIGHT', 3);
    AudioController$SoundEffects$VALIDATION_instance = new AudioController$SoundEffects('VALIDATION', 4);
    AudioController$SoundEffects$FOLDER_OPEN_instance = new AudioController$SoundEffects('FOLDER_OPEN', 5);
    AudioController$SoundEffects$FOLDER_CLOSE_instance = new AudioController$SoundEffects('FOLDER_CLOSE', 6);
  }
  var AudioController$SoundEffects$GENERAL_BUTTON_instance;
  function AudioController$SoundEffects$GENERAL_BUTTON_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$GENERAL_BUTTON_instance;
  }
  var AudioController$SoundEffects$LOCK_instance;
  function AudioController$SoundEffects$LOCK_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$LOCK_instance;
  }
  var AudioController$SoundEffects$SWIPE_LEFT_instance;
  function AudioController$SoundEffects$SWIPE_LEFT_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$SWIPE_LEFT_instance;
  }
  var AudioController$SoundEffects$SWIPE_RIGHT_instance;
  function AudioController$SoundEffects$SWIPE_RIGHT_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$SWIPE_RIGHT_instance;
  }
  var AudioController$SoundEffects$VALIDATION_instance;
  function AudioController$SoundEffects$VALIDATION_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$VALIDATION_instance;
  }
  var AudioController$SoundEffects$FOLDER_OPEN_instance;
  function AudioController$SoundEffects$FOLDER_OPEN_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$FOLDER_OPEN_instance;
  }
  var AudioController$SoundEffects$FOLDER_CLOSE_instance;
  function AudioController$SoundEffects$FOLDER_CLOSE_getInstance() {
    AudioController$SoundEffects_initFields();
    return AudioController$SoundEffects$FOLDER_CLOSE_instance;
  }
  AudioController$SoundEffects.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SoundEffects',
    interfaces: [Enum]
  };
  function AudioController$SoundEffects$values() {
    return [AudioController$SoundEffects$GENERAL_BUTTON_getInstance(), AudioController$SoundEffects$LOCK_getInstance(), AudioController$SoundEffects$SWIPE_LEFT_getInstance(), AudioController$SoundEffects$SWIPE_RIGHT_getInstance(), AudioController$SoundEffects$VALIDATION_getInstance(), AudioController$SoundEffects$FOLDER_OPEN_getInstance(), AudioController$SoundEffects$FOLDER_CLOSE_getInstance()];
  }
  AudioController$SoundEffects.values = AudioController$SoundEffects$values;
  function AudioController$SoundEffects$valueOf(name) {
    switch (name) {
      case 'GENERAL_BUTTON':
        return AudioController$SoundEffects$GENERAL_BUTTON_getInstance();
      case 'LOCK':
        return AudioController$SoundEffects$LOCK_getInstance();
      case 'SWIPE_LEFT':
        return AudioController$SoundEffects$SWIPE_LEFT_getInstance();
      case 'SWIPE_RIGHT':
        return AudioController$SoundEffects$SWIPE_RIGHT_getInstance();
      case 'VALIDATION':
        return AudioController$SoundEffects$VALIDATION_getInstance();
      case 'FOLDER_OPEN':
        return AudioController$SoundEffects$FOLDER_OPEN_getInstance();
      case 'FOLDER_CLOSE':
        return AudioController$SoundEffects$FOLDER_CLOSE_getInstance();
      default:throwISE('No enum constant net.kidjo.app.shared.controllers.AudioController.SoundEffects.' + name);
    }
  }
  AudioController$SoundEffects.valueOf_61zpoe$ = AudioController$SoundEffects$valueOf;
  AudioController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'AudioController',
    interfaces: [AudioLoaderListener, BroadcastReceiverListener]
  };
  function BootController(platformDependencyProvider, api, device, smallStorageController, userController, kidsController, settingsController, favoritesController) {
    this.api_0 = api;
    this.device_0 = device;
    this.smallStorageController_0 = smallStorageController;
    this.userController_0 = userController;
    this.kidsController_0 = kidsController;
    this.settingsController_0 = settingsController;
    this.favoritesController_0 = favoritesController;
    this.broadcaster_0 = platformDependencyProvider.broadcaster;
    this.state_8gn88a$_0 = BootController$State$JUST_BOOTED_getInstance();
    this.timeZoneOffsetInMinutes_0 = 0;
  }
  Object.defineProperty(BootController.prototype, 'state', {
    get: function () {
      return this.state_8gn88a$_0;
    },
    set: function (state) {
      this.state_8gn88a$_0 = state;
    }
  });
  BootController.prototype.retry = function () {
    if (this.state !== BootController$State$ERROR_getInstance())
      return;
    this.state = BootController$State$LOCAL_LOAD_COMPLETE_getInstance();
    this.networkBoot_za3lpa$(this.timeZoneOffsetInMinutes_0);
  };
  BootController.prototype.localBoot_t1yf6p$ = function (localJson) {
    if (this.state !== BootController$State$JUST_BOOTED_getInstance())
      return;
    this.state = BootController$State$LOCAL_LOAD_getInstance();
    getDeviceData(this.smallStorageController_0, this.device_0);
    if (localJson != null)
      this.userController_0.loadInJson_qk3xy8$(localJson);
    else
      getUser(this.smallStorageController_0, this.userController_0.user);
    this.kidsController_0.load_mbla7x$(getKids(this.smallStorageController_0));
    this.state = BootController$State$LOCAL_LOAD_COMPLETE_getInstance();
  };
  function BootController$networkBoot$lambda(this$BootController) {
    return function (responseCode, kids) {
      if (responseCode === 200) {
        if (kids != null)
          this$BootController.kidsController_0.load_13t91c$(kids);
        this$BootController.device_0.lastBootTime = UTCTimeStamp();
        var editor = this$BootController.smallStorageController_0.getEditor();
        setDeviceData(this$BootController.smallStorageController_0, editor, this$BootController.device_0);
        setUser(this$BootController.smallStorageController_0, editor, this$BootController.userController_0.user);
        setKids(this$BootController.smallStorageController_0, this$BootController.kidsController_0.kids, editor);
        this$BootController.smallStorageController_0.commit_6f7vr6$(editor);
      }
      this$BootController.bootHasCompleted_0(responseCode === 200);
      return Unit;
    };
  }
  BootController.prototype.networkBoot_za3lpa$ = function (timeZoneOffsetInMinutes) {
    if (this.state !== BootController$State$LOCAL_LOAD_COMPLETE_getInstance())
      return;
    this.timeZoneOffsetInMinutes_0 = timeZoneOffsetInMinutes;
    this.state = BootController$State$FIRST_NETWORK_getInstance();
    if (this.device_0.isRegistered()) {
      var now = UTCTimeStamp();
      if (now.compareTo_11rb$(this.device_0.lastBootTime.add(********)) < 0) {
        this.bootHasCompleted_0(true);
        return;
      }
    }
    this.api_0.refreshWeb_scx1cm$(timeZoneOffsetInMinutes, this.userController_0.user, BootController$networkBoot$lambda(this));
  };
  BootController.prototype.bootHasCompleted_0 = function (isSuccessful) {
    if (isSuccessful) {
      this.state = BootController$State$COMPLETE_getInstance();
      BroadcastReceiverJs$Companion_getInstance().Broadcast_un8f3q$(this.broadcaster_0, BroadcastReceiverListener$Companion_getInstance().EVENT_ID_BOOT_COMPLETE);
    }
     else {
      this.state = BootController$State$ERROR_getInstance();
      BroadcastReceiverJs$Companion_getInstance().Broadcast_un8f3q$(this.broadcaster_0, BroadcastReceiverListener$Companion_getInstance().EVENT_ID_BOOT_ERROR);
    }
  };
  function BootController$State(name, ordinal, level) {
    Enum.call(this);
    this.level = level;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function BootController$State_initFields() {
    BootController$State_initFields = function () {
    };
    BootController$State$JUST_BOOTED_instance = new BootController$State('JUST_BOOTED', 0, 0);
    BootController$State$LOCAL_LOAD_instance = new BootController$State('LOCAL_LOAD', 1, 1);
    BootController$State$LOCAL_LOAD_COMPLETE_instance = new BootController$State('LOCAL_LOAD_COMPLETE', 2, 1);
    BootController$State$FIRST_NETWORK_instance = new BootController$State('FIRST_NETWORK', 3, 2);
    BootController$State$ERROR_instance = new BootController$State('ERROR', 4, 3);
    BootController$State$COMPLETE_instance = new BootController$State('COMPLETE', 5, 4);
  }
  var BootController$State$JUST_BOOTED_instance;
  function BootController$State$JUST_BOOTED_getInstance() {
    BootController$State_initFields();
    return BootController$State$JUST_BOOTED_instance;
  }
  var BootController$State$LOCAL_LOAD_instance;
  function BootController$State$LOCAL_LOAD_getInstance() {
    BootController$State_initFields();
    return BootController$State$LOCAL_LOAD_instance;
  }
  var BootController$State$LOCAL_LOAD_COMPLETE_instance;
  function BootController$State$LOCAL_LOAD_COMPLETE_getInstance() {
    BootController$State_initFields();
    return BootController$State$LOCAL_LOAD_COMPLETE_instance;
  }
  var BootController$State$FIRST_NETWORK_instance;
  function BootController$State$FIRST_NETWORK_getInstance() {
    BootController$State_initFields();
    return BootController$State$FIRST_NETWORK_instance;
  }
  var BootController$State$ERROR_instance;
  function BootController$State$ERROR_getInstance() {
    BootController$State_initFields();
    return BootController$State$ERROR_instance;
  }
  var BootController$State$COMPLETE_instance;
  function BootController$State$COMPLETE_getInstance() {
    BootController$State_initFields();
    return BootController$State$COMPLETE_instance;
  }
  BootController$State.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'State',
    interfaces: [Enum]
  };
  function BootController$State$values() {
    return [BootController$State$JUST_BOOTED_getInstance(), BootController$State$LOCAL_LOAD_getInstance(), BootController$State$LOCAL_LOAD_COMPLETE_getInstance(), BootController$State$FIRST_NETWORK_getInstance(), BootController$State$ERROR_getInstance(), BootController$State$COMPLETE_getInstance()];
  }
  BootController$State.values = BootController$State$values;
  function BootController$State$valueOf(name) {
    switch (name) {
      case 'JUST_BOOTED':
        return BootController$State$JUST_BOOTED_getInstance();
      case 'LOCAL_LOAD':
        return BootController$State$LOCAL_LOAD_getInstance();
      case 'LOCAL_LOAD_COMPLETE':
        return BootController$State$LOCAL_LOAD_COMPLETE_getInstance();
      case 'FIRST_NETWORK':
        return BootController$State$FIRST_NETWORK_getInstance();
      case 'ERROR':
        return BootController$State$ERROR_getInstance();
      case 'COMPLETE':
        return BootController$State$COMPLETE_getInstance();
      default:throwISE('No enum constant net.kidjo.app.shared.controllers.BootController.State.' + name);
    }
  }
  BootController$State.valueOf_61zpoe$ = BootController$State$valueOf;
  BootController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'BootController',
    interfaces: []
  };
  var OFF;
  function CardPager(numberPerPage, api, listener, limitedPagesForInfiniteScroll) {
    if (limitedPagesForInfiniteScroll === void 0)
      limitedPagesForInfiniteScroll = -1;
    this.numberPerPage_0 = numberPerPage;
    this.api_0 = api;
    this.listener_0 = listener;
    this.limitedPagesForInfiniteScroll_0 = limitedPagesForInfiniteScroll;
    this.isFirstSet_0 = true;
    this.isFinished_g5tslg$_0 = false;
    this.isLoading_xf1f5q$_0 = false;
    this.lastKidId_0 = Kid$Companion_getInstance().NO_ID;
    this.lastKidAge_0 = 4;
    this.lastIsPremium_0 = false;
    this.lastContentType_0 = Folder$ContentType$MIXED_getInstance();
    this.lastFoldersOnly_0 = false;
    this.lastNoGames_0 = false;
    this.lastLanguage_0 = Language$ENGLISH_getInstance();
    this.currentCallbackId_0 = 0;
    this.amountOfCards_0 = 0;
    this.infiniteScrollPage_0 = 0;
    this.pastFolders_0 = ArrayList_init();
  }
  Object.defineProperty(CardPager.prototype, 'isFinished', {
    get: function () {
      return this.isFinished_g5tslg$_0;
    },
    set: function (isFinished) {
      this.isFinished_g5tslg$_0 = isFinished;
    }
  });
  Object.defineProperty(CardPager.prototype, 'isLoading', {
    get: function () {
      return this.isLoading_xf1f5q$_0;
    },
    set: function (isLoading) {
      this.isLoading_xf1f5q$_0 = isLoading;
    }
  });
  CardPager.prototype.set_4lu6kc$ = function (kidId, age, isPremium, contentType, foldersOnly, noGames, language) {
    if (this.isFirstSet_0 || (!equals(this.lastKidId_0, kidId) || this.lastKidAge_0 !== age || this.lastIsPremium_0 !== isPremium || this.lastContentType_0 !== contentType || this.lastFoldersOnly_0 !== foldersOnly || this.lastNoGames_0 !== noGames || this.lastLanguage_0 !== language)) {
      this.forceReset_4lu6kc$(kidId, age, isPremium, contentType, foldersOnly, noGames, language);
      return true;
    }
    return false;
  };
  CardPager.prototype.forceReset_4lu6kc$ = function (kidId, age, isPremium, contentType, foldersOnly, noGames, language) {
    this.currentCallbackId_0 = this.currentCallbackId_0 + 1 | 0;
    this.amountOfCards_0 = 0;
    this.isFirstSet_0 = false;
    this.lastKidId_0 = kidId;
    this.lastKidAge_0 = age;
    this.lastIsPremium_0 = isPremium;
    this.lastContentType_0 = contentType;
    this.lastFoldersOnly_0 = foldersOnly;
    this.lastNoGames_0 = noGames;
    this.lastLanguage_0 = language;
    this.isFinished = false;
    this.isLoading = false;
    this.pastFolders_0.clear();
    this.nextPage();
  };
  function CardPager$nextPage$lambda(closure$theCallbackId, this$CardPager) {
    return function (responseCode, folders) {
      if (closure$theCallbackId === this$CardPager.currentCallbackId_0) {
        this$CardPager.handleResults_0(responseCode, folders);
      }
      return Unit;
    };
  }
  CardPager.prototype.nextPage = function () {
    if (this.isLoading)
      return;
    else if (this.isFinished)
      this.handleInfiniteResults_0();
    this.isLoading = true;
    this.currentCallbackId_0 = this.currentCallbackId_0 + 1 | 0;
    var theCallbackId = this.currentCallbackId_0;
    this.api_0.cardsList_9vj10k$(this.lastKidId_0, this.lastContentType_0, this.lastIsPremium_0, this.lastFoldersOnly_0, this.lastNoGames_0, this.amountOfCards_0, this.numberPerPage_0, CardPager$nextPage$lambda(theCallbackId, this));
  };
  CardPager.prototype.handleResults_0 = function (responseCode, folders) {
    if (responseCode !== 200 || folders == null) {
      this.isLoading = false;
      return;
    }
    println(folders);
    var size = folders.length;
    if (size < this.numberPerPage_0)
      this.isFinished = true;
    this.amountOfCards_0 = this.amountOfCards_0 + size | 0;
    addAll(this.pastFolders_0, folders);
    this.isLoading = false;
    this.listener_0.pagerGotNextSet_3c1pkb$(folders);
  };
  function CardPager$handleInfiniteResults$lambda(this$CardPager) {
    return function () {
      this$CardPager.isLoading = false;
      return Unit;
    };
  }
  var copyToArray = Kotlin.kotlin.collections.copyToArray;
  CardPager.prototype.handleInfiniteResults_0 = function () {
    if (this.limitedPagesForInfiniteScroll_0 === -1)
      return;
    else if (this.infiniteScrollPage_0 >= this.limitedPagesForInfiniteScroll_0 && this.limitedPagesForInfiniteScroll_0 !== -1)
      return;
    this.isLoading = true;
    this.infiniteScrollPage_0 = this.infiniteScrollPage_0 + 1 | 0;
    this.listener_0.pagerGotNextSet_3c1pkb$(copyToArray(this.pastFolders_0));
    SetGenericTimeout(L300, CardPager$handleInfiniteResults$lambda(this));
  };
  function CardPager$Listener() {
  }
  CardPager$Listener.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'Listener',
    interfaces: []
  };
  CardPager.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'CardPager',
    interfaces: []
  };
  function DependencyContainer(jsonReader, jsonFactoryV3, platformDependencyProvider, smallStorageController, api, settingsController, audioController, userController, kidsController, favoritesController, bootController) {
    this.jsonReader = jsonReader;
    this.jsonFactoryV3 = jsonFactoryV3;
    this.platformDependencyProvider = platformDependencyProvider;
    this.smallStorageController = smallStorageController;
    this.api = api;
    this.settingsController = settingsController;
    this.audioController = audioController;
    this.userController = userController;
    this.kidsController = kidsController;
    this.favoritesController = favoritesController;
    this.bootController = bootController;
  }
  DependencyContainer.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'DependencyContainer',
    interfaces: []
  };
  function CreateAppDependencyContainer(language, platformDependencyProvider) {
    var jsonReader = new JsonReaderJs();
    var jsonFactoryV3 = new JsonFactoryV3(jsonReader);
    var hashFunctions = new HashFunctionsJs();
    var api = new Api(platformDependencyProvider.deviceData, platformDependencyProvider.userConfig, hashFunctions, jsonReader, jsonFactoryV3);
    var smallStorageController = new SmallStorageController(platformDependencyProvider.sharedKeyStore, new LocalKeyValueStorageReaderJs(), jsonReader, jsonFactoryV3);
    var userController = new UserController(User$Companion_getInstance().GetEmptyUser(), platformDependencyProvider.deviceData, smallStorageController, api, jsonReader, jsonFactoryV3);
    var kidsController = new KidsController(smallStorageController, api);
    var settingsController = new SettingsController(language, smallStorageController, api);
    var audioController = new AudioController(platformDependencyProvider.broadcaster, platformDependencyProvider.audioLoader, settingsController);
    var favoritesController = new FavoritesController(smallStorageController);
    var bootController = new BootController(platformDependencyProvider, api, platformDependencyProvider.deviceData, smallStorageController, userController, kidsController, settingsController, favoritesController);
    return new DependencyContainer(jsonReader, jsonFactoryV3, platformDependencyProvider, smallStorageController, api, settingsController, audioController, userController, kidsController, favoritesController, bootController);
  }
  function FavoritesController(smallStorageController) {
    this.smallStorageController_0 = smallStorageController;
    this.favorites_0 = ArrayList_init();
    addAll(this.favorites_0, getFavoriteVideos(this.smallStorageController_0));
  }
  FavoritesController.prototype.isFavorited_9u1k3q$ = function (video) {
    return this.isFavorited_61zpoe$(video.videoId);
  };
  FavoritesController.prototype.isFavorited_61zpoe$ = function (videoId) {
    var tmp$;
    tmp$ = this.favorites_0;
    for (var i = 0; i !== tmp$.size; ++i) {
      if (equals(this.favorites_0.get_za3lpa$(i).videoId, videoId))
        return true;
    }
    return false;
  };
  FavoritesController.prototype.addFavorite_9u1k3q$ = function (video) {
    var tmp$;
    var found = false;
    tmp$ = this.favorites_0;
    for (var i = 0; i !== tmp$.size; ++i) {
      if (equals(this.favorites_0.get_za3lpa$(i).videoId, video.videoId)) {
        found = true;
        this.favorites_0.get_za3lpa$(i).storedTime = UTCTimeStamp();
        break;
      }
    }
    if (!found) {
      var newVid = Video_init(video);
      newVid.storedTime = UTCTimeStamp();
      this.favorites_0.add_11rb$(newVid);
    }
    setFavoriteVideos(this.smallStorageController_0, this.favorites_0);
  };
  FavoritesController.prototype.removeFavorite_9u1k3q$ = function (video) {
    this.removeFavorite_61zpoe$(video.videoId);
  };
  FavoritesController.prototype.removeFavorite_61zpoe$ = function (id) {
    var found = false;
    var i = 0;
    while (i < this.favorites_0.size) {
      if (equals(this.favorites_0.get_za3lpa$(i).videoId, id)) {
        this.favorites_0.removeAt_za3lpa$(i);
        found = true;
        continue;
      }
      i = i + 1 | 0;
    }
    if (found)
      setFavoriteVideos(this.smallStorageController_0, this.favorites_0);
  };
  FavoritesController.prototype.getFavorites = function () {
    return ArrayList_init_0(this.favorites_0);
  };
  FavoritesController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FavoritesController',
    interfaces: []
  };
  function KidsController(smallStorageController, api) {
    this.smallStorageController_0 = smallStorageController;
    this.api_0 = api;
    this.kids = ArrayList_init();
  }
  KidsController.prototype.load_mbla7x$ = function (newKids) {
    this.kids.clear();
    addAll(this.kids, newKids);
  };
  KidsController.prototype.load_13t91c$ = function (newKids) {
    this.kids.clear();
    this.kids.addAll_brywnq$(newKids);
  };
  KidsController.prototype.updateKid_bm4lxs$ = function (kidId, age) {
    var tmp$;
    tmp$ = this.kids.iterator();
    while (tmp$.hasNext()) {
      var element = tmp$.next();
      if (equals(element.userId, kidId) && element.age !== age && Kid$Companion_getInstance().CheckAge_za3lpa$(age)) {
        element.age = age;
        this.api_0.updateKid_zblc1k$(kidId, age);
        setKids(this.smallStorageController_0, this.kids);
      }
    }
  };
  KidsController.prototype.getSelectedKid = function () {
    if (this.kids.isEmpty())
      return null;
    return this.kids.get_za3lpa$(0);
  };
  KidsController.prototype.getSelectedKidId = function () {
    var tmp$, tmp$_0;
    return (tmp$_0 = (tmp$ = this.getSelectedKid()) != null ? tmp$.userId : null) != null ? tmp$_0 : Kid$Companion_getInstance().NO_ID;
  };
  KidsController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'KidsController',
    interfaces: []
  };
  function PlatformDependencyProvider(userConfig, deviceData, audioLoader, broadcaster, sharedKeyStore) {
    this.userConfig = userConfig;
    this.deviceData = deviceData;
    this.audioLoader = audioLoader;
    this.broadcaster = broadcaster;
    this.sharedKeyStore = sharedKeyStore;
  }
  PlatformDependencyProvider.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'PlatformDependencyProvider',
    interfaces: []
  };
  function SettingsController(language, smallStorageController, api) {
    SettingsController$Companion_getInstance();
    this.smallStorageController_0 = smallStorageController;
    this.api_0 = api;
    this._screenTimeLimitInMinutes_0 = getSettings_screenTimeLimitInMinutes(this.smallStorageController_0, 90);
    this._screenTimeLimitInMilliseconds_0 = Kotlin.Long.fromInt(this._screenTimeLimitInMinutes_0).multiply(L60000);
    this.screenTimeWatchedInMilliseconds_0 = getSettings_screenTimeWatchedInMinutes(this.smallStorageController_0, 0);
    this.screenTimeWatchedTimeAdded_0 = 0;
    this.screenTimeWatchedLastDate_0 = getSettings_screenTimeWatchedDate(this.smallStorageController_0, UTCTimeStamp());
    this._contentType_0 = getSettings_contentType(this.smallStorageController_0);
    this._language_0 = getSettings_language(this.smallStorageController_0, language);
    this._allowSound_0 = getSettings_allowSounds(this.smallStorageController_0, true);
  }
  Object.defineProperty(SettingsController.prototype, 'screenTimeLimitInMinutes', {
    get: function () {
      return this._screenTimeLimitInMinutes_0;
    },
    set: function (value) {
      if (value === this._screenTimeLimitInMinutes_0)
        return;
      this._screenTimeLimitInMinutes_0 = value;
      this._screenTimeLimitInMilliseconds_0 = Kotlin.Long.fromInt(this._screenTimeLimitInMinutes_0).multiply(L60000);
      var editor = this.smallStorageController_0.getEditor();
      setSettings_screenTimeLimitInMinutes(this.smallStorageController_0, this._screenTimeLimitInMinutes_0, editor);
      this.resetScreenTimeWatched_dzjtkx$(editor);
      this.smallStorageController_0.commitAsync_6f7vr6$(editor);
    }
  });
  Object.defineProperty(SettingsController.prototype, 'screenTimeWatchedInMinutes', {
    get: function () {
      var d = this.screenTimeWatchedInMilliseconds_0;
      var minutes = d / L60000.toNumber();
      return numberToInt(round(minutes));
    }
  });
  SettingsController.prototype.resetScreenTimeWatched_dzjtkx$ = function (editor) {
    if (editor === void 0)
      editor = null;
    this.screenTimeWatchedInMilliseconds_0 = 0;
    setSettings_screenTimeWatchedInMinutes(this.smallStorageController_0, 0, editor);
  };
  SettingsController.prototype.addScreenTimeWatched_14dthe$ = function (seconds) {
    this.addScreenTimeWatched_za3lpa$(numberToInt(seconds * 1000));
  };
  SettingsController.prototype.addScreenTimeWatched_za3lpa$ = function (milliseconds) {
    var editor = this.smallStorageController_0.getEditor();
    this.screenTimeWatchedTimeAdded_0 = this.screenTimeWatchedTimeAdded_0 + 1 | 0;
    if (this.screenTimeWatchedTimeAdded_0 > 20) {
      this.screenTimeWatchedTimeAdded_0 = 0;
      this.checkScreenTimeWatchDate_0(false);
      setSettings_screenTimeWatchedDate(this.smallStorageController_0, this.screenTimeWatchedLastDate_0, editor);
    }
    this.screenTimeWatchedInMilliseconds_0 = this.screenTimeWatchedInMilliseconds_0 + milliseconds | 0;
    setSettings_screenTimeWatchedInMinutes(this.smallStorageController_0, this.screenTimeWatchedInMilliseconds_0, editor);
    this.smallStorageController_0.commitAsync_6f7vr6$(editor);
  };
  SettingsController.prototype.checkScreenTimeWatchDate_0 = function (shouldSave) {
    if (!IsUTCTimeStampToday(this.screenTimeWatchedLastDate_0)) {
      this.screenTimeWatchedInMilliseconds_0 = 0;
      this.screenTimeWatchedLastDate_0 = UTCTimeStamp();
      if (shouldSave) {
        var editor = this.smallStorageController_0.getEditor();
        setSettings_screenTimeWatchedInMinutes(this.smallStorageController_0, 0, editor);
        setSettings_screenTimeWatchedDate(this.smallStorageController_0, this.screenTimeWatchedLastDate_0, editor);
        this.smallStorageController_0.commitAsync_6f7vr6$(editor);
      }
    }
  };
  SettingsController.prototype.isOverScreenTimeLimit = function () {
    this.checkScreenTimeWatchDate_0(true);
    return Kotlin.Long.fromInt(this.screenTimeWatchedInMilliseconds_0).compareTo_11rb$(this._screenTimeLimitInMilliseconds_0) >= 0;
  };
  Object.defineProperty(SettingsController.prototype, 'contentType', {
    get: function () {
      return this._contentType_0;
    },
    set: function (value) {
      if (value === this._contentType_0)
        return;
      this._contentType_0 = value;
      setSettings_contentType(this.smallStorageController_0, this._contentType_0);
    }
  });
  Object.defineProperty(SettingsController.prototype, 'language', {
    get: function () {
      return this._language_0;
    },
    set: function (value) {
      if (this._language_0 === value)
        return;
      this._language_0 = value;
      this.api_0.setLanguage_pljp91$(this._language_0);
      setSettings_language(this.smallStorageController_0, this._language_0);
    }
  });
  Object.defineProperty(SettingsController.prototype, 'allowSound', {
    get: function () {
      return this._allowSound_0;
    },
    set: function (value) {
      if (this._allowSound_0 === value)
        return;
      this._allowSound_0 = value;
      setSettings_allowSounds(this.smallStorageController_0, this._allowSound_0);
    }
  });
  function SettingsController$Companion() {
    SettingsController$Companion_instance = this;
    this.SCREEN_TIME_LIMIT_UNLIMITED = 120;
    this.SCREEN_LIMIT_OPTION_0 = 5;
    this.SCREEN_LIMIT_OPTION_0_TEXT = '5';
    this.SCREEN_LIMIT_OPTION_1 = 20;
    this.SCREEN_LIMIT_OPTION_1_TEXT = '20';
    this.SCREEN_LIMIT_OPTION_2 = 40;
    this.SCREEN_LIMIT_OPTION_2_TEXT = '40';
    this.SCREEN_LIMIT_OPTION_3 = 60;
    this.SCREEN_LIMIT_OPTION_3_TEXT = '60';
    this.SCREEN_LIMIT_OPTION_4 = 90;
    this.SCREEN_LIMIT_OPTION_4_TEXT = '90';
    this.SCREEN_LIMIT_OPTION_5 = 120;
    this.SCREEN_TIME_LIMIT_DEFAULT_MINUTES = 90;
    this.SCREEN_LIMIT_ADD_TIME_BEFORE_CHECK_DATE = 20;
  }
  SettingsController$Companion.prototype.ScreenTimeValueIsUnlimited_za3lpa$ = function (t) {
    return t === 0 || t >= 120;
  };
  SettingsController$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var SettingsController$Companion_instance = null;
  function SettingsController$Companion_getInstance() {
    if (SettingsController$Companion_instance === null) {
      new SettingsController$Companion();
    }
    return SettingsController$Companion_instance;
  }
  SettingsController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SettingsController',
    interfaces: []
  };
  function UserController(user, deviceData, smallStorageController, api, jsonReader, jsonFactoryV3) {
    this.user = user;
    this.deviceData = deviceData;
    this.smallStorageController_0 = smallStorageController;
    this.api_0 = api;
    this.jsonReader_0 = jsonReader;
    this.jsonFactoryV3_0 = jsonFactoryV3;
  }
  UserController.prototype.loadInJson_qk3xy8$ = function (json) {
    var tmp$;
    var userJson = Kotlin.isType(tmp$ = json['user'], Object) ? tmp$ : null;
    if (userJson != null)
      user(this.jsonFactoryV3_0, this.user, userJson);
  };
  UserController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'UserController',
    interfaces: []
  };
  function getBit($receiver, nthPlace) {
    return ($receiver >>> nthPlace & 1) === 1;
  }
  function setBit($receiver, nthPlace, value) {
    if (value)
      return $receiver | 1 << nthPlace;
    return $receiver & ~(1 << nthPlace);
  }
  function deviceData($receiver, deviceData, json) {
    var key = 'deviceId';
    var defaultValue = deviceData.deviceId;
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json[key];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : defaultValue;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = defaultValue;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    deviceData.deviceId = getString_dysybg$result;
    var key_0 = 'deviceSubscribed';
    var defaultValue_0 = deviceData.deviceSubscribed;
    var getBoolean_pemel1$result;
    var value_0 = json[key_0];
    if (typeof value_0 === 'boolean') {
      getBoolean_pemel1$result = value_0;
    }
     else if (Kotlin.isNumber(value_0)) {
      getBoolean_pemel1$result = equals(value_0, 1);
    }
     else {
      getBoolean_pemel1$result = defaultValue_0;
    }
    deviceData.deviceSubscribed = getBoolean_pemel1$result;
    var key_1 = 'videoUrl';
    var defaultValue_1 = deviceData.videoUrl;
    var getString_dysybg$result_0;
    getString_dysybg$break: do {
      var tmp$_0;
      var value_1 = json[key_1];
      if (typeof value_1 === 'string') {
        getString_dysybg$result_0 = value_1;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_0 = (tmp$_0 = value_1 != null ? value_1.toString() : null) != null ? tmp$_0 : defaultValue_1;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_0 = defaultValue_1;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    deviceData.videoUrl = getString_dysybg$result_0;
    var key_2 = 'videoImageUrl';
    var defaultValue_2 = deviceData.videoImageUrl;
    var getString_dysybg$result_1;
    getString_dysybg$break: do {
      var tmp$_1;
      var value_2 = json[key_2];
      if (typeof value_2 === 'string') {
        getString_dysybg$result_1 = value_2;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_1 = (tmp$_1 = value_2 != null ? value_2.toString() : null) != null ? tmp$_1 : defaultValue_2;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_1 = defaultValue_2;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    deviceData.videoImageUrl = getString_dysybg$result_1;
    var key_3 = 'folderImageUrl';
    var defaultValue_3 = deviceData.folderImageUrl;
    var getString_dysybg$result_2;
    getString_dysybg$break: do {
      var tmp$_2;
      var value_3 = json[key_3];
      if (typeof value_3 === 'string') {
        getString_dysybg$result_2 = value_3;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_2 = (tmp$_2 = value_3 != null ? value_3.toString() : null) != null ? tmp$_2 : defaultValue_3;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_2 = defaultValue_3;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    deviceData.folderImageUrl = getString_dysybg$result_2;
    var key_4 = 'lastBuild';
    var defaultValue_4 = deviceData.lastBuild;
    var tmp$_3, tmp$_4;
    deviceData.lastBuild = (tmp$_4 = typeof (tmp$_3 = json[key_4]) === 'number' ? tmp$_3 : null) != null ? tmp$_4 : defaultValue_4;
  }
  function toJson_deviceData($receiver, deviceData) {
    var json = {};
    json['deviceId'] = deviceData.deviceId;
    json['deviceSubscribed'] = deviceData.deviceSubscribed;
    json['videoUrl'] = deviceData.videoUrl;
    json['videoImageUrl'] = deviceData.videoImageUrl;
    json['folderImageUrl'] = deviceData.folderImageUrl;
    json['lastBuild'] = deviceData.build;
    return json;
  }
  function JsonFactoryV3(jsonReader) {
    this.jsonReader = jsonReader;
  }
  JsonFactoryV3.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'JsonFactoryV3',
    interfaces: []
  };
  function folder($receiver, json) {
    var key = 'folderId';
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json[key];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var id = getString_dysybg$result;
    var getString_dysybg$result_0;
    getString_dysybg$break: do {
      var tmp$_0;
      var value_0 = json['id'];
      if (typeof value_0 === 'string') {
        getString_dysybg$result_0 = value_0;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_0 = (tmp$_0 = value_0 != null ? value_0.toString() : null) != null ? tmp$_0 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_0 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var old_id = getString_dysybg$result_0;
    var getString_dysybg$result_1;
    getString_dysybg$break: do {
      var tmp$_1;
      var value_1 = json['image'];
      if (typeof value_1 === 'string') {
        getString_dysybg$result_1 = value_1;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_1 = (tmp$_1 = value_1 != null ? value_1.toString() : null) != null ? tmp$_1 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_1 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var image = getString_dysybg$result_1;
    var key_0 = 'mediaType';
    var getString_dysybg$result_2;
    getString_dysybg$break: do {
      var tmp$_2;
      var value_2 = json[key_0];
      if (typeof value_2 === 'string') {
        getString_dysybg$result_2 = value_2;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_2 = (tmp$_2 = value_2 != null ? value_2.toString() : null) != null ? tmp$_2 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_2 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var mediaType = getString_dysybg$result_2;
    var getString_dysybg$result_3;
    getString_dysybg$break: do {
      var tmp$_3;
      var value_3 = json['type'];
      if (typeof value_3 === 'string') {
        getString_dysybg$result_3 = value_3;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_3 = (tmp$_3 = value_3 != null ? value_3.toString() : null) != null ? tmp$_3 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_3 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var type = getString_dysybg$result_3;
    var getString_dysybg$result_4;
    getString_dysybg$break: do {
      var tmp$_4;
      var value_4 = json['variant'];
      if (typeof value_4 === 'string') {
        getString_dysybg$result_4 = value_4;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_4 = (tmp$_4 = value_4 != null ? value_4.toString() : null) != null ? tmp$_4 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_4 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var variant = getString_dysybg$result_4;
    var tmp$_5;
    var subCardsArray = Kotlin.isType(tmp$_5 = json['subcards'], JsArray) ? tmp$_5 : null;
    var array = JsArray(0);
    var tmp$_6;
    tmp$_6 = array.length - 1 | 0;
    for (var i = 0; i <= tmp$_6; i++) {
      array[i] = Unit;
    }
    var videoSubCards = array;
    var array_0 = JsArray(0);
    var tmp$_7;
    tmp$_7 = array_0.length - 1 | 0;
    for (var i_0 = 0; i_0 <= tmp$_7; i_0++) {
      array_0[i_0] = Unit;
    }
    var gameSubCards = array_0;
    if (subCardsArray != null) {
      var size = subCardsArray.length;
      if (mediaType === 'GAME') {
        var array_1 = JsArray(size);
        var tmp$_8;
        tmp$_8 = array_1.length - 1 | 0;
        for (var i_1 = 0; i_1 <= tmp$_8; i_1++) {
          var tmp$_9;
          var tmp$_10;
          var json_0 = Kotlin.isType(tmp$_10 = subCardsArray.get(i_1), Object) ? tmp$_10 : throwCCE();
          array_1[i_1] = game($receiver, Kotlin.isType(tmp$_9 = json_0, Object) ? tmp$_9 : throwCCE());
        }
        gameSubCards = array_1;
      }
       else if (mediaType === 'VIDEO') {
        var array_2 = JsArray(size);
        var tmp$_11;
        tmp$_11 = array_2.length - 1 | 0;
        for (var i_2 = 0; i_2 <= tmp$_11; i_2++) {
          var tmp$_12;
          var json_1 = ensureNotNull(Kotlin.isType(tmp$_12 = subCardsArray.get(i_2), Object) ? tmp$_12 : throwCCE());
          array_2[i_2] = video($receiver, json_1);
        }
        videoSubCards = array_2;
      }
    }
     else {
      videoSubCards = [];
    }
    if (id === '' || id === null) {
      id = old_id;
    }
    if (mediaType === 'GAME') {
      return new Folder(id, image, mediaType, type, variant, gameSubCards);
    }
     else {
      return new Folder(id, image, mediaType, type, variant, videoSubCards);
    }
  }
  function folderColor($receiver, json, defaultColor) {
    if (defaultColor === void 0)
      defaultColor = Folder$BackgroundColor$RED_getInstance();
    var tmp$, tmp$_0;
    var color = Folder$BackgroundColor$Companion_getInstance().FromIndex_za3lpa$((tmp$_0 = typeof (tmp$ = json['colorIndex']) === 'number' ? tmp$ : null) != null ? tmp$_0 : 0);
    return color;
  }
  function toJson_folder($receiver, folder) {
    var json = {};
    json['folderId'] = folder.id;
    json['image'] = folder.image;
    json['mediaType'] = folder.mediaType;
    json['type'] = folder.type;
    json['variant'] = folder.variant;
    var jsonArray = new JsArray();
    if (folder.mediaType === 'GAME') {
      var $receiver_0 = folder.subcards;
      var tmp$;
      for (tmp$ = 0; tmp$ !== $receiver_0.length; ++tmp$) {
        var element = $receiver_0[tmp$];
        var tmp$_0;
        jsonArray.push(toJson_game($receiver, Kotlin.isType(tmp$_0 = element, Game) ? tmp$_0 : throwCCE()));
      }
    }
     else {
      var $receiver_1 = folder.subcards;
      var tmp$_1;
      for (tmp$_1 = 0; tmp$_1 !== $receiver_1.length; ++tmp$_1) {
        var element_0 = $receiver_1[tmp$_1];
        var tmp$_2;
        jsonArray.push(toJson_video($receiver, Kotlin.isType(tmp$_2 = element_0, Video) ? tmp$_2 : throwCCE()));
      }
    }
    json['subcards'] = jsonArray;
    return json;
  }
  function toJson_folderWithColor($receiver, folder, backgroundColor) {
    var json = toJson_folder($receiver, folder);
    println('toJson_folder');
    println(folder);
    json['colorIndex'] = backgroundColor.raw;
    return json;
  }
  function game($receiver, json) {
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json['id'];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var id = getString_dysybg$result;
    var getString_dysybg$result_0;
    getString_dysybg$break: do {
      var tmp$_0;
      var value_0 = json['dif'];
      if (typeof value_0 === 'string') {
        getString_dysybg$result_0 = value_0;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_0 = (tmp$_0 = value_0 != null ? value_0.toString() : null) != null ? tmp$_0 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_0 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var diff = getString_dysybg$result_0;
    var key = 'gameType';
    var getString_dysybg$result_1;
    getString_dysybg$break: do {
      var tmp$_1;
      var value_1 = json[key];
      if (typeof value_1 === 'string') {
        getString_dysybg$result_1 = value_1;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_1 = (tmp$_1 = value_1 != null ? value_1.toString() : null) != null ? tmp$_1 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_1 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var gameType = getString_dysybg$result_1;
    var getBoolean_pemel1$result;
    var value_2 = json['isLocked'];
    if (typeof value_2 === 'boolean') {
      getBoolean_pemel1$result = value_2;
    }
     else if (Kotlin.isNumber(value_2)) {
      getBoolean_pemel1$result = equals(value_2, 1);
    }
     else {
      getBoolean_pemel1$result = false;
    }
    var premium = getBoolean_pemel1$result;
    var getString_dysybg$result_2;
    getString_dysybg$break: do {
      var tmp$_2;
      var value_3 = json['variant'];
      if (typeof value_3 === 'string') {
        getString_dysybg$result_2 = value_3;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_2 = (tmp$_2 = value_3 != null ? value_3.toString() : null) != null ? tmp$_2 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_2 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var variant = getString_dysybg$result_2;
    return new Game(id, diff, gameType, premium, variant);
  }
  function toJson_game($receiver, game) {
    var json = {};
    json['id'] = game.id;
    json['diff'] = game.diff;
    json['gameType'] = game.gameType;
    json['isLocked'] = game.premium;
    json['variant'] = game.variant;
    return json;
  }
  function kid($receiver, json) {
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json['id'];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var id = getString_dysybg$result;
    var tmp$_0, tmp$_1;
    var age = (tmp$_1 = typeof (tmp$_0 = json['age']) === 'number' ? tmp$_0 : null) != null ? tmp$_1 : 0;
    return Kid$Companion_getInstance().CreateApp_bm4lxs$(id, age);
  }
  function user($receiver, user, json) {
    var defaultValue = user.id;
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json['id'];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : defaultValue;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = defaultValue;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    user.id = getString_dysybg$result;
    var defaultValue_0 = user.name;
    var getString_dysybg$result_0;
    getString_dysybg$break: do {
      var tmp$_0;
      var value_0 = json['name'];
      if (typeof value_0 === 'string') {
        getString_dysybg$result_0 = value_0;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_0 = (tmp$_0 = value_0 != null ? value_0.toString() : null) != null ? tmp$_0 : defaultValue_0;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_0 = defaultValue_0;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    user.name = getString_dysybg$result_0;
    var defaultValue_1 = user.email;
    var getString_dysybg$result_1;
    getString_dysybg$break: do {
      var tmp$_1;
      var value_1 = json['email'];
      if (typeof value_1 === 'string') {
        getString_dysybg$result_1 = value_1;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_1 = (tmp$_1 = value_1 != null ? value_1.toString() : null) != null ? tmp$_1 : defaultValue_1;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_1 = defaultValue_1;
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    user.email = getString_dysybg$result_1;
    var key = 'emailIsConfirmed';
    var defaultValue_2 = user.emailIsConfirmed;
    var getBoolean_pemel1$result;
    var value_2 = json[key];
    if (typeof value_2 === 'boolean') {
      getBoolean_pemel1$result = value_2;
    }
     else if (Kotlin.isNumber(value_2)) {
      getBoolean_pemel1$result = equals(value_2, 1);
    }
     else {
      getBoolean_pemel1$result = defaultValue_2;
    }
    user.emailIsConfirmed = getBoolean_pemel1$result;
    var key_0 = 'activeSubscription';
    var defaultValue_3 = user.isSubscribed;
    var getBoolean_pemel1$result_0;
    var value_3 = json[key_0];
    if (typeof value_3 === 'boolean') {
      getBoolean_pemel1$result_0 = value_3;
    }
     else if (Kotlin.isNumber(value_3)) {
      getBoolean_pemel1$result_0 = equals(value_3, 1);
    }
     else {
      getBoolean_pemel1$result_0 = defaultValue_3;
    }
    user.isSubscribed = getBoolean_pemel1$result_0;
  }
  var numberToLong = Kotlin.numberToLong;
  function video($receiver, json) {
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json['videoId'];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var videoId = getString_dysybg$result;
    var getString_dysybg$result_0;
    getString_dysybg$break: do {
      var tmp$_0;
      var value_0 = json['id'];
      if (typeof value_0 === 'string') {
        getString_dysybg$result_0 = value_0;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_0 = (tmp$_0 = value_0 != null ? value_0.toString() : null) != null ? tmp$_0 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_0 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var id = getString_dysybg$result_0;
    var getString_dysybg$result_1;
    getString_dysybg$break: do {
      var tmp$_1;
      var value_1 = json['title'];
      if (typeof value_1 === 'string') {
        getString_dysybg$result_1 = value_1;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_1 = (tmp$_1 = value_1 != null ? value_1.toString() : null) != null ? tmp$_1 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_1 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var title = getString_dysybg$result_1;
    var tmp$_2, tmp$_3;
    var ageMin = (tmp$_3 = typeof (tmp$_2 = json['ageMin']) === 'number' ? tmp$_2 : null) != null ? tmp$_3 : 1;
    var tmp$_4, tmp$_5;
    var ageMax = (tmp$_5 = typeof (tmp$_4 = json['ageMax']) === 'number' ? tmp$_4 : null) != null ? tmp$_5 : 7;
    var tmp$_6, tmp$_7;
    var duration = (tmp$_7 = typeof (tmp$_6 = json['duration']) === 'number' ? tmp$_6 : null) != null ? tmp$_7 : 0;
    var getBoolean_pemel1$result;
    var value_2 = json['compilation'];
    if (typeof value_2 === 'boolean') {
      getBoolean_pemel1$result = value_2;
    }
     else if (Kotlin.isNumber(value_2)) {
      getBoolean_pemel1$result = equals(value_2, 1);
    }
     else {
      getBoolean_pemel1$result = false;
    }
    var compilation = getBoolean_pemel1$result;
    var getBoolean_pemel1$result_0;
    var value_3 = json['isLocked'];
    if (typeof value_3 === 'boolean') {
      getBoolean_pemel1$result_0 = value_3;
    }
     else if (Kotlin.isNumber(value_3)) {
      getBoolean_pemel1$result_0 = equals(value_3, 1);
    }
     else {
      getBoolean_pemel1$result_0 = false;
    }
    var premium = getBoolean_pemel1$result_0;
    var getBoolean_pemel1$result_1;
    var value_4 = json['premium'];
    if (typeof value_4 === 'boolean') {
      getBoolean_pemel1$result_1 = value_4;
    }
     else if (Kotlin.isNumber(value_4)) {
      getBoolean_pemel1$result_1 = equals(value_4, 1);
    }
     else {
      getBoolean_pemel1$result_1 = false;
    }
    var premium_old = getBoolean_pemel1$result_1;
    var tmp$_8;
    var formatsJsonArray = Kotlin.isType(tmp$_8 = json['formats'], JsArray) ? tmp$_8 : null;
    var formatArray;
    var sizeArray;
    println(formatsJsonArray);
    if (formatsJsonArray != null) {
      var size = formatsJsonArray.length;
      sizeArray = Kotlin.longArray(size);
      var array = JsArray(size);
      var tmp$_9;
      tmp$_9 = array.length - 1 | 0;
      for (var i = 0; i <= tmp$_9; i++) {
        var closure$sizeArray = sizeArray;
        var init$result;
        var tmp$_10;
        var formatJson = ensureNotNull(Kotlin.isType(tmp$_10 = formatsJsonArray.get(i), Object) ? tmp$_10 : throwCCE());
        var tmp$_11, tmp$_12;
        var formatId = (tmp$_12 = typeof (tmp$_11 = formatJson['id']) === 'number' ? tmp$_11 : null) != null ? tmp$_12 : 0;
        if (formatId != null && formatId !== 0) {
          var defaultValue = L0;
          var tmp$_13, tmp$_14, tmp$_15;
          var formatSize = (tmp$_15 = (tmp$_14 = Kotlin.isNumber(tmp$_13 = formatJson['size']) ? tmp$_13 : null) != null ? numberToLong(tmp$_14) : null) != null ? tmp$_15 : defaultValue;
          closure$sizeArray[i] = formatSize;
          init$result = Video$Format$Companion_getInstance().FromId_za3lpa$(formatId);
        }
         else {
          var getString_dysybg$result_2;
          getString_dysybg$break: do {
            var tmp$_16;
            var value_5 = formatJson['sd'];
            if (typeof value_5 === 'string') {
              getString_dysybg$result_2 = value_5;
              break getString_dysybg$break;
            }
            try {
              getString_dysybg$result_2 = (tmp$_16 = value_5 != null ? value_5.toString() : null) != null ? tmp$_16 : '';
            }
             catch (e) {
              if (Kotlin.isType(e, Exception)) {
                getString_dysybg$result_2 = '';
                break getString_dysybg$break;
              }
               else
                throw e;
            }
          }
           while (false);
          var formatSd = getString_dysybg$result_2;
          if (toBoolean(formatSd) || equals(formatSd, 'none')) {
            formatSd = 'MP4';
          }
           else if (formatSd != null) {
            formatSd = formatSd.toUpperCase();
          }
          var tmp$_17, tmp$_18;
          var formatHeight = (tmp$_18 = typeof (tmp$_17 = formatJson['height']) === 'number' ? tmp$_17 : null) != null ? tmp$_18 : 0;
          var formatCodec = formatSd + '_' + formatHeight.toString();
          var key = 'fileSize';
          var defaultValue_0 = L0;
          var tmp$_19, tmp$_20, tmp$_21;
          var formatSize_0 = (tmp$_21 = (tmp$_20 = Kotlin.isNumber(tmp$_19 = formatJson[key]) ? tmp$_19 : null) != null ? numberToLong(tmp$_20) : null) != null ? tmp$_21 : defaultValue_0;
          closure$sizeArray[i] = formatSize_0;
          init$result = Video$Format$Companion_getInstance().FromCodec_61zpoe$(formatCodec);
        }
        array[i] = init$result;
      }
      formatArray = array;
    }
     else {
      formatArray = [];
      sizeArray = Kotlin.longArray(0);
    }
    if (videoId === null || videoId === '') {
      videoId = id;
      premium = premium_old;
    }
    return Video_init_0(videoId, id, title, ageMin, ageMax, premium, duration, compilation, formatArray, sizeArray);
  }
  function videoBackpack($receiver, json) {
    println('json ' + json.toString());
    var getString_dysybg$result;
    getString_dysybg$break: do {
      var tmp$;
      var value = json['videoId'];
      if (typeof value === 'string') {
        getString_dysybg$result = value;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var videoId = getString_dysybg$result;
    var getString_dysybg$result_0;
    getString_dysybg$break: do {
      var tmp$_0;
      var value_0 = json['id'];
      if (typeof value_0 === 'string') {
        getString_dysybg$result_0 = value_0;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_0 = (tmp$_0 = value_0 != null ? value_0.toString() : null) != null ? tmp$_0 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_0 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var id = getString_dysybg$result_0;
    var getString_dysybg$result_1;
    getString_dysybg$break: do {
      var tmp$_1;
      var value_1 = json['title'];
      if (typeof value_1 === 'string') {
        getString_dysybg$result_1 = value_1;
        break getString_dysybg$break;
      }
      try {
        getString_dysybg$result_1 = (tmp$_1 = value_1 != null ? value_1.toString() : null) != null ? tmp$_1 : '';
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          getString_dysybg$result_1 = '';
          break getString_dysybg$break;
        }
         else
          throw e;
      }
    }
     while (false);
    var title = getString_dysybg$result_1;
    var tmp$_2, tmp$_3;
    var ageMin = (tmp$_3 = typeof (tmp$_2 = json['ageMin']) === 'number' ? tmp$_2 : null) != null ? tmp$_3 : 1;
    var tmp$_4, tmp$_5;
    var ageMax = (tmp$_5 = typeof (tmp$_4 = json['ageMax']) === 'number' ? tmp$_4 : null) != null ? tmp$_5 : 7;
    var tmp$_6, tmp$_7;
    var duration = (tmp$_7 = typeof (tmp$_6 = json['duration']) === 'number' ? tmp$_6 : null) != null ? tmp$_7 : 0;
    var getBoolean_pemel1$result;
    var value_2 = json['compilation'];
    if (typeof value_2 === 'boolean') {
      getBoolean_pemel1$result = value_2;
    }
     else if (Kotlin.isNumber(value_2)) {
      getBoolean_pemel1$result = equals(value_2, 1);
    }
     else {
      getBoolean_pemel1$result = false;
    }
    var compilation = getBoolean_pemel1$result;
    var getBoolean_pemel1$result_0;
    var value_3 = json['isLocked'];
    if (typeof value_3 === 'boolean') {
      getBoolean_pemel1$result_0 = value_3;
    }
     else if (Kotlin.isNumber(value_3)) {
      getBoolean_pemel1$result_0 = equals(value_3, 1);
    }
     else {
      getBoolean_pemel1$result_0 = false;
    }
    var premium = getBoolean_pemel1$result_0;
    var tmp$_8;
    var formatsJsonArray = Kotlin.isType(tmp$_8 = json['formats'], JsArray) ? tmp$_8 : null;
    println('formats' + toString(formatsJsonArray));
    var formatArray;
    var sizeArray;
    if (formatsJsonArray != null) {
      var size = formatsJsonArray.length;
      println('size' + toString(size));
      sizeArray = Kotlin.longArray(size);
      var array = JsArray(size);
      var tmp$_9;
      tmp$_9 = array.length - 1 | 0;
      for (var i = 0; i <= tmp$_9; i++) {
        var closure$sizeArray = sizeArray;
        var tmp$_10;
        var formatJson = ensureNotNull(Kotlin.isType(tmp$_10 = formatsJsonArray.get(i), Object) ? tmp$_10 : throwCCE());
        var tmp$_11, tmp$_12;
        var formatId = (tmp$_12 = typeof (tmp$_11 = formatJson['id']) === 'number' ? tmp$_11 : null) != null ? tmp$_12 : 0;
        var defaultValue = L0;
        var tmp$_13, tmp$_14, tmp$_15;
        var formatSize = (tmp$_15 = (tmp$_14 = Kotlin.isNumber(tmp$_13 = formatJson['size']) ? tmp$_13 : null) != null ? numberToLong(tmp$_14) : null) != null ? tmp$_15 : defaultValue;
        closure$sizeArray[i] = formatSize;
        array[i] = Video$Format$Companion_getInstance().FromId_za3lpa$(formatId);
      }
      formatArray = array;
    }
     else {
      formatArray = [];
      sizeArray = Kotlin.longArray(0);
    }
    return Video_init_0(videoId, id, title, ageMin, ageMax, premium, duration, compilation, formatArray, sizeArray);
  }
  function toJson_video($receiver, video) {
    var json = {};
    json['videoId'] = video.videoId;
    json['title'] = video.title;
    json['ageMax'] = video.ageMax;
    json['ageMin'] = video.ageMin;
    json['duration'] = video.durationInSeconds;
    json['compilation'] = video.compilation;
    json['premium'] = video.isPremium;
    var jsonArray = new JsArray();
    var $receiver_0 = video.formats;
    var tmp$, tmp$_0;
    var index = 0;
    for (tmp$ = 0; tmp$ !== $receiver_0.length; ++tmp$) {
      var item = $receiver_0[tmp$];
      var index_0 = (tmp$_0 = index, index = tmp$_0 + 1 | 0, tmp$_0);
      var formatJson = {};
      var size = index_0 < video.formatSizes.length ? video.formatSizes[index_0] : L0;
      formatJson['id'] = item.id;
      formatJson['size'] = size;
      jsonArray.push(formatJson);
    }
    json['formats'] = jsonArray;
    return json;
  }
  function getDeviceData($receiver, deviceData) {
    deviceData.deviceId = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_deviceId', deviceData.deviceId);
    deviceData.lastBootTime = $receiver.keyStoreReader_8be2vx$.getLong_prxk4z$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_lastBootTime', deviceData.lastBootTime);
    deviceData.deviceSubscribed = $receiver.keyStoreReader_8be2vx$.getBoolean_krbqyf$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_deviceSubscribed', deviceData.deviceSubscribed);
    deviceData.videoUrl = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_videoUrl', deviceData.videoUrl);
    deviceData.videoImageUrl = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_videoImageUrl', deviceData.videoImageUrl);
    deviceData.folderImageUrl = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_folderImageUrl', deviceData.folderImageUrl);
    deviceData.lastBuild = $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'dd_lastBuild', deviceData.lastBuild);
  }
  function setDeviceData($receiver, editor, deviceData) {
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'dd_deviceId', deviceData.deviceId);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'dd_lastBootTime', deviceData.lastBootTime.toString());
    $receiver.keyStoreReader_8be2vx$.set_krbqyf$(usingEditor, 'dd_deviceSubscribed', deviceData.deviceSubscribed);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'dd_videoUrl', deviceData.videoUrl);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'dd_videoImageUrl', deviceData.videoImageUrl);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'dd_folderImageUrl', deviceData.folderImageUrl);
    $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, 'dd_lastBuild', deviceData.build);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getKids($receiver) {
    var numberOfKids = $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'ks_num', 0);
    if (numberOfKids === 0)
      return [];
    var array = JsArray(numberOfKids);
    var tmp$;
    tmp$ = array.length - 1 | 0;
    for (var i = 0; i <= tmp$; i++) {
      var id = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, LOCAL_STORAGE_KEY_KIDS_PREFIX + toString(i) + '_id', Kid$Companion_getInstance().NO_ID);
      var age = $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, LOCAL_STORAGE_KEY_KIDS_PREFIX + toString(i) + '_age', 4);
      array[i] = Kid$Companion_getInstance().CreateApp_bm4lxs$(id, age);
    }
    return array;
  }
  function setKids($receiver, kids, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor == null ? $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$) : editor;
    var oldNumberOfKids = $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'ks_num', 0);
    var size = kids.size;
    if (oldNumberOfKids > size) {
      for (var i = 0; i < size; i++) {
        $receiver.keyStoreReader_8be2vx$.delete_y5ifao$(usingEditor, LOCAL_STORAGE_KEY_KIDS_PREFIX + toString(i) + '_id');
        $receiver.keyStoreReader_8be2vx$.delete_y5ifao$(usingEditor, LOCAL_STORAGE_KEY_KIDS_PREFIX + toString(i) + '_age');
      }
    }
    $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, 'ks_num', size);
    for (var i_0 = 0; i_0 < size; i_0++) {
      var kid = kids.get_za3lpa$(i_0);
      $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, LOCAL_STORAGE_KEY_KIDS_PREFIX + toString(i_0) + '_id', kid.userId);
      $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, LOCAL_STORAGE_KEY_KIDS_PREFIX + toString(i_0) + '_age', kid.age);
    }
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getSettings_contentType($receiver) {
    return Folder$ContentType$Companion_getInstance().FromRaw_61zpoe$($receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'set_contentType', Folder$ContentType$MIXED_getInstance().raw));
  }
  function setSettings_contentType($receiver, contentType, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'set_contentType', contentType.raw);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getSettings_language($receiver, language) {
    return Language$Companion_getInstance().FromId_za3lpa$($receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'set_language', language.id));
  }
  function setSettings_language($receiver, language, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, 'set_language', language.id);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getSettings_screenTimeLimitInMinutes($receiver, default_0) {
    return $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'set_screenTimeLimit', default_0);
  }
  function setSettings_screenTimeLimitInMinutes($receiver, value, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, 'set_screenTimeLimit', value);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getSettings_screenTimeWatchedInMinutes($receiver, default_0) {
    return $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'set_screenTimeWatched', default_0);
  }
  function setSettings_screenTimeWatchedInMinutes($receiver, value, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, 'set_screenTimeWatched', value);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getSettings_screenTimeWatchedDate($receiver, default_0) {
    return $receiver.keyStoreReader_8be2vx$.getLong_prxk4z$($receiver.sharedKeyValueStorage_8be2vx$, 'set_screenTimeWatchedDate', default_0);
  }
  function setSettings_screenTimeWatchedDate($receiver, value, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_prxk4z$(usingEditor, 'set_screenTimeWatchedDate', value);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getSettings_allowSounds($receiver, default_0) {
    return $receiver.keyStoreReader_8be2vx$.getBoolean_krbqyf$($receiver.sharedKeyValueStorage_8be2vx$, 'set_allowSounds', default_0);
  }
  function setSettings_allowSounds($receiver, value, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_krbqyf$(usingEditor, 'set_allowSounds', value);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function setUser($receiver, editor, user) {
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'u_id', user.id);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'u_name', user.name);
    $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, 'u_email', user.email);
    $receiver.keyStoreReader_8be2vx$.set_krbqyf$(usingEditor, 'u_emailIsConfirmed', user.emailIsConfirmed);
    $receiver.keyStoreReader_8be2vx$.set_krbqyf$(usingEditor, 'u_activeSubscription', user.isSubscribed);
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getUser($receiver, user) {
    user.id = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'u_id', user.id);
    user.name = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'u_name', user.name);
    user.email = $receiver.keyStoreReader_8be2vx$.getString_mjlkiq$($receiver.sharedKeyValueStorage_8be2vx$, 'u_email', user.email);
    user.emailIsConfirmed = $receiver.keyStoreReader_8be2vx$.getBoolean_krbqyf$($receiver.sharedKeyValueStorage_8be2vx$, 'u_emailIsConfirmed', user.emailIsConfirmed);
    user.isSubscribed = $receiver.keyStoreReader_8be2vx$.getBoolean_krbqyf$($receiver.sharedKeyValueStorage_8be2vx$, 'u_activeSubscription', user.isSubscribed);
  }
  function setFavoriteVideos($receiver, videos, editor) {
    if (editor === void 0)
      editor = null;
    var usingEditor = editor != null ? editor : $receiver.keyStoreReader_8be2vx$.getEditor_6f7vr6$($receiver.sharedKeyValueStorage_8be2vx$);
    var oldNumberOfFavorites = $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'favs_numberOf', 0);
    var newNumberOf = videos.size;
    if (newNumberOf < oldNumberOfFavorites) {
      for (var i = 0; i < oldNumberOfFavorites; i++)
        $receiver.keyStoreReader_8be2vx$.delete_y5ifao$(usingEditor, LOCAL_STORAGE_KEY_FAVORITES + i + '_data');
    }
    $receiver.keyStoreReader_8be2vx$.set_v8w1ha$(usingEditor, 'favs_numberOf', newNumberOf);
    for (var i_0 = 0; i_0 < newNumberOf; i_0++) {
      $receiver.keyStoreReader_8be2vx$.set_mjlkiq$(usingEditor, LOCAL_STORAGE_KEY_FAVORITES + i_0 + '_data', JSON_0.stringify(toJson_video($receiver.jsonFactoryV3_8be2vx$, videos.get_za3lpa$(i_0))));
    }
    if (editor == null)
      $receiver.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(usingEditor);
  }
  function getFavoriteVideos($receiver) {
    var number = $receiver.keyStoreReader_8be2vx$.getInt_v8w1ha$($receiver.sharedKeyValueStorage_8be2vx$, 'favs_numberOf', 0);
    if (number <= 0)
      return [];
    var array = JsArray(number);
    var tmp$;
    tmp$ = array.length - 1 | 0;
    for (var i = 0; i <= tmp$; i++) {
      var tmp$_0 = $receiver.jsonFactoryV3_8be2vx$;
      var jsonString = $receiver.keyStoreReader_8be2vx$.getString_y5ifao$($receiver.sharedKeyValueStorage_8be2vx$, LOCAL_STORAGE_KEY_FAVORITES + i + '_data');
      var tmp$_1;
      try {
        tmp$_1 = JSON_0.parse(jsonString);
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          tmp$_1 = null;
        }
         else
          throw e;
      }
      array[i] = video(tmp$_0, ensureNotNull(tmp$_1));
    }
    return array;
  }
  function SmallStorageController(sharedKeyValueStorage, keyStoreReader, jsonReader, jsonFactoryV3) {
    this.sharedKeyValueStorage_8be2vx$ = sharedKeyValueStorage;
    this.keyStoreReader_8be2vx$ = keyStoreReader;
    this.jsonReader_8be2vx$ = jsonReader;
    this.jsonFactoryV3_8be2vx$ = jsonFactoryV3;
  }
  SmallStorageController.prototype.getEditor = function () {
    return this.keyStoreReader_8be2vx$.getEditor_6f7vr6$(this.sharedKeyValueStorage_8be2vx$);
  };
  SmallStorageController.prototype.commit_6f7vr6$ = function (editor) {
    this.keyStoreReader_8be2vx$.commit_6f7vr6$(editor);
  };
  SmallStorageController.prototype.commitAsync_6f7vr6$ = function (editor) {
    this.keyStoreReader_8be2vx$.commitAsync_6f7vr6$(editor);
  };
  SmallStorageController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SmallStorageController',
    interfaces: []
  };
  var LOCAL_STORAGE_KEY_DEVICE_DATA_PREFIX;
  var LOCAL_STORAGE_KEY_USER_PREFIX;
  var LOCAL_STORAGE_KEY_KIDS_PREFIX;
  var LOCAL_STORAGE_KEY_SETTINGS_PREFIX;
  var LOCAL_STORAGE_KEY_FAVORITES;
  function SubscriptionEvent(type) {
    this.type = type;
  }
  function SubscriptionEvent$Type(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function SubscriptionEvent$Type_initFields() {
    SubscriptionEvent$Type_initFields = function () {
    };
    SubscriptionEvent$Type$OPEN_instance = new SubscriptionEvent$Type('OPEN', 0, 'open');
    SubscriptionEvent$Type$WENT_OFFLINE_instance = new SubscriptionEvent$Type('WENT_OFFLINE', 1, 'went_offline');
    SubscriptionEvent$Type$DID_SUBSCRIBE_instance = new SubscriptionEvent$Type('DID_SUBSCRIBE', 2, 'did_subscribe');
    SubscriptionEvent$Type$NOT_SUBSCRIBE_instance = new SubscriptionEvent$Type('NOT_SUBSCRIBE', 3, 'not_subscribed');
    SubscriptionEvent$Type$Companion_getInstance();
  }
  var SubscriptionEvent$Type$OPEN_instance;
  function SubscriptionEvent$Type$OPEN_getInstance() {
    SubscriptionEvent$Type_initFields();
    return SubscriptionEvent$Type$OPEN_instance;
  }
  var SubscriptionEvent$Type$WENT_OFFLINE_instance;
  function SubscriptionEvent$Type$WENT_OFFLINE_getInstance() {
    SubscriptionEvent$Type_initFields();
    return SubscriptionEvent$Type$WENT_OFFLINE_instance;
  }
  var SubscriptionEvent$Type$DID_SUBSCRIBE_instance;
  function SubscriptionEvent$Type$DID_SUBSCRIBE_getInstance() {
    SubscriptionEvent$Type_initFields();
    return SubscriptionEvent$Type$DID_SUBSCRIBE_instance;
  }
  var SubscriptionEvent$Type$NOT_SUBSCRIBE_instance;
  function SubscriptionEvent$Type$NOT_SUBSCRIBE_getInstance() {
    SubscriptionEvent$Type_initFields();
    return SubscriptionEvent$Type$NOT_SUBSCRIBE_instance;
  }
  function SubscriptionEvent$Type$Companion() {
    SubscriptionEvent$Type$Companion_instance = this;
  }
  SubscriptionEvent$Type$Companion.prototype.FromRawOptional_61zpoe$ = function (raw) {
    var tmp$;
    if (equals(raw, SubscriptionEvent$Type$OPEN_getInstance().raw))
      tmp$ = SubscriptionEvent$Type$OPEN_getInstance();
    else if (equals(raw, SubscriptionEvent$Type$WENT_OFFLINE_getInstance().raw))
      tmp$ = SubscriptionEvent$Type$WENT_OFFLINE_getInstance();
    else if (equals(raw, SubscriptionEvent$Type$DID_SUBSCRIBE_getInstance().raw))
      tmp$ = SubscriptionEvent$Type$DID_SUBSCRIBE_getInstance();
    else if (equals(raw, SubscriptionEvent$Type$NOT_SUBSCRIBE_getInstance().raw))
      tmp$ = SubscriptionEvent$Type$NOT_SUBSCRIBE_getInstance();
    else
      tmp$ = null;
    return tmp$;
  };
  SubscriptionEvent$Type$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var SubscriptionEvent$Type$Companion_instance = null;
  function SubscriptionEvent$Type$Companion_getInstance() {
    SubscriptionEvent$Type_initFields();
    if (SubscriptionEvent$Type$Companion_instance === null) {
      new SubscriptionEvent$Type$Companion();
    }
    return SubscriptionEvent$Type$Companion_instance;
  }
  SubscriptionEvent$Type.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Type',
    interfaces: [Enum]
  };
  function SubscriptionEvent$Type$values() {
    return [SubscriptionEvent$Type$OPEN_getInstance(), SubscriptionEvent$Type$WENT_OFFLINE_getInstance(), SubscriptionEvent$Type$DID_SUBSCRIBE_getInstance(), SubscriptionEvent$Type$NOT_SUBSCRIBE_getInstance()];
  }
  SubscriptionEvent$Type.values = SubscriptionEvent$Type$values;
  function SubscriptionEvent$Type$valueOf(name) {
    switch (name) {
      case 'OPEN':
        return SubscriptionEvent$Type$OPEN_getInstance();
      case 'WENT_OFFLINE':
        return SubscriptionEvent$Type$WENT_OFFLINE_getInstance();
      case 'DID_SUBSCRIBE':
        return SubscriptionEvent$Type$DID_SUBSCRIBE_getInstance();
      case 'NOT_SUBSCRIBE':
        return SubscriptionEvent$Type$NOT_SUBSCRIBE_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.SubscriptionEvent.Type.' + name);
    }
  }
  SubscriptionEvent$Type.valueOf_61zpoe$ = SubscriptionEvent$Type$valueOf;
  SubscriptionEvent.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SubscriptionEvent',
    interfaces: []
  };
  SubscriptionEvent.prototype.component1 = function () {
    return this.type;
  };
  SubscriptionEvent.prototype.copy_68kyrk$ = function (type) {
    return new SubscriptionEvent(type === void 0 ? this.type : type);
  };
  SubscriptionEvent.prototype.toString = function () {
    return 'SubscriptionEvent(type=' + Kotlin.toString(this.type) + ')';
  };
  SubscriptionEvent.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.type) | 0;
    return result;
  };
  SubscriptionEvent.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && Kotlin.equals(this.type, other.type))));
  };
  function BackpackItem() {
  }
  function BackpackItem$Type(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function BackpackItem$Type_initFields() {
    BackpackItem$Type_initFields = function () {
    };
    BackpackItem$Type$VIDEO_instance = new BackpackItem$Type('VIDEO', 0, 'video');
  }
  var BackpackItem$Type$VIDEO_instance;
  function BackpackItem$Type$VIDEO_getInstance() {
    BackpackItem$Type_initFields();
    return BackpackItem$Type$VIDEO_instance;
  }
  BackpackItem$Type.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Type',
    interfaces: [Enum]
  };
  function BackpackItem$Type$values() {
    return [BackpackItem$Type$VIDEO_getInstance()];
  }
  BackpackItem$Type.values = BackpackItem$Type$values;
  function BackpackItem$Type$valueOf(name) {
    switch (name) {
      case 'VIDEO':
        return BackpackItem$Type$VIDEO_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.BackpackItem.Type.' + name);
    }
  }
  BackpackItem$Type.valueOf_61zpoe$ = BackpackItem$Type$valueOf;
  BackpackItem.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'BackpackItem',
    interfaces: []
  };
  var BUILD;
  function DeviceData(deviceId, deviceSubscribed, build, lastBuild, lastBootTime, videoUrl, videoImageUrl, folderImageUrl) {
    if (deviceId === void 0)
      deviceId = '0';
    if (deviceSubscribed === void 0)
      deviceSubscribed = false;
    if (build === void 0)
      build = 5;
    if (lastBuild === void 0)
      lastBuild = 5;
    if (lastBootTime === void 0)
      lastBootTime = L0;
    if (videoUrl === void 0)
      videoUrl = 'https://d23sw6prl9jc74.cloudfront.net/';
    if (videoImageUrl === void 0)
      videoImageUrl = 'https://d22oud9a7xyyql.cloudfront.net/V2/';
    if (folderImageUrl === void 0)
      folderImageUrl = 'https://d2ci7g2i49tul4.cloudfront.net/';
    this.deviceId = deviceId;
    this.deviceSubscribed = deviceSubscribed;
    this.build = build;
    this.lastBuild = lastBuild;
    this.lastBootTime = lastBootTime;
    this.videoUrl = videoUrl;
    this.videoImageUrl = videoImageUrl;
    this.folderImageUrl = folderImageUrl;
  }
  DeviceData.prototype.isRegistered = function () {
    return !equals(this.deviceId, '0');
  };
  DeviceData.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'DeviceData',
    interfaces: []
  };
  DeviceData.prototype.component1 = function () {
    return this.deviceId;
  };
  DeviceData.prototype.component2 = function () {
    return this.deviceSubscribed;
  };
  DeviceData.prototype.component3 = function () {
    return this.build;
  };
  DeviceData.prototype.component4 = function () {
    return this.lastBuild;
  };
  DeviceData.prototype.component5 = function () {
    return this.lastBootTime;
  };
  DeviceData.prototype.component6 = function () {
    return this.videoUrl;
  };
  DeviceData.prototype.component7 = function () {
    return this.videoImageUrl;
  };
  DeviceData.prototype.component8 = function () {
    return this.folderImageUrl;
  };
  DeviceData.prototype.copy_gv2c04$ = function (deviceId, deviceSubscribed, build, lastBuild, lastBootTime, videoUrl, videoImageUrl, folderImageUrl) {
    return new DeviceData(deviceId === void 0 ? this.deviceId : deviceId, deviceSubscribed === void 0 ? this.deviceSubscribed : deviceSubscribed, build === void 0 ? this.build : build, lastBuild === void 0 ? this.lastBuild : lastBuild, lastBootTime === void 0 ? this.lastBootTime : lastBootTime, videoUrl === void 0 ? this.videoUrl : videoUrl, videoImageUrl === void 0 ? this.videoImageUrl : videoImageUrl, folderImageUrl === void 0 ? this.folderImageUrl : folderImageUrl);
  };
  DeviceData.prototype.toString = function () {
    return 'DeviceData(deviceId=' + Kotlin.toString(this.deviceId) + (', deviceSubscribed=' + Kotlin.toString(this.deviceSubscribed)) + (', build=' + Kotlin.toString(this.build)) + (', lastBuild=' + Kotlin.toString(this.lastBuild)) + (', lastBootTime=' + Kotlin.toString(this.lastBootTime)) + (', videoUrl=' + Kotlin.toString(this.videoUrl)) + (', videoImageUrl=' + Kotlin.toString(this.videoImageUrl)) + (', folderImageUrl=' + Kotlin.toString(this.folderImageUrl)) + ')';
  };
  DeviceData.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.deviceId) | 0;
    result = result * 31 + Kotlin.hashCode(this.deviceSubscribed) | 0;
    result = result * 31 + Kotlin.hashCode(this.build) | 0;
    result = result * 31 + Kotlin.hashCode(this.lastBuild) | 0;
    result = result * 31 + Kotlin.hashCode(this.lastBootTime) | 0;
    result = result * 31 + Kotlin.hashCode(this.videoUrl) | 0;
    result = result * 31 + Kotlin.hashCode(this.videoImageUrl) | 0;
    result = result * 31 + Kotlin.hashCode(this.folderImageUrl) | 0;
    return result;
  };
  DeviceData.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.deviceId, other.deviceId) && Kotlin.equals(this.deviceSubscribed, other.deviceSubscribed) && Kotlin.equals(this.build, other.build) && Kotlin.equals(this.lastBuild, other.lastBuild) && Kotlin.equals(this.lastBootTime, other.lastBootTime) && Kotlin.equals(this.videoUrl, other.videoUrl) && Kotlin.equals(this.videoImageUrl, other.videoImageUrl) && Kotlin.equals(this.folderImageUrl, other.folderImageUrl)))));
  };
  function Folder(id, image, mediaType, type, variant, subcards) {
    this.id = id;
    this.image = image;
    this.mediaType = mediaType;
    this.type = type;
    this.variant = variant;
    this.subcards = subcards;
  }
  function Folder$BackgroundColor(name, ordinal, raw, colorName) {
    Enum.call(this);
    this.raw = raw;
    this.colorName = colorName;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Folder$BackgroundColor_initFields() {
    Folder$BackgroundColor_initFields = function () {
    };
    Folder$BackgroundColor$RED_instance = new Folder$BackgroundColor('RED', 0, 0, 'red');
    Folder$BackgroundColor$YELLOW_instance = new Folder$BackgroundColor('YELLOW', 1, 1, 'yellow');
    Folder$BackgroundColor$BLUE_instance = new Folder$BackgroundColor('BLUE', 2, 2, 'blue');
    Folder$BackgroundColor$GREEN_instance = new Folder$BackgroundColor('GREEN', 3, 3, 'green');
    Folder$BackgroundColor$ORANGE_instance = new Folder$BackgroundColor('ORANGE', 4, 4, 'orange');
    Folder$BackgroundColor$PURPLE_instance = new Folder$BackgroundColor('PURPLE', 5, 5, 'purple');
    Folder$BackgroundColor$Companion_getInstance();
  }
  var Folder$BackgroundColor$RED_instance;
  function Folder$BackgroundColor$RED_getInstance() {
    Folder$BackgroundColor_initFields();
    return Folder$BackgroundColor$RED_instance;
  }
  var Folder$BackgroundColor$YELLOW_instance;
  function Folder$BackgroundColor$YELLOW_getInstance() {
    Folder$BackgroundColor_initFields();
    return Folder$BackgroundColor$YELLOW_instance;
  }
  var Folder$BackgroundColor$BLUE_instance;
  function Folder$BackgroundColor$BLUE_getInstance() {
    Folder$BackgroundColor_initFields();
    return Folder$BackgroundColor$BLUE_instance;
  }
  var Folder$BackgroundColor$GREEN_instance;
  function Folder$BackgroundColor$GREEN_getInstance() {
    Folder$BackgroundColor_initFields();
    return Folder$BackgroundColor$GREEN_instance;
  }
  var Folder$BackgroundColor$ORANGE_instance;
  function Folder$BackgroundColor$ORANGE_getInstance() {
    Folder$BackgroundColor_initFields();
    return Folder$BackgroundColor$ORANGE_instance;
  }
  var Folder$BackgroundColor$PURPLE_instance;
  function Folder$BackgroundColor$PURPLE_getInstance() {
    Folder$BackgroundColor_initFields();
    return Folder$BackgroundColor$PURPLE_instance;
  }
  function Folder$BackgroundColor$Companion() {
    Folder$BackgroundColor$Companion_instance = this;
  }
  Folder$BackgroundColor$Companion.prototype.FromCollectionIndex_za3lpa$ = function (index) {
    var pos = index % 6;
    return this.FromIndex_za3lpa$(pos);
  };
  Folder$BackgroundColor$Companion.prototype.FromIndex_za3lpa$ = function (index) {
    var tmp$;
    if (index === Folder$BackgroundColor$RED_getInstance().raw)
      tmp$ = Folder$BackgroundColor$RED_getInstance();
    else if (index === Folder$BackgroundColor$YELLOW_getInstance().raw)
      tmp$ = Folder$BackgroundColor$YELLOW_getInstance();
    else if (index === Folder$BackgroundColor$BLUE_getInstance().raw)
      tmp$ = Folder$BackgroundColor$BLUE_getInstance();
    else if (index === Folder$BackgroundColor$GREEN_getInstance().raw)
      tmp$ = Folder$BackgroundColor$GREEN_getInstance();
    else if (index === Folder$BackgroundColor$ORANGE_getInstance().raw)
      tmp$ = Folder$BackgroundColor$ORANGE_getInstance();
    else if (index === Folder$BackgroundColor$PURPLE_getInstance().raw)
      tmp$ = Folder$BackgroundColor$PURPLE_getInstance();
    else
      tmp$ = Folder$BackgroundColor$RED_getInstance();
    return tmp$;
  };
  Folder$BackgroundColor$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Folder$BackgroundColor$Companion_instance = null;
  function Folder$BackgroundColor$Companion_getInstance() {
    Folder$BackgroundColor_initFields();
    if (Folder$BackgroundColor$Companion_instance === null) {
      new Folder$BackgroundColor$Companion();
    }
    return Folder$BackgroundColor$Companion_instance;
  }
  Folder$BackgroundColor.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'BackgroundColor',
    interfaces: [Enum]
  };
  function Folder$BackgroundColor$values() {
    return [Folder$BackgroundColor$RED_getInstance(), Folder$BackgroundColor$YELLOW_getInstance(), Folder$BackgroundColor$BLUE_getInstance(), Folder$BackgroundColor$GREEN_getInstance(), Folder$BackgroundColor$ORANGE_getInstance(), Folder$BackgroundColor$PURPLE_getInstance()];
  }
  Folder$BackgroundColor.values = Folder$BackgroundColor$values;
  function Folder$BackgroundColor$valueOf(name) {
    switch (name) {
      case 'RED':
        return Folder$BackgroundColor$RED_getInstance();
      case 'YELLOW':
        return Folder$BackgroundColor$YELLOW_getInstance();
      case 'BLUE':
        return Folder$BackgroundColor$BLUE_getInstance();
      case 'GREEN':
        return Folder$BackgroundColor$GREEN_getInstance();
      case 'ORANGE':
        return Folder$BackgroundColor$ORANGE_getInstance();
      case 'PURPLE':
        return Folder$BackgroundColor$PURPLE_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.Folder.BackgroundColor.' + name);
    }
  }
  Folder$BackgroundColor.valueOf_61zpoe$ = Folder$BackgroundColor$valueOf;
  function Folder$ContentType(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Folder$ContentType_initFields() {
    Folder$ContentType_initFields = function () {
    };
    Folder$ContentType$MIXED_instance = new Folder$ContentType('MIXED', 0, 'mix');
    Folder$ContentType$ENTERTAINMENT_instance = new Folder$ContentType('ENTERTAINMENT', 1, 'entertainment');
    Folder$ContentType$EDUCATION_instance = new Folder$ContentType('EDUCATION', 2, 'education');
    Folder$ContentType$Companion_getInstance();
  }
  var Folder$ContentType$MIXED_instance;
  function Folder$ContentType$MIXED_getInstance() {
    Folder$ContentType_initFields();
    return Folder$ContentType$MIXED_instance;
  }
  var Folder$ContentType$ENTERTAINMENT_instance;
  function Folder$ContentType$ENTERTAINMENT_getInstance() {
    Folder$ContentType_initFields();
    return Folder$ContentType$ENTERTAINMENT_instance;
  }
  var Folder$ContentType$EDUCATION_instance;
  function Folder$ContentType$EDUCATION_getInstance() {
    Folder$ContentType_initFields();
    return Folder$ContentType$EDUCATION_instance;
  }
  function Folder$ContentType$Companion() {
    Folder$ContentType$Companion_instance = this;
    this.NUMBER_OF_TYPES = 3;
    this.ROW_MIXED = 0;
    this.ROW_ENTERTAINMENT = 1;
    this.ROW_EDUCATION = 2;
  }
  Folder$ContentType$Companion.prototype.FromRaw_61zpoe$ = function (raw) {
    if (equals(raw, Folder$ContentType$MIXED_getInstance().raw))
      return Folder$ContentType$MIXED_getInstance();
    else if (equals(raw, Folder$ContentType$ENTERTAINMENT_getInstance().raw))
      return Folder$ContentType$ENTERTAINMENT_getInstance();
    else if (equals(raw, Folder$ContentType$EDUCATION_getInstance().raw))
      return Folder$ContentType$EDUCATION_getInstance();
    else
      return Folder$ContentType$MIXED_getInstance();
  };
  Folder$ContentType$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Folder$ContentType$Companion_instance = null;
  function Folder$ContentType$Companion_getInstance() {
    Folder$ContentType_initFields();
    if (Folder$ContentType$Companion_instance === null) {
      new Folder$ContentType$Companion();
    }
    return Folder$ContentType$Companion_instance;
  }
  Folder$ContentType.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ContentType',
    interfaces: [Enum]
  };
  function Folder$ContentType$values() {
    return [Folder$ContentType$MIXED_getInstance(), Folder$ContentType$ENTERTAINMENT_getInstance(), Folder$ContentType$EDUCATION_getInstance()];
  }
  Folder$ContentType.values = Folder$ContentType$values;
  function Folder$ContentType$valueOf(name) {
    switch (name) {
      case 'MIXED':
        return Folder$ContentType$MIXED_getInstance();
      case 'ENTERTAINMENT':
        return Folder$ContentType$ENTERTAINMENT_getInstance();
      case 'EDUCATION':
        return Folder$ContentType$EDUCATION_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.Folder.ContentType.' + name);
    }
  }
  Folder$ContentType.valueOf_61zpoe$ = Folder$ContentType$valueOf;
  Folder.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Folder',
    interfaces: []
  };
  Folder.prototype.component1 = function () {
    return this.id;
  };
  Folder.prototype.component2 = function () {
    return this.image;
  };
  Folder.prototype.component3 = function () {
    return this.mediaType;
  };
  Folder.prototype.component4 = function () {
    return this.type;
  };
  Folder.prototype.component5 = function () {
    return this.variant;
  };
  Folder.prototype.component6 = function () {
    return this.subcards;
  };
  Folder.prototype.copy_cosvzs$ = function (id, image, mediaType, type, variant, subcards) {
    return new Folder(id === void 0 ? this.id : id, image === void 0 ? this.image : image, mediaType === void 0 ? this.mediaType : mediaType, type === void 0 ? this.type : type, variant === void 0 ? this.variant : variant, subcards === void 0 ? this.subcards : subcards);
  };
  Folder.prototype.toString = function () {
    return 'Folder(id=' + Kotlin.toString(this.id) + (', image=' + Kotlin.toString(this.image)) + (', mediaType=' + Kotlin.toString(this.mediaType)) + (', type=' + Kotlin.toString(this.type)) + (', variant=' + Kotlin.toString(this.variant)) + (', subcards=' + Kotlin.toString(this.subcards)) + ')';
  };
  Folder.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.id) | 0;
    result = result * 31 + Kotlin.hashCode(this.image) | 0;
    result = result * 31 + Kotlin.hashCode(this.mediaType) | 0;
    result = result * 31 + Kotlin.hashCode(this.type) | 0;
    result = result * 31 + Kotlin.hashCode(this.variant) | 0;
    result = result * 31 + Kotlin.hashCode(this.subcards) | 0;
    return result;
  };
  Folder.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.id, other.id) && Kotlin.equals(this.image, other.image) && Kotlin.equals(this.mediaType, other.mediaType) && Kotlin.equals(this.type, other.type) && Kotlin.equals(this.variant, other.variant) && Kotlin.equals(this.subcards, other.subcards)))));
  };
  function Game(id, diff, gameType, premium, variant) {
    this.id = id;
    this.diff = diff;
    this.gameType = gameType;
    this.premium = premium;
    this.variant = variant;
  }
  Game.prototype.getGamePieces_za3lpa$ = function (age) {
    if (this.gameType === 'puzzle') {
      return this.getGamePiecesPuzzle_za3lpa$(age);
    }
     else if (this.gameType === 'memory') {
      return this.getGamePiecesMemory_za3lpa$(age);
    }
     else {
      return 0;
    }
  };
  Game.prototype.getGameDifficulty = function () {
    switch (this.diff) {
      case 'easy':
        return 1;
      case 'medium':
        return 2;
      case 'hard':
        return 3;
      case 'veryHard':
        return 4;
      default:return 0;
    }
  };
  Game.prototype.getGameImage_61zpoe$ = function (bucket) {
    var gameImagesUrl = 'https://d3aod987c9rl70.cloudfront.net/gameImages/images/';
    return gameImagesUrl + bucket + this.id + '.png';
  };
  Game.prototype.getCoverImage_61zpoe$ = function (bucket) {
    var gameImagesUrl = 'https://d3aod987c9rl70.cloudfront.net/gameImages/cover/';
    return gameImagesUrl + bucket + this.id + '.png';
  };
  Game.prototype.getGameUrl_19mbxw$ = function (age, bucket) {
    var puzzleGameUrl = 'https://d3aod987c9rl70.cloudfront.net/webApp/games/index_puzzle.html?v=1&global=1';
    var memoryGameUrl = 'https://d3aod987c9rl70.cloudfront.net/webApp/games/index_memory.html?v=1&global=1';
    if (this.gameType === 'puzzle') {
      return puzzleGameUrl + '&img=' + this.getGameImage_61zpoe$(bucket) + '&dif=' + toString(this.getGamePieces_za3lpa$(age));
    }
     else if (this.gameType === 'memory') {
      return memoryGameUrl + '&dif=' + toString(this.getGamePieces_za3lpa$(age));
    }
     else {
      return '';
    }
  };
  Game.prototype.getGamePiecesPuzzle_za3lpa$ = function (age) {
    if (0 < age && age < 3) {
      switch (this.diff) {
        case 'easy':
          return 1;
        case 'medium':
          return 2;
        case 'hard':
          return 3;
        case 'veryHard':
          return 4;
        default:return 0;
      }
    }
     else if (age > 2 && age < 6) {
      switch (this.diff) {
        case 'easy':
          return 2;
        case 'medium':
          return 3;
        case 'hard':
          return 4;
        case 'veryHard':
          return 5;
        default:return 0;
      }
    }
     else if (age > 5 && age < 8) {
      switch (this.diff) {
        case 'easy':
          return 3;
        case 'medium':
          return 4;
        case 'hard':
          return 5;
        case 'veryHard':
          return 6;
        default:return 0;
      }
    }
     else {
      return 0;
    }
  };
  Game.prototype.getGamePiecesMemory_za3lpa$ = function (age) {
    if (age > 0 && age < 3) {
      switch (this.diff) {
        case 'easy':
          return 2;
        case 'medium':
          return 4;
        case 'hard':
          return 6;
        case 'veryHard':
          return 8;
        default:return 0;
      }
    }
     else if (age > 2 && age < 6) {
      switch (this.diff) {
        case 'easy':
          return 8;
        case 'medium':
          return 12;
        case 'hard':
          return 18;
        case 'veryHard':
          return 24;
        default:return 0;
      }
    }
     else if (age > 5 && age < 8) {
      switch (this.diff) {
        case 'easy':
          return 12;
        case 'medium':
          return 24;
        case 'hard':
          return 32;
        case 'veryHard':
          return 40;
        default:return 0;
      }
    }
     else {
      return 0;
    }
  };
  Game.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Game',
    interfaces: []
  };
  Game.prototype.component1 = function () {
    return this.id;
  };
  Game.prototype.component2 = function () {
    return this.diff;
  };
  Game.prototype.component3 = function () {
    return this.gameType;
  };
  Game.prototype.component4 = function () {
    return this.premium;
  };
  Game.prototype.component5 = function () {
    return this.variant;
  };
  Game.prototype.copy_uu5d6f$ = function (id, diff, gameType, premium, variant) {
    return new Game(id === void 0 ? this.id : id, diff === void 0 ? this.diff : diff, gameType === void 0 ? this.gameType : gameType, premium === void 0 ? this.premium : premium, variant === void 0 ? this.variant : variant);
  };
  Game.prototype.toString = function () {
    return 'Game(id=' + Kotlin.toString(this.id) + (', diff=' + Kotlin.toString(this.diff)) + (', gameType=' + Kotlin.toString(this.gameType)) + (', premium=' + Kotlin.toString(this.premium)) + (', variant=' + Kotlin.toString(this.variant)) + ')';
  };
  Game.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.id) | 0;
    result = result * 31 + Kotlin.hashCode(this.diff) | 0;
    result = result * 31 + Kotlin.hashCode(this.gameType) | 0;
    result = result * 31 + Kotlin.hashCode(this.premium) | 0;
    result = result * 31 + Kotlin.hashCode(this.variant) | 0;
    return result;
  };
  Game.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.id, other.id) && Kotlin.equals(this.diff, other.diff) && Kotlin.equals(this.gameType, other.gameType) && Kotlin.equals(this.premium, other.premium) && Kotlin.equals(this.variant, other.variant)))));
  };
  function Kid(serverId, userId, name, age, serverParentAccountId) {
    Kid$Companion_getInstance();
    this.serverId = serverId;
    this.userId = userId;
    this.name = name;
    this.age = age;
    this.serverParentAccountId = serverParentAccountId;
  }
  function Kid$Companion() {
    Kid$Companion_instance = this;
    this.NO_SERVER_ID = _.net.kidjo.common.models.User.Companion.NO_SERVER_ID;
    this.NO_ID = '0';
    this.DEFAULT_AGE = 4;
    this.NO_NAME = '';
    this.NO_PROFILE_IMAGE = 'NONE';
    this.AGE_MAX = 7;
    this.AGE_MIN = 1;
    this.NUMBER_OF_AGE_OPTIONS = 7;
    this.NO_AGE = -1;
    this.MAX_NAME_LENGTH = 12;
    this.NAME_REGEX_0 = Regex.Companion.fromLiteral_61zpoe$('[a-zA-Z]{1,12}');
  }
  Kid$Companion.prototype.CreateApp_bm4lxs$ = function (id, age) {
    return new Kid(_.net.kidjo.common.models.Kid.Companion.NO_SERVER_ID, id, this.NO_NAME, age, _.net.kidjo.common.models.User.Companion.NO_SERVER_ID);
  };
  Kid$Companion.prototype.CheckName_61zpoe$ = function (name) {
    if (name.length === 0 || equals(name, this.NO_NAME) || name.length > 12)
      return false;
    if (this.NAME_REGEX_0.matches_6bul2c$(name))
      return false;
    return true;
  };
  Kid$Companion.prototype.CheckAge_za3lpa$ = function (age) {
    return 1 <= age && age <= 7;
  };
  Kid$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Kid$Companion_instance = null;
  function Kid$Companion_getInstance() {
    if (Kid$Companion_instance === null) {
      new Kid$Companion();
    }
    return Kid$Companion_instance;
  }
  Kid.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Kid',
    interfaces: []
  };
  function Kid_init($this) {
    $this = $this || Object.create(Kid.prototype);
    Kid.call($this, _.net.kidjo.common.models.Kid.Companion.NO_SERVER_ID, Kid$Companion_getInstance().NO_ID, Kid$Companion_getInstance().NO_NAME, 4, _.net.kidjo.common.models.User.Companion.NO_SERVER_ID);
    return $this;
  }
  Kid.prototype.component1 = function () {
    return this.serverId;
  };
  Kid.prototype.component2 = function () {
    return this.userId;
  };
  Kid.prototype.component3 = function () {
    return this.name;
  };
  Kid.prototype.component4 = function () {
    return this.age;
  };
  Kid.prototype.component5 = function () {
    return this.serverParentAccountId;
  };
  Kid.prototype.copy_m7jug2$ = function (serverId, userId, name, age, serverParentAccountId) {
    return new Kid(serverId === void 0 ? this.serverId : serverId, userId === void 0 ? this.userId : userId, name === void 0 ? this.name : name, age === void 0 ? this.age : age, serverParentAccountId === void 0 ? this.serverParentAccountId : serverParentAccountId);
  };
  Kid.prototype.toString = function () {
    return 'Kid(serverId=' + Kotlin.toString(this.serverId) + (', userId=' + Kotlin.toString(this.userId)) + (', name=' + Kotlin.toString(this.name)) + (', age=' + Kotlin.toString(this.age)) + (', serverParentAccountId=' + Kotlin.toString(this.serverParentAccountId)) + ')';
  };
  Kid.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.serverId) | 0;
    result = result * 31 + Kotlin.hashCode(this.userId) | 0;
    result = result * 31 + Kotlin.hashCode(this.name) | 0;
    result = result * 31 + Kotlin.hashCode(this.age) | 0;
    result = result * 31 + Kotlin.hashCode(this.serverParentAccountId) | 0;
    return result;
  };
  Kid.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.serverId, other.serverId) && Kotlin.equals(this.userId, other.userId) && Kotlin.equals(this.name, other.name) && Kotlin.equals(this.age, other.age) && Kotlin.equals(this.serverParentAccountId, other.serverParentAccountId)))));
  };
  function Language(name, ordinal, id, shortName, nativeName) {
    Enum.call(this);
    this.id = id;
    this.shortName = shortName;
    this.nativeName = nativeName;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Language_initFields() {
    Language_initFields = function () {
    };
    Language$ENGLISH_instance = new Language('ENGLISH', 0, 1, Language$Companion_getInstance().SHORT_ENGLISH, Language$Companion_getInstance().NATIVE_ENGLISH);
    Language$FRENCH_instance = new Language('FRENCH', 1, 34, Language$Companion_getInstance().SHORT_FRENCH, Language$Companion_getInstance().NATIVE_FRENCH);
    Language$SPANISH_instance = new Language('SPANISH', 2, 27, Language$Companion_getInstance().SHORT_SPANISH, Language$Companion_getInstance().NATIVE_SPANISH);
    Language$PORTUGUESE_instance = new Language('PORTUGUESE', 3, 89, Language$Companion_getInstance().SHORT_PORTUGUESE, Language$Companion_getInstance().NATIVE_PORTUGUESE);
    Language$Companion_getInstance();
  }
  var Language$ENGLISH_instance;
  function Language$ENGLISH_getInstance() {
    Language_initFields();
    return Language$ENGLISH_instance;
  }
  var Language$FRENCH_instance;
  function Language$FRENCH_getInstance() {
    Language_initFields();
    return Language$FRENCH_instance;
  }
  var Language$SPANISH_instance;
  function Language$SPANISH_getInstance() {
    Language_initFields();
    return Language$SPANISH_instance;
  }
  var Language$PORTUGUESE_instance;
  function Language$PORTUGUESE_getInstance() {
    Language_initFields();
    return Language$PORTUGUESE_instance;
  }
  function Language$Companion() {
    Language$Companion_instance = this;
    this.NO_ID = -1;
    this.ID_ENGLISH = 1;
    this.SHORT_ENGLISH = 'en';
    this.NATIVE_ENGLISH = 'English';
    this.ID_FRENCH = 34;
    this.SHORT_FRENCH = 'fr';
    this.NATIVE_FRENCH = 'Fran\xE7ais';
    this.ID_SPANISH = 27;
    this.SHORT_SPANISH = 'es';
    this.NATIVE_SPANISH = 'Espa\xF1ol';
    this.ID_PORTUGUESE = 89;
    this.SHORT_PORTUGUESE = 'pt';
    this.NATIVE_PORTUGUESE = 'Portugu\xEAs';
  }
  Language$Companion.prototype.FromId_za3lpa$ = function (id) {
    var tmp$;
    if (id === Language$ENGLISH_getInstance().id)
      tmp$ = Language$ENGLISH_getInstance();
    else if (id === Language$FRENCH_getInstance().id)
      tmp$ = Language$FRENCH_getInstance();
    else if (id === Language$SPANISH_getInstance().id)
      tmp$ = Language$SPANISH_getInstance();
    else if (id === Language$PORTUGUESE_getInstance().id)
      tmp$ = Language$PORTUGUESE_getInstance();
    else
      tmp$ = Language$ENGLISH_getInstance();
    return tmp$;
  };
  Language$Companion.prototype.FromShortName_61zpoe$ = function (shortName) {
    var tmp$;
    switch (shortName) {
      case 'en':
        tmp$ = Language$ENGLISH_getInstance();
        break;
      case 'fr':
        tmp$ = Language$FRENCH_getInstance();
        break;
      case 'es':
        tmp$ = Language$SPANISH_getInstance();
        break;
      case 'pt':
        tmp$ = Language$PORTUGUESE_getInstance();
        break;
      default:tmp$ = null;
        break;
    }
    return tmp$;
  };
  Language$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Language$Companion_instance = null;
  function Language$Companion_getInstance() {
    Language_initFields();
    if (Language$Companion_instance === null) {
      new Language$Companion();
    }
    return Language$Companion_instance;
  }
  Language.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Language',
    interfaces: [Enum]
  };
  function Language$values() {
    return [Language$ENGLISH_getInstance(), Language$FRENCH_getInstance(), Language$SPANISH_getInstance(), Language$PORTUGUESE_getInstance()];
  }
  Language.values = Language$values;
  function Language$valueOf(name) {
    switch (name) {
      case 'ENGLISH':
        return Language$ENGLISH_getInstance();
      case 'FRENCH':
        return Language$FRENCH_getInstance();
      case 'SPANISH':
        return Language$SPANISH_getInstance();
      case 'PORTUGUESE':
        return Language$PORTUGUESE_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.Language.' + name);
    }
  }
  Language.valueOf_61zpoe$ = Language$valueOf;
  function User() {
    User$Companion_getInstance();
    this.id = null;
    this.isSubscribed = false;
    this.email = null;
    this.emailIsConfirmed = false;
    this.name = null;
    this.hashedPassword = 'NONE';
    this.brainTreeId = '';
    this.isSignedIn = false;
    this.authId = '';
    this.authToken = '';
    this.authType = User$AuthType$EMAIL_getInstance();
    this.oAuthType = User$OAuthType$NONE_getInstance();
  }
  User.prototype.copy_bepr4$ = function (user) {
    this.id = user.id;
    this.isSubscribed = user.isSubscribed;
    this.email = user.email;
    this.emailIsConfirmed = user.emailIsConfirmed;
    this.name = user.name;
    this.hashedPassword = user.hashedPassword;
    this.brainTreeId = user.brainTreeId;
    this.isSignedIn = user.isSignedIn;
    this.authId = user.authId;
    this.authToken = user.authToken;
    this.authType = user.authType;
    this.oAuthType = user.oAuthType;
  };
  function User$AuthType(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function User$AuthType_initFields() {
    User$AuthType_initFields = function () {
    };
    User$AuthType$MIXED_instance = new User$AuthType('MIXED', 0, 'mixed');
    User$AuthType$EMAIL_instance = new User$AuthType('EMAIL', 1, 'email');
    User$AuthType$OAUTH_instance = new User$AuthType('OAUTH', 2, 'OAUTH');
    User$AuthType$Companion_getInstance();
  }
  var User$AuthType$MIXED_instance;
  function User$AuthType$MIXED_getInstance() {
    User$AuthType_initFields();
    return User$AuthType$MIXED_instance;
  }
  var User$AuthType$EMAIL_instance;
  function User$AuthType$EMAIL_getInstance() {
    User$AuthType_initFields();
    return User$AuthType$EMAIL_instance;
  }
  var User$AuthType$OAUTH_instance;
  function User$AuthType$OAUTH_getInstance() {
    User$AuthType_initFields();
    return User$AuthType$OAUTH_instance;
  }
  function User$AuthType$Companion() {
    User$AuthType$Companion_instance = this;
  }
  User$AuthType$Companion.prototype.FromRaw_61zpoe$ = function (raw) {
    var tmp$;
    if (equals(raw, User$AuthType$MIXED_getInstance().raw))
      tmp$ = User$AuthType$MIXED_getInstance();
    else if (equals(raw, User$AuthType$EMAIL_getInstance().raw))
      tmp$ = User$AuthType$EMAIL_getInstance();
    else if (equals(raw, User$AuthType$OAUTH_getInstance().raw))
      tmp$ = User$AuthType$OAUTH_getInstance();
    else
      tmp$ = User$AuthType$EMAIL_getInstance();
    return tmp$;
  };
  User$AuthType$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var User$AuthType$Companion_instance = null;
  function User$AuthType$Companion_getInstance() {
    User$AuthType_initFields();
    if (User$AuthType$Companion_instance === null) {
      new User$AuthType$Companion();
    }
    return User$AuthType$Companion_instance;
  }
  User$AuthType.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'AuthType',
    interfaces: [Enum]
  };
  function User$AuthType$values() {
    return [User$AuthType$MIXED_getInstance(), User$AuthType$EMAIL_getInstance(), User$AuthType$OAUTH_getInstance()];
  }
  User$AuthType.values = User$AuthType$values;
  function User$AuthType$valueOf(name) {
    switch (name) {
      case 'MIXED':
        return User$AuthType$MIXED_getInstance();
      case 'EMAIL':
        return User$AuthType$EMAIL_getInstance();
      case 'OAUTH':
        return User$AuthType$OAUTH_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.User.AuthType.' + name);
    }
  }
  User$AuthType.valueOf_61zpoe$ = User$AuthType$valueOf;
  function User$OAuthType(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function User$OAuthType_initFields() {
    User$OAuthType_initFields = function () {
    };
    User$OAuthType$NONE_instance = new User$OAuthType('NONE', 0, 'none');
    User$OAuthType$GOOGLE_instance = new User$OAuthType('GOOGLE', 1, 'google');
    User$OAuthType$FACEBOOK_instance = new User$OAuthType('FACEBOOK', 2, 'facebook');
    User$OAuthType$Companion_getInstance();
  }
  var User$OAuthType$NONE_instance;
  function User$OAuthType$NONE_getInstance() {
    User$OAuthType_initFields();
    return User$OAuthType$NONE_instance;
  }
  var User$OAuthType$GOOGLE_instance;
  function User$OAuthType$GOOGLE_getInstance() {
    User$OAuthType_initFields();
    return User$OAuthType$GOOGLE_instance;
  }
  var User$OAuthType$FACEBOOK_instance;
  function User$OAuthType$FACEBOOK_getInstance() {
    User$OAuthType_initFields();
    return User$OAuthType$FACEBOOK_instance;
  }
  function User$OAuthType$Companion() {
    User$OAuthType$Companion_instance = this;
  }
  User$OAuthType$Companion.prototype.FromRaw_61zpoe$ = function (raw) {
    var tmp$;
    if (equals(raw, User$OAuthType$NONE_getInstance().raw))
      tmp$ = User$OAuthType$NONE_getInstance();
    else if (equals(raw, User$OAuthType$GOOGLE_getInstance().raw))
      tmp$ = User$OAuthType$GOOGLE_getInstance();
    else if (equals(raw, User$OAuthType$FACEBOOK_getInstance().raw))
      tmp$ = User$OAuthType$FACEBOOK_getInstance();
    else
      tmp$ = User$OAuthType$NONE_getInstance();
    return tmp$;
  };
  User$OAuthType$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var User$OAuthType$Companion_instance = null;
  function User$OAuthType$Companion_getInstance() {
    User$OAuthType_initFields();
    if (User$OAuthType$Companion_instance === null) {
      new User$OAuthType$Companion();
    }
    return User$OAuthType$Companion_instance;
  }
  User$OAuthType.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'OAuthType',
    interfaces: [Enum]
  };
  function User$OAuthType$values() {
    return [User$OAuthType$NONE_getInstance(), User$OAuthType$GOOGLE_getInstance(), User$OAuthType$FACEBOOK_getInstance()];
  }
  User$OAuthType.values = User$OAuthType$values;
  function User$OAuthType$valueOf(name) {
    switch (name) {
      case 'NONE':
        return User$OAuthType$NONE_getInstance();
      case 'GOOGLE':
        return User$OAuthType$GOOGLE_getInstance();
      case 'FACEBOOK':
        return User$OAuthType$FACEBOOK_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.User.OAuthType.' + name);
    }
  }
  User$OAuthType.valueOf_61zpoe$ = User$OAuthType$valueOf;
  function User$Companion() {
    User$Companion_instance = this;
    this.NO_ID = '0';
    this.NO_SERVER_ID = L0;
  }
  User$Companion.prototype.NameIsValid_61zpoe$ = function (name) {
    return true;
  };
  User$Companion.prototype.GetEmptyUser = function () {
    return User_init(User$Companion_getInstance().NO_ID, '', false, '', '');
  };
  User$Companion.prototype.GetId_61zpoe$ = function (id) {
    try {
      return toLong(id);
    }
     catch (e) {
      if (Kotlin.isType(e, Exception)) {
        return _.net.kidjo.common.models.User.Companion.NO_SERVER_ID;
      }
       else
        throw e;
    }
  };
  User$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var User$Companion_instance = null;
  function User$Companion_getInstance() {
    if (User$Companion_instance === null) {
      new User$Companion();
    }
    return User$Companion_instance;
  }
  User.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'User',
    interfaces: []
  };
  function User_init(id, email, emailIsConfirmed, name, hashedPassword, $this) {
    $this = $this || Object.create(User.prototype);
    User.call($this);
    $this.id = id;
    $this.email = email;
    $this.emailIsConfirmed = emailIsConfirmed;
    $this.name = name;
    $this.hashedPassword = hashedPassword;
    return $this;
  }
  function UserConfig(imageBucket, baseAssetUrl, supportsDASH, supportsHLS, supportsWebp, mobileSafari, shouldRequestAgeBeforeGoingToSettings, apiSecret, platform) {
    UserConfig$Companion_getInstance();
    this.imageBucket = imageBucket;
    this.baseAssetUrl = baseAssetUrl;
    this.supportsDASH = supportsDASH;
    this.supportsHLS = supportsHLS;
    this.supportsWebp = supportsWebp;
    this.mobileSafari = mobileSafari;
    this.shouldRequestAgeBeforeGoingToSettings = shouldRequestAgeBeforeGoingToSettings;
    this.apiSecret = apiSecret;
    this.platform = platform;
  }
  UserConfig.prototype.getImageAssetUrl_61zpoe$ = function (imageName) {
    return this.getBucketAssetUrl() + imageName;
  };
  UserConfig.prototype.getBucketAssetUrl = function () {
    return this.baseAssetUrl + 'images/' + this.imageBucket;
  };
  UserConfig.prototype.getBucketAssetUrlWithCDNUrl_puj7f4$ = function (cdnUrl, imageName) {
    return cdnUrl + this.imageBucket + imageName;
  };
  UserConfig.prototype.getVideoSrcUrl_9u1k3q$ = function (video) {
    return '';
  };
  function UserConfig$Companion() {
    UserConfig$Companion_instance = this;
  }
  UserConfig$Companion.prototype.Create_roax3m$ = function (scale, width, height, supportsDASH, supportsHLS, mobileSafari, supportsWebp, shouldRequestAgeBeforeGoingToSettings, apiSecret, platform) {
    var actualWidth = width * scale;
    var actualHeight = height * scale;
    var aspectRatio;
    var shortestLength;
    if (width > height) {
      aspectRatio = width / height;
      shortestLength = actualHeight;
    }
     else {
      aspectRatio = height / width;
      shortestLength = actualWidth;
    }
    var useTablet = aspectRatio < 1.6;
    var device;
    var size;
    if (useTablet) {
      device = 'tablet';
      if (shortestLength < 768)
        size = 's';
      else if (shortestLength < 1536)
        size = 'm';
      else
        size = 'l';
    }
     else {
      device = 'phone';
      if (shortestLength < 640)
        size = 's';
      else if (shortestLength < 1440)
        size = 'm';
      else
        size = 'l';
    }
    return new UserConfig(device + '-' + size + '/', 'https://d3aod987c9rl70.cloudfront.net/webApp/', supportsDASH, supportsHLS, supportsWebp, mobileSafari, shouldRequestAgeBeforeGoingToSettings, apiSecret, platform);
  };
  UserConfig$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var UserConfig$Companion_instance = null;
  function UserConfig$Companion_getInstance() {
    if (UserConfig$Companion_instance === null) {
      new UserConfig$Companion();
    }
    return UserConfig$Companion_instance;
  }
  function UserConfig$Platform(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function UserConfig$Platform_initFields() {
    UserConfig$Platform_initFields = function () {
    };
    UserConfig$Platform$KIDJO_BRAINTREE_instance = new UserConfig$Platform('KIDJO_BRAINTREE', 0, 'kidjo');
    UserConfig$Platform$IOS_instance = new UserConfig$Platform('IOS', 1, 'ios');
    UserConfig$Platform$PLAYSTORE_instance = new UserConfig$Platform('PLAYSTORE', 2, 'playstore');
    UserConfig$Platform$SAMSUNG_instance = new UserConfig$Platform('SAMSUNG', 3, 'samsung');
    UserConfig$Platform$AMAZON_instance = new UserConfig$Platform('AMAZON', 4, 'amazon');
    UserConfig$Platform$Companion_getInstance();
  }
  var UserConfig$Platform$KIDJO_BRAINTREE_instance;
  function UserConfig$Platform$KIDJO_BRAINTREE_getInstance() {
    UserConfig$Platform_initFields();
    return UserConfig$Platform$KIDJO_BRAINTREE_instance;
  }
  var UserConfig$Platform$IOS_instance;
  function UserConfig$Platform$IOS_getInstance() {
    UserConfig$Platform_initFields();
    return UserConfig$Platform$IOS_instance;
  }
  var UserConfig$Platform$PLAYSTORE_instance;
  function UserConfig$Platform$PLAYSTORE_getInstance() {
    UserConfig$Platform_initFields();
    return UserConfig$Platform$PLAYSTORE_instance;
  }
  var UserConfig$Platform$SAMSUNG_instance;
  function UserConfig$Platform$SAMSUNG_getInstance() {
    UserConfig$Platform_initFields();
    return UserConfig$Platform$SAMSUNG_instance;
  }
  var UserConfig$Platform$AMAZON_instance;
  function UserConfig$Platform$AMAZON_getInstance() {
    UserConfig$Platform_initFields();
    return UserConfig$Platform$AMAZON_instance;
  }
  UserConfig$Platform.prototype.platformDoesNotRequireUserId = function () {
    var tmp$;
    switch (this.name) {
      case 'IOS':
      case 'PLAYSTORE':
      case 'SAMSUNG':
      case 'AMAZON':
        tmp$ = true;
        break;
      case 'KIDJO_BRAINTREE':
        tmp$ = false;
        break;
      default:tmp$ = Kotlin.noWhenBranchMatched();
        break;
    }
    return tmp$;
  };
  function UserConfig$Platform$Companion() {
    UserConfig$Platform$Companion_instance = this;
  }
  UserConfig$Platform$Companion.prototype.FromString_61zpoe$ = function (raw) {
    var tmp$;
    var lowerCaseRaw = raw.toLowerCase();
    switch (lowerCaseRaw) {
      case 'apple':
      case 'ios':
        tmp$ = UserConfig$Platform$IOS_getInstance();
        break;
      case 'android':
      case 'playstore':
        tmp$ = UserConfig$Platform$PLAYSTORE_getInstance();
        break;
      case 'kidjo':
        tmp$ = UserConfig$Platform$KIDJO_BRAINTREE_getInstance();
        break;
      case 'samsung':
        tmp$ = UserConfig$Platform$SAMSUNG_getInstance();
        break;
      case 'amazon':
        tmp$ = UserConfig$Platform$AMAZON_getInstance();
        break;
      default:tmp$ = UserConfig$Platform$KIDJO_BRAINTREE_getInstance();
        break;
    }
    return tmp$;
  };
  UserConfig$Platform$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var UserConfig$Platform$Companion_instance = null;
  function UserConfig$Platform$Companion_getInstance() {
    UserConfig$Platform_initFields();
    if (UserConfig$Platform$Companion_instance === null) {
      new UserConfig$Platform$Companion();
    }
    return UserConfig$Platform$Companion_instance;
  }
  UserConfig$Platform.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Platform',
    interfaces: [Enum]
  };
  function UserConfig$Platform$values() {
    return [UserConfig$Platform$KIDJO_BRAINTREE_getInstance(), UserConfig$Platform$IOS_getInstance(), UserConfig$Platform$PLAYSTORE_getInstance(), UserConfig$Platform$SAMSUNG_getInstance(), UserConfig$Platform$AMAZON_getInstance()];
  }
  UserConfig$Platform.values = UserConfig$Platform$values;
  function UserConfig$Platform$valueOf(name) {
    switch (name) {
      case 'KIDJO_BRAINTREE':
        return UserConfig$Platform$KIDJO_BRAINTREE_getInstance();
      case 'IOS':
        return UserConfig$Platform$IOS_getInstance();
      case 'PLAYSTORE':
        return UserConfig$Platform$PLAYSTORE_getInstance();
      case 'SAMSUNG':
        return UserConfig$Platform$SAMSUNG_getInstance();
      case 'AMAZON':
        return UserConfig$Platform$AMAZON_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.UserConfig.Platform.' + name);
    }
  }
  UserConfig$Platform.valueOf_61zpoe$ = UserConfig$Platform$valueOf;
  UserConfig.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'UserConfig',
    interfaces: []
  };
  UserConfig.prototype.component1 = function () {
    return this.imageBucket;
  };
  UserConfig.prototype.component2 = function () {
    return this.baseAssetUrl;
  };
  UserConfig.prototype.component3 = function () {
    return this.supportsDASH;
  };
  UserConfig.prototype.component4 = function () {
    return this.supportsHLS;
  };
  UserConfig.prototype.component5 = function () {
    return this.supportsWebp;
  };
  UserConfig.prototype.component6 = function () {
    return this.mobileSafari;
  };
  UserConfig.prototype.component7 = function () {
    return this.shouldRequestAgeBeforeGoingToSettings;
  };
  UserConfig.prototype.component8 = function () {
    return this.apiSecret;
  };
  UserConfig.prototype.component9 = function () {
    return this.platform;
  };
  UserConfig.prototype.copy_wmgt24$ = function (imageBucket, baseAssetUrl, supportsDASH, supportsHLS, supportsWebp, mobileSafari, shouldRequestAgeBeforeGoingToSettings, apiSecret, platform) {
    return new UserConfig(imageBucket === void 0 ? this.imageBucket : imageBucket, baseAssetUrl === void 0 ? this.baseAssetUrl : baseAssetUrl, supportsDASH === void 0 ? this.supportsDASH : supportsDASH, supportsHLS === void 0 ? this.supportsHLS : supportsHLS, supportsWebp === void 0 ? this.supportsWebp : supportsWebp, mobileSafari === void 0 ? this.mobileSafari : mobileSafari, shouldRequestAgeBeforeGoingToSettings === void 0 ? this.shouldRequestAgeBeforeGoingToSettings : shouldRequestAgeBeforeGoingToSettings, apiSecret === void 0 ? this.apiSecret : apiSecret, platform === void 0 ? this.platform : platform);
  };
  UserConfig.prototype.toString = function () {
    return 'UserConfig(imageBucket=' + Kotlin.toString(this.imageBucket) + (', baseAssetUrl=' + Kotlin.toString(this.baseAssetUrl)) + (', supportsDASH=' + Kotlin.toString(this.supportsDASH)) + (', supportsHLS=' + Kotlin.toString(this.supportsHLS)) + (', supportsWebp=' + Kotlin.toString(this.supportsWebp)) + (', mobileSafari=' + Kotlin.toString(this.mobileSafari)) + (', shouldRequestAgeBeforeGoingToSettings=' + Kotlin.toString(this.shouldRequestAgeBeforeGoingToSettings)) + (', apiSecret=' + Kotlin.toString(this.apiSecret)) + (', platform=' + Kotlin.toString(this.platform)) + ')';
  };
  UserConfig.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.imageBucket) | 0;
    result = result * 31 + Kotlin.hashCode(this.baseAssetUrl) | 0;
    result = result * 31 + Kotlin.hashCode(this.supportsDASH) | 0;
    result = result * 31 + Kotlin.hashCode(this.supportsHLS) | 0;
    result = result * 31 + Kotlin.hashCode(this.supportsWebp) | 0;
    result = result * 31 + Kotlin.hashCode(this.mobileSafari) | 0;
    result = result * 31 + Kotlin.hashCode(this.shouldRequestAgeBeforeGoingToSettings) | 0;
    result = result * 31 + Kotlin.hashCode(this.apiSecret) | 0;
    result = result * 31 + Kotlin.hashCode(this.platform) | 0;
    return result;
  };
  UserConfig.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.imageBucket, other.imageBucket) && Kotlin.equals(this.baseAssetUrl, other.baseAssetUrl) && Kotlin.equals(this.supportsDASH, other.supportsDASH) && Kotlin.equals(this.supportsHLS, other.supportsHLS) && Kotlin.equals(this.supportsWebp, other.supportsWebp) && Kotlin.equals(this.mobileSafari, other.mobileSafari) && Kotlin.equals(this.shouldRequestAgeBeforeGoingToSettings, other.shouldRequestAgeBeforeGoingToSettings) && Kotlin.equals(this.apiSecret, other.apiSecret) && Kotlin.equals(this.platform, other.platform)))));
  };
  function Video(videoId, serverId, userId, title, ageMin, ageMax, isPremium, durationInSeconds, compilation, storedTime, formats, formatSizes) {
    Video$Companion_getInstance();
    this.videoId = videoId;
    this.serverId = serverId;
    this.userId_294b7w$_0 = userId;
    this.title = title;
    this.ageMin = ageMin;
    this.ageMax = ageMax;
    this.isPremium = isPremium;
    this.durationInSeconds = durationInSeconds;
    this.compilation = compilation;
    this.storedTime_suyhxe$_0 = storedTime;
    this.formats = formats;
    this.formatSizes = formatSizes;
    this.type_yq9i6w$_0 = BackpackItem$Type$VIDEO_getInstance();
  }
  Object.defineProperty(Video.prototype, 'userId', {
    get: function () {
      return this.userId_294b7w$_0;
    }
  });
  Object.defineProperty(Video.prototype, 'storedTime', {
    get: function () {
      return this.storedTime_suyhxe$_0;
    },
    set: function (storedTime) {
      this.storedTime_suyhxe$_0 = storedTime;
    }
  });
  Object.defineProperty(Video.prototype, 'type', {
    get: function () {
      return this.type_yq9i6w$_0;
    }
  });
  Video.prototype.getBestFormat_empd58$ = function (height, userConfig) {
    var currentBestFormat = {v: Video$Format$NONE_getInstance()};
    var $receiver = this.formats;
    var tmp$;
    for (tmp$ = 0; tmp$ !== $receiver.length; ++tmp$) {
      var element = $receiver[tmp$];
      var isOk = element !== Video$Format$NONE_getInstance();
      if (isOk && element.isDash())
        isOk = userConfig.supportsDASH;
      if (isOk && element.isHls())
        isOk = userConfig.supportsHLS;
      if (isOk) {
        if (currentBestFormat.v === Video$Format$NONE_getInstance())
          currentBestFormat.v = element;
        else {
          var heightDiffForCurrent = abs(height - currentBestFormat.v.height | 0);
          var heightDiffForThisOne = abs(height - element.height | 0);
          if (heightDiffForThisOne < heightDiffForCurrent || (!currentBestFormat.v.isAdaptiveStreaming() && element.isAdaptiveStreaming()) || (currentBestFormat.v.isAdaptiveStreaming() && currentBestFormat.v.isHls() && element.isDash() && heightDiffForThisOne === heightDiffForCurrent)) {
            currentBestFormat.v = element;
          }
        }
      }
    }
    return currentBestFormat.v;
  };
  Video.prototype.getBestVideoUrlString_empd58$ = function (height, userConfig) {
    println(userConfig);
    return Video$Format$Companion_getInstance().GetVideoUrlWithFormat_8n7phv$(this.userId, this.getBestFormat_empd58$(height, userConfig));
  };
  Video.prototype.getTimeInMinutesForDisplay = function () {
    if (this.durationInSeconds <= 0) {
      return -1;
    }
    var minutes = this.durationInSeconds / 60.0;
    var minsInInt = numberToInt(round(minutes));
    if (minsInInt <= 0) {
      return 1;
    }
    return minsInInt;
  };
  function Video$Companion() {
    Video$Companion_instance = this;
    this.NO_SERVER_ID = L0;
  }
  Video$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Video$Companion_instance = null;
  function Video$Companion_getInstance() {
    if (Video$Companion_instance === null) {
      new Video$Companion();
    }
    return Video$Companion_instance;
  }
  function Video$Format(name, ordinal, id, height, codec, delivery, extension, bitRate) {
    Enum.call(this);
    this.id = id;
    this.height = height;
    this.codec = codec;
    this.delivery = delivery;
    this.extension = extension;
    this.bitRate = bitRate;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Video$Format_initFields() {
    Video$Format_initFields = function () {
    };
    Video$Format$NONE_instance = new Video$Format('NONE', 0, 0, 0, '', '', '', 0);
    Video$Format$HLS_720_instance = new Video$Format('HLS_720', 1, 1, 720, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_HLS, Video$Format$Companion_getInstance().EXTENSION_HLS, 0);
    Video$Format$DASH_720_instance = new Video$Format('DASH_720', 2, 2, 720, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_DASH, Video$Format$Companion_getInstance().EXTENSION_DASH, 0);
    Video$Format$MP4_720_instance = new Video$Format('MP4_720', 3, 3, 720, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_MP4, Video$Format$Companion_getInstance().EXTENSION_MP4, 2000);
    Video$Format$HLS_480_instance = new Video$Format('HLS_480', 4, 4, 480, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_HLS, Video$Format$Companion_getInstance().EXTENSION_HLS, 0);
    Video$Format$DASH_480_instance = new Video$Format('DASH_480', 5, 5, 480, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_DASH, Video$Format$Companion_getInstance().EXTENSION_DASH, 0);
    Video$Format$MP4_480_instance = new Video$Format('MP4_480', 6, 6, 480, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_MP4, Video$Format$Companion_getInstance().EXTENSION_MP4, 1000);
    Video$Format$MP4_360_instance = new Video$Format('MP4_360', 7, 7, 360, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_MP4, Video$Format$Companion_getInstance().EXTENSION_MP4, 500);
    Video$Format$MP4_240_instance = new Video$Format('MP4_240', 8, 8, 240, Video$Format$Companion_getInstance().FORMAT_TYPE_H264, Video$Format$Companion_getInstance().STREAMING_TYPE_MP4, Video$Format$Companion_getInstance().EXTENSION_MP4, 192);
    Video$Format$Companion_getInstance();
  }
  var Video$Format$NONE_instance;
  function Video$Format$NONE_getInstance() {
    Video$Format_initFields();
    return Video$Format$NONE_instance;
  }
  var Video$Format$HLS_720_instance;
  function Video$Format$HLS_720_getInstance() {
    Video$Format_initFields();
    return Video$Format$HLS_720_instance;
  }
  var Video$Format$DASH_720_instance;
  function Video$Format$DASH_720_getInstance() {
    Video$Format_initFields();
    return Video$Format$DASH_720_instance;
  }
  var Video$Format$MP4_720_instance;
  function Video$Format$MP4_720_getInstance() {
    Video$Format_initFields();
    return Video$Format$MP4_720_instance;
  }
  var Video$Format$HLS_480_instance;
  function Video$Format$HLS_480_getInstance() {
    Video$Format_initFields();
    return Video$Format$HLS_480_instance;
  }
  var Video$Format$DASH_480_instance;
  function Video$Format$DASH_480_getInstance() {
    Video$Format_initFields();
    return Video$Format$DASH_480_instance;
  }
  var Video$Format$MP4_480_instance;
  function Video$Format$MP4_480_getInstance() {
    Video$Format_initFields();
    return Video$Format$MP4_480_instance;
  }
  var Video$Format$MP4_360_instance;
  function Video$Format$MP4_360_getInstance() {
    Video$Format_initFields();
    return Video$Format$MP4_360_instance;
  }
  var Video$Format$MP4_240_instance;
  function Video$Format$MP4_240_getInstance() {
    Video$Format_initFields();
    return Video$Format$MP4_240_instance;
  }
  Video$Format.prototype.isDash = function () {
    return equals(this.delivery, Video$Format$Companion_getInstance().STREAMING_TYPE_DASH);
  };
  Video$Format.prototype.isHls = function () {
    return equals(this.delivery, Video$Format$Companion_getInstance().STREAMING_TYPE_HLS);
  };
  Video$Format.prototype.isAdaptiveStreaming = function () {
    return this.isDash() || this.isHls();
  };
  function Video$Format$Companion() {
    Video$Format$Companion_instance = this;
    this.FORMAT_TYPE_H264 = 'h264';
    this.FORMAT_TYPE_H265 = 'h265';
    this.STREAMING_TYPE_MP4 = 'none';
    this.STREAMING_TYPE_HLS = 'hls';
    this.STREAMING_TYPE_DASH = 'dash';
    this.EXTENSION_MP4 = 'mp4';
    this.EXTENSION_HLS = 'm3u8';
    this.EXTENSION_DASH = 'mpd';
  }
  Video$Format$Companion.prototype.GetVideoUrlWithFormat_8n7phv$ = function (videoId, format) {
    return equals(format.delivery, Video$Format$Companion_getInstance().STREAMING_TYPE_MP4) ? format.id.toString() + '/' + videoId + '.' + format.extension : format.id.toString() + '/' + videoId + '/' + videoId + '.' + format.extension;
  };
  Video$Format$Companion.prototype.FromId_za3lpa$ = function (id) {
    var tmp$;
    if (id === Video$Format$HLS_720_getInstance().id)
      tmp$ = Video$Format$HLS_720_getInstance();
    else if (id === Video$Format$DASH_720_getInstance().id)
      tmp$ = Video$Format$DASH_720_getInstance();
    else if (id === Video$Format$MP4_720_getInstance().id)
      tmp$ = Video$Format$MP4_720_getInstance();
    else if (id === Video$Format$HLS_480_getInstance().id)
      tmp$ = Video$Format$HLS_480_getInstance();
    else if (id === Video$Format$DASH_480_getInstance().id)
      tmp$ = Video$Format$DASH_480_getInstance();
    else if (id === Video$Format$MP4_480_getInstance().id)
      tmp$ = Video$Format$MP4_480_getInstance();
    else if (id === Video$Format$MP4_360_getInstance().id)
      tmp$ = Video$Format$MP4_360_getInstance();
    else if (id === Video$Format$MP4_240_getInstance().id)
      tmp$ = Video$Format$MP4_240_getInstance();
    else
      tmp$ = Video$Format$NONE_getInstance();
    return tmp$;
  };
  Video$Format$Companion.prototype.FromCodec_61zpoe$ = function (codec) {
    var tmp$;
    switch (codec) {
      case 'HLS_720':
        tmp$ = Video$Format$HLS_720_getInstance();
        break;
      case 'DASH_720':
        tmp$ = Video$Format$DASH_720_getInstance();
        break;
      case 'MP4_720':
        tmp$ = Video$Format$MP4_720_getInstance();
        break;
      case 'HLS_480':
        tmp$ = Video$Format$HLS_480_getInstance();
        break;
      case 'DASH_480':
        tmp$ = Video$Format$DASH_480_getInstance();
        break;
      case 'MP4_480':
        tmp$ = Video$Format$MP4_480_getInstance();
        break;
      case 'MP4_360':
        tmp$ = Video$Format$MP4_360_getInstance();
        break;
      case 'MP4_240':
        tmp$ = Video$Format$MP4_240_getInstance();
        break;
      default:tmp$ = Video$Format$NONE_getInstance();
        break;
    }
    return tmp$;
  };
  Video$Format$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Video$Format$Companion_instance = null;
  function Video$Format$Companion_getInstance() {
    Video$Format_initFields();
    if (Video$Format$Companion_instance === null) {
      new Video$Format$Companion();
    }
    return Video$Format$Companion_instance;
  }
  Video$Format.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Format',
    interfaces: [Enum]
  };
  function Video$Format$values() {
    return [Video$Format$NONE_getInstance(), Video$Format$HLS_720_getInstance(), Video$Format$DASH_720_getInstance(), Video$Format$MP4_720_getInstance(), Video$Format$HLS_480_getInstance(), Video$Format$DASH_480_getInstance(), Video$Format$MP4_480_getInstance(), Video$Format$MP4_360_getInstance(), Video$Format$MP4_240_getInstance()];
  }
  Video$Format.values = Video$Format$values;
  function Video$Format$valueOf(name) {
    switch (name) {
      case 'NONE':
        return Video$Format$NONE_getInstance();
      case 'HLS_720':
        return Video$Format$HLS_720_getInstance();
      case 'DASH_720':
        return Video$Format$DASH_720_getInstance();
      case 'MP4_720':
        return Video$Format$MP4_720_getInstance();
      case 'HLS_480':
        return Video$Format$HLS_480_getInstance();
      case 'DASH_480':
        return Video$Format$DASH_480_getInstance();
      case 'MP4_480':
        return Video$Format$MP4_480_getInstance();
      case 'MP4_360':
        return Video$Format$MP4_360_getInstance();
      case 'MP4_240':
        return Video$Format$MP4_240_getInstance();
      default:throwISE('No enum constant net.kidjo.common.models.Video.Format.' + name);
    }
  }
  Video$Format.valueOf_61zpoe$ = Video$Format$valueOf;
  Video.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Video',
    interfaces: [BackpackItem]
  };
  function Video_init(video, $this) {
    $this = $this || Object.create(Video.prototype);
    Video.call($this, video.videoId, video.serverId, video.userId, video.title, video.ageMin, video.ageMax, video.isPremium, video.durationInSeconds, video.compilation, video.storedTime, video.formats.slice(), copyOf(video.formatSizes));
    return $this;
  }
  function Video_init_0(videoId, userId, title, ageMin, ageMax, isPremium, duration, compilation, formats, formatSizes, $this) {
    $this = $this || Object.create(Video.prototype);
    Video.call($this, videoId, _.net.kidjo.common.models.Video.Companion.NO_SERVER_ID, userId, title, ageMin, ageMax, isPremium, duration, compilation, L0, formats, formatSizes);
    return $this;
  }
  Video.prototype.component1 = function () {
    return this.videoId;
  };
  Video.prototype.component2 = function () {
    return this.serverId;
  };
  Video.prototype.component3 = function () {
    return this.userId;
  };
  Video.prototype.component4 = function () {
    return this.title;
  };
  Video.prototype.component5 = function () {
    return this.ageMin;
  };
  Video.prototype.component6 = function () {
    return this.ageMax;
  };
  Video.prototype.component7 = function () {
    return this.isPremium;
  };
  Video.prototype.component8 = function () {
    return this.durationInSeconds;
  };
  Video.prototype.component9 = function () {
    return this.compilation;
  };
  Video.prototype.component10 = function () {
    return this.storedTime;
  };
  Video.prototype.component11 = function () {
    return this.formats;
  };
  Video.prototype.component12 = function () {
    return this.formatSizes;
  };
  Video.prototype.copy_l6dggx$ = function (videoId, serverId, userId, title, ageMin, ageMax, isPremium, durationInSeconds, compilation, storedTime, formats, formatSizes) {
    return new Video(videoId === void 0 ? this.videoId : videoId, serverId === void 0 ? this.serverId : serverId, userId === void 0 ? this.userId : userId, title === void 0 ? this.title : title, ageMin === void 0 ? this.ageMin : ageMin, ageMax === void 0 ? this.ageMax : ageMax, isPremium === void 0 ? this.isPremium : isPremium, durationInSeconds === void 0 ? this.durationInSeconds : durationInSeconds, compilation === void 0 ? this.compilation : compilation, storedTime === void 0 ? this.storedTime : storedTime, formats === void 0 ? this.formats : formats, formatSizes === void 0 ? this.formatSizes : formatSizes);
  };
  Video.prototype.toString = function () {
    return 'Video(videoId=' + Kotlin.toString(this.videoId) + (', serverId=' + Kotlin.toString(this.serverId)) + (', userId=' + Kotlin.toString(this.userId)) + (', title=' + Kotlin.toString(this.title)) + (', ageMin=' + Kotlin.toString(this.ageMin)) + (', ageMax=' + Kotlin.toString(this.ageMax)) + (', isPremium=' + Kotlin.toString(this.isPremium)) + (', durationInSeconds=' + Kotlin.toString(this.durationInSeconds)) + (', compilation=' + Kotlin.toString(this.compilation)) + (', storedTime=' + Kotlin.toString(this.storedTime)) + (', formats=' + Kotlin.toString(this.formats)) + (', formatSizes=' + Kotlin.toString(this.formatSizes)) + ')';
  };
  Video.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.videoId) | 0;
    result = result * 31 + Kotlin.hashCode(this.serverId) | 0;
    result = result * 31 + Kotlin.hashCode(this.userId) | 0;
    result = result * 31 + Kotlin.hashCode(this.title) | 0;
    result = result * 31 + Kotlin.hashCode(this.ageMin) | 0;
    result = result * 31 + Kotlin.hashCode(this.ageMax) | 0;
    result = result * 31 + Kotlin.hashCode(this.isPremium) | 0;
    result = result * 31 + Kotlin.hashCode(this.durationInSeconds) | 0;
    result = result * 31 + Kotlin.hashCode(this.compilation) | 0;
    result = result * 31 + Kotlin.hashCode(this.storedTime) | 0;
    result = result * 31 + Kotlin.hashCode(this.formats) | 0;
    result = result * 31 + Kotlin.hashCode(this.formatSizes) | 0;
    return result;
  };
  Video.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.videoId, other.videoId) && Kotlin.equals(this.serverId, other.serverId) && Kotlin.equals(this.userId, other.userId) && Kotlin.equals(this.title, other.title) && Kotlin.equals(this.ageMin, other.ageMin) && Kotlin.equals(this.ageMax, other.ageMax) && Kotlin.equals(this.isPremium, other.isPremium) && Kotlin.equals(this.durationInSeconds, other.durationInSeconds) && Kotlin.equals(this.compilation, other.compilation) && Kotlin.equals(this.storedTime, other.storedTime) && Kotlin.equals(this.formats, other.formats) && Kotlin.equals(this.formatSizes, other.formatSizes)))));
  };
  function BroadcastReceiverListener() {
    BroadcastReceiverListener$Companion_getInstance();
  }
  function BroadcastReceiverListener$Companion() {
    BroadcastReceiverListener$Companion_instance = this;
    this.EVENT_ID_AUDIO_MUTE_SETTING_CHANGED = 'net.kidjo.b_mute';
    this.EVENT_ID_BOOT_COMPLETE = 'net.kidjo.b_complete';
    this.EVENT_ID_BOOT_ERROR = 'net.kidjo.b_error';
  }
  BroadcastReceiverListener$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var BroadcastReceiverListener$Companion_instance = null;
  function BroadcastReceiverListener$Companion_getInstance() {
    if (BroadcastReceiverListener$Companion_instance === null) {
      new BroadcastReceiverListener$Companion();
    }
    return BroadcastReceiverListener$Companion_instance;
  }
  BroadcastReceiverListener.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'BroadcastReceiverListener',
    interfaces: []
  };
  function HttpRequestType(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function HttpRequestType_initFields() {
    HttpRequestType_initFields = function () {
    };
    HttpRequestType$POST_instance = new HttpRequestType('POST', 0, 'POST');
    HttpRequestType$GET_instance = new HttpRequestType('GET', 1, 'GET');
  }
  var HttpRequestType$POST_instance;
  function HttpRequestType$POST_getInstance() {
    HttpRequestType_initFields();
    return HttpRequestType$POST_instance;
  }
  var HttpRequestType$GET_instance;
  function HttpRequestType$GET_getInstance() {
    HttpRequestType_initFields();
    return HttpRequestType$GET_instance;
  }
  HttpRequestType.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'HttpRequestType',
    interfaces: [Enum]
  };
  function HttpRequestType$values() {
    return [HttpRequestType$POST_getInstance(), HttpRequestType$GET_getInstance()];
  }
  HttpRequestType.values = HttpRequestType$values;
  function HttpRequestType$valueOf(name) {
    switch (name) {
      case 'POST':
        return HttpRequestType$POST_getInstance();
      case 'GET':
        return HttpRequestType$GET_getInstance();
      default:throwISE('No enum constant net.kidjo.common.platform.direct.HttpRequestType.' + name);
    }
  }
  HttpRequestType.valueOf_61zpoe$ = HttpRequestType$valueOf;
  function AudioLoader() {
  }
  AudioLoader.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'AudioLoader',
    interfaces: []
  };
  function AudioLoaderListener() {
  }
  AudioLoaderListener.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'AudioLoaderListener',
    interfaces: []
  };
  var APP_SECRET;
  function main$lambda(closure$application) {
    return function () {
      closure$application.appDidLoad();
      return Unit;
    };
  }
  function main(args) {
    var tmp$, tmp$_0, tmp$_1, tmp$_2, tmp$_3, tmp$_4, tmp$_5, tmp$_6, tmp$_7, tmp$_8, tmp$_9, tmp$_10, tmp$_11, tmp$_12;
    enableFullScreenPolyFill(document);
    var bootData = window['bootData'];
    var isMobileSafari;
    var supportsHLS;
    var supportsDash;
    var supportsWebp;
    var language;
    if (bootData != null) {
      isMobileSafari = (tmp$_0 = typeof (tmp$ = bootData['isMobileSafari']) === 'boolean' ? tmp$ : null) != null ? tmp$_0 : false;
      supportsHLS = (tmp$_2 = typeof (tmp$_1 = bootData['supportsHLS']) === 'boolean' ? tmp$_1 : null) != null ? tmp$_2 : true;
      supportsDash = (tmp$_4 = typeof (tmp$_3 = bootData['supportsDash']) === 'boolean' ? tmp$_3 : null) != null ? tmp$_4 : true;
      supportsWebp = (tmp$_6 = typeof (tmp$_5 = bootData['supportsWebp']) === 'boolean' ? tmp$_5 : null) != null ? tmp$_6 : false;
      language = (tmp$_9 = Language$Companion_getInstance().FromShortName_61zpoe$((tmp$_8 = typeof (tmp$_7 = bootData['language']) === 'string' ? tmp$_7 : null) != null ? tmp$_8 : '')) != null ? tmp$_9 : Language$ENGLISH_getInstance();
    }
     else {
      isMobileSafari = false;
      supportsHLS = true;
      supportsDash = true;
      supportsWebp = false;
      language = Language$ENGLISH_getInstance();
    }
    var userConfig = UserConfig$Companion_getInstance().Create_roax3m$(window.devicePixelRatio, window.innerWidth, window.innerHeight, supportsDash, supportsHLS, isMobileSafari, supportsWebp, true, APP_SECRET, UserConfig$Platform$KIDJO_BRAINTREE_getInstance());
    var deviceData = new DeviceData();
    var audioLoader = new JsAudioLoader(userConfig);
    var platformDependencyProvider = new PlatformDependencyProvider(userConfig, deviceData, audioLoader, document, localStorage);
    var dependencyContainer = CreateAppDependencyContainer(language, platformDependencyProvider);
    var boot = dependencyContainer.bootController;
    boot.localBoot_t1yf6p$(bootData);
    var appContainer = Kotlin.isType(tmp$_10 = document.querySelector('#app-container'), HTMLDivElement) ? tmp$_10 : throwCCE();
    var bootOverlay = Kotlin.isType(tmp$_11 = document.querySelector('#boot-overlay'), HTMLDivElement) ? tmp$_11 : throwCCE();
    var loadingView = Kotlin.isType(tmp$_12 = document.querySelector('#loading-container'), HTMLDivElement) ? tmp$_12 : throwCCE();
    var baseUrl = window.location.pathname;
    var background = createDiv(document);
    background.classList.add('app-background-temp');
    background.style.backgroundImage = "url('" + userConfig.getImageAssetUrl_61zpoe$('background_day_bottom.jpg') + "')";
    appContainer.appendChild(background);
    var application = new Application(baseUrl, language.shortName, bootData, appContainer, loadingView, dependencyContainer);
    var bootListener = new BootListener(bootOverlay, dependencyContainer.platformDependencyProvider.broadcaster, dependencyContainer.bootController, main$lambda(application));
    boot.networkBoot_za3lpa$((new Date()).getTimezoneOffset());
    if (!equals(window.navigator.serviceWorker, undefined)) {
      window.navigator.serviceWorker.register('/app/service_worker.js');
    }
  }
  function BootListener(splashScreen, broadcaster, bootController, bootComplete) {
    this.splashScreen_0 = splashScreen;
    this.broadcaster_0 = broadcaster;
    this.bootController_0 = bootController;
    this.bootComplete_0 = bootComplete;
    this.bootSuccess_0 = new BroadcastReceiverJs(BroadcastReceiverListener$Companion_getInstance().EVENT_ID_BOOT_COMPLETE, this);
    this.bootError_0 = new BroadcastReceiverJs(BroadcastReceiverListener$Companion_getInstance().EVENT_ID_BOOT_ERROR, this);
    this.bootSuccess_0.register_5oryg$(this.broadcaster_0);
    this.bootError_0.register_5oryg$(this.broadcaster_0);
  }
  function BootListener$broadcastReceiverGotBroadcast$lambda(this$BootListener) {
    return function () {
      this$BootListener.bootController_0.retry();
      return Unit;
    };
  }
  BootListener.prototype.broadcastReceiverGotBroadcast_61zpoe$ = function (id) {
    if (equals(id, BroadcastReceiverListener$Companion_getInstance().EVENT_ID_BOOT_COMPLETE)) {
      this.bootSuccess_0.unregister();
      this.bootError_0.unregister();
      this.bootComplete_0();
    }
     else {
      SetGenericTimeout(L1000, BootListener$broadcastReceiverGotBroadcast$lambda(this));
    }
  };
  BootListener.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'BootListener',
    interfaces: [BroadcastReceiverListener]
  };
  function Application(startingUrl, browserLanguageRequest, bootData, rootView, loadingCard, dependencyContainer) {
    this.bootData_0 = bootData;
    this.rootView_0 = rootView;
    this.loadingCard_0 = loadingCard;
    this.dependencyContainer_0 = dependencyContainer;
    var tmp$;
    this.rootHtml_0 = Kotlin.isType(tmp$ = document.getElementsByTagName('html')[0], HTMLElement) ? tmp$ : throwCCE();
    this.jsonReader_0 = this.dependencyContainer_0.jsonReader;
    this.jsonFactoryV3_0 = this.dependencyContainer_0.jsonFactoryV3;
    this.userConfig_0 = this.dependencyContainer_0.platformDependencyProvider.userConfig;
    this.deviceData_0 = this.dependencyContainer_0.platformDependencyProvider.deviceData;
    this.platformDependencyProvider_0 = this.dependencyContainer_0.platformDependencyProvider;
    this.smallStorageController_0 = this.dependencyContainer_0.smallStorageController;
    this.api_0 = this.dependencyContainer_0.api;
    this.settingsController_0 = this.dependencyContainer_0.settingsController;
    this.audioController_0 = this.dependencyContainer_0.audioController;
    this.userController_0 = this.dependencyContainer_0.userController;
    this.kidsController_0 = this.dependencyContainer_0.kidsController;
    this.favoritesController_0 = this.dependencyContainer_0.favoritesController;
    this.stringCheese_0 = StringCheeseProvider$Companion_getInstance().GetSingle_61zpoe$(browserLanguageRequest);
    this.sideBarCoordinator_0 = new SideBarCoordinator(this.stringCheese_0, this.dependencyContainer_0.platformDependencyProvider.userConfig, this.dependencyContainer_0.platformDependencyProvider.deviceData);
    this.navigationController_0 = new NavigationController(this.sideBarCoordinator_0, this.rootView_0);
    this.viewBuilder_0 = new ViewControllerBuilder(this.stringCheese_0, this, this.dependencyContainer_0);
    this.loadingIsShowing_0 = false;
    this.loadingInView_0 = false;
    this.resizeEventTimeoutId_0 = -1;
    this.favoritesIsOpen_0 = false;
    this.resizeMethod_0 = Application$resizeMethod$lambda(this);
    window.onpopstate = Application_init$lambda(this);
    window.onresize = this.resizeMethod_0;
    polyfill_visibilityChange(document, Application_init$lambda_0(this));
    polyfill_webkitfullscreenchange(document, Application_init$lambda_1(this));
    this.sideBarCoordinator_0.settingsButton.onclick = Application_init$lambda_2(this);
    this.sideBarCoordinator_0.favoritesButton.onclick = Application_init$lambda_3(this);
  }
  Application.prototype.onPopStateChange_0 = function (event) {
    var stateJson = event.state;
    var path = window.location.pathname;
    var route = Route$Companion_getInstance().Create_61zpoe$(path);
    if (this.navigationController_0.tryPoppingToRoute_fdlhz7$(route, stateJson))
      return;
    this.navigationController_0.clearStack();
    this.openRoute_ki9wh0$(route, stateJson);
  };
  Application.prototype.openRoute_qlbjqp$ = function (url, json) {
    if (json === void 0)
      json = null;
    this.openRoute_ki9wh0$(Route$Companion_getInstance().Create_61zpoe$(url), json);
  };
  function Application$openRoute$lambda(this$Application) {
    return function (vc) {
      if (vc != null)
        this$Application.navigationController_0.push_8c4kya$(vc);
      else
        this$Application.navigationController_0.push_8c4kya$(this$Application.viewBuilder_0.buildCardSelectionViewController_rn0tbd$(Route$Companion_getInstance().defaultRouteCardSelection()));
      this$Application.loadingInView_0 = false;
      this$Application.changeLoading_6taknv$(false);
      return Unit;
    };
  }
  function Application$openRoute$lambda_0(this$Application, closure$route) {
    return function (vc) {
      if (vc != null)
        this$Application.navigationController_0.push_8c4kya$(vc);
      else
        this$Application.navigationController_0.push_8c4kya$(this$Application.viewBuilder_0.buildCardSelectionViewController_rn0tbd$(closure$route));
      this$Application.loadingInView_0 = false;
      this$Application.changeLoading_6taknv$(false);
      return Unit;
    };
  }
  Application.prototype.openRoute_ki9wh0$ = function (route, json) {
    if (json === void 0)
      json = null;
    switch (route.path.name) {
      case 'CARD_SELECTION':
        this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildCardSelectionViewController_rn0tbd$(route));
        break;
      case 'GAME':
        this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildCardSelectionViewController_rn0tbd$(Route$Companion_getInstance().defaultRouteCardSelection()));
        break;
      case 'FAVORITES':
        this.openFavorites();
        break;
      case 'FOLDER':
        var vcFromState;
        console.log('JSON', json);
        if (json != null)
          vcFromState = this.viewBuilder_0.json_buildVideoSelectionViewController_wxcqwd$(json, Folder$BackgroundColor$RED_getInstance(), route);
        else
          vcFromState = null;
        if (vcFromState != null) {
          this.navigationController_0.push_8c4kya$(vcFromState);
        }
         else {
          this.loadingInView_0 = true;
          this.changeLoading_6taknv$(true);
          this.viewBuilder_0.async_buildVideoSelectionViewController_hclovw$(route, Application$openRoute$lambda(this));
        }

        break;
      case 'VIDEO':
        var vcFromState_0;
        if (json != null)
          vcFromState_0 = this.viewBuilder_0.json_buildVideoPlayer_jvytur$(json, route);
        else
          vcFromState_0 = null;
        if (vcFromState_0 != null) {
          this.navigationController_0.push_8c4kya$(vcFromState_0);
        }
         else {
          this.loadingInView_0 = true;
          this.changeLoading_6taknv$(true);
          this.viewBuilder_0.asyync_buildVideoPlayer_xpmelq$(route, Application$openRoute$lambda_0(this, route));
        }

        break;
      case 'SETTINGS':
        this.openSettings_6taknv$(false);
        break;
      case 'SUBSCRIBE':
        this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildSubscriptionViewController_rn0tbd$(Route$Companion_getInstance().defaultSubscribe()));
        break;
      default:this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildCardSelectionViewController_rn0tbd$(route));
        break;
    }
  };
  Application.prototype.appDidLoad = function () {
    this.openRoute_qlbjqp$(window.location.pathname, this.bootData_0);
    this.navigationController_0.loadTopView();
    this.refreshSideBar();
    this.changeFullScreen_6taknv$(true);
  };
  Application.prototype.handleSizingEvent_0 = function () {
    this.navigationController_0.sizingEvent_vux9f0$(window.innerWidth, window.innerHeight);
  };
  Application.prototype.changeFullScreen_6taknv$ = function (shouldEnter) {
    var tmp$;
    if (!document.fullscreenEnabled)
      return null;
    return shouldEnter ? (tmp$ = document.documentElement) != null ? polyfill_requestFullScreen(tmp$) : null : document.exitFullscreen();
  };
  Application.prototype.goBack = function () {
    window.history.go(-1);
  };
  Application.prototype.closeTopModal = function () {
    this.navigationController_0.popModal_6taknv$(true);
  };
  Application.prototype.changeLoading_6taknv$ = function (show) {
    if (show === this.loadingIsShowing_0)
      return;
    this.loadingIsShowing_0 = show;
    if (show) {
      this.loadingCard_0.classList.remove('hidden');
      this.rootView_0.classList.add('blur');
    }
     else {
      this.loadingCard_0.classList.add('hidden');
      this.rootView_0.classList.remove('blur');
    }
  };
  var Math_0 = Math;
  Application.prototype.refreshSideBar = function () {
    var a = this.dependencyContainer_0.settingsController.screenTimeLimitInMinutes - this.dependencyContainer_0.settingsController.screenTimeWatchedInMinutes | 0;
    var timeLeft = Math_0.max(a, 0);
    var percent;
    var isInfinite = SettingsController$Companion_getInstance().ScreenTimeValueIsUnlimited_za3lpa$(this.dependencyContainer_0.settingsController.screenTimeLimitInMinutes);
    if (isInfinite)
      percent = 0.0;
    else
      percent = this.dependencyContainer_0.settingsController.screenTimeWatchedInMinutes / this.dependencyContainer_0.settingsController.screenTimeLimitInMinutes;
    this.sideBarCoordinator_0.setTimer_pefdam$(timeLeft, percent, isInfinite);
  };
  Application.prototype.setSideBarFavoriteButton_6taknv$ = function (favoritesIsOpen) {
    this.favoritesIsOpen_0 = favoritesIsOpen;
    this.sideBarCoordinator_0.setFavoritesButton_6taknv$(favoritesIsOpen);
  };
  Application.prototype.openFavorites = function () {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildFavoritesViewController_rn0tbd$(Route$Companion_getInstance().defaultRouteFavorites()));
  };
  Application.prototype.openVideoSelection_mi0oz1$ = function (folder, color) {
    console.log('navigationController.push(viewBuilder.buildVideoSelectionViewController', folder, color, Route$Companion_getInstance().defaultRouteFolder_mi0oz1$(folder, color));
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildVideoSelectionViewController_62c174$(folder, color, Route$Companion_getInstance().defaultRouteFolder_mi0oz1$(folder, color)));
  };
  Application.prototype.openVideoPlayer_9u1k3q$ = function (video) {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildVideoPlayer_32j3gj$(video, Route$Companion_getInstance().defaultRouteVideoPlayer_9u1k3q$(video)));
  };
  Application.prototype.openGame_yetmo8$ = function (game, age) {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildGameView_oq631x$(game, Route$Companion_getInstance().defaultGameRoute_b5err$(game), age));
  };
  Application.prototype.openSettings_6taknv$ = function (alreadyHasPermission) {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildSettingsViewController_ivya20$(alreadyHasPermission, Route$Companion_getInstance().defaultRouteSettings()));
  };
  Application.prototype.openSubscriptionView = function () {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildSubscriptionViewController_rn0tbd$(Route$Companion_getInstance().defaultSubscribe()));
  };
  Application.prototype.openAgeGate_5q5pme$ = function (openingRoute, onComplete) {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildAgeGate_edk2z4$(onComplete, openingRoute));
  };
  Application.prototype.openOvertimeLockout_rn0tbd$ = function (openingRoute) {
    this.navigationController_0.push_8c4kya$(this.viewBuilder_0.buildLockout_rn0tbd$(openingRoute));
  };
  Application.prototype.linkToAccountLogin = function () {
    window.location.href = window.location.protocol + '//' + window.location.host + '/login?web=true';
  };
  Application.prototype.linkToAccountRegister = function () {
    window.location.href = window.location.protocol + '//' + window.location.host + '/login?web=true';
  };
  Application.prototype.linkToPrivacyPolicy = function () {
    window.location.href = this.stringCheese_0.urlPrivacy;
  };
  Application.prototype.unsubscribe = function () {
    var user = this.dependencyContainer_0.userController.user;
    this.dependencyContainer_0.api.unsubscribeUser_bepr4$(user);
  };
  function Application$resizeMethod$lambda$lambda(this$Application) {
    return function () {
      this$Application.handleSizingEvent_0();
      return Unit;
    };
  }
  function Application$resizeMethod$lambda(this$Application) {
    return function (event) {
      if (!this$Application.userConfig_0.mobileSafari)
        this$Application.handleSizingEvent_0();
      else {
        this$Application.handleSizingEvent_0();
        window.clearTimeout(this$Application.resizeEventTimeoutId_0);
        this$Application.resizeEventTimeoutId_0 = window.setTimeout(Application$resizeMethod$lambda$lambda(this$Application), 400);
      }
      return Unit;
    };
  }
  function Application_init$lambda(this$Application) {
    return function (event) {
      this$Application.onPopStateChange_0(event);
      return Unit;
    };
  }
  function Application_init$lambda_0(this$Application) {
    return function (it) {
      if (polyfill_hidden(document)) {
        this$Application.audioController_0.changeMusicState_6taknv$(false);
      }
       else {
        this$Application.navigationController_0.loadTopView();
      }
      return Unit;
    };
  }
  function Application_init$lambda_1(this$Application) {
    return function (it) {
      this$Application.navigationController_0.fullscreenChanged_6taknv$(isFullScreenEnabled(document));
      return Unit;
    };
  }
  function Application_init$lambda$lambda(this$Application) {
    return function (modalResult) {
      if (modalResult === ModalResult$SUCCESS_getInstance())
        this$Application.openSettings_6taknv$(true);
      return Unit;
    };
  }
  function Application_init$lambda_2(this$Application) {
    return function (event) {
      event.stopImmediatePropagation();
      this$Application.audioController_0.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      if (this$Application.userConfig_0.shouldRequestAgeBeforeGoingToSettings) {
        this$Application.openAgeGate_5q5pme$(Route$Companion_getInstance().getEmpty(), Application_init$lambda$lambda(this$Application));
      }
       else {
        this$Application.openSettings_6taknv$(false);
      }
      return Unit;
    };
  }
  function Application_init$lambda_3(this$Application) {
    return function (event) {
      this$Application.audioController_0.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      if (this$Application.favoritesIsOpen_0)
        this$Application.goBack();
      else
        this$Application.openFavorites();
      return Unit;
    };
  }
  Application.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Application',
    interfaces: [ApplicationListener]
  };
  function ApplicationListener() {
  }
  ApplicationListener.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'ApplicationListener',
    interfaces: []
  };
  function MediaQuery_isSmallPhone() {
    return window.matchMedia('(max-width: 320px), (max-height: 320px)').matches;
  }
  function MediaQuery_isPhone() {
    return window.matchMedia('(max-width: 420px), (max-height: 420px)').matches;
  }
  function MediaQuery_isPortraitPhone() {
    return window.matchMedia('(max-width: 420px) and (orientation: portrait)').matches;
  }
  function MediaQuery_isPortraitTablet() {
    return window.matchMedia('(min-width: 420px) and (orientation: portrait)').matches;
  }
  function MediaQuery_isTablet() {
    return window.matchMedia('(min-width: 420px) and (min-height: 420px)').matches;
  }
  var Z_INDEX_STEP;
  var Z_INDEX_MODAL_START;
  var Z_INDEX_INIT_LOAD_SCREEN;
  var Z_INDEX_LOADING_CARD;
  function NavigationController(sideBarCoordinator, appContainer) {
    this.sideBarCoordinator_0 = sideBarCoordinator;
    this.appContainer_0 = appContainer;
    this.stack_0 = ArrayList_init();
    this.appContainer_0.appendChild(this.sideBarCoordinator_0.view);
  }
  NavigationController.prototype.loadTopView = function () {
    var tmp$;
    (tmp$ = lastOrNull(this.stack_0)) != null ? (tmp$.onWillAppear(), Unit) : null;
  };
  NavigationController.prototype.sizingEvent_vux9f0$ = function (newWidth, newHeight) {
    var tmp$;
    tmp$ = this.stack_0.iterator();
    while (tmp$.hasNext()) {
      var vc = tmp$.next();
      vc.sizingEvent_vux9f0$(newWidth, newHeight);
    }
  };
  NavigationController.prototype.fullscreenChanged_6taknv$ = function (isFullScreen) {
    var tmp$;
    tmp$ = this.stack_0.iterator();
    while (tmp$.hasNext()) {
      var vc = tmp$.next();
      vc.fullscreenChanged_6taknv$(isFullScreen);
    }
  };
  NavigationController.prototype.tryPoppingToRoute_fdlhz7$ = function (route, state) {
    var tmp$, tmp$_0, tmp$_1, tmp$_2;
    var index = {v: -1};
    var targetStateIndex = -1;
    try {
      targetStateIndex = (tmp$_1 = (tmp$_0 = (tmp$ = state['index']) != null ? tmp$.toString() : null) != null ? toInt(tmp$_0) : null) != null ? tmp$_1 : -1;
    }
     catch (e) {
      if (!Kotlin.isType(e, Exception))
        throw e;
    }
    if (targetStateIndex < 0)
      return false;
    var targetPlace = getOrNull(this.stack_0, targetStateIndex);
    if (equals((tmp$_2 = targetPlace != null ? targetPlace.route : null) != null ? tmp$_2.fullPath : null, route.fullPath))
      index.v = targetStateIndex;
    else {
      var tmp$_3, tmp$_0_0;
      var index_0 = 0;
      tmp$_3 = this.stack_0.iterator();
      while (tmp$_3.hasNext()) {
        var item = tmp$_3.next();
        var innerIndex = (tmp$_0_0 = index_0, index_0 = tmp$_0_0 + 1 | 0, tmp$_0_0);
        if (equals(item.route.fullPath, route.fullPath)) {
          index.v = innerIndex;
        }
      }
    }
    if (index.v < 0)
      return false;
    while (this.stack_0.size > (index.v + 1 | 0))
      this.pop_6taknv$(false);
    return true;
  };
  NavigationController.prototype.pop_6taknv$ = function (animated) {
    if (this.stack_0.size > 0) {
      while (this.popModal_6taknv$(animated)) {
      }
      var topVC = this.stack_0.removeAt_za3lpa$(this.stack_0.size - 1 | 0);
      topVC.onWillDisappear();
      topVC.onWillBeRemoved();
      this.appContainer_0.removeChild(ensureNotNull(this.appContainer_0.lastChild));
      var nextTopVC = lastOrNull(this.stack_0);
      if (nextTopVC != null) {
        nextTopVC.setBlur_6taknv$(false);
        this.setOthersFor_0(nextTopVC);
        nextTopVC.onWillAppear();
      }
      return true;
    }
    return false;
  };
  NavigationController.prototype.popModal_6taknv$ = function (animated) {
    var top = lastOrNull(this.stack_0);
    if (top != null && top.isModal) {
      var topVC = this.stack_0.removeAt_za3lpa$(this.stack_0.size - 1 | 0);
      topVC.onWillDisappear();
      topVC.onWillBeRemoved();
      this.appContainer_0.removeChild(ensureNotNull(this.appContainer_0.lastChild));
      var nextTopVC = lastOrNull(this.stack_0);
      if (nextTopVC != null) {
        nextTopVC.setBlur_6taknv$(false);
        this.setOthersFor_0(nextTopVC);
        nextTopVC.onWillAppear();
      }
      return true;
    }
    return false;
  };
  NavigationController.prototype.clearStack = function () {
    while (this.stack_0.size > 0)
      this.pop_6taknv$(false);
  };
  NavigationController.prototype.replaceStackWith_8c4kya$ = function (vc) {
    while (this.stack_0.size > 0)
      this.pop_6taknv$(false);
    this.push_8c4kya$(vc);
  };
  NavigationController.prototype.push_8c4kya$ = function (vc) {
    console.log('vc', vc);
    var firstView = this.stack_0.isEmpty();
    Kotlin.isType(vc, FolderViewController);
    var below = lastOrNull(this.stack_0);
    below != null ? (below.onWillDisappear(), Unit) : null;
    this.addView_0(vc);
    var viewState = vc.getState();
    viewState['index'] = (this.stack_0.size - 1 | 0).toString();
    if (!vc.isModal) {
      if (firstView)
        window.history.replaceState(viewState, vc.route.path.raw, vc.route.fullPath);
      else
        window.history.pushState(viewState, vc.route.path.raw, vc.route.fullPath);
    }
    vc.onWillAppear();
  };
  NavigationController.prototype.addView_0 = function (vc) {
    this.stack_0.add_11rb$(vc);
    var stackSize = this.stack_0.size;
    var zIndex = Kotlin.imul(stackSize, Z_INDEX_STEP);
    vc.setZIndex_za3lpa$(zIndex);
    this.setOthersFor_0(vc);
    if (vc.isBelowWaterTimer) {
      this.sideBarCoordinator_0.setZIndex_za3lpa$(zIndex + Z_INDEX_STEP | 0);
    }
     else if (this.sideBarCoordinator_0.lastZIndex >= zIndex) {
      this.sideBarCoordinator_0.setZIndex_za3lpa$(zIndex - 50 | 0);
    }
    this.appContainer_0.appendChild(vc.view);
  };
  NavigationController.prototype.setOthersFor_0 = function (vc) {
    var below = getOrNull(this.stack_0, this.stack_0.size - 2 | 0);
    if (!equals(below, vc))
      below != null ? (below.setBlur_6taknv$(vc.blurBackgroundView), Unit) : null;
    if (vc.isBelowWaterTimer) {
      this.sideBarCoordinator_0.setZIndex_za3lpa$(vc.lastZIndex + Z_INDEX_STEP | 0);
      this.sideBarCoordinator_0.setBlur_6taknv$(false);
    }
     else {
      this.sideBarCoordinator_0.setZIndex_za3lpa$(vc.lastZIndex - 50 | 0);
      if (vc.blurBackgroundView)
        this.sideBarCoordinator_0.setBlur_6taknv$(true);
      else
        this.sideBarCoordinator_0.setBlur_6taknv$(false);
    }
  };
  NavigationController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'NavigationController',
    interfaces: []
  };
  function ViewControllerBuilder(stringCheese, applicationListener, dependencyContainer) {
    this.stringCheese = stringCheese;
    this.applicationListener_0 = applicationListener;
    this.dependencyContainer_0 = dependencyContainer;
    this.api_0 = this.dependencyContainer_0.api;
    this.jsonFactoryV3_0 = this.dependencyContainer_0.jsonFactoryV3;
  }
  ViewControllerBuilder.prototype.buildCardSelectionViewController_rn0tbd$ = function (route) {
    return new CardSelectionViewController(route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.buildFavoritesViewController_rn0tbd$ = function (route) {
    return new FavoritesViewController(route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.buildVideoSelectionViewController_62c174$ = function (folder, color, route) {
    return new FolderViewController(folder, color, route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.json_buildVideoSelectionViewController_wxcqwd$ = function (json, color, route) {
    var tmp$;
    console.log('json', json);
    var folderData = Kotlin.isType(tmp$ = json['folder'], Object) ? tmp$ : null;
    console.log('FolderData', folderData);
    var folder_0 = folderData != null ? folder(this.jsonFactoryV3_0, folderData) : null;
    var usingColor = folderColor(this.jsonFactoryV3_0, json, color);
    console.log('Folder', folder_0);
    if (folder_0 != null)
      return new FolderViewController(folder_0, usingColor, route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
    return null;
  };
  function ViewControllerBuilder$async_buildVideoSelectionViewController$lambda(closure$onComplete, closure$route, this$ViewControllerBuilder) {
    return function (responseCode, folder) {
      if (folder != null) {
        closure$onComplete(this$ViewControllerBuilder.buildVideoSelectionViewController_62c174$(folder, Folder$BackgroundColor$RED_getInstance(), closure$route));
      }
       else {
        closure$onComplete(null);
      }
      return Unit;
    };
  }
  ViewControllerBuilder.prototype.async_buildVideoSelectionViewController_hclovw$ = function (route, onComplete) {
    this.api_0.folderGet_p7zhzt$(route.infoPath, ViewControllerBuilder$async_buildVideoSelectionViewController$lambda(onComplete, route, this));
  };
  ViewControllerBuilder.prototype.buildSettingsViewController_ivya20$ = function (alreadyHasParentPermission, route) {
    return new SettingsViewController(alreadyHasParentPermission, route, this.stringCheese, this.applicationListener_0, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.buildGameView_oq631x$ = function (game, route, age) {
    return new GameViewController(game, route, age, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.buildVideoPlayer_32j3gj$ = function (video, route) {
    return new VideoPlayerViewController(video, route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.json_buildVideoPlayer_jvytur$ = function (json, route) {
    var tmp$, tmp$_0;
    var videoData = Kotlin.isType(tmp$ = json['video'], Object) ? tmp$ : null;
    if (videoData != null) {
      var video_0 = video(this.jsonFactoryV3_0, Kotlin.isType(tmp$_0 = videoData, Object) ? tmp$_0 : throwCCE());
      return new VideoPlayerViewController(video_0, route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
    }
    return null;
  };
  function ViewControllerBuilder$asyync_buildVideoPlayer$lambda(closure$onComplete, closure$route, this$ViewControllerBuilder) {
    return function (responseCode, video) {
      if (video != null)
        closure$onComplete(this$ViewControllerBuilder.buildVideoPlayer_32j3gj$(video, closure$route));
      else
        closure$onComplete(null);
      return Unit;
    };
  }
  ViewControllerBuilder.prototype.asyync_buildVideoPlayer_xpmelq$ = function (route, onComplete) {
    this.api_0.videoGet_bm8j90$(route.infoPath, ViewControllerBuilder$asyync_buildVideoPlayer$lambda(onComplete, route, this));
  };
  ViewControllerBuilder.prototype.buildSubscriptionViewController_rn0tbd$ = function (route) {
    return new SubscriptionViewController(route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.buildAgeGate_edk2z4$ = function (onComplete, route) {
    return new AgeGateViewController(onComplete, route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.prototype.buildLockout_rn0tbd$ = function (route) {
    return new LockoutViewController(route, this.applicationListener_0, this.stringCheese, this.dependencyContainer_0);
  };
  ViewControllerBuilder.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ViewControllerBuilder',
    interfaces: []
  };
  function createDiv($receiver) {
    var tmp$;
    return Kotlin.isType(tmp$ = $receiver.createElement('div'), HTMLDivElement) ? tmp$ : throwCCE();
  }
  function createImg($receiver) {
    var tmp$;
    return Kotlin.isType(tmp$ = $receiver.createElement('img'), HTMLImageElement) ? tmp$ : throwCCE();
  }
  function createVideo($receiver) {
    var tmp$;
    return Kotlin.isType(tmp$ = $receiver.createElement('video'), HTMLVideoElement) ? tmp$ : throwCCE();
  }
  function isFullScreenEnabled($receiver) {
    return polyfill_fullscreenElement($receiver) != null;
  }
  function polyfill_fullscreenElement($receiver) {
    var tmp$;
    var dThis = $receiver;
    var ele = dThis.fullscreenElement != undefined ? dThis.fullscreenElement : dThis.webkitFullscreenElement != undefined ? dThis.webkitFullscreenElement : dThis.mozFullscreenElement != undefined ? dThis.mozFullscreenElement : dThis.msFullscreenElement != undefined ? dThis.msFullscreenElement : null;
    return Kotlin.isType(tmp$ = ele, Element) ? tmp$ : null;
  }
  function polyfill_webkitfullscreenchange($receiver, change) {
    var dThis = $receiver;
    if (dThis['webkitFullscreenEnabled'] != undefined) {
      dThis['onwebkitfullscreenchange'] = change;
    }
     else if (dThis['mozFullScreenEnabled'] != undefined) {
      dThis['onmozfullscreenchange'] = change;
    }
     else if (dThis['msFullscreenEnabled'] != undefined) {
      dThis['MSFullscreenChange'] = change;
    }
     else if (dThis['fullscreenEnabled'] != undefined) {
      $receiver.onfullscreenchange = change;
    }
  }
  function enableFullScreenPolyFill($receiver) {
    var dThis = $receiver;
    if (dThis['fullscreenEnabled'] == undefined) {
      if (dThis['webkitFullscreenEnabled'] != undefined) {
        dThis['fullscreen'] = dThis['webkitIsFullScreen'];
        dThis['fullscreenEnabled'] = dThis['webkitFullscreenEnabled'];
        dThis['exitFullscreen'] = dThis['webkitExitFullscreen'];
      }
       else if (dThis['mozFullScreenEnabled'] != undefined) {
        dThis['fullscreen'] = dThis['mozFullScreen'];
        dThis['fullscreenEnabled'] = dThis['mozFullscreenEnabled'];
        dThis['exitFullscreen'] = dThis['mozCancelFullScreen'];
      }
       else if (dThis['msFullscreenEnabled'] != undefined) {
        dThis['fullscreenEnabled'] = dThis['msFullscreenEnabled'];
        dThis['exitFullscreen'] = dThis['msExitFullscreen'];
      }
       else {
        println("Can't polly fill full screen");
        return;
      }
    }
  }
  function polyfill_requestFullScreen($receiver) {
    var dThis = $receiver;
    return dThis.requestFullscreen != undefined ? dThis.requestFullscreen() : dThis.webkitRequestFullscreen != undefined ? dThis.webkitRequestFullscreen() : dThis.mozfullscreenchange != undefined ? dThis.mozfullscreenchange() : dThis.msRequestFullscreen != undefined ? dThis.msRequestFullscreen() : null;
  }
  function polyfill_visibilityChange($receiver, change) {
    var dThis = $receiver;
    if (dThis['hidden'] != undefined) {
      dThis['onvisibilitychange'] = change;
    }
     else if (dThis['webkitHidden'] != undefined) {
      dThis['onwebkitvisibilitychange'] = change;
    }
     else if (dThis['msHidden'] != undefined) {
      dThis['onmsvisibilitychange'] = change;
    }
  }
  function polyfill_hidden($receiver) {
    var dThis = $receiver;
    if (dThis['hidden'] != undefined) {
      return dThis.hidden;
    }
     else if (dThis['webkitHidden'] != undefined) {
      return dThis.webkitHidden;
    }
     else if (dThis['msHidden'] != undefined) {
      return dThis.msHidden;
    }
    return false;
  }
  function Route(fullPath, infoPath, path) {
    Route$Companion_getInstance();
    this.fullPath = fullPath;
    this.infoPath = infoPath;
    this.path = path;
  }
  function Route$Companion() {
    Route$Companion_instance = this;
    this.BASE_URL = '/app';
  }
  Route$Companion.prototype.Create_61zpoe$ = function (fullPath) {
    var $receiver = split(fullPath, ['/']);
    var destination = ArrayList_init();
    var tmp$;
    tmp$ = $receiver.iterator();
    while (tmp$.hasNext()) {
      var element = tmp$.next();
      if (!equals(element, ''))
        destination.add_11rb$(element);
    }
    var parts = destination;
    var firstPart;
    var endPath;
    if (parts.size > 2) {
      firstPart = parts.get_za3lpa$(parts.size - 2 | 0);
      endPath = last(parts);
    }
     else if (parts.size > 1) {
      firstPart = last(parts);
      endPath = '';
    }
     else {
      firstPart = '';
      endPath = '';
    }
    var path = Route$Path$Companion_getInstance().FromRouteString_61zpoe$(firstPart);
    if (Route$Path$Companion_getInstance().RequiresIdPath_yyqixi$(path) && equals(endPath, ''))
      path = Route$Path$CARD_SELECTION_getInstance();
    return new Route(fullPath, endPath, path);
  };
  Route$Companion.prototype.getEmpty = function () {
    return new Route(this.BASE_URL, '', Route$Path$CARD_SELECTION_getInstance());
  };
  Route$Companion.prototype.defaultRouteCardSelection = function () {
    return new Route(this.BASE_URL, '', Route$Path$CARD_SELECTION_getInstance());
  };
  Route$Companion.prototype.defaultRouteFavorites = function () {
    return new Route(this.BASE_URL + '/' + Route$Path$FAVORITES_getInstance().raw, '', Route$Path$FAVORITES_getInstance());
  };
  Route$Companion.prototype.defaultRouteFolder_mi0oz1$ = function (folder, color) {
    return new Route(this.BASE_URL + '/' + Route$Path$FOLDER_getInstance().raw + '/' + folder.id, folder.id, Route$Path$FOLDER_getInstance());
  };
  Route$Companion.prototype.defaultGameRoute_b5err$ = function (game) {
    return new Route(this.BASE_URL + '/' + Route$Path$GAME_getInstance().raw + '/' + game.id, game.id, Route$Path$GAME_getInstance());
  };
  Route$Companion.prototype.defaultRouteVideoPlayer_9u1k3q$ = function (video) {
    return new Route(this.BASE_URL + '/' + Route$Path$VIDEO_getInstance().raw + '/' + video.userId, video.userId, Route$Path$VIDEO_getInstance());
  };
  Route$Companion.prototype.defaultRouteSettings = function () {
    return new Route(this.BASE_URL + '/' + Route$Path$SETTINGS_getInstance().raw, '', Route$Path$SETTINGS_getInstance());
  };
  Route$Companion.prototype.defaultSubscribe = function () {
    return new Route(this.BASE_URL + '/' + Route$Path$SUBSCRIBE_getInstance().raw, '', Route$Path$SUBSCRIBE_getInstance());
  };
  Route$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Route$Companion_instance = null;
  function Route$Companion_getInstance() {
    if (Route$Companion_instance === null) {
      new Route$Companion();
    }
    return Route$Companion_instance;
  }
  function Route$Path(name, ordinal, raw) {
    Enum.call(this);
    this.raw = raw;
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function Route$Path_initFields() {
    Route$Path_initFields = function () {
    };
    Route$Path$CARD_SELECTION_instance = new Route$Path('CARD_SELECTION', 0, '');
    Route$Path$SETTINGS_instance = new Route$Path('SETTINGS', 1, 'settings');
    Route$Path$FAVORITES_instance = new Route$Path('FAVORITES', 2, 'favorites');
    Route$Path$FOLDER_instance = new Route$Path('FOLDER', 3, 'folder');
    Route$Path$VIDEO_instance = new Route$Path('VIDEO', 4, 'video');
    Route$Path$SUBSCRIBE_instance = new Route$Path('SUBSCRIBE', 5, 'subscribe');
    Route$Path$MODAL_AGE_GATE_instance = new Route$Path('MODAL_AGE_GATE', 6, 'age_gate');
    Route$Path$GAME_instance = new Route$Path('GAME', 7, 'game');
    Route$Path$Companion_getInstance();
  }
  var Route$Path$CARD_SELECTION_instance;
  function Route$Path$CARD_SELECTION_getInstance() {
    Route$Path_initFields();
    return Route$Path$CARD_SELECTION_instance;
  }
  var Route$Path$SETTINGS_instance;
  function Route$Path$SETTINGS_getInstance() {
    Route$Path_initFields();
    return Route$Path$SETTINGS_instance;
  }
  var Route$Path$FAVORITES_instance;
  function Route$Path$FAVORITES_getInstance() {
    Route$Path_initFields();
    return Route$Path$FAVORITES_instance;
  }
  var Route$Path$FOLDER_instance;
  function Route$Path$FOLDER_getInstance() {
    Route$Path_initFields();
    return Route$Path$FOLDER_instance;
  }
  var Route$Path$VIDEO_instance;
  function Route$Path$VIDEO_getInstance() {
    Route$Path_initFields();
    return Route$Path$VIDEO_instance;
  }
  var Route$Path$SUBSCRIBE_instance;
  function Route$Path$SUBSCRIBE_getInstance() {
    Route$Path_initFields();
    return Route$Path$SUBSCRIBE_instance;
  }
  var Route$Path$MODAL_AGE_GATE_instance;
  function Route$Path$MODAL_AGE_GATE_getInstance() {
    Route$Path_initFields();
    return Route$Path$MODAL_AGE_GATE_instance;
  }
  var Route$Path$GAME_instance;
  function Route$Path$GAME_getInstance() {
    Route$Path_initFields();
    return Route$Path$GAME_instance;
  }
  function Route$Path$Companion() {
    Route$Path$Companion_instance = this;
  }
  Route$Path$Companion.prototype.FromRouteString_61zpoe$ = function (routeString) {
    var tmp$;
    switch (routeString) {
      case 'settings':
        tmp$ = Route$Path$SETTINGS_getInstance();
        break;
      case 'favorites':
        tmp$ = Route$Path$FAVORITES_getInstance();
        break;
      case 'folder':
        tmp$ = Route$Path$FOLDER_getInstance();
        break;
      case 'video':
        tmp$ = Route$Path$VIDEO_getInstance();
        break;
      case 'subscribe':
        tmp$ = Route$Path$SUBSCRIBE_getInstance();
        break;
      case 'game':
        tmp$ = Route$Path$GAME_getInstance();
        break;
      default:tmp$ = Route$Path$CARD_SELECTION_getInstance();
        break;
    }
    return tmp$;
  };
  Route$Path$Companion.prototype.RequiresIdPath_yyqixi$ = function (path) {
    return path === Route$Path$FOLDER_getInstance() || path === Route$Path$VIDEO_getInstance();
  };
  Route$Path$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var Route$Path$Companion_instance = null;
  function Route$Path$Companion_getInstance() {
    Route$Path_initFields();
    if (Route$Path$Companion_instance === null) {
      new Route$Path$Companion();
    }
    return Route$Path$Companion_instance;
  }
  Route$Path.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Path',
    interfaces: [Enum]
  };
  function Route$Path$values() {
    return [Route$Path$CARD_SELECTION_getInstance(), Route$Path$SETTINGS_getInstance(), Route$Path$FAVORITES_getInstance(), Route$Path$FOLDER_getInstance(), Route$Path$VIDEO_getInstance(), Route$Path$SUBSCRIBE_getInstance(), Route$Path$MODAL_AGE_GATE_getInstance(), Route$Path$GAME_getInstance()];
  }
  Route$Path.values = Route$Path$values;
  function Route$Path$valueOf(name) {
    switch (name) {
      case 'CARD_SELECTION':
        return Route$Path$CARD_SELECTION_getInstance();
      case 'SETTINGS':
        return Route$Path$SETTINGS_getInstance();
      case 'FAVORITES':
        return Route$Path$FAVORITES_getInstance();
      case 'FOLDER':
        return Route$Path$FOLDER_getInstance();
      case 'VIDEO':
        return Route$Path$VIDEO_getInstance();
      case 'SUBSCRIBE':
        return Route$Path$SUBSCRIBE_getInstance();
      case 'MODAL_AGE_GATE':
        return Route$Path$MODAL_AGE_GATE_getInstance();
      case 'GAME':
        return Route$Path$GAME_getInstance();
      default:throwISE('No enum constant net.kidjo.app.js.models.Route.Path.' + name);
    }
  }
  Route$Path.valueOf_61zpoe$ = Route$Path$valueOf;
  Route.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'Route',
    interfaces: []
  };
  Route.prototype.component1 = function () {
    return this.fullPath;
  };
  Route.prototype.component2 = function () {
    return this.infoPath;
  };
  Route.prototype.component3 = function () {
    return this.path;
  };
  Route.prototype.copy_76n7ga$ = function (fullPath, infoPath, path) {
    return new Route(fullPath === void 0 ? this.fullPath : fullPath, infoPath === void 0 ? this.infoPath : infoPath, path === void 0 ? this.path : path);
  };
  Route.prototype.toString = function () {
    return 'Route(fullPath=' + Kotlin.toString(this.fullPath) + (', infoPath=' + Kotlin.toString(this.infoPath)) + (', path=' + Kotlin.toString(this.path)) + ')';
  };
  Route.prototype.hashCode = function () {
    var result = 0;
    result = result * 31 + Kotlin.hashCode(this.fullPath) | 0;
    result = result * 31 + Kotlin.hashCode(this.infoPath) | 0;
    result = result * 31 + Kotlin.hashCode(this.path) | 0;
    return result;
  };
  Route.prototype.equals = function (other) {
    return this === other || (other !== null && (typeof other === 'object' && (Object.getPrototypeOf(this) === Object.getPrototypeOf(other) && (Kotlin.equals(this.fullPath, other.fullPath) && Kotlin.equals(this.infoPath, other.infoPath) && Kotlin.equals(this.path, other.path)))));
  };
  var MAX_AGE;
  var MIN_AGE;
  function AgeGateViewController(onComplete, route, applicationListener, stringCheese, dependencyContainer) {
    AgeGateViewController$Companion_getInstance();
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.onComplete_0 = onComplete;
    this.isBelowWaterTimer_uud0eg$_0 = false;
    this.blurBackgroundView_fb9877$_0 = true;
    this.isModal_7xzx96$_0 = true;
    this.number1_0 = -1;
    this.number2_0 = -1;
    this.number3_0 = -1;
    this.number4_0 = -1;
    this.onClick_0 = AgeGateViewController$onClick$lambda(this);
    this.coordinator_0 = new AgeGateViewCoordinator(stringCheese, this.onClick_0, this.view, this.userConfig, this.deviceData);
    this.setResultsDisplay_0();
    var closeAction = AgeGateViewController_init$lambda(this);
    this.coordinator_0.closeButton.onclick = closeAction;
    this.coordinator_0.closeButtonPortrait.onclick = closeAction;
  }
  Object.defineProperty(AgeGateViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_uud0eg$_0;
    }
  });
  Object.defineProperty(AgeGateViewController.prototype, 'blurBackgroundView', {
    get: function () {
      return this.blurBackgroundView_fb9877$_0;
    }
  });
  Object.defineProperty(AgeGateViewController.prototype, 'isModal', {
    get: function () {
      return this.isModal_7xzx96$_0;
    }
  });
  AgeGateViewController.prototype.addNumber_0 = function (num) {
    if (this.number1_0 === -1)
      this.number1_0 = num;
    else if (this.number2_0 === -1)
      this.number2_0 = num;
    else if (this.number3_0 === -1)
      this.number3_0 = num;
    else if (this.number4_0 === -1)
      this.number4_0 = num;
    this.setResultsDisplay_0();
    this.checkResults_0();
  };
  AgeGateViewController.prototype.removeNumber_0 = function () {
    if (this.number4_0 !== -1)
      this.number4_0 = -1;
    else if (this.number3_0 !== -1)
      this.number3_0 = -1;
    else if (this.number2_0 !== -1)
      this.number2_0 = -1;
    else if (this.number1_0 !== -1)
      this.number1_0 = -1;
    this.setResultsDisplay_0();
  };
  AgeGateViewController.prototype.setResultsDisplay_0 = function () {
    this.coordinator_0.resultText.innerHTML = (this.number1_0 === -1 ? this.stringCheese.ageGateAgePlaceholderYear : this.number1_0).toString() + ' ' + ((this.number2_0 === -1 ? this.stringCheese.ageGateAgePlaceholderYear : this.number2_0).toString() + ' ') + ((this.number3_0 === -1 ? this.stringCheese.ageGateAgePlaceholderYear : this.number3_0).toString() + ' ') + (this.number4_0 === -1 ? this.stringCheese.ageGateAgePlaceholderYear : this.number4_0).toString();
  };
  AgeGateViewController.prototype.checkResults_0 = function () {
    if (this.number1_0 === -1 || this.number2_0 === -1 || this.number3_0 === -1 || this.number4_0 === -1)
      return;
    var userYear = (this.number1_0 * 1000 | 0) + (this.number2_0 * 100 | 0) + (this.number3_0 * 10 | 0) + this.number4_0 | 0;
    var currentYear = (new Date()).getFullYear();
    var age = currentYear - userYear | 0;
    if (18 <= age && age <= 120) {
      this.close_0(ModalResult$SUCCESS_getInstance());
    }
     else {
      this.close_0(ModalResult$FAIL_getInstance());
    }
  };
  AgeGateViewController.prototype.close_0 = function (result) {
    if (result === ModalResult$SUCCESS_getInstance())
      this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$VALIDATION_getInstance());
    else if (result === ModalResult$FAIL_getInstance())
      this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$LOCK_getInstance());
    else
      this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
    this.applicationListener.closeTopModal();
    this.onComplete_0(result);
  };
  function AgeGateViewController$Companion() {
    AgeGateViewController$Companion_instance = this;
    this.NOT_SET_NUMBER = -1;
  }
  AgeGateViewController$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var AgeGateViewController$Companion_instance = null;
  function AgeGateViewController$Companion_getInstance() {
    if (AgeGateViewController$Companion_instance === null) {
      new AgeGateViewController$Companion();
    }
    return AgeGateViewController$Companion_instance;
  }
  function AgeGateViewController$onClick$lambda(this$AgeGateViewController) {
    return function (event) {
      var tmp$, tmp$_0;
      event.stopImmediatePropagation();
      this$AgeGateViewController.tryPlayingAudioAgain_8be2vx$();
      this$AgeGateViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      var button = Kotlin.isType(tmp$ = event.currentTarget, HTMLDivElement) ? tmp$ : throwCCE();
      var index = button.getAttribute('index');
      if (equals(index, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        this$AgeGateViewController.removeNumber_0();
      }
       else {
        var number = -1;
        try {
          number = (tmp$_0 = index != null ? toInt(index) : null) != null ? tmp$_0 : -1;
        }
         catch (e) {
          if (!Kotlin.isType(e, Exception))
            throw e;
        }
        if (number >= 0 && number <= 9)
          this$AgeGateViewController.addNumber_0(number);
      }
      return Unit;
    };
  }
  function AgeGateViewController_init$lambda(this$AgeGateViewController) {
    return function (event) {
      event.stopImmediatePropagation();
      this$AgeGateViewController.tryPlayingAudioAgain_8be2vx$();
      this$AgeGateViewController.close_0(ModalResult$CANCELED_getInstance());
      return Unit;
    };
  }
  AgeGateViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'AgeGateViewController',
    interfaces: [BaseViewController]
  };
  function ModalResult(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function ModalResult_initFields() {
    ModalResult_initFields = function () {
    };
    ModalResult$CANCELED_instance = new ModalResult('CANCELED', 0);
    ModalResult$FAIL_instance = new ModalResult('FAIL', 1);
    ModalResult$SUCCESS_instance = new ModalResult('SUCCESS', 2);
  }
  var ModalResult$CANCELED_instance;
  function ModalResult$CANCELED_getInstance() {
    ModalResult_initFields();
    return ModalResult$CANCELED_instance;
  }
  var ModalResult$FAIL_instance;
  function ModalResult$FAIL_getInstance() {
    ModalResult_initFields();
    return ModalResult$FAIL_instance;
  }
  var ModalResult$SUCCESS_instance;
  function ModalResult$SUCCESS_getInstance() {
    ModalResult_initFields();
    return ModalResult$SUCCESS_instance;
  }
  ModalResult.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ModalResult',
    interfaces: [Enum]
  };
  function ModalResult$values() {
    return [ModalResult$CANCELED_getInstance(), ModalResult$FAIL_getInstance(), ModalResult$SUCCESS_getInstance()];
  }
  ModalResult.values = ModalResult$values;
  function ModalResult$valueOf(name) {
    switch (name) {
      case 'CANCELED':
        return ModalResult$CANCELED_getInstance();
      case 'FAIL':
        return ModalResult$FAIL_getInstance();
      case 'SUCCESS':
        return ModalResult$SUCCESS_getInstance();
      default:throwISE('No enum constant net.kidjo.app.js.viewcontrollers.ModalResult.' + name);
    }
  }
  ModalResult.valueOf_61zpoe$ = ModalResult$valueOf;
  function BaseViewController(route, applicationListener, stringCheese, dependencyContainer) {
    this.route = route;
    this.applicationListener = applicationListener;
    this.stringCheese = stringCheese;
    this.jsonReader = dependencyContainer.jsonReader;
    this.jsonFactoryV3 = dependencyContainer.jsonFactoryV3;
    this.userConfig = dependencyContainer.platformDependencyProvider.userConfig;
    this.deviceData = dependencyContainer.platformDependencyProvider.deviceData;
    this.platformDependencyProvider = dependencyContainer.platformDependencyProvider;
    this.smallStorageController = dependencyContainer.smallStorageController;
    this.api = dependencyContainer.api;
    this.settingsController = dependencyContainer.settingsController;
    this.audioController = dependencyContainer.audioController;
    this.userController = dependencyContainer.userController;
    this.kidsController = dependencyContainer.kidsController;
    this.favoritesController = dependencyContainer.favoritesController;
    this.view = createDiv(document);
    this.lastZIndex_2dpqb4$_0 = 0;
    this.lastWidth_a0w5yy$_0 = -1;
    this.lastHeight_zhl9cx$_0 = -1;
    this.isBelowWaterTimer_7kg3i7$_0 = true;
    this.blurBackgroundView_1glc9w$_0 = false;
    this.isModal_fantm5$_0 = false;
    this.shouldCheckOvertime_esyjr4$_0 = false;
    this.shouldPlayMusic_blczgo$_0 = true;
    this.viewState_3lhyom$_0 = BaseViewController$ViewState$NOT_VISIBLE_getInstance();
    this.view.classList.add('base-view-container');
  }
  Object.defineProperty(BaseViewController.prototype, 'lastZIndex', {
    get: function () {
      return this.lastZIndex_2dpqb4$_0;
    },
    set: function (lastZIndex) {
      this.lastZIndex_2dpqb4$_0 = lastZIndex;
    }
  });
  Object.defineProperty(BaseViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_7kg3i7$_0;
    }
  });
  Object.defineProperty(BaseViewController.prototype, 'blurBackgroundView', {
    get: function () {
      return this.blurBackgroundView_1glc9w$_0;
    }
  });
  Object.defineProperty(BaseViewController.prototype, 'isModal', {
    get: function () {
      return this.isModal_fantm5$_0;
    }
  });
  Object.defineProperty(BaseViewController.prototype, 'shouldCheckOvertime', {
    get: function () {
      return this.shouldCheckOvertime_esyjr4$_0;
    }
  });
  Object.defineProperty(BaseViewController.prototype, 'shouldPlayMusic', {
    get: function () {
      return this.shouldPlayMusic_blczgo$_0;
    }
  });
  Object.defineProperty(BaseViewController.prototype, 'viewState', {
    get: function () {
      return this.viewState_3lhyom$_0;
    },
    set: function (viewState) {
      this.viewState_3lhyom$_0 = viewState;
    }
  });
  BaseViewController.prototype.onWillAppear = function () {
    this.viewState = BaseViewController$ViewState$VISIBLE_getInstance();
    if (this.shouldCheckOvertime)
      this.checkIfOverTime_8be2vx$();
    this.audioController.changeMusicState_6taknv$(this.shouldPlayMusic);
  };
  BaseViewController.prototype.onWillDisappear = function () {
    this.viewState = BaseViewController$ViewState$NOT_VISIBLE_getInstance();
  };
  BaseViewController.prototype.onWillBeRemoved = function () {
  };
  BaseViewController.prototype.setCssVariable_puj7f4$ = function (name, value) {
    this.view.style.setProperty(name, value);
  };
  BaseViewController.prototype.setZIndex_za3lpa$ = function (index) {
    this.view.style.zIndex = index.toString();
    this.lastZIndex = index;
  };
  BaseViewController.prototype.setBlur_6taknv$ = function (on) {
    if (on)
      this.view.classList.add('blur');
    else
      this.view.classList.remove('blur');
  };
  BaseViewController.prototype.sizingEvent_vux9f0$ = function (width, height) {
    if (width <= 0 || height <= 0)
      return;
    if (width !== this.lastWidth_a0w5yy$_0 || height !== this.lastHeight_zhl9cx$_0) {
      this.shouldSize_tjonv8$(this.lastWidth_a0w5yy$_0, width, this.lastHeight_zhl9cx$_0, height);
      this.lastWidth_a0w5yy$_0 = width;
      this.lastHeight_zhl9cx$_0 = height;
    }
  };
  BaseViewController.prototype.shouldSize_tjonv8$ = function (oldWidth, newWidth, oldHeight, newHeight) {
  };
  BaseViewController.prototype.fullscreenChanged_6taknv$ = function (isFullScreen) {
  };
  BaseViewController.prototype.getState = function () {
    return {};
  };
  BaseViewController.prototype.checkIfOverTime_8be2vx$ = function () {
    if (this.settingsController.isOverScreenTimeLimit()) {
      this.applicationListener.openOvertimeLockout_rn0tbd$(this.route);
    }
  };
  BaseViewController.prototype.tryPlayingAudioAgain_8be2vx$ = function () {
    this.audioController.changeMusicState_6taknv$(this.shouldPlayMusic);
  };
  function BaseViewController$ViewState(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function BaseViewController$ViewState_initFields() {
    BaseViewController$ViewState_initFields = function () {
    };
    BaseViewController$ViewState$VISIBLE_instance = new BaseViewController$ViewState('VISIBLE', 0);
    BaseViewController$ViewState$NOT_VISIBLE_instance = new BaseViewController$ViewState('NOT_VISIBLE', 1);
  }
  var BaseViewController$ViewState$VISIBLE_instance;
  function BaseViewController$ViewState$VISIBLE_getInstance() {
    BaseViewController$ViewState_initFields();
    return BaseViewController$ViewState$VISIBLE_instance;
  }
  var BaseViewController$ViewState$NOT_VISIBLE_instance;
  function BaseViewController$ViewState$NOT_VISIBLE_getInstance() {
    BaseViewController$ViewState_initFields();
    return BaseViewController$ViewState$NOT_VISIBLE_instance;
  }
  BaseViewController$ViewState.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'ViewState',
    interfaces: [Enum]
  };
  function BaseViewController$ViewState$values() {
    return [BaseViewController$ViewState$VISIBLE_getInstance(), BaseViewController$ViewState$NOT_VISIBLE_getInstance()];
  }
  BaseViewController$ViewState.values = BaseViewController$ViewState$values;
  function BaseViewController$ViewState$valueOf(name) {
    switch (name) {
      case 'VISIBLE':
        return BaseViewController$ViewState$VISIBLE_getInstance();
      case 'NOT_VISIBLE':
        return BaseViewController$ViewState$NOT_VISIBLE_getInstance();
      default:throwISE('No enum constant net.kidjo.app.js.viewcontrollers.BaseViewController.ViewState.' + name);
    }
  }
  BaseViewController$ViewState.valueOf_61zpoe$ = BaseViewController$ViewState$valueOf;
  BaseViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'BaseViewController',
    interfaces: []
  };
  var DISTANCE_TIL_SOUND;
  function CardSelectionViewController(route, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.isBelowWaterTimer_9z8yvq$_0 = true;
    this.shouldCheckOvertime_rrpsvf$_0 = true;
    this.pager_0 = new CardPager(12, this.api, this, 5);
    this.cards_0 = ArrayList_init();
    this.viewCoordinator_0 = new CardSelectionViewCoordinator(stringCheese, this.userConfig, this.deviceData, this.view);
    this.lastScrolledLeft_0 = 0.0;
    this.lastScrolledTop_0 = 0.0;
    this.swipeTimeStamp_0 = 0.0;
    this.onclick_0 = CardSelectionViewController$onclick$lambda(this);
    this.viewCoordinator_0.cardList.onscroll = CardSelectionViewController_init$lambda(this);
  }
  Object.defineProperty(CardSelectionViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_9z8yvq$_0;
    }
  });
  Object.defineProperty(CardSelectionViewController.prototype, 'shouldCheckOvertime', {
    get: function () {
      return this.shouldCheckOvertime_rrpsvf$_0;
    }
  });
  CardSelectionViewController.prototype.checkScroll_0 = function () {
    var distance;
    if (this.viewCoordinator_0.cardList.scrollLeft > 0) {
      if (this.viewCoordinator_0.cardList.scrollLeft + this.viewCoordinator_0.cardListWidthPlusBuffer > this.viewCoordinator_0.cardList.scrollWidth) {
        this.pager_0.nextPage();
      }
      distance = this.viewCoordinator_0.cardList.scrollLeft - this.lastScrolledLeft_0;
    }
     else if (this.viewCoordinator_0.cardList.scrollTop > 0) {
      if (this.viewCoordinator_0.cardList.scrollTop + this.viewCoordinator_0.cardListHeightPlusBuffer > this.viewCoordinator_0.cardList.scrollHeight) {
        this.pager_0.nextPage();
      }
      distance = this.viewCoordinator_0.cardList.scrollTop - this.lastScrolledTop_0;
    }
     else {
      distance = 0.0;
    }
    var x = distance;
    if (Math_0.abs(x) > -50) {
      var now = Date.now();
      if (this.swipeTimeStamp_0 + 1000 < now) {
        this.swipeTimeStamp_0 = now;
        this.lastScrolledLeft_0 = this.viewCoordinator_0.cardList.scrollLeft;
        this.lastScrolledTop_0 = this.viewCoordinator_0.cardList.scrollTop;
        if (distance > 0)
          this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$SWIPE_RIGHT_getInstance());
        else
          this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$SWIPE_LEFT_getInstance());
      }
    }
  };
  CardSelectionViewController.prototype.checkIfNeedMore_0 = function () {
    var tmp$, tmp$_0;
    tmp$_0 = Kotlin.isType(tmp$ = this.viewCoordinator_0.cardList.lastElementChild, HTMLElement) ? tmp$ : null;
    if (tmp$_0 == null) {
      return;
    }
    var lastChild = tmp$_0;
    if (this.viewCoordinator_0.cardList.clientHeight > this.viewCoordinator_0.cardList.clientWidth) {
      if ((lastChild.offsetTop + lastChild.clientHeight | 0) < this.viewCoordinator_0.cardList.clientHeight)
        this.pager_0.nextPage();
    }
     else {
      if ((lastChild.offsetLeft + lastChild.clientWidth | 0) < this.viewCoordinator_0.cardList.clientWidth)
        this.pager_0.nextPage();
    }
  };
  CardSelectionViewController.prototype.onWillAppear = function () {
    var tmp$, tmp$_0;
    BaseViewController.prototype.onWillAppear.call(this);
    this.viewCoordinator_0.onWillAppear();
    this.applicationListener.refreshSideBar();
    this.applicationListener.setSideBarFavoriteButton_6taknv$(false);
    var selectedKid = this.kidsController.getSelectedKid();
    var id = (tmp$ = selectedKid != null ? selectedKid.userId : null) != null ? tmp$ : Kid$Companion_getInstance().NO_ID;
    var age = (tmp$_0 = selectedKid != null ? selectedKid.age : null) != null ? tmp$_0 : 4;
    var isRefreshing = this.pager_0.set_4lu6kc$(id, age, this.userController.user.isSubscribed, this.settingsController.contentType, true, false, this.settingsController.language);
    if (isRefreshing) {
      this.cards_0.clear();
      this.viewCoordinator_0.clearCardList();
    }
    this.sizeCards_0();
  };
  CardSelectionViewController.prototype.shouldSize_tjonv8$ = function (oldWidth, newWidth, oldHeight, newHeight) {
    BaseViewController.prototype.shouldSize_tjonv8$.call(this, oldWidth, newWidth, oldHeight, newHeight);
    this.lastScrolledLeft_0 = 0.0;
    this.lastScrolledTop_0 = 0.0;
    this.sizeCards_0();
    this.checkIfNeedMore_0();
  };
  CardSelectionViewController.prototype.sizeCards_0 = function () {
    var sizeValue;
    if (this.viewCoordinator_0.cardList.clientHeight > this.viewCoordinator_0.cardList.clientWidth) {
      addClass(this.viewCoordinator_0.cardList, ['horizontal-sizing']);
      this.viewCoordinator_0.cardList.classList.remove('vertical-sizing');
      sizeValue = this.viewCoordinator_0.cardList.clientWidth / 2 | 0;
    }
     else {
      addClass(this.viewCoordinator_0.cardList, ['vertical-sizing']);
      this.viewCoordinator_0.cardList.classList.remove('horizontal-sizing');
      sizeValue = this.viewCoordinator_0.cardList.clientHeight / 2 | 0;
    }
    this.setCssVariable_puj7f4$('--SIZE_OF_FOLDER_CARDS', sizeValue.toString() + 'px');
  };
  CardSelectionViewController.prototype.openCard_0 = function (index) {
    var tmp$;
    this.tryPlayingAudioAgain_8be2vx$();
    console.log('cards', this.cards_0);
    tmp$ = getOrNull(this.cards_0, index);
    if (tmp$ == null) {
      return;
    }
    var folder = tmp$;
    console.log('folder', folder);
    this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$FOLDER_OPEN_getInstance());
    console.log('applicationListener.openVideoSelection', folder, Folder$BackgroundColor$Companion_getInstance().FromCollectionIndex_za3lpa$(index), index);
    this.applicationListener.openVideoSelection_mi0oz1$(folder, Folder$BackgroundColor$Companion_getInstance().FromCollectionIndex_za3lpa$(index));
  };
  CardSelectionViewController.prototype.pagerGotNextSet_3c1pkb$ = function (cards) {
    console.log(cards);
    addAll(this.cards_0, cards);
    var tmp$ = console;
    var destination = ArrayList_init(cards.length);
    var tmp$_0;
    for (tmp$_0 = 0; tmp$_0 !== cards.length; ++tmp$_0) {
      var item = cards[tmp$_0];
      var tmp$_1 = destination.add_11rb$;
      var transform$result;
      if (item.mediaType === 'game') {
        transform$result = item;
      }
       else {
        transform$result = null;
      }
      tmp$_1.call(destination, transform$result);
    }
    var destination_0 = ArrayList_init();
    var tmp$_2;
    tmp$_2 = destination.iterator();
    while (tmp$_2.hasNext()) {
      var element = tmp$_2.next();
      if (element !== null)
        destination_0.add_11rb$(element);
    }
    tmp$.log('All Cards', destination_0);
    this.viewCoordinator_0.addCards_c4njs0$(cards, this.onclick_0);
    this.checkIfNeedMore_0();
  };
  function CardSelectionViewController$onclick$lambda(this$CardSelectionViewController) {
    return function (event) {
      var tmp$, tmp$_0;
      console.log(event);
      var card = Kotlin.isType(tmp$ = event.currentTarget, HTMLDivElement) ? tmp$ : null;
      if (card != null) {
        var index = -1;
        try {
          index = toInt((tmp$_0 = card.getAttribute('index')) != null ? tmp$_0 : '-1');
        }
         catch (e) {
          if (!Kotlin.isType(e, Exception))
            throw e;
        }
        if (index >= 0) {
          console.log(card, index);
          this$CardSelectionViewController.openCard_0(index);
        }
      }
      event.stopImmediatePropagation();
      return Unit;
    };
  }
  function CardSelectionViewController_init$lambda(this$CardSelectionViewController) {
    return function (event) {
      this$CardSelectionViewController.checkScroll_0();
      return Unit;
    };
  }
  CardSelectionViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'CardSelectionViewController',
    interfaces: [CardPager$Listener, BaseViewController]
  };
  function FavoritesViewController(route, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.favorites_0 = ArrayList_init();
    this.onClick_0 = FavoritesViewController$onClick$lambda;
    this.videoListCoordinator_0 = new VideoListCoordinator(true, Folder$BackgroundColor$RED_getInstance(), this.userController.user.isSubscribed, stringCheese, this.deviceData, this.userConfig, this, this.favoritesController, this.kidsController);
    this.viewCoordinators_0 = new FavoritesViewCoordinator(this.userConfig, stringCheese, this.videoListCoordinator_0.view, this.view);
  }
  FavoritesViewController.prototype.onWillAppear = function () {
    BaseViewController.prototype.onWillAppear.call(this);
    this.applicationListener.setSideBarFavoriteButton_6taknv$(true);
    this.videoListCoordinator_0.setSizing();
    var newFavorites = this.favoritesController.getFavorites();
    var isDiff = {v: newFavorites.size !== this.favorites_0.size};
    if (!isDiff.v) {
      var tmp$;
      tmp$ = newFavorites.iterator();
      while (tmp$.hasNext()) {
        var element = tmp$.next();
        action$break: do {
          var tmp$_0;
          tmp$_0 = this.favorites_0.iterator();
          while (tmp$_0.hasNext()) {
            var oldFav = tmp$_0.next();
            if (!equals(oldFav.videoId, element.videoId)) {
              isDiff.v = true;
              break action$break;
            }
          }
        }
         while (false);
      }
    }
    console.log('favorites', this.favorites_0);
    if (isDiff.v) {
      this.favorites_0 = newFavorites;
      this.videoListCoordinator_0.setVideoList_giv38x$(this.favorites_0);
    }
  };
  FavoritesViewController.prototype.shouldSize_tjonv8$ = function (oldWidth, newWidth, oldHeight, newHeight) {
    BaseViewController.prototype.shouldSize_tjonv8$.call(this, oldWidth, newWidth, oldHeight, newHeight);
    this.videoListCoordinator_0.setSizing();
  };
  FavoritesViewController.prototype.videoCardWasClicked_61zpoe$ = function (videoId) {
    var tmp$;
    var video = {v: null};
    var tmp$_0;
    tmp$_0 = this.favorites_0.iterator();
    while (tmp$_0.hasNext()) {
      var element = tmp$_0.next();
      if (equals(element.videoId, videoId)) {
        video.v = element;
      }
    }
    tmp$ = video.v;
    if (tmp$ == null) {
      return;
    }
    var usingVideo = tmp$;
    this.tryPlayingAudioAgain_8be2vx$();
    if (!this.userController.user.isSubscribed && usingVideo.isPremium) {
      this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$LOCK_getInstance());
      this.applicationListener.openSubscriptionView();
    }
     else {
      this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      this.applicationListener.openVideoPlayer_9u1k3q$(usingVideo);
    }
  };
  FavoritesViewController.prototype.videoCardFavoriteClicked_61zpoe$ = function (videoId) {
    this.tryPlayingAudioAgain_8be2vx$();
    this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
    var i = 0;
    this.favoritesController.removeFavorite_61zpoe$(videoId);
    while (i < this.favorites_0.size) {
      if (equals(this.favorites_0.get_za3lpa$(i).videoId, videoId)) {
        this.favorites_0.removeAt_za3lpa$(i);
        continue;
      }
      i = i + 1 | 0;
    }
    return false;
  };
  function FavoritesViewController$onClick$lambda(event) {
    return Unit;
  }
  FavoritesViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FavoritesViewController',
    interfaces: [VideoListCoordinator$Listener, BaseViewController]
  };
  function FolderViewController(folder, backgroundColor, route, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.folder_0 = folder;
    this.backgroundColor_0 = backgroundColor;
    this.isBelowWaterTimer_l75g78$_0 = true;
    this.blurBackgroundView_u6bein$_0 = true;
    this.shouldCheckOvertime_sic8ub$_0 = true;
    this.videoList_0 = new VideoListCoordinator(false, this.backgroundColor_0, this.userController.user.isSubscribed, stringCheese, this.deviceData, this.userConfig, this, this.favoritesController, this.kidsController);
    this.viewCoordinator_0 = new FolderViewCoordinator(this.folder_0, this.backgroundColor_0, this.userController.user.isSubscribed, this.userConfig, this.deviceData, stringCheese, this.videoList_0.view, this.view);
    var tmp$;
    this.viewCoordinator_0.closeButton.onclick = FolderViewController_init$lambda(this, applicationListener);
    this.videoList_0.setVideoList_giv38x$(Kotlin.isType(tmp$ = toList(this.folder_0.subcards), List) ? tmp$ : throwCCE());
  }
  Object.defineProperty(FolderViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_l75g78$_0;
    }
  });
  Object.defineProperty(FolderViewController.prototype, 'blurBackgroundView', {
    get: function () {
      return this.blurBackgroundView_u6bein$_0;
    }
  });
  Object.defineProperty(FolderViewController.prototype, 'shouldCheckOvertime', {
    get: function () {
      return this.shouldCheckOvertime_sic8ub$_0;
    }
  });
  FolderViewController.prototype.onWillAppear = function () {
    BaseViewController.prototype.onWillAppear.call(this);
    this.applicationListener.refreshSideBar();
    this.applicationListener.setSideBarFavoriteButton_6taknv$(false);
    this.setSizing_0();
  };
  FolderViewController.prototype.getState = function () {
    var tmp$;
    var json = {};
    json['folder'] = toJson_folderWithColor(this.jsonFactoryV3, this.folder_0, this.backgroundColor_0);
    console.log('GetState', json);
    return Kotlin.isType(tmp$ = json, Object) ? tmp$ : throwCCE();
  };
  FolderViewController.prototype.shouldSize_tjonv8$ = function (oldWidth, newWidth, oldHeight, newHeight) {
    BaseViewController.prototype.shouldSize_tjonv8$.call(this, oldWidth, newWidth, oldHeight, newHeight);
    this.setSizing_0();
  };
  FolderViewController.prototype.setSizing_0 = function () {
    this.videoList_0.setSizing();
  };
  FolderViewController.prototype.openVideo_0 = function (videoId) {
    var tmp$, tmp$_0, tmp$_1, tmp$_2, tmp$_3;
    console.log('videoClicked', videoId);
    if (this.folder_0.mediaType === 'GAME') {
      var game = {v: null};
      var $receiver = Kotlin.isArray(tmp$ = this.folder_0.subcards) ? tmp$ : throwCCE();
      var tmp$_4;
      for (tmp$_4 = 0; tmp$_4 !== $receiver.length; ++tmp$_4) {
        var element = $receiver[tmp$_4];
        if (equals(element.id, videoId)) {
          game.v = element;
        }
      }
      tmp$_0 = game.v;
      if (tmp$_0 == null) {
        return;
      }
      var usingGame = tmp$_0;
      this.tryPlayingAudioAgain_8be2vx$();
      if (!this.userController.user.isSubscribed && usingGame.premium) {
        this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$LOCK_getInstance());
        this.applicationListener.openSubscriptionView();
      }
       else {
        this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
        this.applicationListener.openGame_yetmo8$(usingGame, (tmp$_1 = this.kidsController.getSelectedKid()) != null ? tmp$_1.age : null);
      }
    }
     else {
      var video = {v: null};
      var $receiver_0 = Kotlin.isArray(tmp$_2 = this.folder_0.subcards) ? tmp$_2 : throwCCE();
      var tmp$_5;
      for (tmp$_5 = 0; tmp$_5 !== $receiver_0.length; ++tmp$_5) {
        var element_0 = $receiver_0[tmp$_5];
        if (equals(element_0.videoId, videoId)) {
          video.v = element_0;
        }
      }
      tmp$_3 = video.v;
      if (tmp$_3 == null) {
        return;
      }
      var usingVideo = tmp$_3;
      this.tryPlayingAudioAgain_8be2vx$();
      if (!this.userController.user.isSubscribed && usingVideo.isPremium) {
        this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$LOCK_getInstance());
        this.applicationListener.openSubscriptionView();
      }
       else {
        this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
        this.applicationListener.openVideoPlayer_9u1k3q$(usingVideo);
      }
    }
  };
  FolderViewController.prototype.videoCardWasClicked_61zpoe$ = function (videoId) {
    this.openVideo_0(videoId);
  };
  FolderViewController.prototype.videoCardFavoriteClicked_61zpoe$ = function (videoId) {
    var tmp$;
    this.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
    this.tryPlayingAudioAgain_8be2vx$();
    if (this.favoritesController.isFavorited_61zpoe$(videoId)) {
      this.favoritesController.removeFavorite_61zpoe$(videoId);
      return false;
    }
     else {
      var $receiver = Kotlin.isArray(tmp$ = this.folder_0.subcards) ? tmp$ : throwCCE();
      var tmp$_0;
      for (tmp$_0 = 0; tmp$_0 !== $receiver.length; ++tmp$_0) {
        var element = $receiver[tmp$_0];
        console.log(element);
        if (equals(element.videoId, videoId)) {
          this.favoritesController.addFavorite_9u1k3q$(element);
          return true;
        }
      }
    }
    console.log(this.favoritesController.isFavorited_61zpoe$(videoId));
    return this.favoritesController.isFavorited_61zpoe$(videoId);
  };
  function FolderViewController_init$lambda(this$FolderViewController, closure$applicationListener) {
    return function (event) {
      this$FolderViewController.tryPlayingAudioAgain_8be2vx$();
      this$FolderViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$FOLDER_CLOSE_getInstance());
      closure$applicationListener.goBack();
      event.stopImmediatePropagation();
      return Unit;
    };
  }
  FolderViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FolderViewController',
    interfaces: [VideoListCoordinator$Listener, BaseViewController]
  };
  function GameViewController(game, route, age, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.game_0 = game;
    var tmp$, tmp$_0;
    var gameBaseUrl = '';
    var age_0 = (tmp$_0 = (tmp$ = this.kidsController.getSelectedKid()) != null ? tmp$.age : null) != null ? tmp$_0 : 0;
    var imageBucket = this.platformDependencyProvider.userConfig.imageBucket;
    var gameUrl = this.game_0.getGameUrl_19mbxw$(age_0, imageBucket);
    println(gameUrl);
    var viewCoordinator = new GameViewCoordinator(this.view, gameUrl);
  }
  GameViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'GameViewController',
    interfaces: [BaseViewController]
  };
  function LockoutViewController(parentRoute, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, parentRoute, applicationListener, stringCheese, dependencyContainer);
    this.isBelowWaterTimer_dzg035$_0 = false;
    this.isModal_f6msf$_0 = true;
    this.blurBackgroundView_fn0g5y$_0 = true;
    this.viewCoordinator_0 = new OvertimeViewCoordinator(stringCheese, this.userConfig.getBucketAssetUrl(), this.view, LockoutViewController$viewCoordinator$lambda(this, applicationListener));
  }
  Object.defineProperty(LockoutViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_dzg035$_0;
    }
  });
  Object.defineProperty(LockoutViewController.prototype, 'isModal', {
    get: function () {
      return this.isModal_f6msf$_0;
    }
  });
  Object.defineProperty(LockoutViewController.prototype, 'blurBackgroundView', {
    get: function () {
      return this.blurBackgroundView_fn0g5y$_0;
    }
  });
  function LockoutViewController$viewCoordinator$lambda$lambda(this$LockoutViewController, closure$applicationListener) {
    return function (modalResult) {
      this$LockoutViewController.tryPlayingAudioAgain_8be2vx$();
      if (modalResult === ModalResult$SUCCESS_getInstance()) {
        this$LockoutViewController.settingsController.resetScreenTimeWatched_dzjtkx$();
        closure$applicationListener.closeTopModal();
      }
      return Unit;
    };
  }
  function LockoutViewController$viewCoordinator$lambda(this$LockoutViewController, closure$applicationListener) {
    return function (event) {
      this$LockoutViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      closure$applicationListener.openAgeGate_5q5pme$(this$LockoutViewController.route, LockoutViewController$viewCoordinator$lambda$lambda(this$LockoutViewController, closure$applicationListener));
      return Unit;
    };
  }
  LockoutViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'LockoutViewController',
    interfaces: [BaseViewController]
  };
  function SettingsViewController(hasParentalPermission, route, stringCheese, applicationListener, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.hasParentalPermission_0 = hasParentalPermission;
    this.isBelowWaterTimer_ohxhox$_0 = false;
    this.blurBackgroundView_q5lhoq$_0 = true;
    this.coordinator_0 = new SettingsViewCoordinator(this.view, stringCheese, dependencyContainer, SettingsViewController$coordinator$lambda(this));
    this.coordinator_0.closeButton.onclick = SettingsViewController_init$lambda(this, applicationListener);
  }
  Object.defineProperty(SettingsViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_ohxhox$_0;
    }
  });
  Object.defineProperty(SettingsViewController.prototype, 'blurBackgroundView', {
    get: function () {
      return this.blurBackgroundView_q5lhoq$_0;
    }
  });
  SettingsViewController.prototype.fullscreenChanged_6taknv$ = function (isFullScreen) {
    BaseViewController.prototype.fullscreenChanged_6taknv$.call(this, isFullScreen);
    this.coordinator_0.extraPanel.setSelected_ivxn3r$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_FULL_SCREEN, isFullScreen);
  };
  function SettingsViewController$setSetting$lambda(this$SettingsViewController, closure$type, closure$result) {
    return function (modalResult) {
      if (modalResult === ModalResult$SUCCESS_getInstance()) {
        this$SettingsViewController.hasParentalPermission_0 = true;
        this$SettingsViewController.setSetting_0(closure$type, closure$result);
      }
      return Unit;
    };
  }
  SettingsViewController.prototype.setSetting_0 = function (type, result) {
    var tmp$;
    this.tryPlayingAudioAgain_8be2vx$();
    if (!this.hasParentalPermission_0) {
      this.applicationListener.openAgeGate_5q5pme$(this.route, SettingsViewController$setSetting$lambda(this, type, result));
      return;
    }
    println(type);
    switch (type.name) {
      case 'AGE':
        var age = -1;
        try {
          age = toInt(result);
        }
         catch (e) {
          if (!Kotlin.isType(e, Exception))
            throw e;
        }

        if (age === -1 || age > 7 || age < 1)
          return;
        this.kidsController.updateKid_bm4lxs$(this.kidsController.getSelectedKidId(), age);
        this.updateSelected_0(type, result);
        break;
      case 'TIME_LIMIT':
        var timeLimit;
        try {
          timeLimit = toInt(result);
        }
         catch (e) {
          if (Kotlin.isType(e, Exception)) {
            return;
          }
           else
            throw e;
        }

        this.settingsController.screenTimeLimitInMinutes = timeLimit;
        this.updateSelected_0(type, result);
        break;
      case 'CONTENT':
        var contentType = Folder$ContentType$Companion_getInstance().FromRaw_61zpoe$(result);
        this.settingsController.contentType = contentType;
        this.updateSelected_0(type, result);
        break;
      case 'LANGUAGE':
        tmp$ = Language$Companion_getInstance().FromShortName_61zpoe$(result);
        if (tmp$ == null) {
          return;
        }

        var language = tmp$;
        this.settingsController.language = language;
        this.updateSelected_0(type, result);
        break;
      case 'EXTRA':
        if (equals(result, SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_FULL_SCREEN)) {
          this.applicationListener.changeFullScreen_6taknv$(!isFullScreenEnabled(document));
        }
         else if (equals(result, SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_SOUND)) {
          this.settingsController.allowSound = !this.settingsController.allowSound;
          this.audioController.changeMusicState_6taknv$(this.settingsController.allowSound);
          this.coordinator_0.extraPanel.setSelected_ivxn3r$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_SOUND, this.settingsController.allowSound);
        }
         else if (equals(result, SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_PRIVACY_POLICY)) {
          this.applicationListener.linkToPrivacyPolicy();
        }
         else if (equals(result, SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_UNSUBSCRIBE)) {
          this.applicationListener.unsubscribe();
          localStorage.setItem('u_activeSubscription', 'false');
        }

        break;
      default:Kotlin.noWhenBranchMatched();
        break;
    }
  };
  SettingsViewController.prototype.updateSelected_0 = function (type, result) {
    this.coordinator_0.moveSelected_caq4b4$(type, result);
  };
  SettingsViewController.prototype.onWillAppear = function () {
    var tmp$, tmp$_0;
    BaseViewController.prototype.onWillAppear.call(this);
    this.coordinator_0.moveSelected_caq4b4$(SettingsPanelViewCoordinator$PanelType$AGE_getInstance(), ((tmp$_0 = (tmp$ = this.kidsController.getSelectedKid()) != null ? tmp$.age : null) != null ? tmp$_0 : 4).toString());
    this.coordinator_0.moveSelected_caq4b4$(SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_getInstance(), this.settingsController.screenTimeLimitInMinutes.toString());
    this.coordinator_0.moveSelected_caq4b4$(SettingsPanelViewCoordinator$PanelType$CONTENT_getInstance(), this.settingsController.contentType.raw);
    this.coordinator_0.moveSelected_caq4b4$(SettingsPanelViewCoordinator$PanelType$LANGUAGE_getInstance(), this.settingsController.language.shortName);
    this.coordinator_0.extraPanel.setSelected_ivxn3r$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_FULL_SCREEN, isFullScreenEnabled(document));
    this.coordinator_0.extraPanel.setSelected_ivxn3r$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_SOUND, this.settingsController.allowSound);
  };
  function SettingsViewController$coordinator$lambda(this$SettingsViewController) {
    return function (type, result) {
      this$SettingsViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      this$SettingsViewController.setSetting_0(type, result);
      return Unit;
    };
  }
  function SettingsViewController_init$lambda(this$SettingsViewController, closure$applicationListener) {
    return function (event) {
      event.stopImmediatePropagation();
      this$SettingsViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      closure$applicationListener.goBack();
      return Unit;
    };
  }
  SettingsViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SettingsViewController',
    interfaces: [BaseViewController]
  };
  function SubscriptionViewController(route, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.isBelowWaterTimer_2b5vg5$_0 = false;
    this.viewCoordinator_0 = new SubscriptionViewCoordinator(stringCheese, this.view);
    this.viewCoordinator_0.closeButton.onclick = SubscriptionViewController_init$lambda(this, applicationListener);
    this.viewCoordinator_0.subscribeButton.onclick = SubscriptionViewController_init$lambda_0(this, applicationListener);
    this.viewCoordinator_0.loginButton.onclick = SubscriptionViewController_init$lambda_1(this, applicationListener);
  }
  Object.defineProperty(SubscriptionViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_2b5vg5$_0;
    }
  });
  SubscriptionViewController.prototype.onWillAppear = function () {
    BaseViewController.prototype.onWillAppear.call(this);
    if (this.userController.user.isSubscribed)
      this.applicationListener.goBack();
  };
  SubscriptionViewController.prototype.getState = function () {
    return {};
  };
  function SubscriptionViewController_init$lambda(this$SubscriptionViewController, closure$applicationListener) {
    return function (event) {
      this$SubscriptionViewController.tryPlayingAudioAgain_8be2vx$();
      this$SubscriptionViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      closure$applicationListener.goBack();
      event.stopImmediatePropagation();
      return Unit;
    };
  }
  function SubscriptionViewController_init$lambda_0(this$SubscriptionViewController, closure$applicationListener) {
    return function (event) {
      this$SubscriptionViewController.tryPlayingAudioAgain_8be2vx$();
      this$SubscriptionViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      closure$applicationListener.linkToAccountRegister();
      event.stopImmediatePropagation();
      return Unit;
    };
  }
  function SubscriptionViewController_init$lambda_1(this$SubscriptionViewController, closure$applicationListener) {
    return function (event) {
      this$SubscriptionViewController.tryPlayingAudioAgain_8be2vx$();
      this$SubscriptionViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      closure$applicationListener.linkToAccountLogin();
      event.stopImmediatePropagation();
      return Unit;
    };
  }
  SubscriptionViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SubscriptionViewController',
    interfaces: [BaseViewController]
  };
  var AMOUNT_WATCHED_TIL_STORED;
  function VideoPlayerViewController(video, route, applicationListener, stringCheese, dependencyContainer) {
    BaseViewController.call(this, route, applicationListener, stringCheese, dependencyContainer);
    this.video_0 = video;
    this.isBelowWaterTimer_ehv9h2$_0 = false;
    this.shouldCheckOvertime_jmgdvv$_0 = true;
    this.shouldPlayMusic_ws7ikz$_0 = false;
    this.viewCoordinator_0 = null;
    this.shakaPlayer_0 = null;
    this.usingFormat_0 = null;
    this.isSeeking_0 = false;
    this.lastTimeOfVideo_0 = 0.0;
    this.currentAmountWatched_0 = 0.0;
    this.usingFormat_0 = this.video_0.getBestFormat_empd58$(window.innerHeight, this.userConfig);
    console.log('format', this.usingFormat_0);
    console.log('videoUrl', this.deviceData.videoUrl);
    var videoSrc = this.deviceData.videoUrl + Video$Format$Companion_getInstance().GetVideoUrlWithFormat_8n7phv$(this.video_0.videoId, this.usingFormat_0);
    this.viewCoordinator_0 = new VideoPlayerViewCoordinator(this.view);
    if (!this.usingFormat_0.isAdaptiveStreaming() || this.userConfig.mobileSafari) {
      this.shakaPlayer_0 = null;
      this.viewCoordinator_0.videoFrame.src = videoSrc;
    }
     else {
      this.shakaPlayer_0 = createShakaPlayer(this.viewCoordinator_0.videoFrame);
      this.shakaPlayer_0.load(videoSrc);
    }
    this.viewCoordinator_0.closeButton.onclick = VideoPlayerViewController_init$lambda(this, applicationListener);
    this.viewCoordinator_0.videoFrame.onseeking = VideoPlayerViewController_init$lambda_0(this);
    this.viewCoordinator_0.videoFrame.onseeked = VideoPlayerViewController_init$lambda_1(this);
    this.viewCoordinator_0.videoFrame.ontimeupdate = VideoPlayerViewController_init$lambda_2(this);
  }
  Object.defineProperty(VideoPlayerViewController.prototype, 'isBelowWaterTimer', {
    get: function () {
      return this.isBelowWaterTimer_ehv9h2$_0;
    }
  });
  Object.defineProperty(VideoPlayerViewController.prototype, 'shouldCheckOvertime', {
    get: function () {
      return this.shouldCheckOvertime_jmgdvv$_0;
    }
  });
  Object.defineProperty(VideoPlayerViewController.prototype, 'shouldPlayMusic', {
    get: function () {
      return this.shouldPlayMusic_ws7ikz$_0;
    }
  });
  VideoPlayerViewController.prototype.logCurrentAmount_0 = function () {
    this.settingsController.addScreenTimeWatched_14dthe$(this.currentAmountWatched_0);
    this.currentAmountWatched_0 = 0.0;
    this.checkIfOverTime_8be2vx$();
  };
  VideoPlayerViewController.prototype.onWillAppear = function () {
    BaseViewController.prototype.onWillAppear.call(this);
    this.viewCoordinator_0.videoFrame.autoplay = true;
    this.viewCoordinator_0.videoFrame.play();
  };
  VideoPlayerViewController.prototype.onWillDisappear = function () {
    BaseViewController.prototype.onWillDisappear.call(this);
    this.viewCoordinator_0.videoFrame.autoplay = false;
    this.viewCoordinator_0.videoFrame.pause();
  };
  VideoPlayerViewController.prototype.getState = function () {
    var tmp$;
    var json = {};
    var videoJson = toJson_video(this.jsonFactoryV3, this.video_0);
    json['video'] = videoJson;
    return Kotlin.isType(tmp$ = json, Object) ? tmp$ : throwCCE();
  };
  function VideoPlayerViewController_init$lambda(this$VideoPlayerViewController, closure$applicationListener) {
    return function (event) {
      this$VideoPlayerViewController.audioController.playSoundEffect_8htko7$(AudioController$SoundEffects$GENERAL_BUTTON_getInstance());
      closure$applicationListener.goBack();
      return Unit;
    };
  }
  function VideoPlayerViewController_init$lambda_0(this$VideoPlayerViewController) {
    return function (event) {
      if (!this$VideoPlayerViewController.isSeeking_0) {
        this$VideoPlayerViewController.isSeeking_0 = true;
        this$VideoPlayerViewController.logCurrentAmount_0();
      }
      return Unit;
    };
  }
  function VideoPlayerViewController_init$lambda_1(this$VideoPlayerViewController) {
    return function (event) {
      this$VideoPlayerViewController.lastTimeOfVideo_0 = this$VideoPlayerViewController.viewCoordinator_0.videoFrame.currentTime;
      this$VideoPlayerViewController.isSeeking_0 = false;
      return Unit;
    };
  }
  function VideoPlayerViewController_init$lambda_2(this$VideoPlayerViewController) {
    return function (event) {
      if (this$VideoPlayerViewController.viewState !== BaseViewController$ViewState$VISIBLE_getInstance()) {
        this$VideoPlayerViewController.viewCoordinator_0.videoFrame.pause();
      }
       else {
        if (!this$VideoPlayerViewController.isSeeking_0) {
          var currentTime = this$VideoPlayerViewController.viewCoordinator_0.videoFrame.currentTime;
          if (currentTime > this$VideoPlayerViewController.lastTimeOfVideo_0) {
            this$VideoPlayerViewController.currentAmountWatched_0 += currentTime - this$VideoPlayerViewController.lastTimeOfVideo_0;
          }
          this$VideoPlayerViewController.lastTimeOfVideo_0 = currentTime;
          if (this$VideoPlayerViewController.currentAmountWatched_0 >= AMOUNT_WATCHED_TIL_STORED)
            this$VideoPlayerViewController.logCurrentAmount_0();
        }
      }
      return Unit;
    };
  }
  VideoPlayerViewController.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'VideoPlayerViewController',
    interfaces: [BaseViewController]
  };
  function createShakaPlayer(element) {
    return new window.shaka.Player(element);
  }
  function AgeGateViewCoordinator(stringCheese, onButtonClick, view, userConfig, deviceData) {
    AgeGateViewCoordinator$Companion_getInstance();
    this.view = view;
    this.closeButton = createDiv(document);
    this.closeButtonPortrait = createDiv(document);
    this.resultText = createDiv(document);
    this.resultTextBackground = createDiv(document);
    var container = createDiv(document);
    var bottomContainer = createDiv(document);
    var textContainer = createDiv(document);
    var resultContainer = createDiv(document);
    var topText1 = createDiv(document);
    var topText2 = createDiv(document);
    var infoText = createDiv(document);
    var buttonContainer = createDiv(document);
    var buttonPad = createDiv(document);
    this.view.classList.add('age-gate-view-container');
    container.classList.add('age-gate-card-container');
    bottomContainer.classList.add('age-gate-bottom-container');
    this.closeButton.classList.add('age-gate-close');
    this.closeButtonPortrait.classList.add('age-gate-close', 'close-portrait');
    textContainer.classList.add('age-gate-text-container');
    topText1.classList.add('age-gate-top-text1');
    topText2.classList.add('age-gate-top-text2');
    resultContainer.classList.add('age-gate-result-container');
    this.resultTextBackground.classList.add('age-gate-result-background');
    this.resultText.classList.add('age-gate-result-text');
    infoText.classList.add('age-gate-info-text');
    buttonContainer.classList.add('age-gate-button-container');
    buttonPad.classList.add('age-gate-button-pad');
    var buttonContainer_0 = createDiv(document);
    buttonContainer_0.classList.add('button-container');
    if (!equals('1', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_0.onclick = onButtonClick;
      buttonContainer_0.setAttribute('index', '1');
      var innerContainer = createDiv(document);
      if (equals('1', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer.classList.add('delete-button');
      }
       else {
        innerContainer.classList.add('num');
        innerContainer.classList.add('number-container');
        var text = createDiv(document);
        text.classList.add('number');
        text.innerHTML = '1';
        innerContainer.appendChild(text);
      }
      buttonContainer_0.appendChild(innerContainer);
    }
     else {
      buttonContainer_0.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_0);
    var buttonContainer_1 = createDiv(document);
    buttonContainer_1.classList.add('button-container');
    if (!equals('2', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_1.onclick = onButtonClick;
      buttonContainer_1.setAttribute('index', '2');
      var innerContainer_0 = createDiv(document);
      if (equals('2', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_0.classList.add('delete-button');
      }
       else {
        innerContainer_0.classList.add('num');
        innerContainer_0.classList.add('number-container');
        var text_0 = createDiv(document);
        text_0.classList.add('number');
        text_0.innerHTML = '2';
        innerContainer_0.appendChild(text_0);
      }
      buttonContainer_1.appendChild(innerContainer_0);
    }
     else {
      buttonContainer_1.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_1);
    var buttonContainer_2 = createDiv(document);
    buttonContainer_2.classList.add('button-container');
    if (!equals('3', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_2.onclick = onButtonClick;
      buttonContainer_2.setAttribute('index', '3');
      var innerContainer_1 = createDiv(document);
      if (equals('3', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_1.classList.add('delete-button');
      }
       else {
        innerContainer_1.classList.add('num');
        innerContainer_1.classList.add('number-container');
        var text_1 = createDiv(document);
        text_1.classList.add('number');
        text_1.innerHTML = '3';
        innerContainer_1.appendChild(text_1);
      }
      buttonContainer_2.appendChild(innerContainer_1);
    }
     else {
      buttonContainer_2.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_2);
    var buttonContainer_3 = createDiv(document);
    buttonContainer_3.classList.add('button-container');
    if (!equals('4', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_3.onclick = onButtonClick;
      buttonContainer_3.setAttribute('index', '4');
      var innerContainer_2 = createDiv(document);
      if (equals('4', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_2.classList.add('delete-button');
      }
       else {
        innerContainer_2.classList.add('num');
        innerContainer_2.classList.add('number-container');
        var text_2 = createDiv(document);
        text_2.classList.add('number');
        text_2.innerHTML = '4';
        innerContainer_2.appendChild(text_2);
      }
      buttonContainer_3.appendChild(innerContainer_2);
    }
     else {
      buttonContainer_3.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_3);
    var buttonContainer_4 = createDiv(document);
    buttonContainer_4.classList.add('button-container');
    if (!equals('5', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_4.onclick = onButtonClick;
      buttonContainer_4.setAttribute('index', '5');
      var innerContainer_3 = createDiv(document);
      if (equals('5', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_3.classList.add('delete-button');
      }
       else {
        innerContainer_3.classList.add('num');
        innerContainer_3.classList.add('number-container');
        var text_3 = createDiv(document);
        text_3.classList.add('number');
        text_3.innerHTML = '5';
        innerContainer_3.appendChild(text_3);
      }
      buttonContainer_4.appendChild(innerContainer_3);
    }
     else {
      buttonContainer_4.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_4);
    var buttonContainer_5 = createDiv(document);
    buttonContainer_5.classList.add('button-container');
    if (!equals('6', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_5.onclick = onButtonClick;
      buttonContainer_5.setAttribute('index', '6');
      var innerContainer_4 = createDiv(document);
      if (equals('6', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_4.classList.add('delete-button');
      }
       else {
        innerContainer_4.classList.add('num');
        innerContainer_4.classList.add('number-container');
        var text_4 = createDiv(document);
        text_4.classList.add('number');
        text_4.innerHTML = '6';
        innerContainer_4.appendChild(text_4);
      }
      buttonContainer_5.appendChild(innerContainer_4);
    }
     else {
      buttonContainer_5.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_5);
    var buttonContainer_6 = createDiv(document);
    buttonContainer_6.classList.add('button-container');
    if (!equals('7', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_6.onclick = onButtonClick;
      buttonContainer_6.setAttribute('index', '7');
      var innerContainer_5 = createDiv(document);
      if (equals('7', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_5.classList.add('delete-button');
      }
       else {
        innerContainer_5.classList.add('num');
        innerContainer_5.classList.add('number-container');
        var text_5 = createDiv(document);
        text_5.classList.add('number');
        text_5.innerHTML = '7';
        innerContainer_5.appendChild(text_5);
      }
      buttonContainer_6.appendChild(innerContainer_5);
    }
     else {
      buttonContainer_6.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_6);
    var buttonContainer_7 = createDiv(document);
    buttonContainer_7.classList.add('button-container');
    if (!equals('8', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_7.onclick = onButtonClick;
      buttonContainer_7.setAttribute('index', '8');
      var innerContainer_6 = createDiv(document);
      if (equals('8', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_6.classList.add('delete-button');
      }
       else {
        innerContainer_6.classList.add('num');
        innerContainer_6.classList.add('number-container');
        var text_6 = createDiv(document);
        text_6.classList.add('number');
        text_6.innerHTML = '8';
        innerContainer_6.appendChild(text_6);
      }
      buttonContainer_7.appendChild(innerContainer_6);
    }
     else {
      buttonContainer_7.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_7);
    var buttonContainer_8 = createDiv(document);
    buttonContainer_8.classList.add('button-container');
    if (!equals('9', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_8.onclick = onButtonClick;
      buttonContainer_8.setAttribute('index', '9');
      var innerContainer_7 = createDiv(document);
      if (equals('9', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_7.classList.add('delete-button');
      }
       else {
        innerContainer_7.classList.add('num');
        innerContainer_7.classList.add('number-container');
        var text_7 = createDiv(document);
        text_7.classList.add('number');
        text_7.innerHTML = '9';
        innerContainer_7.appendChild(text_7);
      }
      buttonContainer_8.appendChild(innerContainer_7);
    }
     else {
      buttonContainer_8.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_8);
    var indexString = AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS;
    var buttonContainer_9 = createDiv(document);
    buttonContainer_9.classList.add('button-container');
    if (!equals(indexString, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_9.onclick = onButtonClick;
      buttonContainer_9.setAttribute('index', indexString);
      var innerContainer_8 = createDiv(document);
      if (equals(indexString, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_8.classList.add('delete-button');
      }
       else {
        innerContainer_8.classList.add('num');
        innerContainer_8.classList.add('number-container');
        var text_8 = createDiv(document);
        text_8.classList.add('number');
        text_8.innerHTML = indexString;
        innerContainer_8.appendChild(text_8);
      }
      buttonContainer_9.appendChild(innerContainer_8);
    }
     else {
      buttonContainer_9.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_9);
    var buttonContainer_10 = createDiv(document);
    buttonContainer_10.classList.add('button-container');
    if (!equals('0', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_10.onclick = onButtonClick;
      buttonContainer_10.setAttribute('index', '0');
      var innerContainer_9 = createDiv(document);
      if (equals('0', AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_9.classList.add('delete-button');
      }
       else {
        innerContainer_9.classList.add('num');
        innerContainer_9.classList.add('number-container');
        var text_9 = createDiv(document);
        text_9.classList.add('number');
        text_9.innerHTML = '0';
        innerContainer_9.appendChild(text_9);
      }
      buttonContainer_10.appendChild(innerContainer_9);
    }
     else {
      buttonContainer_10.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_10);
    var indexString_0 = AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL;
    var buttonContainer_11 = createDiv(document);
    buttonContainer_11.classList.add('button-container');
    if (!equals(indexString_0, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer_11.onclick = onButtonClick;
      buttonContainer_11.setAttribute('index', indexString_0);
      var innerContainer_10 = createDiv(document);
      if (equals(indexString_0, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer_10.classList.add('delete-button');
      }
       else {
        innerContainer_10.classList.add('num');
        innerContainer_10.classList.add('number-container');
        var text_10 = createDiv(document);
        text_10.classList.add('number');
        text_10.innerHTML = indexString_0;
        innerContainer_10.appendChild(text_10);
      }
      buttonContainer_11.appendChild(innerContainer_10);
    }
     else {
      buttonContainer_11.classList.add('button-container-invis');
    }
    buttonPad.appendChild(buttonContainer_11);
    topText1.innerHTML = stringCheese.ageGateTitle;
    topText2.innerHTML = stringCheese.ageGateSubtitle;
    infoText.innerHTML = stringCheese.ageGateAgeSubtext;
    this.view.appendChild(container);
    container.appendChild(this.closeButtonPortrait);
    container.appendChild(bottomContainer);
    bottomContainer.appendChild(textContainer);
    textContainer.appendChild(topText1);
    textContainer.appendChild(topText2);
    textContainer.appendChild(resultContainer);
    resultContainer.appendChild(this.resultTextBackground);
    this.resultTextBackground.appendChild(this.resultText);
    textContainer.appendChild(infoText);
    bottomContainer.appendChild(buttonContainer);
    buttonContainer.appendChild(this.closeButton);
    buttonContainer.appendChild(buttonPad);
  }
  AgeGateViewCoordinator.prototype.addButton_0 = function (indexString, onButtonClick, container) {
    var buttonContainer = createDiv(document);
    buttonContainer.classList.add('button-container');
    if (!equals(indexString, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_INVIS)) {
      buttonContainer.onclick = onButtonClick;
      buttonContainer.setAttribute('index', indexString);
      var innerContainer = createDiv(document);
      if (equals(indexString, AgeGateViewCoordinator$Companion_getInstance().BUTTON_INDEX_DEL)) {
        innerContainer.classList.add('delete-button');
      }
       else {
        innerContainer.classList.add('num');
        innerContainer.classList.add('number-container');
        var text = createDiv(document);
        text.classList.add('number');
        text.innerHTML = indexString;
        innerContainer.appendChild(text);
      }
      buttonContainer.appendChild(innerContainer);
    }
     else {
      buttonContainer.classList.add('button-container-invis');
    }
    container.appendChild(buttonContainer);
  };
  function AgeGateViewCoordinator$Companion() {
    AgeGateViewCoordinator$Companion_instance = this;
    this.BUTTON_INDEX_DEL = 'del';
    this.BUTTON_INDEX_INVIS = 'invis';
  }
  AgeGateViewCoordinator$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var AgeGateViewCoordinator$Companion_instance = null;
  function AgeGateViewCoordinator$Companion_getInstance() {
    if (AgeGateViewCoordinator$Companion_instance === null) {
      new AgeGateViewCoordinator$Companion();
    }
    return AgeGateViewCoordinator$Companion_instance;
  }
  AgeGateViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'AgeGateViewCoordinator',
    interfaces: []
  };
  var BUFFER_SIZE;
  function CardSelectionViewCoordinator(stringCheese, userConfig, deviceData, view) {
    this.userConfig_0 = userConfig;
    this.deviceData_0 = deviceData;
    this.view = view;
    this.cardElements_0 = ArrayList_init();
    this.cardList = createDiv(document);
    this.baseFolderImageUrl_0 = this.setUpBaseFolderImageUrl_0;
    this.cardListWidthPlusBuffer_89iihv$_0 = 0;
    this.cardListHeightPlusBuffer_w5gv5c$_0 = 0;
    this.view.classList.add('card-selection-container');
    this.cardList.classList.add('card-list');
    this.view.style.backgroundImage = "url('" + this.userConfig_0.getImageAssetUrl_61zpoe$('background_day_bottom.jpg') + "')";
    this.view.appendChild(this.cardList);
    this.cardListWidthPlusBuffer = this.cardList.clientWidth + numberToInt(window.innerWidth * BUFFER_SIZE) | 0;
    this.cardListHeightPlusBuffer = this.cardList.clientHeight + numberToInt(window.innerHeight * BUFFER_SIZE) | 0;
  }
  Object.defineProperty(CardSelectionViewCoordinator.prototype, 'setUpBaseFolderImageUrl_0', {
    get: function () {
      return this.deviceData_0.folderImageUrl + 'folderImage/' + this.userConfig_0.imageBucket;
    }
  });
  Object.defineProperty(CardSelectionViewCoordinator.prototype, 'cardListWidthPlusBuffer', {
    get: function () {
      return this.cardListWidthPlusBuffer_89iihv$_0;
    },
    set: function (cardListWidthPlusBuffer) {
      this.cardListWidthPlusBuffer_89iihv$_0 = cardListWidthPlusBuffer;
    }
  });
  Object.defineProperty(CardSelectionViewCoordinator.prototype, 'cardListHeightPlusBuffer', {
    get: function () {
      return this.cardListHeightPlusBuffer_w5gv5c$_0;
    },
    set: function (cardListHeightPlusBuffer) {
      this.cardListHeightPlusBuffer_w5gv5c$_0 = cardListHeightPlusBuffer;
    }
  });
  CardSelectionViewCoordinator.prototype.onWillAppear = function () {
    this.baseFolderImageUrl_0 = this.setUpBaseFolderImageUrl_0;
    this.cardListWidthPlusBuffer = this.cardList.clientWidth + numberToInt(window.innerWidth * BUFFER_SIZE) | 0;
    this.cardListHeightPlusBuffer = this.cardList.clientHeight + numberToInt(window.innerHeight * BUFFER_SIZE) | 0;
  };
  CardSelectionViewCoordinator.prototype.clearCardList = function () {
    this.cardElements_0.clear();
    this.cardList.innerHTML = '';
  };
  CardSelectionViewCoordinator.prototype.addCards_c4njs0$ = function (cards, onclick) {
    if (cards.length === 0)
      return;
    var startingIndex = this.cardElements_0.size;
    var tmp$, tmp$_0;
    var index = 0;
    for (tmp$ = 0; tmp$ !== cards.length; ++tmp$) {
      var item = cards[tmp$];
      var position = (tmp$_0 = index, index = tmp$_0 + 1 | 0, tmp$_0) + startingIndex | 0;
      var color = Folder$BackgroundColor$Companion_getInstance().FromCollectionIndex_za3lpa$(position);
      println(item);
      var element = CreateFolderCardView(this.userConfig_0.getBucketAssetUrlWithCDNUrl_puj7f4$(this.deviceData_0.folderImageUrl + 'folderImage/', item.id + '.png'), false, color);
      element.setAttribute('index', position.toString());
      element.onclick = onclick;
      this.cardElements_0.add_11rb$(element);
      this.cardList.appendChild(element);
    }
  };
  CardSelectionViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'CardSelectionViewCoordinator',
    interfaces: []
  };
  function FavoritesViewCoordinator(userConfig, stringCheese, videoListView, view) {
    this.userConfig_0 = userConfig;
    this.stringCheese_0 = stringCheese;
    this.view = view;
    this.view.classList.add('favorites-container');
    this.view.style.backgroundImage = "url('" + this.userConfig_0.getImageAssetUrl_61zpoe$('background_day_top.jpg') + "')";
    var favoritesTopContainer = createDiv(document);
    var favoritesHeart = createDiv(document);
    var favoritesTitle = createDiv(document);
    favoritesTopContainer.classList.add('favorites-top-container');
    favoritesHeart.classList.add('favorites-heart');
    favoritesTitle.classList.add('favorites-title');
    videoListView.classList.add('favorites-video-list');
    favoritesTitle.innerHTML = this.stringCheese_0.favoritesTitle;
    this.view.appendChild(favoritesTopContainer);
    favoritesTopContainer.appendChild(favoritesHeart);
    favoritesTopContainer.appendChild(favoritesTitle);
    this.view.appendChild(videoListView);
  }
  FavoritesViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FavoritesViewCoordinator',
    interfaces: []
  };
  function FolderCardViewCoordinator(isOpen, color) {
    if (color === void 0)
      color = Folder$BackgroundColor$RED_getInstance();
    this.view = createDiv(document);
    this.image_0 = createDiv(document);
    this.view.classList.add('folder-card', 'card-' + color.colorName, isOpen ? 'open' : 'close');
    this.image_0.classList.add('folder-image');
    this.view.appendChild(this.image_0);
  }
  FolderCardViewCoordinator.prototype.loadInImage_61zpoe$ = function (imageUrl) {
    this.image_0.style.backgroundImage = "url('" + imageUrl + "')";
  };
  FolderCardViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FolderCardViewCoordinator',
    interfaces: []
  };
  function CreateFolderCardView(imageUrl, isOpen, color) {
    var card = createDiv(document);
    var image = createDiv(document);
    card.classList.add('folder-card', 'card-' + color.colorName, isOpen ? 'open' : 'close');
    image.classList.add('folder-image');
    console.log(imageUrl);
    image.style.backgroundImage = "url('" + imageUrl + "')";
    card.appendChild(image);
    return card;
  }
  function FolderViewCoordinator(folder, backgroundColor, isSubscribed, userConfig, deviceData, stringCheese, videoList, view) {
    this.isSubscribed_0 = isSubscribed;
    this.userConfig_0 = userConfig;
    this.deviceData_0 = deviceData;
    this.view = view;
    this.folderView = new FolderCardViewCoordinator(true, backgroundColor);
    this.closeButton = createDiv(document);
    this.view.classList.add('open-folder-view-container');
    var topSection = createDiv(document);
    window.onhashchange = FolderViewCoordinator_init$lambda;
    topSection.classList.add('top-section');
    this.closeButton.classList.add('close-button');
    videoList.classList.add('folder-video-list');
    topSection.appendChild(this.folderView.view);
    topSection.appendChild(this.closeButton);
    this.view.appendChild(topSection);
    this.view.appendChild(videoList);
    this.folderView.loadInImage_61zpoe$(this.userConfig_0.getBucketAssetUrlWithCDNUrl_puj7f4$(this.deviceData_0.folderImageUrl + 'folderImage/', folder.id + '.png'));
  }
  function FolderViewCoordinator_init$lambda(it) {
    var tmp$, tmp$_0;
    var containers = document.getElementsByClassName('side-bar-container');
    return (tmp$_0 = (tmp$ = containers[0]) != null ? tmp$.classList : null) != null ? (tmp$_0.remove('hidden'), Unit) : null;
  }
  FolderViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'FolderViewCoordinator',
    interfaces: []
  };
  function GameViewCoordinator(view, gameUrl) {
    this.view = view;
    this.closeButton = createDiv(document);
    var tmp$, tmp$_0, tmp$_1;
    this.iframe = Kotlin.isType(tmp$ = document.createElement('IFRAME'), HTMLIFrameElement) ? tmp$ : throwCCE();
    this.view.classList.add('game-view-container');
    var containers = document.getElementsByClassName('side-bar-container');
    (tmp$_1 = (tmp$_0 = containers[0]) != null ? tmp$_0.classList : null) != null ? (tmp$_1.add('hidden'), Unit) : null;
    var topSection = createDiv(document);
    topSection.classList.add('top-section');
    this.closeButton.classList.add('close-button');
    topSection.appendChild(this.closeButton);
    this.view.appendChild(topSection);
    this.iframe.allowFullscreen = true;
    this.iframe.scrolling = 'no';
    this.iframe.classList.add('game-iframe');
    this.iframe.src = gameUrl;
    this.view.appendChild(this.iframe);
    window.addEventListener('message', GameViewCoordinator_init$lambda);
    window.setTimeout(GameViewCoordinator_init$lambda_0(this), 10);
  }
  GameViewCoordinator.prototype.isBrowserChrome = function () {
    var userAgent = window.clientInformation.userAgent;
    return contains(userAgent, 'Chrome') && !contains(userAgent, 'Chromium');
  };
  GameViewCoordinator.prototype.isBrowserSafari = function () {
    var userAgent = window.clientInformation.userAgent;
    return contains(userAgent, 'Safari') && !contains(userAgent, 'Chromium') && !contains(userAgent, 'Chrome');
  };
  GameViewCoordinator.prototype.isBrowserFirefox = function () {
    var userAgent = window.clientInformation.userAgent;
    return contains(userAgent, 'Firefox') && !contains(userAgent, 'Seamonkey');
  };
  function GameViewCoordinator_init$lambda(it) {
    var tmp$, tmp$_0;
    console.log('APP: Close clicked!');
    var containers = document.getElementsByClassName('side-bar-container');
    (tmp$_0 = (tmp$ = containers[0]) != null ? tmp$.classList : null) != null ? (tmp$_0.remove('hidden'), Unit) : null;
    document.exitFullscreen();
    window.history.back();
    return Unit;
  }
  function GameViewCoordinator_init$lambda_0(this$GameViewCoordinator) {
    return function () {
      var userAgent = window.clientInformation.userAgent;
      console.log('IsBrowserChrome', this$GameViewCoordinator.isBrowserChrome());
      console.log('IsBrowserSafari', this$GameViewCoordinator.isBrowserSafari());
      if (this$GameViewCoordinator.isBrowserChrome() || this$GameViewCoordinator.isBrowserSafari()) {
        document.documentElement.webkitRequestFullScreen();
      }
      if (this$GameViewCoordinator.isBrowserFirefox()) {
        document.documentElement.mozRequestFullScreen();
      }
      return Unit;
    };
  }
  GameViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'GameViewCoordinator',
    interfaces: []
  };
  function OvertimeViewCoordinator(stringCheese, imageBucketUrl, view, clicked) {
    this.clickableArea_0 = createDiv(document);
    this.wholeScreenIsClickable_0 = false;
    view.classList.add('overtime-modal-container');
    var image = createDiv(document);
    var title = createDiv(document);
    var button;
    if (this.wholeScreenIsClickable_0) {
      button = createDiv(document);
    }
     else {
      button = this.clickableArea_0;
    }
    var buttonTitle = createDiv(document);
    var buttonSubtitle = createDiv(document);
    image.classList.add('overtime-image');
    title.classList.add('overtime-title');
    button.classList.add('overtime-button');
    buttonTitle.classList.add('overtime-button-title');
    buttonSubtitle.classList.add('overtime-button-subtitle');
    image.style.backgroundImage = 'url(' + '"' + imageBucketUrl + 'overtime_moon_a.png' + '"' + ')';
    title.innerHTML = stringCheese.overtimeTitle;
    buttonTitle.innerHTML = stringCheese.overtimeButtonTitleLine1 + ' ' + stringCheese.overtimeButtonTitleLine2;
    buttonSubtitle.innerHTML = stringCheese.overtimeButtonSubtitle;
    view.appendChild(image);
    view.appendChild(title);
    view.appendChild(button);
    button.appendChild(buttonTitle);
    button.appendChild(buttonSubtitle);
    this.clickableArea_0.onclick = clicked;
  }
  OvertimeViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'OvertimeViewCoordinator',
    interfaces: []
  };
  function SettingsViewCoordinator(view, stringCheese, dependencyContainer, listener) {
    this.view = view;
    this.closeButton = createDiv(document);
    this.agePanel = null;
    this.timeLimitPanel = null;
    this.contentPanel = null;
    this.languagePanel = null;
    this.extraPanel = null;
    var tmp$;
    var topContainer = createDiv(document);
    var settingsTitle = createDiv(document);
    this.view.classList.add('settings-container');
    topContainer.classList.add('settings-top-container');
    settingsTitle.classList.add('settings-title');
    this.closeButton.classList.add('settings-close-button');
    settingsTitle.innerHTML = stringCheese.settingsNavigationTitle;
    var array = JsArray(7);
    var tmp$_0;
    tmp$_0 = array.length - 1 | 0;
    for (var i = 0; i <= tmp$_0; i++) {
      array[i] = (i + 1 | 0).toString();
    }
    var ageInfo = array;
    this.agePanel = new SettingsPanelViewCoordinator(false, SettingsPanelViewCoordinator$PanelType$AGE_getInstance(), stringCheese.settingsAgeTitle, ageInfo, ageInfo, dependencyContainer, listener);
    var timeLimitTitles = [SettingsController$Companion_getInstance().SCREEN_LIMIT_OPTION_0_TEXT + ' ' + stringCheese.timeMinuteLongPlural, SettingsController$Companion_getInstance().SCREEN_LIMIT_OPTION_1_TEXT + ' ' + stringCheese.timeMinuteLongPlural, SettingsController$Companion_getInstance().SCREEN_LIMIT_OPTION_2_TEXT + ' ' + stringCheese.timeMinuteLongPlural, SettingsController$Companion_getInstance().SCREEN_LIMIT_OPTION_3_TEXT + ' ' + stringCheese.timeMinuteLongPlural, SettingsController$Companion_getInstance().SCREEN_LIMIT_OPTION_4_TEXT + ' ' + stringCheese.timeMinuteLongPlural, stringCheese.settingsScreenTimeLimitOptionOff];
    var timeLimitResults = [(5).toString(), (20).toString(), (40).toString(), (60).toString(), (90).toString(), (120).toString()];
    this.timeLimitPanel = new SettingsPanelViewCoordinator(false, SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_getInstance(), stringCheese.settingsScreenTimeLimitTitle, timeLimitTitles, timeLimitResults, dependencyContainer, listener);
    var contentTypeTitles = [stringCheese.contentTypeMixed, stringCheese.contentTypeEntertainment, stringCheese.contentTypeEducation];
    var contentTypeResults = [Folder$ContentType$MIXED_getInstance().raw, Folder$ContentType$ENTERTAINMENT_getInstance().raw, Folder$ContentType$EDUCATION_getInstance().raw];
    this.contentPanel = new SettingsPanelViewCoordinator(false, SettingsPanelViewCoordinator$PanelType$CONTENT_getInstance(), stringCheese.settingsContentTypeTitle, contentTypeTitles, contentTypeResults, dependencyContainer, listener);
    var languageTitles = [Language$ENGLISH_getInstance().nativeName, Language$SPANISH_getInstance().nativeName, Language$FRENCH_getInstance().nativeName, Language$PORTUGUESE_getInstance().nativeName];
    var languageResults = [Language$ENGLISH_getInstance().shortName, Language$SPANISH_getInstance().shortName, Language$FRENCH_getInstance().shortName, Language$PORTUGUESE_getInstance().shortName];
    this.languagePanel = new SettingsPanelViewCoordinator(false, SettingsPanelViewCoordinator$PanelType$LANGUAGE_getInstance(), stringCheese.settingsLanguageTitle, languageTitles, languageResults, dependencyContainer, listener);
    var extraTitles = ArrayList_init();
    var extraResults = ArrayList_init();
    extraTitles.add_11rb$(stringCheese.settingsSound);
    extraResults.add_11rb$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_SOUND);
    if (document.exitFullscreen != undefined) {
      extraTitles.add_11rb$(stringCheese.settingsFullScreen);
      extraResults.add_11rb$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_FULL_SCREEN);
    }
    extraTitles.add_11rb$(stringCheese.privacyPolicyTitle);
    extraResults.add_11rb$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_RESULT_PRIVACY_POLICY);
    if (((tmp$ = localStorage.getItem('u_activeSubscription')) != null ? toBoolean(tmp$) : null) === true) {
      extraTitles.add_11rb$(stringCheese.unsubscribe);
      extraResults.add_11rb$(SettingsPanelViewCoordinator$PanelType$Companion_getInstance().EXTRA_UNSUBSCRIBE);
    }
    this.extraPanel = new SettingsPanelViewCoordinator(true, SettingsPanelViewCoordinator$PanelType$EXTRA_getInstance(), stringCheese.settingsExtraTitle, copyToArray(extraTitles), copyToArray(extraResults), dependencyContainer, listener);
    var settingsPanelContainer = createDiv(document);
    settingsPanelContainer.classList.add('settings-panel-list');
    settingsPanelContainer.appendChild(this.agePanel.view);
    settingsPanelContainer.appendChild(this.timeLimitPanel.view);
    settingsPanelContainer.appendChild(this.contentPanel.view);
    settingsPanelContainer.appendChild(this.languagePanel.view);
    settingsPanelContainer.appendChild(this.extraPanel.view);
    this.view.appendChild(topContainer);
    topContainer.appendChild(settingsTitle);
    topContainer.appendChild(this.closeButton);
    this.view.appendChild(settingsPanelContainer);
  }
  SettingsViewCoordinator.prototype.moveSelected_caq4b4$ = function (type, selected) {
    switch (type.name) {
      case 'AGE':
        this.agePanel.setSelected_ivxn3r$(selected);
        break;
      case 'TIME_LIMIT':
        this.timeLimitPanel.setSelected_ivxn3r$(selected);
        break;
      case 'CONTENT':
        this.contentPanel.setSelected_ivxn3r$(selected);
        break;
      case 'LANGUAGE':
        this.languagePanel.setSelected_ivxn3r$(selected);
        break;
    }
  };
  SettingsViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SettingsViewCoordinator',
    interfaces: []
  };
  function SettingsPanelViewCoordinator(allowMultibleSelected, type, title, listOfText, listOfResults, dependencyContainer, listener) {
    this.allowMultibleSelected_0 = allowMultibleSelected;
    this.type_0 = type;
    this.listOfResults_0 = listOfResults;
    this.dependencyContainer_0 = dependencyContainer;
    this.listener_0 = listener;
    this.view = createDiv(document);
    this.innerView = createDiv(document);
    this.lastSelected_0 = -1;
    this.elements_0 = null;
    this.onClick_0 = SettingsPanelViewCoordinator$onClick$lambda(this);
    var user = this.dependencyContainer_0.userController.user;
    var panel = createDiv(document);
    this.view.classList.add('settings-panel-container');
    panel.classList.add('settings-panel');
    this.innerView.classList.add('settings-panel-inner');
    var titleElement = createDiv(document);
    titleElement.classList.add('settings-panel-title');
    titleElement.innerHTML = title;
    this.view.appendChild(panel);
    panel.appendChild(this.innerView);
    this.innerView.appendChild(titleElement);
    var elementList = createDiv(document);
    elementList.classList.add('settings-panel-element-list');
    this.innerView.appendChild(elementList);
    var num = listOfText.length;
    var array = JsArray(num);
    var tmp$;
    tmp$ = array.length - 1 | 0;
    for (var i = 0; i <= tmp$; i++) {
      var element = createDiv(document);
      var card = createDiv(document);
      var text = createDiv(document);
      var check = createDiv(document);
      element.classList.add('settings-panel-element');
      card.classList.add('panel-element-card');
      text.classList.add('panel-element-text');
      check.classList.add('panel-element-check');
      text.innerHTML = listOfText[i];
      if (i < this.listOfResults_0.length)
        element.setAttribute('index', this.listOfResults_0[i]);
      else
        element.setAttribute('index', 'NONE');
      element.onclick = this.onClick_0;
      element.appendChild(card);
      card.appendChild(text);
      card.appendChild(check);
      elementList.appendChild(element);
      array[i] = element;
    }
    this.elements_0 = array;
  }
  SettingsPanelViewCoordinator.prototype.setSelected_ivxn3r$ = function (result, isSelected) {
    if (isSelected === void 0)
      isSelected = true;
    var i = {v: -1};
    var $receiver = this.listOfResults_0;
    var tmp$, tmp$_0;
    var index = 0;
    for (tmp$ = 0; tmp$ !== $receiver.length; ++tmp$) {
      var item = $receiver[tmp$];
      var index_0 = (tmp$_0 = index, index = tmp$_0 + 1 | 0, tmp$_0);
      if (equals(item, result)) {
        i.v = index_0;
      }
    }
    if (i.v >= 0 && i.v < this.elements_0.length) {
      if (!this.allowMultibleSelected_0 && this.lastSelected_0 >= 0 && this.lastSelected_0 < this.elements_0.length)
        this.elements_0[this.lastSelected_0].classList.remove('selected');
      if (isSelected)
        addClass(this.elements_0[i.v], ['selected']);
      else
        this.elements_0[i.v].classList.remove('selected');
      this.lastSelected_0 = i.v;
    }
  };
  function SettingsPanelViewCoordinator$PanelType(name, ordinal) {
    Enum.call(this);
    this.name$ = name;
    this.ordinal$ = ordinal;
  }
  function SettingsPanelViewCoordinator$PanelType_initFields() {
    SettingsPanelViewCoordinator$PanelType_initFields = function () {
    };
    SettingsPanelViewCoordinator$PanelType$AGE_instance = new SettingsPanelViewCoordinator$PanelType('AGE', 0);
    SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_instance = new SettingsPanelViewCoordinator$PanelType('TIME_LIMIT', 1);
    SettingsPanelViewCoordinator$PanelType$CONTENT_instance = new SettingsPanelViewCoordinator$PanelType('CONTENT', 2);
    SettingsPanelViewCoordinator$PanelType$LANGUAGE_instance = new SettingsPanelViewCoordinator$PanelType('LANGUAGE', 3);
    SettingsPanelViewCoordinator$PanelType$EXTRA_instance = new SettingsPanelViewCoordinator$PanelType('EXTRA', 4);
    SettingsPanelViewCoordinator$PanelType$Companion_getInstance();
  }
  var SettingsPanelViewCoordinator$PanelType$AGE_instance;
  function SettingsPanelViewCoordinator$PanelType$AGE_getInstance() {
    SettingsPanelViewCoordinator$PanelType_initFields();
    return SettingsPanelViewCoordinator$PanelType$AGE_instance;
  }
  var SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_instance;
  function SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_getInstance() {
    SettingsPanelViewCoordinator$PanelType_initFields();
    return SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_instance;
  }
  var SettingsPanelViewCoordinator$PanelType$CONTENT_instance;
  function SettingsPanelViewCoordinator$PanelType$CONTENT_getInstance() {
    SettingsPanelViewCoordinator$PanelType_initFields();
    return SettingsPanelViewCoordinator$PanelType$CONTENT_instance;
  }
  var SettingsPanelViewCoordinator$PanelType$LANGUAGE_instance;
  function SettingsPanelViewCoordinator$PanelType$LANGUAGE_getInstance() {
    SettingsPanelViewCoordinator$PanelType_initFields();
    return SettingsPanelViewCoordinator$PanelType$LANGUAGE_instance;
  }
  var SettingsPanelViewCoordinator$PanelType$EXTRA_instance;
  function SettingsPanelViewCoordinator$PanelType$EXTRA_getInstance() {
    SettingsPanelViewCoordinator$PanelType_initFields();
    return SettingsPanelViewCoordinator$PanelType$EXTRA_instance;
  }
  function SettingsPanelViewCoordinator$PanelType$Companion() {
    SettingsPanelViewCoordinator$PanelType$Companion_instance = this;
    this.EXTRA_RESULT_SOUND = 'sound';
    this.EXTRA_RESULT_FULL_SCREEN = 'full_screen';
    this.EXTRA_RESULT_PRIVACY_POLICY = 'privacy';
    this.EXTRA_UNSUBSCRIBE = 'unsubscribe';
  }
  SettingsPanelViewCoordinator$PanelType$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var SettingsPanelViewCoordinator$PanelType$Companion_instance = null;
  function SettingsPanelViewCoordinator$PanelType$Companion_getInstance() {
    SettingsPanelViewCoordinator$PanelType_initFields();
    if (SettingsPanelViewCoordinator$PanelType$Companion_instance === null) {
      new SettingsPanelViewCoordinator$PanelType$Companion();
    }
    return SettingsPanelViewCoordinator$PanelType$Companion_instance;
  }
  SettingsPanelViewCoordinator$PanelType.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'PanelType',
    interfaces: [Enum]
  };
  function SettingsPanelViewCoordinator$PanelType$values() {
    return [SettingsPanelViewCoordinator$PanelType$AGE_getInstance(), SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_getInstance(), SettingsPanelViewCoordinator$PanelType$CONTENT_getInstance(), SettingsPanelViewCoordinator$PanelType$LANGUAGE_getInstance(), SettingsPanelViewCoordinator$PanelType$EXTRA_getInstance()];
  }
  SettingsPanelViewCoordinator$PanelType.values = SettingsPanelViewCoordinator$PanelType$values;
  function SettingsPanelViewCoordinator$PanelType$valueOf(name) {
    switch (name) {
      case 'AGE':
        return SettingsPanelViewCoordinator$PanelType$AGE_getInstance();
      case 'TIME_LIMIT':
        return SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_getInstance();
      case 'CONTENT':
        return SettingsPanelViewCoordinator$PanelType$CONTENT_getInstance();
      case 'LANGUAGE':
        return SettingsPanelViewCoordinator$PanelType$LANGUAGE_getInstance();
      case 'EXTRA':
        return SettingsPanelViewCoordinator$PanelType$EXTRA_getInstance();
      default:throwISE('No enum constant net.kidjo.app.js.views.SettingsPanelViewCoordinator.PanelType.' + name);
    }
  }
  SettingsPanelViewCoordinator$PanelType.valueOf_61zpoe$ = SettingsPanelViewCoordinator$PanelType$valueOf;
  function SettingsPanelViewCoordinator$onClick$lambda(this$SettingsPanelViewCoordinator) {
    return function (event) {
      var tmp$, tmp$_0;
      event.stopImmediatePropagation();
      var target = Kotlin.isType(tmp$ = event.currentTarget, HTMLDivElement) ? tmp$ : null;
      var results = (tmp$_0 = target != null ? target.getAttribute('index') : null) != null ? tmp$_0 : 'NONE1';
      this$SettingsPanelViewCoordinator.listener_0(this$SettingsPanelViewCoordinator.type_0, results);
      return Unit;
    };
  }
  SettingsPanelViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SettingsPanelViewCoordinator',
    interfaces: []
  };
  var ANIMATION_HEIGHT;
  var ANIMATION_WIDTH;
  var ANIMATION_ASPECT_RATIO_WIDTH_TO_HEIGHT;
  var ANIMATION_ASPECT_RATIO_HEIGHT_TO_WIDTH;
  function SideBarCoordinator(stringCheese, userConfig, deviceData) {
    this.stringCheese_0 = stringCheese;
    this.userConfig_0 = userConfig;
    this.deviceData_0 = deviceData;
    this.view = createDiv(document);
    this.timerAmountContainer_0 = createDiv(document);
    this.timerMinutesText_0 = createDiv(document);
    this.timerMinutesTitleText_0 = createDiv(document);
    this.timerInfinite_0 = createDiv(document);
    this.settingsButton = createDiv(document);
    this.favoritesButton = createDiv(document);
    this.waterImageContainer_0 = createDiv(document);
    var tmp$;
    this.waterAnimation_0 = Kotlin.isType(tmp$ = document.createElementNS('http://www.w3.org/2000/svg', 'svg'), SVGElement) ? tmp$ : throwCCE();
    this.animationObject_0 = null;
    this.lastTimerMinutes_0 = -1;
    this.lastTimerPosition_0 = 0.0;
    this.lastTimerWasInfinite_0 = false;
    this.lastZIndex_qo4kbu$_0 = -1;
    this.lastWidth_0 = -1;
    this.lastHeight_0 = -1;
    this.sideBarWaterAnimationWidth_0 = 245;
    this.sideBarWaterAnimationHeight_0 = 1058;
    var settingsInner = createDiv(document);
    var settingsText = createDiv(document);
    var favoritesImage = createDiv(document);
    var favoritesText = createDiv(document);
    this.view.classList.add('side-bar-container');
    this.waterImageContainer_0.classList.add('water-timer-image-container');
    this.timerAmountContainer_0.classList.add('water-timer-text-container');
    this.timerMinutesText_0.classList.add('water-timer-minutes');
    this.timerMinutesTitleText_0.classList.add('water-timer-minutes-title');
    this.timerInfinite_0.classList.add('water-timer-infinity', 'hidden');
    this.settingsButton.classList.add('side-bar-settings-button');
    settingsInner.classList.add('settings-button-container');
    settingsText.classList.add('settings-button-text');
    this.favoritesButton.classList.add('side-bar-favorite-button');
    favoritesImage.classList.add('side-bar-favorite-image');
    favoritesText.classList.add('side-bar-favorite-text');
    this.view.appendChild(this.favoritesButton);
    this.favoritesButton.appendChild(favoritesImage);
    this.favoritesButton.appendChild(favoritesText);
    this.view.appendChild(this.waterImageContainer_0);
    this.timerAmountContainer_0.appendChild(this.timerMinutesText_0);
    this.timerAmountContainer_0.appendChild(this.timerMinutesTitleText_0);
    this.view.appendChild(this.timerAmountContainer_0);
    this.view.appendChild(this.timerInfinite_0);
    this.view.appendChild(this.settingsButton);
    this.settingsButton.appendChild(settingsInner);
    settingsInner.appendChild(settingsText);
    this.waterAnimation_0.classList.add('water-animation-svg');
    this.waterAnimation_0.setAttribute('viewBox', '0 0 ' + this.sideBarWaterAnimationWidth_0 + ' ' + this.sideBarWaterAnimationHeight_0);
    this.waterAnimation_0.setAttribute('preserveAspectRatio', 'xMaxYMax slice');
    this.waterImageContainer_0.appendChild(this.waterAnimation_0);
    this.animationObject_0 = CreateAnimation(this.waterAnimation_0);
    this.waterAnimation_0.setAttribute('viewBox', '0 0 ' + this.sideBarWaterAnimationWidth_0 + ' ' + this.sideBarWaterAnimationHeight_0);
    this.waterAnimation_0.setAttribute('preserveAspectRatio', 'xMaxYMax slice');
    this.waterAnimation_0.setAttribute('width', '100%');
    this.waterAnimation_0.setAttribute('height', '100%');
    favoritesText.innerHTML = this.stringCheese_0.favoritesNavigationTitle;
  }
  Object.defineProperty(SideBarCoordinator.prototype, 'lastZIndex', {
    get: function () {
      return this.lastZIndex_qo4kbu$_0;
    },
    set: function (lastZIndex) {
      this.lastZIndex_qo4kbu$_0 = lastZIndex;
    }
  });
  SideBarCoordinator.prototype.setZIndex_za3lpa$ = function (z) {
    this.lastZIndex = z;
    this.view.style.zIndex = z.toString();
  };
  SideBarCoordinator.prototype.setBlur_6taknv$ = function (shouldBlur) {
    if (shouldBlur)
      addClass(this.view, ['blur']);
    else
      this.view.classList.remove('blur');
  };
  SideBarCoordinator.prototype.setFavoritesButton_6taknv$ = function (isFavorite) {
    if (isFavorite) {
      addClass(this.favoritesButton, ['favorites-open']);
    }
     else {
      removeClass(this.favoritesButton, ['favorites-open']);
    }
  };
  SideBarCoordinator.prototype.setTimer_pefdam$ = function (minutes, percent, isInfinite) {
    if (isInfinite !== this.lastTimerWasInfinite_0) {
      this.lastTimerWasInfinite_0 = isInfinite;
      if (isInfinite) {
        addClass(this.timerAmountContainer_0, ['hidden']);
        this.timerInfinite_0.classList.remove('hidden');
      }
       else {
        this.timerAmountContainer_0.classList.remove('hidden');
        addClass(this.timerInfinite_0, ['hidden']);
      }
    }
    if (!isInfinite) {
      if (minutes === 1)
        this.timerMinutesTitleText_0.innerHTML = this.stringCheese_0.timeMinuteShort;
      else
        this.timerMinutesTitleText_0.innerHTML = this.stringCheese_0.timeMinuteShortPlural;
      this.timerMinutesText_0.innerHTML = minutes.toString();
      var usingPercent = numberToInt(round(percent * 100));
      this.waterAnimation_0.style.top = usingPercent.toString() + '%';
    }
  };
  SideBarCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SideBarCoordinator',
    interfaces: []
  };
  function CreateAnimation(svg) {
    return new SVGAnim(waterTimerAnimationJson, svg, 24);
  }
  function SubscriptionViewCoordinator(stringCheese, view) {
    this.stringCheese_0 = stringCheese;
    this.view = view;
    this.closeButton = createDiv(document);
    this.subscribeButton = createDiv(document);
    this.loginButton = createDiv(document);
    var backgroundImage = createDiv(document);
    var mainContainer = createDiv(document);
    var logo = createDiv(document);
    var innerContainer = createDiv(document);
    var message1 = createDiv(document);
    var message2 = createDiv(document);
    var subscribeMessage = createDiv(document);
    var loginButtonUnderline = document.createElement('u');
    this.view.classList.add('subscription-view-container');
    backgroundImage.classList.add('subscription-view-background');
    this.closeButton.classList.add('close-button');
    mainContainer.classList.add('subscription-main-container');
    logo.classList.add('logo');
    innerContainer.classList.add('subscription-inner-container');
    message1.classList.add('message1');
    message2.classList.add('message2');
    subscribeMessage.classList.add('subscribe-message');
    this.subscribeButton.classList.add('subscribe-button');
    this.loginButton.classList.add('login-button');
    this.view.appendChild(backgroundImage);
    this.view.appendChild(mainContainer);
    this.view.appendChild(this.closeButton);
    mainContainer.appendChild(logo);
    mainContainer.appendChild(innerContainer);
    innerContainer.appendChild(message1);
    innerContainer.appendChild(message2);
    innerContainer.appendChild(this.subscribeButton);
    innerContainer.appendChild(this.loginButton);
    this.subscribeButton.appendChild(subscribeMessage);
    this.loginButton.appendChild(loginButtonUnderline);
    message1.innerHTML = this.stringCheese_0.subscribeTitle;
    message2.innerHTML = this.stringCheese_0.subscribeSubtitle;
    subscribeMessage.innerHTML = this.stringCheese_0.subscribeButtonTitle;
    loginButtonUnderline.innerHTML = this.stringCheese_0.subscribeHaveAccount;
  }
  SubscriptionViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'SubscriptionViewCoordinator',
    interfaces: []
  };
  function VideoCardCoordinator(color, stringCheese) {
    this.view = createDiv(document);
    this.card_0 = createDiv(document);
    this.image_0 = createDiv(document);
    this.title_0 = createDiv(document);
    this.favoriteButton = createDiv(document);
    this.timeContainer_0 = createDiv(document);
    this.timeValueText_0 = createDiv(document);
    this.lockContainer_0 = createDiv(document);
    this.videoIsLocked_0 = false;
    this.lastVideoId_dmskdb$_0 = '';
    var topContainer = createDiv(document);
    var lockBackgroundContainer = createDiv(document);
    var lockBackgroundTop = createDiv(document);
    var lockBackgroundBottom = createDiv(document);
    var lockImage = createDiv(document);
    var lockText = createDiv(document);
    var timeTypeText = createDiv(document);
    var blackBar = createDiv(document);
    var titleContainer = createDiv(document);
    var favoriteIcon = createDiv(document);
    this.view.classList.add('video-card-container');
    this.card_0.classList.add('video-card', 'card-' + color.colorName);
    topContainer.classList.add('video-top-container');
    this.lockContainer_0.classList.add('lock-container');
    lockBackgroundContainer.classList.add('lock-background-container');
    lockBackgroundTop.classList.add('lock-background-top');
    lockBackgroundBottom.classList.add('lock-background-bottom');
    lockImage.classList.add('lock-image');
    lockText.classList.add('lock-text');
    this.image_0.classList.add('video-image');
    this.timeContainer_0.classList.add('time-container');
    this.timeValueText_0.classList.add('time-value');
    timeTypeText.classList.add('time-type-text');
    blackBar.classList.add('black-bar');
    titleContainer.classList.add('video-title-container');
    this.title_0.classList.add('video-title');
    this.favoriteButton.classList.add('video-favorite-container');
    favoriteIcon.classList.add('video-favorite-image');
    this.view.appendChild(this.card_0);
    this.card_0.appendChild(topContainer);
    this.card_0.appendChild(blackBar);
    this.card_0.appendChild(titleContainer);
    topContainer.appendChild(this.lockContainer_0);
    topContainer.appendChild(this.timeContainer_0);
    topContainer.appendChild(this.image_0);
    this.lockContainer_0.appendChild(lockBackgroundContainer);
    lockBackgroundContainer.appendChild(lockBackgroundTop);
    lockBackgroundContainer.appendChild(lockBackgroundBottom);
    this.lockContainer_0.appendChild(lockImage);
    this.lockContainer_0.appendChild(lockText);
    this.timeContainer_0.appendChild(this.timeValueText_0);
    this.timeContainer_0.appendChild(timeTypeText);
    titleContainer.appendChild(this.title_0);
    titleContainer.appendChild(this.favoriteButton);
    this.favoriteButton.appendChild(favoriteIcon);
    lockText.innerHTML = stringCheese.cardVideoLockTitle;
    timeTypeText.innerHTML = stringCheese.timeMinuteShortPlural;
  }
  Object.defineProperty(VideoCardCoordinator.prototype, 'lastVideoId', {
    get: function () {
      return this.lastVideoId_dmskdb$_0;
    },
    set: function (lastVideoId) {
      this.lastVideoId_dmskdb$_0 = lastVideoId;
    }
  });
  VideoCardCoordinator.prototype.set_z230k1$ = function (video, constructedBaseImageUrl, isSubscribed, isFavorited) {
    var tmp$;
    console.log('video', video);
    if (Kotlin.isType(video, Video)) {
      if (equals(video.videoId, this.lastVideoId))
        return;
      this.videoIsLocked_0 = video.isPremium;
      this.lastVideoId = video.videoId;
      var minutes = video.getTimeInMinutesForDisplay();
      if (minutes > 0)
        this.timeValueText_0.innerHTML = minutes.toString();
      else
        this.timeContainer_0.classList.add('hidden');
      console.log(this.title_0, typeof this.title_0);
      this.title_0.innerHTML = video.title;
      console.log(video);
      this.image_0.style.backgroundImage = "url('" + constructedBaseImageUrl + video.videoId + ".png')";
      this.setFavorited_6taknv$(isFavorited);
      this.setLock_6taknv$(isSubscribed);
    }
     else if (Kotlin.isType(video, Game)) {
      var imageBucket = constructedBaseImageUrl;
      var diffElement = createDiv(document);
      tmp$ = video.getGameDifficulty();
      for (var i = 1; i <= tmp$; i++) {
        var img = createImg(document);
        img.style.width = '10%';
        img.style.marginRight = '5px';
        img.src = 'https://s3.amazonaws.com/kidjo/public/webApp/images/star.png';
        diffElement.appendChild(img);
      }
      this.title_0.appendChild(diffElement);
      this.videoIsLocked_0 = video.premium;
      this.image_0.style.backgroundImage = "url('" + video.getCoverImage_61zpoe$(imageBucket) + "')";
      addClass(this.timeContainer_0, ['hidden']);
      addClass(this.favoriteButton, ['hidden']);
      this.setFavorited_6taknv$(isFavorited);
      this.setLock_6taknv$(isSubscribed);
    }
  };
  VideoCardCoordinator.prototype.setFavorited_6taknv$ = function (isFavorited) {
    if (isFavorited)
      addClass(this.favoriteButton, ['is-favorited']);
    else
      this.favoriteButton.classList.remove('is-favorited');
  };
  VideoCardCoordinator.prototype.setLock_6taknv$ = function (isSubscribed) {
    console.log('setLock', isSubscribed, this.videoIsLocked_0);
    if (isSubscribed || !this.videoIsLocked_0)
      addClass(this.lockContainer_0, ['hidden']);
    else
      this.lockContainer_0.classList.remove('hidden');
  };
  VideoCardCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'VideoCardCoordinator',
    interfaces: []
  };
  var VIDEO_CARD_ASPECT_RATIO_WIDTH_TO_HEIGHT;
  var VIDEO_CARD_ASPECT_RATIO_HEIGHT_TO_WIDTH;
  var VIDEO_CARD_IMAGE_ASPECT_RATIO;
  var VIDEO_CARD_SIDE_PADDING;
  var VIDEO_CARD_MIN_WIDTH;
  var VIDEO_CARD_TARGET_NUMBER_PER_VIEW_SMALL_MOBILE;
  var VIDEO_CARD_TARGET_NUMBER_PER_VIEW_MOBILE;
  var VIDEO_CARD_TARGET_NUMBER_PER_VIEW_TABLET;
  var VIDEO_CARD_TARGET_NUMBER_PER_VIEW_MOBILE_PORTRAIT;
  function VideoListCoordinator(isFavoriteList, backgroundColor, isSubscribed, stringCheese, deviceData, userConfig, listener, favoritesController, kidsController) {
    this.isFavoriteList_0 = isFavoriteList;
    this.backgroundColor_0 = backgroundColor;
    this.isSubscribed_0 = isSubscribed;
    this.stringCheese_0 = stringCheese;
    this.deviceData_0 = deviceData;
    this.userConfig_0 = userConfig;
    this.listener_0 = listener;
    this.favoritesController_0 = favoritesController;
    this.kidsController_0 = kidsController;
    this.view = createDiv(document);
    this.videoCardCoordinators_0 = ArrayList_init();
    this.view.classList.add('video-list');
    this.onClick_0 = VideoListCoordinator$onClick$lambda(this);
    this.onClickFavorites_0 = VideoListCoordinator$onClickFavorites$lambda(this);
  }
  VideoListCoordinator.prototype.handleOnClick_0 = function (event, isFavoriteButton) {
    var tmp$, tmp$_0;
    event.stopImmediatePropagation();
    console.log('HandleClick', event, isFavoriteButton);
    var element = Kotlin.isType(tmp$ = event.currentTarget, HTMLDivElement) ? tmp$ : null;
    if (element != null) {
      var videoId = (tmp$_0 = element.getAttribute('videoId')) != null ? tmp$_0 : '';
      if (isFavoriteButton) {
        var isFavorited = this.listener_0.videoCardFavoriteClicked_61zpoe$(videoId);
        var index = {v: -1};
        var tmp$_1, tmp$_0_0;
        var index_0 = 0;
        tmp$_1 = this.videoCardCoordinators_0.iterator();
        while (tmp$_1.hasNext()) {
          var item = tmp$_1.next();
          var i = (tmp$_0_0 = index_0, index_0 = tmp$_0_0 + 1 | 0, tmp$_0_0);
          if (equals(item.lastVideoId, videoId)) {
            index.v = i;
          }
        }
        if (index.v >= 0) {
          if (this.isFavoriteList_0) {
            var coordinator = this.videoCardCoordinators_0.removeAt_za3lpa$(index.v);
            coordinator.view.remove();
          }
           else {
            this.videoCardCoordinators_0.get_za3lpa$(index.v).setFavorited_6taknv$(isFavorited);
          }
        }
      }
       else
        this.listener_0.videoCardWasClicked_61zpoe$(videoId);
    }
  };
  VideoListCoordinator.prototype.setVideoList_giv38x$ = function (videos) {
    var tmp$;
    console.log('videos', videos);
    var newSize = videos.size;
    console.log('newSize', newSize);
    while (this.videoCardCoordinators_0.size > newSize) {
      var last = this.videoCardCoordinators_0.removeAt_za3lpa$(this.videoCardCoordinators_0.size - 1 | 0);
      last.view.remove();
    }
    for (var i = 0; i < newSize; i++) {
      var video = videos.get_za3lpa$(i);
      var videoCardCoordinator;
      if (i < this.videoCardCoordinators_0.size) {
        videoCardCoordinator = this.videoCardCoordinators_0.get_za3lpa$(i);
      }
       else {
        videoCardCoordinator = new VideoCardCoordinator(this.backgroundColor_0, this.stringCheese_0);
        videoCardCoordinator.view.onclick = this.onClick_0;
        videoCardCoordinator.favoriteButton.onclick = this.onClickFavorites_0;
        this.view.appendChild(videoCardCoordinator.view);
        this.videoCardCoordinators_0.add_11rb$(videoCardCoordinator);
      }
      console.log('videoCardCoordinator.set', video);
      if (Kotlin.isType(video, Video)) {
        videoCardCoordinator.view.setAttribute('videoId', video.videoId);
        videoCardCoordinator.favoriteButton.setAttribute('videoId', video.videoId);
        videoCardCoordinator.set_z230k1$(video, this.deviceData_0.videoImageUrl + this.userConfig_0.imageBucket, this.isSubscribed_0, this.favoritesController_0.isFavorited_9u1k3q$(video));
      }
       else if (Kotlin.isType(video, Game)) {
        videoCardCoordinator.view.setAttribute('videoId', video.id);
        println('kidAge');
        println((tmp$ = this.kidsController_0.getSelectedKid()) != null ? tmp$.age : null);
        videoCardCoordinator.set_z230k1$(video, this.userConfig_0.imageBucket, this.isSubscribed_0, false);
      }
    }
  };
  VideoListCoordinator.prototype.setSizing = function () {
    var usingNumberOfViews;
    if (MediaQuery_isPortraitPhone()) {
      usingNumberOfViews = VIDEO_CARD_TARGET_NUMBER_PER_VIEW_MOBILE_PORTRAIT;
    }
     else if (MediaQuery_isTablet()) {
      usingNumberOfViews = VIDEO_CARD_TARGET_NUMBER_PER_VIEW_TABLET;
    }
     else if (MediaQuery_isSmallPhone()) {
      usingNumberOfViews = VIDEO_CARD_TARGET_NUMBER_PER_VIEW_SMALL_MOBILE;
    }
     else {
      usingNumberOfViews = VIDEO_CARD_TARGET_NUMBER_PER_VIEW_MOBILE;
    }
    var widthOfListMinusCardPadding = this.view.clientWidth - VIDEO_CARD_SIDE_PADDING * usingNumberOfViews;
    var a = VIDEO_CARD_MIN_WIDTH;
    var b = widthOfListMinusCardPadding / usingNumberOfViews;
    var width = Math_0.max(a, b);
    var containerWidth = width + VIDEO_CARD_SIDE_PADDING;
    var height = width * VIDEO_CARD_ASPECT_RATIO_WIDTH_TO_HEIGHT;
    var containerHeight = height + VIDEO_CARD_SIDE_PADDING;
    var imageHeight = width * VIDEO_CARD_IMAGE_ASPECT_RATIO;
    if (height > this.view.clientHeight) {
      height = this.view.clientHeight - VIDEO_CARD_SIDE_PADDING;
      width = height * VIDEO_CARD_ASPECT_RATIO_HEIGHT_TO_WIDTH;
      containerWidth = width + VIDEO_CARD_SIDE_PADDING;
      containerHeight = height + VIDEO_CARD_SIDE_PADDING;
      imageHeight = width * VIDEO_CARD_IMAGE_ASPECT_RATIO;
    }
    this.setCssVariable_0('--WIDTH_OF_VIDEO_CARD_CONTAINER', numberToInt(containerWidth).toString() + 'px');
    this.setCssVariable_0('--HEIGHT_OF_VIDEO_CARD_CONTAINER', numberToInt(containerHeight).toString() + 'px');
    this.setCssVariable_0('--WIDTH_OF_VIDEO_CARD', numberToInt(width).toString() + 'px');
    this.setCssVariable_0('--HEIGHT_OF_VIDEO_CARD', numberToInt(height).toString() + 'px');
    this.setCssVariable_0('--HEIGHT_OF_VIDEO_IMAGE', numberToInt(imageHeight).toString() + 'px');
  };
  VideoListCoordinator.prototype.setCssVariable_0 = function (name, value) {
    this.view.style.setProperty(name, value);
  };
  function VideoListCoordinator$Listener() {
  }
  VideoListCoordinator$Listener.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'Listener',
    interfaces: []
  };
  function VideoListCoordinator$onClick$lambda(this$VideoListCoordinator) {
    return function (event) {
      this$VideoListCoordinator.handleOnClick_0(event, false);
      return Unit;
    };
  }
  function VideoListCoordinator$onClickFavorites$lambda(this$VideoListCoordinator) {
    return function (event) {
      this$VideoListCoordinator.handleOnClick_0(event, true);
      return Unit;
    };
  }
  VideoListCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'VideoListCoordinator',
    interfaces: []
  };
  function VideoPlayerViewCoordinator(view) {
    this.view = view;
    var tmp$;
    this.videoFrame = Kotlin.isType(tmp$ = document.createElement('video'), HTMLVideoElement) ? tmp$ : throwCCE();
    this.controlLayer = createDiv(document);
    this.closeButton = createDiv(document);
    this.view.classList.add('video-player-container');
    this.videoFrame.classList.add('video-frame');
    this.controlLayer.classList.add('video-control-container');
    this.closeButton.classList.add('close-button');
    this.view.appendChild(this.videoFrame);
    this.view.appendChild(this.controlLayer);
    this.controlLayer.appendChild(this.closeButton);
    this.videoFrame.controls = true;
    this.videoFrame.setAttribute('controlsList', 'nodownload');
  }
  VideoPlayerViewCoordinator.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'VideoPlayerViewCoordinator',
    interfaces: []
  };
  function SetGenericTimeout(duration, run) {
    window.setTimeout(run, duration.toInt());
  }
  function BroadcastReceiverJs(id, listener) {
    BroadcastReceiverJs$Companion_getInstance();
    this.id_0 = id;
    this.listener_0 = listener;
  }
  BroadcastReceiverJs.prototype.register_5oryg$ = function (broadcaster) {
    broadcaster.addEventListener(this.id_0, this);
  };
  BroadcastReceiverJs.prototype.unregister = function () {
    document.removeEventListener(this.id_0, this);
  };
  BroadcastReceiverJs.prototype.handleEvent = function (event) {
    this.listener_0.broadcastReceiverGotBroadcast_61zpoe$(event.type);
  };
  function BroadcastReceiverJs$Companion() {
    BroadcastReceiverJs$Companion_instance = this;
  }
  BroadcastReceiverJs$Companion.prototype.Broadcast_un8f3q$ = function (broadcaster, id) {
    var event = new Event(id);
    broadcaster.dispatchEvent(event);
  };
  BroadcastReceiverJs$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var BroadcastReceiverJs$Companion_instance = null;
  function BroadcastReceiverJs$Companion_getInstance() {
    if (BroadcastReceiverJs$Companion_instance === null) {
      new BroadcastReceiverJs$Companion();
    }
    return BroadcastReceiverJs$Companion_instance;
  }
  BroadcastReceiverJs.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'BroadcastReceiverJs',
    interfaces: []
  };
  function HashFunctionsJs() {
  }
  HashFunctionsJs.prototype.sha1Hash_61zpoe$ = function (s) {
    return sha1(s);
  };
  HashFunctionsJs.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'HashFunctionsJs',
    interfaces: []
  };
  function UrlBuilder(baseUrl) {
    this.url_0 = baseUrl;
    this.hasAddedFirstQuery_0 = false;
  }
  UrlBuilder.prototype.addQuery_puj7f4$ = function (key, value) {
    this.url_0 += (this.hasAddedFirstQuery_0 ? '&' : '?') + key + '=' + value;
    this.hasAddedFirstQuery_0 = true;
  };
  UrlBuilder.prototype.build = function () {
    return encodeURI(this.url_0);
  };
  UrlBuilder.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'UrlBuilder',
    interfaces: []
  };
  function HttpRequestReaderJs() {
  }
  function HttpRequestReaderJs$createGet$lambda(closure$request, closure$onComplete) {
    return function (it) {
      if (closure$request.readyState === XMLHttpRequest.DONE) {
        closure$onComplete(closure$request.status, closure$request.responseText);
      }
      return Unit;
    };
  }
  HttpRequestReaderJs.prototype.createGet_t3ddc1$ = function (requestType, url, onComplete) {
    var request = new XMLHttpRequest();
    request.open(requestType.raw, url);
    if (onComplete != null) {
      request.onreadystatechange = HttpRequestReaderJs$createGet$lambda(request, onComplete);
    }
    return request;
  };
  HttpRequestReaderJs.prototype.setHeader_fn1ioh$ = function (request, key, value) {
    request.setRequestHeader(key, value);
  };
  HttpRequestReaderJs.prototype.send_28on4w$ = function (request, body) {
    request.send(body);
  };
  HttpRequestReaderJs.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'HttpRequestReaderJs',
    interfaces: []
  };
  function JsonReaderJs() {
  }
  JsonReaderJs.prototype.set_2s9n7q$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.set_2s9n7q$', function (json, key, value) {
    json[key] = value;
  });
  JsonReaderJs.prototype.has_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.has_953mwi$', function (json, key) {
    return json[key] != null;
  });
  JsonReaderJs.prototype.getInt_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getInt_953mwi$', function (json, key) {
    var tmp$, tmp$_0;
    return (tmp$_0 = typeof (tmp$ = json[key]) === 'number' ? tmp$ : null) != null ? tmp$_0 : 0;
  });
  JsonReaderJs.prototype.getInt_fy68v8$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getInt_fy68v8$', function (json, key, defaultValue) {
    var tmp$, tmp$_0;
    return (tmp$_0 = typeof (tmp$ = json[key]) === 'number' ? tmp$ : null) != null ? tmp$_0 : defaultValue;
  });
  JsonReaderJs.prototype.getLong_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getLong_953mwi$', wrapFunction(function () {
    var L0 = Kotlin.Long.ZERO;
    var numberToLong = Kotlin.numberToLong;
    return function (json, key) {
      var defaultValue = L0;
      var tmp$, tmp$_0, tmp$_1;
      return (tmp$_1 = (tmp$_0 = Kotlin.isNumber(tmp$ = json[key]) ? tmp$ : null) != null ? numberToLong(tmp$_0) : null) != null ? tmp$_1 : defaultValue;
    };
  }));
  JsonReaderJs.prototype.getLong_2sgokv$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getLong_2sgokv$', wrapFunction(function () {
    var numberToLong = Kotlin.numberToLong;
    return function (json, key, defaultValue) {
      var tmp$, tmp$_0, tmp$_1;
      return (tmp$_1 = (tmp$_0 = Kotlin.isNumber(tmp$ = json[key]) ? tmp$ : null) != null ? numberToLong(tmp$_0) : null) != null ? tmp$_1 : defaultValue;
    };
  }));
  JsonReaderJs.prototype.getDouble_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getDouble_953mwi$', function (json, key) {
    var tmp$, tmp$_0;
    return (tmp$_0 = typeof (tmp$ = json[key]) === 'number' ? tmp$ : null) != null ? tmp$_0 : 0.0;
  });
  JsonReaderJs.prototype.getDouble_l56hh8$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getDouble_l56hh8$', function (json, key, defaultValue) {
    var tmp$, tmp$_0;
    return (tmp$_0 = typeof (tmp$ = json[key]) === 'number' ? tmp$ : null) != null ? tmp$_0 : defaultValue;
  });
  JsonReaderJs.prototype.getBoolean_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getBoolean_953mwi$', wrapFunction(function () {
    var equals = Kotlin.equals;
    return function (json, key) {
      var getBoolean_pemel1$result;
      var value = json[key];
      if (typeof value === 'boolean') {
        getBoolean_pemel1$result = value;
      }
       else if (Kotlin.isNumber(value)) {
        getBoolean_pemel1$result = equals(value, 1);
      }
       else {
        getBoolean_pemel1$result = false;
      }
      return getBoolean_pemel1$result;
    };
  }));
  JsonReaderJs.prototype.getBoolean_pemel1$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getBoolean_pemel1$', wrapFunction(function () {
    var equals = Kotlin.equals;
    return function (json, key, defaultValue) {
      var value = json[key];
      if (typeof value === 'boolean')
        return value;
      else if (Kotlin.isNumber(value))
        return equals(value, 1);
      else
        return defaultValue;
    };
  }));
  JsonReaderJs.prototype.getString_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getString_953mwi$', wrapFunction(function () {
    var Exception = Kotlin.kotlin.Exception;
    return function (json, key) {
      var getString_dysybg$result;
      getString_dysybg$break: do {
        var tmp$;
        var value = json[key];
        if (typeof value === 'string') {
          getString_dysybg$result = value;
          break getString_dysybg$break;
        }
        try {
          getString_dysybg$result = (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : '';
        }
         catch (e) {
          if (Kotlin.isType(e, Exception)) {
            getString_dysybg$result = '';
            break getString_dysybg$break;
          }
           else
            throw e;
        }
      }
       while (false);
      return getString_dysybg$result;
    };
  }));
  JsonReaderJs.prototype.getString_dysybg$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getString_dysybg$', wrapFunction(function () {
    var Exception = Kotlin.kotlin.Exception;
    return function (json, key, defaultValue) {
      var tmp$;
      var value = json[key];
      if (typeof value === 'string')
        return value;
      try {
        return (tmp$ = value != null ? value.toString() : null) != null ? tmp$ : defaultValue;
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          return defaultValue;
        }
         else
          throw e;
      }
    };
  }));
  JsonReaderJs.prototype.getJson_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getJson_953mwi$', function (json, key) {
    var tmp$;
    return Kotlin.isType(tmp$ = json[key], Object) ? tmp$ : null;
  });
  JsonReaderJs.prototype.getJsonArray_953mwi$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getJsonArray_953mwi$', wrapFunction(function () {
    var JsArray = Array;
    return function (json, key) {
      var tmp$;
      return Kotlin.isType(tmp$ = json[key], JsArray) ? tmp$ : null;
    };
  }));
  JsonReaderJs.prototype.getEmpty = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getEmpty', function () {
    return {};
  });
  JsonReaderJs.prototype.jsonToString_t1yf6p$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.jsonToString_t1yf6p$', wrapFunction(function () {
    var JSON_0 = JSON;
    return function (json) {
      if (json == null)
        return null;
      return JSON_0.stringify(json);
    };
  }));
  JsonReaderJs.prototype.jsonToString_qk3xy8$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.jsonToString_qk3xy8$', wrapFunction(function () {
    var JSON_0 = JSON;
    return function (json) {
      return JSON_0.stringify(json);
    };
  }));
  JsonReaderJs.prototype.stringToJson_61zpoe$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.stringToJson_61zpoe$', wrapFunction(function () {
    var JSON_0 = JSON;
    var Exception = Kotlin.kotlin.Exception;
    return function (jsonString) {
      var tmp$;
      try {
        tmp$ = JSON_0.parse(jsonString);
      }
       catch (e) {
        if (Kotlin.isType(e, Exception)) {
          tmp$ = null;
        }
         else
          throw e;
      }
      return tmp$;
    };
  }));
  JsonReaderJs.prototype.getEmptyArray = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getEmptyArray', wrapFunction(function () {
    var JsArray_init = Array;
    return function () {
      return new JsArray_init();
    };
  }));
  JsonReaderJs.prototype.set_gzhgzw$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.set_gzhgzw$', function (json, index, value) {
    json.set(index, value);
  });
  JsonReaderJs.prototype.push_mphxu4$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.push_mphxu4$', function (json, value) {
    json.push(value);
  });
  JsonReaderJs.prototype.get_mphrwp$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.get_mphrwp$', function (json, index) {
    return json.get(index);
  });
  JsonReaderJs.prototype.getJson_mphrwp$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getJson_mphrwp$', wrapFunction(function () {
    var throwCCE = Kotlin.throwCCE;
    return function (json, index) {
      var tmp$;
      return Kotlin.isType(tmp$ = json.get(index), Object) ? tmp$ : throwCCE();
    };
  }));
  JsonReaderJs.prototype.getInt_mphrwp$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getInt_mphrwp$', function (json, index) {
    var tmp$, tmp$_0;
    return (tmp$_0 = typeof (tmp$ = json.get(index)) === 'number' ? tmp$ : null) != null ? tmp$_0 : 0;
  });
  JsonReaderJs.prototype.getLong_mphrwp$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.getLong_mphrwp$', wrapFunction(function () {
    var numberToLong = Kotlin.numberToLong;
    var L0 = Kotlin.Long.ZERO;
    return function (json, index) {
      var tmp$, tmp$_0, tmp$_1;
      return (tmp$_1 = (tmp$_0 = Kotlin.isNumber(tmp$ = json.get(index)) ? tmp$ : null) != null ? numberToLong(tmp$_0) : null) != null ? tmp$_1 : L0;
    };
  }));
  JsonReaderJs.prototype.length_g61j61$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.length_g61j61$', function (json) {
    return json.length;
  });
  JsonReaderJs.prototype.iterate_p6gg8v$ = defineInlineFunction('app.net.kidjo.common.platform.direct.JsonReaderJs.iterate_p6gg8v$', function (json, op) {
    var tmp$;
    var size = json.length;
    for (var i = 0; i < size; i++) {
      tmp$ = json.get(i);
      if (tmp$ == null) {
        continue;
      }
      var value = tmp$;
      op(i, value);
    }
  });
  JsonReaderJs.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'JsonReaderJs',
    interfaces: []
  };
  function LocalKeyValueStorageReaderJs() {
  }
  LocalKeyValueStorageReaderJs.prototype.getString_y5ifao$ = function (editor, key) {
    return this.getString_mjlkiq$(editor, key, '');
  };
  LocalKeyValueStorageReaderJs.prototype.getString_mjlkiq$ = function (editor, key, defaultValue) {
    var tmp$;
    return (tmp$ = editor[key]) != null ? tmp$ : defaultValue;
  };
  LocalKeyValueStorageReaderJs.prototype.getInt_v8w1ha$ = function (editor, key, defaultValue) {
    var tmp$;
    try {
      tmp$ = toInt(this.getString_y5ifao$(editor, key));
    }
     catch (e) {
      if (Kotlin.isType(e, Exception)) {
        tmp$ = defaultValue;
      }
       else
        throw e;
    }
    return tmp$;
  };
  LocalKeyValueStorageReaderJs.prototype.getInt_y5ifao$ = function (editor, key) {
    return this.getInt_v8w1ha$(editor, key, 0);
  };
  LocalKeyValueStorageReaderJs.prototype.getDouble_fd81cy$ = function (editor, key, defaultValue) {
    var tmp$;
    try {
      tmp$ = toDouble(this.getString_y5ifao$(editor, key));
    }
     catch (e) {
      if (Kotlin.isType(e, Exception)) {
        tmp$ = defaultValue;
      }
       else
        throw e;
    }
    return tmp$;
  };
  LocalKeyValueStorageReaderJs.prototype.getDouble_y5ifao$ = function (editor, key) {
    return this.getDouble_fd81cy$(editor, key, 0.0);
  };
  LocalKeyValueStorageReaderJs.prototype.getBoolean_krbqyf$ = function (editor, key, defaultValue) {
    if (!this.has_y5ifao$(editor, key))
      return defaultValue;
    return equals(this.getString_y5ifao$(editor, key), 'true');
  };
  LocalKeyValueStorageReaderJs.prototype.getBoolean_y5ifao$ = function (editor, key) {
    return this.getBoolean_krbqyf$(editor, key, false);
  };
  LocalKeyValueStorageReaderJs.prototype.getLong_y5ifao$ = function (editor, key) {
    return this.getLong_prxk4z$(editor, key, L0);
  };
  LocalKeyValueStorageReaderJs.prototype.getLong_prxk4z$ = function (editor, key, defaultValue) {
    if (!this.has_y5ifao$(editor, key))
      return defaultValue;
    var value = this.getString_y5ifao$(editor, key);
    try {
      return toLong(value);
    }
     catch (e) {
      if (Kotlin.isType(e, Exception)) {
        return defaultValue;
      }
       else
        throw e;
    }
  };
  LocalKeyValueStorageReaderJs.prototype.getEditor_6f7vr6$ = function (local) {
    return localStorage;
  };
  LocalKeyValueStorageReaderJs.prototype.has_y5ifao$ = function (editor, key) {
    return editor[key] != null;
  };
  LocalKeyValueStorageReaderJs.prototype.delete_y5ifao$ = function (editor, key) {
    editor.removeItem(key);
  };
  LocalKeyValueStorageReaderJs.prototype.set_mjlkiq$ = function (editor, key, value) {
    editor[key] = value;
  };
  LocalKeyValueStorageReaderJs.prototype.set_v8w1ha$ = function (editor, key, value) {
    this.set_mjlkiq$(editor, key, value.toString());
  };
  LocalKeyValueStorageReaderJs.prototype.set_fd81cy$ = function (editor, key, value) {
    this.set_mjlkiq$(editor, key, value.toString());
  };
  LocalKeyValueStorageReaderJs.prototype.set_krbqyf$ = function (editor, key, value) {
    this.set_mjlkiq$(editor, key, value.toString());
  };
  LocalKeyValueStorageReaderJs.prototype.set_prxk4z$ = function (editor, key, value) {
    this.set_mjlkiq$(editor, key, value.toString());
  };
  LocalKeyValueStorageReaderJs.prototype.commit_6f7vr6$ = function (editor) {
  };
  LocalKeyValueStorageReaderJs.prototype.commitAsync_6f7vr6$ = function (editor) {
  };
  LocalKeyValueStorageReaderJs.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'LocalKeyValueStorageReaderJs',
    interfaces: []
  };
  function UTCTimeStamp() {
    return Kotlin.Long.fromNumber(Date.now());
  }
  function IsUTCTimeStampToday(timestamp) {
    var now = new Date();
    var checkDate = new Date(timestamp);
    return now.getDate() === checkDate.getDate() && now.getMonth() === checkDate.getMonth() && now.getFullYear() === checkDate.getFullYear();
  }
  function UTCAPITimeStamp() {
    return (new Date()).toUTCString();
  }
  function JsAudioLoader(userConfig) {
    this.listener_vflc08$_0 = null;
    this.musicIsLoaded_uho2jo$_0 = true;
    this.music_0 = new Audio(userConfig.baseAssetUrl + MUSIC_ASSET_FOLDER + 'loop.mp3');
    this.soundEffect_generalButton_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'general_button.mp3');
    this.soundEffect_lock_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'lock.mp3');
    this.soundEffect_swipeLeft_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'swipe_left.mp3');
    this.soundEffect_swipeRight_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'swipe_right.mp3');
    this.soundEffect_validation_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'validation.mp3');
    this.soundEffect_folderOpen_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'card_folder_open.mp3');
    this.soundEffect_folderClose_0 = new Audio(userConfig.baseAssetUrl + SOUND_EFFECTS_ASSET_FOLDER + 'card_folder_close.mp3');
    this.musicIsPlaying_md2xst$_0 = false;
    this.music_0.loop = true;
    this.music_0.volume = 0.3;
  }
  Object.defineProperty(JsAudioLoader.prototype, 'listener', {
    get: function () {
      return this.listener_vflc08$_0;
    },
    set: function (listener) {
      this.listener_vflc08$_0 = listener;
    }
  });
  Object.defineProperty(JsAudioLoader.prototype, 'musicIsLoaded', {
    get: function () {
      return this.musicIsLoaded_uho2jo$_0;
    }
  });
  Object.defineProperty(JsAudioLoader.prototype, 'musicIsPlaying', {
    get: function () {
      return this.musicIsPlaying_md2xst$_0;
    }
  });
  JsAudioLoader.prototype.setMusicPlayState_6taknv$ = function (play) {
    if (play)
      this.music_0.play();
    else
      this.music_0.pause();
  };
  JsAudioLoader.prototype.playSound_jn3wr6$ = function (effects) {
    switch (effects.name) {
      case 'GENERAL_BUTTON':
        this.soundEffect_generalButton_0.play();
        break;
      case 'LOCK':
        this.soundEffect_lock_0.play();
        break;
      case 'SWIPE_LEFT':
        this.soundEffect_swipeLeft_0.play();
        break;
      case 'SWIPE_RIGHT':
        this.soundEffect_swipeRight_0.play();
        break;
      case 'VALIDATION':
        this.soundEffect_validation_0.play();
        break;
      case 'FOLDER_OPEN':
        this.soundEffect_folderOpen_0.play();
        break;
      case 'FOLDER_CLOSE':
        this.soundEffect_folderClose_0.play();
        break;
    }
  };
  JsAudioLoader.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'JsAudioLoader',
    interfaces: [AudioLoader]
  };
  var MUSIC_ASSET_FOLDER;
  var SOUND_EFFECTS_ASSET_FOLDER;
  function StringCheeseProvider() {
    StringCheeseProvider$Companion_getInstance();
    this.obj_en_StringCheese = new en_StringCheese();
    this.obj_es_StringCheese = new es_StringCheese();
    this.obj_fr_StringCheese = new fr_StringCheese();
    this.obj_pt_StringCheese = new pt_StringCheese();
  }
  StringCheeseProvider.prototype.get_61zpoe$ = function (languageShortName) {
    var tmp$;
    switch (languageShortName) {
      case 'en':
        tmp$ = this.obj_en_StringCheese;
        break;
      case 'es':
        tmp$ = this.obj_es_StringCheese;
        break;
      case 'fr':
        tmp$ = this.obj_fr_StringCheese;
        break;
      case 'pt':
        tmp$ = this.obj_pt_StringCheese;
        break;
      default:tmp$ = this.obj_en_StringCheese;
        break;
    }
    return tmp$;
  };
  function StringCheeseProvider$Companion() {
    StringCheeseProvider$Companion_instance = this;
  }
  StringCheeseProvider$Companion.prototype.GetSingle_61zpoe$ = function (languageShortName) {
    var tmp$;
    switch (languageShortName) {
      case 'en':
        tmp$ = new en_StringCheese();
        break;
      case 'es':
        tmp$ = new es_StringCheese();
        break;
      case 'fr':
        tmp$ = new fr_StringCheese();
        break;
      case 'pt':
        tmp$ = new pt_StringCheese();
        break;
      default:tmp$ = new en_StringCheese();
        break;
    }
    return tmp$;
  };
  StringCheeseProvider$Companion.$metadata$ = {
    kind: Kind_OBJECT,
    simpleName: 'Companion',
    interfaces: []
  };
  var StringCheeseProvider$Companion_instance = null;
  function StringCheeseProvider$Companion_getInstance() {
    if (StringCheeseProvider$Companion_instance === null) {
      new StringCheeseProvider$Companion();
    }
    return StringCheeseProvider$Companion_instance;
  }
  StringCheeseProvider.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'StringCheeseProvider',
    interfaces: []
  };
  function StringCheese() {
  }
  StringCheese.$metadata$ = {
    kind: Kind_INTERFACE,
    simpleName: 'StringCheese',
    interfaces: []
  };
  function en_StringCheese() {
    this.ageGateAgePlaceholderYear_rtrvqj$_0 = 'Y';
    this.ageGateAgeSubtext_djjzzi$_0 = 'This information is never stored and is only used to gain access to the parental controls.';
    this.ageGateSubtitle_r8181w$_0 = 'Please confirm your year of birth before continuing.';
    this.ageGateTitle_s5k7gg$_0 = 'Hello,';
    this.cardFavoritesRemove_yldu0r$_0 = 'DELETE';
    this.cardVideoFavoritesAdd_hn4x1t$_0 = 'FAVORITE';
    this.cardVideoLockTitle_zi90yc$_0 = 'Press to unlock';
    this.contentTypeEducation_26bd7d$_0 = 'Educational';
    this.contentTypeEntertainment_x2kyiv$_0 = 'Entertainment';
    this.contentTypeMixed_pb6mna$_0 = 'Mix';
    this.emailSupport_7biiqj$_0 = '<EMAIL>';
    this.favoritesNavigationTitle_b4cufz$_0 = 'Playlist';
    this.favoritesTitle_tugfot$_0 = 'Playlist';
    this.folderSelectionNavigationTitle_snyyt4$_0 = 'Videos';
    this.kidEmptyName_b77ce4$_0 = 'First Name';
    this.overtimeButtonSubtitle_iuu265$_0 = 'PARENTS ONLY!';
    this.overtimeButtonTitleLine1_72whbq$_0 = 'Press anywhere';
    this.overtimeButtonTitleLine2_72whcl$_0 = 'to reset the timer?';
    this.overtimeTitle_u970nj$_0 = 'You have run out of screen time! See you next time!';
    this.privacyPolicyTitle_xww7g0$_0 = 'Our privacy policy';
    this.settingsAgeTitle_shgkdu$_0 = 'Filter by age';
    this.settingsContentTypeTitle_yw61s6$_0 = 'Choose content';
    this.settingsExtraTitle_5ap4n1$_0 = 'Extra';
    this.settingsFullScreen_f9dwk0$_0 = 'Full screen';
    this.settingsLanguageTitle_2enzwp$_0 = 'Language';
    this.settingsNavigationTitle_niiav9$_0 = 'Settings';
    this.settingsPrivacyPolicy_dssmof$_0 = 'Privacy policy';
    this.settingsScreenTimeLimitOptionOff_4hciml$_0 = 'Off';
    this.settingsScreenTimeLimitTitle_842wk5$_0 = 'Set time limit';
    this.settingsSound_tnkhxi$_0 = 'Sound';
    this.subscribeButtonTitle_qx8bjm$_0 = 'WATCH NOW!';
    this.subscribeHaveAccount_m1s4qb$_0 = 'Already have an account';
    this.subscribePrivacyText_bxvib$_0 = 'Our privacy policy it can be found at: kidjo.tv/privacy and our terms of service can be found at: kidjo.tv/terms';
    this.subscribeSubtitle_6mogtg$_0 = '100% Fun, 100% Ad Free';
    this.subscribeTitle_zeflsw$_0 = "<span class='subscribe-highlight-text'>UNLIMITED<\/span><br/> video access!";
    this.timeMinuteLong_y7wjlb$_0 = 'minute';
    this.timeMinuteLongPlural_2ewhhv$_0 = 'minutes';
    this.timeMinuteShort_7v5ngr$_0 = 'min';
    this.timeMinuteShortPlural_cc046f$_0 = 'mins';
    this.toastApiDefaultError_2h9sp8$_0 = 'Uh oh! Something went wrong!';
    this.toastRequiresInternet_3cqspc$_0 = 'Sorry, you need a connection to the internet to do this.';
    this.toastTooYoung_xlid9h$_0 = 'Sorry, please get your parent!';
    this.urlHelp_hoxel2$_0 = 'https://www.kidjo.tv/faq';
    this.urlPrivacy_k4bc0l$_0 = 'https://www.kidjo.tv/privacy';
    this.urlTerms_e3hlu2$_0 = 'https://www.kidjo.tv/terms';
    this.unsubscribe_10r9h1$_0 = 'Unsubscribe';
  }
  Object.defineProperty(en_StringCheese.prototype, 'ageGateAgePlaceholderYear', {
    get: function () {
      return this.ageGateAgePlaceholderYear_rtrvqj$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'ageGateAgeSubtext', {
    get: function () {
      return this.ageGateAgeSubtext_djjzzi$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'ageGateSubtitle', {
    get: function () {
      return this.ageGateSubtitle_r8181w$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'ageGateTitle', {
    get: function () {
      return this.ageGateTitle_s5k7gg$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'cardFavoritesRemove', {
    get: function () {
      return this.cardFavoritesRemove_yldu0r$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'cardVideoFavoritesAdd', {
    get: function () {
      return this.cardVideoFavoritesAdd_hn4x1t$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'cardVideoLockTitle', {
    get: function () {
      return this.cardVideoLockTitle_zi90yc$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'contentTypeEducation', {
    get: function () {
      return this.contentTypeEducation_26bd7d$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'contentTypeEntertainment', {
    get: function () {
      return this.contentTypeEntertainment_x2kyiv$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'contentTypeMixed', {
    get: function () {
      return this.contentTypeMixed_pb6mna$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'emailSupport', {
    get: function () {
      return this.emailSupport_7biiqj$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'favoritesNavigationTitle', {
    get: function () {
      return this.favoritesNavigationTitle_b4cufz$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'favoritesTitle', {
    get: function () {
      return this.favoritesTitle_tugfot$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'folderSelectionNavigationTitle', {
    get: function () {
      return this.folderSelectionNavigationTitle_snyyt4$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'kidEmptyName', {
    get: function () {
      return this.kidEmptyName_b77ce4$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'overtimeButtonSubtitle', {
    get: function () {
      return this.overtimeButtonSubtitle_iuu265$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'overtimeButtonTitleLine1', {
    get: function () {
      return this.overtimeButtonTitleLine1_72whbq$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'overtimeButtonTitleLine2', {
    get: function () {
      return this.overtimeButtonTitleLine2_72whcl$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'overtimeTitle', {
    get: function () {
      return this.overtimeTitle_u970nj$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'privacyPolicyTitle', {
    get: function () {
      return this.privacyPolicyTitle_xww7g0$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsAgeTitle', {
    get: function () {
      return this.settingsAgeTitle_shgkdu$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsContentTypeTitle', {
    get: function () {
      return this.settingsContentTypeTitle_yw61s6$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsExtraTitle', {
    get: function () {
      return this.settingsExtraTitle_5ap4n1$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsFullScreen', {
    get: function () {
      return this.settingsFullScreen_f9dwk0$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsLanguageTitle', {
    get: function () {
      return this.settingsLanguageTitle_2enzwp$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsNavigationTitle', {
    get: function () {
      return this.settingsNavigationTitle_niiav9$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsPrivacyPolicy', {
    get: function () {
      return this.settingsPrivacyPolicy_dssmof$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsScreenTimeLimitOptionOff', {
    get: function () {
      return this.settingsScreenTimeLimitOptionOff_4hciml$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsScreenTimeLimitTitle', {
    get: function () {
      return this.settingsScreenTimeLimitTitle_842wk5$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'settingsSound', {
    get: function () {
      return this.settingsSound_tnkhxi$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'subscribeButtonTitle', {
    get: function () {
      return this.subscribeButtonTitle_qx8bjm$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'subscribeHaveAccount', {
    get: function () {
      return this.subscribeHaveAccount_m1s4qb$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'subscribePrivacyText', {
    get: function () {
      return this.subscribePrivacyText_bxvib$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'subscribeSubtitle', {
    get: function () {
      return this.subscribeSubtitle_6mogtg$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'subscribeTitle', {
    get: function () {
      return this.subscribeTitle_zeflsw$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'timeMinuteLong', {
    get: function () {
      return this.timeMinuteLong_y7wjlb$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'timeMinuteLongPlural', {
    get: function () {
      return this.timeMinuteLongPlural_2ewhhv$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'timeMinuteShort', {
    get: function () {
      return this.timeMinuteShort_7v5ngr$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'timeMinuteShortPlural', {
    get: function () {
      return this.timeMinuteShortPlural_cc046f$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'toastApiDefaultError', {
    get: function () {
      return this.toastApiDefaultError_2h9sp8$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'toastRequiresInternet', {
    get: function () {
      return this.toastRequiresInternet_3cqspc$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'toastTooYoung', {
    get: function () {
      return this.toastTooYoung_xlid9h$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'urlHelp', {
    get: function () {
      return this.urlHelp_hoxel2$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'urlPrivacy', {
    get: function () {
      return this.urlPrivacy_k4bc0l$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'urlTerms', {
    get: function () {
      return this.urlTerms_e3hlu2$_0;
    }
  });
  Object.defineProperty(en_StringCheese.prototype, 'unsubscribe', {
    get: function () {
      return this.unsubscribe_10r9h1$_0;
    }
  });
  en_StringCheese.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'en_StringCheese',
    interfaces: [StringCheese]
  };
  function es_StringCheese() {
    this.ageGateAgePlaceholderYear_9tsei8$_0 = 'A';
    this.ageGateAgeSubtext_oyqrkd$_0 = 'Esta informaci\xF3n nunca se guarda y es s\xF3lo usada para tener acceso a los controles parentales.';
    this.ageGateSubtitle_essj3r$_0 = 'Por favor confirme su a\xF1o de nacimiento antes de continuar.';
    this.ageGateTitle_lt8emz$_0 = 'Hola,';
    this.cardFavoritesRemove_z7oxz4$_0 = 'BORRAR';
    this.cardVideoFavoritesAdd_q4b0h6$_0 = 'FAVORITO';
    this.cardVideoLockTitle_lqlth5$_0 = 'Desbloquear';
    this.contentTypeEducation_ldvrsk$_0 = 'Educacional';
    this.contentTypeEntertainment_gr8n84$_0 = 'Entretenimiento';
    this.contentTypeMixed_15l8ch$_0 = 'Mixto';
    this.emailSupport_dqsx56$_0 = '<EMAIL>';
    this.favoritesNavigationTitle_wbnao4$_0 = 'Favoritos';
    this.favoritesTitle_us55l4$_0 = 'Favoritos';
    this.folderSelectionNavigationTitle_fjky1v$_0 = 'Videos';
    this.kidEmptyName_9v43hl$_0 = 'Primer Nombre';
    this.overtimeButtonSubtitle_pwevz6$_0 = '\xA1S\xD3LO PADRES!';
    this.overtimeButtonTitleLine1_e4dywf$_0 = 'Presione cualquier lugar';
    this.overtimeButtonTitleLine2_e4dyvk$_0 = 'para restablecer el temporizador';
    this.overtimeTitle_gjcu50$_0 = '\xA1Se ha terminado tu tiempo de pantalla!';
    this.privacyPolicyTitle_nckn1x$_0 = 'Nuestra pol\xEDtica de privacidad';
    this.settingsAgeTitle_4bv631$_0 = 'Filtrar por edad';
    this.settingsContentTypeTitle_dovlk1$_0 = 'Elegir contenido';
    this.settingsExtraTitle_8gy2u6$_0 = 'Extraer';
    this.settingsFullScreen_1hqp2t$_0 = 'Pantalla completa';
    this.settingsLanguageTitle_toc4cu$_0 = 'Idiomas';
    this.settingsNavigationTitle_i8wssq$_0 = 'Ajustes';
    this.settingsPrivacyPolicy_tynauk$_0 = 'Nuestra pol\xEDtica de privacidad';
    this.settingsScreenTimeLimitOptionOff_4nuuxa$_0 = 'Apagado';
    this.settingsScreenTimeLimitTitle_5l6rww$_0 = 'L\xEDmite de tiempo';
    this.settingsSound_rnpdj3$_0 = 'Sonar';
    this.subscribeButtonTitle_owbbub$_0 = '\xA1VER AHORA!';
    this.subscribeHaveAccount_trrinm$_0 = 'Ya tienes una cuenta';
    this.subscribePrivacyText_jjia3i$_0 = 'Nuestra pol\xEDtica de privacidad se puede encontrar en: kidjo.tv/privacy y nuestros t\xE9rminos de servicio pueden encontrarse en: https://www.kidjo.tv/terms';
    this.subscribeSubtitle_vvmaqf$_0 = '100% divertido, 100% sin publicidad';
    this.subscribeTitle_yozq9x$_0 = "<span class='subscribe-highlight-text'>\xA1ILIMITADO<\/span><br/>acceso de video!";
    this.timeMinuteLong_xa7tp0$_0 = 'minuto';
    this.timeMinuteLongPlural_gsnx3c$_0 = 'minutos';
    this.timeMinuteShort_y5o3ow$_0 = 'min';
    this.timeMinuteShortPlural_vfftck$_0 = 'mins';
    this.toastApiDefaultError_gqalvz$_0 = '\xA1Oh, oh! \xA1Algo sali\xF3 mal!';
    this.toastRequiresInternet_umex5h$_0 = 'Lo sentimos, eso requiere una conexi\xF3n a internet.';
    this.toastTooYoung_npri74$_0 = '\xA1Lo sentimos, por favor trae a tu padre!';
    this.urlHelp_lfsl9h$_0 = 'https://www.kidjo.tv/faq';
    this.urlPrivacy_monn74$_0 = 'https://www.kidjo.tv/privacy';
    this.urlTerms_jmyjjj$_0 = 'https://www.kidjo.tv/terms';
    this.unsubscribe_9i8u4a$_0 = 'Unsubscribe';
  }
  Object.defineProperty(es_StringCheese.prototype, 'ageGateAgePlaceholderYear', {
    get: function () {
      return this.ageGateAgePlaceholderYear_9tsei8$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'ageGateAgeSubtext', {
    get: function () {
      return this.ageGateAgeSubtext_oyqrkd$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'ageGateSubtitle', {
    get: function () {
      return this.ageGateSubtitle_essj3r$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'ageGateTitle', {
    get: function () {
      return this.ageGateTitle_lt8emz$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'cardFavoritesRemove', {
    get: function () {
      return this.cardFavoritesRemove_z7oxz4$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'cardVideoFavoritesAdd', {
    get: function () {
      return this.cardVideoFavoritesAdd_q4b0h6$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'cardVideoLockTitle', {
    get: function () {
      return this.cardVideoLockTitle_lqlth5$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'contentTypeEducation', {
    get: function () {
      return this.contentTypeEducation_ldvrsk$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'contentTypeEntertainment', {
    get: function () {
      return this.contentTypeEntertainment_gr8n84$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'contentTypeMixed', {
    get: function () {
      return this.contentTypeMixed_15l8ch$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'emailSupport', {
    get: function () {
      return this.emailSupport_dqsx56$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'favoritesNavigationTitle', {
    get: function () {
      return this.favoritesNavigationTitle_wbnao4$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'favoritesTitle', {
    get: function () {
      return this.favoritesTitle_us55l4$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'folderSelectionNavigationTitle', {
    get: function () {
      return this.folderSelectionNavigationTitle_fjky1v$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'kidEmptyName', {
    get: function () {
      return this.kidEmptyName_9v43hl$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'overtimeButtonSubtitle', {
    get: function () {
      return this.overtimeButtonSubtitle_pwevz6$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'overtimeButtonTitleLine1', {
    get: function () {
      return this.overtimeButtonTitleLine1_e4dywf$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'overtimeButtonTitleLine2', {
    get: function () {
      return this.overtimeButtonTitleLine2_e4dyvk$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'overtimeTitle', {
    get: function () {
      return this.overtimeTitle_gjcu50$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'privacyPolicyTitle', {
    get: function () {
      return this.privacyPolicyTitle_nckn1x$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsAgeTitle', {
    get: function () {
      return this.settingsAgeTitle_4bv631$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsContentTypeTitle', {
    get: function () {
      return this.settingsContentTypeTitle_dovlk1$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsExtraTitle', {
    get: function () {
      return this.settingsExtraTitle_8gy2u6$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsFullScreen', {
    get: function () {
      return this.settingsFullScreen_1hqp2t$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsLanguageTitle', {
    get: function () {
      return this.settingsLanguageTitle_toc4cu$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsNavigationTitle', {
    get: function () {
      return this.settingsNavigationTitle_i8wssq$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsPrivacyPolicy', {
    get: function () {
      return this.settingsPrivacyPolicy_tynauk$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsScreenTimeLimitOptionOff', {
    get: function () {
      return this.settingsScreenTimeLimitOptionOff_4nuuxa$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsScreenTimeLimitTitle', {
    get: function () {
      return this.settingsScreenTimeLimitTitle_5l6rww$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'settingsSound', {
    get: function () {
      return this.settingsSound_rnpdj3$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'subscribeButtonTitle', {
    get: function () {
      return this.subscribeButtonTitle_owbbub$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'subscribeHaveAccount', {
    get: function () {
      return this.subscribeHaveAccount_trrinm$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'subscribePrivacyText', {
    get: function () {
      return this.subscribePrivacyText_jjia3i$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'subscribeSubtitle', {
    get: function () {
      return this.subscribeSubtitle_vvmaqf$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'subscribeTitle', {
    get: function () {
      return this.subscribeTitle_yozq9x$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'timeMinuteLong', {
    get: function () {
      return this.timeMinuteLong_xa7tp0$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'timeMinuteLongPlural', {
    get: function () {
      return this.timeMinuteLongPlural_gsnx3c$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'timeMinuteShort', {
    get: function () {
      return this.timeMinuteShort_y5o3ow$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'timeMinuteShortPlural', {
    get: function () {
      return this.timeMinuteShortPlural_vfftck$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'toastApiDefaultError', {
    get: function () {
      return this.toastApiDefaultError_gqalvz$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'toastRequiresInternet', {
    get: function () {
      return this.toastRequiresInternet_umex5h$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'toastTooYoung', {
    get: function () {
      return this.toastTooYoung_npri74$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'urlHelp', {
    get: function () {
      return this.urlHelp_lfsl9h$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'urlPrivacy', {
    get: function () {
      return this.urlPrivacy_monn74$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'urlTerms', {
    get: function () {
      return this.urlTerms_jmyjjj$_0;
    }
  });
  Object.defineProperty(es_StringCheese.prototype, 'unsubscribe', {
    get: function () {
      return this.unsubscribe_9i8u4a$_0;
    }
  });
  es_StringCheese.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'es_StringCheese',
    interfaces: [StringCheese]
  };
  function fr_StringCheese() {
    this.ageGateAgePlaceholderYear_r50ewi$_0 = 'A';
    this.ageGateAgeSubtext_s3wv0x$_0 = 'Cette information ne sera pas conserv\xE9e.';
    this.ageGateSubtitle_h6p5uv$_0 = 'Veuillez renseigner votre ann\xE9e de naissance';
    this.ageGateTitle_xjk6o3$_0 = 'Bonjour,';
    this.cardFavoritesRemove_w3kg9u$_0 = 'EFFACER';
    this.cardVideoFavoritesAdd_4kidqk$_0 = 'FAVORIS';
    this.cardVideoLockTitle_a5uml3$_0 = 'D\xE9verrouiller';
    this.contentTypeEducation_5exwmi$_0 = 'Apprendre';
    this.contentTypeEntertainment_vlq1ti$_0 = "S'amuser";
    this.contentTypeMixed_1pr1i5$_0 = 'M\xE9li-M\xE9lo';
    this.emailSupport_1xijiw$_0 = '<EMAIL>';
    this.favoritesNavigationTitle_hh5w2q$_0 = 'Playlist';
    this.favoritesTitle_ymul06$_0 = 'Playlist';
    this.folderSelectionNavigationTitle_3bjsor$_0 = 'Vid\xE9os';
    this.kidEmptyName_5t7d6h$_0 = 'Pr\xE9nom';
    this.overtimeButtonSubtitle_2v875s$_0 = 'Que pour les Parents !';
    this.overtimeButtonTitleLine1_q3foz$_0 = "Appuyez n'importe o\xF9";
    this.overtimeButtonTitleLine2_q3fpu$_0 = 'Afin de r\xE9initialiser le Timer ?';
    this.overtimeTitle_59ft0y$_0 = 'Votre session a expir\xE9 !';
    this.privacyPolicyTitle_brtg5v$_0 = 'Mentions L\xE9gales';
    this.settingsAgeTitle_1giw8f$_0 = 'Quel \xE2ge a votre enfant ?';
    this.settingsContentTypeTitle_sjd05f$_0 = 'Quel type de contenu ?';
    this.settingsExtraTitle_k1p9q8$_0 = 'Autres';
    this.settingsFullScreen_a30ht9$_0 = 'Plein \xE9cran';
    this.settingsLanguageTitle_jszavo$_0 = 'Langue';
    this.settingsNavigationTitle_dco7mg$_0 = 'R\xE9glages';
    this.settingsPrivacyPolicy_8euo3y$_0 = 'Mentions L\xE9gales';
    this.settingsScreenTimeLimitOptionOff_bi0xqo$_0 = 'Aucune limite';
    this.settingsScreenTimeLimitTitle_grosry$_0 = "Limitez le temps d'usage";
    this.settingsSound_gdscf1$_0 = 'Musique et Son';
    this.subscribeButtonTitle_jbz1pr$_0 = 'COMMENCER !';
    this.subscribeHaveAccount_egiuwg$_0 = "J'ai d\xE9j\xE0 un compte";
    this.subscribePrivacyText_79bebk$_0 = "Plus d'infos sur les conditions d'utilisation ici : kidjo.tv/privacy https://www.kidjo.tv/terms";
    this.subscribeSubtitle_l71buv$_0 = '100% Fun, 100% Sans Pub';
    this.subscribeTitle_t2vew3$_0 = "Acc\xE8s aux vid\xE9os en<br/><span class='subscribe-highlight-text'>ILLIMIT\xC9 !<\/span>";
    this.timeMinuteLong_ro3ib6$_0 = 'minute';
    this.timeMinuteLongPlural_a05rbq$_0 = 'minutes';
    this.timeMinuteShort_266eqa$_0 = 'min';
    this.timeMinuteShortPlural_9vn6ly$_0 = 'mins';
    this.toastApiDefaultError_a2j2j3$_0 = 'Oops ! Un probl\xE8me est survenu !';
    this.toastRequiresInternet_iuwi31$_0 = 'Veuillez vous connecter \xE0 Internet.';
    this.toastTooYoung_cfuh32$_0 = "Demande de l'aide \xE0 tes parents !";
    this.urlHelp_rwbnjt$_0 = 'https://www.kidjo.tv/faq';
    this.urlPrivacy_wyejou$_0 = 'https://www.kidjo.tv/privacy';
    this.urlTerms_i5bw6r$_0 = 'https://www.kidjo.tv/terms';
    this.unsubscribe_alxrzc$_0 = 'Unsubscribe';
  }
  Object.defineProperty(fr_StringCheese.prototype, 'ageGateAgePlaceholderYear', {
    get: function () {
      return this.ageGateAgePlaceholderYear_r50ewi$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'ageGateAgeSubtext', {
    get: function () {
      return this.ageGateAgeSubtext_s3wv0x$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'ageGateSubtitle', {
    get: function () {
      return this.ageGateSubtitle_h6p5uv$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'ageGateTitle', {
    get: function () {
      return this.ageGateTitle_xjk6o3$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'cardFavoritesRemove', {
    get: function () {
      return this.cardFavoritesRemove_w3kg9u$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'cardVideoFavoritesAdd', {
    get: function () {
      return this.cardVideoFavoritesAdd_4kidqk$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'cardVideoLockTitle', {
    get: function () {
      return this.cardVideoLockTitle_a5uml3$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'contentTypeEducation', {
    get: function () {
      return this.contentTypeEducation_5exwmi$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'contentTypeEntertainment', {
    get: function () {
      return this.contentTypeEntertainment_vlq1ti$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'contentTypeMixed', {
    get: function () {
      return this.contentTypeMixed_1pr1i5$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'emailSupport', {
    get: function () {
      return this.emailSupport_1xijiw$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'favoritesNavigationTitle', {
    get: function () {
      return this.favoritesNavigationTitle_hh5w2q$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'favoritesTitle', {
    get: function () {
      return this.favoritesTitle_ymul06$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'folderSelectionNavigationTitle', {
    get: function () {
      return this.folderSelectionNavigationTitle_3bjsor$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'kidEmptyName', {
    get: function () {
      return this.kidEmptyName_5t7d6h$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'overtimeButtonSubtitle', {
    get: function () {
      return this.overtimeButtonSubtitle_2v875s$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'overtimeButtonTitleLine1', {
    get: function () {
      return this.overtimeButtonTitleLine1_q3foz$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'overtimeButtonTitleLine2', {
    get: function () {
      return this.overtimeButtonTitleLine2_q3fpu$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'overtimeTitle', {
    get: function () {
      return this.overtimeTitle_59ft0y$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'privacyPolicyTitle', {
    get: function () {
      return this.privacyPolicyTitle_brtg5v$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsAgeTitle', {
    get: function () {
      return this.settingsAgeTitle_1giw8f$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsContentTypeTitle', {
    get: function () {
      return this.settingsContentTypeTitle_sjd05f$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsExtraTitle', {
    get: function () {
      return this.settingsExtraTitle_k1p9q8$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsFullScreen', {
    get: function () {
      return this.settingsFullScreen_a30ht9$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsLanguageTitle', {
    get: function () {
      return this.settingsLanguageTitle_jszavo$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsNavigationTitle', {
    get: function () {
      return this.settingsNavigationTitle_dco7mg$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsPrivacyPolicy', {
    get: function () {
      return this.settingsPrivacyPolicy_8euo3y$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsScreenTimeLimitOptionOff', {
    get: function () {
      return this.settingsScreenTimeLimitOptionOff_bi0xqo$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsScreenTimeLimitTitle', {
    get: function () {
      return this.settingsScreenTimeLimitTitle_grosry$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'settingsSound', {
    get: function () {
      return this.settingsSound_gdscf1$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'subscribeButtonTitle', {
    get: function () {
      return this.subscribeButtonTitle_jbz1pr$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'subscribeHaveAccount', {
    get: function () {
      return this.subscribeHaveAccount_egiuwg$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'subscribePrivacyText', {
    get: function () {
      return this.subscribePrivacyText_79bebk$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'subscribeSubtitle', {
    get: function () {
      return this.subscribeSubtitle_l71buv$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'subscribeTitle', {
    get: function () {
      return this.subscribeTitle_t2vew3$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'timeMinuteLong', {
    get: function () {
      return this.timeMinuteLong_ro3ib6$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'timeMinuteLongPlural', {
    get: function () {
      return this.timeMinuteLongPlural_a05rbq$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'timeMinuteShort', {
    get: function () {
      return this.timeMinuteShort_266eqa$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'timeMinuteShortPlural', {
    get: function () {
      return this.timeMinuteShortPlural_9vn6ly$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'toastApiDefaultError', {
    get: function () {
      return this.toastApiDefaultError_a2j2j3$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'toastRequiresInternet', {
    get: function () {
      return this.toastRequiresInternet_iuwi31$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'toastTooYoung', {
    get: function () {
      return this.toastTooYoung_cfuh32$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'urlHelp', {
    get: function () {
      return this.urlHelp_rwbnjt$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'urlPrivacy', {
    get: function () {
      return this.urlPrivacy_wyejou$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'urlTerms', {
    get: function () {
      return this.urlTerms_i5bw6r$_0;
    }
  });
  Object.defineProperty(fr_StringCheese.prototype, 'unsubscribe', {
    get: function () {
      return this.unsubscribe_alxrzc$_0;
    }
  });
  fr_StringCheese.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'fr_StringCheese',
    interfaces: [StringCheese]
  };
  function pt_StringCheese() {
    this.ageGateAgePlaceholderYear_dx0au$_0 = 'A';
    this.ageGateAgeSubtext_gl09tj$_0 = 'Esta informa\xE7\xE3o nunca \xE9 armazenada e \xE9 usada apenas para obter acesso ao controle parental.';
    this.ageGateSubtitle_jirwtd$_0 = 'Por favor, confirme seu ano de nascimento antes de continuar.';
    this.ageGateTitle_1mjqbp$_0 = 'Ol\xE1,';
    this.cardFavoritesRemove_7mxlwq$_0 = 'EXCLUIR';
    this.cardVideoFavoritesAdd_7qzzkk$_0 = 'FAVORITO';
    this.cardVideoLockTitle_ozvapd$_0 = 'Desbloquear';
    this.contentTypeEducation_sfid2a$_0 = 'Educacional';
    this.contentTypeEntertainment_r2zhxe$_0 = 'Entretenimento';
    this.contentTypeMixed_35ngr9$_0 = 'Variado';
    this.emailSupport_xxhlgg$_0 = '<EMAIL>';
    this.favoritesNavigationTitle_54rdui$_0 = 'Playlist';
    this.favoritesTitle_nr73si$_0 = 'Playlist';
    this.folderSelectionNavigationTitle_mlrn7$_0 = 'V\xEDdeos';
    this.kidEmptyName_u1srsv$_0 = 'Nome';
    this.overtimeButtonSubtitle_ootjp4$_0 = 'SOMENTE PAIS!';
    this.overtimeButtonTitleLine1_d2hxx7$_0 = 'Clique em qualquer lugar';
    this.overtimeButtonTitleLine2_d2hxy2$_0 = 'Reiniciar o temporizador?';
    this.overtimeTitle_u24e7e$_0 = 'Voc\xEA ficou sem tempo de tela! At\xE9 a pr\xF3xima!';
    this.privacyPolicyTitle_ndwh4l$_0 = 'Nossa pol\xEDtica de privacidade';
    this.settingsAgeTitle_mgzb$_0 = 'Filtrar por idade';
    this.settingsContentTypeTitle_u5cjlh$_0 = 'Escolher conte\xFAdo';
    this.settingsExtraTitle_ftouyg$_0 = 'Extrair';
    this.settingsFullScreen_psdmvf$_0 = 'Tela cheia';
    this.settingsLanguageTitle_mzgwpo$_0 = 'Idioma';
    this.settingsNavigationTitle_f8toio$_0 = 'Configura\xE7\xF5es';
    this.settingsPrivacyPolicy_blc9xy$_0 = 'Nossa pol\xEDtica de privacidade';
    this.settingsScreenTimeLimitOptionOff_jaaxo8$_0 = 'Desligado';
    this.settingsScreenTimeLimitTitle_n7y9sq$_0 = 'Definir tempo de uso';
    this.settingsSound_tun4dn$_0 = 'Som';
    this.subscribeButtonTitle_3oleq1$_0 = 'VER AGORA!';
    this.subscribeHaveAccount_8k1ljc$_0 = 'J\xE1 tem uma conta';
    this.subscribePrivacyText_u9vurc$_0 = 'Nossa pol\xEDtica de privacidade pode ser encontrada em: kidjo.tv/privacy e nosssos termos de servi\xE7o podem ser encontrados em: kidjo.tv/terms';
    this.subscribeSubtitle_nhvszl$_0 = '100% divertido, 100% sem publicidade';
    this.subscribeTitle_tb69wl$_0 = "Acesso de v\xEDdeo<br/><span class='subscribe-highlight-text'>ILIMITADO!<\/span>";
    this.timeMinuteLong_upy6hi$_0 = 'minuto';
    this.timeMinuteLongPlural_x0q7ri$_0 = 'minutos';
    this.timeMinuteShort_w5gkkm$_0 = 'min';
    this.timeMinuteShortPlural_d24sfy$_0 = 'mins';
    this.toastApiDefaultError_x33iyv$_0 = 'Ah, n\xE3o! Algo deu errado!';
    this.toastRequiresInternet_m1e3x1$_0 = 'Desculpe, voc\xEA precisa de uma conex\xE3o \xE0 internet para fazer isso.';
    this.toastTooYoung_xskzpm$_0 = 'Sinto muito, por favor chame seus pais!';
    this.urlHelp_d1j0zz$_0 = 'https://www.kidjo.tv/faq';
    this.urlPrivacy_eyhobq$_0 = 'https://www.kidjo.tv/privacy';
    this.urlTerms_rjxqpn$_0 = 'https://www.kidjo.tv/terms';
    this.unsubscribe_kxj7hs$_0 = 'Unsubscribe';
  }
  Object.defineProperty(pt_StringCheese.prototype, 'ageGateAgePlaceholderYear', {
    get: function () {
      return this.ageGateAgePlaceholderYear_dx0au$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'ageGateAgeSubtext', {
    get: function () {
      return this.ageGateAgeSubtext_gl09tj$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'ageGateSubtitle', {
    get: function () {
      return this.ageGateSubtitle_jirwtd$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'ageGateTitle', {
    get: function () {
      return this.ageGateTitle_1mjqbp$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'cardFavoritesRemove', {
    get: function () {
      return this.cardFavoritesRemove_7mxlwq$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'cardVideoFavoritesAdd', {
    get: function () {
      return this.cardVideoFavoritesAdd_7qzzkk$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'cardVideoLockTitle', {
    get: function () {
      return this.cardVideoLockTitle_ozvapd$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'contentTypeEducation', {
    get: function () {
      return this.contentTypeEducation_sfid2a$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'contentTypeEntertainment', {
    get: function () {
      return this.contentTypeEntertainment_r2zhxe$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'contentTypeMixed', {
    get: function () {
      return this.contentTypeMixed_35ngr9$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'emailSupport', {
    get: function () {
      return this.emailSupport_xxhlgg$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'favoritesNavigationTitle', {
    get: function () {
      return this.favoritesNavigationTitle_54rdui$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'favoritesTitle', {
    get: function () {
      return this.favoritesTitle_nr73si$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'folderSelectionNavigationTitle', {
    get: function () {
      return this.folderSelectionNavigationTitle_mlrn7$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'kidEmptyName', {
    get: function () {
      return this.kidEmptyName_u1srsv$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'overtimeButtonSubtitle', {
    get: function () {
      return this.overtimeButtonSubtitle_ootjp4$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'overtimeButtonTitleLine1', {
    get: function () {
      return this.overtimeButtonTitleLine1_d2hxx7$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'overtimeButtonTitleLine2', {
    get: function () {
      return this.overtimeButtonTitleLine2_d2hxy2$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'overtimeTitle', {
    get: function () {
      return this.overtimeTitle_u24e7e$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'privacyPolicyTitle', {
    get: function () {
      return this.privacyPolicyTitle_ndwh4l$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsAgeTitle', {
    get: function () {
      return this.settingsAgeTitle_mgzb$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsContentTypeTitle', {
    get: function () {
      return this.settingsContentTypeTitle_u5cjlh$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsExtraTitle', {
    get: function () {
      return this.settingsExtraTitle_ftouyg$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsFullScreen', {
    get: function () {
      return this.settingsFullScreen_psdmvf$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsLanguageTitle', {
    get: function () {
      return this.settingsLanguageTitle_mzgwpo$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsNavigationTitle', {
    get: function () {
      return this.settingsNavigationTitle_f8toio$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsPrivacyPolicy', {
    get: function () {
      return this.settingsPrivacyPolicy_blc9xy$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsScreenTimeLimitOptionOff', {
    get: function () {
      return this.settingsScreenTimeLimitOptionOff_jaaxo8$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsScreenTimeLimitTitle', {
    get: function () {
      return this.settingsScreenTimeLimitTitle_n7y9sq$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'settingsSound', {
    get: function () {
      return this.settingsSound_tun4dn$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'subscribeButtonTitle', {
    get: function () {
      return this.subscribeButtonTitle_3oleq1$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'subscribeHaveAccount', {
    get: function () {
      return this.subscribeHaveAccount_8k1ljc$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'subscribePrivacyText', {
    get: function () {
      return this.subscribePrivacyText_u9vurc$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'subscribeSubtitle', {
    get: function () {
      return this.subscribeSubtitle_nhvszl$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'subscribeTitle', {
    get: function () {
      return this.subscribeTitle_tb69wl$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'timeMinuteLong', {
    get: function () {
      return this.timeMinuteLong_upy6hi$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'timeMinuteLongPlural', {
    get: function () {
      return this.timeMinuteLongPlural_x0q7ri$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'timeMinuteShort', {
    get: function () {
      return this.timeMinuteShort_w5gkkm$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'timeMinuteShortPlural', {
    get: function () {
      return this.timeMinuteShortPlural_d24sfy$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'toastApiDefaultError', {
    get: function () {
      return this.toastApiDefaultError_x33iyv$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'toastRequiresInternet', {
    get: function () {
      return this.toastRequiresInternet_m1e3x1$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'toastTooYoung', {
    get: function () {
      return this.toastTooYoung_xskzpm$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'urlHelp', {
    get: function () {
      return this.urlHelp_d1j0zz$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'urlPrivacy', {
    get: function () {
      return this.urlPrivacy_eyhobq$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'urlTerms', {
    get: function () {
      return this.urlTerms_rjxqpn$_0;
    }
  });
  Object.defineProperty(pt_StringCheese.prototype, 'unsubscribe', {
    get: function () {
      return this.unsubscribe_kxj7hs$_0;
    }
  });
  pt_StringCheese.$metadata$ = {
    kind: Kind_CLASS,
    simpleName: 'pt_StringCheese',
    interfaces: [StringCheese]
  };
  $$importsForInline$$.app = _;
  var package$net = _.net || (_.net = {});
  var package$kidjo = package$net.kidjo || (package$net.kidjo = {});
  var package$app = package$kidjo.app || (package$kidjo.app = {});
  var package$shared = package$app.shared || (package$app.shared = {});
  var package$controllers = package$shared.controllers || (package$shared.controllers = {});
  package$controllers.Api = Api;
  Object.defineProperty(AudioController$SoundEffects, 'GENERAL_BUTTON', {
    get: AudioController$SoundEffects$GENERAL_BUTTON_getInstance
  });
  Object.defineProperty(AudioController$SoundEffects, 'LOCK', {
    get: AudioController$SoundEffects$LOCK_getInstance
  });
  Object.defineProperty(AudioController$SoundEffects, 'SWIPE_LEFT', {
    get: AudioController$SoundEffects$SWIPE_LEFT_getInstance
  });
  Object.defineProperty(AudioController$SoundEffects, 'SWIPE_RIGHT', {
    get: AudioController$SoundEffects$SWIPE_RIGHT_getInstance
  });
  Object.defineProperty(AudioController$SoundEffects, 'VALIDATION', {
    get: AudioController$SoundEffects$VALIDATION_getInstance
  });
  Object.defineProperty(AudioController$SoundEffects, 'FOLDER_OPEN', {
    get: AudioController$SoundEffects$FOLDER_OPEN_getInstance
  });
  Object.defineProperty(AudioController$SoundEffects, 'FOLDER_CLOSE', {
    get: AudioController$SoundEffects$FOLDER_CLOSE_getInstance
  });
  AudioController.SoundEffects = AudioController$SoundEffects;
  package$controllers.AudioController = AudioController;
  Object.defineProperty(BootController$State, 'JUST_BOOTED', {
    get: BootController$State$JUST_BOOTED_getInstance
  });
  Object.defineProperty(BootController$State, 'LOCAL_LOAD', {
    get: BootController$State$LOCAL_LOAD_getInstance
  });
  Object.defineProperty(BootController$State, 'LOCAL_LOAD_COMPLETE', {
    get: BootController$State$LOCAL_LOAD_COMPLETE_getInstance
  });
  Object.defineProperty(BootController$State, 'FIRST_NETWORK', {
    get: BootController$State$FIRST_NETWORK_getInstance
  });
  Object.defineProperty(BootController$State, 'ERROR', {
    get: BootController$State$ERROR_getInstance
  });
  Object.defineProperty(BootController$State, 'COMPLETE', {
    get: BootController$State$COMPLETE_getInstance
  });
  BootController.State = BootController$State;
  package$controllers.BootController = BootController;
  Object.defineProperty(package$controllers, 'OFF', {
    get: function () {
      return OFF;
    }
  });
  CardPager.Listener = CardPager$Listener;
  package$controllers.CardPager = CardPager;
  package$controllers.DependencyContainer = DependencyContainer;
  package$controllers.CreateAppDependencyContainer_wcdjfz$ = CreateAppDependencyContainer;
  package$controllers.FavoritesController = FavoritesController;
  package$controllers.KidsController = KidsController;
  package$controllers.PlatformDependencyProvider = PlatformDependencyProvider;
  Object.defineProperty(SettingsController, 'Companion', {
    get: SettingsController$Companion_getInstance
  });
  package$controllers.SettingsController = SettingsController;
  package$controllers.UserController = UserController;
  var package$common = package$kidjo.common || (package$kidjo.common = {});
  var package$extensions = package$common.extensions || (package$common.extensions = {});
  package$extensions.getBit_dqglrj$ = getBit;
  package$extensions.setBit_z3q3t4$ = setBit;
  var package$json = package$common.json || (package$common.json = {});
  var package$v3 = package$json.v3 || (package$json.v3 = {});
  package$v3.deviceData_k7hffx$ = deviceData;
  package$v3.toJson_deviceData_1u4k1r$ = toJson_deviceData;
  package$v3.JsonFactoryV3 = JsonFactoryV3;
  package$v3.folder_xh21cm$ = folder;
  package$v3.folderColor_otoqt0$ = folderColor;
  package$v3.toJson_folder_prtinn$ = toJson_folder;
  package$v3.toJson_folderWithColor_r3w4on$ = toJson_folderWithColor;
  package$v3.game_xh21cm$ = game;
  package$v3.toJson_game_ons3o1$ = toJson_game;
  package$v3.kid_xh21cm$ = kid;
  package$v3.user_w785cy$ = user;
  package$v3.video_xh21cm$ = video;
  package$v3.videoBackpack_xh21cm$ = videoBackpack;
  package$v3.toJson_video_gmnltw$ = toJson_video;
  var package$localkeystore = package$common.localkeystore || (package$common.localkeystore = {});
  package$localkeystore.getDeviceData_di10v8$ = getDeviceData;
  package$localkeystore.setDeviceData_ua89td$ = setDeviceData;
  package$localkeystore.getKids_f72m8h$ = getKids;
  package$localkeystore.setKids_na786c$ = setKids;
  package$localkeystore.getSettings_contentType_f72m8h$ = getSettings_contentType;
  package$localkeystore.setSettings_contentType_krdd5i$ = setSettings_contentType;
  package$localkeystore.getSettings_language_g45s84$ = getSettings_language;
  package$localkeystore.setSettings_language_fc5cj3$ = setSettings_language;
  package$localkeystore.getSettings_screenTimeLimitInMinutes_4kyy5r$ = getSettings_screenTimeLimitInMinutes;
  package$localkeystore.setSettings_screenTimeLimitInMinutes_ic4zb0$ = setSettings_screenTimeLimitInMinutes;
  package$localkeystore.getSettings_screenTimeWatchedInMinutes_4kyy5r$ = getSettings_screenTimeWatchedInMinutes;
  package$localkeystore.setSettings_screenTimeWatchedInMinutes_ic4zb0$ = setSettings_screenTimeWatchedInMinutes;
  package$localkeystore.getSettings_screenTimeWatchedDate_6mmy$ = getSettings_screenTimeWatchedDate;
  package$localkeystore.setSettings_screenTimeWatchedDate_44umkz$ = setSettings_screenTimeWatchedDate;
  package$localkeystore.getSettings_allowSounds_5m9bre$ = getSettings_allowSounds;
  package$localkeystore.setSettings_allowSounds_g803kz$ = setSettings_allowSounds;
  package$localkeystore.setUser_h5of18$ = setUser;
  package$localkeystore.getUser_dwqjbj$ = getUser;
  package$localkeystore.setFavoriteVideos_733ijd$ = setFavoriteVideos;
  package$localkeystore.getFavoriteVideos_f72m8h$ = getFavoriteVideos;
  package$localkeystore.SmallStorageController = SmallStorageController;
  Object.defineProperty(package$localkeystore, 'LOCAL_STORAGE_KEY_DEVICE_DATA_PREFIX_8be2vx$', {
    get: function () {
      return LOCAL_STORAGE_KEY_DEVICE_DATA_PREFIX;
    }
  });
  Object.defineProperty(package$localkeystore, 'LOCAL_STORAGE_KEY_USER_PREFIX_8be2vx$', {
    get: function () {
      return LOCAL_STORAGE_KEY_USER_PREFIX;
    }
  });
  Object.defineProperty(package$localkeystore, 'LOCAL_STORAGE_KEY_KIDS_PREFIX_8be2vx$', {
    get: function () {
      return LOCAL_STORAGE_KEY_KIDS_PREFIX;
    }
  });
  Object.defineProperty(package$localkeystore, 'LOCAL_STORAGE_KEY_SETTINGS_PREFIX_8be2vx$', {
    get: function () {
      return LOCAL_STORAGE_KEY_SETTINGS_PREFIX;
    }
  });
  Object.defineProperty(package$localkeystore, 'LOCAL_STORAGE_KEY_FAVORITES_8be2vx$', {
    get: function () {
      return LOCAL_STORAGE_KEY_FAVORITES;
    }
  });
  Object.defineProperty(SubscriptionEvent$Type, 'OPEN', {
    get: SubscriptionEvent$Type$OPEN_getInstance
  });
  Object.defineProperty(SubscriptionEvent$Type, 'WENT_OFFLINE', {
    get: SubscriptionEvent$Type$WENT_OFFLINE_getInstance
  });
  Object.defineProperty(SubscriptionEvent$Type, 'DID_SUBSCRIBE', {
    get: SubscriptionEvent$Type$DID_SUBSCRIBE_getInstance
  });
  Object.defineProperty(SubscriptionEvent$Type, 'NOT_SUBSCRIBE', {
    get: SubscriptionEvent$Type$NOT_SUBSCRIBE_getInstance
  });
  Object.defineProperty(SubscriptionEvent$Type, 'Companion', {
    get: SubscriptionEvent$Type$Companion_getInstance
  });
  SubscriptionEvent.Type = SubscriptionEvent$Type;
  var package$models = package$common.models || (package$common.models = {});
  package$models.SubscriptionEvent = SubscriptionEvent;
  Object.defineProperty(BackpackItem$Type, 'VIDEO', {
    get: BackpackItem$Type$VIDEO_getInstance
  });
  BackpackItem.Type = BackpackItem$Type;
  package$models.BackpackItem = BackpackItem;
  Object.defineProperty(package$models, 'BUILD', {
    get: function () {
      return BUILD;
    }
  });
  package$models.DeviceData = DeviceData;
  Object.defineProperty(Folder$BackgroundColor, 'RED', {
    get: Folder$BackgroundColor$RED_getInstance
  });
  Object.defineProperty(Folder$BackgroundColor, 'YELLOW', {
    get: Folder$BackgroundColor$YELLOW_getInstance
  });
  Object.defineProperty(Folder$BackgroundColor, 'BLUE', {
    get: Folder$BackgroundColor$BLUE_getInstance
  });
  Object.defineProperty(Folder$BackgroundColor, 'GREEN', {
    get: Folder$BackgroundColor$GREEN_getInstance
  });
  Object.defineProperty(Folder$BackgroundColor, 'ORANGE', {
    get: Folder$BackgroundColor$ORANGE_getInstance
  });
  Object.defineProperty(Folder$BackgroundColor, 'PURPLE', {
    get: Folder$BackgroundColor$PURPLE_getInstance
  });
  Object.defineProperty(Folder$BackgroundColor, 'Companion', {
    get: Folder$BackgroundColor$Companion_getInstance
  });
  Folder.BackgroundColor = Folder$BackgroundColor;
  Object.defineProperty(Folder$ContentType, 'MIXED', {
    get: Folder$ContentType$MIXED_getInstance
  });
  Object.defineProperty(Folder$ContentType, 'ENTERTAINMENT', {
    get: Folder$ContentType$ENTERTAINMENT_getInstance
  });
  Object.defineProperty(Folder$ContentType, 'EDUCATION', {
    get: Folder$ContentType$EDUCATION_getInstance
  });
  Object.defineProperty(Folder$ContentType, 'Companion', {
    get: Folder$ContentType$Companion_getInstance
  });
  Folder.ContentType = Folder$ContentType;
  package$models.Folder = Folder;
  package$models.Game = Game;
  Object.defineProperty(Kid, 'Companion', {
    get: Kid$Companion_getInstance
  });
  package$models.Kid_init = Kid_init;
  package$models.Kid = Kid;
  Object.defineProperty(Language, 'ENGLISH', {
    get: Language$ENGLISH_getInstance
  });
  Object.defineProperty(Language, 'FRENCH', {
    get: Language$FRENCH_getInstance
  });
  Object.defineProperty(Language, 'SPANISH', {
    get: Language$SPANISH_getInstance
  });
  Object.defineProperty(Language, 'PORTUGUESE', {
    get: Language$PORTUGUESE_getInstance
  });
  Object.defineProperty(Language, 'Companion', {
    get: Language$Companion_getInstance
  });
  package$models.Language = Language;
  Object.defineProperty(User$AuthType, 'MIXED', {
    get: User$AuthType$MIXED_getInstance
  });
  Object.defineProperty(User$AuthType, 'EMAIL', {
    get: User$AuthType$EMAIL_getInstance
  });
  Object.defineProperty(User$AuthType, 'OAUTH', {
    get: User$AuthType$OAUTH_getInstance
  });
  Object.defineProperty(User$AuthType, 'Companion', {
    get: User$AuthType$Companion_getInstance
  });
  User.AuthType = User$AuthType;
  Object.defineProperty(User$OAuthType, 'NONE', {
    get: User$OAuthType$NONE_getInstance
  });
  Object.defineProperty(User$OAuthType, 'GOOGLE', {
    get: User$OAuthType$GOOGLE_getInstance
  });
  Object.defineProperty(User$OAuthType, 'FACEBOOK', {
    get: User$OAuthType$FACEBOOK_getInstance
  });
  Object.defineProperty(User$OAuthType, 'Companion', {
    get: User$OAuthType$Companion_getInstance
  });
  User.OAuthType = User$OAuthType;
  Object.defineProperty(User, 'Companion', {
    get: User$Companion_getInstance
  });
  package$models.User_init_4n31gb$ = User_init;
  package$models.User = User;
  Object.defineProperty(UserConfig, 'Companion', {
    get: UserConfig$Companion_getInstance
  });
  Object.defineProperty(UserConfig$Platform, 'KIDJO_BRAINTREE', {
    get: UserConfig$Platform$KIDJO_BRAINTREE_getInstance
  });
  Object.defineProperty(UserConfig$Platform, 'IOS', {
    get: UserConfig$Platform$IOS_getInstance
  });
  Object.defineProperty(UserConfig$Platform, 'PLAYSTORE', {
    get: UserConfig$Platform$PLAYSTORE_getInstance
  });
  Object.defineProperty(UserConfig$Platform, 'SAMSUNG', {
    get: UserConfig$Platform$SAMSUNG_getInstance
  });
  Object.defineProperty(UserConfig$Platform, 'AMAZON', {
    get: UserConfig$Platform$AMAZON_getInstance
  });
  Object.defineProperty(UserConfig$Platform, 'Companion', {
    get: UserConfig$Platform$Companion_getInstance
  });
  UserConfig.Platform = UserConfig$Platform;
  package$models.UserConfig = UserConfig;
  Object.defineProperty(Video, 'Companion', {
    get: Video$Companion_getInstance
  });
  Object.defineProperty(Video$Format, 'NONE', {
    get: Video$Format$NONE_getInstance
  });
  Object.defineProperty(Video$Format, 'HLS_720', {
    get: Video$Format$HLS_720_getInstance
  });
  Object.defineProperty(Video$Format, 'DASH_720', {
    get: Video$Format$DASH_720_getInstance
  });
  Object.defineProperty(Video$Format, 'MP4_720', {
    get: Video$Format$MP4_720_getInstance
  });
  Object.defineProperty(Video$Format, 'HLS_480', {
    get: Video$Format$HLS_480_getInstance
  });
  Object.defineProperty(Video$Format, 'DASH_480', {
    get: Video$Format$DASH_480_getInstance
  });
  Object.defineProperty(Video$Format, 'MP4_480', {
    get: Video$Format$MP4_480_getInstance
  });
  Object.defineProperty(Video$Format, 'MP4_360', {
    get: Video$Format$MP4_360_getInstance
  });
  Object.defineProperty(Video$Format, 'MP4_240', {
    get: Video$Format$MP4_240_getInstance
  });
  Object.defineProperty(Video$Format, 'Companion', {
    get: Video$Format$Companion_getInstance
  });
  Video.Format = Video$Format;
  package$models.Video_init_9u1k3q$ = Video_init;
  package$models.Video_init_ldjwg1$ = Video_init_0;
  package$models.Video = Video;
  Object.defineProperty(BroadcastReceiverListener, 'Companion', {
    get: BroadcastReceiverListener$Companion_getInstance
  });
  var package$platform = package$common.platform || (package$common.platform = {});
  var package$direct = package$platform.direct || (package$platform.direct = {});
  package$direct.BroadcastReceiverListener = BroadcastReceiverListener;
  Object.defineProperty(HttpRequestType, 'POST', {
    get: HttpRequestType$POST_getInstance
  });
  Object.defineProperty(HttpRequestType, 'GET', {
    get: HttpRequestType$GET_getInstance
  });
  package$direct.HttpRequestType = HttpRequestType;
  var package$interfaces = package$platform.interfaces || (package$platform.interfaces = {});
  package$interfaces.AudioLoader = AudioLoader;
  package$interfaces.AudioLoaderListener = AudioLoaderListener;
  var package$js = package$app.js || (package$app.js = {});
  package$js.main_kand9s$ = main;
  package$js.BootListener = BootListener;
  var package$application = package$js.application || (package$js.application = {});
  package$application.Application = Application;
  package$application.ApplicationListener = ApplicationListener;
  package$application.MediaQuery_isSmallPhone = MediaQuery_isSmallPhone;
  package$application.MediaQuery_isPhone = MediaQuery_isPhone;
  package$application.MediaQuery_isPortraitPhone = MediaQuery_isPortraitPhone;
  package$application.MediaQuery_isPortraitTablet = MediaQuery_isPortraitTablet;
  package$application.MediaQuery_isTablet = MediaQuery_isTablet;
  package$application.NavigationController = NavigationController;
  package$application.ViewControllerBuilder = ViewControllerBuilder;
  var package$extensions_0 = package$js.extensions || (package$js.extensions = {});
  package$extensions_0.createDiv_4wc2mh$ = createDiv;
  package$extensions_0.createImg_4wc2mh$ = createImg;
  package$extensions_0.createVideo_4wc2mh$ = createVideo;
  package$extensions_0.isFullScreenEnabled_4wc2mh$ = isFullScreenEnabled;
  package$extensions_0.polyfill_fullscreenElement_4wc2mh$ = polyfill_fullscreenElement;
  package$extensions_0.polyfill_webkitfullscreenchange_pcq9h2$ = polyfill_webkitfullscreenchange;
  package$extensions_0.enableFullScreenPolyFill_4wc2mh$ = enableFullScreenPolyFill;
  package$extensions_0.polyfill_requestFullScreen_ejp6nk$ = polyfill_requestFullScreen;
  package$extensions_0.polyfill_visibilityChange_pcq9h2$ = polyfill_visibilityChange;
  package$extensions_0.polyfill_hidden_4wc2mh$ = polyfill_hidden;
  Object.defineProperty(Route, 'Companion', {
    get: Route$Companion_getInstance
  });
  Object.defineProperty(Route$Path, 'CARD_SELECTION', {
    get: Route$Path$CARD_SELECTION_getInstance
  });
  Object.defineProperty(Route$Path, 'SETTINGS', {
    get: Route$Path$SETTINGS_getInstance
  });
  Object.defineProperty(Route$Path, 'FAVORITES', {
    get: Route$Path$FAVORITES_getInstance
  });
  Object.defineProperty(Route$Path, 'FOLDER', {
    get: Route$Path$FOLDER_getInstance
  });
  Object.defineProperty(Route$Path, 'VIDEO', {
    get: Route$Path$VIDEO_getInstance
  });
  Object.defineProperty(Route$Path, 'SUBSCRIBE', {
    get: Route$Path$SUBSCRIBE_getInstance
  });
  Object.defineProperty(Route$Path, 'MODAL_AGE_GATE', {
    get: Route$Path$MODAL_AGE_GATE_getInstance
  });
  Object.defineProperty(Route$Path, 'GAME', {
    get: Route$Path$GAME_getInstance
  });
  Object.defineProperty(Route$Path, 'Companion', {
    get: Route$Path$Companion_getInstance
  });
  Route.Path = Route$Path;
  var package$models_0 = package$js.models || (package$js.models = {});
  package$models_0.Route = Route;
  Object.defineProperty(AgeGateViewController, 'Companion', {
    get: AgeGateViewController$Companion_getInstance
  });
  var package$viewcontrollers = package$js.viewcontrollers || (package$js.viewcontrollers = {});
  package$viewcontrollers.AgeGateViewController = AgeGateViewController;
  Object.defineProperty(ModalResult, 'CANCELED', {
    get: ModalResult$CANCELED_getInstance
  });
  Object.defineProperty(ModalResult, 'FAIL', {
    get: ModalResult$FAIL_getInstance
  });
  Object.defineProperty(ModalResult, 'SUCCESS', {
    get: ModalResult$SUCCESS_getInstance
  });
  package$viewcontrollers.ModalResult = ModalResult;
  Object.defineProperty(BaseViewController$ViewState, 'VISIBLE', {
    get: BaseViewController$ViewState$VISIBLE_getInstance
  });
  Object.defineProperty(BaseViewController$ViewState, 'NOT_VISIBLE', {
    get: BaseViewController$ViewState$NOT_VISIBLE_getInstance
  });
  BaseViewController.ViewState = BaseViewController$ViewState;
  package$viewcontrollers.BaseViewController = BaseViewController;
  package$viewcontrollers.CardSelectionViewController = CardSelectionViewController;
  package$viewcontrollers.FavoritesViewController = FavoritesViewController;
  package$viewcontrollers.FolderViewController = FolderViewController;
  package$viewcontrollers.GameViewController = GameViewController;
  package$viewcontrollers.LockoutViewController = LockoutViewController;
  package$viewcontrollers.SettingsViewController = SettingsViewController;
  package$viewcontrollers.SubscriptionViewController = SubscriptionViewController;
  package$viewcontrollers.VideoPlayerViewController = VideoPlayerViewController;
  Object.defineProperty(AgeGateViewCoordinator, 'Companion', {
    get: AgeGateViewCoordinator$Companion_getInstance
  });
  var package$views = package$js.views || (package$js.views = {});
  package$views.AgeGateViewCoordinator = AgeGateViewCoordinator;
  package$views.CardSelectionViewCoordinator = CardSelectionViewCoordinator;
  package$views.FavoritesViewCoordinator = FavoritesViewCoordinator;
  package$views.FolderCardViewCoordinator = FolderCardViewCoordinator;
  package$views.CreateFolderCardView_5o4el$ = CreateFolderCardView;
  package$views.FolderViewCoordinator = FolderViewCoordinator;
  package$views.GameViewCoordinator = GameViewCoordinator;
  package$views.OvertimeViewCoordinator = OvertimeViewCoordinator;
  package$views.SettingsViewCoordinator = SettingsViewCoordinator;
  Object.defineProperty(SettingsPanelViewCoordinator$PanelType, 'AGE', {
    get: SettingsPanelViewCoordinator$PanelType$AGE_getInstance
  });
  Object.defineProperty(SettingsPanelViewCoordinator$PanelType, 'TIME_LIMIT', {
    get: SettingsPanelViewCoordinator$PanelType$TIME_LIMIT_getInstance
  });
  Object.defineProperty(SettingsPanelViewCoordinator$PanelType, 'CONTENT', {
    get: SettingsPanelViewCoordinator$PanelType$CONTENT_getInstance
  });
  Object.defineProperty(SettingsPanelViewCoordinator$PanelType, 'LANGUAGE', {
    get: SettingsPanelViewCoordinator$PanelType$LANGUAGE_getInstance
  });
  Object.defineProperty(SettingsPanelViewCoordinator$PanelType, 'EXTRA', {
    get: SettingsPanelViewCoordinator$PanelType$EXTRA_getInstance
  });
  Object.defineProperty(SettingsPanelViewCoordinator$PanelType, 'Companion', {
    get: SettingsPanelViewCoordinator$PanelType$Companion_getInstance
  });
  SettingsPanelViewCoordinator.PanelType = SettingsPanelViewCoordinator$PanelType;
  package$views.SettingsPanelViewCoordinator = SettingsPanelViewCoordinator;
  package$views.SideBarCoordinator = SideBarCoordinator;
  package$views.SubscriptionViewCoordinator = SubscriptionViewCoordinator;
  package$views.VideoCardCoordinator = VideoCardCoordinator;
  VideoListCoordinator.Listener = VideoListCoordinator$Listener;
  package$views.VideoListCoordinator = VideoListCoordinator;
  package$views.VideoPlayerViewCoordinator = VideoPlayerViewCoordinator;
  package$direct.SetGenericTimeout_u83pai$ = SetGenericTimeout;
  Object.defineProperty(BroadcastReceiverJs, 'Companion', {
    get: BroadcastReceiverJs$Companion_getInstance
  });
  package$direct.BroadcastReceiverJs = BroadcastReceiverJs;
  package$direct.HashFunctionsJs = HashFunctionsJs;
  package$direct.UrlBuilder = UrlBuilder;
  package$direct.HttpRequestReaderJs = HttpRequestReaderJs;
  package$direct.JsonReaderJs = JsonReaderJs;
  package$direct.LocalKeyValueStorageReaderJs = LocalKeyValueStorageReaderJs;
  package$direct.UTCTimeStamp = UTCTimeStamp;
  package$direct.IsUTCTimeStampToday_s8cxhz$ = IsUTCTimeStampToday;
  package$direct.UTCAPITimeStamp = UTCAPITimeStamp;
  var package$implementation = package$platform.implementation || (package$platform.implementation = {});
  package$implementation.JsAudioLoader = JsAudioLoader;
  Object.defineProperty(package$implementation, 'MUSIC_ASSET_FOLDER', {
    get: function () {
      return MUSIC_ASSET_FOLDER;
    }
  });
  Object.defineProperty(package$implementation, 'SOUND_EFFECTS_ASSET_FOLDER', {
    get: function () {
      return SOUND_EFFECTS_ASSET_FOLDER;
    }
  });
  Object.defineProperty(StringCheeseProvider, 'Companion', {
    get: StringCheeseProvider$Companion_getInstance
  });
  var package$org = _.org || (_.org = {});
  var package$stringcheese = package$org.stringcheese || (package$org.stringcheese = {});
  package$stringcheese.StringCheeseProvider = StringCheeseProvider;
  package$stringcheese.StringCheese = StringCheese;
  package$stringcheese.en_StringCheese = en_StringCheese;
  package$stringcheese.es_StringCheese = es_StringCheese;
  package$stringcheese.fr_StringCheese = fr_StringCheese;
  package$stringcheese.pt_StringCheese = pt_StringCheese;
  BASE_API_URL = '/v2.0';
  PATH_VERSION = '';
  PATH_REFRESH_WEB = '/app/api/3/device/refreshWeb';
  PATH_REFRESH = '/app/api/3/device/refreshWeb';
  PATH_CARDS_LIST = PATH_VERSION + '/getCards';
  PATH_CARD_FOLDER_GET = PATH_VERSION + '/cards/folder/';
  PATH_CARD_VIDEO_GET = PATH_VERSION + '/cards/video/';
  PATH_LANGUAGE_SET = PATH_VERSION + '/language/set';
  PATH_KID_UPDATE_1 = PATH_VERSION + '/kid/';
  PATH_KID_UPDATE_2 = '/update';
  OFF = -1;
  LOCAL_STORAGE_KEY_DEVICE_DATA_PREFIX = 'dd_';
  LOCAL_STORAGE_KEY_USER_PREFIX = 'u_';
  LOCAL_STORAGE_KEY_KIDS_PREFIX = 'ks_';
  LOCAL_STORAGE_KEY_SETTINGS_PREFIX = 'set_';
  LOCAL_STORAGE_KEY_FAVORITES = 'favs_';
  BUILD = 5;
  APP_SECRET = 'Tg4TwzUgR8';
  Z_INDEX_STEP = 100;
  Z_INDEX_MODAL_START = 30000;
  Z_INDEX_INIT_LOAD_SCREEN = 100002;
  Z_INDEX_LOADING_CARD = 100000;
  MAX_AGE = 120;
  MIN_AGE = 18;
  DISTANCE_TIL_SOUND = -50;
  AMOUNT_WATCHED_TIL_STORED = 1.0;
  BUFFER_SIZE = 0.1;
  ANIMATION_HEIGHT = 1058.0;
  ANIMATION_WIDTH = 245.0;
  ANIMATION_ASPECT_RATIO_WIDTH_TO_HEIGHT = ANIMATION_HEIGHT / ANIMATION_WIDTH;
  ANIMATION_ASPECT_RATIO_HEIGHT_TO_WIDTH = ANIMATION_WIDTH / ANIMATION_HEIGHT;
  VIDEO_CARD_ASPECT_RATIO_WIDTH_TO_HEIGHT = 0.73;
  VIDEO_CARD_ASPECT_RATIO_HEIGHT_TO_WIDTH = 1.0 / VIDEO_CARD_ASPECT_RATIO_WIDTH_TO_HEIGHT;
  VIDEO_CARD_IMAGE_ASPECT_RATIO = 9.0 / 16.0;
  VIDEO_CARD_SIDE_PADDING = 16.0;
  VIDEO_CARD_MIN_WIDTH = 190.0;
  VIDEO_CARD_TARGET_NUMBER_PER_VIEW_SMALL_MOBILE = 1.9;
  VIDEO_CARD_TARGET_NUMBER_PER_VIEW_MOBILE = 2.4;
  VIDEO_CARD_TARGET_NUMBER_PER_VIEW_TABLET = 2.8;
  VIDEO_CARD_TARGET_NUMBER_PER_VIEW_MOBILE_PORTRAIT = 1.0;
  MUSIC_ASSET_FOLDER = 'music/';
  SOUND_EFFECTS_ASSET_FOLDER = 'sounds/';
  main([]);
  Kotlin.defineModule('app', _);
  return _;
}(typeof app === 'undefined' ? {} : app, kotlin);

//# sourceMappingURL=app.js.map
