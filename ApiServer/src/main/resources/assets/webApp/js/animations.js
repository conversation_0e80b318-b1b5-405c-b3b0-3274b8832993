!function(a){var b,c,d="0.4.2",e="hasOwnProperty",f=/[\.\/]/,g=/\s*,\s*/,h="*",i=function(a,b){return a-b},j={n:{}},k=function(){for(var a=0,b=this.length;b>a;a++)if("undefined"!=typeof this[a])return this[a]},l=function(){for(var a=this.length;--a;)if("undefined"!=typeof this[a])return this[a]},m=function(a,d){a=String(a);var e,f=c,g=Array.prototype.slice.call(arguments,2),h=m.listeners(a),j=0,n=[],o={},p=[],q=b;p.firstDefined=k,p.lastDefined=l,b=a,c=0;for(var r=0,s=h.length;s>r;r++)"zIndex"in h[r]&&(n.push(h[r].zIndex),h[r].zIndex<0&&(o[h[r].zIndex]=h[r]));for(n.sort(i);n[j]<0;)if(e=o[n[j++]],p.push(e.apply(d,g)),c)return c=f,p;for(r=0;s>r;r++)if(e=h[r],"zIndex"in e)if(e.zIndex==n[j]){if(p.push(e.apply(d,g)),c)break;do if(j++,e=o[n[j]],e&&p.push(e.apply(d,g)),c)break;while(e)}else o[e.zIndex]=e;else if(p.push(e.apply(d,g)),c)break;return c=f,b=q,p};m._events=j,m.listeners=function(a){var b,c,d,e,g,i,k,l,m=a.split(f),n=j,o=[n],p=[];for(e=0,g=m.length;g>e;e++){for(l=[],i=0,k=o.length;k>i;i++)for(n=o[i].n,c=[n[m[e]],n[h]],d=2;d--;)b=c[d],b&&(l.push(b),p=p.concat(b.f||[]));o=l}return p},m.on=function(a,b){if(a=String(a),"function"!=typeof b)return function(){};for(var c=a.split(g),d=0,e=c.length;e>d;d++)!function(a){for(var c,d=a.split(f),e=j,g=0,h=d.length;h>g;g++)e=e.n,e=e.hasOwnProperty(d[g])&&e[d[g]]||(e[d[g]]={n:{}});for(e.f=e.f||[],g=0,h=e.f.length;h>g;g++)if(e.f[g]==b){c=!0;break}!c&&e.f.push(b)}(c[d]);return function(a){+a==+a&&(b.zIndex=+a)}},m.f=function(a){var b=[].slice.call(arguments,1);return function(){m.apply(null,[a,null].concat(b).concat([].slice.call(arguments,0)))}},m.stop=function(){c=1},m.nt=function(a){return a?new RegExp("(?:\\.|\\/|^)"+a+"(?:\\.|\\/|$)").test(b):b},m.nts=function(){return b.split(f)},m.off=m.unbind=function(a,b){if(!a)return void(m._events=j={n:{}});var c=a.split(g);if(c.length>1)for(var d=0,i=c.length;i>d;d++)m.off(c[d],b);else{c=a.split(f);var k,l,n,d,i,o,p,q=[j];for(d=0,i=c.length;i>d;d++)for(o=0;o<q.length;o+=n.length-2){if(n=[o,1],k=q[o].n,c[d]!=h)k[c[d]]&&n.push(k[c[d]]);else for(l in k)k[e](l)&&n.push(k[l]);q.splice.apply(q,n)}for(d=0,i=q.length;i>d;d++)for(k=q[d];k.n;){if(b){if(k.f){for(o=0,p=k.f.length;p>o;o++)if(k.f[o]==b){k.f.splice(o,1);break}!k.f.length&&delete k.f}for(l in k.n)if(k.n[e](l)&&k.n[l].f){var r=k.n[l].f;for(o=0,p=r.length;p>o;o++)if(r[o]==b){r.splice(o,1);break}!r.length&&delete k.n[l].f}}else{delete k.f;for(l in k.n)k.n[e](l)&&k.n[l].f&&delete k.n[l].f}k=k.n}}},m.once=function(a,b){var c=function(){return m.unbind(a,c),b.apply(this,arguments)};return m.on(a,c)},m.version=d,m.toString=function(){return"You are running Eve "+d},"undefined"!=typeof module&&module.exports?module.exports=m:"function"==typeof define&&define.amd?define("eve",[],function(){return m}):a.eve=m}(this),function(a,b){if("function"==typeof define&&define.amd)define(["eve"],function(c){return b(a,c)});else if("undefined"!=typeof exports){var c=require("eve");module.exports=b(a,c)}else b(a,a.eve)}(window||this,function(a,b){var c=function(b){var c={},d=a.requestAnimationFrame||a.webkitRequestAnimationFrame||a.mozRequestAnimationFrame||a.oRequestAnimationFrame||a.msRequestAnimationFrame||function(a){setTimeout(a,16)},e=Array.isArray||function(a){return a instanceof Array||"[object Array]"==Object.prototype.toString.call(a)},f=0,g="M"+(+new Date).toString(36),h=function(){return g+(f++).toString(36)},i=Date.now||function(){return+new Date},j=function(a){var b=this;if(null==a)return b.s;var c=b.s-a;b.b+=b.dur*c,b.B+=b.dur*c,b.s=a},k=function(a){var b=this;return null==a?b.spd:void(b.spd=a)},l=function(a){var b=this;return null==a?b.dur:(b.s=b.s*a/b.dur,void(b.dur=a))},m=function(){var a=this;delete c[a.id],a.update(),b("mina.stop."+a.id,a)},n=function(){var a=this;a.pdif||(delete c[a.id],a.update(),a.pdif=a.get()-a.b)},o=function(){var a=this;a.pdif&&(a.b=a.get()-a.pdif,delete a.pdif,c[a.id]=a)},p=function(){var a,b=this;if(e(b.start)){a=[];for(var c=0,d=b.start.length;d>c;c++)a[c]=+b.start[c]+(b.end[c]-b.start[c])*b.easing(b.s)}else a=+b.start+(b.end-b.start)*b.easing(b.s);b.set(a)},q=function(){var a=0;for(var e in c)if(c.hasOwnProperty(e)){var f=c[e],g=f.get();a++,f.s=(g-f.b)/(f.dur/f.spd),f.s>=1&&(delete c[e],f.s=1,a--,function(a){setTimeout(function(){b("mina.finish."+a.id,a)})}(f)),f.update()}a&&d(q)},r=function(a,b,e,f,g,i,s){var t={id:h(),start:a,end:b,b:e,s:0,dur:f-e,spd:1,get:g,set:i,easing:s||r.linear,status:j,speed:k,duration:l,stop:m,pause:n,resume:o,update:p};c[t.id]=t;var u,v=0;for(u in c)if(c.hasOwnProperty(u)&&(v++,2==v))break;return 1==v&&d(q),t};return r.time=i,r.getById=function(a){return c[a]||null},r.linear=function(a){return a},r.easeout=function(a){return Math.pow(a,1.7)},r.easein=function(a){return Math.pow(a,.48)},r.easeinout=function(a){if(1==a)return 1;if(0==a)return 0;var b=.48-a/1.04,c=Math.sqrt(.1734+b*b),d=c-b,e=Math.pow(Math.abs(d),1/3)*(0>d?-1:1),f=-c-b,g=Math.pow(Math.abs(f),1/3)*(0>f?-1:1),h=e+g+.5;return 3*(1-h)*h*h+h*h*h},r.backin=function(a){if(1==a)return 1;var b=1.70158;return a*a*((b+1)*a-b)},r.backout=function(a){if(0==a)return 0;a-=1;var b=1.70158;return a*a*((b+1)*a+b)+1},r.elastic=function(a){return a==!!a?a:Math.pow(2,-10*a)*Math.sin(2*(a-.075)*Math.PI/.3)+1},r.bounce=function(a){var b,c=7.5625,d=2.75;return 1/d>a?b=c*a*a:2/d>a?(a-=1.5/d,b=c*a*a+.75):2.5/d>a?(a-=2.25/d,b=c*a*a+.9375):(a-=2.625/d,b=c*a*a+.984375),b},a.mina=r,r}("undefined"==typeof b?function(){}:b),d=function(a){function c(a,b){if(a){if(a.nodeType)return w(a);if(e(a,"array")&&c.set)return c.set.apply(c,a);if(a instanceof s)return a;if(null==b)return a=y.doc.querySelector(String(a)),w(a)}return a=null==a?"100%":a,b=null==b?"100%":b,new v(a,b)}function d(a,b){if(b){if("#text"==a&&(a=y.doc.createTextNode(b.text||b["#text"]||"")),"#comment"==a&&(a=y.doc.createComment(b.text||b["#text"]||"")),"string"==typeof a&&(a=d(a)),"string"==typeof b)return 1==a.nodeType?"xlink:"==b.substring(0,6)?a.getAttributeNS(T,b.substring(6)):"xml:"==b.substring(0,4)?a.getAttributeNS(U,b.substring(4)):a.getAttribute(b):"text"==b?a.nodeValue:null;if(1==a.nodeType){for(var c in b)if(b[z](c)){var e=A(b[c]);e?"xlink:"==c.substring(0,6)?a.setAttributeNS(T,c.substring(6),e):"xml:"==c.substring(0,4)?a.setAttributeNS(U,c.substring(4),e):a.setAttribute(c,e):a.removeAttribute(c)}}else"text"in b&&(a.nodeValue=b.text)}else a=y.doc.createElementNS(U,a);return a}function e(a,b){return b=A.prototype.toLowerCase.call(b),"finite"==b?isFinite(a):"array"==b&&(a instanceof Array||Array.isArray&&Array.isArray(a))?!0:"null"==b&&null===a||b==typeof a&&null!==a||"object"==b&&a===Object(a)||J.call(a).slice(8,-1).toLowerCase()==b}function f(a){if("function"==typeof a||Object(a)!==a)return a;var b=new a.constructor;for(var c in a)a[z](c)&&(b[c]=f(a[c]));return b}function h(a,b){for(var c=0,d=a.length;d>c;c++)if(a[c]===b)return a.push(a.splice(c,1)[0])}function i(a,b,c){function d(){var e=Array.prototype.slice.call(arguments,0),f=e.join("␀"),g=d.cache=d.cache||{},i=d.count=d.count||[];return g[z](f)?(h(i,f),c?c(g[f]):g[f]):(i.length>=1e3&&delete g[i.shift()],i.push(f),g[f]=a.apply(b,e),c?c(g[f]):g[f])}return d}function j(a,b,c,d,e,f){if(null==e){var g=a-c,h=b-d;return g||h?(180+180*D.atan2(-h,-g)/H+360)%360:0}return j(a,b,e,f)-j(c,d,e,f)}function k(a){return a%360*H/180}function l(a){return 180*a/H%360}function m(a){var b=[];return a=a.replace(/(?:^|\s)(\w+)\(([^)]+)\)/g,function(a,c,d){return d=d.split(/\s*,\s*|\s+/),"rotate"==c&&1==d.length&&d.push(0,0),"scale"==c&&(d.length>2?d=d.slice(0,2):2==d.length&&d.push(0,0),1==d.length&&d.push(d[0],0,0)),b.push("skewX"==c?["m",1,0,D.tan(k(d[0])),1,0,0]:"skewY"==c?["m",1,D.tan(k(d[0])),0,1,0,0]:[c.charAt(0)].concat(d)),a}),b}function n(a,b){var d=ab(a),e=new c.Matrix;if(d)for(var f=0,g=d.length;g>f;f++){var h,i,j,k,l,m=d[f],n=m.length,o=A(m[0]).toLowerCase(),p=m[0]!=o,q=p?e.invert():0;"t"==o&&2==n?e.translate(m[1],0):"t"==o&&3==n?p?(h=q.x(0,0),i=q.y(0,0),j=q.x(m[1],m[2]),k=q.y(m[1],m[2]),e.translate(j-h,k-i)):e.translate(m[1],m[2]):"r"==o?2==n?(l=l||b,e.rotate(m[1],l.x+l.width/2,l.y+l.height/2)):4==n&&(p?(j=q.x(m[2],m[3]),k=q.y(m[2],m[3]),e.rotate(m[1],j,k)):e.rotate(m[1],m[2],m[3])):"s"==o?2==n||3==n?(l=l||b,e.scale(m[1],m[n-1],l.x+l.width/2,l.y+l.height/2)):4==n?p?(j=q.x(m[2],m[3]),k=q.y(m[2],m[3]),e.scale(m[1],m[1],j,k)):e.scale(m[1],m[1],m[2],m[3]):5==n&&(p?(j=q.x(m[3],m[4]),k=q.y(m[3],m[4]),e.scale(m[1],m[2],j,k)):e.scale(m[1],m[2],m[3],m[4])):"m"==o&&7==n&&e.add(m[1],m[2],m[3],m[4],m[5],m[6])}return e}function o(a){var b=a.node.ownerSVGElement&&w(a.node.ownerSVGElement)||a.node.parentNode&&w(a.node.parentNode)||c.select("svg")||c(0,0),d=b.select("defs"),e=null==d?!1:d.node;return e||(e=u("defs",b.node).node),e}function p(a){return a.node.ownerSVGElement&&w(a.node.ownerSVGElement)||c.select("svg")}function q(a,b,c){function e(a){if(null==a)return I;if(a==+a)return a;d(j,{width:a});try{return j.getBBox().width}catch(b){return 0}}function f(a){if(null==a)return I;if(a==+a)return a;d(j,{height:a});try{return j.getBBox().height}catch(b){return 0}}function g(d,e){null==b?i[d]=e(a.attr(d)||0):d==b&&(i=e(null==c?a.attr(d)||0:c))}var h=p(a).node,i={},j=h.querySelector(".svg---mgr");switch(j||(j=d("rect"),d(j,{x:-9e9,y:-9e9,width:10,height:10,"class":"svg---mgr",fill:"none"}),h.appendChild(j)),a.type){case"rect":g("rx",e),g("ry",f);case"image":g("width",e),g("height",f);case"text":g("x",e),g("y",f);break;case"circle":g("cx",e),g("cy",f),g("r",e);break;case"ellipse":g("cx",e),g("cy",f),g("rx",e),g("ry",f);break;case"line":g("x1",e),g("x2",e),g("y1",f),g("y2",f);break;case"marker":g("refX",e),g("markerWidth",e),g("refY",f),g("markerHeight",f);break;case"radialGradient":g("fx",e),g("fy",f);break;case"tspan":g("dx",e),g("dy",f);break;default:g(b,e)}return h.removeChild(j),i}function r(a){e(a,"array")||(a=Array.prototype.slice.call(arguments,0));for(var b=0,c=0,d=this.node;this[b];)delete this[b++];for(b=0;b<a.length;b++)"set"==a[b].type?a[b].forEach(function(a){d.appendChild(a.node)}):d.appendChild(a[b].node);var f=d.childNodes;for(b=0;b<f.length;b++)this[c++]=w(f[b]);return this}function s(a){if(a.snap in V)return V[a.snap];var b;try{b=a.ownerSVGElement}catch(c){}this.node=a,b&&(this.paper=new v(b)),this.type=a.tagName||a.nodeName;var d=this.id=S(this);if(this.anims={},this._={transform:[]},a.snap=d,V[d]=this,"g"==this.type&&(this.add=r),this.type in{g:1,mask:1,pattern:1,symbol:1})for(var e in v.prototype)v.prototype[z](e)&&(this[e]=v.prototype[e])}function t(a){this.node=a}function u(a,b){var c=d(a);b.appendChild(c);var e=w(c);return e}function v(a,b){var c,e,f,g=v.prototype;if(a&&"svg"==a.tagName){if(a.snap in V)return V[a.snap];var h=a.ownerDocument;c=new s(a),e=a.getElementsByTagName("desc")[0],f=a.getElementsByTagName("defs")[0],e||(e=d("desc"),e.appendChild(h.createTextNode("Created with Snap")),c.node.appendChild(e)),f||(f=d("defs"),c.node.appendChild(f)),c.defs=f;for(var i in g)g[z](i)&&(c[i]=g[i]);c.paper=c.root=c}else c=u("svg",y.doc.body),d(c.node,{height:b,version:1.1,width:a,xmlns:U});return c}function w(a){return a?a instanceof s||a instanceof t?a:a.tagName&&"svg"==a.tagName.toLowerCase()?new v(a):a.tagName&&"object"==a.tagName.toLowerCase()&&"image/svg+xml"==a.type?new v(a.contentDocument.getElementsByTagName("svg")[0]):new s(a):a}function x(a,b){for(var c=0,d=a.length;d>c;c++){var e={type:a[c].type,attr:a[c].attr()},f=a[c].children();b.push(e),f.length&&x(f,e.childNodes=[])}}c.version="0.4.0",c.toString=function(){return"Snap v"+this.version},c._={};var y={win:a.window,doc:a.window.document};c._.glob=y;{var z="hasOwnProperty",A=String,B=parseFloat,C=parseInt,D=Math,E=D.max,F=D.min,G=D.abs,H=(D.pow,D.PI),I=(D.round,""),J=Object.prototype.toString,K=/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?%?)\s*\))\s*$/i,L=(c._.separator=/[,\s]+/,/[\s]*,[\s]*/),M={hs:1,rg:1},N=/([a-z])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/gi,O=/([rstm])[\s,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\s]*,?[\s]*)+)/gi,P=/(-?\d*\.?\d*(?:e[\-+]?\\d+)?)[\s]*,?[\s]*/gi,Q=0,R="S"+(+new Date).toString(36),S=function(a){return(a&&a.type?a.type:I)+R+(Q++).toString(36)},T="http://www.w3.org/1999/xlink",U="http://www.w3.org/2000/svg",V={};c.url=function(a){return"url('#"+a+"')"}}c._.$=d,c._.id=S,c.format=function(){var a=/\{([^\}]+)\}/g,b=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,c=function(a,c,d){var e=d;return c.replace(b,function(a,b,c,d,f){b=b||d,e&&(b in e&&(e=e[b]),"function"==typeof e&&f&&(e=e()))}),e=(null==e||e==d?a:e)+""};return function(b,d){return A(b).replace(a,function(a,b){return c(a,b,d)})}}(),c._.clone=f,c._.cacher=i,c.rad=k,c.deg=l,c.sin=function(a){return D.sin(c.rad(a))},c.tan=function(a){return D.tan(c.rad(a))},c.cos=function(a){return D.cos(c.rad(a))},c.asin=function(a){return c.deg(D.asin(a))},c.acos=function(a){return c.deg(D.acos(a))},c.atan=function(a){return c.deg(D.atan(a))},c.atan2=function(a){return c.deg(D.atan2(a))},c.angle=j,c.len=function(a,b,d,e){return Math.sqrt(c.len2(a,b,d,e))},c.len2=function(a,b,c,d){return(a-c)*(a-c)+(b-d)*(b-d)},c.closestPoint=function(a,b,c){function d(a){var d=a.x-b,e=a.y-c;return d*d+e*e}for(var e,f,g,h,i=a.node,j=i.getTotalLength(),k=j/i.pathSegList.numberOfItems*.125,l=1/0,m=0;j>=m;m+=k)(h=d(g=i.getPointAtLength(m)))<l&&(e=g,f=m,l=h);for(k*=.5;k>.5;){var n,o,p,q,r,s;(p=f-k)>=0&&(r=d(n=i.getPointAtLength(p)))<l?(e=n,f=p,l=r):(q=f+k)<=j&&(s=d(o=i.getPointAtLength(q)))<l?(e=o,f=q,l=s):k*=.5}return e={x:e.x,y:e.y,length:f,distance:Math.sqrt(l)}},c.is=e,c.snapTo=function(a,b,c){if(c=e(c,"finite")?c:10,e(a,"array")){for(var d=a.length;d--;)if(G(a[d]-b)<=c)return a[d]}else{a=+a;var f=b%a;if(c>f)return b-f;if(f>a-c)return b-f+a}return b},c.getRGB=i(function(a){if(!a||(a=A(a)).indexOf("-")+1)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:Z};if("none"==a)return{r:-1,g:-1,b:-1,hex:"none",toString:Z};if(!(M[z](a.toLowerCase().substring(0,2))||"#"==a.charAt())&&(a=W(a)),!a)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:Z};var b,d,f,g,h,i,j=a.match(K);return j?(j[2]&&(f=C(j[2].substring(5),16),d=C(j[2].substring(3,5),16),b=C(j[2].substring(1,3),16)),j[3]&&(f=C((h=j[3].charAt(3))+h,16),d=C((h=j[3].charAt(2))+h,16),b=C((h=j[3].charAt(1))+h,16)),j[4]&&(i=j[4].split(L),b=B(i[0]),"%"==i[0].slice(-1)&&(b*=2.55),d=B(i[1]),"%"==i[1].slice(-1)&&(d*=2.55),f=B(i[2]),"%"==i[2].slice(-1)&&(f*=2.55),"rgba"==j[1].toLowerCase().slice(0,4)&&(g=B(i[3])),i[3]&&"%"==i[3].slice(-1)&&(g/=100)),j[5]?(i=j[5].split(L),b=B(i[0]),"%"==i[0].slice(-1)&&(b/=100),d=B(i[1]),"%"==i[1].slice(-1)&&(d/=100),f=B(i[2]),"%"==i[2].slice(-1)&&(f/=100),("deg"==i[0].slice(-3)||"°"==i[0].slice(-1))&&(b/=360),"hsba"==j[1].toLowerCase().slice(0,4)&&(g=B(i[3])),i[3]&&"%"==i[3].slice(-1)&&(g/=100),c.hsb2rgb(b,d,f,g)):j[6]?(i=j[6].split(L),b=B(i[0]),"%"==i[0].slice(-1)&&(b/=100),d=B(i[1]),"%"==i[1].slice(-1)&&(d/=100),f=B(i[2]),"%"==i[2].slice(-1)&&(f/=100),("deg"==i[0].slice(-3)||"°"==i[0].slice(-1))&&(b/=360),"hsla"==j[1].toLowerCase().slice(0,4)&&(g=B(i[3])),i[3]&&"%"==i[3].slice(-1)&&(g/=100),c.hsl2rgb(b,d,f,g)):(b=F(D.round(b),255),d=F(D.round(d),255),f=F(D.round(f),255),g=F(E(g,0),1),j={r:b,g:d,b:f,toString:Z},j.hex="#"+(16777216|f|d<<8|b<<16).toString(16).slice(1),j.opacity=e(g,"finite")?g:1,j)):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:Z}},c),c.hsb=i(function(a,b,d){return c.hsb2rgb(a,b,d).hex}),c.hsl=i(function(a,b,d){return c.hsl2rgb(a,b,d).hex}),c.rgb=i(function(a,b,c,d){if(e(d,"finite")){var f=D.round;return"rgba("+[f(a),f(b),f(c),+d.toFixed(2)]+")"}return"#"+(16777216|c|b<<8|a<<16).toString(16).slice(1)});var W=function(a){var b=y.doc.getElementsByTagName("head")[0]||y.doc.getElementsByTagName("svg")[0],c="rgb(255, 0, 0)";return(W=i(function(a){if("red"==a.toLowerCase())return c;b.style.color=c,b.style.color=a;var d=y.doc.defaultView.getComputedStyle(b,I).getPropertyValue("color");return d==c?null:d}))(a)},X=function(){return"hsb("+[this.h,this.s,this.b]+")"},Y=function(){return"hsl("+[this.h,this.s,this.l]+")"},Z=function(){return 1==this.opacity||null==this.opacity?this.hex:"rgba("+[this.r,this.g,this.b,this.opacity]+")"},$=function(a,b,d){if(null==b&&e(a,"object")&&"r"in a&&"g"in a&&"b"in a&&(d=a.b,b=a.g,a=a.r),null==b&&e(a,string)){var f=c.getRGB(a);a=f.r,b=f.g,d=f.b}return(a>1||b>1||d>1)&&(a/=255,b/=255,d/=255),[a,b,d]},_=function(a,b,d,f){a=D.round(255*a),b=D.round(255*b),d=D.round(255*d);var g={r:a,g:b,b:d,opacity:e(f,"finite")?f:1,hex:c.rgb(a,b,d),toString:Z};return e(f,"finite")&&(g.opacity=f),g};c.color=function(a){var b;return e(a,"object")&&"h"in a&&"s"in a&&"b"in a?(b=c.hsb2rgb(a),a.r=b.r,a.g=b.g,a.b=b.b,a.opacity=1,a.hex=b.hex):e(a,"object")&&"h"in a&&"s"in a&&"l"in a?(b=c.hsl2rgb(a),a.r=b.r,a.g=b.g,a.b=b.b,a.opacity=1,a.hex=b.hex):(e(a,"string")&&(a=c.getRGB(a)),e(a,"object")&&"r"in a&&"g"in a&&"b"in a&&!("error"in a)?(b=c.rgb2hsl(a),a.h=b.h,a.s=b.s,a.l=b.l,b=c.rgb2hsb(a),a.v=b.b):(a={hex:"none"},a.r=a.g=a.b=a.h=a.s=a.v=a.l=-1,a.error=1)),a.toString=Z,a},c.hsb2rgb=function(a,b,c,d){e(a,"object")&&"h"in a&&"s"in a&&"b"in a&&(c=a.b,b=a.s,d=a.o,a=a.h),a*=360;var f,g,h,i,j;return a=a%360/60,j=c*b,i=j*(1-G(a%2-1)),f=g=h=c-j,a=~~a,f+=[j,i,0,0,i,j][a],g+=[i,j,j,i,0,0][a],h+=[0,0,i,j,j,i][a],_(f,g,h,d)},c.hsl2rgb=function(a,b,c,d){e(a,"object")&&"h"in a&&"s"in a&&"l"in a&&(c=a.l,b=a.s,a=a.h),(a>1||b>1||c>1)&&(a/=360,b/=100,c/=100),a*=360;var f,g,h,i,j;return a=a%360/60,j=2*b*(.5>c?c:1-c),i=j*(1-G(a%2-1)),f=g=h=c-j/2,a=~~a,f+=[j,i,0,0,i,j][a],g+=[i,j,j,i,0,0][a],h+=[0,0,i,j,j,i][a],_(f,g,h,d)},c.rgb2hsb=function(a,b,c){c=$(a,b,c),a=c[0],b=c[1],c=c[2];var d,e,f,g;return f=E(a,b,c),g=f-F(a,b,c),d=0==g?null:f==a?(b-c)/g:f==b?(c-a)/g+2:(a-b)/g+4,d=(d+360)%6*60/360,e=0==g?0:g/f,{h:d,s:e,b:f,toString:X}},c.rgb2hsl=function(a,b,c){c=$(a,b,c),a=c[0],b=c[1],c=c[2];var d,e,f,g,h,i;return g=E(a,b,c),h=F(a,b,c),i=g-h,d=0==i?null:g==a?(b-c)/i:g==b?(c-a)/i+2:(a-b)/i+4,d=(d+360)%6*60/360,f=(g+h)/2,e=0==i?0:.5>f?i/(2*f):i/(2-2*f),{h:d,s:e,l:f,toString:Y}},c.parsePathString=function(a){if(!a)return null;var b=c.path(a);if(b.arr)return c.path.clone(b.arr);var d={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},f=[];return e(a,"array")&&e(a[0],"array")&&(f=c.path.clone(a)),f.length||A(a).replace(N,function(a,b,c){var e=[],g=b.toLowerCase();if(c.replace(P,function(a,b){b&&e.push(+b)}),"m"==g&&e.length>2&&(f.push([b].concat(e.splice(0,2))),g="l",b="m"==b?"l":"L"),"o"==g&&1==e.length&&f.push([b,e[0]]),"r"==g)f.push([b].concat(e));else for(;e.length>=d[g]&&(f.push([b].concat(e.splice(0,d[g]))),d[g]););}),f.toString=c.path.toString,b.arr=c.path.clone(f),f};var ab=c.parseTransformString=function(a){if(!a)return null;var b=[];return e(a,"array")&&e(a[0],"array")&&(b=c.path.clone(a)),b.length||A(a).replace(O,function(a,c,d){{var e=[];c.toLowerCase()}d.replace(P,function(a,b){b&&e.push(+b)}),b.push([c].concat(e))}),b.toString=c.path.toString,b};c._.svgTransform2string=m,c._.rgTransform=/^[a-z][\s]*-?\.?\d/i,c._.transform2matrix=n,c._unit2px=q;y.doc.contains||y.doc.compareDocumentPosition?function(a,b){var c=9==a.nodeType?a.documentElement:a,d=b&&b.parentNode;return a==d||!(!d||1!=d.nodeType||!(c.contains?c.contains(d):a.compareDocumentPosition&&16&a.compareDocumentPosition(d)))}:function(a,b){if(b)for(;b;)if(b=b.parentNode,b==a)return!0;return!1};c._.getSomeDefs=o,c._.getSomeSVG=p,c.select=function(a){return a=A(a).replace(/([^\\]):/g,"$1\\:"),w(y.doc.querySelector(a))},c.selectAll=function(a){for(var b=y.doc.querySelectorAll(a),d=(c.set||Array)(),e=0;e<b.length;e++)d.push(w(b[e]));return d},setInterval(function(){for(var a in V)if(V[z](a)){var b=V[a],c=b.node;("svg"!=b.type&&!c.ownerSVGElement||"svg"==b.type&&(!c.parentNode||"ownerSVGElement"in c.parentNode&&!c.ownerSVGElement))&&delete V[a]}},1e4),s.prototype.attr=function(a,c){var d=this,f=d.node;if(!a){if(1!=f.nodeType)return{text:f.nodeValue};for(var g=f.attributes,h={},i=0,j=g.length;j>i;i++)h[g[i].nodeName]=g[i].nodeValue;return h}if(e(a,"string")){if(!(arguments.length>1))return b("snap.util.getattr."+a,d).firstDefined();var k={};k[a]=c,a=k}for(var l in a)a[z](l)&&b("snap.util.attr."+l,d,a[l]);return d},c.parse=function(a){var b=y.doc.createDocumentFragment(),c=!0,d=y.doc.createElement("div");if(a=A(a),a.match(/^\s*<\s*svg(?:\s|>)/)||(a="<svg>"+a+"</svg>",c=!1),d.innerHTML=a,a=d.getElementsByTagName("svg")[0])if(c)b=a;else for(;a.firstChild;)b.appendChild(a.firstChild);return new t(b)},c.fragment=function(){for(var a=Array.prototype.slice.call(arguments,0),b=y.doc.createDocumentFragment(),d=0,e=a.length;e>d;d++){var f=a[d];f.node&&f.node.nodeType&&b.appendChild(f.node),f.nodeType&&b.appendChild(f),"string"==typeof f&&b.appendChild(c.parse(f).node)}return new t(b)},c._.make=u,c._.wrap=w,v.prototype.el=function(a,b){var c=u(a,this.node);return b&&c.attr(b),c},s.prototype.children=function(){for(var a=[],b=this.node.childNodes,d=0,e=b.length;e>d;d++)a[d]=c(b[d]);return a},s.prototype.toJSON=function(){var a=[];return x([this],a),a[0]},b.on("snap.util.getattr",function(){var a=b.nt();a=a.substring(a.lastIndexOf(".")+1);var c=a.replace(/[A-Z]/g,function(a){return"-"+a.toLowerCase()});return bb[z](c)?this.node.ownerDocument.defaultView.getComputedStyle(this.node,null).getPropertyValue(c):d(this.node,a)});var bb={"alignment-baseline":0,"baseline-shift":0,clip:0,"clip-path":0,"clip-rule":0,color:0,"color-interpolation":0,"color-interpolation-filters":0,"color-profile":0,"color-rendering":0,cursor:0,direction:0,display:0,"dominant-baseline":0,"enable-background":0,fill:0,"fill-opacity":0,"fill-rule":0,filter:0,"flood-color":0,"flood-opacity":0,font:0,"font-family":0,"font-size":0,"font-size-adjust":0,"font-stretch":0,"font-style":0,"font-variant":0,"font-weight":0,"glyph-orientation-horizontal":0,"glyph-orientation-vertical":0,"image-rendering":0,kerning:0,"letter-spacing":0,"lighting-color":0,marker:0,"marker-end":0,"marker-mid":0,"marker-start":0,mask:0,opacity:0,overflow:0,"pointer-events":0,"shape-rendering":0,"stop-color":0,"stop-opacity":0,stroke:0,"stroke-dasharray":0,"stroke-dashoffset":0,"stroke-linecap":0,"stroke-linejoin":0,"stroke-miterlimit":0,"stroke-opacity":0,"stroke-width":0,"text-anchor":0,"text-decoration":0,"text-rendering":0,"unicode-bidi":0,visibility:0,"word-spacing":0,"writing-mode":0};b.on("snap.util.attr",function(a){var c=b.nt(),e={};c=c.substring(c.lastIndexOf(".")+1),e[c]=a;var f=c.replace(/-(\w)/gi,function(a,b){return b.toUpperCase()}),g=c.replace(/[A-Z]/g,function(a){return"-"+a.toLowerCase()});bb[z](g)?this.node.style[f]=null==a?I:a:d(this.node,e)}),function(){}(v.prototype),c.ajax=function(a,c,d,f){var g=new XMLHttpRequest,h=S();if(g){if(e(c,"function"))f=d,d=c,c=null;else if(e(c,"object")){var i=[];for(var j in c)c.hasOwnProperty(j)&&i.push(encodeURIComponent(j)+"="+encodeURIComponent(c[j]));c=i.join("&")}return g.open(c?"POST":"GET",a,!0),c&&(g.setRequestHeader("X-Requested-With","XMLHttpRequest"),g.setRequestHeader("Content-type","application/x-www-form-urlencoded")),d&&(b.once("snap.ajax."+h+".0",d),b.once("snap.ajax."+h+".200",d),b.once("snap.ajax."+h+".304",d)),g.onreadystatechange=function(){4==g.readyState&&b("snap.ajax."+h+"."+g.status,f,g)},4==g.readyState?g:(g.send(c),g)}},c.load=function(a,b,d){c.ajax(a,function(a){var e=c.parse(a.responseText);d?b.call(d,e):b(e)})};var cb=function(a){var b=a.getBoundingClientRect(),c=a.ownerDocument,d=c.body,e=c.documentElement,f=e.clientTop||d.clientTop||0,h=e.clientLeft||d.clientLeft||0,i=b.top+(g.win.pageYOffset||e.scrollTop||d.scrollTop)-f,j=b.left+(g.win.pageXOffset||e.scrollLeft||d.scrollLeft)-h;return{y:i,x:j}};return c.getElementByPoint=function(a,b){var c=this,d=(c.canvas,y.doc.elementFromPoint(a,b));if(y.win.opera&&"svg"==d.tagName){var e=cb(d),f=d.createSVGRect();f.x=a-e.x,f.y=b-e.y,f.width=f.height=1;var g=d.getIntersectionList(f,null);g.length&&(d=g[g.length-1])}return d?w(d):null},c.plugin=function(a){a(c,s,v,y,t)},y.win.Snap=c,c}(a||this);return d.plugin(function(d,e,f,g,h){function i(a,b){if(null==b){var c=!0;if(b=a.node.getAttribute("linearGradient"==a.type||"radialGradient"==a.type?"gradientTransform":"pattern"==a.type?"patternTransform":"transform"),!b)return new d.Matrix;b=d._.svgTransform2string(b)}else b=d._.rgTransform.test(b)?o(b).replace(/\.{3}|\u2026/g,a._.transform||""):d._.svgTransform2string(b),n(b,"array")&&(b=d.path?d.path.toString.call(b):o(b)),a._.transform=b;var e=d._.transform2matrix(b,a.getBBox(1));return c?e:void(a.matrix=e)}function j(a){function b(a,b){var c=q(a.node,b);c=c&&c.match(f),c=c&&c[2],c&&"#"==c.charAt()&&(c=c.substring(1),c&&(h[c]=(h[c]||[]).concat(function(c){var d={};d[b]=URL(c),q(a.node,d)})))}function c(a){var b=q(a.node,"xlink:href");b&&"#"==b.charAt()&&(b=b.substring(1),b&&(h[b]=(h[b]||[]).concat(function(b){a.attr("xlink:href","#"+b)})))}for(var d,e=a.selectAll("*"),f=/^\s*url\(("|'|)(.*)\1\)\s*$/,g=[],h={},i=0,j=e.length;j>i;i++){d=e[i],b(d,"fill"),b(d,"stroke"),b(d,"filter"),b(d,"mask"),b(d,"clip-path"),c(d);var k=q(d.node,"id");k&&(q(d.node,{id:d.id}),g.push({old:k,id:d.id}))}for(i=0,j=g.length;j>i;i++){var l=h[g[i].old];if(l)for(var m=0,n=l.length;n>m;m++)l[m](g[i].id)}}function k(a,b,c){return function(d){var e=d.slice(a,b);return 1==e.length&&(e=e[0]),c?c(e):e}}function l(a){return function(){var b=a?"<"+this.type:"",c=this.node.attributes,d=this.node.childNodes;if(a)for(var e=0,f=c.length;f>e;e++)b+=" "+c[e].name+'="'+c[e].value.replace(/"/g,'\\"')+'"';if(d.length){for(a&&(b+=">"),e=0,f=d.length;f>e;e++)3==d[e].nodeType?b+=d[e].nodeValue:1==d[e].nodeType&&(b+=u(d[e]).toString());a&&(b+="</"+this.type+">")}else a&&(b+="/>");return b}}var m=e.prototype,n=d.is,o=String,p=d._unit2px,q=d._.$,r=d._.make,s=d._.getSomeDefs,t="hasOwnProperty",u=d._.wrap;m.getBBox=function(a){if(!d.Matrix||!d.path)return this.node.getBBox();var b=this,c=new d.Matrix;if(b.removed)return d._.box();for(;"use"==b.type;)if(a||(c=c.add(b.transform().localMatrix.translate(b.attr("x")||0,b.attr("y")||0))),b.original)b=b.original;else{var e=b.attr("xlink:href");b=b.original=b.node.ownerDocument.getElementById(e.substring(e.indexOf("#")+1))}var f=b._,g=d.path.get[b.type]||d.path.get.deflt;try{return a?(f.bboxwt=g?d.path.getBBox(b.realPath=g(b)):d._.box(b.node.getBBox()),d._.box(f.bboxwt)):(b.realPath=g(b),b.matrix=b.transform().localMatrix,f.bbox=d.path.getBBox(d.path.map(b.realPath,c.add(b.matrix))),d._.box(f.bbox))}catch(h){return d._.box()}};var v=function(){return this.string};m.transform=function(a){var b=this._;if(null==a){for(var c,e=this,f=new d.Matrix(this.node.getCTM()),g=i(this),h=[g],j=new d.Matrix,k=g.toTransformString(),l=o(g)==o(this.matrix)?o(b.transform):k;"svg"!=e.type&&(e=e.parent());)h.push(i(e));for(c=h.length;c--;)j.add(h[c]);return{string:l,globalMatrix:f,totalMatrix:j,localMatrix:g,diffMatrix:f.clone().add(g.invert()),global:f.toTransformString(),total:j.toTransformString(),local:k,toString:v}}return a instanceof d.Matrix?(this.matrix=a,this._.transform=a.toTransformString()):i(this,a),this.node&&("linearGradient"==this.type||"radialGradient"==this.type?q(this.node,{gradientTransform:this.matrix}):"pattern"==this.type?q(this.node,{patternTransform:this.matrix}):q(this.node,{transform:this.matrix})),this},m.parent=function(){return u(this.node.parentNode)},m.append=m.add=function(a){if(a){if("set"==a.type){var b=this;return a.forEach(function(a){b.add(a)}),this}a=u(a),this.node.appendChild(a.node),a.paper=this.paper}return this},m.appendTo=function(a){return a&&(a=u(a),a.append(this)),this},m.prepend=function(a){if(a){if("set"==a.type){var b,c=this;return a.forEach(function(a){b?b.after(a):c.prepend(a),b=a}),this}a=u(a);var d=a.parent();this.node.insertBefore(a.node,this.node.firstChild),this.add&&this.add(),a.paper=this.paper,this.parent()&&this.parent().add(),d&&d.add()}return this},m.prependTo=function(a){return a=u(a),a.prepend(this),this},m.before=function(a){if("set"==a.type){var b=this;return a.forEach(function(a){var c=a.parent();b.node.parentNode.insertBefore(a.node,b.node),c&&c.add()}),this.parent().add(),this}a=u(a);var c=a.parent();return this.node.parentNode.insertBefore(a.node,this.node),this.parent()&&this.parent().add(),c&&c.add(),a.paper=this.paper,this},m.after=function(a){a=u(a);var b=a.parent();return this.node.nextSibling?this.node.parentNode.insertBefore(a.node,this.node.nextSibling):this.node.parentNode.appendChild(a.node),this.parent()&&this.parent().add(),b&&b.add(),a.paper=this.paper,this},m.insertBefore=function(a){a=u(a);var b=this.parent();return a.node.parentNode.insertBefore(this.node,a.node),this.paper=a.paper,b&&b.add(),a.parent()&&a.parent().add(),this},m.insertAfter=function(a){a=u(a);var b=this.parent();return a.node.parentNode.insertBefore(this.node,a.node.nextSibling),this.paper=a.paper,b&&b.add(),a.parent()&&a.parent().add(),this},m.remove=function(){var a=this.parent();return this.node.parentNode&&this.node.parentNode.removeChild(this.node),delete this.paper,this.removed=!0,a&&a.add(),this},m.select=function(a){return u(this.node.querySelector(a))},m.selectAll=function(a){for(var b=this.node.querySelectorAll(a),c=(d.set||Array)(),e=0;e<b.length;e++)c.push(u(b[e]));return c},m.asPX=function(a,b){return null==b&&(b=this.attr(a)),+p(this,a,b)},m.use=function(){var a,b=this.node.id;return b||(b=this.id,q(this.node,{id:b})),a="linearGradient"==this.type||"radialGradient"==this.type||"pattern"==this.type?r(this.type,this.node.parentNode):r("use",this.node.parentNode),q(a.node,{"xlink:href":"#"+b}),a.original=this,a},m.clone=function(){var a=u(this.node.cloneNode(!0));return q(a.node,"id")&&q(a.node,{id:a.id}),j(a),a.insertAfter(this),a},m.toDefs=function(){var a=s(this);return a.appendChild(this.node),this},m.pattern=m.toPattern=function(a,b,c,d){var e=r("pattern",s(this));return null==a&&(a=this.getBBox()),n(a,"object")&&"x"in a&&(b=a.y,c=a.width,d=a.height,a=a.x),q(e.node,{x:a,y:b,width:c,height:d,patternUnits:"userSpaceOnUse",id:e.id,viewBox:[a,b,c,d].join(" ")}),e.node.appendChild(this.node),e},m.marker=function(a,b,c,d,e,f){var g=r("marker",s(this));return null==a&&(a=this.getBBox()),n(a,"object")&&"x"in a&&(b=a.y,c=a.width,d=a.height,e=a.refX||a.cx,f=a.refY||a.cy,a=a.x),q(g.node,{viewBox:[a,b,c,d].join(" "),markerWidth:c,markerHeight:d,orient:"auto",refX:e||0,refY:f||0,id:g.id}),g.node.appendChild(this.node),g};var w=function(a,b,d,e){"function"!=typeof d||d.length||(e=d,d=c.linear),this.attr=a,this.dur=b,d&&(this.easing=d),e&&(this.callback=e)};d._.Animation=w,d.animation=function(a,b,c,d){return new w(a,b,c,d)},m.inAnim=function(){var a=this,b=[];for(var c in a.anims)a.anims[t](c)&&!function(a){b.push({anim:new w(a._attrs,a.dur,a.easing,a._callback),mina:a,curStatus:a.status(),status:function(b){return a.status(b)},stop:function(){a.stop()}})}(a.anims[c]);return b},d.animate=function(a,d,e,f,g,h){"function"!=typeof g||g.length||(h=g,g=c.linear);var i=c.time(),j=c(a,d,i,i+f,c.time,e,g);return h&&b.once("mina.finish."+j.id,h),j},m.stop=function(){for(var a=this.inAnim(),b=0,c=a.length;c>b;b++)a[b].stop();return this},m.animate=function(a,d,e,f){"function"!=typeof e||e.length||(f=e,e=c.linear),a instanceof w&&(f=a.callback,e=a.easing,d=a.dur,a=a.attr);var g,h,i,j,l=[],m=[],p={},q=this;for(var r in a)if(a[t](r)){q.equal?(j=q.equal(r,o(a[r])),g=j.from,h=j.to,i=j.f):(g=+q.attr(r),h=+a[r]);var s=n(g,"array")?g.length:1;p[r]=k(l.length,l.length+s,i),l=l.concat(g),m=m.concat(h)}var u=c.time(),v=c(l,m,u,u+d,c.time,function(a){var b={};for(var c in p)p[t](c)&&(b[c]=p[c](a));q.attr(b)},e);return q.anims[v.id]=v,v._attrs=a,v._callback=f,b("snap.animcreated."+q.id,v),b.once("mina.finish."+v.id,function(){delete q.anims[v.id],f&&f.call(q)}),b.once("mina.stop."+v.id,function(){delete q.anims[v.id]}),q};var x={};m.data=function(a,c){var e=x[this.id]=x[this.id]||{};if(0==arguments.length)return b("snap.data.get."+this.id,this,e,null),e;
    if(1==arguments.length){if(d.is(a,"object")){for(var f in a)a[t](f)&&this.data(f,a[f]);return this}return b("snap.data.get."+this.id,this,e[a],a),e[a]}return e[a]=c,b("snap.data.set."+this.id,this,c,a),this},m.removeData=function(a){return null==a?x[this.id]={}:x[this.id]&&delete x[this.id][a],this},m.outerSVG=m.toString=l(1),m.innerSVG=l(),m.toDataURL=function(){if(a&&a.btoa){var b=this.getBBox(),c=d.format('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="{width}" height="{height}" viewBox="{x} {y} {width} {height}">{contents}</svg>',{x:+b.x.toFixed(3),y:+b.y.toFixed(3),width:+b.width.toFixed(3),height:+b.height.toFixed(3),contents:this.outerSVG()});return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(c)))}},h.prototype.select=m.select,h.prototype.selectAll=m.selectAll}),d.plugin(function(a){function b(a,b,d,e,f,g){return null==b&&"[object SVGMatrix]"==c.call(a)?(this.a=a.a,this.b=a.b,this.c=a.c,this.d=a.d,this.e=a.e,void(this.f=a.f)):void(null!=a?(this.a=+a,this.b=+b,this.c=+d,this.d=+e,this.e=+f,this.f=+g):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0))}var c=Object.prototype.toString,d=String,e=Math,f="";!function(c){function g(a){return a[0]*a[0]+a[1]*a[1]}function h(a){var b=e.sqrt(g(a));a[0]&&(a[0]/=b),a[1]&&(a[1]/=b)}c.add=function(a,c,d,e,f,g){var h,i,j,k,l=[[],[],[]],m=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],n=[[a,d,f],[c,e,g],[0,0,1]];for(a&&a instanceof b&&(n=[[a.a,a.c,a.e],[a.b,a.d,a.f],[0,0,1]]),h=0;3>h;h++)for(i=0;3>i;i++){for(k=0,j=0;3>j;j++)k+=m[h][j]*n[j][i];l[h][i]=k}return this.a=l[0][0],this.b=l[1][0],this.c=l[0][1],this.d=l[1][1],this.e=l[0][2],this.f=l[1][2],this},c.invert=function(){var a=this,c=a.a*a.d-a.b*a.c;return new b(a.d/c,-a.b/c,-a.c/c,a.a/c,(a.c*a.f-a.d*a.e)/c,(a.b*a.e-a.a*a.f)/c)},c.clone=function(){return new b(this.a,this.b,this.c,this.d,this.e,this.f)},c.translate=function(a,b){return this.add(1,0,0,1,a,b)},c.scale=function(a,b,c,d){return null==b&&(b=a),(c||d)&&this.add(1,0,0,1,c,d),this.add(a,0,0,b,0,0),(c||d)&&this.add(1,0,0,1,-c,-d),this},c.rotate=function(b,c,d){b=a.rad(b),c=c||0,d=d||0;var f=+e.cos(b).toFixed(9),g=+e.sin(b).toFixed(9);return this.add(f,g,-g,f,c,d),this.add(1,0,0,1,-c,-d)},c.x=function(a,b){return a*this.a+b*this.c+this.e},c.y=function(a,b){return a*this.b+b*this.d+this.f},c.get=function(a){return+this[d.fromCharCode(97+a)].toFixed(4)},c.toString=function(){return"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")"},c.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},c.determinant=function(){return this.a*this.d-this.b*this.c},c.split=function(){var b={};b.dx=this.e,b.dy=this.f;var c=[[this.a,this.c],[this.b,this.d]];b.scalex=e.sqrt(g(c[0])),h(c[0]),b.shear=c[0][0]*c[1][0]+c[0][1]*c[1][1],c[1]=[c[1][0]-c[0][0]*b.shear,c[1][1]-c[0][1]*b.shear],b.scaley=e.sqrt(g(c[1])),h(c[1]),b.shear/=b.scaley,this.determinant()<0&&(b.scalex=-b.scalex);var d=-c[0][1],f=c[1][1];return 0>f?(b.rotate=a.deg(e.acos(f)),0>d&&(b.rotate=360-b.rotate)):b.rotate=a.deg(e.asin(d)),b.isSimple=!(+b.shear.toFixed(9)||b.scalex.toFixed(9)!=b.scaley.toFixed(9)&&b.rotate),b.isSuperSimple=!+b.shear.toFixed(9)&&b.scalex.toFixed(9)==b.scaley.toFixed(9)&&!b.rotate,b.noRotation=!+b.shear.toFixed(9)&&!b.rotate,b},c.toTransformString=function(a){var b=a||this.split();return+b.shear.toFixed(9)?"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]:(b.scalex=+b.scalex.toFixed(4),b.scaley=+b.scaley.toFixed(4),b.rotate=+b.rotate.toFixed(4),(b.dx||b.dy?"t"+[+b.dx.toFixed(4),+b.dy.toFixed(4)]:f)+(1!=b.scalex||1!=b.scaley?"s"+[b.scalex,b.scaley,0,0]:f)+(b.rotate?"r"+[+b.rotate.toFixed(4),0,0]:f))}}(b.prototype),a.Matrix=b,a.matrix=function(a,c,d,e,f,g){return new b(a,c,d,e,f,g)}}),d.plugin(function(a,c,d,e,f){function g(d){return function(e){if(b.stop(),e instanceof f&&1==e.node.childNodes.length&&("radialGradient"==e.node.firstChild.tagName||"linearGradient"==e.node.firstChild.tagName||"pattern"==e.node.firstChild.tagName)&&(e=e.node.firstChild,n(this).appendChild(e),e=l(e)),e instanceof c)if("radialGradient"==e.type||"linearGradient"==e.type||"pattern"==e.type){e.node.id||p(e.node,{id:e.id});var g=q(e.node.id)}else g=e.attr(d);else if(g=a.color(e),g.error){var h=a(n(this).ownerSVGElement).gradient(e);h?(h.node.id||p(h.node,{id:h.id}),g=q(h.node.id)):g=e}else g=r(g);var i={};i[d]=g,p(this.node,i),this.node.style[d]=t}}function h(a){b.stop(),a==+a&&(a+="px"),this.node.style.fontSize=a}function i(a){for(var b=[],c=a.childNodes,d=0,e=c.length;e>d;d++){var f=c[d];3==f.nodeType&&b.push(f.nodeValue),"tspan"==f.tagName&&b.push(1==f.childNodes.length&&3==f.firstChild.nodeType?f.firstChild.nodeValue:i(f))}return b}function j(){return b.stop(),this.node.style.fontSize}var k=a._.make,l=a._.wrap,m=a.is,n=a._.getSomeDefs,o=/^url\(#?([^)]+)\)$/,p=a._.$,q=a.url,r=String,s=a._.separator,t="";b.on("snap.util.attr.mask",function(a){if(a instanceof c||a instanceof f){if(b.stop(),a instanceof f&&1==a.node.childNodes.length&&(a=a.node.firstChild,n(this).appendChild(a),a=l(a)),"mask"==a.type)var d=a;else d=k("mask",n(this)),d.node.appendChild(a.node);!d.node.id&&p(d.node,{id:d.id}),p(this.node,{mask:q(d.id)})}}),function(a){b.on("snap.util.attr.clip",a),b.on("snap.util.attr.clip-path",a),b.on("snap.util.attr.clipPath",a)}(function(a){if(a instanceof c||a instanceof f){if(b.stop(),"clipPath"==a.type)var d=a;else d=k("clipPath",n(this)),d.node.appendChild(a.node),!d.node.id&&p(d.node,{id:d.id});p(this.node,{"clip-path":q(d.node.id||d.id)})}}),b.on("snap.util.attr.fill",g("fill")),b.on("snap.util.attr.stroke",g("stroke"));var u=/^([lr])(?:\(([^)]*)\))?(.*)$/i;b.on("snap.util.grad.parse",function(a){a=r(a);var b=a.match(u);if(!b)return null;var c=b[1],d=b[2],e=b[3];return d=d.split(/\s*,\s*/).map(function(a){return+a==a?+a:a}),1==d.length&&0==d[0]&&(d=[]),e=e.split("-"),e=e.map(function(a){a=a.split(":");var b={color:a[0]};return a[1]&&(b.offset=parseFloat(a[1])),b}),{type:c,params:d,stops:e}}),b.on("snap.util.attr.d",function(c){b.stop(),m(c,"array")&&m(c[0],"array")&&(c=a.path.toString.call(c)),c=r(c),c.match(/[ruo]/i)&&(c=a.path.toAbsolute(c)),p(this.node,{d:c})})(-1),b.on("snap.util.attr.#text",function(a){b.stop(),a=r(a);for(var c=e.doc.createTextNode(a);this.node.firstChild;)this.node.removeChild(this.node.firstChild);this.node.appendChild(c)})(-1),b.on("snap.util.attr.path",function(a){b.stop(),this.attr({d:a})})(-1),b.on("snap.util.attr.class",function(a){b.stop(),this.node.className.baseVal=a})(-1),b.on("snap.util.attr.viewBox",function(a){var c;c=m(a,"object")&&"x"in a?[a.x,a.y,a.width,a.height].join(" "):m(a,"array")?a.join(" "):a,p(this.node,{viewBox:c}),b.stop()})(-1),b.on("snap.util.attr.transform",function(a){this.transform(a),b.stop()})(-1),b.on("snap.util.attr.r",function(a){"rect"==this.type&&(b.stop(),p(this.node,{rx:a,ry:a}))})(-1),b.on("snap.util.attr.textpath",function(a){if(b.stop(),"text"==this.type){var d,e,f;if(!a&&this.textPath){for(e=this.textPath;e.node.firstChild;)this.node.appendChild(e.node.firstChild);return e.remove(),void delete this.textPath}if(m(a,"string")){var g=n(this),h=l(g.parentNode).path(a);g.appendChild(h.node),d=h.id,h.attr({id:d})}else a=l(a),a instanceof c&&(d=a.attr("id"),d||(d=a.id,a.attr({id:d})));if(d)if(e=this.textPath,f=this.node,e)e.attr({"xlink:href":"#"+d});else{for(e=p("textPath",{"xlink:href":"#"+d});f.firstChild;)e.appendChild(f.firstChild);f.appendChild(e),this.textPath=l(e)}}})(-1),b.on("snap.util.attr.text",function(a){if("text"==this.type){for(var c=this.node,d=function(a){var b=p("tspan");if(m(a,"array"))for(var c=0;c<a.length;c++)b.appendChild(d(a[c]));else b.appendChild(e.doc.createTextNode(a));return b.normalize&&b.normalize(),b};c.firstChild;)c.removeChild(c.firstChild);for(var f=d(a);f.firstChild;)c.appendChild(f.firstChild)}b.stop()})(-1),b.on("snap.util.attr.fontSize",h)(-1),b.on("snap.util.attr.font-size",h)(-1),b.on("snap.util.getattr.transform",function(){return b.stop(),this.transform()})(-1),b.on("snap.util.getattr.textpath",function(){return b.stop(),this.textPath})(-1),function(){function c(c){return function(){b.stop();var d=e.doc.defaultView.getComputedStyle(this.node,null).getPropertyValue("marker-"+c);return"none"==d?d:a(e.doc.getElementById(d.match(o)[1]))}}function d(a){return function(c){b.stop();var d="marker"+a.charAt(0).toUpperCase()+a.substring(1);if(""==c||!c)return void(this.node.style[d]="none");if("marker"==c.type){var e=c.node.id;return e||p(c.node,{id:c.id}),void(this.node.style[d]=q(e))}}}b.on("snap.util.getattr.marker-end",c("end"))(-1),b.on("snap.util.getattr.markerEnd",c("end"))(-1),b.on("snap.util.getattr.marker-start",c("start"))(-1),b.on("snap.util.getattr.markerStart",c("start"))(-1),b.on("snap.util.getattr.marker-mid",c("mid"))(-1),b.on("snap.util.getattr.markerMid",c("mid"))(-1),b.on("snap.util.attr.marker-end",d("end"))(-1),b.on("snap.util.attr.markerEnd",d("end"))(-1),b.on("snap.util.attr.marker-start",d("start"))(-1),b.on("snap.util.attr.markerStart",d("start"))(-1),b.on("snap.util.attr.marker-mid",d("mid"))(-1),b.on("snap.util.attr.markerMid",d("mid"))(-1)}(),b.on("snap.util.getattr.r",function(){return"rect"==this.type&&p(this.node,"rx")==p(this.node,"ry")?(b.stop(),p(this.node,"rx")):void 0})(-1),b.on("snap.util.getattr.text",function(){if("text"==this.type||"tspan"==this.type){b.stop();var a=i(this.node);return 1==a.length?a[0]:a}})(-1),b.on("snap.util.getattr.#text",function(){return this.node.textContent})(-1),b.on("snap.util.getattr.viewBox",function(){b.stop();var c=p(this.node,"viewBox");return c?(c=c.split(s),a._.box(+c[0],+c[1],+c[2],+c[3])):void 0})(-1),b.on("snap.util.getattr.points",function(){var a=p(this.node,"points");return b.stop(),a?a.split(s):void 0})(-1),b.on("snap.util.getattr.path",function(){var a=p(this.node,"d");return b.stop(),a})(-1),b.on("snap.util.getattr.class",function(){return this.node.className.baseVal})(-1),b.on("snap.util.getattr.fontSize",j)(-1),b.on("snap.util.getattr.font-size",j)(-1)}),d.plugin(function(a,b){var c=/\S+/g,d=String,e=b.prototype;e.addClass=function(a){var b,e,f,g,h=d(a||"").match(c)||[],i=this.node,j=i.className.baseVal,k=j.match(c)||[];if(h.length){for(b=0;f=h[b++];)e=k.indexOf(f),~e||k.push(f);g=k.join(" "),j!=g&&(i.className.baseVal=g)}return this},e.removeClass=function(a){var b,e,f,g,h=d(a||"").match(c)||[],i=this.node,j=i.className.baseVal,k=j.match(c)||[];if(k.length){for(b=0;f=h[b++];)e=k.indexOf(f),~e&&k.splice(e,1);g=k.join(" "),j!=g&&(i.className.baseVal=g)}return this},e.hasClass=function(a){var b=this.node,d=b.className.baseVal,e=d.match(c)||[];return!!~e.indexOf(a)},e.toggleClass=function(a,b){if(null!=b)return b?this.addClass(a):this.removeClass(a);var d,e,f,g,h=(a||"").match(c)||[],i=this.node,j=i.className.baseVal,k=j.match(c)||[];for(d=0;f=h[d++];)e=k.indexOf(f),~e?k.splice(e,1):k.push(f);return g=k.join(" "),j!=g&&(i.className.baseVal=g),this}}),d.plugin(function(){function a(a){return a}function c(a){return function(b){return+b.toFixed(3)+a}}var d={"+":function(a,b){return a+b},"-":function(a,b){return a-b},"/":function(a,b){return a/b},"*":function(a,b){return a*b}},e=String,f=/[a-z]+$/i,g=/^\s*([+\-\/*])\s*=\s*([\d.eE+\-]+)\s*([^\d\s]+)?\s*$/;b.on("snap.util.attr",function(a){var c=e(a).match(g);if(c){var h=b.nt(),i=h.substring(h.lastIndexOf(".")+1),j=this.attr(i),k={};b.stop();var l=c[3]||"",m=j.match(f),n=d[c[1]];if(m&&m==l?a=n(parseFloat(j),+c[2]):(j=this.asPX(i),a=n(this.asPX(i),this.asPX(i,c[2]+l))),isNaN(j)||isNaN(a))return;k[i]=a,this.attr(k)}})(-10),b.on("snap.util.equal",function(h,i){var j=e(this.attr(h)||""),k=e(i).match(g);if(k){b.stop();var l=k[3]||"",m=j.match(f),n=d[k[1]];return m&&m==l?{from:parseFloat(j),to:n(parseFloat(j),+k[2]),f:c(m)}:(j=this.asPX(h),{from:j,to:n(j,this.asPX(h,k[2]+l)),f:a})}})(-10)}),d.plugin(function(c,d,e,f){var g=e.prototype,h=c.is;g.rect=function(a,b,c,d,e,f){var g;return null==f&&(f=e),h(a,"object")&&"[object Object]"==a?g=a:null!=a&&(g={x:a,y:b,width:c,height:d},null!=e&&(g.rx=e,g.ry=f)),this.el("rect",g)},g.circle=function(a,b,c){var d;return h(a,"object")&&"[object Object]"==a?d=a:null!=a&&(d={cx:a,cy:b,r:c}),this.el("circle",d)};var i=function(){function a(){this.parentNode.removeChild(this)}return function(b,c){var d=f.doc.createElement("img"),e=f.doc.body;d.style.cssText="position:absolute;left:-9999em;top:-9999em",d.onload=function(){c.call(d),d.onload=d.onerror=null,e.removeChild(d)},d.onerror=a,e.appendChild(d),d.src=b}}();g.image=function(a,b,d,e,f){var g=this.el("image");if(h(a,"object")&&"src"in a)g.attr(a);else if(null!=a){var j={"xlink:href":a,preserveAspectRatio:"none"};null!=b&&null!=d&&(j.x=b,j.y=d),null!=e&&null!=f?(j.width=e,j.height=f):i(a,function(){c._.$(g.node,{width:this.offsetWidth,height:this.offsetHeight})}),c._.$(g.node,j)}return g},g.ellipse=function(a,b,c,d){var e;return h(a,"object")&&"[object Object]"==a?e=a:null!=a&&(e={cx:a,cy:b,rx:c,ry:d}),this.el("ellipse",e)},g.path=function(a){var b;return h(a,"object")&&!h(a,"array")?b=a:a&&(b={d:a}),this.el("path",b)},g.group=g.g=function(a){var b=this.el("g");return 1==arguments.length&&a&&!a.type?b.attr(a):arguments.length&&b.add(Array.prototype.slice.call(arguments,0)),b},g.svg=function(a,b,c,d,e,f,g,i){var j={};return h(a,"object")&&null==b?j=a:(null!=a&&(j.x=a),null!=b&&(j.y=b),null!=c&&(j.width=c),null!=d&&(j.height=d),null!=e&&null!=f&&null!=g&&null!=i&&(j.viewBox=[e,f,g,i])),this.el("svg",j)},g.mask=function(a){var b=this.el("mask");return 1==arguments.length&&a&&!a.type?b.attr(a):arguments.length&&b.add(Array.prototype.slice.call(arguments,0)),b},g.ptrn=function(a,b,c,d,e,f,g,i){if(h(a,"object"))var j=a;else j={patternUnits:"userSpaceOnUse"},a&&(j.x=a),b&&(j.y=b),null!=c&&(j.width=c),null!=d&&(j.height=d),j.viewBox=null!=e&&null!=f&&null!=g&&null!=i?[e,f,g,i]:[a||0,b||0,c||0,d||0];return this.el("pattern",j)},g.use=function(a){return null!=a?(a instanceof d&&(a.attr("id")||a.attr({id:c._.id(a)}),a=a.attr("id")),"#"==String(a).charAt()&&(a=a.substring(1)),this.el("use",{"xlink:href":"#"+a})):d.prototype.use.call(this)},g.symbol=function(a,b,c,d){var e={};return null!=a&&null!=b&&null!=c&&null!=d&&(e.viewBox=[a,b,c,d]),this.el("symbol",e)},g.text=function(a,b,c){var d={};return h(a,"object")?d=a:null!=a&&(d={x:a,y:b,text:c||""}),this.el("text",d)},g.line=function(a,b,c,d){var e={};return h(a,"object")?e=a:null!=a&&(e={x1:a,x2:c,y1:b,y2:d}),this.el("line",e)},g.polyline=function(a){arguments.length>1&&(a=Array.prototype.slice.call(arguments,0));var b={};return h(a,"object")&&!h(a,"array")?b=a:null!=a&&(b={points:a}),this.el("polyline",b)},g.polygon=function(a){arguments.length>1&&(a=Array.prototype.slice.call(arguments,0));var b={};return h(a,"object")&&!h(a,"array")?b=a:null!=a&&(b={points:a}),this.el("polygon",b)},function(){function d(){return this.selectAll("stop")}function e(a,b){var d=k("stop"),e={offset:+b+"%"};return a=c.color(a),e["stop-color"]=a.hex,a.opacity<1&&(e["stop-opacity"]=a.opacity),k(d,e),this.node.appendChild(d),this}function f(){if("linearGradient"==this.type){var a=k(this.node,"x1")||0,b=k(this.node,"x2")||1,d=k(this.node,"y1")||0,e=k(this.node,"y2")||0;return c._.box(a,d,math.abs(b-a),math.abs(e-d))}var f=this.node.cx||.5,g=this.node.cy||.5,h=this.node.r||0;return c._.box(f-h,g-h,2*h,2*h)}function h(a,c){function d(a,b){for(var c=(b-l)/(a-m),d=m;a>d;d++)g[d].offset=+(+l+c*(d-m)).toFixed(2);m=a,l=b}var e,f=b("snap.util.grad.parse",null,c).firstDefined();if(!f)return null;f.params.unshift(a),e="l"==f.type.toLowerCase()?i.apply(0,f.params):j.apply(0,f.params),f.type!=f.type.toLowerCase()&&k(e.node,{gradientUnits:"userSpaceOnUse"});var g=f.stops,h=g.length,l=0,m=0;h--;for(var n=0;h>n;n++)"offset"in g[n]&&d(n,g[n].offset);for(g[h].offset=g[h].offset||100,d(h,g[h].offset),n=0;h>=n;n++){var o=g[n];e.addStop(o.color,o.offset)}return e}function i(a,b,g,h,i){var j=c._.make("linearGradient",a);return j.stops=d,j.addStop=e,j.getBBox=f,null!=b&&k(j.node,{x1:b,y1:g,x2:h,y2:i}),j}function j(a,b,g,h,i,j){var l=c._.make("radialGradient",a);return l.stops=d,l.addStop=e,l.getBBox=f,null!=b&&k(l.node,{cx:b,cy:g,r:h}),null!=i&&null!=j&&k(l.node,{fx:i,fy:j}),l}var k=c._.$;g.gradient=function(a){return h(this.defs,a)},g.gradientLinear=function(a,b,c,d){return i(this.defs,a,b,c,d)},g.gradientRadial=function(a,b,c,d,e){return j(this.defs,a,b,c,d,e)},g.toString=function(){var a,b=this.node.ownerDocument,d=b.createDocumentFragment(),e=b.createElement("div"),f=this.node.cloneNode(!0);return d.appendChild(e),e.appendChild(f),c._.$(f,{xmlns:"http://www.w3.org/2000/svg"}),a=e.innerHTML,d.removeChild(d.firstChild),a},g.toDataURL=function(){return a&&a.btoa?"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(this))):void 0},g.clear=function(){for(var a,b=this.node.firstChild;b;)a=b.nextSibling,"defs"!=b.tagName?b.parentNode.removeChild(b):g.clear.call({node:b}),b=a}}()}),d.plugin(function(a,b){function c(a){var b=c.ps=c.ps||{};return b[a]?b[a].sleep=100:b[a]={sleep:100},setTimeout(function(){for(var c in b)b[K](c)&&c!=a&&(b[c].sleep--,!b[c].sleep&&delete b[c])}),b[a]}function d(a,b,c,d){return null==a&&(a=b=c=d=0),null==b&&(b=a.y,c=a.width,d=a.height,a=a.x),{x:a,y:b,width:c,w:c,height:d,h:d,x2:a+c,y2:b+d,cx:a+c/2,cy:b+d/2,r1:N.min(c,d)/2,r2:N.max(c,d)/2,r0:N.sqrt(c*c+d*d)/2,path:w(a,b,c,d),vb:[a,b,c,d].join(" ")}}function e(){return this.join(",").replace(L,"$1")}function f(a){var b=J(a);return b.toString=e,b}function g(a,b,c,d,e,f,g,h,j){return null==j?n(a,b,c,d,e,f,g,h):i(a,b,c,d,e,f,g,h,o(a,b,c,d,e,f,g,h,j))}function h(c,d){function e(a){return+(+a).toFixed(3)}return a._.cacher(function(a,f,h){a instanceof b&&(a=a.attr("d")),a=E(a);for(var j,k,l,m,n,o="",p={},q=0,r=0,s=a.length;s>r;r++){if(l=a[r],"M"==l[0])j=+l[1],k=+l[2];else{if(m=g(j,k,l[1],l[2],l[3],l[4],l[5],l[6]),q+m>f){if(d&&!p.start){if(n=g(j,k,l[1],l[2],l[3],l[4],l[5],l[6],f-q),o+=["C"+e(n.start.x),e(n.start.y),e(n.m.x),e(n.m.y),e(n.x),e(n.y)],h)return o;p.start=o,o=["M"+e(n.x),e(n.y)+"C"+e(n.n.x),e(n.n.y),e(n.end.x),e(n.end.y),e(l[5]),e(l[6])].join(),q+=m,j=+l[5],k=+l[6];continue}if(!c&&!d)return n=g(j,k,l[1],l[2],l[3],l[4],l[5],l[6],f-q)}q+=m,j=+l[5],k=+l[6]}o+=l.shift()+l}return p.end=o,n=c?q:d?p:i(j,k,l[0],l[1],l[2],l[3],l[4],l[5],1)},null,a._.clone)}function i(a,b,c,d,e,f,g,h,i){var j=1-i,k=R(j,3),l=R(j,2),m=i*i,n=m*i,o=k*a+3*l*i*c+3*j*i*i*e+n*g,p=k*b+3*l*i*d+3*j*i*i*f+n*h,q=a+2*i*(c-a)+m*(e-2*c+a),r=b+2*i*(d-b)+m*(f-2*d+b),s=c+2*i*(e-c)+m*(g-2*e+c),t=d+2*i*(f-d)+m*(h-2*f+d),u=j*a+i*c,v=j*b+i*d,w=j*e+i*g,x=j*f+i*h,y=90-180*N.atan2(q-s,r-t)/O;return{x:o,y:p,m:{x:q,y:r},n:{x:s,y:t},start:{x:u,y:v},end:{x:w,y:x},alpha:y}}function j(b,c,e,f,g,h,i,j){a.is(b,"array")||(b=[b,c,e,f,g,h,i,j]);var k=D.apply(null,b);return d(k.min.x,k.min.y,k.max.x-k.min.x,k.max.y-k.min.y)}function k(a,b,c){return b>=a.x&&b<=a.x+a.width&&c>=a.y&&c<=a.y+a.height}function l(a,b){return a=d(a),b=d(b),k(b,a.x,a.y)||k(b,a.x2,a.y)||k(b,a.x,a.y2)||k(b,a.x2,a.y2)||k(a,b.x,b.y)||k(a,b.x2,b.y)||k(a,b.x,b.y2)||k(a,b.x2,b.y2)||(a.x<b.x2&&a.x>b.x||b.x<a.x2&&b.x>a.x)&&(a.y<b.y2&&a.y>b.y||b.y<a.y2&&b.y>a.y)}function m(a,b,c,d,e){var f=-3*b+9*c-9*d+3*e,g=a*f+6*b-12*c+6*d;return a*g-3*b+3*c}function n(a,b,c,d,e,f,g,h,i){null==i&&(i=1),i=i>1?1:0>i?0:i;for(var j=i/2,k=12,l=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],n=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],o=0,p=0;k>p;p++){var q=j*l[p]+j,r=m(q,a,c,e,g),s=m(q,b,d,f,h),t=r*r+s*s;o+=n[p]*N.sqrt(t)}return j*o}function o(a,b,c,d,e,f,g,h,i){if(!(0>i||n(a,b,c,d,e,f,g,h)<i)){var j,k=1,l=k/2,m=k-l,o=.01;for(j=n(a,b,c,d,e,f,g,h,m);S(j-i)>o;)l/=2,m+=(i>j?1:-1)*l,j=n(a,b,c,d,e,f,g,h,m);return m}}function p(a,b,c,d,e,f,g,h){if(!(Q(a,c)<P(e,g)||P(a,c)>Q(e,g)||Q(b,d)<P(f,h)||P(b,d)>Q(f,h))){var i=(a*d-b*c)*(e-g)-(a-c)*(e*h-f*g),j=(a*d-b*c)*(f-h)-(b-d)*(e*h-f*g),k=(a-c)*(f-h)-(b-d)*(e-g);if(k){var l=i/k,m=j/k,n=+l.toFixed(2),o=+m.toFixed(2);if(!(n<+P(a,c).toFixed(2)||n>+Q(a,c).toFixed(2)||n<+P(e,g).toFixed(2)||n>+Q(e,g).toFixed(2)||o<+P(b,d).toFixed(2)||o>+Q(b,d).toFixed(2)||o<+P(f,h).toFixed(2)||o>+Q(f,h).toFixed(2)))return{x:l,y:m}}}}function q(a,b,c){var d=j(a),e=j(b);if(!l(d,e))return c?0:[];for(var f=n.apply(0,a),g=n.apply(0,b),h=~~(f/8),k=~~(g/8),m=[],o=[],q={},r=c?0:[],s=0;h+1>s;s++){var t=i.apply(0,a.concat(s/h));m.push({x:t.x,y:t.y,t:s/h})}for(s=0;k+1>s;s++)t=i.apply(0,b.concat(s/k)),o.push({x:t.x,y:t.y,t:s/k});for(s=0;h>s;s++)for(var u=0;k>u;u++){var v=m[s],w=m[s+1],x=o[u],y=o[u+1],z=S(w.x-v.x)<.001?"y":"x",A=S(y.x-x.x)<.001?"y":"x",B=p(v.x,v.y,w.x,w.y,x.x,x.y,y.x,y.y);if(B){if(q[B.x.toFixed(4)]==B.y.toFixed(4))continue;q[B.x.toFixed(4)]=B.y.toFixed(4);var C=v.t+S((B[z]-v[z])/(w[z]-v[z]))*(w.t-v.t),D=x.t+S((B[A]-x[A])/(y[A]-x[A]))*(y.t-x.t);C>=0&&1>=C&&D>=0&&1>=D&&(c?r++:r.push({x:B.x,y:B.y,t1:C,t2:D}))}}return r}function r(a,b){return t(a,b)}function s(a,b){return t(a,b,1)}function t(a,b,c){a=E(a),b=E(b);for(var d,e,f,g,h,i,j,k,l,m,n=c?0:[],o=0,p=a.length;p>o;o++){var r=a[o];if("M"==r[0])d=h=r[1],e=i=r[2];else{"C"==r[0]?(l=[d,e].concat(r.slice(1)),d=l[6],e=l[7]):(l=[d,e,d,e,h,i,h,i],d=h,e=i);for(var s=0,t=b.length;t>s;s++){var u=b[s];if("M"==u[0])f=j=u[1],g=k=u[2];else{"C"==u[0]?(m=[f,g].concat(u.slice(1)),f=m[6],g=m[7]):(m=[f,g,f,g,j,k,j,k],f=j,g=k);var v=q(l,m,c);if(c)n+=v;else{for(var w=0,x=v.length;x>w;w++)v[w].segment1=o,v[w].segment2=s,v[w].bez1=l,v[w].bez2=m;n=n.concat(v)}}}}}return n}function u(a,b,c){var d=v(a);return k(d,b,c)&&t(a,[["M",b,c],["H",d.x2+10]],1)%2==1}function v(a){var b=c(a);if(b.bbox)return J(b.bbox);if(!a)return d();a=E(a);for(var e,f=0,g=0,h=[],i=[],j=0,k=a.length;k>j;j++)if(e=a[j],"M"==e[0])f=e[1],g=e[2],h.push(f),i.push(g);else{var l=D(f,g,e[1],e[2],e[3],e[4],e[5],e[6]);h=h.concat(l.min.x,l.max.x),i=i.concat(l.min.y,l.max.y),f=e[5],g=e[6]}var m=P.apply(0,h),n=P.apply(0,i),o=Q.apply(0,h),p=Q.apply(0,i),q=d(m,n,o-m,p-n);return b.bbox=J(q),q}function w(a,b,c,d,f){if(f)return[["M",+a+ +f,b],["l",c-2*f,0],["a",f,f,0,0,1,f,f],["l",0,d-2*f],["a",f,f,0,0,1,-f,f],["l",2*f-c,0],["a",f,f,0,0,1,-f,-f],["l",0,2*f-d],["a",f,f,0,0,1,f,-f],["z"]];var g=[["M",a,b],["l",c,0],["l",0,d],["l",-c,0],["z"]];return g.toString=e,g}function x(a,b,c,d,f){if(null==f&&null==d&&(d=c),a=+a,b=+b,c=+c,d=+d,null!=f)var g=Math.PI/180,h=a+c*Math.cos(-d*g),i=a+c*Math.cos(-f*g),j=b+c*Math.sin(-d*g),k=b+c*Math.sin(-f*g),l=[["M",h,j],["A",c,c,0,+(f-d>180),0,i,k]];else l=[["M",a,b],["m",0,-d],["a",c,d,0,1,1,0,2*d],["a",c,d,0,1,1,0,-2*d],["z"]];return l.toString=e,l}function y(b){var d=c(b),g=String.prototype.toLowerCase;if(d.rel)return f(d.rel);a.is(b,"array")&&a.is(b&&b[0],"array")||(b=a.parsePathString(b));var h=[],i=0,j=0,k=0,l=0,m=0;"M"==b[0][0]&&(i=b[0][1],j=b[0][2],k=i,l=j,m++,h.push(["M",i,j]));for(var n=m,o=b.length;o>n;n++){var p=h[n]=[],q=b[n];if(q[0]!=g.call(q[0]))switch(p[0]=g.call(q[0]),p[0]){case"a":p[1]=q[1],p[2]=q[2],p[3]=q[3],p[4]=q[4],p[5]=q[5],p[6]=+(q[6]-i).toFixed(3),p[7]=+(q[7]-j).toFixed(3);break;case"v":p[1]=+(q[1]-j).toFixed(3);break;case"m":k=q[1],l=q[2];default:for(var r=1,s=q.length;s>r;r++)p[r]=+(q[r]-(r%2?i:j)).toFixed(3)}else{p=h[n]=[],"m"==q[0]&&(k=q[1]+i,l=q[2]+j);for(var t=0,u=q.length;u>t;t++)h[n][t]=q[t]}var v=h[n].length;switch(h[n][0]){case"z":i=k,j=l;break;case"h":i+=+h[n][v-1];break;case"v":j+=+h[n][v-1];break;default:i+=+h[n][v-2],j+=+h[n][v-1]}}return h.toString=e,d.rel=f(h),h}function z(b){var d=c(b);if(d.abs)return f(d.abs);if(I(b,"array")&&I(b&&b[0],"array")||(b=a.parsePathString(b)),!b||!b.length)return[["M",0,0]];var g,h=[],i=0,j=0,k=0,l=0,m=0;"M"==b[0][0]&&(i=+b[0][1],j=+b[0][2],k=i,l=j,m++,h[0]=["M",i,j]);for(var n,o,p=3==b.length&&"M"==b[0][0]&&"R"==b[1][0].toUpperCase()&&"Z"==b[2][0].toUpperCase(),q=m,r=b.length;r>q;q++){if(h.push(n=[]),o=b[q],g=o[0],g!=g.toUpperCase())switch(n[0]=g.toUpperCase(),n[0]){case"A":n[1]=o[1],n[2]=o[2],n[3]=o[3],n[4]=o[4],n[5]=o[5],n[6]=+o[6]+i,n[7]=+o[7]+j;break;case"V":n[1]=+o[1]+j;break;case"H":n[1]=+o[1]+i;break;case"R":for(var s=[i,j].concat(o.slice(1)),t=2,u=s.length;u>t;t++)s[t]=+s[t]+i,s[++t]=+s[t]+j;h.pop(),h=h.concat(G(s,p));break;case"O":h.pop(),s=x(i,j,o[1],o[2]),s.push(s[0]),h=h.concat(s);break;case"U":h.pop(),h=h.concat(x(i,j,o[1],o[2],o[3])),n=["U"].concat(h[h.length-1].slice(-2));break;case"M":k=+o[1]+i,l=+o[2]+j;default:for(t=1,u=o.length;u>t;t++)n[t]=+o[t]+(t%2?i:j)}else if("R"==g)s=[i,j].concat(o.slice(1)),h.pop(),h=h.concat(G(s,p)),n=["R"].concat(o.slice(-2));else if("O"==g)h.pop(),s=x(i,j,o[1],o[2]),s.push(s[0]),h=h.concat(s);else if("U"==g)h.pop(),h=h.concat(x(i,j,o[1],o[2],o[3])),n=["U"].concat(h[h.length-1].slice(-2));else for(var v=0,w=o.length;w>v;v++)n[v]=o[v];if(g=g.toUpperCase(),"O"!=g)switch(n[0]){case"Z":i=+k,j=+l;break;case"H":i=n[1];break;case"V":j=n[1];break;case"M":k=n[n.length-2],l=n[n.length-1];default:i=n[n.length-2],j=n[n.length-1]}}return h.toString=e,d.abs=f(h),h}function A(a,b,c,d){return[a,b,c,d,c,d]}function B(a,b,c,d,e,f){var g=1/3,h=2/3;return[g*a+h*c,g*b+h*d,g*e+h*c,g*f+h*d,e,f]}function C(b,c,d,e,f,g,h,i,j,k){var l,m=120*O/180,n=O/180*(+f||0),o=[],p=a._.cacher(function(a,b,c){var d=a*N.cos(c)-b*N.sin(c),e=a*N.sin(c)+b*N.cos(c);return{x:d,y:e}});if(k)y=k[0],z=k[1],w=k[2],x=k[3];else{l=p(b,c,-n),b=l.x,c=l.y,l=p(i,j,-n),i=l.x,j=l.y;var q=(N.cos(O/180*f),N.sin(O/180*f),(b-i)/2),r=(c-j)/2,s=q*q/(d*d)+r*r/(e*e);s>1&&(s=N.sqrt(s),d=s*d,e=s*e);var t=d*d,u=e*e,v=(g==h?-1:1)*N.sqrt(S((t*u-t*r*r-u*q*q)/(t*r*r+u*q*q))),w=v*d*r/e+(b+i)/2,x=v*-e*q/d+(c+j)/2,y=N.asin(((c-x)/e).toFixed(9)),z=N.asin(((j-x)/e).toFixed(9));y=w>b?O-y:y,z=w>i?O-z:z,0>y&&(y=2*O+y),0>z&&(z=2*O+z),h&&y>z&&(y-=2*O),!h&&z>y&&(z-=2*O)}var A=z-y;if(S(A)>m){var B=z,D=i,E=j;z=y+m*(h&&z>y?1:-1),i=w+d*N.cos(z),j=x+e*N.sin(z),o=C(i,j,d,e,f,0,h,D,E,[z,B,w,x])}A=z-y;var F=N.cos(y),G=N.sin(y),H=N.cos(z),I=N.sin(z),J=N.tan(A/4),K=4/3*d*J,L=4/3*e*J,M=[b,c],P=[b+K*G,c-L*F],Q=[i+K*I,j-L*H],R=[i,j];if(P[0]=2*M[0]-P[0],P[1]=2*M[1]-P[1],k)return[P,Q,R].concat(o);o=[P,Q,R].concat(o).join().split(",");for(var T=[],U=0,V=o.length;V>U;U++)T[U]=U%2?p(o[U-1],o[U],n).y:p(o[U],o[U+1],n).x;return T}function D(a,b,c,d,e,f,g,h){for(var i,j,k,l,m,n,o,p,q=[],r=[[],[]],s=0;2>s;++s)if(0==s?(j=6*a-12*c+6*e,i=-3*a+9*c-9*e+3*g,k=3*c-3*a):(j=6*b-12*d+6*f,i=-3*b+9*d-9*f+3*h,k=3*d-3*b),S(i)<1e-12){if(S(j)<1e-12)continue;l=-k/j,l>0&&1>l&&q.push(l)}else o=j*j-4*k*i,p=N.sqrt(o),0>o||(m=(-j+p)/(2*i),m>0&&1>m&&q.push(m),n=(-j-p)/(2*i),n>0&&1>n&&q.push(n));for(var t,u=q.length,v=u;u--;)l=q[u],t=1-l,r[0][u]=t*t*t*a+3*t*t*l*c+3*t*l*l*e+l*l*l*g,r[1][u]=t*t*t*b+3*t*t*l*d+3*t*l*l*f+l*l*l*h;return r[0][v]=a,r[1][v]=b,r[0][v+1]=g,r[1][v+1]=h,r[0].length=r[1].length=v+2,{min:{x:P.apply(0,r[0]),y:P.apply(0,r[1])},max:{x:Q.apply(0,r[0]),y:Q.apply(0,r[1])}}}function E(a,b){var d=!b&&c(a);if(!b&&d.curve)return f(d.curve);for(var e=z(a),g=b&&z(b),h={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},i={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},j=(function(a,b,c){var d,e;if(!a)return["C",b.x,b.y,b.x,b.y,b.x,b.y];switch(!(a[0]in{T:1,Q:1})&&(b.qx=b.qy=null),a[0]){case"M":b.X=a[1],b.Y=a[2];break;case"A":a=["C"].concat(C.apply(0,[b.x,b.y].concat(a.slice(1))));break;case"S":"C"==c||"S"==c?(d=2*b.x-b.bx,e=2*b.y-b.by):(d=b.x,e=b.y),a=["C",d,e].concat(a.slice(1));break;case"T":"Q"==c||"T"==c?(b.qx=2*b.x-b.qx,b.qy=2*b.y-b.qy):(b.qx=b.x,b.qy=b.y),a=["C"].concat(B(b.x,b.y,b.qx,b.qy,a[1],a[2]));break;case"Q":b.qx=a[1],b.qy=a[2],a=["C"].concat(B(b.x,b.y,a[1],a[2],a[3],a[4]));break;case"L":a=["C"].concat(A(b.x,b.y,a[1],a[2]));break;case"H":a=["C"].concat(A(b.x,b.y,a[1],b.y));break;case"V":a=["C"].concat(A(b.x,b.y,b.x,a[1]));break;case"Z":a=["C"].concat(A(b.x,b.y,b.X,b.Y))}return a}),k=function(a,b){if(a[b].length>7){a[b].shift();for(var c=a[b];c.length;)m[b]="A",g&&(n[b]="A"),a.splice(b++,0,["C"].concat(c.splice(0,6)));a.splice(b,1),r=Q(e.length,g&&g.length||0)}},l=function(a,b,c,d,f){a&&b&&"M"==a[f][0]&&"M"!=b[f][0]&&(b.splice(f,0,["M",d.x,d.y]),c.bx=0,c.by=0,c.x=a[f][1],c.y=a[f][2],r=Q(e.length,g&&g.length||0))},m=[],n=[],o="",p="",q=0,r=Q(e.length,g&&g.length||0);r>q;q++){e[q]&&(o=e[q][0]),"C"!=o&&(m[q]=o,q&&(p=m[q-1])),e[q]=j(e[q],h,p),"A"!=m[q]&&"C"==o&&(m[q]="C"),k(e,q),g&&(g[q]&&(o=g[q][0]),"C"!=o&&(n[q]=o,q&&(p=n[q-1])),g[q]=j(g[q],i,p),"A"!=n[q]&&"C"==o&&(n[q]="C"),k(g,q)),l(e,g,h,i,q),l(g,e,i,h,q);var s=e[q],t=g&&g[q],u=s.length,v=g&&t.length;h.x=s[u-2],h.y=s[u-1],h.bx=M(s[u-4])||h.x,h.by=M(s[u-3])||h.y,i.bx=g&&(M(t[v-4])||i.x),i.by=g&&(M(t[v-3])||i.y),i.x=g&&t[v-2],i.y=g&&t[v-1]}return g||(d.curve=f(e)),g?[e,g]:e}function F(a,b){if(!b)return a;var c,d,e,f,g,h,i;for(a=E(a),e=0,g=a.length;g>e;e++)for(i=a[e],f=1,h=i.length;h>f;f+=2)c=b.x(i[f],i[f+1]),d=b.y(i[f],i[f+1]),i[f]=c,i[f+1]=d;return a}function G(a,b){for(var c=[],d=0,e=a.length;e-2*!b>d;d+=2){var f=[{x:+a[d-2],y:+a[d-1]},{x:+a[d],y:+a[d+1]},{x:+a[d+2],y:+a[d+3]},{x:+a[d+4],y:+a[d+5]}];b?d?e-4==d?f[3]={x:+a[0],y:+a[1]}:e-2==d&&(f[2]={x:+a[0],y:+a[1]},f[3]={x:+a[2],y:+a[3]}):f[0]={x:+a[e-2],y:+a[e-1]}:e-4==d?f[3]=f[2]:d||(f[0]={x:+a[d],y:+a[d+1]}),c.push(["C",(-f[0].x+6*f[1].x+f[2].x)/6,(-f[0].y+6*f[1].y+f[2].y)/6,(f[1].x+6*f[2].x-f[3].x)/6,(f[1].y+6*f[2].y-f[3].y)/6,f[2].x,f[2].y])}return c}var H=b.prototype,I=a.is,J=a._.clone,K="hasOwnProperty",L=/,?([a-z]),?/gi,M=parseFloat,N=Math,O=N.PI,P=N.min,Q=N.max,R=N.pow,S=N.abs,T=h(1),U=h(),V=h(0,1),W=a._unit2px,X={path:function(a){return a.attr("path")},circle:function(a){var b=W(a);return x(b.cx,b.cy,b.r)},ellipse:function(a){var b=W(a);return x(b.cx||0,b.cy||0,b.rx,b.ry)},rect:function(a){var b=W(a);return w(b.x||0,b.y||0,b.width,b.height,b.rx,b.ry)},image:function(a){var b=W(a);return w(b.x||0,b.y||0,b.width,b.height)},line:function(a){return"M"+[a.attr("x1")||0,a.attr("y1")||0,a.attr("x2"),a.attr("y2")]},polyline:function(a){return"M"+a.attr("points")},polygon:function(a){return"M"+a.attr("points")+"z"},deflt:function(a){var b=a.node.getBBox();return w(b.x,b.y,b.width,b.height)}};a.path=c,a.path.getTotalLength=T,a.path.getPointAtLength=U,a.path.getSubpath=function(a,b,c){if(this.getTotalLength(a)-c<1e-6)return V(a,b).end;var d=V(a,c,1);return b?V(d,b).end:d},H.getTotalLength=function(){return this.node.getTotalLength?this.node.getTotalLength():void 0},H.getPointAtLength=function(a){return U(this.attr("d"),a)},H.getSubpath=function(b,c){return a.path.getSubpath(this.attr("d"),b,c)},a._.box=d,a.path.findDotsAtSegment=i,a.path.bezierBBox=j,a.path.isPointInsideBBox=k,a.closest=function(b,c,e,f){for(var g=100,h=d(b-g/2,c-g/2,g,g),i=[],j=e[0].hasOwnProperty("x")?function(a){return{x:e[a].x,y:e[a].y}}:function(a){return{x:e[a],y:f[a]}},l=0;1e6>=g&&!l;){for(var m=0,n=e.length;n>m;m++){var o=j(m);if(k(h,o.x,o.y)){l++,i.push(o);break}}l||(g*=2,h=d(b-g/2,c-g/2,g,g))}if(1e6!=g){var p,q=1/0;for(m=0,n=i.length;n>m;m++){var r=a.len(b,c,i[m].x,i[m].y);q>r&&(q=r,i[m].len=r,p=i[m])}return p}},a.path.isBBoxIntersect=l,a.path.intersection=r,a.path.intersectionNumber=s,a.path.isPointInside=u,a.path.getBBox=v,a.path.get=X,a.path.toRelative=y,a.path.toAbsolute=z,a.path.toCubic=E,a.path.map=F,a.path.toString=e,a.path.clone=f}),d.plugin(function(a){var d=Math.max,e=Math.min,f=function(a){if(this.items=[],this.bindings={},this.length=0,this.type="set",a)for(var b=0,c=a.length;c>b;b++)a[b]&&(this[this.items.length]=this.items[this.items.length]=a[b],this.length++)},g=f.prototype;g.push=function(){for(var a,b,c=0,d=arguments.length;d>c;c++)a=arguments[c],a&&(b=this.items.length,this[b]=this.items[b]=a,this.length++);return this},g.pop=function(){return this.length&&delete this[this.length--],this.items.pop()},g.forEach=function(a,b){for(var c=0,d=this.items.length;d>c;c++)if(a.call(b,this.items[c],c)===!1)return this;return this},g.animate=function(d,e,f,g){"function"!=typeof f||f.length||(g=f,f=c.linear),d instanceof a._.Animation&&(g=d.callback,f=d.easing,e=f.dur,d=d.attr);var h=arguments;if(a.is(d,"array")&&a.is(h[h.length-1],"array"))var i=!0;var j,k=function(){j?this.b=j:j=this.b},l=0,m=this,n=g&&function(){++l==m.length&&g.call(this)
};return this.forEach(function(a,c){b.once("snap.animcreated."+a.id,k),i?h[c]&&a.animate.apply(a,h[c]):a.animate(d,e,f,n)})},g.remove=function(){for(;this.length;)this.pop().remove();return this},g.bind=function(a,b,c){var d={};if("function"==typeof b)this.bindings[a]=b;else{var e=c||a;this.bindings[a]=function(a){d[e]=a,b.attr(d)}}return this},g.attr=function(a){var b={};for(var c in a)this.bindings[c]?this.bindings[c](a[c]):b[c]=a[c];for(var d=0,e=this.items.length;e>d;d++)this.items[d].attr(b);return this},g.clear=function(){for(;this.length;)this.pop()},g.splice=function(a,b){a=0>a?d(this.length+a,0):a,b=d(0,e(this.length-a,b));var c,g=[],h=[],i=[];for(c=2;c<arguments.length;c++)i.push(arguments[c]);for(c=0;b>c;c++)h.push(this[a+c]);for(;c<this.length-a;c++)g.push(this[a+c]);var j=i.length;for(c=0;c<j+g.length;c++)this.items[a+c]=this[a+c]=j>c?i[c]:g[c-j];for(c=this.items.length=this.length-=b-j;this[c];)delete this[c++];return new f(h)},g.exclude=function(a){for(var b=0,c=this.length;c>b;b++)if(this[b]==a)return this.splice(b,1),!0;return!1},g.insertAfter=function(a){for(var b=this.items.length;b--;)this.items[b].insertAfter(a);return this},g.getBBox=function(){for(var a=[],b=[],c=[],f=[],g=this.items.length;g--;)if(!this.items[g].removed){var h=this.items[g].getBBox();a.push(h.x),b.push(h.y),c.push(h.x+h.width),f.push(h.y+h.height)}return a=e.apply(0,a),b=e.apply(0,b),c=d.apply(0,c),f=d.apply(0,f),{x:a,y:b,x2:c,y2:f,width:c-a,height:f-b,cx:a+(c-a)/2,cy:b+(f-b)/2}},g.clone=function(a){a=new f;for(var b=0,c=this.items.length;c>b;b++)a.push(this.items[b].clone());return a},g.toString=function(){return"Snap‘s set"},g.type="set",a.Set=f,a.set=function(){var a=new f;return arguments.length&&a.push.apply(a,Array.prototype.slice.call(arguments,0)),a}}),d.plugin(function(a,c){function d(a){var b=a[0];switch(b.toLowerCase()){case"t":return[b,0,0];case"m":return[b,1,0,0,1,0,0];case"r":return 4==a.length?[b,0,a[2],a[3]]:[b,0];case"s":return 5==a.length?[b,1,1,a[3],a[4]]:3==a.length?[b,1,1]:[b,1]}}function e(b,c,e){c=p(c).replace(/\.{3}|\u2026/g,b),b=a.parseTransformString(b)||[],c=a.parseTransformString(c)||[];for(var f,g,h,i,l=Math.max(b.length,c.length),m=[],n=[],o=0;l>o;o++){if(h=b[o]||d(c[o]),i=c[o]||d(h),h[0]!=i[0]||"r"==h[0].toLowerCase()&&(h[2]!=i[2]||h[3]!=i[3])||"s"==h[0].toLowerCase()&&(h[3]!=i[3]||h[4]!=i[4])){b=a._.transform2matrix(b,e()),c=a._.transform2matrix(c,e()),m=[["m",b.a,b.b,b.c,b.d,b.e,b.f]],n=[["m",c.a,c.b,c.c,c.d,c.e,c.f]];break}for(m[o]=[],n[o]=[],f=0,g=Math.max(h.length,i.length);g>f;f++)f in h&&(m[o][f]=h[f]),f in i&&(n[o][f]=i[f])}return{from:k(m),to:k(n),f:j(m)}}function f(a){return a}function g(a){return function(b){return+b.toFixed(3)+a}}function h(a){return a.join(" ")}function i(b){return a.rgb(b[0],b[1],b[2])}function j(a){var b,c,d,e,f,g,h=0,i=[];for(b=0,c=a.length;c>b;b++){for(f="[",g=['"'+a[b][0]+'"'],d=1,e=a[b].length;e>d;d++)g[d]="val["+h++ +"]";f+=g+"]",i[b]=f}return Function("val","return Snap.path.toString.call(["+i+"])")}function k(a){for(var b=[],c=0,d=a.length;d>c;c++)for(var e=1,f=a[c].length;f>e;e++)b.push(a[c][e]);return b}function l(a){return isFinite(parseFloat(a))}function m(b,c){return a.is(b,"array")&&a.is(c,"array")?b.toString()==c.toString():!1}var n={},o=/[a-z]+$/i,p=String;n.stroke=n.fill="colour",c.prototype.equal=function(a,c){return b("snap.util.equal",this,a,c).firstDefined()},b.on("snap.util.equal",function(b,c){var d,q,r=p(this.attr(b)||""),s=this;if(l(r)&&l(c))return{from:parseFloat(r),to:parseFloat(c),f:f};if("colour"==n[b])return d=a.color(r),q=a.color(c),{from:[d.r,d.g,d.b,d.opacity],to:[q.r,q.g,q.b,q.opacity],f:i};if("viewBox"==b)return d=this.attr(b).vb.split(" ").map(Number),q=c.split(" ").map(Number),{from:d,to:q,f:h};if("transform"==b||"gradientTransform"==b||"patternTransform"==b)return c instanceof a.Matrix&&(c=c.toTransformString()),a._.rgTransform.test(c)||(c=a._.svgTransform2string(c)),e(r,c,function(){return s.getBBox(1)});if("d"==b||"path"==b)return d=a.path.toCubic(r,c),{from:k(d[0]),to:k(d[1]),f:j(d[0])};if("points"==b)return d=p(r).split(a._.separator),q=p(c).split(a._.separator),{from:d,to:q,f:function(a){return a}};var t=r.match(o),u=p(c).match(o);return t&&m(t,u)?{from:parseFloat(r),to:parseFloat(c),f:g(t)}:{from:this.asPX(b),to:this.asPX(b,c),f:f}})}),d.plugin(function(a,c,d,e){for(var f=c.prototype,g="hasOwnProperty",h=("createTouch"in e.doc),i=["click","dblclick","mousedown","mousemove","mouseout","mouseover","mouseup","touchstart","touchmove","touchend","touchcancel"],j={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},k=(function(a,b){var c="y"==a?"scrollTop":"scrollLeft",d=b&&b.node?b.node.ownerDocument:e.doc;return d[c in d.documentElement?"documentElement":"body"][c]}),l=function(){return this.originalEvent.preventDefault()},m=function(){return this.originalEvent.stopPropagation()},n=function(a,b,c,d){var e=h&&j[b]?j[b]:b,f=function(e){var f=k("y",d),i=k("x",d);if(h&&j[g](b))for(var n=0,o=e.targetTouches&&e.targetTouches.length;o>n;n++)if(e.targetTouches[n].target==a||a.contains(e.targetTouches[n].target)){var p=e;e=e.targetTouches[n],e.originalEvent=p,e.preventDefault=l,e.stopPropagation=m;break}var q=e.clientX+i,r=e.clientY+f;return c.call(d,e,q,r)};return b!==e&&a.addEventListener(b,f,!1),a.addEventListener(e,f,!1),function(){return b!==e&&a.removeEventListener(b,f,!1),a.removeEventListener(e,f,!1),!0}},o=[],p=function(a){for(var c,d=a.clientX,e=a.clientY,f=k("y"),g=k("x"),i=o.length;i--;){if(c=o[i],h){for(var j,l=a.touches&&a.touches.length;l--;)if(j=a.touches[l],j.identifier==c.el._drag.id||c.el.node.contains(j.target)){d=j.clientX,e=j.clientY,(a.originalEvent?a.originalEvent:a).preventDefault();break}}else a.preventDefault();{var m=c.el.node;m.nextSibling,m.parentNode,m.style.display}d+=g,e+=f,b("snap.drag.move."+c.el.id,c.move_scope||c.el,d-c.el._drag.x,e-c.el._drag.y,d,e,a)}},q=function(c){a.unmousemove(p).unmouseup(q);for(var d,e=o.length;e--;)d=o[e],d.el._drag={},b("snap.drag.end."+d.el.id,d.end_scope||d.start_scope||d.move_scope||d.el,c),b.off("snap.drag.*."+d.el.id);o=[]},r=i.length;r--;)!function(b){a[b]=f[b]=function(c,d){if(a.is(c,"function"))this.events=this.events||[],this.events.push({name:b,f:c,unbind:n(this.node||document,b,c,d||this)});else for(var e=0,f=this.events.length;f>e;e++)if(this.events[e].name==b)try{this.events[e].f.call(this)}catch(g){}return this},a["un"+b]=f["un"+b]=function(a){for(var c=this.events||[],d=c.length;d--;)if(c[d].name==b&&(c[d].f==a||!a))return c[d].unbind(),c.splice(d,1),!c.length&&delete this.events,this;return this}}(i[r]);f.hover=function(a,b,c,d){return this.mouseover(a,c).mouseout(b,d||c)},f.unhover=function(a,b){return this.unmouseover(a).unmouseout(b)};var s=[];f.drag=function(c,d,e,f,g,h){function i(i,j,l){(i.originalEvent||i).preventDefault(),k._drag.x=j,k._drag.y=l,k._drag.id=i.identifier,!o.length&&a.mousemove(p).mouseup(q),o.push({el:k,move_scope:f,start_scope:g,end_scope:h}),d&&b.on("snap.drag.start."+k.id,d),c&&b.on("snap.drag.move."+k.id,c),e&&b.on("snap.drag.end."+k.id,e),b("snap.drag.start."+k.id,g||f||k,j,l,i)}function j(a,c,d){b("snap.draginit."+k.id,k,a,c,d)}var k=this;if(!arguments.length){var l;return k.drag(function(a,b){this.attr({transform:l+(l?"T":"t")+[a,b]})},function(){l=this.transform().local})}return b.on("snap.draginit."+k.id,i),k._drag={},s.push({el:k,start:i,init:j}),k.mousedown(j),k},f.undrag=function(){for(var c=s.length;c--;)s[c].el==this&&(this.unmousedown(s[c].init),s.splice(c,1),b.unbind("snap.drag.*."+this.id),b.unbind("snap.draginit."+this.id));return!s.length&&a.unmousemove(p).unmouseup(q),this}}),d.plugin(function(a,c,d){var e=(c.prototype,d.prototype),f=/^\s*url\((.+)\)/,g=String,h=a._.$;a.filter={},e.filter=function(b){var d=this;"svg"!=d.type&&(d=d.paper);var e=a.parse(g(b)),f=a._.id(),i=(d.node.offsetWidth,d.node.offsetHeight,h("filter"));return h(i,{id:f,filterUnits:"userSpaceOnUse"}),i.appendChild(e.node),d.defs.appendChild(i),new c(i)},b.on("snap.util.getattr.filter",function(){b.stop();var c=h(this.node,"filter");if(c){var d=g(c).match(f);return d&&a.select(d[1])}}),b.on("snap.util.attr.filter",function(d){if(d instanceof c&&"filter"==d.type){b.stop();var e=d.node.id;e||(h(d.node,{id:d.id}),e=d.id),h(this.node,{filter:a.url(e)})}d&&"none"!=d||(b.stop(),this.node.removeAttribute("filter"))}),a.filter.blur=function(b,c){null==b&&(b=2);var d=null==c?b:[b,c];return a.format('<feGaussianBlur stdDeviation="{def}"/>',{def:d})},a.filter.blur.toString=function(){return this()},a.filter.shadow=function(b,c,d,e,f){return"string"==typeof d&&(e=d,f=e,d=4),"string"!=typeof e&&(f=e,e="#000"),e=e||"#000",null==d&&(d=4),null==f&&(f=1),null==b&&(b=0,c=2),null==c&&(c=b),e=a.color(e),a.format('<feGaussianBlur in="SourceAlpha" stdDeviation="{blur}"/><feOffset dx="{dx}" dy="{dy}" result="offsetblur"/><feFlood flood-color="{color}"/><feComposite in2="offsetblur" operator="in"/><feComponentTransfer><feFuncA type="linear" slope="{opacity}"/></feComponentTransfer><feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge>',{color:e,dx:b,dy:c,blur:d,opacity:f})},a.filter.shadow.toString=function(){return this()},a.filter.grayscale=function(b){return null==b&&(b=1),a.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {b} {h} 0 0 0 0 0 1 0"/>',{a:.2126+.7874*(1-b),b:.7152-.7152*(1-b),c:.0722-.0722*(1-b),d:.2126-.2126*(1-b),e:.7152+.2848*(1-b),f:.0722-.0722*(1-b),g:.2126-.2126*(1-b),h:.0722+.9278*(1-b)})},a.filter.grayscale.toString=function(){return this()},a.filter.sepia=function(b){return null==b&&(b=1),a.format('<feColorMatrix type="matrix" values="{a} {b} {c} 0 0 {d} {e} {f} 0 0 {g} {h} {i} 0 0 0 0 0 1 0"/>',{a:.393+.607*(1-b),b:.769-.769*(1-b),c:.189-.189*(1-b),d:.349-.349*(1-b),e:.686+.314*(1-b),f:.168-.168*(1-b),g:.272-.272*(1-b),h:.534-.534*(1-b),i:.131+.869*(1-b)})},a.filter.sepia.toString=function(){return this()},a.filter.saturate=function(b){return null==b&&(b=1),a.format('<feColorMatrix type="saturate" values="{amount}"/>',{amount:1-b})},a.filter.saturate.toString=function(){return this()},a.filter.hueRotate=function(b){return b=b||0,a.format('<feColorMatrix type="hueRotate" values="{angle}"/>',{angle:b})},a.filter.hueRotate.toString=function(){return this()},a.filter.invert=function(b){return null==b&&(b=1),a.format('<feComponentTransfer><feFuncR type="table" tableValues="{amount} {amount2}"/><feFuncG type="table" tableValues="{amount} {amount2}"/><feFuncB type="table" tableValues="{amount} {amount2}"/></feComponentTransfer>',{amount:b,amount2:1-b})},a.filter.invert.toString=function(){return this()},a.filter.brightness=function(b){return null==b&&(b=1),a.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}"/><feFuncG type="linear" slope="{amount}"/><feFuncB type="linear" slope="{amount}"/></feComponentTransfer>',{amount:b})},a.filter.brightness.toString=function(){return this()},a.filter.contrast=function(b){return null==b&&(b=1),a.format('<feComponentTransfer><feFuncR type="linear" slope="{amount}" intercept="{amount2}"/><feFuncG type="linear" slope="{amount}" intercept="{amount2}"/><feFuncB type="linear" slope="{amount}" intercept="{amount2}"/></feComponentTransfer>',{amount:b,amount2:.5-b/2})},a.filter.contrast.toString=function(){return this()}}),d.plugin(function(a,b){var c=a._.box,d=a.is,e=/^[^a-z]*([tbmlrc])/i,f=function(){return"T"+this.dx+","+this.dy};b.prototype.getAlign=function(a,b){null==b&&d(a,"string")&&(b=a,a=null),a=a||this.paper;var g=a.getBBox?a.getBBox():c(a),h=this.getBBox(),i={};switch(b=b&&b.match(e),b=b?b[1].toLowerCase():"c"){case"t":i.dx=0,i.dy=g.y-h.y;break;case"b":i.dx=0,i.dy=g.y2-h.y2;break;case"m":i.dx=0,i.dy=g.cy-h.cy;break;case"l":i.dx=g.x-h.x,i.dy=0;break;case"r":i.dx=g.x2-h.x2,i.dy=0;break;default:i.dx=g.cx-h.cx,i.dy=0}return i.toString=f,i},b.prototype.align=function(a,b){return this.transform("..."+this.getAlign(a,b))}}),d});

var SVGAnim=function(root){function updateMaskContent(a,b){var c=b.maskElement,d=a.getChildById(b.id);c.clear(),clone=d.el.clone(),clone.attr({visibility:"visible"}),c.append(clone)}function SVGAnim(a,b,c,d,e){function f(b){var c,e,f;for(void 0!==n.rootAnimator&&n.rootAnimator.dispose(),n.linkage={},f=a.DOMDocument.Timeline.length-1;f>-1;f-=1){if("undefined"==typeof a.DOMDocument.Timeline[f].linkageName){c=f;break}n.linkage[a.DOMDocument.Timeline[f].linkageName]=a.DOMDocument.Timeline[f]}e=n.resourceManager.m_data.DOMDocument.Timeline[c],n.mc=new MovieClip(e,n.s,n.resourceManager,id),l=setTimeout(g,1e3/d)}function g(){n.mc._animate(),clearTimeout(l),j&&(l=setTimeout(g,1e3/d))}function h(a){switch(a.keyCode){case 39:g();break;case 32:n.mc.playing?n.stop():n.play()}}function i(){function a(b,d){var e,f;for(f=0;f<d.children.length;f+=1){for(e=0;b>e;e+=1)c+="-";c+=d.children[f].id+":"+d.children[f].children.length,d.children[f].isMask&&(c+=" (MASK till:"+d.children[f].maskTill+")"),d.children[f].isMasked&&(c+=" (masked by: "+d.children[f].mask+")"),c+="<br/>",a(b+5,d.children[f])}}var b=document.getElementById("debug"),c="";b||(b=document.createElement("div"),b.id="debug",b.style.position="absolute",b.style.top="0",b.style.right="0",b.style.backgroundColor="black",b.style.color="white",b.style.padding="1em",document.body.appendChild(b)),c+=n.mc.id+"<br/>",c+=n.mc.m_currentFrameNo+"<br/>",a(2,n.mc),b.innerHTML=c}var j,k,l,m,n=this,o="#008460";n.version="1.2.1",m="Snap.svg Animator v"+n.version,console.log("%c"+m,"color:"+o+";font-weight:bold;"),e=e||{},d=d||24,b=b||100,c=c||100,k=e.autoplay||!0,j=k,n.debug=!1,SVGAnim.prototype.toString=function(){return m},n.MovieClip=MovieClip,n.resourceManager=new ResourceManager(a),n.s=new Snap(b,c),id=n.s.id,n.s.attr("id",id),n.s.attr("viewBox","0 0 "+b+" "+c),n.s.attr("preserveAspectRatio","xMidYMid meet"),f(n.s),n.debug&&(j=!1,window.addEventListener("keydown",h)),this.play=function(){n.mc.play(),j=!0},this.stop=function(){n.mc.stop(),j=!1},this.setLoop=function(a){n.mc.loops=a},n.debug&&setInterval(i,100),k?n.play():g()}SVGAnim.version="0.0.2";var GarbagePool=function(){this.EMPTY_POOL=[],this.REF_POOL=[]};GarbagePool.prototype.addEmpty=function(a){this.EMPTY_POOL.push(a)},GarbagePool.prototype.addRef=function(a,b){var c,d;for(c=0;c<this.REF_POOL.length;c+=1)if(this.REF_POOL[c].el.id==a.id){for(d=0;d<b.length;d+=1)this.REF_POOL[c].refs.push(b[d]);return}this.REF_POOL.push({el:a,refs:b})},GarbagePool.prototype.purge=function(){this.purgeEmptyPool(),this.purgeRefPool()},GarbagePool.prototype.purgeEmptyPool=function(){var a,b;for(a=this.EMPTY_POOL.length-1;a>-1;a-=1)b=this.EMPTY_POOL[a],0===b.children().length&&(b.remove(),this.EMPTY_POOL.splice(a,1))},GarbagePool.prototype.purgeRefPool=function(){var a,b,c,d;for(a=this.REF_POOL.length-1;a>-1;a-=1)for(d=this.REF_POOL[a],c=0,b=0;b<d.refs.length;b+=1)d.refs[b].removed&&(c+=1),c==d.refs.length&&(d.el.remove(),this.REF_POOL.splice(a,1))};var GP=new GarbagePool,Bitmap=function(a,b,c,d,e,f){var g=this,h=a.el;this.create=function(){var i,j;g.el=a.el.g(),g.id=d,g.el.attr({"class":"shape",token:g.id}),g.children=[],g.isMask=!1,g.isMasked=!1,g.mask=null,g.maskTill=null;for(var k=0;k<b.m_data.DOMDocument.Bitmaps.length;k++)if(b.m_data.DOMDocument.Bitmaps[k].charid==c){var l=b.m_data.DOMDocument.Bitmaps[k].bitmapPath,m=a.el.paper.image(l);g.el.add(m)}i=f.split(","),j=new Snap.Matrix(i[0],i[1],i[2],i[3],i[4],i[5]),g.el.transform(j),e&&0!==parseInt(e)?(afterMC=a.getChildById(parseInt(e)),afterMC.isMasked?afterMC.el.parent().before(g.el):afterMC.el.before(g.el)):h.add(g.el)},this.create()},Text=function(a,b,c,d,e,f,g){var h=this,i=a.el;this.create=function(){var g,j,k;for(h.el=a.el.g(),h.id=d,h.el.attr({"class":"text",token:h.id}),h.children=[],h.isMask=!1,h.isMasked=!1,h.mask=null,h.maskTill=null,g=0;g<b.m_data.DOMDocument.Text.length;g++)b.m_data.DOMDocument.Text[g].charid==c&&h.addText(b.m_data.DOMDocument.Text[g]);j=f.split(","),k=new Snap.Matrix(j[0],j[1],j[2],j[3],j[4],j[5]),h.el.transform(k),e&&0!==parseInt(e)?(afterMC=a.getChildById(parseInt(e)),afterMC.isMasked?afterMC.el.parent().before(h.el):afterMC.el.before(h.el)):i.add(h.el)},this.addText=function(a){var b,c,d,e,f,i,j,k,l,m,n,o,p;c=h.el.g(),o=g?g.split(","):[0,0,200,100],f=a.behaviour.lineMode,l=a.paras[0].alignment,n="single"==f?"central":"auto",i=a.paras[0].textRun[0].style.fontSize,j=a.paras[0].textRun[0].style.fontName,k=a.paras[0].textRun[0].style.fontColor,letterSpacing=a.paras[0].textRun[0].style.letterSpacing,"left"==l?m="start":"center"==l?m="middle":"right"==l&&(m="end"),p={"text-anchor":m,"dominant-baseline":n,"font-family":j,"font-size":i,"letter-spacing":letterSpacing,fill:k},"false"!==a.behaviour.isBorderDrawn&&(textRect=c.rect(o[0],o[1],o[2],o[3]),textRect.attr({stroke:"black",fill:"transparent"})),"single"==f?(b=c.text(0,0,a.txt),e=parseFloat(o[1])+parseFloat(o[3])/2):(b=h.multiLine(c,a,o,p),e=parseFloat(o[1])-2*parseFloat(a.paras[0].linespacing)),d="left"==l?parseFloat(o[0]):parseFloat(o[0])+parseFloat(o[2])/2,b.attr(p),b.transform("translate("+d+","+e+")")},this.multiLine=function(a,b,c,d){for(var e,f,g,h,i=(b.txt,[]),j="",k=parseFloat(c[2]),l=0;l>-1;)j+=b.txt.charAt(l),f=a.text(0,0,j),f.attr(d),h=f.getBBox(),h.w>k?(newIndex=j.lastIndexOf(" "),e=j.slice(0,newIndex),i.push(e),l=l-(j.length-e.length)+2,j=""):l+=1,l>=b.txt.length&&(e=j.slice(0,newIndex),i.push(e),l=-1),f.remove();return text=a.text(0,0,i),g=text.selectAll("tspan"),g.attr({x:0,dy:h.h+parseFloat(b.paras[0].linespacing)}),text},this.create()},Shape=function(a,b,c,d,e,f){function g(a){var b=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a);return b?{r:parseInt(b[1],16),g:parseInt(b[2],16),b:parseInt(b[3],16)}:null}function h(a,b,c){var d,e;return d=i.el.image(a),e=d.pattern(0,0,b.width,b.height),e.attr({x:c.e,y:c.f}),e}var i=this,j=a.el;this.create=function(){var g,h,k,l;for(i.el=a.el.g(),i.id=d,i.el.attr({"class":"shape",token:i.id}),i.children=[],i.isMask=!1,i.isMasked=!1,i.mask=null,i.maskTill=null,g=0;g<b.m_data.DOMDocument.Shape.length;g++)if(b.m_data.DOMDocument.Shape[g].charid==c)for(h=0;h<b.m_data.DOMDocument.Shape[g].path.length;h++)i.addPath(g,h);k=f.split(","),l=new Snap.Matrix(k[0],k[1],k[2],k[3],k[4],k[5]),i.el.transform(l),e&&0!==parseInt(e)?(afterMC=a.getChildById(parseInt(e)),afterMC.isMasked?afterMC.el.parent().before(i.el):afterMC.el.before(i.el)):j.add(i.el)},this.addPath=function(a,c){var d;shape=i.el.path(),resourcePath=b.m_data.DOMDocument.Shape[a].path[c],d=resourcePath.d,shape.attr({fill:"transparent"}),shape.attr({d:d}),"Fill"==resourcePath.pathType?this.addFill(shape,resourcePath):"Stroke"==resourcePath.pathType&&this.addStroke(shape,resourcePath)},this.getFillColor=function(a){var b,c,d,e,f,g;return b=resourcePath.color,c=parseInt(b.substring(1,3),16),d=parseInt(b.substring(3,5),16),e=parseInt(b.substring(5,7),16),f=resourcePath.colorOpacity,g="rgba("+c+","+d+","+e+","+f+")"},this.getFillImage=function(b){var c,d,e,f,g=0;return c=b.patternTransform.split(","),g=0,d=new Snap.Matrix(c[g],c[g+1],c[g+1],c[g+3],c[g+4],c[g+5]),e=b.bitmapPath,f=a.el.paper.select("defs pattern image"),f&&f.attr("href")==e?fillImage=f.parent():fillImage=h(e,b,d),fillImage},this.getFillGradient=function(b,c,d){var e,f,h,i,j,k,l,m,n,o;for("linear"==c?(e=parseFloat(b.x1),f=parseFloat(b.y1),h=parseFloat(b.x2),i=parseFloat(b.y2),l="L(",l+=e+", ",l+=f+", ",l+=h+", ",l+=i+")"):(e=d.getBBox().x+d.getBBox().width/2+b.cx/10,f=d.getBBox().y+d.getBBox().height/2+b.cy/10,j=d.getBBox().x+d.getBBox().width/2+parseFloat(b.fx),k=d.getBBox().y+d.getBBox().height/2+parseFloat(b.fy),l="R(",l+=e+", ",l+=f+", ",l+=b.r+", ",l+=j+", ",l+=k+")"),n=0;n<b.stop.length;n+=1)o=g(b.stop[n].stopColor),l+="rgba("+o.r+","+o.g+","+o.b+","+b.stop[n].stopOpacity+")",l+=":",l+=b.stop[n].offset,n!==b.stop.length-1&&(l+="-");return m=a.el.paper.gradient(l)},this.addFill=function(a,b){var c,d,e,f,g;b.color&&(c=i.getFillColor(b),a.attr({fill:c})),b.image&&(f=b.image,d=i.getFillImage(f),a.attr({fill:d})),b.linearGradient&&(g=b.linearGradient,e=i.getFillGradient(g,"linear"),a.attr({fill:e})),b.radialGradient&&(g=b.radialGradient,e=i.getFillGradient(g,"radial",a),a.attr({fill:e}))},this.addStroke=function(a,b){var c,d,e,f,g;b.color&&(clr=b.color,c=parseInt(clr.substring(1,3),16),d=parseInt(clr.substring(3,5),16),e=parseInt(clr.substring(5,7),16),f=b.colorOpacity,g="rgba("+c+","+d+","+e+","+f+")",a.attr({stroke:g,strokeWidth:b.strokeWidth}))},this.create()},MovieClip=function(a,b,c,d,e,f){var g,h;parentEl="svg"==b.type?b:b.el,d&&(this.id=d),e&&(this.name=e),this.el=parentEl.g(),this.el.attr({"class":"movieclip",token:this.id}),this.transform=f,this.m_timeline=a,this.m_currentFrameNo=0,this.m_frameCount=this.m_timeline.frameCount,this._scripts={},this._labels=[],this.children=[],this.isMask=!1,this.isMasked=!1,this.mask=null,this.maskElement=null,this.maskTill=null,this.loops=!0,this.playing=!0,this.resourceManager=c,this.commandList=[],this.matrix=new Snap.Matrix,"undefined"!=typeof this.m_timeline.Label&&(this._labels=this.m_timeline.Label),void 0!==this.transform&&(g=this.transform,h=g.split(","),this.matrix=new Snap.Matrix(h[0],h[1],h[2],h[3],h[4],h[5]),this.el.transform(this.matrix))};MovieClip.prototype.addChild=function(a,b){b=b?b:0,this.insertAtIndex(a,b)},MovieClip.prototype._addChild=function(a,b){if(a.name&&(this[a.name]=a),b&&0!==parseInt(b)){var c=this.getChildById(parseInt(b));c.isMasked?c.el.parent().before(a.el):c.el.before(a.el)}else this.el.add(a.el)},MovieClip.prototype.getChildById=function(a){var b;for(b=0;b<this.children.length;b+=1)if(this.children[b].id==a)return this.children[b];return!1},MovieClip.prototype.getChildIndexById=function(a){var b;for(b=0;b<this.children.length;b+=1)if(this.children[b].id==a)return b;return!1},MovieClip.prototype.removeChildById=function(a){var b;for(b=0;b<this.children.length;b+=1)if(this.children[b].id==a)return void this.children.splice(b,1)},MovieClip.prototype.swapChildIndex=function(a,b){var c,d;for(c=0;c<this.children.length;c+=1)if(this.children[c].id==a){d=this.children.splice(c,1);break}for(c=0;c<this.children.length;c+=1)if(this.children[c].id==b){this.children.splice(c+1,0,d[0]);break}},MovieClip.prototype.insertAtIndex=function(a,b){var c;for(this._addChild(a,b),0===parseInt(b)&&this.children.unshift(a),c=0;c<this.children.length;c+=1)if(this.children[c].id==b){this.children.splice(c+1,0,a);break}},MovieClip.prototype.containsMask=function(){var a;for(a=0;a<this.children.length;a+=1)if(this.children[a].isMask)return!0;return!1},MovieClip.prototype.getFrameLabels=function(){return this._labels},MovieClip.prototype.getMatrix=function(){return this.matrix?this.matrix:new Snap.Matrix},MovieClip.prototype.getX=function(){var a=0;return this.matrix&&(a=this.matrix.x()),a},MovieClip.prototype.getY=function(){var a=0;return this.matrix&&(a=this.matrix.y()),a},MovieClip.prototype.mouseover=function(a){this.el.mouseover(a)},MovieClip.prototype.mouseout=function(a){this.el.mouseout(a)},MovieClip.prototype.mousedown=function(a){this.el.mousedown(a)},MovieClip.prototype.mousemove=function(a){this.el.mousemove(a)},MovieClip.prototype.click=function(a){this.el.click(a)},MovieClip.prototype.executeFrameScript=function(script){eval("(function () {"+script+"}).call(this);")},MovieClip.prototype.removeFrameScript=function(a){delete this._scripts[a]},MovieClip.prototype.addFrameScript=function(a,b){this._scripts[a]=b},MovieClip.prototype.getFrame=function(a){var b;for(b=0;b<this.m_timeline.Frame.length;b+=1)if(this.m_timeline.Frame[b].num==a)return this.m_timeline.Frame[b]},MovieClip.prototype._checkLoop=function(){if(this.m_currentFrameNo==this.m_frameCount){if(!this.loops)return;this._loop()}},MovieClip.prototype._loop=function(){var a,b,d,e,f;if(this.m_currentFrameNo=0,a=this.getFrame(this.m_currentFrameNo),!a)return void this.clearChildren();for(b=a.Command,d=0;d<this.children.length;d+=1){for(e=!1,child=this.children[d],c=0;c<b.length;++c)if(cmdData=b[c],f=cmdData.cmdType,"Place"==f&&parseInt(child.id)==parseInt(cmdData.objectId)){e=!0;break}e===!1&&(command=new CMD.RemoveObjectCommand(child.id),this.commandList.push(command))}},MovieClip.prototype.clearChildren=function(){var a,b,c;for(a=0;a<this.children.length;a+=1)b=this.children[a],c=new CMD.RemoveObjectCommand(b.id),this.commandList.push(c)},MovieClip.prototype._animate=function(){var a;for(this.step_1_animTimeline(),this.step_2_enterFrame(),this.step_4_frameConstructed(),this.step_5_frameScripts(),this.step_6_exitFrame(),a=0;a<this.children.length;a+=1)this.children[a]._animate&&this.children[a]._animate();GP.purge()},MovieClip.prototype._runCommands=function(a){var b,c,d,e,f;for(b=0;b<a.length;b+=1)switch(c=a[b],e=c.cmdType,d=null,e){case"Place":f=this.getChildById(c.objectId),f?(d=new CMD.MoveObjectCommand(c.objectId,c.transformMatrix),this.commandList.push(d),d=new CMD.UpdateObjectCommand(c.objectId,c.placeAfter),this.commandList.push(d)):(d=new CMD.PlaceObjectCommand(c.charid,c.objectId,c.name,c.placeAfter,c.transformMatrix,c.bounds),this.commandList.push(d));break;case"Move":d=new CMD.MoveObjectCommand(c.objectId,c.transformMatrix),this.commandList.push(d);break;case"Remove":d=new CMD.RemoveObjectCommand(c.objectId),this.commandList.push(d);break;case"UpdateZOrder":d=new CMD.UpdateObjectCommand(c.objectId,c.placeAfter),this.commandList.push(d);break;case"UpdateVisibility":d=new CMD.UpdateVisibilityCommand(c.objectId,c.visibility),this.commandList.push(d);break;case"UpdateColorTransform":d=new CMD.UpdateColorTransformCommand(c.objectId,c.colorMatrix),this.commandList.push(d);break;case"UpdateBlendMode":break;case"UpdateMask":d=new CMD.UpdateMaskCommand(c.objectId,c.maskTill),this.commandList.push(d);break;case"AddFrameScript":d=new CMD.AddFrameScriptCommand(c.scriptId,c.script),this.commandList.push(d);break;case"RemoveFrameScript":d=new CMD.RemoveFrameScriptCommand(c.scriptId),this.commandList.push(d);break;case"SetFrameLabel":d=new CMD.SetFrameLabelCommand(c.Name),this.commandList.push(d)}this.containsMask&&(d=new CMD.ApplyMaskCommand,this.commandList.push(d)),this.executeCommands(this.commandList,this.resourceManager)},MovieClip.prototype.step_1_animTimeline=function(a,b){"undefined"==typeof a&&(a=!1),"undefined"==typeof b&&(b=!1);var c,d;this.playing&&(this.commandList=[],this._checkLoop(),d=this.getFrame(this.m_currentFrameNo),this.m_currentFrameNo++,d&&(c=d.Command,this._runCommands(c)))},MovieClip.prototype.step_2_enterFrame=function(){},MovieClip.prototype.step_3_addPending=function(){},MovieClip.prototype.step_4_frameConstructed=function(){},MovieClip.prototype.step_5_frameScripts=function(){for(var a in this._scripts)this.executeFrameScript(this._scripts[a])},MovieClip.prototype.step_6_exitFrame=function(){},MovieClip.prototype.play=function(){this.playing=!0},MovieClip.prototype.stop=function(){this.playing=!1},MovieClip.prototype.gotoAndStop=function(a){this._gotoAndPlayStop(a,!0)},MovieClip.prototype.gotoAndPlay=function(a){this._gotoAndPlayStop(a,!1)},MovieClip.prototype._gotoAndPlayStop=function(a,b){if("string"==typeof a){for(var c=this.getFrameLabels(),d=!1,e=c.length-1;e>=0;e--)if(a===c[e].name){a=parseInt(c[e].frameNum)+1,d=!0;break}if(d===!1)return}if(!(1>a||a>this.m_frameCount)){if(a==this.m_currentFrameNo)return void(b===!1?this.play():this.stop());if(this.play(),a<this.m_currentFrameNo){var f=1==a;this._loopAround(!0,f)}for(;this.m_currentFrameNo<a;){var f=a==this.m_currentFrameNo;this.step_1_animTimeline(!0,f);for(var e=0;e<this.children.length;e+=1)this.children[e].step_1_animTimeline&&this.children[e].step_1_animTimeline(!0,f)}b===!1?this.play():this.stop(),this.step_4_frameConstructed(),this.step_5_frameScripts(),this.step_6_exitFrame()}},MovieClip.prototype._loopAround=function(a,b){"undefined"==typeof a&&(a=!1),"undefined"==typeof b&&(b=!1),this.commandList=[],this._checkLoop(),this.m_currentFrameNo=0,frame=this.getFrame(this.m_currentFrameNo),frame&&(commands=frame.Command,this._runCommands(commands))},MovieClip.prototype.executeCommands=function(a,b){var c;for(c=0;c<a.length;c++)void 0!==a[c]&&a[c].execute(this,b)},MovieClip.prototype.log=function(){if(this.id.indexOf("svg")>-1){var a=Array.prototype.slice.call(arguments);a.unshift(this.id.toUpperCase())}};var CMD={};CMD.PlaceObjectCommand=function(a,b,c,d,e,f){this.m_charID=a,this.m_objectID=b,this.m_name=c,this.m_placeAfter=d,this.m_transform=e,this.m_bounds=f},CMD.PlaceObjectCommand.prototype.execute=function(a,b){var c,d,e,f,g,h=b.getShape(this.m_charID),i=b.getBitmap(this.m_charID),j=b.getText(this.m_charID);null!==h&&void 0!==h?(d=new Shape(a,b,this.m_charID,this.m_objectID,this.m_placeAfter,this.m_transform),a.insertAtIndex(d,this.m_placeAfter)):null!==i&&void 0!==i?(e=new Bitmap(a,b,this.m_charID,this.m_objectID,this.m_placeAfter,this.m_transform),a.insertAtIndex(e,this.m_placeAfter)):null!==j&&void 0!==j?(c=new Text(a,b,this.m_charID,this.m_objectID,this.m_placeAfter,this.m_transform,this.m_bounds),a.insertAtIndex(c,this.m_placeAfter)):(f=b.getMovieClip(this.m_charID),f&&(g=new MovieClip(f,a,b,this.m_objectID,this.m_name,this.m_transform),a.insertAtIndex(g,this.m_placeAfter),g.play()))},CMD.MoveObjectCommand=function(a,b){this.m_objectID=a,this.m_transform=b},CMD.MoveObjectCommand.prototype.execute=function(a,b){var c,d,e;c=this.m_transform,d=c.split(","),e=new Snap.Matrix(d[0],d[1],d[2],d[3],d[4],d[5]),child=a.getChildById(this.m_objectID),child.matrix=e,child.el.transform(e)},CMD.UpdateObjectCommand=function(a,b){this.m_objectID=a,this.m_placeAfter=b},CMD.UpdateObjectCommand.prototype.execute=function(a,b){},CMD.RemoveObjectCommand=function(a){this.m_objectID=a},CMD.RemoveObjectCommand.prototype.execute=function(a,b){var c;c=a.getChildById(this.m_objectID),c.el.remove(),a.removeChildById(this.m_objectID)},CMD.UpdateVisibilityCommand=function(a,b){this.m_objectID=a,this.m_visibility=b},CMD.UpdateVisibilityCommand.prototype.execute=function(a,b){var c,d;c=a.getChildById(this.m_objectID),d="true"==this.m_visibility?"visible":"hidden",c.el.attr({visibility:d})},CMD.UpdateMaskCommand=function(a,b){this.m_objectID=a,this.m_maskTill=b},CMD.UpdateMaskCommand.prototype.execute=function(a,b){var c,d;maskContent=a.getChildById(this.m_objectID),maskContent.isMask=!0,maskContent.maskTill=this.m_maskTill,c=a.el.mask(),c.attr("mask-type","alpha"),clone=maskContent.el.clone(),clone.attr({visibility:"visible"}),d=c.toDefs(),d.append(clone),maskContent.maskElement=d,maskContent.el.attr({visibility:"hidden"})},CMD.ApplyMaskCommand=function(){},CMD.ApplyMaskCommand.prototype.execute=function(a,b){var c,d,e=!1,f=null,g=null,h=null;for(c=0;c<a.children.length;c+=1)child=a.children[c],child.isMask?(updateMaskContent(a,child),e=!0,f=child,g=child.maskElement,h=child.maskTill,d=a.el.g(),d.attr({"class":"maskGroup"}),child.el.after(d),d.attr({mask:g}),GP.addEmpty(d),GP.addRef(g,[d]),child.id==child.maskTill&&(e=!1)):e&&(d.prepend(child.el),child.isMasked=!0,child.mask=f.id,child.id==h&&(e=!1,f=null,h=null))},CMD.UpdateColorTransformCommand=function(a,b){this.m_objectID=a,this.m_colorMatrix=b},CMD.UpdateColorTransformCommand.prototype.execute=function(a,b){var c,d;c=a.getChildById(this.m_objectID),d=this.m_colorMatrix.split(",",7),c.el.attr({opacity:parseFloat(d[6])})},CMD.AddFrameScriptCommand=function(a,b){this.m_scriptID=a,this.m_script=b},CMD.AddFrameScriptCommand.prototype.execute=function(a,b){a.addFrameScript(this.m_scriptID,this.m_script)},CMD.RemoveFrameScriptCommand=function(a){this.m_scriptID=a},CMD.RemoveFrameScriptCommand.prototype.execute=function(a,b){a.removeFrameScript(this.m_scriptID)},CMD.SetFrameLabelCommand=function(a){this.m_labelName=a},CMD.SetFrameLabelCommand.prototype.execute=function(a,b){};var ResourceManager=function(a){var b;this.m_shapes=[],this.m_movieClips=[],this.m_bitmaps=[],this.m_text=[],this.m_data=a;for(var c=0;c<this.m_data.DOMDocument.Shape.length;c++){b=this.m_data.DOMDocument.Shape[c].charid;var d=this.m_data.DOMDocument.Shape[c];this.m_shapes[b]=d}for(var e=0;e<this.m_data.DOMDocument.Bitmaps.length;e++){b=this.m_data.DOMDocument.Bitmaps[e].charid;var f=this.m_data.DOMDocument.Bitmaps[e];this.m_bitmaps[b]=f}for(var g=0;g<this.m_data.DOMDocument.Text.length;g++){b=this.m_data.DOMDocument.Text[g].charid;var h=this.m_data.DOMDocument.Text[g];this.m_text[b]=h}if(void 0!==this.m_data.DOMDocument.Timeline)for(var i=0;i<this.m_data.DOMDocument.Timeline.length-1;i++){b=this.m_data.DOMDocument.Timeline[i].charid;var j=this.m_data.DOMDocument.Timeline[i];this.m_movieClips[b]=j}console.log(this.m_data.DOMDocument)};return ResourceManager.prototype.getShape=function(a){return this.m_shapes[a]},ResourceManager.prototype.getMovieClip=function(a){return this.m_movieClips[a]},ResourceManager.prototype.getBitmap=function(a){return this.m_bitmaps[a]},ResourceManager.prototype.getText=function(a){return this.m_text[a]},window.SVGAnim=SVGAnim,SVGAnim}(window||this);

var waterTimerAnimationJson = {"DOMDocument":{"Shape":[{"charid":"1","path":[{"color":"#ffffff","colorOpacity":"1","d":"M 42.35 45.9q2.55 -2 3.45 -4.6q0.85 -2.6 -0.5 -4.3q-0.4 -0.7 -1.3 -0.8q-0.75 0 -2.2 1.35q-2.45 2.35 -3.1 2.85q-1.7 1.35 -4.8 3.25q-2 1.55 -0.9 2.95q1.35 1.65 3.55 1.5q3.2 -0.05 5.8 -2.2","pathType":"Fill","stroke":"none"}]},{"charid":"2","path":[{"color":"#ffffff","colorOpacity":"1","d":"M 26.6 15.55q3.4 -1.5 1.95 -3.1q-0.95 -1.05 -2.7 -1.65q-1.8 -0.6 -3.95 -0.6q-3.55 0 -6.55 1.65q-1.1 0.65 -1.95 1.4q-3.2 2.9 -3.45 7.6q-0.2 4.3 1.9 6.65q0.4 0.55 1.15 0.7q0.75 0 2.3 -3q1.9 -3.75 3.95 -5.55q1.8 -1.6 7.35 -4.1","pathType":"Fill","stroke":"none"}]},{"charid":"3","path":[{"radialGradient":{"cx":"0","cy":"0","r":"35.175","fx":"0","fy":"0","gradientTransform":"1,0,0,1,28.75,28.75","spreadMethod":"pad","stop":[{"offset":"0","stopColor":"#4cb7ff","stopOpacity":"1"},{"offset":"74.118","stopColor":"#96d3fd","stopOpacity":"1"},{"offset":"78.039","stopColor":"#dff0fb","stopOpacity":"1"}]},"d":"M 49.05 49.1q8.45 -8.45 8.45 -20.35q0 -11.9 -8.45 -20.3q-8.4 -8.45 -20.3 -8.45q-11.9 0 -20.35 8.45q-8.4 8.4 -8.4 20.3q0 11.95 8.4 20.35q8.45 8.4 20.35 8.4q11.9 0 20.3 -8.4","pathType":"Fill","stroke":"none"}]},{"charid":"5","path":[{"linearGradient":{"x1":"245","y1":"62.125","x2":"245","y2":"720.075","spreadMethod":"pad","stop":[{"offset":"3.922","stopColor":"#00e8ff","stopOpacity":"1"},{"offset":"46.275","stopColor":"#00abff","stopOpacity":"1"},{"offset":"93.725","stopColor":"#006eff","stopOpacity":"1"}]},"d":"M 435.05 49.65q-16 9.55 -28.05 13.7q-18 6.25 -39.5 6.25q-12.45 0 -29 -6.25q-9.2 -3.5 -30.35 -13.75q-20.2 -9.8 -31.35 -13.75q-17.6 -6.25 -31.8 -6.25q-14.2 0 -31.8 6.25q-11.15 3.95 -31.3 13.75q-21.2 10.25 -30.4 13.75q-16.55 6.25 -29 6.25q-20.9 0 -38.95 -6.25q-12.1 -4.2 -28.5 -13.7q-18.65 -10.85 -25.45 -13.75q-14.6 -6.25 -29.6 -6.25l0 1028.35l490 0l0 -1028.35q-15.55 0 -30.05 6.25q-6.85 2.95 -24.9 13.75","pathType":"Fill","stroke":"none"}]}],"Bitmaps":[],"Sounds":[],"Text":[],"Timeline":[{"charid":"4","frameCount":"1","name":"Bubble","Frame":[{"num":"0","Command":[{"cmdType":"Place","charid":"1","objectId":"1","placeAfter":"0","transformMatrix":"1,0,0,1,-28.75,-28.75"},{"cmdType":"Place","charid":"2","objectId":"2","placeAfter":"1","transformMatrix":"1,0,0,1,-28.75,-28.75"},{"cmdType":"Place","charid":"3","objectId":"3","placeAfter":"2","transformMatrix":"1,0,0,1,-28.75,-28.75"}]}]},{"charid":"6","frameCount":"1","name":"Background","Frame":[{"num":"0","Command":[{"cmdType":"Place","charid":"5","objectId":"1","placeAfter":"0","transformMatrix":"1,0,0,1,-245,-543.8"}]}]},{"frameCount":"74","Frame":[{"num":"0","Command":[{"cmdType":"Place","charid":"4","objectId":"1","placeAfter":"0","transformMatrix":"1,0,0,1,28.75,1103"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.75,0,"},{"cmdType":"UpdateVisibility","objectId":"1","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"1","blendMode":"Normal"},{"cmdType":"Place","charid":"4","objectId":"2","placeAfter":"1","transformMatrix":"0.609,0,0,0.609,17.5,1075.5"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"UpdateVisibility","objectId":"2","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"2","blendMode":"Normal"},{"cmdType":"Place","charid":"6","objectId":"3","placeAfter":"2","transformMatrix":"1,0,0,1,0,543.8","name":"Background"},{"cmdType":"UpdateColorTransform","objectId":"3","colorMatrix":"1,0,1,0,1,0,1,0,"},{"cmdType":"UpdateVisibility","objectId":"3","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"3","blendMode":"Normal"}]},{"num":"1","Command":[{"cmdType":"Move","objectId":"1","transformMatrix":"0.999,0,0,0.999,32.069,1089.372"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,24.8,1063.734"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,3.35,543.8"}]},{"num":"2","Command":[{"cmdType":"Move","objectId":"1","transformMatrix":"0.998,0,0,0.998,35.089,1075.461"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,30.75,1050.984"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,6.7,543.8"}]},{"num":"3","Command":[{"cmdType":"Move","objectId":"1","transformMatrix":"0.997,0,0,0.997,37.708,1061.232"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,35.5,1037.334"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,10.05,543.8"}]},{"num":"4","Command":[{"cmdType":"Move","objectId":"1","transformMatrix":"0.996,0,0,0.996,39.978,1046.771"},{"cmdType":"Place","charid":"4","objectId":"4","placeAfter":"1","transformMatrix":"0.522,0,0,0.522,160.45,1081.45"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.35,0,"},{"cmdType":"UpdateVisibility","objectId":"4","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"4","blendMode":"Normal"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"UpdateZOrder","objectId":"2","placeAfter":"4"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,39.1,1022.884"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,13.4,543.8"}]},{"num":"5","Command":[{"cmdType":"Move","objectId":"1","transformMatrix":"0.995,0,0,0.995,41.997,1032.043"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,156.698,1068.883"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,41.7,1007.734"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,16.75,543.8"}]},{"num":"6","Command":[{"cmdType":"Move","objectId":"1","transformMatrix":"0.994,0,0,0.994,43.667,1017.082"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,154.148,1055.633"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,43.4,991.834"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,20.1,543.8"}]},{"num":"7","Command":[{"cmdType":"Place","charid":"4","objectId":"5","placeAfter":"0","transformMatrix":"1,0,0,1,217.65,1102.25"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.75,0,"},{"cmdType":"UpdateVisibility","objectId":"5","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"5","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"1","placeAfter":"5"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.993,0,0,0.993,45.036,1001.854"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.37,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,152.748,1041.783"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,44.25,975.384"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,23.45,543.8"}]},{"num":"8","Command":[{"cmdType":"Move","objectId":"5","transformMatrix":"0.999,0,0,0.999,216.611,1088.789"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.992,0,0,0.992,46.156,986.492"},{"cmdType":"Place","charid":"4","objectId":"6","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,95.05,974.2"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"UpdateVisibility","objectId":"6","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"6","blendMode":"Normal"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"UpdateZOrder","objectId":"4","placeAfter":"6"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,152.448,1027.333"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.44,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,44.4,958.384"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,26.8,543.8"}]},{"num":"9","Command":[{"cmdType":"Move","objectId":"5","transformMatrix":"0.998,0,0,0.998,215.675,1074.995"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.99,0,0,0.99,47.025,970.864"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,102.446,940.488"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,153.098,1012.333"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,43.95,940.884"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,30.2,543.8"}]},{"num":"10","Command":[{"cmdType":"Move","objectId":"5","transformMatrix":"0.997,0,0,0.997,214.889,1060.851"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.989,0,0,0.989,47.695,955.053"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.434,108.493,909.976"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.39,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,154.598,996.883"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,43,922.984"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,33.55,543.8"}]},{"num":"11","Command":[{"cmdType":"Move","objectId":"5","transformMatrix":"0.996,0,0,0.996,214.153,1046.507"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.988,0,0,0.988,48.114,939.075"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.434,113.538,881.893"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,156.898,981.033"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,41.65,904.734"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,36.9,543.8"}]},{"num":"12","Command":[{"cmdType":"Move","objectId":"5","transformMatrix":"0.995,0,0,0.995,213.568,1031.912"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.987,0,0,0.987,48.334,922.913"},{"cmdType":"Place","charid":"4","objectId":"7","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,88,1097.9"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"7","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"7","blendMode":"Normal"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"UpdateZOrder","objectId":"6","placeAfter":"7"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.434,117.738,855.596"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,159.848,964.733"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,39.95,886.184"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,40.25,543.8"}]},{"num":"13","Command":[{"cmdType":"Move","objectId":"5","transformMatrix":"0.993,0,0,0.993,213.132,1017.068"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.986,0,0,0.986,48.303,906.585"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,76.046,1068.598"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.434,121.236,830.614"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,163.398,948.133"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.47,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,38.1,867.434"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,43.6,543.8"}]},{"num":"14","Command":[{"cmdType":"Place","charid":"4","objectId":"8","placeAfter":"0","transformMatrix":"0.87,0,0,0.87,118.75,1109"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.75,0,"},{"cmdType":"UpdateVisibility","objectId":"8","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"8","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"5","placeAfter":"8"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.992,0,0,0.992,212.796,1002.024"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.985,0,0,0.985,48.173,890.124"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.434,66.443,1042.996"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.434,124.083,806.566"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,167.448,931.233"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,36.15,848.534"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,46.95,543.8"}]},{"num":"15","Command":[{"cmdType":"Move","objectId":"8","transformMatrix":"0.873,0,0,0.873,114.055,1093.997"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.991,0,0,0.991,212.46,986.68"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.984,0,0,0.984,47.892,873.496"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.434,58.839,1019.828"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.434,126.328,783.004"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,171.898,914.083"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,34.2,829.584"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,50.3,543.8"}]},{"num":"16","Command":[{"cmdType":"Move","objectId":"8","transformMatrix":"0.876,0,0,0.876,109.711,1079.062"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.99,0,0,0.99,212.275,971.186"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.983,0,0,0.983,47.412,856.785"},{"cmdType":"Place","charid":"4","objectId":"9","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,183.8,1087.1"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"9","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"9","blendMode":"Normal"},{"cmdType":"Place","charid":"4","objectId":"10","placeAfter":"9","transformMatrix":"0.348,0,0,0.348,127.05,910.8"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateVisibility","objectId":"10","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"10","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"7","placeAfter":"10"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.434,53.239,998.093"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.44,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.449,0,0,0.448,128.047,759.646"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.44,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,176.698,896.683"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,32.35,810.584"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,53.65,543.8"}]},{"num":"17","Command":[{"cmdType":"Move","objectId":"8","transformMatrix":"0.88,0,0,0.88,105.968,1064.076"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.989,0,0,0.989,212.189,955.492"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.982,0,0,0.982,46.881,839.856"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,188.042,1051.452"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.07,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.36,0,0,0.36,131.431,899.079"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.434,49.537,977.075"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.464,0,0,0.463,129.068,736.624"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,181.648,879.133"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,30.7,791.634"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,57.05,543.8"}]},{"num":"18","Command":[{"cmdType":"Move","objectId":"8","transformMatrix":"0.883,0,0,0.883,102.725,1049.09"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.988,0,0,0.988,212.153,939.598"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.981,0,0,0.981,46.151,822.945"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.434,186.486,1016.353"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.14,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.373,0,0,0.373,132.917,887.65"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.434,47.884,955.79"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.478,0,0,0.478,129.482,714.716"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,186.748,861.433"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,29.4,772.784"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,60.4,543.8"}]},{"num":"19","Command":[{"cmdType":"Move","objectId":"8","transformMatrix":"0.886,0,0,0.886,99.931,1034.155"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.987,0,0,0.987,212.217,923.503"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.98,0,0,0.98,45.37,805.867"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.434,181.578,982.188"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.385,0,0,0.385,132.055,876.684"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.434,48.53,933.338"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.493,0,0,0.492,129.301,694.509"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,191.898,843.683"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,28.45,754.134"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,63.75,543.8"}]},{"num":"20","Command":[{"cmdType":"Place","charid":"4","objectId":"11","placeAfter":"0","transformMatrix":"0.87,0,0,0.87,129.85,1169.5"},{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.75,0,"},{"cmdType":"UpdateVisibility","objectId":"11","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"11","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"8","placeAfter":"11"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.89,0,0,0.89,97.636,1019.202"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.986,0,0,0.986,212.331,907.259"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.979,0,0,0.979,44.54,788.706"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.434,175.028,949.356"},{"cmdType":"Place","charid":"4","objectId":"12","placeAfter":"9","transformMatrix":"0.348,0,0,0.348,103.6,752.15"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateVisibility","objectId":"12","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"12","blendMode":"Normal"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.28,0,"},{"cmdType":"UpdateZOrder","objectId":"10","placeAfter":"12"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.397,0,0,0.397,129.392,865.805"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.449,0,0,0.448,52.014,911.744"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.507,0,0,0.507,128.62,676.501"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.47,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,196.948,825.933"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,28.1,735.734"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,67.1,543.8"}]},{"num":"21","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.877,0,0,0.877,134.536,1150.633"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.893,0,0,0.893,95.795,1004.234"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.985,0,0,0.985,212.496,890.865"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.978,0,0,0.978,43.56,771.477"},{"cmdType":"Place","charid":"4","objectId":"13","placeAfter":"1","transformMatrix":"0.609,0,0,0.609,123.1,1075.5"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"13","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"13","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"9","placeAfter":"13"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.434,168.522,918.291"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.36,0,0,0.36,95.294,738.822"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.35,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.41,0,0,0.41,125.528,855.025"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.464,0,0,0.463,57.999,893.734"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.522,0,0,0.522,127.689,660.729"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,201.848,808.133"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,28.35,717.634"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,70.45,543.8"}]},{"num":"22","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.883,0,0,0.883,138.574,1132.334"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.896,0,0,0.896,94.351,989.348"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.984,0,0,0.984,212.66,874.371"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.977,0,0,0.977,42.579,754.216"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,130.148,1063.884"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.434,163.766,889.659"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.11,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.373,0,0,0.373,89.793,726.877"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.422,0,0,0.422,120.866,844.21"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.478,0,0,0.478,65.079,878.64"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.47,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.509,0,0,0.509,126.521,646.972"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,206.498,790.383"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.31,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,29.3,699.934"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,73.8,543.8"}]},{"num":"23","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.89,0,0,0.89,141.813,1114.635"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.9,0,0,0.9,93.308,974.462"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.983,0,0,0.983,212.924,857.677"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.976,0,0,0.976,41.599,736.888"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,135.498,1051.584"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.434,162.858,864.211"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.17,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.385,0,0,0.385,86.744,715.945"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.435,0,0,0.435,116.047,833.239"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.493,0,0,0.492,71.513,865.246"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.497,0,0,0.497,125.356,634.694"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,210.798,772.783"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,31.1,682.634"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,77.15,543.8"}]},{"num":"24","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.897,0,0,0.897,144.353,1097.553"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.903,0,0,0.903,92.664,959.576"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.982,0,0,0.982,213.238,840.883"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.975,0,0,0.975,40.518,719.477"},{"cmdType":"Place","charid":"4","objectId":"14","placeAfter":"1","transformMatrix":"0.522,0,0,0.522,36.25,1081.45"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"14","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"14","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"13","placeAfter":"14"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,139.248,1038.634"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.449,0,0,0.448,165.986,839.404"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.397,0,0,0.397,85.893,705.651"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.42,0,0,0.42,111.658,822.076"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.507,0,0,0.507,75.797,852.153"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.484,0,0,0.484,124.09,623.452"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,214.648,755.283"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,33.8,665.884"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,80.5,543.8"}]},{"num":"25","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.904,0,0,0.904,146.241,1080.854"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.906,0,0,0.906,92.421,944.691"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.98,0,0,0.98,213.552,823.989"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.973,0,0,0.973,39.438,702.099"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,32.649,1068.833"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,141.498,1025.184"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.464,0,0,0.463,170.817,812.53"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.28,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.41,0,0,0.41,86.842,695.607"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.33,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.406,0,0,0.406,108.212,810.457"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.522,0,0,0.522,76.581,838.192"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.472,0,0,0.472,122.825,612.974"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,217.948,737.983"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,37.55,649.684"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,83.9,543.8"}]},{"num":"26","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.911,0,0,0.911,147.531,1064.723"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.91,0,0,0.91,92.528,929.805"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.979,0,0,0.979,213.917,806.994"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.972,0,0,0.972,38.357,684.637"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,30.599,1055.333"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,142.448,1011.184"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.478,0,0,0.478,176.586,785.373"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.34,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.422,0,0,0.422,89.193,685.424"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.25,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.391,0,0,0.391,106.268,798.353"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.509,0,0,0.509,73.865,824.348"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.46,0,0,0.46,121.56,603.097"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,220.598,720.933"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,42.45,634.134"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,87.25,543.8"}]},{"num":"27","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.918,0,0,0.918,148.268,1049.006"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.913,0,0,0.913,92.934,914.97"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.978,0,0,0.978,214.281,789.85"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.971,0,0,0.971,37.377,667.159"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,30.049,1041.133"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,142.198,996.734"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.493,0,0,0.492,182.014,759.865"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.435,0,0,0.435,92.637,674.596"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.377,0,0,0.377,106.323,785.648"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.497,0,0,0.497,68.951,811.387"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.447,0,0,0.447,120.395,593.519"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.31,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,222.548,704.133"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,48.55,619.284"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,90.6,543.8"}]},{"num":"28","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.925,0,0,0.925,148.456,1033.607"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.916,0,0,0.916,93.693,900.101"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.977,0,0,0.977,214.695,772.656"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.97,0,0,0.97,36.396,649.698"},{"cmdType":"Place","charid":"4","objectId":"15","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,158.9,906.9"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"15","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"15","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"14","placeAfter":"15"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,30.849,1026.234"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,140.848,981.834"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.507,0,0,0.507,186.042,737.958"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.33,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.42,0,0,0.42,96.575,663.237"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0.08,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.362,0,0,0.362,109.027,772.229"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.484,0,0,0.484,63.086,798.509"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,119.325,584.147"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,223.698,687.633"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0.04,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,56.05,605.234"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,93.95,543.8"}]},{"num":"29","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.932,0,0,0.932,148.096,1018.626"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.92,0,0,0.92,94.747,885.248"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.976,0,0,0.976,215.009,755.412"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.969,0,0,0.969,35.416,632.22"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,165.443,878.159"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,32.849,1010.683"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,138.548,966.534"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.522,0,0,0.522,187.82,720.684"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.26,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.406,0,0,0.406,99.806,651.582"},{"cmdType":"UpdateColorTransform","objectId":"10","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"10","transformMatrix":"0.348,0,0,0.348,114.833,757.875"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.472,0,0,0.472,57.572,785.148"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,118.475,574.897"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,223.898,671.533"},{"cmdType":"UpdateColorTransform","objectId":"2","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"2","transformMatrix":"0.609,0,0,0.609,65,591.984"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,97.3,543.8"}]},{"num":"30","Command":[{"cmdType":"Remove","objectId":"2"},{"cmdType":"Remove","objectId":"10"},{"cmdType":"Move","objectId":"11","transformMatrix":"0.939,0,0,0.939,147.234,1003.876"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.923,0,0,0.923,96.054,870.463"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.975,0,0,0.975,215.423,738.068"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.968,0,0,0.968,34.585,614.809"},{"cmdType":"Place","charid":"4","objectId":"16","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,92.35,1171.15"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"16","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"16","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"15","placeAfter":"16"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.434,169.588,859.719"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,35.949,994.533"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,135.448,950.934"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.509,0,0,0.509,186.864,684.899"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.2,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.391,0,0,0.391,101.789,639.588"},{"cmdType":"UpdateZOrder","objectId":"7","placeAfter":"12"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.46,0,0,0.46,53.409,770.687"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,117.975,565.947"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,223.098,655.833"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"4"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,100.65,543.8"}]},{"num":"31","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.946,0,0,0.946,146.023,989.477"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.926,0,0,0.926,97.561,855.677"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.974,0,0,0.974,215.838,720.724"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.967,0,0,0.967,33.805,597.43"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,96.596,1135.525"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.434,172.131,847.506"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,40.049,977.883"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,131.698,934.984"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.497,0,0,0.497,182.564,653.596"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.377,0,0,0.377,101.522,626.945"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.447,0,0,0.447,51.895,754.276"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,117.825,557.297"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,221.198,640.583"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,104,543.8"}]},{"num":"32","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.953,0,0,0.953,144.263,975.296"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.93,0,0,0.93,99.367,840.841"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.973,0,0,0.973,216.202,703.279"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.966,0,0,0.966,33.124,580.019"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.434,95.043,1100.449"},{"cmdType":"Place","charid":"4","objectId":"17","placeAfter":"16","transformMatrix":"0.435,0,0,0.435,143.25,1096.45"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"17","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"17","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"15","placeAfter":"17"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.434,173.581,838.279"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,44.999,960.783"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,127.398,918.834"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.484,0,0,0.484,176.211,626.177"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0.06,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.362,0,0,0.362,98.353,613.54"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,54.527,734.965"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.31,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,118.275,548.847"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,218.098,625.783"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,107.35,543.8"}]},{"num":"33","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.96,0,0,0.96,142.149,961.229"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.933,0,0,0.933,101.324,826.056"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.972,0,0,0.972,216.566,685.835"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.965,0,0,0.965,32.544,562.741"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.434,90.139,1066.26"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,134.993,1068.049"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.434,174.526,829.116"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,50.749,943.283"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,122.748,902.484"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.472,0,0,0.472,169.011,602.425"},{"cmdType":"UpdateColorTransform","objectId":"12","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"12","transformMatrix":"0.348,0,0,0.348,91.386,599.147"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,57.177,725.065"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,119.275,540.597"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0.04,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,213.698,611.583"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,110.75,543.8"}]},{"num":"34","Command":[{"cmdType":"Remove","objectId":"12"},{"cmdType":"Move","objectId":"11","transformMatrix":"0.967,0,0,0.967,139.637,947.38"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.936,0,0,0.936,103.482,811.337"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.971,0,0,0.971,216.93,668.391"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.964,0,0,0.964,32.113,545.48"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.434,83.589,1033.453"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.434,134.489,1044.397"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.434,175.471,817.039"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,57.099,925.383"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,117.748,885.934"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.46,0,0,0.46,162.061,582.323"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"UpdateZOrder","objectId":"7","placeAfter":"9"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,60.027,713.815"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,121.025,532.397"},{"cmdType":"UpdateColorTransform","objectId":"4","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"4","transformMatrix":"0.522,0,0,0.522,207.948,597.933"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,114.1,543.8"}]},{"num":"35","Command":[{"cmdType":"Remove","objectId":"4"},{"cmdType":"Move","objectId":"11","transformMatrix":"0.974,0,0,0.974,136.777,933.699"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.94,0,0,0.94,105.839,796.501"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.97,0,0,0.97,217.244,650.897"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.963,0,0,0.963,31.833,528.302"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.434,77.086,1002.363"},{"cmdType":"Place","charid":"4","objectId":"18","placeAfter":"16","transformMatrix":"0.348,0,0,0.348,53.25,645"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateVisibility","objectId":"18","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"18","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"17","placeAfter":"18"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.434,138.833,1023.879"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.434,176.964,798.449"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,63.999,907.183"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,112.648,869.334"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.447,0,0,0.447,156.411,566.12"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,62.827,701.565"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,123.525,524.247"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"6"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,117.45,543.8"}]},{"num":"36","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.981,0,0,0.981,133.666,920.049"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.943,0,0,0.943,108.294,781.749"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.969,0,0,0.969,217.509,633.353"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.962,0,0,0.962,31.652,511.24"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.434,72.333,973.756"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.36,0,0,0.36,44.049,631.351"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.434,145.933,1004.995"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.449,0,0,0.448,176.411,776.576"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,71.299,888.733"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,107.498,852.584"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,153.452,554.218"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,65.177,688.615"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,126.925,516.047"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,120.8,543.8"}]},{"num":"37","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.988,0,0,0.988,130.156,906.468"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.946,0,0,0.946,110.85,767.013"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.967,0,0,0.967,217.673,615.859"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.961,0,0,0.961,31.722,494.212"},{"cmdType":"Place","charid":"4","objectId":"19","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,263.55,974.2"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"UpdateVisibility","objectId":"19","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"19","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"16","placeAfter":"19"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.434,71.429,948.28"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.11,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.373,0,0,0.373,36.05,617.931"},{"cmdType":"Place","charid":"4","objectId":"20","placeAfter":"18","transformMatrix":"0.348,0,0,0.348,225,1003.95"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateVisibility","objectId":"20","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"20","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"17","placeAfter":"20"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.434,154.178,986.527"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.464,0,0,0.463,171.462,757.639"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,78.899,870.083"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,102.498,835.834"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,153.102,543.168"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,66.727,675.415"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,131.175,507.797"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,124.15,543.8"}]},{"num":"38","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"0.995,0,0,0.995,126.444,892.869"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.95,0,0,0.95,113.507,752.327"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.966,0,0,0.966,217.887,598.415"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.96,0,0,0.96,31.991,477.301"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,251.588,944.938"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.449,0,0,0.448,74.508,923.452"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.17,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.385,0,0,0.385,29.502,604.121"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.07,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.36,0,0,0.36,229.373,992.201"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.434,161.524,967.292"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.478,0,0,0.478,163.702,741.266"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,86.649,851.283"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,97.748,819.134"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,153.852,529.068"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,67.077,662.365"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0.04,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,136.375,499.347"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,127.5,543.8"}]},{"num":"39","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.002,0,0,1.002,122.432,879.32"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.953,0,0,0.953,116.164,737.542"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.965,0,0,0.965,218.001,580.97"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.959,0,0,0.959,32.411,460.523"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.434,241.98,919.326"},{"cmdType":"Place","charid":"4","objectId":"21","placeAfter":"19","transformMatrix":"0.609,0,0,0.609,168.85,1103.55"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"21","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"21","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"16","placeAfter":"21"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.464,0,0,0.463,79.388,896.556"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.397,0,0,0.397,24.454,589.051"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.14,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.373,0,0,0.373,230.857,980.797"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.434,165.967,945.641"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.493,0,0,0.492,155.4,726.543"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,94.449,832.383"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,93.348,802.484"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,155.702,512.518"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,65.827,649.865"},{"cmdType":"UpdateColorTransform","objectId":"6","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"6","transformMatrix":"0.435,0,0,0.435,142.575,490.697"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,130.85,543.8"}]},{"num":"40","Command":[{"cmdType":"Remove","objectId":"6"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,118.221,865.671"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,118.87,722.856"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.964,0,0,0.964,218.115,563.526"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.958,0,0,0.958,33.08,443.861"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.434,234.368,896.143"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,176.397,1091.583"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.478,0,0,0.478,85.113,869.378"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.28,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.41,0,0,0.41,20.955,571.831"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.385,0,0,0.385,230.044,969.809"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.449,0,0,0.448,165.493,922.998"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.507,0,0,0.507,148.798,712.57"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,102.199,813.483"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,89.548,785.884"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,158.552,494.218"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,62.677,638.315"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"7"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,134.2,543.8"}]},{"num":"41","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,113.821,851.921"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,121.67,708.106"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.963,0,0,0.963,218.13,546.182"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.957,0,0,0.957,33.95,427.35"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.434,228.768,874.446"},{"cmdType":"Place","charid":"4","objectId":"22","placeAfter":"19","transformMatrix":"0.522,0,0,0.522,95.55,1117.4"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.6,0,"},{"cmdType":"UpdateVisibility","objectId":"22","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"22","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"21","placeAfter":"22"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,183.047,1078.333"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.493,0,0,0.492,90.592,843.9"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.34,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.422,0,0,0.422,19.057,551.621"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.28,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.397,0,0,0.397,227.328,958.956"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.464,0,0,0.463,160.871,901.989"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.522,0,0,0.522,145.596,698.234"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,109.699,794.583"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,86.348,769.484"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,162.402,474.768"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,57.327,628.115"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,137.6,543.8"}]},{"num":"42","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,109.221,838.171"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,124.47,693.406"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.962,0,0,0.962,218.094,528.888"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.69,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.95,0,0,0.95,35.171,410.933"},{"cmdType":"Place","charid":"4","objectId":"23","placeAfter":"1","transformMatrix":"0.435,0,0,0.435,119.3,1088.25"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"UpdateVisibility","objectId":"23","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"23","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"19","placeAfter":"23"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.434,225.06,853.364"},{"cmdType":"Place","charid":"4","objectId":"24","placeAfter":"19","transformMatrix":"0.435,0,0,0.435,18.7,906.95"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"UpdateVisibility","objectId":"24","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"24","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"22","placeAfter":"24"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,91.899,1104.483"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,188.847,1063.833"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.507,0,0,0.507,94.571,821.972"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.435,0,0,0.435,18.806,527.621"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.35,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.41,0,0,0.41,223.462,948.152"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.478,0,0,0.478,153.491,882.897"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.509,0,0,0.509,147.702,683.701"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,116.949,775.783"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,83.948,753.284"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,167.002,455.018"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,49.527,619.565"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,140.95,543.8"}]},{"num":"43","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,104.521,824.271"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,127.27,678.706"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.961,0,0,0.961,217.908,511.644"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.64,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.944,0,0,0.944,36.643,394.583"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,123.545,1052.601"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.434,223.402,832.066"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,26.099,873.209"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,89.749,1090.233"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,193.847,1048.233"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.522,0,0,0.522,96.4,804.726"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.33,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.42,0,0,0.42,22.089,501.122"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.422,0,0,0.422,218.849,937.364"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.493,0,0,0.492,145.567,865.504"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.497,0,0,0.497,154.363,669.346"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,123.799,757.083"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,82.448,737.284"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,172.302,435.718"},{"cmdType":"UpdateColorTransform","objectId":"7","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"7","transformMatrix":"0.435,0,0,0.435,39.027,612.965"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,144.3,543.8"}]},{"num":"44","Command":[{"cmdType":"Remove","objectId":"7"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,99.671,810.221"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,130.02,663.956"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.96,0,0,0.96,217.722,494.5"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.58,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.938,0,0,0.938,38.414,378.283"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.434,121.991,1017.503"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.434,224.04,809.604"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.434,32.149,842.719"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,88.949,1074.733"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,198.097,1031.583"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.509,0,0,0.509,95.409,768.937"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.26,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.406,0,0,0.406,28.72,478.134"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.435,0,0,0.435,214.023,926.415"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.507,0,0,0.507,139.293,849.512"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.484,0,0,0.484,163.322,655.377"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,130.049,738.533"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,82.048,721.584"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,178.152,417.668"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"9"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,147.65,543.8"}]},{"num":"45","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,94.821,795.971"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,132.72,649.206"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.959,0,0,0.959,217.436,477.405"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.53,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.932,0,0,0.932,40.536,362.133"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.434,117.085,983.337"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.44,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.449,0,0,0.448,226.127,788.047"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.434,37.198,814.606"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,89.399,1058.083"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,201.547,1014.083"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.497,0,0,0.497,91.121,737.584"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.2,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.391,0,0,0.391,36.251,457.956"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.42,0,0,0.42,209.607,915.225"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.522,0,0,0.522,136.219,834.452"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.472,0,0,0.472,173.183,642.022"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,135.699,720.283"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,82.798,706.234"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,184.352,401.468"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,151,543.8"}]},{"num":"46","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,89.921,781.571"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,135.37,634.406"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.958,0,0,0.958,217.001,460.411"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.925,0,0,0.925,42.957,346.133"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.434,110.535,950.505"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.464,0,0,0.463,228.017,769.924"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.434,41.398,788.329"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,91.049,1040.433"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,204.347,995.733"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.484,0,0,0.484,84.782,710.163"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.377,0,0,0.377,42.232,440.128"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.33,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.406,0,0,0.406,206.177,903.573"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.509,0,0,0.509,137.073,820.699"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.46,0,0,0.46,182.194,629.366"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,140.549,702.283"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,84.898,691.234"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,190.852,387.918"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,154.35,543.8"}]},{"num":"47","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,85.021,766.921"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,137.92,619.556"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.957,0,0,0.957,216.515,443.567"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.919,0,0,0.919,45.778,330.333"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.434,104.032,919.439"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.478,0,0,0.478,229.592,754.666"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.434,44.847,763.316"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,93.699,1021.783"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,206.397,976.633"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.472,0,0,0.472,77.594,686.41"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0.06,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.362,0,0,0.362,44.062,423.89"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.25,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.391,0,0,0.391,204.201,891.487"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.497,0,0,0.497,140.831,807.93"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.447,0,0,0.447,188.805,617.761"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,144.499,684.633"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,88.448,676.634"},{"cmdType":"UpdateColorTransform","objectId":"9","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"9","transformMatrix":"0.435,0,0,0.435,197.502,377.418"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,157.7,543.8"}]},{"num":"48","Command":[{"cmdType":"Remove","objectId":"9"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,80.171,752.021"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,140.42,604.656"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.69,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.95,0,0,0.95,215.863,426.772"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.37,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.913,0,0,0.913,49,314.783"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.434,99.278,890.807"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.493,0,0,0.492,230.629,741.109"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.434,47.697,739.289"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,97.249,1002.333"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,207.847,956.883"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.46,0,0,0.46,70.606,666.306"},{"cmdType":"UpdateColorTransform","objectId":"18","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"18","transformMatrix":"0.348,0,0,0.348,39.193,408.611"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.377,0,0,0.377,204.275,878.75"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.484,0,0,0.484,146.387,795.344"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,191.009,607.465"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,147.399,667.383"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,93.548,662.484"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"13"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,161.05,543.8"}]},{"num":"49","Command":[{"cmdType":"Remove","objectId":"18"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,75.371,736.821"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,142.77,589.706"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.64,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.944,0,0,0.944,215.108,409.91"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.907,0,0,0.907,52.571,299.483"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.434,98.373,865.359"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.507,0,0,0.507,230.816,727.951"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.434,49.996,715.699"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,101.699,982.083"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,208.647,936.633"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.447,0,0,0.447,64.968,650.153"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0.08,0,"},{"cmdType":"UpdateZOrder","objectId":"20","placeAfter":"16"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.362,0,0,0.362,206.995,865.298"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.472,0,0,0.472,153.095,782.324"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,190.009,597.465"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,149.249,650.583"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,100.398,648.884"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,164.45,543.8"}]},{"num":"50","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,70.671,721.321"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,144.97,574.656"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.58,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.938,0,0,0.938,214.203,393.098"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.26,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.901,0,0,0.901,56.543,284.483"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.44,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.449,0,0,0.448,101.45,840.54"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.522,0,0,0.522,230.153,713.929"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.44,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.449,0,0,0.448,51.619,692.327"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,106.849,961.133"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,208.897,915.883"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,62.026,638.246"},{"cmdType":"UpdateColorTransform","objectId":"20","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"20","transformMatrix":"0.348,0,0,0.348,212.769,850.962"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.46,0,0,0.46,160.403,768.055"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,188.859,586.415"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,149.799,634.333"},{"cmdType":"UpdateColorTransform","objectId":"13","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"13","transformMatrix":"0.609,0,0,0.609,109.098,635.784"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,167.8,543.8"}]},{"num":"51","Command":[{"cmdType":"Remove","objectId":"13"},{"cmdType":"Remove","objectId":"20"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,66.071,705.471"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,147.07,559.556"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.53,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.932,0,0,0.932,213.101,376.302"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.894,0,0,0.894,60.964,269.883"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.464,0,0,0.463,106.33,813.655"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.47,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.509,0,0,0.509,228.767,700.122"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.464,0,0,0.463,52.693,669.292"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,112.549,939.633"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,208.597,894.833"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,61.676,627.196"},{"cmdType":"UpdateZOrder","objectId":"17","placeAfter":"16"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.447,0,0,0.447,167.511,751.935"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,187.409,574.615"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,148.999,618.583"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"14"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,171.15,543.8"}]},{"num":"52","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,61.721,689.171"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,148.97,544.306"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.926,0,0,0.926,211.946,359.69"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.888,0,0,0.888,65.786,255.583"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.478,0,0,0.478,112.052,786.436"},{"cmdType":"Place","charid":"4","objectId":"25","placeAfter":"23","transformMatrix":"0.348,0,0,0.348,74.3,864.35"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateVisibility","objectId":"25","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"25","blendMode":"Normal"},{"cmdType":"UpdateZOrder","objectId":"19","placeAfter":"25"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.497,0,0,0.497,227.039,687.244"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.478,0,0,0.478,53.116,647.421"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,118.799,917.633"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,207.797,873.483"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,62.426,613.096"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,173.813,732.716"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,185.509,562.315"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,146.699,603.483"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,174.5,543.8"}]},{"num":"53","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,57.521,672.521"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,150.67,529.006"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.919,0,0,0.919,210.591,343.178"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.882,0,0,0.882,71.007,241.683"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.493,0,0,0.492,117.529,760.968"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.07,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.36,0,0,0.36,78.7,852.622"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.484,0,0,0.484,225.406,674.452"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.493,0,0,0.492,52.939,627.2"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,125.349,895.233"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,206.497,851.933"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,64.276,596.546"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,176.963,720.816"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,182.859,549.865"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,142.849,589.083"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,177.85,543.8"}]},{"num":"54","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,53.521,655.371"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.69,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,152.22,513.606"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.37,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.913,0,0,0.913,209.04,326.933"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.876,0,0,0.876,76.729,228.233"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.507,0,0,0.507,121.507,739.049"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.14,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.373,0,0,0.373,80.202,841.235"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.472,0,0,0.472,224.428,661.224"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.507,0,0,0.507,52.262,609.229"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,132.249,872.533"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,204.747,830.333"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,67.126,578.246"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,181.563,703.866"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,179.309,537.615"},{"cmdType":"UpdateColorTransform","objectId":"14","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"14","transformMatrix":"0.522,0,0,0.522,137.249,575.333"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,181.2,543.8"}]},{"num":"55","Command":[{"cmdType":"Remove","objectId":"14"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,49.871,637.771"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.64,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,153.52,498.106"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.907,0,0,0.907,207.335,310.92"},{"cmdType":"UpdateColorTransform","objectId":"1","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"1","transformMatrix":"0.87,0,0,0.87,82.85,215.183"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.522,0,0,0.522,123.334,721.814"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.385,0,0,0.385,79.357,830.21"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.46,0,0,0.46,224.4,646.847"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.522,0,0,0.522,51.336,593.394"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,139.249,849.583"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,202.597,808.733"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,70.976,558.796"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,187.163,682.716"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,174.659,525.865"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"15"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,184.55,543.8"}]},{"num":"56","Command":[{"cmdType":"Remove","objectId":"1"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,46.471,619.671"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.58,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,154.62,482.456"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.26,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.901,0,0,0.901,205.43,295.208"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.47,0,"},{"cmdType":"UpdateZOrder","objectId":"23","placeAfter":"5"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.509,0,0,0.509,122.373,685.996"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.28,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.397,0,0,0.397,76.609,819.372"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.447,0,0,0.447,226.072,630.519"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.47,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.509,0,0,0.509,50.201,579.709"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,146.299,826.533"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,200.047,787.233"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,75.576,539.046"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,193.313,658.266"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,168.809,515.065"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,187.9,543.8"}]},{"num":"57","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,43.421,601.021"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.53,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,155.42,466.656"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.894,0,0,0.894,203.375,279.896"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.497,0,0,0.497,118.065,654.66"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.35,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.41,0,0,0.41,72.762,808.584"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,230.082,611.297"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.497,0,0,0.497,48.967,567.403"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,153.299,803.433"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,197.147,765.983"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,80.876,519.746"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,199.613,631.716"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,161.559,505.415"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,191.3,543.8"}]},{"num":"58","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,40.721,581.771"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,155.97,450.706"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.888,0,0,0.888,201.123,264.951"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.484,0,0,0.484,111.705,627.259"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.422,0,0,0.422,68.116,797.759"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,232.332,600.447"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.48,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.484,0,0,0.484,47.733,556.182"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,160.099,780.383"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,193.947,744.983"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,86.726,501.696"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,205.463,604.166"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,152.859,497.215"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,194.65,543.8"}]},{"num":"59","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,38.421,561.971"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.42,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,156.22,434.656"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.882,0,0,0.882,198.668,250.489"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.472,0,0,0.472,104.498,603.523"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.435,0,0,0.435,63.316,786.832"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,234.132,586.447"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.472,0,0,0.472,46.448,545.726"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,166.599,757.533"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,190.447,724.333"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,92.926,485.496"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,210.463,576.816"},{"cmdType":"UpdateColorTransform","objectId":"15","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"15","transformMatrix":"0.435,0,0,0.435,142.559,490.665"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,198,543.8"}]},{"num":"60","Command":[{"cmdType":"Remove","objectId":"15"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,36.571,541.521"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.37,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,156.17,418.406"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.876,0,0,0.876,196.063,236.427"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.46,0,0,0.46,97.59,583.438"},{"cmdType":"Place","charid":"4","objectId":"26","placeAfter":"23","transformMatrix":"0.348,0,0,0.348,143.45,743.3"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateVisibility","objectId":"26","visibility":"true"},{"cmdType":"UpdateBlendMode","objectId":"26","blendMode":"Normal"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.41,0,"},{"cmdType":"UpdateZOrder","objectId":"25","placeAfter":"26"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.42,0,0,0.42,58.923,775.653"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,235.332,569.947"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.46,0,0,0.46,45.164,535.819"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,172.699,734.883"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,186.697,704.233"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,99.426,471.946"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,214.113,550.816"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"17"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,201.35,543.8"}]},{"num":"61","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,35.171,520.371"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,155.82,401.956"},{"cmdType":"UpdateColorTransform","objectId":"5","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"5","transformMatrix":"0.87,0,0,0.87,193.261,222.831"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.447,0,0,0.447,91.932,567.203"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.06,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.36,0,0,0.36,135.115,729.956"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.33,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.406,0,0,0.406,55.476,763.97"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.31,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,235.632,551.697"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.447,0,0,0.447,44.03,526.213"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.54,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,178.299,712.533"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,182.747,684.633"},{"cmdType":"UpdateColorTransform","objectId":"16","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"16","transformMatrix":"0.435,0,0,0.435,106.076,461.446"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,216.013,527.266"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,204.7,543.8"}]},{"num":"62","Command":[{"cmdType":"Remove","objectId":"16"},{"cmdType":"Remove","objectId":"5"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,34.271,498.571"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.26,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,155.07,385.356"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"UpdateZOrder","objectId":"23","placeAfter":"8"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,88.969,555.318"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.12,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.373,0,0,0.373,129.587,717.996"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.25,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.391,0,0,0.391,53.53,751.901"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,234.882,532.547"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.5,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,42.995,516.815"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.49,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,183.299,690.633"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,178.647,665.733"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"UpdateZOrder","objectId":"17","placeAfter":"21"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,215.713,507.266"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,208.05,543.8"}]},{"num":"63","Command":[{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,33.921,476.071"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,153.97,368.556"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,88.619,544.268"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.19,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.385,0,0,0.385,126.562,707.097"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.377,0,0,0.377,53.583,739.182"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,232.782,513.297"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,42.095,507.615"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.43,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,187.549,669.283"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,174.347,647.583"},{"cmdType":"UpdateColorTransform","objectId":"17","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"17","transformMatrix":"0.435,0,0,0.435,212.963,491.616"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,211.4,543.8"}]},{"num":"64","Command":[{"cmdType":"Remove","objectId":"17"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,34.121,452.821"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,152.47,351.556"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,89.369,530.168"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.25,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.397,0,0,0.397,125.733,696.837"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0.08,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.362,0,0,0.362,56.286,725.75"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,229.282,494.797"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.4,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,41.595,498.665"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,190.949,648.483"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,169.947,630.283"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"21"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,214.75,543.8"}]},{"num":"65","Command":[{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.65,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,34.871,428.821"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,150.57,334.306"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,91.219,513.618"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.41,0,0,0.41,126.655,686.777"},{"cmdType":"UpdateColorTransform","objectId":"25","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"25","transformMatrix":"0.348,0,0,0.348,62.09,711.381"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,224.082,477.947"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.36,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,41.495,490.015"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.32,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,193.399,628.383"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,165.547,613.933"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,218.15,543.8"}]},{"num":"66","Command":[{"cmdType":"Remove","objectId":"25"},{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.56,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,36.321,403.971"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,148.22,316.856"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.31,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,94.069,495.318"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.38,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.422,0,0,0.422,129.03,676.578"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"UpdateZOrder","objectId":"19","placeAfter":"26"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,217.082,463.547"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.31,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,41.895,481.565"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,194.749,609.083"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,160.997,598.633"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,221.5,543.8"}]},{"num":"67","Command":[{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.46,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,38.371,378.321"},{"cmdType":"UpdateColorTransform","objectId":"8","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"8","transformMatrix":"0.956,0,0,0.956,145.42,299.106"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,97.919,475.868"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.45,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.435,0,0,0.435,132.445,665.784"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0.04,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,208.182,452.247"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.27,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,42.945,473.265"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.21,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,194.949,590.683"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,156.447,584.433"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,224.85,543.8"}]},{"num":"68","Command":[{"cmdType":"Remove","objectId":"8"},{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.37,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,41.071,351.871"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"UpdateZOrder","objectId":"23","placeAfter":"11"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,102.519,456.118"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.37,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.42,0,0,0.42,136.424,654.394"},{"cmdType":"UpdateColorTransform","objectId":"19","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"19","transformMatrix":"0.435,0,0,0.435,197.332,444.647"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,44.645,465.115"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.16,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,193.849,573.233"},{"cmdType":"UpdateColorTransform","objectId":"21","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"21","transformMatrix":"0.609,0,0,0.609,151.897,571.433"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,228.2,543.8"}]},{"num":"69","Command":[{"cmdType":"Remove","objectId":"21"},{"cmdType":"Remove","objectId":"19"},{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.28,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,44.521,324.471"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,107.819,436.818"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.3,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.406,0,0,0.406,139.644,642.708"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"UpdateZOrder","objectId":"24","placeAfter":"26"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,47.195,456.965"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.1,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,191.299,556.883"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"22"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,231.55,543.8"}]},{"num":"70","Command":[{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.18,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,48.721,296.221"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,113.669,418.768"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.22,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.391,0,0,0.391,141.616,630.733"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.13,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,50.545,448.765"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0.05,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,187.299,541.633"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,234.9,543.8"}]},{"num":"71","Command":[{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,53.671,267.071"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,119.869,402.568"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.15,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.377,0,0,0.377,141.388,618.109"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.09,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,54.845,440.515"},{"cmdType":"UpdateColorTransform","objectId":"22","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"22","transformMatrix":"0.522,0,0,0.522,181.649,527.633"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,238.25,543.8"}]},{"num":"72","Command":[{"cmdType":"Remove","objectId":"22"},{"cmdType":"UpdateColorTransform","objectId":"11","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"11","transformMatrix":"1.009,0,0,1.009,59.371,236.921"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0.04,0,"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,126.369,389.018"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0.07,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.362,0,0,0.362,138.158,604.723"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0.04,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,60.045,432.065"},{"cmdType":"UpdateZOrder","objectId":"3","placeAfter":"24"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,241.6,543.8"}]},{"num":"73","Command":[{"cmdType":"Remove","objectId":"11"},{"cmdType":"UpdateColorTransform","objectId":"23","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"UpdateZOrder","objectId":"23","placeAfter":"0"},{"cmdType":"Move","objectId":"23","transformMatrix":"0.435,0,0,0.435,133.019,378.518"},{"cmdType":"UpdateColorTransform","objectId":"26","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"26","transformMatrix":"0.348,0,0,0.348,131.23,590.298"},{"cmdType":"UpdateColorTransform","objectId":"24","colorMatrix":"1,0,1,0,1,0,0,0,"},{"cmdType":"Move","objectId":"24","transformMatrix":"0.435,0,0,0.435,66.245,423.415"},{"cmdType":"Move","objectId":"3","transformMatrix":"1,0,0,1,245,543.8"}]}]}]}};

// var testAnimation = new SVGAnim(
//     waterTimerAnimationJson,
//     245,
//     1058,
//     24
// );