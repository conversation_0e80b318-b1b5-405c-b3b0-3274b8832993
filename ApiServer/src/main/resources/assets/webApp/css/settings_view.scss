@import "constants";

$SETTINGS_BOX_INNER_BORDER_RADIUS: 12px;
$SETTINGS_BOX_BORDER_RADIUS: 18px;

.settings-container {
  background: $DARK_BLUR_BACKGROUND_COLOR;
  display: flex;
  flex-direction: column;
  .settings-top-container {
    height: 64px;
    width: 100%;
    display: flex;
    .settings-title {
      @extend .bold-font;
      font-size: 32px;
      color: white;
      margin: auto;
      text-align: center;
      text-shadow: 2px 2px rgba(0, 0, 0, 0.74);
      }
    .settings-close-button {
      position: absolute;
      height: 48px;
      width: 48px;
      top: 8px;
      right: 8px;
      margin: 0;
      background-image: url($ASSET_ROOT + "images/svgs/close_button_square.svg");
      background-position: center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .settings-panel-list {
    display: flex;
    flex-direction: row;
    overflow-x: scroll;
    overflow-y: hidden;
    flex: 1;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    .settings-panel-container {
      width: 37%;
      min-width: 37%;
      overflow-y: scroll;
      .settings-panel {
        margin: 16px 8px 16px 8px;
        border-radius: $SETTINGS_BOX_BORDER_RADIUS;
        display: flex;
        flex-direction: column;
        background-color: rgba(0,0,0,0.2);
        .settings-panel-inner {
          border: rgba(255, 255, 255, 0.4) 2px dashed;
          border-radius: $SETTINGS_BOX_INNER_BORDER_RADIUS;
          flex: 1;
          margin: 12px;
          display: flex;
          flex-direction: column;
          .settings-panel-title {
            @extend .regular-font;
            margin-top: 8px;
            color: white;
            font-size: 22px;
            text-align: center;
          }
          .settings-panel-element-list {
            flex: 1;
            display: flex;
            overflow-x: hidden;
            overflow-y: scroll;
            flex-direction: column;
            margin-top: 6px;
            margin-bottom: 8px;
            padding-left: 4px;
            padding-right: 4px;
            .settings-panel-element {
              .panel-element-card {
                position: relative;
                height: 44px;
                display: flex;
                .panel-element-text {
                  @extend .regular-font;
                  text-align: center;
                  color: white;
                  font-size: 18px;
                  margin: auto;
                }
                .panel-element-check {
                  width: 24px;
                  height: 24px;
                  right: 10px;
                  align-self: center;
                  position: absolute;
                  display: none;
                  background-image: url($ASSET_ROOT + "images/svgs/settings_check.svg");
                  background-position: center;
                  background-size: contain;
                  background-repeat: no-repeat;
                }
              }
              &:focus, &:hover {
                .panel-element-card {
                  background-color: #f7f7f7;
                  border-radius: 2px;
                  box-shadow: $GENERAL_BOX_SHADOW;
                  .panel-element-text {
                    font-size: 20px;
                    color: $TEXT_COLOR_DARK;
                  }
                }
              }
              &.selected {
                .panel-element-card {
                  .panel-element-text {
                    @extend .bold-font;
                    font-size: 20px;
                    color: $KIDJO_GREEN;
                  }
                  .panel-element-check {
                    display: unset;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX) {
  .settings-container {
    .settings-top-container {
      .settings-close-button {
      }
    }
    .settings-panel-list {
      .settings-panel-container {
        width: 40%;
        min-width: 40%;
      }
    }
  }
}

@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  .settings-container {
    .settings-top-container {
      height: 72px;
      .settings-close-button {
        top: 12px;
        right: 12px;
      }
    }
    .settings-panel-list {
      .settings-panel-container {
        width: 80%;
        min-width: 80%;
        margin: 22px 16px auto 16px;
        .settings-panel-inner {
          .settings-panel-title {
            font-size: 28px;
          }
          .settings-panel-element-list {
            margin-top: 8px;
            .settings-panel-element {
              .panel-element-card {
                height: 52px;
                .panel-element-text {
                  font-size: 24px;
                }
                .panel-element-check {
                  width: 28px;
                  height: 28px;
                  right: 6px;
                }
              }

              &:focus, &:hover {
                .panel-element-card {
                  .panel-element-text {
                    font-size: 26px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (min-height: $MEDIA_QUERY_TABLET_MIN) {
  .settings-container {
    .settings-top-container {
      height: 82px;
      .settings-title {
        font-size: 42px;
      }
      .settings-close-button {
        top: 18px;
        right: 18px;
      }
    }
    .settings-panel-list {
      .settings-panel-container {
        margin: 22px 12px 22px 12px;
        width: 29%;
        min-width: 29%;
        .settings-panel-inner {
          .settings-panel-title {
            margin-top: 16px;
            font-size: 28px;
          }
          .settings-panel-element-list {
            margin-top: 12px;
            margin-bottom: 16px;
            padding-left: 16px;
            padding-right: 16px;
            .settings-panel-element {
              .panel-element-card {
                height: 58px;
                .panel-element-text {
                  font-size: 24px;
                }
                .panel-element-check {
                  width: 30px;
                  height: 30px;
                  right: 12px;
                }
              }

              &:focus, &:hover {
                .panel-element-card {
                  .panel-element-text {
                    font-size: 26px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (orientation: portrait) {
  .settings-container {
    .settings-top-container {
      height: 102px;
      .settings-title {
        font-size: 52px;
      }
      .settings-close-button {
        height: 56px;
        width: 56px;
        top: 18px;
        right: 18px;
      }
    }
    .settings-panel-list {
      margin-bottom: 16px;
      .settings-panel-container {
        width: 42%;
        min-width: 42%;
        .settings-panel-inner {
          .settings-panel-title {
            font-size: 32px;
          }
          .settings-panel-element-list {
            .settings-panel-element {
              .panel-element-card {
                .panel-element-text {
                  font-size: 26px;
                }
                .panel-element-check {
                }
              }

              &:focus, &:hover {
                .panel-element-card {
                  .panel-element-text {
                    font-size: 28px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}