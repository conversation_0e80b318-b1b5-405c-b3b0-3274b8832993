@import "constants";

:root {
  --WIDTH_OF_WATER_TIMER: 78px;
  --WIDTH_OF_WATER_TIMER_PLUS_8: 86px;
  --WIDTH_OF_WATER_TIMER_MINUS_SOME: 56px;
  --WATER_TIMER_RADIUS_CURVE: 25px;
}

.side-bar-container {
  position: absolute;
  left: 0;
  width: var(--WIDTH_OF_WATER_TIMER);
  bottom: 0;
  top: 0;
  max-height: 600px;
  margin-top: auto;
  margin-bottom: auto;

  background: linear-gradient(to right, rgba(94, 198, 195, 0.85), rgba(53, 179, 165, 0.85));
  border-top-right-radius: var(--WATER_TIMER_RADIUS_CURVE);
  border-bottom-right-radius: var(--WATER_TIMER_RADIUS_CURVE);
  overflow: hidden;
  box-shadow: $GENERAL_BOX_SHADOW;
  display: flex;
  flex-direction: column;
  .side-bar-favorite-button {
    height: 88px;
    min-height: 88px;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: radial-gradient(rgba(255, 255, 255, 0.34), rgba(0, 0, 0, 0.34));
    border-top-right-radius: var(--WATER_TIMER_RADIUS_CURVE);
    border-bottom: rgba(0,0,0,0.3) 1px solid;
    .side-bar-favorite-image {
      flex: 1;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
      background-image: url($ASSET_ROOT + "images/svgs/video_card_heart_white.svg");
      margin: 12px 12px 4px 12px;
    }
    .side-bar-favorite-text {
      @extend .bold-font;
      font-size: 18px;
      text-align: center;
      color: white;
      margin: auto auto 8px auto;
    }
    &.favorites-open {
      background: unset;
      box-shadow: inset 0 0 12px 4px rgba(0, 0, 0, 0.4);
      background-color: $FAVORITES_RED;
    }
  }
  .water-timer-image-container {
    width: 100%;
    flex: 1;
    .water-animation-svg {
      position: relative;
    }
  }
  .water-timer-text-container {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    pointer-events: none;
    .water-timer-minutes {
      @extend .bold-font;
      color: white;
      font-size: 34px;
      margin: auto auto 0 auto;
      text-shadow: 2px 2px rgba(0, 0, 0, 0.74);
    }
    .water-timer-minutes-title {
      @extend .regular-font;
      color: white;
      font-size: 22px;
      margin: 0 auto auto auto;
      text-shadow: 2px 2px rgba(0, 0, 0, 0.74);

    }
  }
  .water-timer-infinity {
    position: absolute;
    top: 0;
    left: 8%;
    height: 100%;
    width: 84%;
    background-image: url($ASSET_ROOT + "images/svgs/infinite.svg");
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    pointer-events: none;
  }

  .side-bar-settings-button {
    position: absolute;
    width: 100%;
    height: 82px;
    bottom: 0;
    display: flex;
    .settings-button-container {
      height: 48px;
      width: 48px;
      border: rgba(255,255,255,0.89) solid 3px;
      border-radius: var(--WATER_TIMER_RADIUS_CURVE);
      display: flex;
      margin: auto auto 8px auto;
      .settings-button-text {
        opacity: 0.89;
        flex: 1;
        margin: 5px;
        background-image: url($ASSET_ROOT + "images/svgs/gear_white.svg");
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
      }
    }
  }
}

@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  :root {
    --WIDTH_OF_WATER_TIMER: 72px;
    --WIDTH_OF_WATER_TIMER_PLUS_8: 80px;
  }


  //.side-bar-container {
  //  .side-bar-settings-button {
  //    .settings-button-container {
  //      padding: 8px 3px 8px 3px;
  //    }
  //  }
  //}
}