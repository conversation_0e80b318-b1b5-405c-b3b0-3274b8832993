@import "constants";

//================
//Folder views
//================
.folder-card {
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  display: flex;
  &.card-red.open {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_red_open.svg");
  }
  &.card-red.close {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_red.svg");
  }
  &.card-yellow.open {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_yellow_open.svg");
  }
  &.card-yellow.close {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_yellow.svg");
  }
  &.card-blue.open {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_blue_open.svg");
  }
  &.card-blue.close {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_blue.svg");
  }
  &.card-green.open {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_green_open.svg");
  }
  &.card-green.close {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_green.svg");
  }
  &.card-orange.open {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_orange_open.svg");
  }
  &.card-orange.close {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_orange.svg");
  }
  &.card-purple.open {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_purple_open.svg");
  }
  &.card-purple.close {
    background-image: url($ASSET_ROOT + "images/svgs/folder_card_purple.svg");
  }
  .folder-image {
    flex: 1;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    margin: 18%;
  }
}