@import "constants";

.overtime-modal-container {
  display: flex;
  flex-direction: column;
  background-color: $DARK_BLUR_BACKGROUND_COLOR;

  .overtime-image {
    width: 100%;
    height: 45%;
    margin: 16px auto 0 auto;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
  }
  .overtime-title{
    @extend .bold-font;
    text-align: center;
    color: white;
    font-size: 32px;
    margin:  auto auto 0 auto;
  }
  .overtime-button {
    width: 255px;
    margin: 12px auto auto auto;
    padding: 8px 0 8px 0;
    border: $KIDJO_GREEN solid 4px;
    border-radius: 12px;
    .overtime-button-title {
      @extend .bold-font;
      text-align: center;
      font-size: 28px;
      color: $KIDJO_GREEN;
    }
    .overtime-button-subtitle{
      @extend .regular-font;
      text-align: center;
      font-size: 18px;
      color: $KIDJO_GREEN;
    }
  }
}


@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  .overtime-modal-container {

    .overtime-image {
      width: 90%;
    }
    .overtime-title{
      margin-top: 16px;
    }
    .overtime-button {
      margin-top: 28px;
      .overtime-button-title {
      }
      .overtime-button-subtitle{
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX) {
  .overtime-modal-container {

    .overtime-image {
    }
    .overtime-title{
      margin-top: 6px;
      font-size: 22px;
    }
    .overtime-button {
      .overtime-button-title {
        font-size: 22px;
      }
      .overtime-button-subtitle{
      }
    }
  }
}
@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (min-height: $MEDIA_QUERY_TABLET_MIN) {
  .overtime-modal-container {

    .overtime-image {
      margin-top: 52px;
    }
    .overtime-title{
      //margin-top: 16px;
    }
    .overtime-button {
      margin-top: 38px;
      .overtime-button-title {
      }
      .overtime-button-subtitle{
      }
    }
  }
}
@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (orientation: portrait) {
  .overtime-modal-container {

    .overtime-image {
      width: 90%;
    }
    .overtime-title{
      margin-top: 38px;
    }
    .overtime-button {
      .overtime-button-title {
      }
      .overtime-button-subtitle{
      }
    }
  }
}