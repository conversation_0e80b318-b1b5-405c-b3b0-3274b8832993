@import "constants";

$INNER_CONTAINER_MARGIN: 12px;
$INNER_CONTAINER_MARGIN_PORTRAIT: 8px;
$INNER_TEXT_CONTAINER_MARGIN_PLUS: 12px;


.age-gate-close {
  width: 44px;
  height: 44px;
  margin-left: auto;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url($ASSET_ROOT + "images/svgs/close_button_square.svg");
}

.age-gate-view-container {
  background-color: $LIGHT_BLUR_BACKGROUND_COLOR;
  display: flex;
  .age-gate-card-container {
    background-color: white;
    width: 84vw;
    height: 90vh;
    margin: auto;
    border-radius: 12px;
    box-shadow: $GENERAL_BOX_SHADOW;
    display: flex;
    flex-direction: column;

    max-width: 750px;
    max-height: 500px;
    >.age-gate-close {
      display: none;
    }
    .age-gate-bottom-container {
      display: flex;
      flex: 1;
      flex-direction: row;
      .age-gate-text-container{
        flex: 1;
        margin: $INNER_CONTAINER_MARGIN+$INNER_TEXT_CONTAINER_MARGIN_PLUS 0 $INNER_CONTAINER_MARGIN+$INNER_TEXT_CONTAINER_MARGIN_PLUS $INNER_CONTAINER_MARGIN+$INNER_TEXT_CONTAINER_MARGIN_PLUS;
        flex-direction: column;
        display: flex;
        .age-gate-top-text1 {
          @extend .regular-font;
          font-size: 28px;
          color: $TEXT_COLOR_LITE_DARK;
        }
        .age-gate-top-text2 {
          @extend .regular-font;
          font-size: 22px;
          margin-top: 8px;
          color: $TEXT_COLOR_LITE_DARK;

        }
        .age-gate-result-container {
          display: flex;
          margin-top: auto;
          margin-bottom: auto;
          .age-gate-result-background {
            display:inline-block;
            background-color: #f1f1f1;
            border: solid 6px #d2d2d2;
            margin: auto;
            padding: 8px 12px 8px 12px;
            border-radius: 12px;
            .age-gate-result-text {
              @extend .bold-font;
              font-size: 48px;
              width: 150px;
              color: $TEXT_COLOR_EXTRA_LITE_DARK;
              text-align: center;
              white-space:nowrap;
            }
          }
        }
        .age-gate-info-text {
          @extend .regular-font;
          font-size: 14px;
          margin-bottom: 0;
          color: $TEXT_COLOR_EXTRA_LITE_DARK;

        }
      }
      .age-gate-button-container {
        flex: 1;
        margin: $INNER_CONTAINER_MARGIN $INNER_CONTAINER_MARGIN $INNER_CONTAINER_MARGIN 0;
        display: flex;
        flex-direction: column;
        .age-gate-button-pad {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          margin-top: 4px;
          .button-container {
            flex: 1;
            width: 33%;
            min-width: 33%;
            height: 25%;
            min-height: 25%;
            display: flex;
            //justify-content: center;
            div {
              flex: 1;
              width: 85%;
              max-width: 85%;
              height: 85%;
              max-height: 85%;
              margin: auto;
            }
            .num:focus, .num:hover {
              width: 95%;
              max-width: 95%;
              height: 95%;
              max-height: 95%;
            }
            .number-container {
              background-position: center;
              background-size: contain;
              background-repeat: no-repeat;
              background-image: url($ASSET_ROOT + "images/svgs/age_gate_pad.svg");
              display: flex;
              .number {
                @extend .regular-font;
                width: unset;
                height: unset;
                display: inline-block;
                font-size: 26px;
                margin: auto;
                text-align: center;
                padding-bottom: 4px;
              }
              &:focus, &:hover {
                background-image: url($ASSET_ROOT + "images/svgs/age_gate_pad_focus.svg");
              }
            }
            .delete-button {
              margin: auto;
              width: 60%;
              max-width: 60%;
              height: 60%;
              max-height: 60%;

              background-position: center;
              background-size: contain;
              background-repeat: no-repeat;
              background-image: url($ASSET_ROOT + "images/svgs/age_gate_delete.svg");
            }
            .delete-button:focus, .delete-button:hover {
              background-image: url($ASSET_ROOT + "images/svgs/age_gate_delete_focus.svg");
            }
          }
        }
      }
    }
  }
}

@media (max-height: 320px) and (min-width: 720px) and (orientation: landscape) {
  .age-gate-view-container {
    .age-gate-card-container {
      height: 80vh;
      .age-gate-bottom-container {
        .age-gate-text-container {
          margin: 16px 0 16px 16px !important;
          .age-gate-top-text1 {
            display: none;
          }

          .age-gate-top-text2 {
            font-size: 18px !important;
          }

          .age-gate-info-text {
            text-align: center;
          }
        }
      }
    }
  }
}


@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {

  .age-gate-view-container {
    .age-gate-card-container {
      width: 95vw;
      height: 85vh;
      max-height: unset;
      max-width: unset;
      .age-gate-close {
        display: unset;
        margin-top: $INNER_CONTAINER_MARGIN_PORTRAIT;
        margin-right: $INNER_CONTAINER_MARGIN_PORTRAIT;
      }
      .age-gate-bottom-container {
        flex-direction: column;
        margin: $INNER_CONTAINER_MARGIN_PORTRAIT;
        .age-gate-text-container{
          margin: $INNER_CONTAINER_MARGIN_PORTRAIT;
          flex: unset;
          .age-gate-top-text1 {
            display: none;
          }
          .age-gate-top-text2 {
            margin-top: 0;
            font-size: 24px;
            text-align: center;
          }
          .age-gate-result-container {
            margin-top: 12px;
            margin-bottom: 14px;
            .age-gate-result-background {
              padding: 4px 8px 4px 8px;
              .age-gate-result-text {
                font-size: 38px;
              }
            }
          }
          .age-gate-info-text {
            text-align: center;
          }
        }
        .age-gate-button-container {
          margin: 18px 0 0 0;
          .age-gate-close {
            display: none;
          }
          .age-gate-button-pad {
            margin-top: 0;
            .button-container {
              .number-container {
                .number {
                  font-size: 32px;
                }
              }
              .delete-button {
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX){
  .age-gate-view-container {
    .age-gate-card-container {
      .age-gate-close {
      }
      .age-gate-bottom-container {
        .age-gate-text-container{
          .age-gate-top-text1 {
            font-size: 20px;
          }
          .age-gate-top-text2 {
            font-size: 20px;
          }
          .age-gate-result-container {
            .age-gate-result-background {
              padding: 6px 8px 6px 8px;
              .age-gate-result-text {
                font-size: 40px;
              }
            }
          }
          .age-gate-info-text {
            font-size: 14px;
          }
        }
        .age-gate-button-container {
          .age-gate-close {
          }
          .age-gate-button-pad {
            .button-container {
              .number-container {
                .number {
                }
              }
              .delete-button {
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (min-height: $MEDIA_QUERY_TABLET_MIN) {
  .age-gate-view-container {
    .age-gate-card-container {
      .age-gate-close {
      }
      .age-gate-bottom-container {
        .age-gate-text-container{
          margin: 36px 0 36px 36px;
          .age-gate-top-text1 {
            font-size: 42px;
          }
          .age-gate-top-text2 {
            font-size: 32px;
          }
          .age-gate-result-container {
            .age-gate-result-background {

              padding: 10px 18px 10px 18px;
              .age-gate-result-text {
                font-size: 52px;
                width: 180px;
              }
            }
          }
          .age-gate-info-text {
            font-size: 18px;
          }
        }
        .age-gate-button-container {
          margin: 18px 18px 16px 0;
          .age-gate-close {
          }
          .age-gate-button-pad {
            .button-container {
              .number-container {
                .number {
                }
              }
              .delete-button {
              }
            }
          }
        }
      }
    }
  }
}