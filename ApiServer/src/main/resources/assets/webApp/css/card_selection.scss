@import "constants";

$LANDSCAPE_LIST_MARGIN: 15%;
$LANDSCAPE_SIDE_PADDING_INNER: 8px;
$PORTRAIT_LIST_MARGIN: 15%;
$PORTRAIT_VERTICAL_PADDING_INNER: 4px;
:root {
  --SIZE_OF_FOLDER_CARDS: 150px;
}
.card-selection-container {
  display: flex;
  flex-direction: column;
  background-position: top;
  background-size: cover;
  background-repeat: no-repeat;

  .card-list {
    flex: 1;
    overflow-x: scroll;
    overflow-y: hidden;
    left: 0;
    white-space: nowrap;
    display: flex;
    align-items: center;
    padding: 0 var(--WIDTH_OF_WATER_TIMER_PLUS_8) 0 var(--WIDTH_OF_WATER_TIMER_PLUS_8);
    flex-wrap: wrap;
    flex-direction: column;
    box-sizing: border-box;
    align-content: flex-start;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    &.vertical-sizing {
      .folder-card {
        width: var(--SIZE_OF_FOLDER_CARDS);
        min-width: var(--SIZE_OF_FOLDER_CARDS);
        height: 50%;
        min-height: 50%;
      }
    }
    &.horizontal-sizing {
      .folder-card {
        width: 50%;
        min-width: 50%;
        height: var(--SIZE_OF_FOLDER_CARDS);
        min-height: var(--SIZE_OF_FOLDER_CARDS);
      }
    }
    .folder-card {
      display: flex;
      transform: scale(0.9);
      &:focus, &:hover {
        transform: scale(1.0);
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  .card-selection-container {
    flex-direction: row;
    .card-list {
      overflow-x: hidden;
      overflow-y: scroll;
      flex-direction: row;
      top: 0;
      right: 0;
      left: 0;
      margin-left: var(--WIDTH_OF_WATER_TIMER_PLUS_8);
      width: auto;
      height: 100%;
      padding: $PORTRAIT_VERTICAL_PADDING_INNER $PORTRAIT_VERTICAL_PADDING_INNER $PORTRAIT_VERTICAL_PADDING_INNER $PORTRAIT_VERTICAL_PADDING_INNER;
    }
  }
}

@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (min-height: $MEDIA_QUERY_TABLET_MIN) {
  .card-selection-container {
    .card-list {
      margin-top: 20vh;
      margin-bottom: 20vh;
    }
  }
}
