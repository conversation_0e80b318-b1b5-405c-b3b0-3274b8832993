@import "constants";

.favorites-container {
  background-position: top;
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  .favorites-top-container {
    padding-left: var(--WIDTH_OF_WATER_TIMER_PLUS_8);
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
    .favorites-heart {
      height: 86px;
      width: 100%;
      margin: 16px auto 0 auto;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
      background-image: url($ASSET_ROOT + "images/svgs/favorites_heart.svg");
    }
    .favorites-title {
      @extend .bold-font;
      font-size: 32px;
      color: white;
      text-align: center;
      margin: 0 auto auto auto;
      text-shadow: 2px 2px rgba(0, 0, 0, 0.74);
    }
  }
  .favorites-video-list {

  }
}
@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX){
  .favorites-container {
    .favorites-top-container {
      .favorites-heart {
      }
      .favorites-title {
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  .favorites-container {
    .favorites-top-container {
      height: 70px;
      flex-direction: row;

      .favorites-heart {
        width: 55px;
        height: unset;
        margin: 8px 0 8px auto;
        background-position: right center;
      }
      .favorites-title {
        margin: auto auto auto 0;
        text-align: left;
      }
    }
  }

}