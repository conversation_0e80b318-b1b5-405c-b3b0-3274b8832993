@import "constants";


////===========
//FONTS
@font-face {
  font-family: 'BreeBold';
  src: url($FONT_ASSETS + 'BreeBold.woff2') format('woff2'),
  url($FONT_ASSETS + 'BreeBold.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'BreeRegular';
  src: url($FONT_ASSETS + 'BreeRegular.woff2') format('woff2'),
  url($FONT_ASSETS + 'BreeRegular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

body, html {
  position: fixed;
  background: $BACKGROUND_COLOR;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;

  font-family: 'BreeRegular', Arial, sans-serif;
  color: $TEXT_COLOR_DARK;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}
.app-container {
  width: 100%;
  height: 100%;
  .app-background-temp {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    @extend .blur;
    background-position: top;
    background-size: cover;
    background-repeat: no-repeat;
  }
  .base-view-container {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
  }
}
.boot-overlay {
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: $Z_INDEX_INIT_LOAD_SCREEN;
  background-color: white;
  display: flex;
  .boot-overlay-text {
    @extend .bold-font;
    color: white;
    font-size: 48px;
    text-align: center;
    margin: auto;
  }
}
.blur {
  -webkit-transition: 400ms;
  -moz-transition: 400ms;
  -ms-transition: 400ms;
  -o-transition: 400ms;
  transition: 400ms;
  -webkit-filter: blur(30px);
  filter: blur(30px);
}
.hidden {
  display: none !important;
}

.loading-container {
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: $Z_INDEX_LOADING_CARD;
  background: $LIGHT_BLUR_BACKGROUND_COLOR;
  display: flex;
  .loading-card {
    background: white;
    width: 30%;
    max-width: 300px;
    height: 30%;
    max-height: 300px;
    margin: auto;
    .loading-text {

    }
  }
}

//general views
@import "folder_cards";
@import "video_cards";
@import "side_bar";

//view controllers
@import "card_selection";
@import "favorites_view";
@import "folder_view";
@import "video_player";
@import "subscription_view";
@import "settings_view";
@import "age_gate";
@import "lock_out_view";
