@import "constants";

$LEFT_MARGIN: 4vw;
$LEFT_MARGIN_BUTTON: 4vw+2vw;

.subscribe-text-border {
  display: block;text-shadow: rgb(255, 255, 255) 4px 0px 0px, rgb(255, 255, 255) 3.87565px 0.989616px 0px, rgb(255, 255, 255) 3.51033px 1.9177px 0px, rgb(255, 255, 255) 2.92676px 2.72656px 0px, rgb(255, 255, 255) 2.16121px 3.36588px 0px, rgb(255, 255, 255) 1.26129px 3.79594px 0px, rgb(255, 255, 255) 0.282949px 3.98998px 0px, rgb(255, 255, 255) -0.712984px 3.93594px 0px, rgb(255, 255, 255) -1.66459px 3.63719px 0px, rgb(255, 255, 255) -2.51269px 3.11229px 0px, rgb(255, 255, 255) -3.20457px 2.39389px 0px, rgb(255, 255, 255) -3.69721px 1.52664px 0px, rgb(255, 255, 255) -3.95997px 0.56448px 0px, rgb(255, 255, 255) -3.97652px -0.432781px 0px, rgb(255, 255, 255) -3.74583px -1.40313px 0px, rgb(255, 255, 255) -3.28224px -2.28625px 0px, rgb(255, 255, 255) -2.61457px -3.02721px 0px, rgb(255, 255, 255) -1.78435px -3.57996px 0px, rgb(255, 255, 255) -0.843183px -3.91012px 0px, rgb(255, 255, 255) 0.150409px -3.99717px 0px, rgb(255, 255, 255) 1.13465px -3.8357px 0px, rgb(255, 255, 255) 2.04834px -3.43574px 0px, rgb(255, 255, 255) 2.83468px -2.82216px 0px, rgb(255, 255, 255) 3.44477px -2.03312px 0px, rgb(255, 255, 255) 3.84068px -1.11766px 0px, rgb(255, 255, 255) 3.9978px -0.132717px 0px;
}
.subscription-view-container {
  background-color: white;
  display: flex;
  flex-direction: column;
  .subscription-view-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url($ASSET_ROOT + "images/todo/subscription_background_land_row.jpg");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: -1;
  }

  .close-button {
    position: absolute;
    width: 48px;
    height: 48px;
    top: 8px;
    right: 8px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left center;
    background-image: url($ASSET_ROOT + "images/svgs/close_button_square.svg");
  }
  .subscription-main-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    .logo {
      height: 13vh;
      width: 100%;
      margin: 1vh 0 0 1vw;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: left center;
      background-image: url($ASSET_ROOT + "images/svgs/kidjo_logo.svg");
    }
    .subscription-inner-container {
      margin: auto 0 auto 0;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding-left: constant(safe-area-inset-left);
      .message1 {
        @extend .bold-font;
        @extend .subscribe-text-border;
        text-decoration: none;
        font-size: 50px;
        margin: 0 0 0 3vw;
        .subscribe-highlight-text {
          color: $SUBSCRIPTION_BUTTON_RED;
          font-size: 58px;
        }
      }
      .message2 {
        @extend .regular-font;
        @extend .subscribe-text-border;
        font-size: 32px;
        margin: 0 0 0 3vw;
      }
      .subscribe-button {
        height: 48px;
        width: 300px;
        text-decoration: none;
        background: linear-gradient(-90deg, $SUBSCRIPTION_BUTTON_RED_GRADIENT, $SUBSCRIPTION_BUTTON_RED, $SUBSCRIPTION_BUTTON_RED_GRADIENT);
        margin: 8px 0 0 3vw;
        display: flex;
        border: #fff solid 4px;
        border-radius: 8px;
        box-shadow: rgba(0, 0, 0, 0.3) 0 1px 10px 0;
        animation: shake 6s infinite forwards;
        .subscribe-message {
          @extend .bold-font;
          margin: auto;
          text-align: center;
          color: #fff;
          font-size: 32px;
        }
      }
      .login-button {
        @extend .regular-font;
        width: 300px;
        margin: 8px 0 0 3vw;
        text-align: center;
        font-size: 16px;
      }
    }
  }
}
//
//@media (max-width: 270px) and (orientation: portrait) {
//  .subscription-view-container {
//    .logo {
//      margin-top: 70px;
//    }
//    .subscribe-button {
//      .message1 {
//        font-size: 32px!important;
//      }
//      .message2 {
//        font-size: 18px!important;
//      }
//      .subscribe-message {
//        font-size: 22px!important;
//      }
//    }
//  }
//}

//@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
//  .subscription-view-container {
//    .subscription-view-background {
//      //background-image: url($ASSET_ROOT + "images/todo/subscription_background_land_row.jpg");
//    }
//    .logo {
//      height: 75px;
//    }
//    .close-button {
//      top: 12px;
//      right: 12px;
//    }
//    .message1 {
//      text-align: center;
//      margin: auto auto 4px auto;
//    }
//    .message2 {
//      text-align: center;
//      margin: 4px auto 22px auto;
//    }
//    .subscribe-button {
//      width: 80%;
//      margin: 0 auto 0 auto;
//      .subscribe-message {
//        font-size: 26px;
//      }
//    }
//    .login-button {
//      margin: 0 auto auto auto;
//      .login-button-text {
//        font-size: 16px;
//      }
//    }
//  }
//}
//
//@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (min-height: $MEDIA_QUERY_TABLET_MIN) {
//  .subscription-view-container {
//    .subscription-view-background {
//    }
//    .logo {
//      height: 100px;
//    }
//    .close-button {
//    }
//    .message1 {
//      font-size: 48px;
//      margin-bottom: 14px;
//    }
//    .message2 {
//      font-size: 36px;
//      margin-bottom: 66px;
//    }
//    .subscribe-button {
//      width: 400px;
//      height: 62px;
//      .subscribe-message {
//        font-size: 36px;
//      }
//    }
//    .login-button {
//      width: 400px;
//      .login-button-text {
//        font-size: 22px;
//      }
//    }
//  }
//}



//Horrible animation stuff
.subscription-view-background{-webkit-animation-name: zoomIn;animation-name: zoomIn;animation-delay: 0.5s; -webkit-animation-delay: 0.5s; -webkit-animation-duration: 3.5s;animation-duration: 3.5s;-webkit-animation-fill-mode: both;animation-fill-mode: both;  }
@-webkit-keyframes zoomIn {
  0% { transform: scale(1);}
  100% { transform: scale(1.18); }
}
@keyframes zoomIn {
  0% { transform: scale(1);}
  100% { transform: scale(1.18); }
}
@-webkit-keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
  }

  71%, 75%, 79%, 83%, 87% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  73%, 77%, 81%, 85%, 89%{
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
