@import "constants";

.open-folder-view-container {
  background-color: $LIGHT_BLUR_BACKGROUND_COLOR;
  display: flex;
  flex-direction: column;
  .top-section {
    height: 23%;
    margin-left: var(--WIDTH_OF_WATER_TIMER);
    display: flex;
    .close-button {
      width: 48px;
      height: 48px;
      background-image: url($ASSET_ROOT + "images/svgs/close_button_square.svg");
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
      margin-top: 8px;
      margin-right: 8px;
    }
    .folder-card {
      height: 85%;
      width: 150px;
      margin: auto;
      .folder-image {
        margin: 12px;
      }
    }
  }
  .video-list {
    margin-top: 16px;
  }
}

.game-iframe {
    width: 1px;
    min-width: 100%;
    *width: 100%;
    height: 100vh;
    position: absolute;
    top: 0;
}

@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX) {
  .open-folder-view-container {
    .top-section {
      height: 56px;
      .folder-card {
        display: none;
      }
      .close-button {
        margin-left: auto;
      }
    }
  }
}


@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  .open-folder-view-container {
    .top-section {
      position: absolute;
      width: 100%;
      margin-left: 0;
      height: 54px;
      z-index: 100;
      .close-button {
        margin-left: auto;
      }
      .folder-card {
        display: none;
      }
    }
    .video-list {
      margin-top: 0;
      padding-top: 62px;
    }
  }
}
@media (min-width: $MEDIA_QUERY_TABLET_MIN) and (min-height: $MEDIA_QUERY_TABLET_MIN) {
  .open-folder-view-container {
    .top-section {
      height: 19%;
      .close-button {
        width: 56px;
        height: 56px;
        margin-top: 24px;
        margin-right: 24px;
      }
    }
  }
}