@import "constants";

:root {
  --WIDTH_OF_VIDEO_CARD_CONTAINER: 78px;
  --HEIGHT_OF_VIDEO_CARD_CONTAINER: 100%;
  --WIDTH_OF_VIDEO_CARD: 78px;
  --HEIGHT_OF_VIDEO_CARD: 78px;
  --HEIGHT_OF_VIDEO_IMAGE: 78px;
}

$LOCK_BACKGROUND_CURVE: 8px;

.video-list {
  flex: 1;
  overflow-x: scroll;
  overflow-y: hidden;
  width: auto;
  display: flex;
  padding: 0 var(--WIDTH_OF_WATER_TIMER_PLUS_8) 0 var(--WIDTH_OF_WATER_TIMER_PLUS_8);
  flex-wrap: wrap;
  box-sizing: border-box;
  flex-direction: column;
  align-content: flex-start;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  .video-card-container {
    height: var(--HEIGHT_OF_VIDEO_CARD_CONTAINER);
    min-height: var(--HEIGHT_OF_VIDEO_CARD_CONTAINER);
    width: var(--WIDTH_OF_VIDEO_CARD_CONTAINER);
    min-width: var(--WIDTH_OF_VIDEO_CARD_CONTAINER);
    max-width: var(--WIDTH_OF_VIDEO_CARD_CONTAINER);
    .video-card {
      width: var(--WIDTH_OF_VIDEO_CARD);
      min-width: var(--WIDTH_OF_VIDEO_CARD);
      max-width: var(--WIDTH_OF_VIDEO_CARD);
      height: var(--HEIGHT_OF_VIDEO_CARD);
      min-height: var(--HEIGHT_OF_VIDEO_CARD);
      max-height: var(--HEIGHT_OF_VIDEO_CARD);
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      border: none;
      overflow: hidden;
      margin: auto;
      padding: 0;
      box-shadow: $GENERAL_BOX_SHADOW;

      &.card-red {
        background-color: $CARD_COLOR_RED;
      }
      &.card-yellow {
        background-color: $CARD_COLOR_YELLOW;
      }
      &.card-blue{
        background-color: $CARD_COLOR_BLUE;
      }
      &.card-green{
        background-color: $CARD_COLOR_GREEN;
      }
      &.card-orange {
        background-color: $CARD_COLOR_ORANGE;
      }
      &.card-purple {
        background-color: $CARD_COLOR_PURPLE;
      }

      .video-top-container {
        position: relative;
        width: 100%;
        height: var(--HEIGHT_OF_VIDEO_IMAGE);
        min-height: var(--HEIGHT_OF_VIDEO_IMAGE);
        max-height: var(--HEIGHT_OF_VIDEO_IMAGE);

        .lock-container {
          position: absolute;
          height: 20%;
          width: 100%;
          z-index: 3;
          display: flex;
          align-items: center;
          justify-content: center;
          .lock-background-container {
            width: 100%;
            height: 100%;
            position: absolute;
            .lock-background-top {
              width: 100%;
              height: 50%;
              background: black;
            }
            .lock-background-bottom {
              width: 55%;
              height: 50%;
              margin: auto;
              background: black;
              position:relative;
              border-bottom-left-radius: $LOCK_BACKGROUND_CURVE;
              border-bottom-right-radius: $LOCK_BACKGROUND_CURVE;
              content: " ";
            }
            .lock-background-bottom:before,
            .lock-background-bottom:after {
              border: 1px solid black;
              position: absolute;
              top: -1px;
              width: $LOCK_BACKGROUND_CURVE;
              height: $LOCK_BACKGROUND_CURVE;
              content: "";
            }

            .lock-background-bottom:before {
              left: -7px;
              border-top-right-radius: $LOCK_BACKGROUND_CURVE;
              border-width: 1px 1px 0px 0px;
              box-shadow: 2px 0px 0 black;
            }

            .lock-background-bottom:after {
              right: -7px;
              border-top-left-radius: $LOCK_BACKGROUND_CURVE;
              border-width: 1px 0px 0px 1px;
              box-shadow: -2px 0px 0 black;
            }
          }
          .lock-image {
            z-index: 1;
            height: 100%;
            width: 12px;
            margin-bottom: 3px;
            margin-right: 8px;
            background-image: url($ASSET_ROOT + "images/svgs/video_card_lock.svg");
            background-repeat: no-repeat;
            background-size: contain;
            background-position: center;
          }
          .lock-text {
            font-size: 14px;
            z-index: 1;
            color: white;
          }
        }
        .time-container {
          position: absolute;
          background-color: rgba(0,0,0,0.65);
          border-radius: 10px;
          left: 3%;
          bottom: 5%;
          padding: 6px;
          display: flex;
          align-items: center;
          .time-value {
            color: white;
            margin-right: 4px;
            font-size: 18px;
          }
          .time-type-text {
            color: white;
            font-size: 14px;
          }
        }

        .video-image {
          width: 100%;
          height: var(--HEIGHT_OF_VIDEO_IMAGE);
          min-height: var(--HEIGHT_OF_VIDEO_IMAGE);
          max-height: var(--HEIGHT_OF_VIDEO_IMAGE);
          z-index: 2;
          background-repeat: no-repeat;
          background-size: cover;
          background-position: center;
        }
      }
      .black-bar {
        height: 4px;
        width: 100%;
        background-color: black;
      }
      .video-title-container {
        flex: 1;
        display: flex;
        .video-title {
          margin: 2px 6px 0 6px;
          color: white;
          flex: 1;
        }
        .video-favorite-container {
          height: 100%;
          width: 56px;
          display: flex;
          background: radial-gradient(rgba(255, 255, 255, 0.5), rgba(0, 0, 0, 0.2));
          border-left: rgba(0,0,0,0.3) 1px solid;
          .video-favorite-image {
            margin: 12px;
            flex: 1;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
            background-image: url($ASSET_ROOT + "images/svgs/video_card_heart_white.svg");
          }
          &.is-favorited {
            .video-favorite-image {
              background-image: url($ASSET_ROOT + "images/svgs/favorites_heart.svg");
              margin: 4px;
            }
          }
        }
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX), (max-height: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX) {
  .video-list {
    .video-card-container {
      .video-card {
        .video-top-container {
          .lock-container {
            height: 25%;
            .lock-text {
            }
          }
          .time-container {
            .time-value {
            }
            .time-type-text {
            }
          }
        }
        .video-title {
        }
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_VERY_SMALL_PHONE_MAX) and(orientation: portrait) {
  .video-list {
    .video-card-container {
      .video-card {
        .video-top-container {
          .lock-container {
            .lock-text {
              font-size: 14px;
            }
          }
          .time-container {
            .time-value {
              font-size: 14px;
            }
            .time-type-text {
              font-size: 12px;
            }
          }
        }
        .video-title {
          font-size: 14px;
        }
      }
    }
  }
}
@media (max-width: $MEDIA_QUERY_PHONE_PORTRAIT_MAX_WIDTH) and (orientation: portrait) {
  .video-list {
    margin-left: var(--WIDTH_OF_WATER_TIMER_PLUS_8);
    overflow-y: scroll;
    overflow-x: hidden;
    flex-direction: row;
    padding: 0;
  }
}