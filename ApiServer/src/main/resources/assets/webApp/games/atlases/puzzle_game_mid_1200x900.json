{"frames": [{"filename": "frame1", "frame": {"x": 2, "y": 2, "w": 841, "h": 665}, "rotated": false, "trimmed": true, "spriteSourceSize": {"x": 1, "y": 0, "w": 841, "h": 665}, "sourceSize": {"w": 844, "h": 665}}, {"filename": "frame2", "frame": {"x": 841, "y": 1342, "w": 177, "h": 155}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 177, "h": 155}, "sourceSize": {"w": 177, "h": 155}}, {"filename": "frame3", "frame": {"x": 2, "y": 672, "w": 841, "h": 665}, "rotated": false, "trimmed": true, "spriteSourceSize": {"x": 1, "y": 0, "w": 841, "h": 665}, "sourceSize": {"w": 844, "h": 665}}, {"filename": "header", "frame": {"x": 2, "y": 1342, "w": 834, "h": 140}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 834, "h": 140}, "sourceSize": {"w": 834, "h": 140}}, {"filename": "help_off", "frame": {"x": 848, "y": 322, "w": 111, "h": 111}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 111, "h": 111}, "sourceSize": {"w": 111, "h": 111}}, {"filename": "help_on", "frame": {"x": 848, "y": 438, "w": 111, "h": 111}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 111, "h": 111}, "sourceSize": {"w": 111, "h": 111}}, {"filename": "hide", "frame": {"x": 848, "y": 2, "w": 156, "h": 75}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 156, "h": 75}, "sourceSize": {"w": 156, "h": 75}}, {"filename": "increase", "frame": {"x": 848, "y": 82, "w": 116, "h": 115}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 116, "h": 115}, "sourceSize": {"w": 116, "h": 115}}, {"filename": "level", "frame": {"x": 848, "y": 554, "w": 91, "h": 91}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 91, "h": 91}, "sourceSize": {"w": 91, "h": 91}}, {"filename": "progress", "frame": {"x": 452, "y": 1502, "w": 441, "h": 21}, "rotated": false, "trimmed": true, "spriteSourceSize": {"x": 2, "y": 1, "w": 441, "h": 21}, "sourceSize": {"w": 445, "h": 23}}, {"filename": "progress_bg", "frame": {"x": 2, "y": 1487, "w": 445, "h": 23}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 445, "h": 23}, "sourceSize": {"w": 445, "h": 23}}, {"filename": "reduce", "frame": {"x": 848, "y": 202, "w": 116, "h": 115}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 116, "h": 115}, "sourceSize": {"w": 116, "h": 115}}], "meta": {"app": "http://www.codeandweb.com/texturepacker", "version": "1.0", "image": "puzzle_game_mid_1200x900.png", "format": "RGBA8888", "size": {"w": 1024, "h": 2048}, "scale": "0.4395", "smartupdate": "$TexturePacker:SmartUpdate:c6a21f1888033b63be80e08d8d275646:8598063dd8584a6b07ab9acedf3f74c2:95888ec2dcfc0504f0562400a044b6e5$"}}