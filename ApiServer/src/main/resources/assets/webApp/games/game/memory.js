var __extends = (this && this.__extends) || (function () {
    var extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var GameEngine = (function (_super) {
            __extends(GameEngine, _super);
            function GameEngine() {
                var _this = _super.call(this, Config.GW, Config.GH, Phaser.AUTO, Config.DOM_PARENT_ID, null, true) || this;
                _this.state.add(States.BOOT, Client.Boot, false);
                _this.state.add(States.PRELOADER, Client.Preloader, false);
                _this.state.add(States.MAINMENU, Client.MainMenu, false);
                _this.state.add(States.GAME, Client.GameCtrl, false);
                _this.state.start(States.BOOT);
                return _this;
            }
            return GameEngine;
        }(Phaser.Game));
        Client.GameEngine = GameEngine;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
window.onload = function () {
    new PhaserGame.Client.GameEngine();
};
var Config;
(function (Config) {
    Config.DOM_PARENT_ID = 'content';
    Config.GW = 1200;
    Config.GH = 900;
    Config.GSW = 1200;
    Config.GSH = 600;
    Config.SHOW_FPS = false;
})(Config || (Config = {}));
var Params;
(function (Params) {
    Params.isIOS = false;
    Params.isTablet = false;
    Params.q_version = 2;
    Params.q_dif = 12;
    Params.q_path = 'pic0.png';
    Params.q_is_global_path = false;
    Params.q_s_min = 1;
    Params.q_s_max = 20;
    Params.pairs_opened_cnt = 0;
})(Params || (Params = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var BoomEff = (function (_super) {
            __extends(BoomEff, _super);
            function BoomEff(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.particles = [];
                _this.buffer = [];
                _this.addTimer = 0;
                _this.curr_rotation = 0;
                _this.part_cnt = 20;
                _this.part_live_time = 1;
                _this.parent_owner = false;
                _this.addTime = 0.02;
                _this.radius = 80;
                _this.spd_x_min = 0;
                _this.spd_x_max = 0;
                _this.spd_y_min = 0;
                _this.spd_y_max = 0;
                _this.isActive = false;
                _this.part_scale = 1;
                _this.grav_y = 0;
                _this.min_ang = -Math.PI;
                _this.max_ang = Math.PI;
                return _this;
            }
            BoomEff.prototype.getFromBuffer = function (pid) {
                for (var i = this.buffer.length - 1; i >= 0; i--) {
                    if (this.buffer[i].pid == pid)
                        return this.buffer.splice(i, 1)[0];
                }
                return null;
            };
            BoomEff.prototype.doEffect = function () {
                for (var i = 0; i < this.part_cnt; i++) {
                    this.addParticle();
                }
            };
            BoomEff.prototype.addParticle = function () {
                var pid = MyMath.randomIntInRange(1, 7);
                var p = this.getFromBuffer(pid);
                if (p == null) {
                    p = new Client.EffParticle(this.game, 0, 0, pid);
                }
                p.ay = this.grav_y;
                var rot = MyMath.randomInRange(this.min_ang, this.max_ang);
                p.x = Math.cos(rot) * MyMath.randomInRange(0, this.radius) + (this.parent_owner ? this.x : 0);
                p.y = Math.sin(rot) * MyMath.randomInRange(0, this.radius) + (this.parent_owner ? this.y : 0);
                p.vx = Math.cos(rot) * MyMath.randomInRange(this.spd_x_min, this.spd_x_max);
                p.vy = Math.sin(rot) * MyMath.randomInRange(this.spd_y_min, this.spd_y_max);
                p.vxr = 0.995;
                p.vyr = 0.995;
                p.rotSpd = MyMath.randomInRange(-6, 6);
                p.p_scale = this.part_scale;
                p.reUse(this.part_live_time);
                this.particles.push(p);
                if (this.parent_owner) {
                    if (this.parent != null)
                        this.parent.addChild(p);
                }
                else {
                    this.addChild(p);
                }
            };
            BoomEff.prototype.removeParticle = function (id) {
                try {
                    this.particles[id].parent.removeChild(this.particles[id]);
                    this.buffer = this.buffer.concat(this.particles.splice(id, 1));
                }
                catch (e) {
                    LogMng.error('SparkEffect.removeParticle(): ' + e);
                }
            };
            BoomEff.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                for (var i = this.particles.length - 1; i >= 0; i--) {
                    this.particles[i].update();
                    if (this.particles[i].isDead) {
                        this.removeParticle(i);
                    }
                }
            };
            return BoomEff;
        }(Phaser.Sprite));
        Client.BoomEff = BoomEff;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var CircularEffect = (function (_super) {
            __extends(CircularEffect, _super);
            function CircularEffect(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.particles = [];
                _this.buffer = [];
                _this.addTimer = 0;
                _this.curr_rotation = 0;
                _this.parent_owner = false;
                _this.addTime = 0.02;
                _this.radius = 80;
                _this.spd = 4;
                _this.isActive = false;
                _this.part_scale = 1;
                return _this;
            }
            CircularEffect.prototype.addParticle = function () {
                var p;
                if (this.buffer.length > 0) {
                    p = this.buffer.shift();
                }
                else {
                    p = new Client.EffParticle(this.game, 0, 0, 1);
                }
                p.ay = 90;
                var rot = MyMath.randomInRange(0, Math.PI * 2);
                p.x = Math.cos(rot) * this.radius + (this.parent_owner ? this.x : 0);
                p.y = Math.sin(rot) * this.radius + (this.parent_owner ? this.y : 0);
                p.vx = Math.cos(rot) * 50;
                p.vy = Math.sin(rot) * 50;
                p.rotSpd = MyMath.randomInRange(3, 6);
                p.p_scale = this.part_scale;
                p.reUse(1);
                this.particles.push(p);
                if (this.parent_owner) {
                    if (this.parent != null)
                        this.parent.addChild(p);
                }
                else {
                    this.addChild(p);
                }
            };
            CircularEffect.prototype.removeParticle = function (id) {
                try {
                    this.particles[id].parent.removeChild(this.particles[id]);
                    this.buffer = this.buffer.concat(this.particles.splice(id, 1));
                }
                catch (e) {
                    LogMng.error('SparkEffect.removeParticle(): ' + e);
                }
            };
            CircularEffect.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.curr_rotation += this.spd * dt;
                this.addTimer -= dt;
                if (this.isActive && this.addTimer <= 0) {
                    this.addTimer = this.addTime;
                    this.addParticle();
                }
                for (var i = this.particles.length - 1; i >= 0; i--) {
                    this.particles[i].update();
                    if (this.particles[i].isDead) {
                        this.removeParticle(i);
                    }
                }
            };
            return CircularEffect;
        }(Phaser.Sprite));
        Client.CircularEffect = CircularEffect;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var EffParticle = (function (_super) {
            __extends(EffParticle, _super);
            function EffParticle(game, x, y, pid) {
                var _this = _super.call(this, game, x, y) || this;
                _this.isDead = false;
                _this.vx = 0;
                _this.vy = 0;
                _this.ax = 0;
                _this.ay = 0;
                _this.rotSpd = 0;
                _this.vxr = 0.999;
                _this.vyr = 0.999;
                _this.p_scale = 1;
                _this.pid = pid;
                _this.spark = new Phaser.Sprite(_this.game, 0, 0, 'final', 'particle' + pid);
                _this.spark.anchor.set(0.5);
                return _this;
            }
            EffParticle.prototype.reUse = function (aLiveTime) {
                this.liveTime = aLiveTime;
                this.scale.set(0);
                this.alpha = 1;
                this.game.add.tween(this.scale).to({ x: this.p_scale, y: this.p_scale }, 200, Phaser.Easing.Linear.None, true).chain(this.game.add.tween(this.scale).to({ x: 0, y: 0 }, 200, Phaser.Easing.Linear.None, false, aLiveTime * 1000 - 400));
                this.isDead = false;
                this.addChild(this.spark);
            };
            EffParticle.prototype.update = function () {
                if (this.isDead)
                    return;
                var dt = this.game.time.elapsed / 1000;
                this.vx += this.ax * dt;
                this.vy += this.ay * dt;
                this.vx *= this.vxr;
                this.vy *= this.vyr;
                this.x += this.vx * dt;
                this.y += this.vy * dt;
                this.rotation += this.rotSpd * dt;
                this.liveTime -= dt;
                if (this.liveTime <= 0) {
                    this.isDead = true;
                }
            };
            return EffParticle;
        }(Phaser.Sprite));
        Client.EffParticle = EffParticle;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var SparkEffect = (function (_super) {
            __extends(SparkEffect, _super);
            function SparkEffect(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.particles = [];
                _this.buffer = [];
                _this.addTimer = 0;
                _this.addTime = 0.2;
                _this.area_w = 100;
                _this.area_h = 100;
                _this.isActive = false;
                return _this;
            }
            SparkEffect.prototype.addParticle = function () {
                var p;
                if (this.buffer.length > 0) {
                    p = this.buffer.shift();
                }
                else {
                    p = new Client.EffParticle(this.game, 0, 0, 1);
                }
                p.vx = 0;
                p.vy = 0;
                p.ay = 100;
                p.x = this.x + MyMath.randomInRange(-this.area_w / 2, this.area_w / 2);
                p.y = this.y + MyMath.randomInRange(-this.area_h / 2, this.area_h / 2);
                p.rotSpd = MyMath.randomInRange(3, 6);
                p.p_scale = this.scale.x;
                p.reUse(1);
                this.particles.push(p);
                if (this.parent != null)
                    this.parent.addChild(p);
            };
            SparkEffect.prototype.removeParticle = function (id) {
                try {
                    this.particles[id].parent.removeChild(this.particles[id]);
                    this.buffer = this.buffer.concat(this.particles.splice(id, 1));
                }
                catch (e) {
                    LogMng.error('SparkEffect.removeParticle(): ' + e);
                }
            };
            SparkEffect.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.addTimer -= dt;
                if (this.isActive && this.addTimer <= 0) {
                    this.addTimer = this.addTime;
                    this.addParticle();
                }
                for (var i = this.particles.length - 1; i >= 0; i--) {
                    this.particles[i].update();
                    if (this.particles[i].isDead) {
                        this.removeParticle(i);
                    }
                }
            };
            return SparkEffect;
        }(Phaser.Sprite));
        Client.SparkEffect = SparkEffect;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var BarScroller = (function (_super) {
            __extends(BarScroller, _super);
            function BarScroller(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.scroller = new Phaser.Sprite(game, 0, 0, 'game', 'level');
                _this.scroller.anchor.set(0.5);
                _this.addChild(_this.scroller);
                _this.text = new Phaser.Text(game, 0, 0, '0');
                _this.text.fontSize = 36;
                _this.text.anchor.set(0.5);
                _this.text.addColor('#fffefc', 0);
                _this.addChild(_this.text);
                return _this;
            }
            BarScroller.prototype.setText = function (aText) {
                this.text.text = aText;
            };
            return BarScroller;
        }(Phaser.Sprite));
        Client.BarScroller = BarScroller;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var DifPanel = (function (_super) {
            __extends(DifPanel, _super);
            function DifPanel(game, x, y, difs) {
                var _this = _super.call(this, game, x, y) || this;
                _this.difs = [];
                _this.curr_dif_id = 0;
                _this.last_drag_dif_id = -1;
                _this.onChangedSignal = new Phaser.Signal();
                _this.difs = difs;
                if (_this.difs.length > 2) {
                    _this.curr_dif_id = Math.round(_this.difs.length / 2);
                }
                _this.bg = new Phaser.Sprite(_this.game, 0, 0, 'game', 'header');
                _this.bg.anchor.set(0.5, 0);
                _this.addChild(_this.bg);
                _this.bar_bg = new Phaser.Sprite(_this.game, 0, _this.bg.height / 2 - 10, 'game', 'progress_bg');
                _this.bar_bg.anchor.set(0.5);
                _this.addChild(_this.bar_bg);
                _this.bar = new Phaser.Sprite(_this.game, 0, _this.bg.height / 2 - 10, 'game', 'progress');
                _this.bar.anchor.set(0.5);
                _this.addChild(_this.bar);
                _this.bar_mask = new Phaser.Graphics(game, _this.bar.x - _this.bar.width / 2, _this.bar.y - _this.bar.height / 2);
                _this.addChild(_this.bar_mask);
                _this.bar.mask = _this.bar_mask;
                _this.bar_scroller = new Client.BarScroller(game, _this.bar.x, _this.bar.y - 1);
                _this.addChild(_this.bar_scroller);
                _this.bar_scroller.inputEnabled = true;
                _this.bar_scroller.input.useHandCursor = true;
                _this.bar_scroller.input.enableDrag(false, false);
                _this.bar_scroller.input.allowVerticalDrag = false;
                _this.bar_scroller.events.onDragUpdate.add(_this.onDragUpdate, _this);
                _this.bar_scroller.events.onDragStop.add(_this.onDragStop, _this);
                _this.updScroller();
                _this.btnMin = new Phaser.Button(_this.game, -_this.bg.width / 2 + 110, 10, 'game', _this.onMinClick, _this, 'reduce', 'reduce');
                _this.btnMin.anchor.set(0.5, 0);
                _this.addChild(_this.btnMin);
                _this.btnPlus = new Phaser.Button(_this.game, _this.bg.width / 2 - 110, 10, 'game', _this.onPlusClick, _this, 'increase', 'increase');
                _this.btnPlus.anchor.set(0.5, 0);
                _this.addChild(_this.btnPlus);
                return _this;
            }
            DifPanel.prototype.getDifIdByPerc = function (aPerc) {
                var different = 0;
                var res = -1;
                for (var i = 0; i < this.difs.length; i++) {
                    var perc = i / this.difs.length;
                    if (Math.abs(perc - aPerc) < different || res == -1) {
                        res = i;
                        different = Math.abs(perc - aPerc);
                    }
                }
                return res;
            };
            DifPanel.prototype.onDragUpdate = function () {
                var min_x = this.bar.x - this.bar.width / 2;
                var max_x = this.bar.x + this.bar.width / 2;
                if (this.bar_scroller.x < min_x)
                    this.bar_scroller.x = min_x;
                if (this.bar_scroller.x > max_x)
                    this.bar_scroller.x = max_x;
                var perc = (this.bar_scroller.x - min_x) / (max_x - min_x);
                var new_dif_id = Math.round(perc * (this.difs.length - 1));
                if (this.last_drag_dif_id != new_dif_id) {
                    this.last_drag_dif_id = new_dif_id;
                    this.setDefId(new_dif_id, false);
                }
            };
            DifPanel.prototype.onDragStop = function () {
                this.setDefId(this.curr_dif_id);
            };
            DifPanel.prototype.onMinClick = function () {
                SndMng.sfxClick();
                if (this.curr_dif_id <= 0)
                    return;
                this.setDefId(this.curr_dif_id - 1);
            };
            DifPanel.prototype.onPlusClick = function () {
                SndMng.sfxClick();
                if (this.curr_dif_id >= this.difs.length - 1)
                    return;
                this.setDefId(this.curr_dif_id + 1);
            };
            DifPanel.prototype.updScroller = function (aMoving) {
                if (aMoving === void 0) { aMoving = true; }
                if (aMoving) {
                    var perc = this.difs.length > 0 ? this.curr_dif_id / (this.difs.length - 1) : 0;
                    this.game.tweens.removeFrom(this.bar_scroller);
                    var tw = this.game.add.tween(this.bar_scroller).to({ x: this.bar.x - this.bar.width / 2 + this.bar.width * perc }, 500, Phaser.Easing.Sinusoidal.Out, true);
                }
                this.bar_scroller.setText(String(this.difs[this.curr_dif_id]));
            };
            DifPanel.prototype.updateMask = function () {
                this.bar_mask.clear();
                this.bar_mask.beginFill(0, 1);
                this.bar_mask.drawRect(0, 0, this.bar_scroller.x - this.bar_mask.x, this.bar.height);
                this.bar_mask.endFill();
            };
            DifPanel.prototype.setDefId = function (id, aMoving) {
                if (aMoving === void 0) { aMoving = true; }
                this.curr_dif_id = id;
                this.onChangedSignal.dispatch(this.difs[this.curr_dif_id]);
                this.updScroller(aMoving);
            };
            DifPanel.prototype.setDefVal = function (aVal, aMoving) {
                if (aMoving === void 0) { aMoving = true; }
                for (var i = 0; i < this.difs.length; i++) {
                    if (aVal == this.difs[i]) {
                        this.setDefId(i, aMoving);
                        break;
                    }
                }
            };
            DifPanel.prototype.getPiecesCount = function () {
                return this.difs[this.curr_dif_id];
            };
            DifPanel.prototype.update = function () {
                this.updateMask();
            };
            return DifPanel;
        }(Phaser.Sprite));
        Client.DifPanel = DifPanel;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var HelpBtn = (function (_super) {
            __extends(HelpBtn, _super);
            function HelpBtn(game, x, y) {
                var _this = _super.call(this, game, x, y, 'game') || this;
                _this.isPressed = false;
                _this.onClickSignal = new Phaser.Signal();
                _this.onInputDown.add(_this.onClick, _this);
                _this.setFrames('help_off', 'help_off', 'help_on');
                _this.updateFrames();
                return _this;
            }
            HelpBtn.prototype.turnOn = function () {
                if (!this.isPressed)
                    this.onClick();
            };
            HelpBtn.prototype.updateFrames = function () {
            };
            HelpBtn.prototype.onClick = function () {
                this.isPressed = !this.isPressed;
                this.updateFrames();
                this.onClickSignal.dispatch();
            };
            return HelpBtn;
        }(Phaser.Button));
        Client.HelpBtn = HelpBtn;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var PAN_TIME = 2;
        var TopPanel = (function (_super) {
            __extends(TopPanel, _super);
            function TopPanel(game, x, y, difs, aDefaultDifVal) {
                if (aDefaultDifVal === void 0) { aDefaultDifVal = 4; }
                var _this = _super.call(this, game, x, y) || this;
                _this.panOpenTimer = PAN_TIME;
                _this.isTopPanelVisible = true;
                _this.onHelpClickSignal = new Phaser.Signal();
                _this.onCloseClickSignal = new Phaser.Signal();
                _this.onDifChangeSignal = new Phaser.Signal();
                _this.onPanelOpenSignal = new Phaser.Signal();
                _this.onPanelHidedSignal = new Phaser.Signal();
                _this.btnArrow = new Phaser.Button(_this.game, 0, 0, 'game', _this.onArrowClick, _this, 'hide', 'hide');
                _this.btnArrow.anchor.set(0.5, 0);
                _this.addChild(_this.btnArrow);
                _this.panel = new Client.DifPanel(_this.game, 0, 0, difs);
                _this.panel.setDefVal(aDefaultDifVal);
                _this.panel.onChangedSignal.add(_this.onDifChange, _this);
                _this.addChild(_this.panel);
                _this.btnHelp = new Client.HelpBtn(_this.game, -Config.GSW / 2 + 10, 10);
                _this.btnHelp.onClickSignal.add(_this.onHelpClick, _this);
                _this.addChild(_this.btnHelp);
                _this.btnClose = new Phaser.Button(_this.game, Config.GSW / 2 - 10, 10, 'game', _this.onCloseClick, _this, 'close', 'close');
                _this.btnClose.anchor.set(1, 0);
                _this.addChild(_this.btnClose);
                return _this;
            }
            TopPanel.prototype.onDifChange = function (aDif) {
                this.onDifChangeSignal.dispatch(aDif);
                this.openPanel();
            };
            TopPanel.prototype.onArrowClick = function () {
                SndMng.sfxClick();
                this.openPanel();
            };
            TopPanel.prototype.onHelpClick = function () {
                SndMng.sfxClick();
                this.onHelpClickSignal.dispatch(this.btnHelp.isPressed);
            };
            TopPanel.prototype.onCloseClick = function () {
                SndMng.sfxClick();
                window.dispatchEvent(new Event("close"));
//                window.location.replace("http://app_links_close")
                this.onCloseClickSignal.dispatch();
            };
            TopPanel.prototype.refreshPanelTimer = function () {
                this.panOpenTimer = PAN_TIME;
            };
            TopPanel.prototype.onPanelHided = function () {
                this.onPanelHidedSignal.dispatch();
            };
            TopPanel.prototype.setTopPanelVisible = function (aVis) {
                LogMng.debug('setTopPanelVisible val = ' + aVis);
                this.isTopPanelVisible = aVis;
                this.btnArrow.visible = aVis;
                this.panel.visible = aVis;
                this.btnHelp.visible = aVis;
                this.isTopPanelVisible ? this.openPanel() : this.hidePanel(true);
            };
            TopPanel.prototype.getPiecesCount = function () {
                return this.panel.getPiecesCount();
            };
            TopPanel.prototype.showHelp = function () {
                this.btnHelp.turnOn();
            };
            TopPanel.prototype.openPanel = function () {
                if (!this.isTopPanelVisible)
                    return;
                this.onPanelOpenSignal.dispatch();
                this.refreshPanelTimer();
                this.game.tweens.removeFrom(this.panel);
                this.game.add.tween(this.panel).to({ y: 0 }, 500, Phaser.Easing.Sinusoidal.Out, true);
            };
            TopPanel.prototype.hidePanel = function (aFast) {
                if (aFast === void 0) { aFast = false; }
                if (aFast) {
                    this.panel.y = -this.panel.height;
                    this.onPanelHidedSignal.dispatch();
                }
                else {
                    this.game.tweens.removeFrom(this.panel);
                    this.game.add.tween(this.panel).to({ y: -140 }, 500, Phaser.Easing.Sinusoidal.In, true).onComplete.add(this.onPanelHided, this);
                }
            };
            TopPanel.prototype.isHelpPressed = function () {
                return this.btnHelp.isPressed;
            };
            TopPanel.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                if (this.panOpenTimer > 0) {
                    this.panOpenTimer -= dt;
                    if (this.panOpenTimer <= 0) {
                        this.hidePanel();
                    }
                }
                if (this.panel != null)
                    this.panel.update();
            };
            return TopPanel;
        }(Phaser.Sprite));
        Client.TopPanel = TopPanel;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var BLOONS_DATA = [
            { id: 3, x: -300, y: 220, an: -40 },
            { id: 5, x: -300, y: 220, an: 5 },
            { id: 4, x: -300, y: 220, an: -20 },
            { id: 2, x: -300, y: 220, an: 15 },
            { id: 1, x: -300, y: 220, an: -20 },
            { id: 6, x: -300, y: 220, an: -5 },
            { id: 0, x: -300, y: 220, an: 2 },
            { id: 5, x: 300, y: 220, an: -10 },
            { id: 3, x: 300, y: 220, an: 40 },
            { id: 4, x: 300, y: 220, an: 20 },
            { id: 2, x: 300, y: 220, an: -15 },
            { id: 0, x: 300, y: 220, an: 20 },
            { id: 6, x: 300, y: 220, an: 5 },
            { id: 1, x: 300, y: 220, an: 0 }
        ];
        var WinWindow = (function (_super) {
            __extends(WinWindow, _super);
            function WinWindow(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.inited = false;
                _this.bloons = [];
                _this.onRestartClickEvent = new Phaser.Signal();
                _this.onCloseClickEvent = new Phaser.Signal();
                return _this;
            }
            WinWindow.prototype.showScreen = function () {
                if (this.inited)
                    return;
                MyUtils.updateBackColor(true);
                this.bg = new Phaser.Sprite(this.game, 0, 0, 'trophybg');
                this.bg.anchor.set(0.5);
                this.bg.inputEnabled = true;
                this.bg.input.useHandCursor = false;
                this.addChild(this.bg);
                this.cloud = new Phaser.Sprite(this.game, 0, -Config.GH / 2, 'final', 'cloud');
                this.cloud.anchor.set(0.5, 0);
                this.bg.addChild(this.cloud);
                this.conf = new Phaser.Sprite(this.game, 0, 0, 'fireworks');
                this.conf.anchor.set(0.5);
                this.addChild(this.conf);
                this.rocket = new Client.Rocket(this.game, 0, 400);
                this.rocket.visible = false;
                this.addChild(this.rocket);
                this.dummyBloons = new Phaser.Sprite(this.game, 0, Config.GH * 1.5);
                this.addChild(this.dummyBloons);
                this.bloons = [];
                for (var i = 0; i < BLOONS_DATA.length; i++) {
                    var bl = new Client.Bloon(this.game, BLOONS_DATA[i].x, BLOONS_DATA[i].y, BLOONS_DATA[i].id);
                    bl.scale.set(1.8);
                    bl.angle = BLOONS_DATA[i].an;
                    this.dummyBloons.addChild(bl);
                    this.bloons.push(bl);
                }
                this.game.add.tween(this.dummyBloons).to({ y: 0 }, 4000, Phaser.Easing.Sinusoidal.Out, true).onComplete.addOnce(this.onBloonsUp, this);
                this.dummyGui = new Phaser.Sprite(this.game, 0, -Config.GH / 2);
                this.updateGuiPanel();
                this.addChild(this.dummyGui);
                this.btnRestart = new Phaser.Button(this.game, -Config.GSW / 2 + 10, 10, 'final', this.onRestartClick, this, 'replay', 'replay');
                this.btnRestart.anchor.set(0, 0);
                this.dummyGui.addChild(this.btnRestart);
                this.btnClose = new Phaser.Button(this.game, Config.GSW / 2 - 10, 10, 'final', this.onCloseClick, this, 'close_puzzle', 'close_puzzle');
                this.btnClose.anchor.set(1, 0);
                this.dummyGui.addChild(this.btnClose);
                this.bg.alpha = 0;
                this.game.add.tween(this.bg).to({ alpha: 1 }, 800, Phaser.Easing.Linear.None, true);
                this.conf.scale.set(0);
                this.game.add.tween(this.conf.scale).to({ x: 1, y: 1 }, 400, Phaser.Easing.Sinusoidal.Out, true);
                this.rocket.y = 600;
                this.rocket.visible = true;
                this.game.add.tween(this.rocket).to({ y: -20 }, 3000, Phaser.Easing.Sinusoidal.Out, true);
                this.alpha = 1;
                SndMng.fadeOutMusic(0, 100);
                SndMng.sfxWin();
                this.game.time.events.add(5000, SndMng.fadeInMusic, this);
                this.inited = true;
            };
            WinWindow.prototype.onBloonsUp = function () {
                this.game.add.tween(this.dummyBloons).to({ y: 50 }, 1600, Phaser.Easing.Sinusoidal.InOut, true, 0, -1, true);
            };
            WinWindow.prototype.onRestartClick = function () {
                SndMng.sfxClick();
                this.onRestartClickEvent.dispatch();
                MyUtils.updateBackColor(false);
            };
            WinWindow.prototype.onCloseClick = function () {
                SndMng.sfxClick();
                this.onCloseClickEvent.dispatch();
            };
            WinWindow.prototype.hideScreen = function () {
                if (!this.inited)
                    return;
                this.game.add.tween(this).to({ alpha: 0 }, 200, null, true).onComplete.add(this.onHideComplete, this);
            };
            WinWindow.prototype.onHideComplete = function () {
                this.inited = false;
                for (var i = 0; i < this.bloons.length; i++) {
                    this.bloons[i].kill();
                }
                this.bloons = [];
                this.game.tweens.removeFrom(this.dummyBloons);
                this.rocket.free();
                this.rocket.kill();
                this.dummyGui.kill();
                this.removeChildren();
            };
            WinWindow.prototype.updateGuiPanel = function () {
                if (this.dummyGui == null)
                    return;
                if (ScaleManager.gameViewH < Config.GH) {
                    this.dummyGui.y = -Config.GH / 2 + (Config.GH - ScaleManager.gameViewH) / 2 - 1;
                }
                else {
                    this.dummyGui.y = -Config.GH / 2;
                }
            };
            WinWindow.prototype.update = function () {
                if (!this.inited)
                    return;
                this.updateGuiPanel();
                for (var i = 0; i < this.bloons.length; i++) {
                    this.bloons[i].update();
                }
            };
            return WinWindow;
        }(Phaser.Sprite));
        Client.WinWindow = WinWindow;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var OPEN2_TIMER_TIME = 2.4;
        var TIME_HELP = 6;
        var GameMng = (function (_super) {
            __extends(GameMng, _super);
            function GameMng(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.available_height = Config.GH;
                _this.top_padding = 100;
                _this.timerUpdScaling = 0.2;
                _this.cards = [];
                _this.openedCards = [];
                _this.closedCount = 0;
                _this.isWaiting = false;
                _this.open2timer = 0;
                _this.cardsCount = 0;
                _this.isHelp = false;
                _this.timerHelp = 0;
                _this.helpCards = [];
                _this.onGameCompleteEvent = new Phaser.Signal();
                _this.dummyCards = new Phaser.Sprite(_this.game, 0, 0);
                _this.addChild(_this.dummyCards);
                return _this;
            }
            GameMng.prototype.setHelpEnabled = function (aVal) {
                this.doHelp();
            };
            GameMng.prototype.getCardsWHCount = function (aCardsCount) {
                var res = new Phaser.Point();
                switch (aCardsCount) {
                    case 2:
                        res.set(2, 1);
                        break;
                    case 4:
                        res.set(2, 2);
                        break;
                    case 6:
                        res.set(3, 2);
                        break;
                    case 8:
                        res.set(4, 2);
                        break;
                    case 10:
                        res.set(5, 2);
                        break;
                    case 12:
                        res.set(4, 3);
                        break;
                    case 14:
                        res.set(5, 3);
                        break;
                    case 16:
                        res.set(6, 3);
                        break;
                    case 18:
                        res.set(6, 3);
                        break;
                    case 20:
                        res.set(7, 3);
                        break;
                    case 22:
                        res.set(8, 3);
                        break;
                    case 24:
                        res.set(8, 3);
                        break;
                    case 26:
                        res.set(9, 3);
                        break;
                    case 28:
                        res.set(7, 4);
                        break;
                    case 30:
                        res.set(10, 3);
                        break;
                    case 32:
                        res.set(8, 4);
                        break;
                    case 34:
                        res.set(9, 4);
                        break;
                    case 36:
                        res.set(9, 4);
                        break;
                    case 38:
                        res.set(10, 4);
                        break;
                    case 40:
                        res.set(10, 4);
                        break;
                }
                return res;
            };
            GameMng.prototype.clearField = function () {
                for (var i = 0; i < this.cards.length; i++) {
                    this.cards[i].kill();
                }
                this.cards = [];
                this.openedCards = [];
            };
            GameMng.prototype.restartGame = function (aCardsCount) {
                this.clearField();
                this.cardsCount = aCardsCount;
                this.cards_wh_cnt = this.getCardsWHCount(aCardsCount);
                var cards_ids = [];
                for (var i = Params.q_s_min; i <= Params.q_s_max; i++)
                    cards_ids.push(i);
                for (var i = 0; i < cards_ids.length * 4; i++) {
                    var id1 = MyMath.randomIntInRange(0, cards_ids.length - 1);
                    var id2 = MyMath.randomIntInRange(0, cards_ids.length - 1);
                    if (id1 == id2)
                        continue;
                    var itm1 = cards_ids[id1];
                    cards_ids[id1] = cards_ids[id2];
                    cards_ids[id2] = itm1;
                }
                var curr_id = 0;
                for (var i = 0; i < aCardsCount / 2; i++) {
                    if (curr_id >= cards_ids.length)
                        curr_id = 0;
                    this.cards.push(new Client.Card3(this.game, 0, 0, cards_ids[curr_id]));
                    this.cards.push(new Client.Card3(this.game, 0, 0, cards_ids[curr_id]));
                    curr_id++;
                }
                for (var i = 0; i < this.cards.length * 4; i++) {
                    var id1 = MyMath.randomIntInRange(0, this.cards.length - 1);
                    var id2 = 0;
                    do {
                        id2 = MyMath.randomIntInRange(0, this.cards.length - 1);
                    } while (this.cards.length > 1 && id2 == id1);
                    var citem = this.cards[id1];
                    this.cards[id1] = this.cards[id2];
                    this.cards[id2] = citem;
                }
                var cdx = 220;
                var cdy = 170;
                var cscale = 1;
                LogMng.debug('cards putting start...');
                for (var i = 0; i < this.cards.length; i++) {
                    var cpos = this.getCardPos(this.cards_wh_cnt.x, this.cards_wh_cnt.y, i);
                    this.cards[i].x = cpos.x;
                    this.cards[i].y = cpos.y;
                    this.cards[i].inputEnabled = true;
                    this.cards[i].input.useHandCursor = true;
                    this.cards[i].events.onInputDown.add(this.onCardInputDown, this);
                    this.cards[i].onOpenedSignal.add(this.onCardOpened, this);
                    this.dummyCards.addChild(this.cards[i]);
                }
                this.openedCards = [];
                this.closedCount = 0;
                this.isWaiting = false;
                this.updateScaling(true);
            };
            GameMng.prototype.getCardPos = function (cw, ch, cid) {
                var res = new Phaser.Point(0, 0);
                var cdx = 220;
                var cdy = 170;
                res.x = Config.GW / 2 - (cw - 1) / 2 * cdx + cid % cw * cdx;
                res.y = 280 + cdy * Math.floor(cid / cw);
                return res;
            };
            GameMng.prototype.updateScaling = function (aForce) {
                if (aForce === void 0) { aForce = false; }
                var dt = 0.4;
                var bot_pad = 12;
                var inc_top_pad = 0;
                if (ScaleManager.gameViewH < Config.GH) {
                    inc_top_pad = (Config.GH - ScaleManager.gameViewH) / 2;
                    this.available_height = Config.GH - (this.top_padding + inc_top_pad) - (Config.GH - ScaleManager.gameViewH) / 2 - bot_pad;
                }
                else {
                    this.available_height = Config.GH - (this.top_padding + inc_top_pad) - bot_pad;
                }
                var left_pad = 6;
                var av_w = Config.GW - left_pad * 2;
                var field_x = Config.GW / 2;
                var field_y = this.available_height / 2 + (this.top_padding + inc_top_pad) - bot_pad;
                var cw = this.cards_wh_cnt.x;
                var ch = this.cards_wh_cnt.y;
                var card_real_w = 330;
                var card_real_h = Params.isTablet ? 430 : 330;
                var cdx = av_w / cw;
                var cdy = this.available_height / ch;
                var cscale = Math.min(cdy / card_real_h, cdx / card_real_w);
                var max_cdx = card_real_w * cscale * 1.1;
                var max_cdy = card_real_h * cscale * 1.1;
                if (cdx > max_cdx)
                    cdx = max_cdx;
                if (cdy > max_cdy)
                    cdy = max_cdy;
                var row_num = 0;
                var in_row_cnt = 0;
                for (var i = 0; i < this.cards.length; i++) {
                    row_num = Math.floor(i / cw);
                    in_row_cnt = cw;
                    var len2 = this.cards.length;
                    for (var j = 0; j < row_num; j++) {
                        len2 -= cw;
                    }
                    if (len2 < cw)
                        in_row_cnt = len2;
                    var nx = field_x - (in_row_cnt - 1) / 2 * cdx + i % cw * cdx;
                    var ny = field_y - (ch - 1) / 2 * cdy + cdy * Math.floor(i / cw);
                    if (aForce) {
                        this.cards[i].x = nx;
                        this.cards[i].y = ny;
                        this.cards[i].scale.set(cscale);
                    }
                    else {
                        this.cards[i].x += (nx - this.cards[i].x) * dt;
                        this.cards[i].y += (ny - this.cards[i].y) * dt;
                        this.cards[i].scale.x += (cscale - this.cards[i].scale.x) * dt;
                        this.cards[i].scale.y += (cscale - this.cards[i].scale.y) * dt;
                    }
                }
            };
            GameMng.prototype.onCardInputDown = function (aCard) {
                if (this.isWaiting)
                    return;
                if (aCard.isBusy)
                    return;
                if (this.openedCards.length >= 2)
                    return;
                if (aCard.isOpened)
                    return;
                this.open2timer = OPEN2_TIMER_TIME;
                this.dummyCards.setChildIndex(aCard, this.dummyCards.children.length - 1);
                this.openedCards.push(aCard);
                aCard.doOpen();
            };
            GameMng.prototype.onCardOpened = function () {
                if (this.openedCards.length == 2 && this.isPairOpened())
                    this.closePair();
            };
            GameMng.prototype.isPairOpened = function () {
                if (this.openedCards.length != 2)
                    return false;
                var card1 = this.openedCards[0];
                var Card3 = this.openedCards[1];
                if (!card1.isOpened || card1.isBusy)
                    return false;
                if (!Card3.isOpened || Card3.isBusy)
                    return false;
                return card1.id == Card3.id;
            };
            GameMng.prototype.closePair = function () {
                var card1 = this.openedCards[0];
                var Card3 = this.openedCards[1];
                if (card1.id == Card3.id) {
                    this.completePair(card1, Card3);
                }
            };
            GameMng.prototype.completePair = function (aCard1, aCard3) {
                this.isWaiting = true;
                aCard1.events.onInputDown.removeAll(this);
                aCard3.events.onInputDown.removeAll(this);
                aCard1.doFound();
                aCard3.doFound();
                var joinDelay = 400;
                var joinDuration = 500;
                this.game.add.tween(aCard1).
                    to({ alpha: 0.5 }, joinDuration, Phaser.Easing.Linear.None, true, joinDelay);
                this.game.add.tween(aCard3).
                    to({ alpha: 0.5 }, joinDuration, Phaser.Easing.Linear.None, true, joinDelay);
                this.game.time.events.add(joinDelay + joinDuration, this.onPairClosed, this, aCard1, aCard3);
                SndMng.sfxPlay('found');
            };
            GameMng.prototype.onPairClosed = function (aCard1, aCard3) {
                this.isWaiting = false;
                this.closedCount += 2;
                this.openedCards = [];
                this.checkToWin();
            };
            GameMng.prototype.setTopPadding = function (aVal) {
                this.top_padding = aVal;
            };
            GameMng.prototype.setAvailableHeight = function (aHeight) {
                this.available_height = aHeight;
            };
            GameMng.prototype.checkToWin = function () {
                if (this.closedCount == this.cards.length) {
                    LogMng.debug('GameMng checkToWin!');
                    this.onGameCompleteEvent.dispatch();
                }
            };
            GameMng.prototype.closeOpened = function () {
                for (var i = 0; i < this.openedCards.length; i++)
                    this.openedCards[i].doClose();
                this.openedCards = [];
            };
            GameMng.prototype.getRandomCard = function () {
                var rid = MyMath.randomIntInRange(0, this.cards.length - 1);
                var res = null;
                while (res == null || res.isFound || res.isOpened) {
                    res = this.cards[rid];
                    rid++;
                    if (rid >= this.cards.length)
                        rid = 0;
                }
                return res;
            };
            GameMng.prototype.getRandomPair = function () {
                var res = [];
                res[0] = this.getRandomCard();
                for (var i = 0; i < this.cards.length; i++) {
                    if (this.cards[i] != res[0] &&
                        this.cards[i].id == res[0].id &&
                        !this.cards[i].isFound &&
                        !this.cards[i].isOpened)
                        res[1] = this.cards[i];
                }
                return res;
            };
            GameMng.prototype.doHelp = function () {
                this.helpCards = this.getRandomPair();
                if (this.helpCards.length == 2) {
                    this.timerHelp = TIME_HELP;
                    SndMng.sfxPlay('help');
                    for (var i = 0; i < this.helpCards.length; i++) {
                        this.helpCards[i].doHelp();
                    }
                }
            };
            GameMng.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.timerUpdScaling -= dt;
                if (this.timerUpdScaling <= 0) {
                    this.timerUpdScaling = 0.1;
                    this.updateScaling(true);
                }
                for (var i = 0; i < this.cards.length; i++) {
                    this.cards[i].update();
                }
                if (this.openedCards.length == 2 && !this.isWaiting) {
                    this.open2timer -= dt;
                    if (this.open2timer <= 0) {
                        this.closeOpened();
                    }
                }
                if (this.isHelp && this.closedCount < this.cards.length - 3) {
                    this.timerHelp -= dt;
                    if (this.timerHelp <= 0) {
                        this.doHelp();
                    }
                }
            };
            return GameMng;
        }(Phaser.Sprite));
        Client.GameMng = GameMng;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var OPEN2_TIMER_TIME = 2;
        var TIME_HELP = 6;
        var GameMng2 = (function (_super) {
            __extends(GameMng2, _super);
            function GameMng2(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.available_height = Config.GH;
                _this.top_padding = 100;
                _this.timerUpdScaling = 0.2;
                _this.cards = [];
                _this.openedCards = [];
                _this.closedCount = 0;
                _this.isWaiting = false;
                _this.open2timer = 0;
                _this.cardsCount = 0;
                _this.isHelp = false;
                _this.timerHelp = 0;
                _this.helpCards = [];
                _this.onGameCompleteEvent = new Phaser.Signal();
                _this.dummyCards = new Phaser.Sprite(_this.game, 0, 0);
                _this.addChild(_this.dummyCards);
                return _this;
            }
            GameMng2.prototype.setHelpEnabled = function (aVal) {
                this.doHelp();
            };
            GameMng2.prototype.getCardsWHCount = function (aCardsCount) {
                var res = new Phaser.Point();
                switch (aCardsCount) {
                    case 2:
                        res.set(2, 1);
                        break;
                    case 4:
                        res.set(2, 2);
                        break;
                    case 6:
                        res.set(3, 2);
                        break;
                    case 8:
                        res.set(4, 2);
                        break;
                    case 10:
                        res.set(5, 2);
                        break;
                    case 12:
                        res.set(4, 3);
                        break;
                    case 14:
                        res.set(5, 3);
                        break;
                    case 16:
                        res.set(6, 3);
                        break;
                    case 18:
                        res.set(6, 3);
                        break;
                    case 20:
                        res.set(7, 3);
                        break;
                    case 22:
                        res.set(8, 3);
                        break;
                    case 24:
                        res.set(8, 3);
                        break;
                    case 26:
                        res.set(9, 3);
                        break;
                    case 28:
                        res.set(7, 4);
                        break;
                    case 30:
                        res.set(10, 3);
                        break;
                    case 32:
                        res.set(8, 4);
                        break;
                    case 34:
                        res.set(9, 4);
                        break;
                    case 36:
                        res.set(9, 4);
                        break;
                    case 38:
                        res.set(10, 4);
                        break;
                    case 40:
                        res.set(10, 4);
                        break;
                }
                return res;
            };
            GameMng2.prototype.clearField = function () {
                for (var i = 0; i < this.cards.length; i++) {
                    this.cards[i].kill();
                }
                this.cards = [];
                this.openedCards = [];
            };
            GameMng2.prototype.restartGame = function (aCardsCount) {
                this.clearField();
                this.cardsCount = aCardsCount;
                this.cards_wh_cnt = this.getCardsWHCount(aCardsCount);
                var cards_ids = [];
                for (var i = Params.q_s_min; i <= Params.q_s_max; i++)
                    cards_ids.push(i);
                for (var i = 0; i < cards_ids.length * 4; i++) {
                    var id1 = MyMath.randomIntInRange(0, cards_ids.length - 1);
                    var id2 = MyMath.randomIntInRange(0, cards_ids.length - 1);
                    if (id1 == id2)
                        continue;
                    var itm1 = cards_ids[id1];
                    cards_ids[id1] = cards_ids[id2];
                    cards_ids[id2] = itm1;
                }
                var curr_id = 0;
                for (var i = 0; i < aCardsCount / 2; i++) {
                    if (curr_id >= cards_ids.length)
                        curr_id = 0;
                    this.cards.push(new Client.Card4(this.game, 0, 0, cards_ids[curr_id]));
                    this.cards.push(new Client.Card4(this.game, 0, 0, cards_ids[curr_id]));
                    curr_id++;
                }
                for (var i = 0; i < this.cards.length * 4; i++) {
                    var id1 = MyMath.randomIntInRange(0, this.cards.length - 1);
                    var id2 = 0;
                    do {
                        id2 = MyMath.randomIntInRange(0, this.cards.length - 1);
                    } while (this.cards.length > 1 && id2 == id1);
                    var citem = this.cards[id1];
                    this.cards[id1] = this.cards[id2];
                    this.cards[id2] = citem;
                }
                var cdx = 220;
                var cdy = 170;
                var cscale = 1;
                LogMng.debug('cards putting start...');
                for (var i = 0; i < this.cards.length; i++) {
                    var cpos = this.getCardPos(this.cards_wh_cnt.x, this.cards_wh_cnt.y, i);
                    this.cards[i].x = cpos.x;
                    this.cards[i].y = cpos.y;
                    this.cards[i].onInputDownSignal.add(this.onCardInputDown, this);
                    this.cards[i].onOpenedSignal.add(this.onCardOpened, this);
                    this.cards[i].setParent(this.dummyCards);
                }
                this.openedCards = [];
                this.closedCount = 0;
                this.isWaiting = false;
                this.updateScaling(true);
            };
            GameMng2.prototype.getCardPos = function (cw, ch, cid) {
                var res = new Phaser.Point(0, 0);
                var cdx = 220;
                var cdy = 170;
                res.x = Config.GW / 2 - (cw - 1) / 2 * cdx + cid % cw * cdx;
                res.y = 280 + cdy * Math.floor(cid / cw);
                return res;
            };
            GameMng2.prototype.updateScaling = function (aForce) {
                if (aForce === void 0) { aForce = false; }
                var dt = 0.4;
                var bot_pad = 12;
                var inc_top_pad = 0;
                if (ScaleManager.gameViewH < Config.GH) {
                    inc_top_pad = (Config.GH - ScaleManager.gameViewH) / 2;
                    this.available_height = Config.GH - (this.top_padding + inc_top_pad) - (Config.GH - ScaleManager.gameViewH) / 2 - bot_pad;
                }
                else {
                    this.available_height = Config.GH - (this.top_padding + inc_top_pad) - bot_pad;
                }
                var left_pad = 6;
                var av_w = Config.GW - left_pad * 2;
                var field_x = Config.GW / 2;
                var field_y = this.available_height / 2 + (this.top_padding + inc_top_pad) - bot_pad;
                var cw = this.cards_wh_cnt.x;
                var ch = this.cards_wh_cnt.y;
                var card_real_w = 330;
                var card_real_h = Params.isTablet ? 430 : 330;
                var cdx = av_w / cw;
                var cdy = this.available_height / ch;
                var cscale = Math.min(cdy / card_real_h, cdx / card_real_w);
                var max_cdx = card_real_w * cscale * 1.1;
                var max_cdy = card_real_h * cscale * 1.1;
                if (cdx > max_cdx)
                    cdx = max_cdx;
                if (cdy > max_cdy)
                    cdy = max_cdy;
                var row_num = 0;
                var in_row_cnt = 0;
                for (var i = 0; i < this.cards.length; i++) {
                    row_num = Math.floor(i / cw);
                    in_row_cnt = cw;
                    var len2 = this.cards.length;
                    for (var j = 0; j < row_num; j++) {
                        len2 -= cw;
                    }
                    if (len2 < cw)
                        in_row_cnt = len2;
                    var nx = field_x - (in_row_cnt - 1) / 2 * cdx + i % cw * cdx;
                    var ny = field_y - (ch - 1) / 2 * cdy + cdy * Math.floor(i / cw);
                    if (aForce) {
                        this.cards[i].x = nx;
                        this.cards[i].y = ny;
                        this.cards[i].sx = this.cards[i].sy = cscale;
                    }
                    else {
                        this.cards[i].x += (nx - this.cards[i].x) * dt;
                        this.cards[i].y += (ny - this.cards[i].y) * dt;
                        this.cards[i].sx += (cscale - this.cards[i].sx) * dt;
                        this.cards[i].sy += (cscale - this.cards[i].sy) * dt;
                    }
                }
            };
            GameMng2.prototype.onCardInputDown = function (aCard) {
                if (this.isWaiting)
                    return;
                if (aCard.isBusy)
                    return;
                if (this.openedCards.length >= 2)
                    return;
                if (aCard.isOpened)
                    return;
                this.open2timer = OPEN2_TIMER_TIME;
                aCard.bringToTop();
                this.openedCards.push(aCard);
                aCard.doOpen();
            };
            GameMng2.prototype.onCardOpened = function () {
                if (this.openedCards.length == 2 && this.isPairOpened())
                    this.closePair();
            };
            GameMng2.prototype.isPairOpened = function () {
                if (this.openedCards.length != 2)
                    return false;
                var card1 = this.openedCards[0];
                var Card4 = this.openedCards[1];
                if (!card1.isOpened || card1.isBusy)
                    return false;
                if (!Card4.isOpened || Card4.isBusy)
                    return false;
                return card1.id == Card4.id;
            };
            GameMng2.prototype.closePair = function () {
                var card1 = this.openedCards[0];
                var Card4 = this.openedCards[1];
                if (card1.id == Card4.id) {
                    this.completePair(card1, Card4);
                }
            };
            GameMng2.prototype.completePair = function (aCard1, aCard2) {
                this.isWaiting = true;
                aCard1.removeClickEvent();
                aCard2.removeClickEvent();
                aCard1.doFound();
                aCard2.doFound();
                Params.pairs_opened_cnt++;
                var joinDelay = 400;
                var joinDuration = 500;
                this.game.add.tween(aCard1).
                    to({ alpha: 0.5 }, joinDuration, Phaser.Easing.Linear.None, true, joinDelay);
                this.game.add.tween(aCard2).
                    to({ alpha: 0.5 }, joinDuration, Phaser.Easing.Linear.None, true, joinDelay);
                this.game.time.events.add(joinDelay + joinDuration, this.onPairClosed, this, aCard1, aCard2);
                SndMng.sfxPlay('found');
            };
            GameMng2.prototype.onPairClosed = function (aCard1, aCard4) {
                this.isWaiting = false;
                this.closedCount += 2;
                this.openedCards = [];
                this.checkToWin();
            };
            GameMng2.prototype.setTopPadding = function (aVal) {
                this.top_padding = aVal;
            };
            GameMng2.prototype.setAvailableHeight = function (aHeight) {
                this.available_height = aHeight;
            };
            GameMng2.prototype.checkToWin = function () {
                if (this.closedCount == this.cards.length) {
                    LogMng.debug('GameMng checkToWin!');
                    this.onGameCompleteEvent.dispatch();
                }
            };
            GameMng2.prototype.closeOpened = function () {
                for (var i = 0; i < this.openedCards.length; i++)
                    this.openedCards[i].doClose();
                this.openedCards = [];
            };
            GameMng2.prototype.getRandomCard = function () {
                var rid = MyMath.randomIntInRange(0, this.cards.length - 1);
                var res = null;
                while (res == null || res.isFound || res.isOpened) {
                    res = this.cards[rid];
                    rid++;
                    if (rid >= this.cards.length)
                        rid = 0;
                }
                return res;
            };
            GameMng2.prototype.getRandomPair = function () {
                var res = [];
                res[0] = this.getRandomCard();
                for (var i = 0; i < this.cards.length; i++) {
                    if (this.cards[i] != res[0] &&
                        this.cards[i].id == res[0].id &&
                        !this.cards[i].isFound &&
                        !this.cards[i].isOpened)
                        res[1] = this.cards[i];
                }
                return res;
            };
            GameMng2.prototype.doHelp = function () {
                this.helpCards = this.getRandomPair();
                if (this.helpCards.length == 2) {
                    this.timerHelp = TIME_HELP;
                    SndMng.sfxPlay('help');
                    for (var i = 0; i < this.helpCards.length; i++) {
                        this.helpCards[i].doHelp();
                    }
                }
            };
            GameMng2.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.updateScaling();
                for (var i = 0; i < this.cards.length; i++) {
                    this.cards[i].update();
                }
                if (this.openedCards.length == 2 && !this.isWaiting) {
                    this.open2timer -= dt;
                    if (this.open2timer <= 0) {
                        this.closeOpened();
                    }
                }
                if (this.isHelp && this.closedCount < this.cards.length - 3) {
                    this.timerHelp -= dt;
                    if (this.timerHelp <= 0) {
                        this.doHelp();
                    }
                }
            };
            return GameMng2;
        }(Phaser.Sprite));
        Client.GameMng2 = GameMng2;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var LoadMng;
(function (LoadMng) {
    var SceneLoader = (function () {
        function SceneLoader(game) {
            this.isFinalLoaded = false;
            this.onAllLoadedSignal = new Phaser.Signal();
            this.game = game;
            this.loader = new Phaser.Loader(game);
        }
        SceneLoader.prototype.startLoading = function () {
            this.loader.image('trophybg', './assets/sprites/trophybg.png');
            this.loader.image('fireworks', './assets/sprites/fireworks.png');
            this.loader.onLoadComplete.addOnce(this.onStageLoaded, this);
            this.loader.start();
        };
        SceneLoader.prototype.onStageLoaded = function () {
            this.isFinalLoaded = true;
            LogMng.debug('onAllResLoaded...');
            this.onAllLoadedSignal.dispatch();
        };
        return SceneLoader;
    }());
    LoadMng.SceneLoader = SceneLoader;
})(LoadMng || (LoadMng = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var AREA_POS = [
            { x: 0, y: -120 },
            { x: 0, y: -140 },
            { x: 0, y: -150 },
            { x: 0, y: -160 },
            { x: 0, y: -195 },
            { x: 0, y: -195 },
            { x: 0, y: -195 },
        ];
        var Bloon = (function (_super) {
            __extends(Bloon, _super);
            function Bloon(game, x, y, id) {
                var _this = _super.call(this, game, x, y) || this;
                _this.id = id;
                _this.bloon = new Phaser.Sprite(game, 0, 0, 'final', 'ballon2_' + String(id));
                _this.bloon.anchor.set(0.5, 1);
                _this.addChild(_this.bloon);
                _this.click_area = new Phaser.Graphics(_this.game, AREA_POS[_this.id].x, AREA_POS[_this.id].y);
                _this.click_area.beginFill(0, 0);
                _this.click_area.drawCircle(0, 0, 80);
                _this.click_area.endFill();
                _this.click_area.inputEnabled = true;
                _this.click_area.input.useHandCursor = true;
                _this.click_area.events.onInputDown.addOnce(_this.onClick, _this);
                _this.addChild(_this.click_area);
                _this.boomEff = new Client.BoomEff(_this.game, AREA_POS[_this.id].x, AREA_POS[_this.id].y);
                _this.boomEff.part_cnt = 5;
                _this.boomEff.scale.set(0.5);
                _this.boomEff.grav_y = 100;
                _this.boomEff.radius = 50;
                _this.boomEff.part_live_time = 1;
                _this.boomEff.spd_x_min = 150;
                _this.boomEff.spd_x_max = 250;
                _this.boomEff.spd_y_min = 150;
                _this.boomEff.spd_y_max = 300;
                _this.addChild(_this.boomEff);
                return _this;
            }
            Bloon.prototype.onClick = function () {
                this.click_area.visible = false;
                this.bloon.visible = false;
                this.boomEff.doEffect();
                SndMng.sfxBloon();
            };
            Bloon.prototype.update = function () {
                this.boomEff.update();
            };
            return Bloon;
        }(Phaser.Sprite));
        Client.Bloon = Bloon;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var TIME_OPEN = 400;
        var Card = (function (_super) {
            __extends(Card, _super);
            function Card(game, x, y, cid) {
                var _this = _super.call(this, game, x, y) || this;
                _this.isOpened = false;
                _this.isBusy = false;
                _this.isFound = false;
                _this.onOpenedSignal = new Phaser.Signal();
                _this.id = cid;
                _this.dummyMain = new Phaser.Sprite(game, 0, 0);
                _this.addChild(_this.dummyMain);
                var gr = new Phaser.Group(_this.game);
                _this.dummyFace = new Phaser.Sprite(game, 0, 0);
                _this.dummyFace.visible = false;
                _this.dummyMain.addChild(_this.dummyFace);
                _this.dummyShirt = new Phaser.Sprite(game, 0, 0);
                _this.dummyMain.addChild(_this.dummyShirt);
                _this.shirt = new Phaser.Sprite(game, 2, 2, 'game', Params.isTablet ? 'card_back_t' : 'card_back');
                _this.shirt.anchor.set(0.5);
                _this.dummyShirt.addChild(_this.shirt);
                var imgBack = new Phaser.Sprite(game, 0, 0, 'game', Params.isTablet ? 'card_blank_t' : 'card_blank');
                imgBack.anchor.set(0.5);
                imgBack.scale.set(1.04);
                _this.dummyFace.addChild(imgBack);
                try {
                    _this.img = new Phaser.Sprite(game, 0, 0, 'game', 'symbol' + String(cid));
                    _this.img.anchor.set(0.5);
                    if (!Params.isTablet)
                        _this.img.scale.set(0.75);
                    _this.dummyFace.addChild(_this.img);
                }
                catch (e) {
                    LogMng.error('fail loading card image: ' + e);
                }
                var fr_key = 'card_v' + String(MyMath.randomIntInRange(1, 2));
                if (Params.isTablet)
                    fr_key += '_t';
                var fr = new Phaser.Sprite(_this.game, 5, 5, 'game', fr_key);
                fr.anchor.set(0.5);
                _this.dummyFace.addChild(fr);
                return _this;
            }
            Card.prototype.doOpen = function () {
                if (this.isOpened)
                    return;
                this.isOpened = true;
                this.isBusy = true;
                this.game.add.tween(this.dummyShirt.scale).
                    to({ x: 0 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onOpenComplete1, this);
                this.game.add.tween(this.dummyMain.scale).
                    to({ x: 1.1, y: 1.1 }, TIME_OPEN, Phaser.Easing.Sinusoidal.InOut, true, 50, 0, true);
            };
            Card.prototype.onOpenComplete1 = function () {
                this.dummyFace.scale.x = 0;
                this.dummyFace.visible = true;
                this.dummyShirt.visible = false;
                this.game.add.tween(this.dummyFace.scale).to({ x: 1 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).
                    onComplete.add(this.onOpenComplete2, this);
            };
            Card.prototype.onOpenComplete2 = function () {
                this.isBusy = false;
                this.onOpenedSignal.dispatch();
            };
            Card.prototype.doClose = function () {
                if (!this.isOpened)
                    return;
                this.isOpened = false;
                this.isBusy = true;
                this.game.add.tween(this.dummyFace.scale).to({ x: 0 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onCloseComplete1, this);
                this.game.add.tween(this.dummyMain.scale).to({ x: 1.1, y: 1.1 }, TIME_OPEN, Phaser.Easing.Sinusoidal.InOut, true, 50, 0, true);
            };
            Card.prototype.onCloseComplete1 = function () {
                this.dummyFace.visible = false;
                this.dummyShirt.scale.x = 0;
                this.dummyShirt.visible = true;
                this.game.add.tween(this.dummyShirt.scale).to({ x: 1 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onCloseComplete2, this);
            };
            Card.prototype.onCloseComplete2 = function () {
                this.isBusy = false;
            };
            Card.prototype.doFound = function () {
                this.isOpened = false;
                this.isBusy = false;
                this.isFound = true;
            };
            Card.prototype.doHelp = function () {
                var tw1 = this.game.add.tween(this.dummyMain).to({ angle: -5 }, 500, Phaser.Easing.Sinusoidal.InOut);
                var tw2 = this.game.add.tween(this.dummyMain).to({ angle: 5 }, 1000, Phaser.Easing.Sinusoidal.InOut, false, 0, 1, true);
                var tw3 = this.game.add.tween(this.dummyMain).to({ angle: 0 }, 500, Phaser.Easing.Sinusoidal.InOut);
                tw1.chain(tw2);
                tw2.chain(tw3);
                tw1.start();
            };
            Card.prototype.update = function () {
            };
            return Card;
        }(Phaser.Sprite));
        Client.Card = Card;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var TIME_OPEN = 350;
        var TURN_WITH_ANIM = true;
        var TEST1 = true;
        var TEST2 = true;
        var TEST3 = true;
        var Card3 = (function (_super) {
            __extends(Card3, _super);
            function Card3(game, x, y, cid) {
                var _this = _super.call(this, game, x, y) || this;
                _this.isOpened = false;
                _this.isBusy = false;
                _this.isFound = false;
                _this.onOpenedSignal = new Phaser.Signal();
                _this.id = cid;
                if (TEST3) {
                    _this.shirt = new Phaser.Sprite(game, 2, 2, 'game', Params.isTablet ? 'card_back_t' : 'card_back');
                    _this.shirt.anchor.set(0.5);
                    _this.loadTexture('game', Params.isTablet ? 'card_back_t' : 'card_back');
                }
                else if (TEST2) {
                    _this.shirt = new Phaser.Sprite(game, 2, 2, 'game', Params.isTablet ? 'card_back_t' : 'card_back');
                    _this.shirt.anchor.set(0.5);
                    _this.addChild(_this.shirt);
                    _this.imgBack = new Phaser.Sprite(game, 0, 0, 'game', Params.isTablet ? 'card_blank_t' : 'card_blank');
                    _this.imgBack.anchor.set(0.5);
                    _this.imgBack.scale.set(1.04);
                    _this.addChild(_this.imgBack);
                    try {
                        _this.img = new Phaser.Sprite(game, 0, 0, 'game', 'symbol' + String(cid));
                        _this.img.anchor.set(0.5);
                        if (!Params.isTablet)
                            _this.img.scale.set(0.75);
                        _this.addChild(_this.img);
                    }
                    catch (e) {
                        LogMng.error('fail loading card image: ' + e);
                    }
                    var fr_key = 'card_v' + String(MyMath.randomIntInRange(1, 2));
                    if (Params.isTablet)
                        fr_key += '_t';
                    _this.fr = new Phaser.Sprite(_this.game, 5, 5, 'game', fr_key);
                    _this.fr.anchor.set(0.5);
                    _this.addChild(_this.fr);
                }
                else if (TEST1) {
                    _this.dummyMain = new Phaser.Sprite(game, 0, 0);
                    _this.addChild(_this.dummyMain);
                    _this.shirt = new Phaser.Sprite(game, 2, 2, 'game', Params.isTablet ? 'card_back_t' : 'card_back');
                    _this.shirt.anchor.set(0.5);
                    _this.dummyMain.addChild(_this.shirt);
                    _this.imgBack = new Phaser.Sprite(game, 0, 0, 'game', Params.isTablet ? 'card_blank_t' : 'card_blank');
                    _this.imgBack.anchor.set(0.5);
                    _this.imgBack.scale.set(1.04);
                    _this.dummyMain.addChild(_this.imgBack);
                    try {
                        _this.img = new Phaser.Sprite(game, 0, 0, 'game', 'symbol' + String(cid));
                        _this.img.anchor.set(0.5);
                        if (!Params.isTablet)
                            _this.img.scale.set(0.75);
                        _this.dummyMain.addChild(_this.img);
                    }
                    catch (e) {
                        LogMng.error('fail loading card image: ' + e);
                    }
                    var fr_key = 'card_v' + String(MyMath.randomIntInRange(1, 2));
                    if (Params.isTablet)
                        fr_key += '_t';
                    _this.fr = new Phaser.Sprite(_this.game, 5, 5, 'game', fr_key);
                    _this.fr.anchor.set(0.5);
                    _this.dummyMain.addChild(_this.fr);
                }
                else {
                    _this.dummyMain = new Phaser.Sprite(game, 0, 0);
                    _this.addChild(_this.dummyMain);
                    _this.dummyFace = new Phaser.Sprite(game, 0, 0);
                    _this.dummyFace.visible = false;
                    _this.dummyMain.addChild(_this.dummyFace);
                    _this.dummyShirt = new Phaser.Sprite(game, 0, 0);
                    _this.dummyMain.addChild(_this.dummyShirt);
                    _this.shirt = new Phaser.Sprite(game, 2, 2, 'game', Params.isTablet ? 'card_back_t' : 'card_back');
                    _this.shirt.anchor.set(0.5);
                    _this.dummyShirt.addChild(_this.shirt);
                    _this.imgBack = new Phaser.Sprite(game, 0, 0, 'game', Params.isTablet ? 'card_blank_t' : 'card_blank');
                    _this.imgBack.anchor.set(0.5);
                    _this.imgBack.scale.set(1.04);
                    _this.dummyFace.addChild(_this.imgBack);
                    try {
                        _this.img = new Phaser.Sprite(game, 0, 0, 'game', 'symbol' + String(cid));
                        _this.img.anchor.set(0.5);
                        if (!Params.isTablet)
                            _this.img.scale.set(0.75);
                        _this.dummyFace.addChild(_this.img);
                    }
                    catch (e) {
                        LogMng.error('fail loading card image: ' + e);
                    }
                    var fr_key = 'card_v' + String(MyMath.randomIntInRange(1, 2));
                    if (Params.isTablet)
                        fr_key += '_t';
                    _this.fr = new Phaser.Sprite(_this.game, 5, 5, 'game', fr_key);
                    _this.fr.anchor.set(0.5);
                    _this.dummyFace.addChild(_this.fr);
                }
                return _this;
            }
            Card3.prototype.doOpen = function () {
                if (this.isOpened)
                    return;
                this.isOpened = true;
                this.isBusy = true;
                if (TURN_WITH_ANIM) {
                    this.game.add.tween(this.dummyShirt.scale).to({ x: 0 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onOpenComplete1, this);
                    this.game.add.tween(this.dummyMain.scale).to({ x: 1.1, y: 1.1 }, TIME_OPEN, Phaser.Easing.Sinusoidal.InOut, true, 50, 0, true);
                }
                else {
                    this.onOpenComplete1();
                }
            };
            Card3.prototype.onOpenComplete1 = function () {
                if (TURN_WITH_ANIM)
                    this.dummyFace.scale.x = 0;
                this.dummyFace.visible = true;
                this.dummyShirt.visible = false;
                if (TURN_WITH_ANIM) {
                    this.game.add.tween(this.dummyFace.scale).to({ x: 1 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onOpenComplete2, this);
                }
                else {
                    this.onOpenComplete2();
                }
            };
            Card3.prototype.onOpenComplete2 = function () {
                this.isBusy = false;
                this.onOpenedSignal.dispatch();
            };
            Card3.prototype.doClose = function () {
                if (!this.isOpened)
                    return;
                this.isOpened = false;
                this.isBusy = true;
                if (TURN_WITH_ANIM) {
                    this.game.add.tween(this.dummyFace.scale).to({ x: 0 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onCloseComplete1, this);
                    this.game.add.tween(this.dummyMain.scale).to({ x: 1.1, y: 1.1 }, TIME_OPEN, Phaser.Easing.Sinusoidal.InOut, true, 50, 0, true);
                }
                else {
                    this.onCloseComplete1();
                }
            };
            Card3.prototype.onCloseComplete1 = function () {
                this.dummyFace.visible = false;
                if (TURN_WITH_ANIM)
                    this.dummyShirt.scale.x = 0;
                this.dummyShirt.visible = true;
                if (TURN_WITH_ANIM) {
                    this.game.add.tween(this.dummyShirt.scale).to({ x: 1 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onCloseComplete2, this);
                }
                else {
                    this.onCloseComplete2();
                }
            };
            Card3.prototype.onCloseComplete2 = function () {
                this.isBusy = false;
            };
            Card3.prototype.doFound = function () {
                this.isOpened = false;
                this.isBusy = false;
                this.isFound = true;
            };
            Card3.prototype.doHelp = function () {
                var tw1 = this.game.add.tween(this.dummyMain).to({ angle: -5 }, 500, Phaser.Easing.Sinusoidal.InOut);
                var tw2 = this.game.add.tween(this.dummyMain).to({ angle: 5 }, 1000, Phaser.Easing.Sinusoidal.InOut, false, 0, 1, true);
                var tw3 = this.game.add.tween(this.dummyMain).to({ angle: 0 }, 500, Phaser.Easing.Sinusoidal.InOut);
                tw1.chain(tw2);
                tw2.chain(tw3);
                tw1.start();
            };
            Card3.prototype.update = function () {
            };
            return Card3;
        }(Phaser.Sprite));
        Client.Card3 = Card3;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var TIME_OPEN = 350;
        var TURN_WITH_ANIM = true;
        var Card4 = (function () {
            function Card4(game, x, y, cid) {
                this._imgScale = 1;
                this._imgBackScale = 1;
                this._frScale = 1;
                this._shirtScale = 1;
                this._dummyMainScale = new Phaser.Point(1, 1);
                this._dummyShirtScale = new Phaser.Point(1, 1);
                this._dummyFaceScale = new Phaser.Point(1, 1);
                this.x = 0;
                this.y = 0;
                this.sx = 0;
                this.sy = 0;
                this.angle = 0;
                this.alpha = 1;
                this.isOpened = false;
                this.isBusy = false;
                this.isFound = false;
                this.onInputDownSignal = new Phaser.Signal();
                this.onOpenedSignal = new Phaser.Signal();
                this._game = game;
                this.x = x;
                this.y = y;
                this.id = cid;
                this._shirt = new Phaser.Sprite(game, 2, 2, 'game', Params.isTablet ? 'card_back_t' : 'card_back');
                this._shirt.anchor.set(0.5);
                this._shirt.inputEnabled = true;
                this._shirt.input.useHandCursor = true;
                this._shirt.events.onInputDown.add(this.onShirtInputDown, this);
                this._imgBack = new Phaser.Sprite(game, 0, 0, 'game', Params.isTablet ? 'card_blank_t' : 'card_blank');
                this._imgBack.anchor.set(0.5);
                this._imgBackScale = 1.04;
                try {
                    this._img = new Phaser.Sprite(game, 0, 0, 'game', 'symbol' + String(cid));
                    this._img.anchor.set(0.5);
                    if (!Params.isTablet) {
                        this._img.scale.set(0.75);
                        this._imgScale = 0.75;
                    }
                }
                catch (e) {
                    LogMng.error('fail loading card image: ' + e);
                }
                var fr_key = 'card_v' + String(MyMath.randomIntInRange(1, 2));
                if (Params.isTablet)
                    fr_key += '_t';
                this._fr = new Phaser.Sprite(this._game, 5, 5, 'game', fr_key);
                this._fr.anchor.set(0.5);
                this.setFaceVisible(false);
                this.setShirtVisible(true);
            }
            Card4.prototype.onShirtInputDown = function () {
                this.onInputDownSignal.dispatch(this);
            };
            Card4.prototype.setFaceVisible = function (aVal) {
                this._imgBack.visible = aVal;
                this._img.visible = aVal;
                this._fr.visible = aVal;
            };
            Card4.prototype.setShirtVisible = function (aVal) {
                this._shirt.visible = aVal;
            };
            Card4.prototype.setParent = function (aParent) {
                aParent.addChild(this._imgBack);
                aParent.addChild(this._img);
                aParent.addChild(this._fr);
                aParent.addChild(this._shirt);
                this._parent = aParent;
            };
            Card4.prototype.bringToTop = function () {
                if (this._parent != null) {
                    this._parent.setChildIndex(this._imgBack, this._parent.children.length - 1);
                    this._parent.setChildIndex(this._img, this._parent.children.length - 1);
                    this._parent.setChildIndex(this._fr, this._parent.children.length - 1);
                    this._parent.setChildIndex(this._shirt, this._parent.children.length - 1);
                }
            };
            Card4.prototype.removeClickEvent = function () {
                this._shirt.events.onInputDown.removeAll(this);
            };
            Card4.prototype.doOpen = function () {
                if (this.isOpened)
                    return;
                this.isOpened = true;
                this.isBusy = true;
                if (TURN_WITH_ANIM) {
                    this._game.add.tween(this._dummyShirtScale).to({ x: 0 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onOpenComplete1, this);
                    this._game.add.tween(this._dummyMainScale).to({ x: 1.1, y: 1.1 }, TIME_OPEN, Phaser.Easing.Sinusoidal.InOut, true, 50, 0, true);
                }
                else {
                    this.onOpenComplete1();
                }
            };
            Card4.prototype.onOpenComplete1 = function () {
                if (TURN_WITH_ANIM)
                    this._dummyFaceScale.x = 0;
                this.updateScale();
                this.setFaceVisible(true);
                this.setShirtVisible(false);
                if (TURN_WITH_ANIM) {
                    this._game.add.tween(this._dummyFaceScale).to({ x: 1 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onOpenComplete2, this);
                }
                else {
                    this.onOpenComplete2();
                }
            };
            Card4.prototype.onOpenComplete2 = function () {
                this.isBusy = false;
                this.onOpenedSignal.dispatch();
            };
            Card4.prototype.doClose = function () {
                if (!this.isOpened)
                    return;
                this.isOpened = false;
                this.isBusy = true;
                if (TURN_WITH_ANIM) {
                    this._game.add.tween(this._dummyFaceScale).to({ x: 0 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onCloseComplete1, this);
                    this._game.add.tween(this._dummyMainScale).to({ x: 1.1, y: 1.1 }, TIME_OPEN, Phaser.Easing.Sinusoidal.InOut, true, 50, 0, true);
                }
                else {
                    this.onCloseComplete1();
                }
            };
            Card4.prototype.onCloseComplete1 = function () {
                this.setFaceVisible(false);
                if (TURN_WITH_ANIM)
                    this._dummyShirtScale.x = 0;
                this.updateScale();
                this.setShirtVisible(true);
                if (TURN_WITH_ANIM) {
                    this._game.add.tween(this._dummyShirtScale).to({ x: 1 }, TIME_OPEN / 2, Phaser.Easing.Linear.None, true).onComplete.add(this.onCloseComplete2, this);
                }
                else {
                    this.onCloseComplete2();
                }
            };
            Card4.prototype.onCloseComplete2 = function () {
                this.isBusy = false;
            };
            Card4.prototype.doFound = function () {
                this.isOpened = false;
                this.isBusy = false;
                this.isFound = true;
            };
            Card4.prototype.doHelp = function () {
                var tw1 = this._game.add.tween(this).to({ angle: -5 }, 500, Phaser.Easing.Sinusoidal.InOut);
                var tw2 = this._game.add.tween(this).to({ angle: 5 }, 1000, Phaser.Easing.Sinusoidal.InOut, false, 0, 1, true);
                var tw3 = this._game.add.tween(this).to({ angle: 0 }, 500, Phaser.Easing.Sinusoidal.InOut);
                tw1.chain(tw2);
                tw2.chain(tw3);
                tw1.start();
            };
            Card4.prototype.kill = function () {
                this._img.kill();
                this._imgBack.kill();
                this._fr.kill();
                this._shirt.kill();
            };
            Card4.prototype.updateScale = function () {
                this._imgBack.scale.set(this._imgBackScale * this.sx * this._dummyFaceScale.x * this._dummyMainScale.x, this._imgBackScale * this.sy * this._dummyFaceScale.y * this._dummyMainScale.y);
                this._img.scale.set(this._imgScale * this.sx * this._dummyFaceScale.x * this._dummyMainScale.x, this._imgScale * this.sy * this._dummyFaceScale.y * this._dummyMainScale.y);
                this._fr.scale.set(this._frScale * this.sx * this._dummyFaceScale.x * this._dummyMainScale.x, this._frScale * this.sy * this._dummyFaceScale.y * this._dummyMainScale.y);
                this._shirt.scale.set(this._shirtScale * this.sx * this._dummyShirtScale.x * this._dummyMainScale.x, this._shirtScale * this.sy * this._dummyShirtScale.y * this._dummyMainScale.y);
            };
            Card4.prototype.updateAlpha = function () {
                this._imgBack.alpha = this.alpha;
                this._img.alpha = this.alpha;
                this._fr.alpha = this.alpha;
                this._shirt.alpha = this.alpha;
            };
            Card4.prototype.update = function () {
                this._imgBack.position.set(this.x, this.y);
                this._img.position.set(this.x, this.y);
                this._fr.position.set(this.x, this.y);
                this._shirt.position.set(this.x, this.y);
                this._shirt.angle = this.angle;
                this.updateScale();
                this.updateAlpha();
            };
            return Card4;
        }());
        Client.Card4 = Card4;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var Rocket = (function (_super) {
            __extends(Rocket, _super);
            function Rocket(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                var smoke = new Phaser.Sprite(game, 0, -60, 'final', 'smoke');
                smoke.anchor.set(0.5, 0);
                _this.addChild(smoke);
                _this.fire = new Phaser.Sprite(game, 0 - 11, 75, 'final', 'fire');
                _this.fire.anchor.set(0.5, 0);
                _this.fire.scale.y = 0.9;
                _this.game.add.tween(_this.fire.scale).to({ y: 1.1 }, 100, null, true, 0, -1, true);
                _this.addChild(_this.fire);
                var rocket = new Phaser.Sprite(game, 0 - 11, 120, 'final', 'Rocket');
                rocket.anchor.set(0.5, 1);
                _this.addChild(rocket);
                return _this;
            }
            Rocket.prototype.free = function () {
                this.game.tweens.removeFrom(this.fire.scale);
                this.fire = null;
            };
            return Rocket;
        }(Phaser.Sprite));
        Client.Rocket = Rocket;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var Boot = (function (_super) {
            __extends(Boot, _super);
            function Boot() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            Boot.prototype.preload = function () {
                this.load.image('bg', './assets/sprites/memory/bg.png');
            };
            Boot.prototype.create = function () {
                this.stage.setBackgroundColor(0x37b4f3);
                this.input.maxPointers = 1;
                this.stage.disableVisibilityChange = true;
                ScaleManager.init(this.game, Config.DOM_PARENT_ID, Config.GW, Config.GH, Config.GSW, Config.GSH);
                LogMng.system('log mode: ' + LogMng.getMode());
                this.time.events.add(100, this.onWaitComplete, this);
            };
            Boot.prototype.onWaitComplete = function () {
                Params.isTablet = window.innerWidth / window.innerHeight <= 3 / 2;
                this.game.state.start(States.PRELOADER, true, false);
            };
            return Boot;
        }(Phaser.State));
        Client.Boot = Boot;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var GameCtrl = (function (_super) {
            __extends(GameCtrl, _super);
            function GameCtrl() {
                var _this = _super !== null && _super.apply(this, arguments) || this;
                _this.fps_timer = 0;
                return _this;
            }
            GameCtrl.prototype.create = function () {
                var loadMng = new LoadMng.SceneLoader(this.game);
                loadMng.startLoading();
                this.bg = new Phaser.Sprite(this.game, 0, 0, 'bg');
                this.add.existing(this.bg);
                this.gameMng = new Client.GameMng2(this.game, 0, 0);
                this.gameMng.onGameCompleteEvent.add(this.onGameComplete, this);
                this.add.existing(this.gameMng);
                this.dummyGui = this.add.sprite(0, 0);
                var difs = [4, 8, 12, 18, 24, 28, 32, 36, 40];
                if (Params.q_version == 1) {
                    difs = [];
                    for (var i = 1; i <= 20; i++) {
                        difs.push(i * 2);
                    }
                }
                this.topPanel = new Client.TopPanel(this.game, Config.GW / 2, 0, difs, Params.q_dif);
                this.topPanel.onHelpClickSignal.add(this.onHelpClicked, this);
                this.topPanel.onDifChangeSignal.add(this.onDifChanged, this);
                this.topPanel.onPanelOpenSignal.add(this.onTopPanelOpen, this);
                this.topPanel.onPanelHidedSignal.add(this.onTopPanelHided, this);
                this.updateTopPanel();
                this.topPanel.setTopPanelVisible(Params.q_version == 2);
                this.topPanel.openPanel();
                this.dummyGui.addChild(this.topPanel);
                this.winWnd = new Client.WinWindow(this.game, Config.GW / 2, Config.GH / 2);
                this.winWnd.onRestartClickEvent.add(this.onRestartClick, this);
                this.winWnd.onCloseClickEvent.add(this.onCloseClick, this);
                this.dummyGui.addChild(this.winWnd);
                this.onWndResize();
                ScaleManager.onWindowResize.add(this.onWndResize, this);
                this.gameMng.restartGame(this.topPanel.getPiecesCount());
                SndMng.sfxStartGame();
                SndMng.fadeInMusic();
                if (Config.SHOW_FPS) {
                    this.game.time.advancedTiming = true;
                    this.fps_text = new Phaser.Text(this.game, 110, 180, '');
                    this.fps_text.fontSize = 40;
                    this.add.existing(this.fps_text);
                }
            };
            GameCtrl.prototype.onWndResize = function () {
                if (ScaleManager.gameViewH < Config.GH) {
                    this.gameMng.setAvailableHeight(ScaleManager.gameViewH - 100);
                }
                else {
                    this.gameMng.setAvailableHeight(Config.GH - 100);
                }
            };
            GameCtrl.prototype.onTopPanelOpen = function () {
                this.gameMng.setTopPadding(140);
            };
            GameCtrl.prototype.onTopPanelHided = function () {
                this.gameMng.setTopPadding(114);
            };
            GameCtrl.prototype.onHelpClicked = function (isHelp) {
                this.gameMng.setHelpEnabled(isHelp);
            };
            GameCtrl.prototype.onDifChanged = function (aDif) {
                this.gameMng.restartGame(this.topPanel.getPiecesCount());
            };
            GameCtrl.prototype.onGameComplete = function () {
                this.bg.visible = false;
                this.winWnd.showScreen();
            };
            GameCtrl.prototype.onRestartClick = function () {
                this.winWnd.hideScreen();
                this.gameMng.restartGame(this.topPanel.getPiecesCount());
                SndMng.sfxStartGame();
                this.bg.visible = true;
            };
            GameCtrl.prototype.onCloseClick = function () {
                pressedClose(Params.pairs_opened_cnt);
            };
            GameCtrl.prototype.updateTopPanel = function () {
                if (ScaleManager.gameViewH < Config.GH) {
                    this.topPanel.y = (Config.GH - ScaleManager.gameViewH) / 2 - 1;
                }
                else {
                    this.topPanel.y = 0;
                }
                this.topPanel.update();
            };
            GameCtrl.prototype.updateGameDummy = function () {
                if (ScaleManager.gameViewH < Config.GH) {
                }
                else {
                    this.topPanel.y = 0;
                }
            };
            GameCtrl.prototype.update = function () {
                this.updateTopPanel();
                this.updateGameDummy();
                if (this.winWnd != null)
                    this.winWnd.update();
            };
            GameCtrl.prototype.render = function () {
                if (Config.SHOW_FPS) {
                    var dt = this.game.time.elapsed * 0.001;
                    this.fps_timer -= dt;
                    if (this.fps_timer <= 0) {
                        this.fps_timer = 1;
                        var s_fps = 'FPS: ' + this.game.time.fps.toFixed(0) || '--';
                        this.fps_text.text = s_fps;
                    }
                }
            };
            return GameCtrl;
        }(Phaser.State));
        Client.GameCtrl = GameCtrl;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var MainMenu = (function (_super) {
            __extends(MainMenu, _super);
            function MainMenu() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            MainMenu.prototype.create = function () {
                this.game.state.start(States.GAME, true, false);
            };
            MainMenu.prototype.update = function () {
            };
            return MainMenu;
        }(Phaser.State));
        Client.MainMenu = MainMenu;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var PhaserGame;
(function (PhaserGame) {
    var Client;
    (function (Client) {
        var Preloader = (function (_super) {
            __extends(Preloader, _super);
            function Preloader() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            Preloader.prototype.preload = function () {
                var q_v = MyUtils.getQueryValue('v');
                if (q_v != null && q_v != undefined && q_v >= 1 && q_v <= 2)
                    Params.q_version = q_v;
                LogMng.debug('version = ' + Params.q_version);
                var q_dif = MyUtils.getQueryValue('dif');
                if (q_dif != null && q_dif != undefined && q_dif > 0)
                    Params.q_dif = q_dif;
                LogMng.debug('dif = ' + Params.q_dif);
                var q_path = MyUtils.getQueryValue('img');
                if (q_path != null && q_path != undefined && q_path != '')
                    Params.q_path = q_path;
                LogMng.debug('path = ' + Params.q_path);
                var q_is_global_path = MyUtils.getQueryValue('global');
                if (q_is_global_path != null && q_is_global_path != undefined && q_is_global_path != '')
                    Params.q_is_global_path = Number(q_is_global_path) == 1;
                LogMng.debug('is_global_pic = ' + Params.q_is_global_path);
                this.add.sprite(0, 0, 'bg');
                this.load.atlasJSONArray('game', './assets/atlases/memory_game_mid_1200x900.png', './assets/atlases/memory_game_mid_1200x900.json');
                this.load.atlasJSONArray('final', './assets/atlases/final_mid_1200x900.png', './assets/atlases/final_mid_1200x900.json');
                SndMng.init(this.game, true);
                var sndFiles = SndMng.LOAD_SOUNDS;
                for (var i = 0; i < sndFiles.length; i++) {
                    var ogg = './assets/sounds/ogg/' + sndFiles[i] + '.ogg';
                    var mp3 = './assets/sounds/mp3/' + sndFiles[i] + '.mp3';
                    this.load.audio(sndFiles[i], [mp3]);
                }
            };
            Preloader.prototype.create = function () {
                Params.isIOS =
                    this.game.device.iOS ||
                        this.game.device.iPhone ||
                        this.game.device.iPhone4 ||
                        this.game.device.iPad ||
                        this.game.device.mobileSafari;
                this.startMainMenu();
            };
            Preloader.prototype.startMainMenu = function () {
                this.game.state.start(States.MAINMENU, true, false);
            };
            return Preloader;
        }(Phaser.State));
        Client.Preloader = Preloader;
    })(Client = PhaserGame.Client || (PhaserGame.Client = {}));
})(PhaserGame || (PhaserGame = {}));
var States;
(function (States) {
    States.BOOT = 'Boot';
    States.PRELOADER = 'Preloader';
    States.MAINMENU = 'MainMenu';
    States.GAME = 'Game';
})(States || (States = {}));
var LogMng;
(function (LogMng) {
    LogMng.MODE_DEBUG = 'MODE_DEBUG';
    LogMng.MODE_RELEASE = 'MODE_RELEASE';
    var DEBUG = 'DEBUG';
    var INFO = 'INFO';
    var NETWORK = 'NETWORK';
    var WARNING = 'WARNING';
    var ERROR = 'ERROR';
    var mode = LogMng.MODE_DEBUG;
    var levels = [DEBUG, INFO, NETWORK, WARNING, ERROR];
    function setMode(aMode) {
        mode = aMode;
        switch (mode) {
            case LogMng.MODE_DEBUG:
                levels = [DEBUG, INFO, NETWORK, WARNING, ERROR];
                break;
            case LogMng.MODE_RELEASE:
                levels = [WARNING, ERROR];
                break;
        }
    }
    LogMng.setMode = setMode;
    function getMode() {
        return mode;
    }
    LogMng.getMode = getMode;
    function getCSS(bgColor) {
        return 'background: ' + bgColor + ';' +
            'background-repeat: no-repeat;' +
            'color: #1df9a8;' +
            'line-height: 16px;' +
            'padding: 1px 0;' +
            'margin: 0;' +
            'user-select: none;' +
            '-webkit-user-select: none;' +
            '-moz-user-select: none;';
    }
    ;
    function getLink(color) {
        return 'background: ' + color + ';' +
            'background-repeat: no-repeat;' +
            'font-size: 12px;' +
            'color: #446d96;' +
            'line-height: 14px';
    }
    ;
    function log(aMsg, aLevel) {
        if (aLevel === void 0) { aLevel = DEBUG; }
        if (levels.indexOf(aLevel) < 0)
            return;
        var css = '';
        switch (aLevel) {
            case INFO:
                css = 'background: #308AE4; color: #fff; padding: 1px 4px';
                break;
            case WARNING:
                css = 'background: #f7a148; color: #fff; padding: 1px 4px';
                break;
            case ERROR:
                css = 'background: #DB5252; color: #fff; padding: 1px 4px';
                break;
            case NETWORK:
                css = 'background: #7D2998; color: #fff; padding: 1px 4px';
                break;
            case DEBUG:
            default:
                css = 'background: #ADADAD; color: #fff; padding: 1px 4px';
        }
        console.log("%c%s", css, aLevel, aMsg);
    }
    ;
    function system(aMsg, aLink) {
        if (aLink === void 0) { aLink = ''; }
        console.log("%c %c %c %s %c %c %c %c%s", getCSS('#5C6166'), getCSS('#4F5357'), getCSS('#313335'), aMsg, getCSS('#4F5357'), getCSS('#5C6166'), getLink('none'), getLink('none'), aLink);
    }
    LogMng.system = system;
    function debug(aMsg) {
        log(aMsg, DEBUG);
    }
    LogMng.debug = debug;
    function info(aMsg) {
        log(aMsg, INFO);
    }
    LogMng.info = info;
    function net(aMsg) {
        log(aMsg, NETWORK);
    }
    LogMng.net = net;
    function warn(aMsg) {
        log(aMsg, WARNING);
    }
    LogMng.warn = warn;
    function error(aMsg) {
        log(aMsg, ERROR);
    }
    LogMng.error = error;
})(LogMng || (LogMng = {}));
var MyMath;
(function (MyMath) {
    var RectABCD = (function () {
        function RectABCD(a, b, c, d) {
            this.a = a;
            this.b = b;
            this.c = c;
            this.d = d;
        }
        return RectABCD;
    }());
    MyMath.RectABCD = RectABCD;
    function randomInRange(aMin, aMax) {
        return Math.random() * Math.abs(aMax - aMin) + aMin;
    }
    MyMath.randomInRange = randomInRange;
    function randomIntInRange(aMin, aMax) {
        return Math.round(randomInRange(aMin, aMax));
    }
    MyMath.randomIntInRange = randomIntInRange;
    function toRadian(aDeg) {
        return aDeg * Math.PI / 180;
    }
    MyMath.toRadian = toRadian;
    function toDeg(aRad) {
        return aRad * 180 / Math.PI;
    }
    MyMath.toDeg = toDeg;
    function IsPointInTriangle(ax, ay, bx, by, cx, cy, px, py) {
        var b0x, b0y, c0x, c0y, p0x, p0y;
        var m, l;
        var res = false;
        b0x = bx - ax;
        b0y = by - ay;
        c0x = cx - ax;
        c0y = cy - ay;
        p0x = px - ax;
        p0y = py - ay;
        m = (p0x * b0y - b0x * p0y) / (c0x * b0y - b0x * c0y);
        if (m >= 0 && m <= 1) {
            l = (p0x - m * c0x) / b0x;
            if (l >= 0 && (m + l) <= 1)
                res = true;
        }
        return res;
    }
    MyMath.IsPointInTriangle = IsPointInTriangle;
    function isPointInRect(rect, p) {
        return IsPointInTriangle(rect.a.x, rect.a.y, rect.b.x, rect.b.y, rect.c.x, rect.c.y, p.x, p.y) &&
            IsPointInTriangle(rect.c.x, rect.c.y, rect.d.x, rect.d.y, rect.a.x, rect.a.y, p.x, p.y);
    }
    MyMath.isPointInRect = isPointInRect;
    function isCirclesIntersect(x1, y1, r1, x2, y2, r2) {
        var veclen = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
        return veclen <= r1 + r2;
    }
    MyMath.isCirclesIntersect = isCirclesIntersect;
})(MyMath || (MyMath = {}));
var MyUtils;
(function (MyUtils) {
    var query_values = null;
    function readQueryValues() {
        var vals = {};
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (typeof vals[pair[0]] === "undefined") {
                vals[pair[0]] = decodeURIComponent(pair[1]);
            }
            else if (typeof vals[pair[0]] === "string") {
                var arr = [vals[pair[0]], decodeURIComponent(pair[1])];
                vals[pair[0]] = arr;
            }
            else {
                vals[pair[0]].push(decodeURIComponent(pair[1]));
            }
        }
        query_values = vals;
    }
    function getQueryValue(aValName) {
        if (query_values == null)
            readQueryValues();
        return query_values[aValName];
    }
    MyUtils.getQueryValue = getQueryValue;
    function updateBackColor(aIsFinal) {
        var b = document.getElementsByTagName('body')[0];
        aIsFinal ? b.style.background = '#37b4f3' : b.style.background = '#fd6f52';
    }
    MyUtils.updateBackColor = updateBackColor;
})(MyUtils || (MyUtils = {}));
var ScaleManager = (function () {
    function ScaleManager() {
    }
    ScaleManager.init = function (aGame, aDomId, GW, GH, GSW, GSH) {
        var _this = this;
        this.game = aGame;
        this.dom_id = aDomId;
        this.dom = document.getElementById(this.dom_id);
        this.game_w = GW;
        this.game_h = GH;
        this.game_sw = GSW;
        this.game_sh = GSH;
        aGame.scale.scaleMode = Phaser.ScaleManager.USER_SCALE;
        ScaleManager.SizeCalculation();
        window.onresize = function () {
            ScaleManager.SizeCalculation();
            _this.onWindowResize.dispatch();
        };
    };
    ScaleManager.doEventOriChange = function () {
        this.onOrientationChanged.dispatch(this.isPortrait);
    };
    ScaleManager.SizeCalculation = function () {
        var wnd = {
            w: window.innerWidth,
            h: window.innerHeight
        };
        var g = {
            w: ScaleManager.game_w,
            h: ScaleManager.game_h,
            sw: ScaleManager.game_sw,
            sh: ScaleManager.game_sh
        };
        var gw;
        var gh;
        if (g.h / g.w > wnd.h / wnd.w) {
            if (g.sh / g.w > wnd.h / wnd.w) {
                gh = wnd.h * g.h / g.sh;
                gw = gh * g.w / g.h;
            }
            else {
                gw = wnd.w;
                gh = gw * g.h / g.w;
            }
        }
        else {
            if (g.h / g.sw > wnd.h / wnd.w) {
                gh = wnd.h;
                gw = gh * g.w / g.h;
            }
            else {
                gw = wnd.w * g.w / g.sw;
                gh = gw * g.h / g.w;
            }
        }
        var sclX = gw / g.sw;
        var sclY = gh / g.sh;
        var newScale = Math.min(sclX, sclY);
        ScaleManager.game.scale.setUserScale(newScale, newScale, 0, 0);
        this.dtx = (wnd.w - gw) / 2;
        this.dty = (wnd.h - gh) / 2;
        this.gameViewW = this.game_w + 2 * this.dtx / newScale;
        if (this.gameViewW > this.game_w)
            this.gameViewW = this.game_w;
        this.gameViewH = this.game_h + 2 * this.dty / newScale;
        if (this.gameViewH > this.game_h)
            this.gameViewH = this.game_h;
        this.dom.style.marginLeft = Math.round(this.dtx).toString() + 'px';
        this.dom.style.marginTop = Math.round(this.dty).toString() + 'px';
        this.dom.style.maxWidth = String(gw) + 'px';
        this.dom.style.maxHeight = String(gh) + 'px';
        ScaleManager.game.scale.refresh();
        var oldOri = this.isPortrait;
        this.isPortrait = wnd.h > wnd.w;
        if (this.isPortrait != oldOri)
            this.doEventOriChange();
    };
    ScaleManager.handleIncorrect = function () {
        if (!this.game.device.desktop) {
            document.getElementById("turn").style.display = "block";
            ScaleManager.game.world.isPaused = true;
        }
    };
    ScaleManager.handleCorrect = function () {
        if (!this.game.device.desktop) {
            document.getElementById("turn").style.display = "none";
            ScaleManager.game.world.isPaused = false;
        }
        setTimeout("window.scrollTo(0,0)", 1000);
    };
    ScaleManager.dom_id = '';
    ScaleManager.dtx = 0;
    ScaleManager.dty = 0;
    ScaleManager.onOrientationChanged = new Phaser.Signal();
    ScaleManager.onWindowResize = new Phaser.Signal();
    return ScaleManager;
}());
var SndMng;
(function (SndMng) {
    var MUSIC = 'music';
    SndMng.LOAD_SOUNDS = ['found', 'help', 'click', 'main_start', 'put2', 'bloon1', 'bloon2', 'bloon3', 'bloon4', 'win'];
    var MUS_MAX_VOL = 0.8;
    var game;
    var enabled;
    var music = null;
    function init(aGame, aEnabled) {
        game = aGame;
        enabled = aEnabled;
    }
    SndMng.init = init;
    function fadeInMusic(aVolFrom, aVolEnd, aDuration) {
        if (aVolFrom === void 0) { aVolFrom = 0; }
        if (aVolEnd === void 0) { aVolEnd = 1; }
        if (aDuration === void 0) { aDuration = 500; }
        if (aVolEnd > MUS_MAX_VOL)
            aVolEnd = MUS_MAX_VOL;
        if (music == null) {
            return;
        }
        if (enabled) {
            game.tweens.removeFrom(music);
            if (!music.isPlaying) {
                music.volume = aVolFrom;
                music.play();
            }
            game.add.tween(music).to({ volume: aVolEnd }, aDuration, Phaser.Easing.Linear.None, true);
        }
    }
    SndMng.fadeInMusic = fadeInMusic;
    function fadeOutMusic(aVol, aDuration) {
        if (aVol === void 0) { aVol = 0; }
        if (aDuration === void 0) { aDuration = 500; }
        if (music == null)
            return;
        game.tweens.removeFrom(music);
        return game.add.tween(music).to({ volume: aVol }, aDuration, Phaser.Easing.Linear.None, true);
    }
    SndMng.fadeOutMusic = fadeOutMusic;
    function setEnabled(aEnabled) {
        enabled = aEnabled;
        if (enabled) {
            fadeInMusic();
        }
        else {
            fadeOutMusic().onComplete.add(function () { music.stop(); });
        }
    }
    SndMng.setEnabled = setEnabled;
    function getEnabled() {
        return enabled;
    }
    SndMng.getEnabled = getEnabled;
    function sfxPlay(aName, aVol) {
        if (aVol === void 0) { aVol = 1; }
        if (!enabled)
            return;
        game.add.audio(aName, aVol).play();
    }
    SndMng.sfxPlay = sfxPlay;
    function sfxClick() {
        sfxPlay('click');
    }
    SndMng.sfxClick = sfxClick;
    function sfxStartGame() {
        sfxPlay('main_start');
    }
    SndMng.sfxStartGame = sfxStartGame;
    function sfxPut() {
        sfxPlay('put2');
    }
    SndMng.sfxPut = sfxPut;
    function sfxWin() {
        sfxPlay('win', 1);
    }
    SndMng.sfxWin = sfxWin;
    function sfxBloon() {
        sfxPlay('bloon' + String(MyMath.randomIntInRange(1, 4)));
    }
    SndMng.sfxBloon = sfxBloon;
})(SndMng || (SndMng = {}));
var TextUtils;
(function (TextUtils) {
    function addZero(aNum, aLen) {
        var text = String(aNum);
        while (text.length < aLen)
            text = '0' + text;
        return text;
    }
    TextUtils.addZero = addZero;
    function sizingBitmapTextByW(aBmpText, aW, aInc, aDec) {
        if (aBmpText.text == '' || aBmpText.height == 0 || aBmpText.width == 0) {
            LogMng.debug('TextUtils.ts sizingBitmapTextByW(): aBmpText.text == ""');
            LogMng.debug('TextUtils.ts sizingBitmapTextByW(): aBmpText.width = ' + aBmpText.width);
            LogMng.debug('TextUtils.ts sizingBitmapTextByW(): aBmpText.height = ' + aBmpText.height);
            return;
        }
        if (aInc) {
            if (aBmpText.width < aW) {
                while (aBmpText.width < aW) {
                    aBmpText.fontSize++;
                }
            }
        }
        if (aDec) {
            if (aBmpText.width > aW) {
                while (aBmpText.width > aW) {
                    aBmpText.fontSize--;
                }
            }
        }
    }
    TextUtils.sizingBitmapTextByW = sizingBitmapTextByW;
    function sizingBitmapTextByH(aBmpText, aH, aInc, aDec) {
        if (aBmpText.text == '' || aBmpText.height == 0 || aBmpText.width == 0) {
            LogMng.debug('TextUtils.ts sizingBitmapTextByH(): aBmpText.text == ""');
            LogMng.debug('TextUtils.ts sizingBitmapTextByH(): aBmpText.width = ' + aBmpText.width);
            LogMng.debug('TextUtils.ts sizingBitmapTextByH(): aBmpText.height = ' + aBmpText.height);
            return;
        }
        if (aInc) {
            if (aBmpText.height < aH) {
                while (aBmpText.height < aH) {
                    aBmpText.fontSize++;
                }
            }
        }
        if (aDec) {
            if (aBmpText.height > aH) {
                while (aBmpText.height > aH) {
                    aBmpText.fontSize--;
                }
            }
        }
    }
    TextUtils.sizingBitmapTextByH = sizingBitmapTextByH;
})(TextUtils || (TextUtils = {}));
var PhaserNineSlice;
(function (PhaserNineSlice) {
    var NineSlice = (function (_super) {
        __extends(NineSlice, _super);
        function NineSlice(game, x, y, key, frame, width, height, data) {
            var _this = _super.call(this, game, x, y, key, frame) || this;
            _this.baseTexture = _this.texture.baseTexture;
            _this.baseFrame = _this.texture.frame;
            if (frame !== null && !data) {
                data = game.cache.getNineSlice(frame);
            }
            else if (!data) {
                data = game.cache.getNineSlice(key);
            }
            if (undefined === data) {
                return _this;
            }
            _this.topSize = data.top;
            if (!data.left) {
                _this.leftSize = _this.topSize;
            }
            else {
                _this.leftSize = data.left;
            }
            if (!data.right) {
                _this.rightSize = _this.leftSize;
            }
            else {
                _this.rightSize = data.right;
            }
            if (!data.bottom) {
                _this.bottomSize = _this.topSize;
            }
            else {
                _this.bottomSize = data.bottom;
            }
            _this.loadTexture(new Phaser.RenderTexture(_this.game, _this.localWidth, _this.localHeight));
            _this.resize(width, height);
            return _this;
        }
        NineSlice.prototype.renderTexture = function () {
            this.texture.resize(this.localWidth, this.localHeight, true);
            var textureXs = [0, this.leftSize, this.baseFrame.width - this.rightSize, this.baseFrame.width];
            var textureYs = [0, this.topSize, this.baseFrame.height - this.bottomSize, this.baseFrame.height];
            var finalXs = [0, this.leftSize, this.localWidth - this.rightSize, this.localWidth];
            var finalYs = [0, this.topSize, this.localHeight - this.bottomSize, this.localHeight];
            for (var yi = 0; yi < 3; yi++) {
                for (var xi = 0; xi < 3; xi++) {
                    var s = this.createTexturePart(textureXs[xi], textureYs[yi], textureXs[xi + 1] - textureXs[xi], textureYs[yi + 1] - textureYs[yi]);
                    s.width = finalXs[xi + 1] - finalXs[xi];
                    s.height = finalYs[yi + 1] - finalYs[yi];
                    this.texture.renderXY(s, finalXs[xi], finalYs[yi]);
                }
            }
        };
        NineSlice.prototype.resize = function (width, height) {
            this.localWidth = width;
            this.localHeight = height;
            this.renderTexture();
        };
        NineSlice.prototype.createTexturePart = function (x, y, width, height) {
            var frame = new PIXI.Rectangle(this.baseFrame.x + this.texture.frame.x + x, this.baseFrame.y + this.texture.frame.y + y, Math.max(width, 1), Math.max(height, 1));
            return new Phaser.Sprite(this.game, 0, 0, new PIXI.Texture(this.baseTexture, frame));
        };
        return NineSlice;
    }(Phaser.Sprite));
    PhaserNineSlice.NineSlice = NineSlice;
})(PhaserNineSlice || (PhaserNineSlice = {}));
var PhaserNineSlice;
(function (PhaserNineSlice) {
    var Plugin = (function (_super) {
        __extends(Plugin, _super);
        function Plugin(game, parent) {
            var _this = _super.call(this, game, parent) || this;
            _this.addNineSliceCache();
            _this.addNineSliceFactory();
            _this.addNineSliceLoader();
            return _this;
        }
        Plugin.prototype.addNineSliceLoader = function () {
            Phaser.Loader.prototype.nineSlice = function (key, url, top, left, right, bottom) {
                var cacheData = {
                    top: top
                };
                if (left) {
                    cacheData.left = left;
                }
                if (right) {
                    cacheData.right = right;
                }
                if (bottom) {
                    cacheData.bottom = bottom;
                }
                this.addToFileList('image', key, url);
                this.game.cache.addNineSlice(key, cacheData);
            };
        };
        Plugin.prototype.addNineSliceFactory = function () {
            Phaser.GameObjectFactory.prototype.nineSlice = function (x, y, key, frame, width, height, group) {
                if (group === undefined) {
                    group = this.world;
                }
                var nineSliceObject = new PhaserNineSlice.NineSlice(this.game, x, y, key, frame, width, height);
                return group.add(nineSliceObject);
            };
            Phaser.GameObjectCreator.prototype.nineSlice = function (x, y, key, frame, width, height) {
                return new PhaserNineSlice.NineSlice(this.game, x, y, key, frame, width, height);
            };
        };
        Plugin.prototype.addNineSliceCache = function () {
            Phaser.Cache.prototype.nineSlice = {};
            Phaser.Cache.prototype.addNineSlice = function (key, data) {
                this.nineSlice[key] = data;
            };
            Phaser.Cache.prototype.getNineSlice = function (key) {
                var data = this.nineSlice[key];
                if (undefined === data) {
                    console.warn('Phaser.Cache.getNineSlice: Key "' + key + '" not found in Cache.');
                }
                return data;
            };
        };
        return Plugin;
    }(Phaser.Plugin));
    PhaserNineSlice.Plugin = Plugin;
})(PhaserNineSlice || (PhaserNineSlice = {}));
//# sourceMappingURL=memory.js.map
