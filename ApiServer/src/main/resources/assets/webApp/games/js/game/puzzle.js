var __extends = (this && this.__extends) || (function () {
    var extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var GameEngine = (function (_super) {
            __extends(GameEngine, _super);
            function GameEngine() {
                var _this = _super.call(this, Config.GW, Config.GH, Phaser.AUTO, Config.DOM_PARENT_ID, null, true) || this;
                _this.state.add(States.BOOT, Client.Boot, false);
                _this.state.add(States.PRELOADER, Client.Preloader, false);
                _this.state.add(States.MAINMENU, Client.MainMenu, false);
                _this.state.add(States.GAME, Client.GameCtrl, false);
                _this.state.start(States.BOOT);
                return _this;
            }
            return GameEngine;
        }(Phaser.Game));
        Client.GameEngine = GameEngine;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
window.onload = function () {
    new PuzzleGame.Client.GameEngine();
};
var Config;
(function (Config) {
    Config.DOM_PARENT_ID = 'content';
    Config.GW = 1200;
    Config.GH = 900;
    Config.GSW = 1200;
    Config.GSH = 600;
})(Config || (Config = {}));
var Params;
(function (Params) {
    Params.isIOS = false;
    Params.q_version = 2;
    Params.q_dif_id = 2;
    Params.q_img = 'pic0.png';
    Params.q_is_global_pic = false;
    Params.pieces_cnt = 0;
})(Params || (Params = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var BoomEff = (function (_super) {
            __extends(BoomEff, _super);
            function BoomEff(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.particles = [];
                _this.buffer = [];
                _this.addTimer = 0;
                _this.curr_rotation = 0;
                _this.part_cnt = 20;
                _this.part_live_time = 1;
                _this.parent_owner = false;
                _this.addTime = 0.02;
                _this.radius = 80;
                _this.spd_x_min = 0;
                _this.spd_x_max = 0;
                _this.spd_y_min = 0;
                _this.spd_y_max = 0;
                _this.isActive = false;
                _this.part_scale = 1;
                _this.grav_y = 0;
                _this.min_ang = -Math.PI;
                _this.max_ang = Math.PI;
                return _this;
            }
            BoomEff.prototype.getFromBuffer = function (pid) {
                for (var i = this.buffer.length - 1; i >= 0; i--) {
                    if (this.buffer[i].pid == pid)
                        return this.buffer.splice(i, 1)[0];
                }
                return null;
            };
            BoomEff.prototype.doEffect = function () {
                for (var i = 0; i < this.part_cnt; i++) {
                    this.addParticle();
                }
            };
            BoomEff.prototype.addParticle = function () {
                var pid = MyMath.randomIntInRange(1, 7);
                var p = this.getFromBuffer(pid);
                if (p == null) {
                    p = new Client.EffParticle(this.game, 0, 0, pid);
                }
                p.ay = this.grav_y;
                var rot = MyMath.randomInRange(this.min_ang, this.max_ang);
                p.x = Math.cos(rot) * MyMath.randomInRange(0, this.radius) + (this.parent_owner ? this.x : 0);
                p.y = Math.sin(rot) * MyMath.randomInRange(0, this.radius) + (this.parent_owner ? this.y : 0);
                p.vx = Math.cos(rot) * MyMath.randomInRange(this.spd_x_min, this.spd_x_max);
                p.vy = Math.sin(rot) * MyMath.randomInRange(this.spd_y_min, this.spd_y_max);
                p.vxr = 0.995;
                p.vyr = 0.995;
                p.rotSpd = MyMath.randomInRange(-6, 6);
                p.p_scale = this.part_scale;
                p.reUse(this.part_live_time);
                this.particles.push(p);
                if (this.parent_owner) {
                    if (this.parent != null)
                        this.parent.addChild(p);
                }
                else {
                    this.addChild(p);
                }
            };
            BoomEff.prototype.removeParticle = function (id) {
                try {
                    this.particles[id].parent.removeChild(this.particles[id]);
                    this.buffer = this.buffer.concat(this.particles.splice(id, 1));
                }
                catch (e) {
                    LogMng.error('SparkEffect.removeParticle(): ' + e);
                }
            };
            BoomEff.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                for (var i = this.particles.length - 1; i >= 0; i--) {
                    this.particles[i].update();
                    if (this.particles[i].isDead) {
                        this.removeParticle(i);
                    }
                }
            };
            return BoomEff;
        }(Phaser.Sprite));
        Client.BoomEff = BoomEff;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var CircularEffect = (function (_super) {
            __extends(CircularEffect, _super);
            function CircularEffect(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.particles = [];
                _this.buffer = [];
                _this.addTimer = 0;
                _this.curr_rotation = 0;
                _this.parent_owner = false;
                _this.addTime = 0.02;
                _this.radius = 80;
                _this.spd = 4;
                _this.isActive = false;
                _this.part_scale = 1;
                return _this;
            }
            CircularEffect.prototype.addParticle = function () {
                var p;
                if (this.buffer.length > 0) {
                    p = this.buffer.shift();
                }
                else {
                    p = new Client.EffParticle(this.game, 0, 0, 1);
                }
                p.ay = 90;
                var rot = MyMath.randomInRange(0, Math.PI * 2);
                p.x = Math.cos(rot) * this.radius + (this.parent_owner ? this.x : 0);
                p.y = Math.sin(rot) * this.radius + (this.parent_owner ? this.y : 0);
                p.vx = Math.cos(rot) * 50;
                p.vy = Math.sin(rot) * 50;
                p.rotSpd = MyMath.randomInRange(3, 6);
                p.p_scale = this.part_scale;
                p.reUse(1);
                this.particles.push(p);
                if (this.parent_owner) {
                    if (this.parent != null)
                        this.parent.addChild(p);
                }
                else {
                    this.addChild(p);
                }
            };
            CircularEffect.prototype.removeParticle = function (id) {
                try {
                    this.particles[id].parent.removeChild(this.particles[id]);
                    this.buffer = this.buffer.concat(this.particles.splice(id, 1));
                }
                catch (e) {
                    LogMng.error('SparkEffect.removeParticle(): ' + e);
                }
            };
            CircularEffect.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.curr_rotation += this.spd * dt;
                this.addTimer -= dt;
                if (this.isActive && this.addTimer <= 0) {
                    this.addTimer = this.addTime;
                    this.addParticle();
                }
                for (var i = this.particles.length - 1; i >= 0; i--) {
                    this.particles[i].update();
                    if (this.particles[i].isDead) {
                        this.removeParticle(i);
                    }
                }
            };
            return CircularEffect;
        }(Phaser.Sprite));
        Client.CircularEffect = CircularEffect;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var EffParticle = (function (_super) {
            __extends(EffParticle, _super);
            function EffParticle(game, x, y, pid) {
                var _this = _super.call(this, game, x, y) || this;
                _this.isDead = false;
                _this.vx = 0;
                _this.vy = 0;
                _this.ax = 0;
                _this.ay = 0;
                _this.rotSpd = 0;
                _this.vxr = 0.999;
                _this.vyr = 0.999;
                _this.p_scale = 1;
                _this.pid = pid;
                _this.spark = new Phaser.Sprite(_this.game, 0, 0, 'final', 'particle' + pid);
                _this.spark.anchor.set(0.5);
                return _this;
            }
            EffParticle.prototype.reUse = function (aLiveTime) {
                this.liveTime = aLiveTime;
                this.scale.set(0);
                this.alpha = 1;
                this.game.add.tween(this.scale).to({ x: this.p_scale, y: this.p_scale }, 200, Phaser.Easing.Linear.None, true).chain(this.game.add.tween(this.scale).to({ x: 0, y: 0 }, 200, Phaser.Easing.Linear.None, false, aLiveTime * 1000 - 400));
                this.isDead = false;
                this.addChild(this.spark);
            };
            EffParticle.prototype.update = function () {
                if (this.isDead)
                    return;
                var dt = this.game.time.elapsed / 1000;
                this.vx += this.ax * dt;
                this.vy += this.ay * dt;
                this.vx *= this.vxr;
                this.vy *= this.vyr;
                this.x += this.vx * dt;
                this.y += this.vy * dt;
                this.rotation += this.rotSpd * dt;
                this.liveTime -= dt;
                if (this.liveTime <= 0) {
                    this.isDead = true;
                }
            };
            return EffParticle;
        }(Phaser.Sprite));
        Client.EffParticle = EffParticle;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var SparkEffect = (function (_super) {
            __extends(SparkEffect, _super);
            function SparkEffect(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.particles = [];
                _this.buffer = [];
                _this.addTimer = 0;
                _this.addTime = 0.2;
                _this.area_w = 100;
                _this.area_h = 100;
                _this.isActive = false;
                return _this;
            }
            SparkEffect.prototype.addParticle = function () {
                var p;
                if (this.buffer.length > 0) {
                    p = this.buffer.shift();
                }
                else {
                    p = new Client.EffParticle(this.game, 0, 0, 1);
                }
                p.vx = 0;
                p.vy = 0;
                p.ay = 100;
                p.x = this.x + MyMath.randomInRange(-this.area_w / 2, this.area_w / 2);
                p.y = this.y + MyMath.randomInRange(-this.area_h / 2, this.area_h / 2);
                p.rotSpd = MyMath.randomInRange(3, 6);
                p.p_scale = this.scale.x;
                p.reUse(1);
                this.particles.push(p);
                if (this.parent != null)
                    this.parent.addChild(p);
            };
            SparkEffect.prototype.removeParticle = function (id) {
                try {
                    this.particles[id].parent.removeChild(this.particles[id]);
                    this.buffer = this.buffer.concat(this.particles.splice(id, 1));
                }
                catch (e) {
                    LogMng.error('SparkEffect.removeParticle(): ' + e);
                }
            };
            SparkEffect.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.addTimer -= dt;
                if (this.isActive && this.addTimer <= 0) {
                    this.addTimer = this.addTime;
                    this.addParticle();
                }
                for (var i = this.particles.length - 1; i >= 0; i--) {
                    this.particles[i].update();
                    if (this.particles[i].isDead) {
                        this.removeParticle(i);
                    }
                }
            };
            return SparkEffect;
        }(Phaser.Sprite));
        Client.SparkEffect = SparkEffect;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var BarScroller = (function (_super) {
            __extends(BarScroller, _super);
            function BarScroller(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.scroller = new Phaser.Sprite(game, 0, 0, 'game', 'level');
                _this.scroller.anchor.set(0.5);
                _this.addChild(_this.scroller);
                _this.text = new Phaser.Text(game, 0 - 2, 0 + 2, '0');
                _this.text.anchor.set(0.5);
                _this.text.addColor('#FFFFFF', 0);
                _this.addChild(_this.text);
                return _this;
            }
            BarScroller.prototype.setText = function (aText) {
                this.text.text = aText;
            };
            return BarScroller;
        }(Phaser.Sprite));
        Client.BarScroller = BarScroller;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var DifPanel = (function (_super) {
            __extends(DifPanel, _super);
            function DifPanel(game, x, y, difs) {
                var _this = _super.call(this, game, x, y) || this;
                _this.difs = [];
                _this.curr_dif_id = 0;
                _this.last_drag_dif_id = -1;
                _this.onChangedSignal = new Phaser.Signal();
                _this.difs = difs;
                if (_this.difs.length > 2) {
                    _this.curr_dif_id = Math.round(_this.difs.length / 2);
                }
                _this.bg = new Phaser.Sprite(_this.game, 0, 0, 'game', 'header');
                _this.bg.anchor.set(0.5, 0);
                _this.addChild(_this.bg);
                _this.bar_bg = new Phaser.Sprite(_this.game, 0, _this.bg.height / 2 - 10, 'game', 'progress_bg');
                _this.bar_bg.anchor.set(0.5);
                _this.addChild(_this.bar_bg);
                _this.bar = new Phaser.Sprite(_this.game, 0, _this.bg.height / 2 - 10, 'game', 'progress');
                _this.bar.anchor.set(0.5);
                _this.addChild(_this.bar);
                _this.bar_mask = new Phaser.Graphics(game, _this.bar.x - _this.bar.width / 2, _this.bar.y - _this.bar.height / 2);
                _this.addChild(_this.bar_mask);
                _this.bar.mask = _this.bar_mask;
                _this.bar_scroller = new Client.BarScroller(game, _this.bar.x, _this.bar.y - 1);
                _this.addChild(_this.bar_scroller);
                _this.bar_scroller.inputEnabled = true;
                _this.bar_scroller.input.useHandCursor = true;
                _this.bar_scroller.input.enableDrag(false, false);
                _this.bar_scroller.input.allowVerticalDrag = false;
                _this.bar_scroller.events.onDragUpdate.add(_this.onDragUpdate, _this);
                _this.bar_scroller.events.onDragStop.add(_this.onDragStop, _this);
                _this.updScroller();
                _this.btnMin = new Phaser.Button(_this.game, -_this.bg.width / 2 + 110, 10, 'game', _this.onMinClick, _this, 'reduce', 'reduce');
                _this.btnMin.anchor.set(0.5, 0);
                _this.addChild(_this.btnMin);
                _this.btnPlus = new Phaser.Button(_this.game, _this.bg.width / 2 - 110, 10, 'game', _this.onPlusClick, _this, 'increase', 'increase');
                _this.btnPlus.anchor.set(0.5, 0);
                _this.addChild(_this.btnPlus);
                return _this;
            }
            DifPanel.prototype.getDifIdByPerc = function (aPerc) {
                var different = 0;
                var res = -1;
                for (var i = 0; i < this.difs.length; i++) {
                    var perc = i / this.difs.length;
                    if (Math.abs(perc - aPerc) < different || res == -1) {
                        res = i;
                        different = Math.abs(perc - aPerc);
                    }
                }
                return res;
            };
            DifPanel.prototype.onDragUpdate = function () {
                var min_x = this.bar.x - this.bar.width / 2;
                var max_x = this.bar.x + this.bar.width / 2;
                if (this.bar_scroller.x < min_x)
                    this.bar_scroller.x = min_x;
                if (this.bar_scroller.x > max_x)
                    this.bar_scroller.x = max_x;
                var perc = (this.bar_scroller.x - min_x) / (max_x - min_x);
                var new_dif_id = Math.round(perc * (this.difs.length - 1));
                if (this.last_drag_dif_id != new_dif_id) {
                    this.last_drag_dif_id = new_dif_id;
                    this.setDefId(new_dif_id, false);
                }
            };
            DifPanel.prototype.onDragStop = function () {
                this.setDefId(this.curr_dif_id);
            };
            DifPanel.prototype.onMinClick = function () {
                SndMng.sfxClick();
                if (this.curr_dif_id <= 0)
                    return;
                this.setDefId(this.curr_dif_id - 1);
            };
            DifPanel.prototype.onPlusClick = function () {
                SndMng.sfxClick();
                if (this.curr_dif_id >= this.difs.length - 1)
                    return;
                this.setDefId(this.curr_dif_id + 1);
            };
            DifPanel.prototype.updScroller = function (aMoving) {
                if (aMoving === void 0) { aMoving = true; }
                if (aMoving) {
                    var perc = this.difs.length > 0 ? this.curr_dif_id / (this.difs.length - 1) : 0;
                    this.game.tweens.removeFrom(this.bar_scroller);
                    var tw = this.game.add.tween(this.bar_scroller).to({ x: this.bar.x - this.bar.width / 2 + this.bar.width * perc }, 500, Phaser.Easing.Sinusoidal.Out, true);
                }
                this.bar_scroller.setText(String(this.difs[this.curr_dif_id]));
            };
            DifPanel.prototype.updateMask = function () {
                this.bar_mask.clear();
                this.bar_mask.beginFill(0, 1);
                this.bar_mask.drawRect(0, 0, this.bar_scroller.x - this.bar_mask.x, this.bar.height);
                this.bar_mask.endFill();
            };
            DifPanel.prototype.setDefId = function (id, aMoving) {
                if (aMoving === void 0) { aMoving = true; }
                this.curr_dif_id = id;
                this.onChangedSignal.dispatch(this.difs[this.curr_dif_id]);
                this.updScroller(aMoving);
            };
            DifPanel.prototype.getPiecesCount = function () {
                return this.difs[this.curr_dif_id];
            };
            DifPanel.prototype.update = function () {
                this.updateMask();
            };
            return DifPanel;
        }(Phaser.Sprite));
        Client.DifPanel = DifPanel;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var HelpBtn = (function (_super) {
            __extends(HelpBtn, _super);
            function HelpBtn(game, x, y) {
                var _this = _super.call(this, game, x, y, 'game') || this;
                _this.isPressed = false;
                _this.onClickSignal = new Phaser.Signal();
                _this.onInputDown.add(_this.onClick, _this);
                _this.updateFrames();
                return _this;
            }
            HelpBtn.prototype.turnOn = function () {
                if (!this.isPressed)
                    this.onClick();
            };
            HelpBtn.prototype.updateFrames = function () {
                if (this.isPressed)
                    this.setFrames('help_on', 'help_on');
                else
                    this.setFrames('help_off', 'help_off');
            };
            HelpBtn.prototype.onClick = function () {
                this.isPressed = !this.isPressed;
                this.updateFrames();
                this.onClickSignal.dispatch();
            };
            return HelpBtn;
        }(Phaser.Button));
        Client.HelpBtn = HelpBtn;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var PAN_TIME = 2;
        var TopPanel = (function (_super) {
            __extends(TopPanel, _super);
            function TopPanel(game, x, y, difs, aDefaultDefId) {
                if (aDefaultDefId === void 0) { aDefaultDefId = 2; }
                var _this = _super.call(this, game, x, y) || this;
                _this.panOpenTimer = PAN_TIME;
                _this.isTopPanelVisible = true;
                _this.onHelpClickSignal = new Phaser.Signal();
                _this.onCloseClickSignal = new Phaser.Signal();
                _this.onDifChangeSignal = new Phaser.Signal();
                _this.onPanelOpenSignal = new Phaser.Signal();
                _this.onPanelHidedSignal = new Phaser.Signal();
                _this.btnArrow = new Phaser.Button(_this.game, 0, 0, 'game', _this.onArrowClick, _this, 'hide', 'hide');
                _this.btnArrow.anchor.set(0.5, 0);
                _this.addChild(_this.btnArrow);
                _this.panel = new Client.DifPanel(_this.game, 0, 0, difs);
                _this.panel.setDefId(aDefaultDefId);
                _this.panel.onChangedSignal.add(_this.onDifChange, _this);
                _this.addChild(_this.panel);
                _this.btnHelp = new Client.HelpBtn(_this.game, -Config.GSW / 2 + 10, 10);
                _this.btnHelp.onClickSignal.add(_this.onHelpClick, _this);
                _this.addChild(_this.btnHelp);
                _this.btnClose = new Phaser.Button(_this.game, Config.GSW / 2 - 10, 10, 'final', _this.onCloseClick, _this, 'close_puzzle', 'close_puzzle');
                _this.btnClose.anchor.set(1, 0);
                _this.addChild(_this.btnClose);
                return _this;
            }
            TopPanel.prototype.onDifChange = function (aDif) {
                this.onDifChangeSignal.dispatch(aDif);
                this.openPanel();
            };
            TopPanel.prototype.onArrowClick = function () {
                SndMng.sfxClick();
                this.openPanel();
            };
            TopPanel.prototype.onHelpClick = function () {
                SndMng.sfxClick();
                this.onHelpClickSignal.dispatch(this.btnHelp.isPressed);
            };
            TopPanel.prototype.onCloseClick = function () {
                SndMng.sfxClick();
                pressedClose();
//                window.location.replace("http://app_links_close")
                this.onCloseClickSignal.dispatch();
            };
            TopPanel.prototype.refreshPanelTimer = function () {
                this.panOpenTimer = PAN_TIME;
            };
            TopPanel.prototype.onPanelHided = function () {
                this.onPanelHidedSignal.dispatch();
            };
            TopPanel.prototype.setTopPanelVisible = function (aVis) {
                LogMng.debug('setTopPanelVisible val = ' + aVis);
                this.isTopPanelVisible = aVis;
                this.btnArrow.visible = aVis;
                this.panel.visible = aVis;
                this.btnHelp.visible = aVis;
                this.isTopPanelVisible ? this.openPanel() : this.hidePanel(true);
            };
            TopPanel.prototype.getPiecesCount = function () {
                return this.panel.getPiecesCount();
            };
            TopPanel.prototype.showHelp = function () {
                this.btnHelp.turnOn();
            };
            TopPanel.prototype.openPanel = function () {
                if (!this.isTopPanelVisible)
                    return;
                this.onPanelOpenSignal.dispatch();
                this.refreshPanelTimer();
                this.game.tweens.removeFrom(this.panel);
                this.game.add.tween(this.panel).to({ y: 0 }, 500, Phaser.Easing.Sinusoidal.Out, true);
            };
            TopPanel.prototype.hidePanel = function (aFast) {
                if (aFast === void 0) { aFast = false; }
                if (aFast) {
                    this.panel.y = -this.panel.height;
                    this.onPanelHidedSignal.dispatch();
                }
                else {
                    this.game.tweens.removeFrom(this.panel);
                    this.game.add.tween(this.panel).to({ y: -140 }, 500, Phaser.Easing.Sinusoidal.In, true).onComplete.add(this.onPanelHided, this);
                }
            };
            TopPanel.prototype.isHelpPressed = function () {
                return this.btnHelp.isPressed;
            };
            TopPanel.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                if (this.panOpenTimer > 0) {
                    this.panOpenTimer -= dt;
                    if (this.panOpenTimer <= 0) {
                        this.hidePanel();
                    }
                }
                if (this.panel != null)
                    this.panel.update();
            };
            return TopPanel;
        }(Phaser.Sprite));
        Client.TopPanel = TopPanel;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var BLOONS_DATA = [
            { id: 3, x: -300, y: 220, an: -40 },
            { id: 5, x: -300, y: 220, an: 5 },
            { id: 4, x: -300, y: 220, an: -20 },
            { id: 2, x: -300, y: 220, an: 15 },
            { id: 1, x: -300, y: 220, an: -20 },
            { id: 6, x: -300, y: 220, an: -5 },
            { id: 0, x: -300, y: 220, an: 2 },
            { id: 5, x: 300, y: 220, an: -10 },
            { id: 3, x: 300, y: 220, an: 40 },
            { id: 4, x: 300, y: 220, an: 20 },
            { id: 2, x: 300, y: 220, an: -15 },
            { id: 0, x: 300, y: 220, an: 20 },
            { id: 6, x: 300, y: 220, an: 5 },
            { id: 1, x: 300, y: 220, an: 0 }
        ];
        var WinWindow = (function (_super) {
            __extends(WinWindow, _super);
            function WinWindow(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.inited = false;
                _this.bloons = [];
                _this.onRestartClickEvent = new Phaser.Signal();
                _this.onCloseClickEvent = new Phaser.Signal();
                return _this;
            }
            WinWindow.prototype.showScreen = function () {
                if (this.inited)
                    return;
                this.bg = new Phaser.Sprite(this.game, 0, 0, 'trophybg');
                this.bg.anchor.set(0.5);
                this.bg.inputEnabled = true;
                this.bg.input.useHandCursor = false;
                this.addChild(this.bg);
                this.cloud = new Phaser.Sprite(this.game, 0, -Config.GH / 2, 'final', 'cloud');
                this.cloud.anchor.set(0.5, 0);
                this.bg.addChild(this.cloud);
                this.conf = new Phaser.Sprite(this.game, 0, 0, 'fireworks');
                this.conf.anchor.set(0.5);
                this.addChild(this.conf);
                this.rocket = new Client.Rocket(this.game, 0, 400);
                this.rocket.visible = false;
                this.addChild(this.rocket);
                this.dummyBloons = new Phaser.Sprite(this.game, 0, Config.GH * 1.5);
                this.addChild(this.dummyBloons);
                this.bloons = [];
                for (var i = 0; i < BLOONS_DATA.length; i++) {
                    var bl = new Client.Bloon(this.game, BLOONS_DATA[i].x, BLOONS_DATA[i].y, BLOONS_DATA[i].id);
                    bl.scale.set(1.8);
                    bl.angle = BLOONS_DATA[i].an;
                    this.dummyBloons.addChild(bl);
                    this.bloons.push(bl);
                }
                this.game.add.tween(this.dummyBloons).to({ y: 0 }, 4000, Phaser.Easing.Sinusoidal.Out, true).onComplete.addOnce(this.onBloonsUp, this);
                this.dummyGui = new Phaser.Sprite(this.game, 0, -Config.GH / 2);
                this.updateGuiPanel();
                this.addChild(this.dummyGui);
                this.btnRestart = new Phaser.Button(this.game, -Config.GSW / 2 + 10, 10, 'final', this.onRestartClick, this, 'replay', 'replay');
                this.btnRestart.anchor.set(0, 0);
                this.dummyGui.addChild(this.btnRestart);
                this.btnClose = new Phaser.Button(this.game, Config.GSW / 2 - 10, 10, 'final', this.onCloseClick, this, 'close_puzzle', 'close_puzzle');
                this.btnClose.anchor.set(1, 0);
                this.dummyGui.addChild(this.btnClose);
                this.bg.alpha = 0;
                this.game.add.tween(this.bg).to({ alpha: 1 }, 800, Phaser.Easing.Linear.None, true);
                this.conf.scale.set(0);
                this.game.add.tween(this.conf.scale).to({ x: 1, y: 1 }, 400, Phaser.Easing.Sinusoidal.Out, true);
                this.rocket.y = 600;
                this.rocket.visible = true;
                this.game.add.tween(this.rocket).to({ y: -20 }, 3000, Phaser.Easing.Sinusoidal.Out, true);
                this.alpha = 1;
                SndMng.fadeOutMusic(0, 100);
                SndMng.sfxWin();
                this.game.time.events.add(5000, SndMng.fadeInMusic, this);
                this.inited = true;
            };
            WinWindow.prototype.onBloonsUp = function () {
                this.game.add.tween(this.dummyBloons).to({ y: 50 }, 1600, Phaser.Easing.Sinusoidal.InOut, true, 0, -1, true);
            };
            WinWindow.prototype.onRestartClick = function () {
                SndMng.sfxClick();
                this.onRestartClickEvent.dispatch();
            };
            WinWindow.prototype.onCloseClick = function () {
                SndMng.sfxClick();
                var event = new Event("close");
                pressedClose();
                this.onCloseClickEvent.dispatch();
            };
            WinWindow.prototype.hideScreen = function () {
                if (!this.inited)
                    return;
                this.game.add.tween(this).to({ alpha: 0 }, 200, null, true).onComplete.add(this.onHideComplete, this);
            };
            WinWindow.prototype.onHideComplete = function () {
                this.inited = false;
                for (var i = 0; i < this.bloons.length; i++) {
                    this.bloons[i].kill();
                }
                this.bloons = [];
                this.game.tweens.removeFrom(this.dummyBloons);
                this.rocket.free();
                this.rocket.kill();
                this.dummyGui.kill();
                this.removeChildren();
            };
            WinWindow.prototype.updateGuiPanel = function () {
                if (this.dummyGui == null)
                    return;
                if (ScaleManager.gameViewH < Config.GH) {
                    this.dummyGui.y = -Config.GH / 2 + (Config.GH - ScaleManager.gameViewH) / 2 - 1;
                }
                else {
                    this.dummyGui.y = -Config.GH / 2;
                }
            };
            WinWindow.prototype.update = function () {
                if (!this.inited)
                    return;
                this.updateGuiPanel();
                for (var i = 0; i < this.bloons.length; i++) {
                    this.bloons[i].update();
                }
            };
            return WinWindow;
        }(Phaser.Sprite));
        Client.WinWindow = WinWindow;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var SIDE_PIECES_SIZE = 180;
        var GameMng = (function (_super) {
            __extends(GameMng, _super);
            function GameMng(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                _this.img_w0 = 0;
                _this.img_h0 = 0;
                _this.img_scale = 1;
                _this.img_x = 0;
                _this.img_y = 0;
                _this.available_height = Config.GH;
                _this.top_padding = 100;
                _this.timerUpdScaling = 0.2;
                _this.needUpdScaling = false;
                _this.pieces = [];
                _this.l_pieces = [];
                _this.r_pieces = [];
                _this.bot_pieces = [];
                _this.onGameCompleteEvent = new Phaser.Signal();
                _this.dummyImage = new Phaser.Sprite(_this.game, 0, 0);
                _this.addChild(_this.dummyImage);
                _this.dummyBoard = new Phaser.Sprite(_this.game, 0, 0);
                _this.addChild(_this.dummyBoard);
                _this.dummyPieces = new Phaser.Sprite(_this.game, 0, 0);
                _this.addChild(_this.dummyPieces);
                _this.dummyEffect = new Phaser.Sprite(_this.game, 0, 0);
                _this.addChild(_this.dummyEffect);
                _this.boardFrameNum = MyMath.randomIntInRange(1, 3);
                _this.helpImage = new Phaser.Sprite(_this.game, 0, 0, 'img');
                _this.img_w0 = _this.helpImage.width;
                _this.img_h0 = _this.helpImage.height;
                _this.helpImage.alpha = 0;
                _this.dummyImage.addChild(_this.helpImage);
                _this.ht_bd = new Phaser.BitmapData(game, 'help_bd', _this.helpImage.width, _this.helpImage.height);
                _this.helpTiles = new Phaser.Sprite(game, 0, 0, _this.ht_bd);
                _this.dummyImage.addChild(_this.helpTiles);
                _this.boomEff = new Client.BoomEff(_this.game, 0, 0);
                _this.boomEff.part_cnt = 10;
                _this.boomEff.grav_y = 100;
                _this.boomEff.radius = 50;
                _this.boomEff.part_live_time = 1;
                _this.boomEff.spd_x_min = 150;
                _this.boomEff.spd_x_max = 250;
                _this.boomEff.spd_y_min = 150;
                _this.boomEff.spd_y_max = 300;
                _this.dummyEffect.addChild(_this.boomEff);
                return _this;
            }
            GameMng.prototype.updateScaling = function (aForce) {
                if (aForce === void 0) { aForce = true; }
                var pad_left = SIDE_PIECES_SIZE;
                var av_w = Config.GSW - pad_left * 2;
                var av_h = this.available_height - this.top_padding;
                this.img_scale = av_w / this.img_w0;
                if (this.img_h0 * this.img_scale > av_h) {
                    this.img_scale = av_h / this.img_h0;
                }
                var img_w = this.img_w0 * this.img_scale;
                var img_h = this.img_h0 * this.img_scale;
                this.img_x = pad_left + (av_w - img_w) / 2;
                this.img_y = this.top_padding + (Config.GH - img_h) / 2;
                if (aForce) {
                    this.dummyImage.position.set(this.img_x, this.img_y);
                    this.dummyImage.scale.set(this.img_scale);
                }
                if (this.imgBoard != null) {
                    this.imgBoard.kill();
                }
                this.imgBoard = null;
                var fr_dx = 0;
                var fr_dy = 0;
                var fr_dw = 0;
                var fr_dh = 0;
                switch (this.boardFrameNum) {
                    case 1:
                        fr_dx = -24;
                        fr_dy = -24;
                        fr_dw = 62;
                        fr_dh = 60;
                        break;
                    case 2:
                        fr_dx = -24;
                        fr_dy = -24;
                        fr_dw = 60;
                        fr_dh = 60;
                        break;
                    case 3:
                        fr_dx = -24;
                        fr_dy = -24;
                        fr_dw = 64;
                        fr_dh = 58;
                        break;
                }
                if (aForce) {
                    this.imgBoard = new PhaserNineSlice.NineSlice(this.game, this.img_x + fr_dx, this.img_y + fr_dy, 'game', 'frame' + String(this.boardFrameNum), img_w + fr_dw, img_h + fr_dh, { top: 60, bottom: 60, left: 60, right: 60 });
                    this.dummyBoard.addChild(this.imgBoard);
                }
            };
            GameMng.prototype.getPieceWHCount = function (aPiecesCount) {
                var res = new Phaser.Point();
                switch (aPiecesCount) {
                    case 2:
                        res.set(2, 1);
                        break;
                    case 6:
                        res.set(3, 2);
                        break;
                    case 12:
                        res.set(4, 3);
                        break;
                    case 20:
                        res.set(5, 4);
                        break;
                    case 30:
                        res.set(6, 5);
                        break;
                    case 42:
                        res.set(7, 6);
                        break;
                }
                return res;
            };
            GameMng.prototype.clearField = function () {
                for (var i = 0; i < this.pieces.length; i++) {
                    this.pieces[i].kill();
                }
                this.pieces = [];
                this.l_pieces = [];
                this.r_pieces = [];
                this.bot_pieces = [];
            };
            GameMng.prototype.restartGame = function (aPiecesCount) {
                this.clearField();
                var tiles = [];
                var img = new Phaser.Sprite(this.game, 0, 0, 'img');
                this.p_wh_cnt = this.getPieceWHCount(aPiecesCount);
                this.p_size_w = img.width / this.p_wh_cnt.x;
                this.p_size_h = img.height / this.p_wh_cnt.y;
                this.p_fr_size_w = this.p_size_w * 0.2;
                this.p_fr_size_h = this.p_size_h * 0.2;
                this.p_side_scale = SIDE_PIECES_SIZE / this.p_size_w / 1.6;
                var ht_gr = new Phaser.Graphics(this.game, 0, 0);
                this.ht_bd.clear();
                var hm_prev_pos = new Phaser.Point();
                for (var x = 0; x < this.p_wh_cnt.x; x++) {
                    tiles[x] = [];
                    for (var y = 0; y < this.p_wh_cnt.y; y++) {
                        var piece = new Phaser.Sprite(this.game, 0, 0, 'img');
                        var crect_x = x * this.p_size_w - this.p_fr_size_w;
                        if (x == 0)
                            crect_x = 0;
                        var crect_y = y * this.p_size_h - this.p_fr_size_h;
                        if (y == 0)
                            crect_y = 0;
                        var crect_w = this.p_size_w + this.p_fr_size_w * 2;
                        if (x == 0 || x == this.p_wh_cnt.x - 1)
                            crect_w = this.p_size_w + this.p_fr_size_w;
                        var crect_h = this.p_size_h + this.p_fr_size_h * 2;
                        if (y == 0 || y == this.p_wh_cnt.y - 1)
                            crect_h = this.p_size_h + this.p_fr_size_h;
                        var crect = new Phaser.Rectangle(crect_x, crect_y, crect_w, crect_h);
                        piece.crop(crect, true);
                        var tst = MyMath.randomIntInRange(1, 2);
                        if (y == 0) {
                            tst = Client.TileSideType.line;
                        }
                        else {
                            switch (tiles[x][y - 1].tileMask.botType) {
                                case Client.TileSideType.inner:
                                    tst = Client.TileSideType.outer;
                                    break;
                                case Client.TileSideType.outer:
                                    tst = Client.TileSideType.inner;
                                    break;
                            }
                        }
                        var rst = MyMath.randomIntInRange(1, 2);
                        if (x == this.p_wh_cnt.x - 1) {
                            rst = Client.TileSideType.line;
                        }
                        var bst = MyMath.randomIntInRange(1, 2);
                        if (y == this.p_wh_cnt.y - 1) {
                            bst = Client.TileSideType.line;
                        }
                        var lst = MyMath.randomIntInRange(1, 2);
                        if (x == 0) {
                            lst = Client.TileSideType.line;
                        }
                        else {
                            switch (tiles[x - 1][y].tileMask.rType) {
                                case Client.TileSideType.inner:
                                    lst = Client.TileSideType.outer;
                                    break;
                                case Client.TileSideType.outer:
                                    lst = Client.TileSideType.inner;
                                    break;
                            }
                        }
                        var tmask = new Client.TileMask(this.game, 0, 0, this.p_size_w, this.p_size_h, tst, rst, bst, lst);
                        var tile = new Client.TilePuzzle(this.game, 0, 0, x, y, piece, tmask, lst == Client.TileSideType.line ? 0 : this.p_fr_size_w, tst == Client.TileSideType.line ? 0 : this.p_fr_size_h);
                        this.pieces.push(tile);
                        tiles[x][y] = tile;
                        hm_prev_pos.x = this.p_size_w * x;
                        hm_prev_pos.y = this.p_size_h * y;
                        tmask.getMask(false, 0xFFFFFF, piece.width / 70, 0.5, ht_gr, hm_prev_pos.x, hm_prev_pos.y);
                    }
                }
                this.ht_bd.draw(new Phaser.Sprite(this.game, 0, 0, ht_gr.generateTexture()), this.helpImage.x - this.p_size_w / 70, this.helpImage.y - this.p_size_h / 70);
            };
            GameMng.prototype.showHelp = function () {
                this.game.tweens.removeFrom(this.helpImage);
                this.game.add.tween(this.helpImage).to({ alpha: 0.5 }, 500).start();
                this.game.tweens.removeFrom(this.helpTiles);
                this.game.add.tween(this.helpTiles).to({ alpha: 0.5 }, 500).start();
            };
            GameMng.prototype.hideHelp = function () {
                this.game.tweens.removeFrom(this.helpImage);
                this.game.add.tween(this.helpImage).to({ alpha: 0 }, 500).start();
                this.game.tweens.removeFrom(this.helpTiles);
                this.game.add.tween(this.helpTiles).to({ alpha: 0 }, 500).start();
            };
            GameMng.prototype.setTopPadding = function (aVal) {
                this.top_padding = aVal;
                this.needUpdScaling = true;
            };
            GameMng.prototype.setAvailableHeight = function (aHeight) {
                this.available_height = aHeight;
                this.needUpdScaling = true;
            };
            GameMng.prototype.getTileToSide = function () {
                var hiden_pieces_ids = [];
                for (var i = 0; i < this.pieces.length; i++) {
                    if (!this.pieces[i].isShowed && !this.pieces[i].isComplete)
                        hiden_pieces_ids.push(i);
                }
                if (hiden_pieces_ids.length <= 0)
                    return null;
                var rnd_id = MyMath.randomIntInRange(0, hiden_pieces_ids.length - 1);
                var rnd_t_id = hiden_pieces_ids[rnd_id];
                hiden_pieces_ids.splice(rnd_id, 1);
                var rnd_t = this.pieces[rnd_t_id];
                this.game.tweens.removeFrom(rnd_t);
                rnd_t.isShowed = true;
                rnd_t.scale.set(this.p_side_scale);
                rnd_t.alpha = 0;
                this.game.add.tween(rnd_t).to({ alpha: 1 }, 800, Phaser.Easing.Linear.None, true);
                rnd_t.inputEnabled = true;
                rnd_t.input.useHandCursor = true;
                rnd_t.input.enableDrag(true, false);
                rnd_t.events.onDragStart.add(this.onTileDragStart, this);
                rnd_t.events.onDragStop.add(this.onTileDragStop, this);
                rnd_t.events.onDragUpdate.add(this.onTileDragUpd, this);
                return rnd_t;
            };
            GameMng.prototype.getTileTruePos = function (aTile) {
                var res = new Phaser.Point();
                res.x = aTile.tx_id * this.p_size_w - this.p_fr_size_w - aTile.pad_x;
                if (aTile.tx_id == 0) {
                    res.x += this.p_fr_size_w;
                }
                res.y = aTile.ty_id * this.p_size_h - this.p_fr_size_h - aTile.pad_y;
                if (aTile.ty_id == 0) {
                    res.y += this.p_fr_size_h;
                }
                return res;
            };
            GameMng.prototype.checkTilePos = function (aTile) {
                var distance = 0.3 * this.img_scale * (this.p_size_w + this.p_size_h) / 2;
                var res = false;
                var tpos = this.getTileTruePos(aTile);
                var p_gl_pos_x = this.dummyImage.x + tpos.x * this.img_scale;
                var p_gl_pos_y = this.dummyImage.y + tpos.y * this.img_scale;
                res = Math.abs(aTile.x - p_gl_pos_x) < distance && Math.abs(aTile.y - p_gl_pos_y) < distance;
                if (!res)
                    return res;
                aTile.inputEnabled = false;
                aTile.isComplete = true;
                this.game.tweens.removeFrom(aTile);
                this.game.add.tween(aTile).to({ x: p_gl_pos_x, y: p_gl_pos_y }, 600, Phaser.Easing.Cubic.Out, true).onComplete.add(this.onTileOnToPos, this, 0, aTile);
                return res;
            };
            GameMng.prototype.getTileTrueCenterPos = function (aTile) {
                var ttp = this.getTileTruePos(aTile);
                if (aTile.tx_id > 0)
                    ttp.x += this.p_fr_size_w / 2;
                if (aTile.ty_id > 0)
                    ttp.y += this.p_fr_size_h / 2;
                return ttp;
            };
            GameMng.prototype.onTileOnToPos = function (aTile) {
                SndMng.sfxPut();
                var ttp = this.getTileTruePos(aTile);
                var ttpc = this.getTileTrueCenterPos(aTile);
                this.boomEff.radius = this.p_size_w / 2 * this.img_scale;
                this.boomEff.position.set(this.dummyImage.x + ttpc.x * this.img_scale, this.dummyImage.y + ttpc.y * this.img_scale);
                this.boomEff.doEffect();
                this.dummyImage.addChild(aTile);
                aTile.scale.set(1);
                aTile.position.set(ttp.x, ttp.y);
                Params.pieces_cnt++;
                this.checkToWin();
            };
            GameMng.prototype.checkToWin = function () {
                for (var i = 0; i < this.pieces.length; i++) {
                    if (!this.pieces[i].isComplete)
                        return;
                }
                LogMng.debug('GameMng checkToWin!');
                this.onGameCompleteEvent.dispatch();
            };
            GameMng.prototype.onTileDragStart = function (aTile, pointer) {
                aTile.isDragged = true;
                aTile.scale.set(this.img_scale);
            };
            GameMng.prototype.onTileDragStop = function (aTile, pointer) {
                aTile.isDragged = false;
                aTile.scale.set(this.p_side_scale);
            };
            GameMng.prototype.onTileDragUpd = function (aTile, pointer) {
                this.checkTilePos(aTile);
            };
            GameMng.prototype.updateLeftTiles = function () {
                var p_w = this.p_size_w * this.p_side_scale;
                var p_h = this.p_size_h * this.p_side_scale;
                var p_top_pad = 50;
                var p_av_h = this.available_height - p_top_pad;
                var p_cnt = Math.floor(0.7 * p_av_h / p_h);
                var dist_y = p_av_h / p_cnt;
                if (this.l_pieces.length != 0)
                    dist_y = p_av_h / this.l_pieces.length;
                var replace_id = -1;
                for (var i = 0; i < this.l_pieces.length; i++) {
                    if (this.l_pieces[i] == null || this.l_pieces[i].isComplete) {
                        if (replace_id < 0)
                            replace_id = i;
                        continue;
                    }
                    if (this.l_pieces[i].isDragged)
                        continue;
                    var px = SIDE_PIECES_SIZE / 2;
                    var py = (Config.GH / 2 + p_top_pad) -
                        dist_y * ((this.l_pieces.length - 1) / 2) + i * dist_y;
                    this.l_pieces[i].x += (px - this.l_pieces[i].x) / 8;
                    this.l_pieces[i].y += (py - this.l_pieces[i].y) / 8;
                }
                var true_len = this.l_pieces.length;
                if (replace_id >= 0)
                    true_len--;
                if (true_len == p_cnt)
                    return;
                if (true_len < p_cnt) {
                    var new_tile = this.getTileToSide();
                    if (new_tile != null) {
                        new_tile.x = -p_w * 2;
                        new_tile.y = Config.GH / 2;
                        if (replace_id >= 0)
                            this.l_pieces[replace_id] = new_tile;
                        else
                            this.l_pieces.push(new_tile);
                        this.dummyPieces.addChild(new_tile);
                    }
                    else {
                        if (replace_id >= 0)
                            this.l_pieces.splice(replace_id, 1);
                    }
                }
                else if (true_len > p_cnt) {
                    while (this.l_pieces.length > p_cnt) {
                        var last_tile = this.l_pieces.pop();
                        if (last_tile == null)
                            continue;
                        last_tile.inputEnabled = false;
                        last_tile.events.onDragStart.removeAll(this);
                        this.game.tweens.removeFrom(last_tile);
                        this.game.add.tween(last_tile).to({ alpha: 0, x: -p_w * 2 }, 500, Phaser.Easing.Linear.None, true);
                        last_tile.isShowed = false;
                    }
                }
            };
            GameMng.prototype.updateRightTiles = function () {
                var p_w = this.p_size_w * this.p_side_scale;
                var p_h = this.p_size_h * this.p_side_scale;
                var p_top_pad = 50;
                var p_av_h = this.available_height - p_top_pad;
                var p_cnt = Math.floor(0.7 * p_av_h / p_h);
                var dist_y = p_av_h / p_cnt;
                if (this.r_pieces.length != 0)
                    dist_y = p_av_h / this.r_pieces.length;
                var replace_id = -1;
                for (var i = 0; i < this.r_pieces.length; i++) {
                    if (this.r_pieces[i] == null || this.r_pieces[i].isComplete) {
                        if (replace_id < 0)
                            replace_id = i;
                        continue;
                    }
                    if (this.r_pieces[i].isDragged)
                        continue;
                    var px = Config.GW - SIDE_PIECES_SIZE / 2;
                    var py = (Config.GH / 2 + p_top_pad) -
                        dist_y * ((this.r_pieces.length - 1) / 2) + i * dist_y;
                    this.r_pieces[i].x += (px - this.r_pieces[i].x) / 8;
                    this.r_pieces[i].y += (py - this.r_pieces[i].y) / 8;
                }
                var true_len = this.r_pieces.length;
                if (replace_id >= 0)
                    true_len--;
                if (true_len == p_cnt)
                    return;
                if (true_len < p_cnt) {
                    var new_tile = this.getTileToSide();
                    if (new_tile != null) {
                        new_tile.x = Config.GW + p_w;
                        new_tile.y = Config.GH / 2;
                        if (replace_id >= 0)
                            this.r_pieces[replace_id] = new_tile;
                        else
                            this.r_pieces.push(new_tile);
                        this.dummyPieces.addChild(new_tile);
                    }
                    else {
                        if (replace_id >= 0)
                            this.r_pieces.splice(replace_id, 1);
                    }
                }
                else if (true_len > p_cnt) {
                    while (this.r_pieces.length > p_cnt) {
                        var last_tile = this.r_pieces.pop();
                        if (last_tile == null)
                            continue;
                        last_tile.inputEnabled = false;
                        last_tile.events.onDragStart.removeAll(this);
                        this.game.tweens.removeFrom(last_tile);
                        this.game.add.tween(last_tile).to({ alpha: 0, x: Config.GW + p_w }, 500, Phaser.Easing.Linear.None, true);
                        last_tile.isShowed = false;
                    }
                }
            };
            GameMng.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.updateLeftTiles();
                this.updateRightTiles();
                this.timerUpdScaling -= dt;
                if (this.needUpdScaling && this.timerUpdScaling <= 0) {
                    this.timerUpdScaling = 0.2;
                    this.needUpdScaling = false;
                    this.updateScaling();
                }
                if (this.boomEff != null)
                    this.boomEff.update();
            };
            return GameMng;
        }(Phaser.Sprite));
        Client.GameMng = GameMng;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var LoadMng;
(function (LoadMng) {
    var SceneLoader = (function () {
        function SceneLoader(game) {
            this.isFinalLoaded = false;
            this.onAllLoadedSignal = new Phaser.Signal();
            this.game = game;
            this.loader = new Phaser.Loader(game);
        }
        SceneLoader.prototype.startLoading = function () {
            this.loader.image('trophybg', './assets/sprites/trophybg.png');
            this.loader.image('fireworks', './assets/sprites/fireworks.png');
            this.loader.onLoadComplete.addOnce(this.onStageLoaded, this);
            this.loader.start();
        };
        SceneLoader.prototype.onStageLoaded = function () {
            this.isFinalLoaded = true;
            LogMng.debug('onAllResLoaded...');
            this.onAllLoadedSignal.dispatch();
        };
        return SceneLoader;
    }());
    LoadMng.SceneLoader = SceneLoader;
})(LoadMng || (LoadMng = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var AREA_POS = [
            { x: 0, y: -120 },
            { x: 0, y: -140 },
            { x: 0, y: -150 },
            { x: 0, y: -160 },
            { x: 0, y: -195 },
            { x: 0, y: -195 },
            { x: 0, y: -195 },
        ];
        var Bloon = (function (_super) {
            __extends(Bloon, _super);
            function Bloon(game, x, y, id) {
                var _this = _super.call(this, game, x, y) || this;
                _this.id = id;
                _this.bloon = new Phaser.Sprite(game, 0, 0, 'final', 'ballon2_' + String(id));
                _this.bloon.anchor.set(0.5, 1);
                _this.addChild(_this.bloon);
                _this.click_area = new Phaser.Graphics(_this.game, AREA_POS[_this.id].x, AREA_POS[_this.id].y);
                _this.click_area.beginFill(0, 0);
                _this.click_area.drawCircle(0, 0, 80);
                _this.click_area.endFill();
                _this.click_area.inputEnabled = true;
                _this.click_area.input.useHandCursor = true;
                _this.click_area.events.onInputDown.addOnce(_this.onClick, _this);
                _this.addChild(_this.click_area);
                _this.boomEff = new Client.BoomEff(_this.game, AREA_POS[_this.id].x, AREA_POS[_this.id].y);
                _this.boomEff.part_cnt = 5;
                _this.boomEff.scale.set(0.5);
                _this.boomEff.grav_y = 100;
                _this.boomEff.radius = 50;
                _this.boomEff.part_live_time = 1;
                _this.boomEff.spd_x_min = 150;
                _this.boomEff.spd_x_max = 250;
                _this.boomEff.spd_y_min = 150;
                _this.boomEff.spd_y_max = 300;
                _this.addChild(_this.boomEff);
                return _this;
            }
            Bloon.prototype.onClick = function () {
                this.click_area.visible = false;
                this.bloon.visible = false;
                this.boomEff.doEffect();
                SndMng.sfxBloon();
            };
            Bloon.prototype.update = function () {
                this.boomEff.update();
            };
            return Bloon;
        }(Phaser.Sprite));
        Client.Bloon = Bloon;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var Rocket = (function (_super) {
            __extends(Rocket, _super);
            function Rocket(game, x, y) {
                var _this = _super.call(this, game, x, y) || this;
                var smoke = new Phaser.Sprite(game, 0, -60, 'final', 'smoke');
                smoke.anchor.set(0.5, 0);
                _this.addChild(smoke);
                _this.fire = new Phaser.Sprite(game, 0 - 11, 75, 'final', 'fire');
                _this.fire.anchor.set(0.5, 0);
                _this.fire.scale.y = 0.9;
                _this.game.add.tween(_this.fire.scale).to({ y: 1.1 }, 100, null, true, 0, -1, true);
                _this.addChild(_this.fire);
                var rocket = new Phaser.Sprite(game, 0 - 11, 120, 'final', 'Rocket');
                rocket.anchor.set(0.5, 1);
                _this.addChild(rocket);
                return _this;
            }
            Rocket.prototype.free = function () {
                this.game.tweens.removeFrom(this.fire.scale);
                this.fire = null;
            };
            return Rocket;
        }(Phaser.Sprite));
        Client.Rocket = Rocket;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var TileSideType;
        (function (TileSideType) {
            TileSideType[TileSideType["line"] = 0] = "line";
            TileSideType[TileSideType["inner"] = 1] = "inner";
            TileSideType[TileSideType["outer"] = 2] = "outer";
        })(TileSideType = Client.TileSideType || (Client.TileSideType = {}));
        ;
        var TileMask = (function (_super) {
            __extends(TileMask, _super);
            function TileMask(game, x, y, t_size_w, t_size_h, topType, rType, botType, leftType) {
                var _this = _super.call(this, game, x, y) || this;
                _this.tile_size_w = t_size_w;
                _this.tile_size_h = t_size_h;
                _this.topType = topType;
                _this.rType = rType;
                _this.botType = botType;
                _this.leftType = leftType;
                return _this;
            }
            TileMask.prototype.getMask = function (aIsFill, aBorderColor, aBorderThik, aBorderAlpha, aParent, x0, y0) {
                if (aBorderColor === void 0) { aBorderColor = 0xFFFFFF; }
                if (aBorderThik === void 0) { aBorderThik = 3; }
                if (aBorderAlpha === void 0) { aBorderAlpha = 0.3; }
                if (aParent === void 0) { aParent = null; }
                if (x0 === void 0) { x0 = 0; }
                if (y0 === void 0) { y0 = 0; }
                var hc1 = new Phaser.Point(0.4, -0.05);
                var hp1 = new Phaser.Point(0.36, 0);
                var hc2 = new Phaser.Point(0.3, 0.2);
                var hp2 = new Phaser.Point(0.5, 0.2);
                var hc3 = new Phaser.Point(1 - hc2.x, hc2.y);
                var hp3 = new Phaser.Point(1 - hp1.x, hp1.y);
                var hc4 = new Phaser.Point(1 - hc1.x, hc1.y);
                var hp4 = new Phaser.Point(1, 0);
                var topInLines = [
                    { cx: hc1.x, cy: hc1.y, x: hp1.x, y: hp1.y },
                    { cx: hc2.x, cy: hc2.y, x: hp2.x, y: hp2.y },
                    { cx: hc3.x, cy: hc3.y, x: hp3.x, y: hp3.y },
                    { cx: hc4.x, cy: hc4.y, x: hp4.x, y: hp4.y }
                ];
                var topOutLines = [
                    { cx: hc1.x, cy: -hc1.y, x: hp1.x, y: -hp1.y },
                    { cx: hc2.x, cy: -hc2.y, x: hp2.x, y: -hp2.y },
                    { cx: hc3.x, cy: -hc3.y, x: hp3.x, y: -hp3.y },
                    { cx: hc4.x, cy: -hc4.y, x: hp4.x, y: -hp4.y }
                ];
                var rInLines = [
                    { cx: 1 - hc1.y, cy: hc1.x, x: 1 - hp1.y, y: hp1.x },
                    { cx: 1 - hc2.y, cy: hc2.x, x: 1 - hp2.y, y: hp2.x },
                    { cx: 1 - hc2.y, cy: 1 - hc2.x, x: 1 - hp1.y, y: 1 - hp1.x },
                    { cx: 1 - hc1.y, cy: 1 - hc1.x, x: 1, y: 1 }
                ];
                var rOutLines = [
                    { cx: 1 + hc1.y, cy: hc1.x, x: 1 + hp1.y, y: hp1.x },
                    { cx: 1 + hc2.y, cy: hc2.x, x: 1 + hp2.y, y: hp2.x },
                    { cx: 1 + hc2.y, cy: 1 - hc2.x, x: 1 + hp1.y, y: 1 - hp1.x },
                    { cx: 1 + hc1.y, cy: 1 - hc1.x, x: 1, y: 1 }
                ];
                var botInLines = [
                    { cx: 1 - hc1.x, cy: 1 - hc1.y, x: 1 - hp1.x, y: 1 - hp1.y },
                    { cx: 1 - hc2.x, cy: 1 - hc2.y, x: 1 - hp2.x, y: 1 - hp2.y },
                    { cx: 1 - hc3.x, cy: 1 - hc3.y, x: 1 - hp3.x, y: 1 - hp3.y },
                    { cx: 1 - hc4.x, cy: 1 - hc4.y, x: 1 - hp4.x, y: 1 - hp4.y }
                ];
                var botOutLines = [
                    { cx: 1 - hc1.x, cy: 1 + hc1.y, x: 1 - hp1.x, y: 1 + hp1.y },
                    { cx: 1 - hc2.x, cy: 1 + hc2.y, x: 1 - hp2.x, y: 1 + hp2.y },
                    { cx: 1 - hc3.x, cy: 1 + hc3.y, x: 1 - hp3.x, y: 1 + hp3.y },
                    { cx: 1 - hc4.x, cy: 1 + hc4.y, x: 1 - hp4.x, y: 1 + hp4.y }
                ];
                var leftInLines = [
                    { cx: hc1.y, cy: 1 - hc1.x, x: hp1.y, y: 1 - hp1.x },
                    { cx: hc2.y, cy: 1 - hc2.x, x: hp2.y, y: 1 - hp2.x },
                    { cx: hc2.y, cy: hc2.x, x: hp1.y, y: hp1.x },
                    { cx: hc1.y, cy: hc1.x, x: 0, y: 0 }
                ];
                var leftOutLines = [
                    { cx: -hc1.y, cy: 1 - hc1.x, x: -hp1.y, y: 1 - hp1.x },
                    { cx: -hc2.y, cy: 1 - hc2.x, x: -hp2.y, y: 1 - hp2.x },
                    { cx: -hc2.y, cy: hc2.x, x: -hp1.y, y: hp1.x },
                    { cx: -hc1.y, cy: hc1.x, x: 0, y: 0 }
                ];
                var f_w = this.tile_size_w;
                var f_h = this.tile_size_h;
                var mask = aParent != null ? aParent : new Phaser.Graphics(this.game, 0, 0);
                if (aIsFill)
                    mask.beginFill(0x00AA00);
                else
                    mask.beginFill(0x000000, 0);
                mask.lineStyle(aBorderThik, aBorderColor, aBorderAlpha);
                mask.moveTo(x0, y0);
                switch (this.topType) {
                    case TileSideType.line:
                        mask.lineTo(x0 + 1 * f_w, y0 + 0);
                        break;
                    case TileSideType.inner:
                        for (var i = 0; i < topInLines.length; i++) {
                            mask.quadraticCurveTo(x0 + topInLines[i].cx * f_w, y0 + topInLines[i].cy * f_h, x0 + topInLines[i].x * f_w, y0 + topInLines[i].y * f_h);
                        }
                        break;
                    case TileSideType.outer:
                        for (var i = 0; i < topOutLines.length; i++) {
                            mask.quadraticCurveTo(x0 + topOutLines[i].cx * f_w, y0 + topOutLines[i].cy * f_h, x0 + topOutLines[i].x * f_w, y0 + topOutLines[i].y * f_h);
                        }
                        break;
                }
                switch (this.rType) {
                    case TileSideType.line:
                        mask.lineTo(x0 + 1 * f_w, y0 + 1 * f_h);
                        break;
                    case TileSideType.inner:
                        for (var i = 0; i < rInLines.length; i++) {
                            mask.quadraticCurveTo(x0 + rInLines[i].cx * f_w, y0 + rInLines[i].cy * f_h, x0 + rInLines[i].x * f_w, y0 + rInLines[i].y * f_h);
                        }
                        break;
                    case TileSideType.outer:
                        for (var i = 0; i < rOutLines.length; i++) {
                            mask.quadraticCurveTo(x0 + rOutLines[i].cx * f_w, y0 + rOutLines[i].cy * f_h, x0 + rOutLines[i].x * f_w, y0 + rOutLines[i].y * f_h);
                        }
                        break;
                }
                switch (this.botType) {
                    case TileSideType.line:
                        mask.lineTo(x0 + 0, y0 + 1 * f_h);
                        break;
                    case TileSideType.inner:
                        for (var i = 0; i < botInLines.length; i++) {
                            mask.quadraticCurveTo(x0 + botInLines[i].cx * f_w, y0 + botInLines[i].cy * f_h, x0 + botInLines[i].x * f_w, y0 + botInLines[i].y * f_h);
                        }
                        break;
                    case TileSideType.outer:
                        for (var i = 0; i < botOutLines.length; i++) {
                            mask.quadraticCurveTo(x0 + botOutLines[i].cx * f_w, y0 + botOutLines[i].cy * f_h, x0 + botOutLines[i].x * f_w, y0 + botOutLines[i].y * f_h);
                        }
                        break;
                }
                switch (this.leftType) {
                    case TileSideType.line:
                        mask.lineTo(x0 + 0, y0 + 0);
                        break;
                    case TileSideType.inner:
                        for (var i = 0; i < leftInLines.length; i++) {
                            mask.quadraticCurveTo(x0 + leftInLines[i].cx * f_w, y0 + leftInLines[i].cy * f_h, x0 + leftInLines[i].x * f_w, y0 + leftInLines[i].y * f_h);
                        }
                        break;
                    case TileSideType.outer:
                        for (var i = 0; i < leftOutLines.length; i++) {
                            mask.quadraticCurveTo(x0 + leftOutLines[i].cx * f_w, y0 + leftOutLines[i].cy * f_h, x0 + leftOutLines[i].x * f_w, y0 + leftOutLines[i].y * f_h);
                        }
                        break;
                }
                return mask;
            };
            return TileMask;
        }(Phaser.Sprite));
        Client.TileMask = TileMask;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var TilePuzzle = (function (_super) {
            __extends(TilePuzzle, _super);
            function TilePuzzle(game, x, y, tx_id, ty_id, aTileImg, aMask, aMaskDx, aMaskDy) {
                var _this = _super.call(this, game, x, y) || this;
                _this.tx_id = 0;
                _this.ty_id = 0;
                _this.mask_dx = 0;
                _this.mask_dy = 0;
                _this.pad_x = 0;
                _this.pad_y = 0;
                _this.isShowed = false;
                _this.isComplete = false;
                _this.isDragged = false;
                _this.tx_id = tx_id;
                _this.ty_id = ty_id;
                _this.mask_dx = aMaskDx;
                _this.mask_dy = aMaskDy;
                _this.tileImg = aTileImg;
                _this.tileMask = aMask;
                _this.tMask = _this.tileMask.getMask(true, 0, 0, 1);
                _this.tBorder = _this.tileMask.getMask(false, 0xFFFFFF, _this.tileImg.width / 70, 1);
                _this.tMask.x = _this.tBorder.x = aMaskDx;
                _this.tMask.y = _this.tBorder.y = aMaskDy;
                _this.pad_x = -_this.tileImg.width / 2;
                _this.pad_y = -_this.tileImg.height / 2;
                _this.tileImg.x += _this.pad_x;
                _this.tileImg.y += _this.pad_y;
                _this.tMask.x += _this.pad_x;
                _this.tMask.y += _this.pad_y;
                _this.tBorder.x += _this.pad_x;
                _this.tBorder.y += _this.pad_y;
                _this.addChild(_this.tileImg);
                _this.addChild(_this.tMask);
                _this.addChild(_this.tBorder);
                _this.tileImg.mask = _this.tMask;
                var daIncBorder_x = _this.tileImg.width * 0.5;
                var daIncBorder_y = _this.tileImg.height * 0.5;
                _this.dragArea = new Phaser.Graphics(game, -daIncBorder_x, -daIncBorder_y);
                _this.dragArea.beginFill(0xFFFFFF, 0);
                _this.dragArea.drawRect(0, 0, _this.tileImg.width + daIncBorder_x * 2, _this.tileImg.height + daIncBorder_y * 2);
                _this.dragArea.endFill();
                _this.dragArea.x += _this.pad_x;
                _this.dragArea.y += _this.pad_y;
                _this.addChild(_this.dragArea);
                return _this;
            }
            TilePuzzle.prototype.doInvis = function () {
                this.tileImg.alpha = 0;
            };
            TilePuzzle.prototype.doVis = function () {
                this.tileImg.alpha = 1;
            };
            TilePuzzle.prototype.getOldX = function () {
                return this.x + this.pad_x;
            };
            TilePuzzle.prototype.getOldY = function () {
                return this.y + this.pad_y;
            };
            return TilePuzzle;
        }(Phaser.Sprite));
        Client.TilePuzzle = TilePuzzle;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var Boot = (function (_super) {
            __extends(Boot, _super);
            function Boot() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            Boot.prototype.preload = function () {
                this.load.image('bg', './assets/sprites/puzzle/bg.png');
            };
            Boot.prototype.create = function () {
                this.stage.setBackgroundColor(0x37b4f3);
                this.input.maxPointers = 1;
                this.stage.disableVisibilityChange = true;
                ScaleManager.init(this.game, Config.DOM_PARENT_ID, Config.GW, Config.GH, Config.GSW, Config.GSH);
                LogMng.setMode(LogMng.MODE_RELEASE);
                LogMng.system('log mode: ' + LogMng.getMode());
                this.time.events.add(100, this.onWaitComplete, this);
            };
            Boot.prototype.onWaitComplete = function () {
                this.game.state.start(States.PRELOADER, true, false);
            };
            return Boot;
        }(Phaser.State));
        Client.Boot = Boot;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var GameCtrl = (function (_super) {
            __extends(GameCtrl, _super);
            function GameCtrl() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            GameCtrl.prototype.create = function () {
                var loadMng = new LoadMng.SceneLoader(this.game);
                loadMng.startLoading();
                this.bg = new Phaser.Sprite(this.game, 0, 0, 'bg');
                this.add.existing(this.bg);
                this.gameMng = new Client.GameMng(this.game, 0, 0);
                this.gameMng.onGameCompleteEvent.add(this.onGameComplete, this);
                this.add.existing(this.gameMng);
                this.dummyGui = this.add.sprite(0, 0);
                this.topPanel = new Client.TopPanel(this.game, Config.GW / 2, 0, [2, 6, 12, 20, 30, 42], Params.q_dif_id);
                this.topPanel.onHelpClickSignal.add(this.onHelpClicked, this);
                this.topPanel.onDifChangeSignal.add(this.onDifChanged, this);
                this.topPanel.onPanelOpenSignal.add(this.onTopPanelOpen, this);
                this.topPanel.onPanelHidedSignal.add(this.onTopPanelHided, this);
                this.topPanel.onCloseClickSignal.add(this.onCloseClick, this);
                this.updateTopPanel();
                this.topPanel.setTopPanelVisible(Params.q_version == 2);
                this.topPanel.openPanel();
                this.dummyGui.addChild(this.topPanel);
                this.winWnd = new Client.WinWindow(this.game, Config.GW / 2, Config.GH / 2);
                this.winWnd.onRestartClickEvent.add(this.onRestartClick, this);
                this.winWnd.onCloseClickEvent.add(this.onCloseClick, this);
                this.dummyGui.addChild(this.winWnd);
                this.onWndResize();
                ScaleManager.onWindowResize.add(this.onWndResize, this);
                this.gameMng.restartGame(this.topPanel.getPiecesCount());
                this.topPanel.showHelp();
                SndMng.sfxStartGame();
                SndMng.fadeInMusic();
            };
            GameCtrl.prototype.onWndResize = function () {
                if (ScaleManager.gameViewH < Config.GH) {
                    this.gameMng.setAvailableHeight(ScaleManager.gameViewH - 100);
                }
                else {
                    this.gameMng.setAvailableHeight(Config.GH - 100);
                }
            };
            GameCtrl.prototype.onTopPanelOpen = function () {
                this.gameMng.setTopPadding(60);
            };
            GameCtrl.prototype.onTopPanelHided = function () {
                this.gameMng.setTopPadding(30);
            };
            GameCtrl.prototype.onHelpClicked = function (isHelp) {
                isHelp ? this.gameMng.showHelp() : this.gameMng.hideHelp();
            };
            GameCtrl.prototype.onDifChanged = function (aDif) {
                this.gameMng.restartGame(this.topPanel.getPiecesCount());
            };
            GameCtrl.prototype.onGameComplete = function () {
                this.winWnd.showScreen();
            };
            GameCtrl.prototype.onRestartClick = function () {
                this.winWnd.hideScreen();
                this.gameMng.restartGame(this.topPanel.getPiecesCount());
                SndMng.sfxStartGame();
            };
            GameCtrl.prototype.onCloseClick = function () {
                pressedClose(Params.pieces_cnt);
            };
            GameCtrl.prototype.updateTopPanel = function () {
                if (ScaleManager.gameViewH < Config.GH) {
                    this.topPanel.y = (Config.GH - ScaleManager.gameViewH) / 2 - 1;
                }
                else {
                    this.topPanel.y = 0;
                }
                this.topPanel.update();
            };
            GameCtrl.prototype.updateGameDummy = function () {
                if (ScaleManager.gameViewH < Config.GH) {
                }
                else {
                    this.topPanel.y = 0;
                }
            };
            GameCtrl.prototype.update = function () {
                var dt = this.game.time.elapsed / 1000;
                this.updateTopPanel();
                this.updateGameDummy();
                if (this.winWnd != null)
                    this.winWnd.update();
            };
            return GameCtrl;
        }(Phaser.State));
        Client.GameCtrl = GameCtrl;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var MainMenu = (function (_super) {
            __extends(MainMenu, _super);
            function MainMenu() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            MainMenu.prototype.create = function () {
                this.game.state.start(States.GAME, true, false);
            };
            MainMenu.prototype.update = function () {
            };
            return MainMenu;
        }(Phaser.State));
        Client.MainMenu = MainMenu;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var PuzzleGame;
(function (PuzzleGame) {
    var Client;
    (function (Client) {
        var Preloader = (function (_super) {
            __extends(Preloader, _super);
            function Preloader() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            Preloader.prototype.preload = function () {
                 this.load.crossOrigin = "anonymous";
                var q_v = MyUtils.getQueryValue('v');
                LogMng.debug('v = ' + q_v);
                if (q_v != null && q_v != undefined && q_v >= 1 && q_v <= 2)
                    Params.q_version = q_v;
                LogMng.debug('Params.q_version = ' + Params.q_version);
                var q_dif = MyUtils.getQueryValue('dif');
                LogMng.debug('dif = ' + q_dif);
                if (q_dif != null && q_dif != undefined && q_dif > 0)
                    Params.q_dif_id = q_dif - 1;
                LogMng.debug('Params.q_dif_id = ' + Params.q_dif_id);
                var q_img = MyUtils.getQueryValue('img');
                LogMng.debug('img = ' + q_img);
                if (q_img != null && q_img != undefined && q_img != '')
                    Params.q_img = q_img;
                LogMng.debug('Params.q_img = ' + Params.q_img);
                var q_is_global = MyUtils.getQueryValue('global');
                LogMng.debug('q_is_global = ' + q_is_global);
                if (q_is_global != null && q_is_global != undefined && q_is_global != '')
                    Params.q_is_global_pic = Number(q_is_global) == 1;
                LogMng.debug('Params.q_is_global_pic = ' + Params.q_is_global_pic);
                this.add.sprite(0, 0, 'bg');
                if (Params.q_is_global_pic) {
                    this.load.image('img', Params.q_img);
                }
                else {
                    this.load.image('img', './assets/sprites/' + Params.q_img);
                }
                var assName = 'puzzle_game_mid_1200x900';
                this.load.atlasJSONArray('game', './assets/atlases/' + assName + '.png', './assets/atlases/' + assName + '.json');
                this.load.atlasJSONArray('final', './assets/atlases/final_mid_1200x900.png', './assets/atlases/final_mid_1200x900.json');
                SndMng.init(this.game, true);
                var sndFiles = SndMng.LOAD_SOUNDS;
                for (var i = 0; i < sndFiles.length; i++) {
                    var mp3 = './assets/sounds/mp3/' + sndFiles[i] + '.mp3';
                    this.load.audio(sndFiles[i], [mp3]);
                }
            };
            Preloader.prototype.create = function () {
                Params.isIOS =
                    this.game.device.iOS ||
                        this.game.device.iPhone ||
                        this.game.device.iPhone4 ||
                        this.game.device.iPad ||
                        this.game.device.mobileSafari;
                this.startMainMenu();
            };
            Preloader.prototype.startMainMenu = function () {
                this.game.state.start(States.MAINMENU, true, false);
            };
            return Preloader;
        }(Phaser.State));
        Client.Preloader = Preloader;
    })(Client = PuzzleGame.Client || (PuzzleGame.Client = {}));
})(PuzzleGame || (PuzzleGame = {}));
var States;
(function (States) {
    States.BOOT = 'Boot';
    States.PRELOADER = 'Preloader';
    States.MAINMENU = 'MainMenu';
    States.GAME = 'Game';
})(States || (States = {}));
var LogMng;
(function (LogMng) {
    LogMng.MODE_DEBUG = 'MODE_DEBUG';
    LogMng.MODE_RELEASE = 'MODE_RELEASE';
    var DEBUG = 'DEBUG';
    var INFO = 'INFO';
    var NETWORK = 'NETWORK';
    var WARNING = 'WARNING';
    var ERROR = 'ERROR';
    var mode = LogMng.MODE_DEBUG;
    var levels = [DEBUG, INFO, NETWORK, WARNING, ERROR];
    function setMode(aMode) {
        mode = aMode;
        switch (mode) {
            case LogMng.MODE_DEBUG:
                levels = [DEBUG, INFO, NETWORK, WARNING, ERROR];
                break;
            case LogMng.MODE_RELEASE:
                levels = [WARNING, ERROR];
                break;
        }
    }
    LogMng.setMode = setMode;
    function getMode() {
        return mode;
    }
    LogMng.getMode = getMode;
    function getCSS(bgColor) {
        return 'background: ' + bgColor + ';' +
            'background-repeat: no-repeat;' +
            'color: #1df9a8;' +
            'line-height: 16px;' +
            'padding: 1px 0;' +
            'margin: 0;' +
            'user-select: none;' +
            '-webkit-user-select: none;' +
            '-moz-user-select: none;';
    }
    ;
    function getLink(color) {
        return 'background: ' + color + ';' +
            'background-repeat: no-repeat;' +
            'font-size: 12px;' +
            'color: #446d96;' +
            'line-height: 14px';
    }
    ;
    function log(aMsg, aLevel) {
        if (aLevel === void 0) { aLevel = DEBUG; }
        if (levels.indexOf(aLevel) < 0)
            return;
        var css = '';
        switch (aLevel) {
            case INFO:
                css = 'background: #308AE4; color: #fff; padding: 1px 4px';
                break;
            case WARNING:
                css = 'background: #f7a148; color: #fff; padding: 1px 4px';
                break;
            case ERROR:
                css = 'background: #DB5252; color: #fff; padding: 1px 4px';
                break;
            case NETWORK:
                css = 'background: #7D2998; color: #fff; padding: 1px 4px';
                break;
            case DEBUG:
            default:
                css = 'background: #ADADAD; color: #fff; padding: 1px 4px';
        }
        console.log("%c%s", css, aLevel, aMsg);
    }
    ;
    function system(aMsg, aLink) {
        if (aLink === void 0) { aLink = ''; }
        console.log("%c %c %c %s %c %c %c %c%s", getCSS('#5C6166'), getCSS('#4F5357'), getCSS('#313335'), aMsg, getCSS('#4F5357'), getCSS('#5C6166'), getLink('none'), getLink('none'), aLink);
    }
    LogMng.system = system;
    function debug(aMsg) {
        log(aMsg, DEBUG);
    }
    LogMng.debug = debug;
    function info(aMsg) {
        log(aMsg, INFO);
    }
    LogMng.info = info;
    function net(aMsg) {
        log(aMsg, NETWORK);
    }
    LogMng.net = net;
    function warn(aMsg) {
        log(aMsg, WARNING);
    }
    LogMng.warn = warn;
    function error(aMsg) {
        log(aMsg, ERROR);
    }
    LogMng.error = error;
})(LogMng || (LogMng = {}));
var MyMath;
(function (MyMath) {
    var RectABCD = (function () {
        function RectABCD(a, b, c, d) {
            this.a = a;
            this.b = b;
            this.c = c;
            this.d = d;
        }
        return RectABCD;
    }());
    MyMath.RectABCD = RectABCD;
    function randomInRange(aMin, aMax) {
        return Math.random() * Math.abs(aMax - aMin) + aMin;
    }
    MyMath.randomInRange = randomInRange;
    function randomIntInRange(aMin, aMax) {
        return Math.round(randomInRange(aMin, aMax));
    }
    MyMath.randomIntInRange = randomIntInRange;
    function toRadian(aDeg) {
        return aDeg * Math.PI / 180;
    }
    MyMath.toRadian = toRadian;
    function toDeg(aRad) {
        return aRad * 180 / Math.PI;
    }
    MyMath.toDeg = toDeg;
    function IsPointInTriangle(ax, ay, bx, by, cx, cy, px, py) {
        var b0x, b0y, c0x, c0y, p0x, p0y;
        var m, l;
        var res = false;
        b0x = bx - ax;
        b0y = by - ay;
        c0x = cx - ax;
        c0y = cy - ay;
        p0x = px - ax;
        p0y = py - ay;
        m = (p0x * b0y - b0x * p0y) / (c0x * b0y - b0x * c0y);
        if (m >= 0 && m <= 1) {
            l = (p0x - m * c0x) / b0x;
            if (l >= 0 && (m + l) <= 1)
                res = true;
        }
        return res;
    }
    MyMath.IsPointInTriangle = IsPointInTriangle;
    function isPointInRect(rect, p) {
        return IsPointInTriangle(rect.a.x, rect.a.y, rect.b.x, rect.b.y, rect.c.x, rect.c.y, p.x, p.y) &&
            IsPointInTriangle(rect.c.x, rect.c.y, rect.d.x, rect.d.y, rect.a.x, rect.a.y, p.x, p.y);
    }
    MyMath.isPointInRect = isPointInRect;
    function isCirclesIntersect(x1, y1, r1, x2, y2, r2) {
        var veclen = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
        return veclen <= r1 + r2;
    }
    MyMath.isCirclesIntersect = isCirclesIntersect;
})(MyMath || (MyMath = {}));
var MyUtils;
(function (MyUtils) {
    var query_values = null;
    function readQueryValues() {
        var vals = {};
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (typeof vals[pair[0]] === "undefined") {
                vals[pair[0]] = decodeURIComponent(pair[1]);
            }
            else if (typeof vals[pair[0]] === "string") {
                var arr = [vals[pair[0]], decodeURIComponent(pair[1])];
                vals[pair[0]] = arr;
            }
            else {
                vals[pair[0]].push(decodeURIComponent(pair[1]));
            }
        }
        query_values = vals;
    }
    function getQueryValue(aValName) {
        if (query_values == null)
            readQueryValues();
        return query_values[aValName];
    }
    MyUtils.getQueryValue = getQueryValue;
})(MyUtils || (MyUtils = {}));
var ScaleManager = (function () {
    function ScaleManager() {
    }
    ScaleManager.init = function (aGame, aDomId, GW, GH, GSW, GSH) {
        var _this = this;
        this.game = aGame;
        this.dom_id = aDomId;
        this.dom = document.getElementById(this.dom_id);
        this.game_w = GW;
        this.game_h = GH;
        this.game_sw = GSW;
        this.game_sh = GSH;
        aGame.scale.scaleMode = Phaser.ScaleManager.USER_SCALE;
        ScaleManager.SizeCalculation();
        window.onresize = function () {
            ScaleManager.SizeCalculation();
            _this.onWindowResize.dispatch();
        };
    };
    ScaleManager.doEventOriChange = function () {
        this.onOrientationChanged.dispatch(this.isPortrait);
    };
    ScaleManager.SizeCalculation = function () {
        var wnd = {
            w: window.innerWidth,
            h: window.innerHeight
        };
        var g = {
            w: ScaleManager.game_w,
            h: ScaleManager.game_h,
            sw: ScaleManager.game_sw,
            sh: ScaleManager.game_sh
        };
        var gw;
        var gh;
        if (g.h / g.w > wnd.h / wnd.w) {
            if (g.sh / g.w > wnd.h / wnd.w) {
                gh = wnd.h * g.h / g.sh;
                gw = gh * g.w / g.h;
            }
            else {
                gw = wnd.w;
                gh = gw * g.h / g.w;
            }
        }
        else {
            if (g.h / g.sw > wnd.h / wnd.w) {
                gh = wnd.h;
                gw = gh * g.w / g.h;
            }
            else {
                gw = wnd.w * g.w / g.sw;
                gh = gw * g.h / g.w;
            }
        }
        var sclX = gw / g.sw;
        var sclY = gh / g.sh;
        var newScale = Math.min(sclX, sclY);
        ScaleManager.game.scale.setUserScale(newScale, newScale, 0, 0);
        this.dtx = (wnd.w - gw) / 2;
        this.dty = (wnd.h - gh) / 2;
        this.gameViewW = this.game_w + 2 * this.dtx / newScale;
        if (this.gameViewW > this.game_w)
            this.gameViewW = this.game_w;
        this.gameViewH = this.game_h + 2 * this.dty / newScale;
        if (this.gameViewH > this.game_h)
            this.gameViewH = this.game_h;
        this.dom.style.marginLeft = Math.round(this.dtx).toString() + 'px';
        this.dom.style.marginTop = Math.round(this.dty).toString() + 'px';
        this.dom.style.maxWidth = String(gw) + 'px';
        this.dom.style.maxHeight = String(gh) + 'px';
        ScaleManager.game.scale.refresh();
        var oldOri = this.isPortrait;
        this.isPortrait = wnd.h > wnd.w;
        if (this.isPortrait != oldOri)
            this.doEventOriChange();
    };
    ScaleManager.handleIncorrect = function () {
        if (!this.game.device.desktop) {
            document.getElementById("turn").style.display = "block";
            ScaleManager.game.world.isPaused = true;
        }
    };
    ScaleManager.handleCorrect = function () {
        if (!this.game.device.desktop) {
            document.getElementById("turn").style.display = "none";
            ScaleManager.game.world.isPaused = false;
        }
        setTimeout("window.scrollTo(0,0)", 1000);
    };
    ScaleManager.dom_id = '';
    ScaleManager.dtx = 0;
    ScaleManager.dty = 0;
    ScaleManager.onOrientationChanged = new Phaser.Signal();
    ScaleManager.onWindowResize = new Phaser.Signal();
    return ScaleManager;
}());
var SndMng;
(function (SndMng) {
    var MUSIC = 'music';
    SndMng.LOAD_SOUNDS = ['click', 'main_start', 'put2', 'bloon1', 'bloon2', 'bloon3', 'bloon4', 'win'];
    var MUS_MAX_VOL = 0.8;
    var game;
    var enabled;
    var music = null;
    function init(aGame, aEnabled) {
        game = aGame;
        enabled = aEnabled;
    }
    SndMng.init = init;
    function fadeInMusic(aVolFrom, aVolEnd, aDuration) {
        if (aVolFrom === void 0) { aVolFrom = 0; }
        if (aVolEnd === void 0) { aVolEnd = 1; }
        if (aDuration === void 0) { aDuration = 500; }
        if (aVolEnd > MUS_MAX_VOL)
            aVolEnd = MUS_MAX_VOL;
        if (music == null) {
            return;
        }
        if (enabled) {
            game.tweens.removeFrom(music);
            if (!music.isPlaying) {
                music.volume = aVolFrom;
                music.play();
            }
            game.add.tween(music).to({ volume: aVolEnd }, aDuration, Phaser.Easing.Linear.None, true);
        }
    }
    SndMng.fadeInMusic = fadeInMusic;
    function fadeOutMusic(aVol, aDuration) {
        if (aVol === void 0) { aVol = 0; }
        if (aDuration === void 0) { aDuration = 500; }
        if (music == null)
            return;
        game.tweens.removeFrom(music);
        return game.add.tween(music).to({ volume: aVol }, aDuration, Phaser.Easing.Linear.None, true);
    }
    SndMng.fadeOutMusic = fadeOutMusic;
    function setEnabled(aEnabled) {
        enabled = aEnabled;
        if (enabled) {
            fadeInMusic();
        }
        else {
            fadeOutMusic().onComplete.add(function () { music.stop(); });
        }
    }
    SndMng.setEnabled = setEnabled;
    function getEnabled() {
        return enabled;
    }
    SndMng.getEnabled = getEnabled;
    function sfxPlay(aName, aVol) {
        if (aVol === void 0) { aVol = 1; }
        if (!enabled)
            return;
        game.add.audio(aName, aVol).play();
    }
    SndMng.sfxPlay = sfxPlay;
    function sfxClick() {
        sfxPlay('click');
    }
    SndMng.sfxClick = sfxClick;
    function sfxStartGame() {
        sfxPlay('main_start');
    }
    SndMng.sfxStartGame = sfxStartGame;
    function sfxPut() {
        sfxPlay('put2');
    }
    SndMng.sfxPut = sfxPut;
    function sfxWin() {
        sfxPlay('win', 1);
    }
    SndMng.sfxWin = sfxWin;
    function sfxBloon() {
        sfxPlay('bloon' + String(MyMath.randomIntInRange(1, 4)));
    }
    SndMng.sfxBloon = sfxBloon;
})(SndMng || (SndMng = {}));
var TextUtils;
(function (TextUtils) {
    function addZero(aNum, aLen) {
        var text = String(aNum);
        while (text.length < aLen)
            text = '0' + text;
        return text;
    }
    TextUtils.addZero = addZero;
    function sizingBitmapTextByW(aBmpText, aW, aInc, aDec) {
        if (aBmpText.text == '' || aBmpText.height == 0 || aBmpText.width == 0) {
            LogMng.debug('TextUtils.ts sizingBitmapTextByW(): aBmpText.text == ""');
            LogMng.debug('TextUtils.ts sizingBitmapTextByW(): aBmpText.width = ' + aBmpText.width);
            LogMng.debug('TextUtils.ts sizingBitmapTextByW(): aBmpText.height = ' + aBmpText.height);
            return;
        }
        if (aInc) {
            if (aBmpText.width < aW) {
                while (aBmpText.width < aW) {
                    aBmpText.fontSize++;
                }
            }
        }
        if (aDec) {
            if (aBmpText.width > aW) {
                while (aBmpText.width > aW) {
                    aBmpText.fontSize--;
                }
            }
        }
    }
    TextUtils.sizingBitmapTextByW = sizingBitmapTextByW;
    function sizingBitmapTextByH(aBmpText, aH, aInc, aDec) {
        if (aBmpText.text == '' || aBmpText.height == 0 || aBmpText.width == 0) {
            LogMng.debug('TextUtils.ts sizingBitmapTextByH(): aBmpText.text == ""');
            LogMng.debug('TextUtils.ts sizingBitmapTextByH(): aBmpText.width = ' + aBmpText.width);
            LogMng.debug('TextUtils.ts sizingBitmapTextByH(): aBmpText.height = ' + aBmpText.height);
            return;
        }
        if (aInc) {
            if (aBmpText.height < aH) {
                while (aBmpText.height < aH) {
                    aBmpText.fontSize++;
                }
            }
        }
        if (aDec) {
            if (aBmpText.height > aH) {
                while (aBmpText.height > aH) {
                    aBmpText.fontSize--;
                }
            }
        }
    }
    TextUtils.sizingBitmapTextByH = sizingBitmapTextByH;
})(TextUtils || (TextUtils = {}));
var PhaserNineSlice;
(function (PhaserNineSlice) {
    var NineSlice = (function (_super) {
        __extends(NineSlice, _super);
        function NineSlice(game, x, y, key, frame, width, height, data) {
            var _this = _super.call(this, game, x, y, key, frame) || this;
            _this.baseTexture = _this.texture.baseTexture;
            _this.baseFrame = _this.texture.frame;
            if (frame !== null && !data) {
                data = game.cache.getNineSlice(frame);
            }
            else if (!data) {
                data = game.cache.getNineSlice(key);
            }
            if (undefined === data) {
                return _this;
            }
            _this.topSize = data.top;
            if (!data.left) {
                _this.leftSize = _this.topSize;
            }
            else {
                _this.leftSize = data.left;
            }
            if (!data.right) {
                _this.rightSize = _this.leftSize;
            }
            else {
                _this.rightSize = data.right;
            }
            if (!data.bottom) {
                _this.bottomSize = _this.topSize;
            }
            else {
                _this.bottomSize = data.bottom;
            }
            _this.loadTexture(new Phaser.RenderTexture(_this.game, _this.localWidth, _this.localHeight));
            _this.resize(width, height);
            return _this;
        }
        NineSlice.prototype.renderTexture = function () {
            this.texture.resize(this.localWidth, this.localHeight, true);
            var textureXs = [0, this.leftSize, this.baseFrame.width - this.rightSize, this.baseFrame.width];
            var textureYs = [0, this.topSize, this.baseFrame.height - this.bottomSize, this.baseFrame.height];
            var finalXs = [0, this.leftSize, this.localWidth - this.rightSize, this.localWidth];
            var finalYs = [0, this.topSize, this.localHeight - this.bottomSize, this.localHeight];
            for (var yi = 0; yi < 3; yi++) {
                for (var xi = 0; xi < 3; xi++) {
                    var s = this.createTexturePart(textureXs[xi], textureYs[yi], textureXs[xi + 1] - textureXs[xi], textureYs[yi + 1] - textureYs[yi]);
                    s.width = finalXs[xi + 1] - finalXs[xi];
                    s.height = finalYs[yi + 1] - finalYs[yi];
                    this.texture.renderXY(s, finalXs[xi], finalYs[yi]);
                }
            }
        };
        NineSlice.prototype.resize = function (width, height) {
            this.localWidth = width;
            this.localHeight = height;
            this.renderTexture();
        };
        NineSlice.prototype.createTexturePart = function (x, y, width, height) {
            var frame = new PIXI.Rectangle(this.baseFrame.x + this.texture.frame.x + x, this.baseFrame.y + this.texture.frame.y + y, Math.max(width, 1), Math.max(height, 1));
            return new Phaser.Sprite(this.game, 0, 0, new PIXI.Texture(this.baseTexture, frame));
        };
        return NineSlice;
    }(Phaser.Sprite));
    PhaserNineSlice.NineSlice = NineSlice;
})(PhaserNineSlice || (PhaserNineSlice = {}));
var PhaserNineSlice;
(function (PhaserNineSlice) {
    var Plugin = (function (_super) {
        __extends(Plugin, _super);
        function Plugin(game, parent) {
            var _this = _super.call(this, game, parent) || this;
            _this.addNineSliceCache();
            _this.addNineSliceFactory();
            _this.addNineSliceLoader();
            return _this;
        }
        Plugin.prototype.addNineSliceLoader = function () {
            Phaser.Loader.prototype.nineSlice = function (key, url, top, left, right, bottom) {
                var cacheData = {
                    top: top
                };
                if (left) {
                    cacheData.left = left;
                }
                if (right) {
                    cacheData.right = right;
                }
                if (bottom) {
                    cacheData.bottom = bottom;
                }
                this.addToFileList('image', key, url);
                this.game.cache.addNineSlice(key, cacheData);
            };
        };
        Plugin.prototype.addNineSliceFactory = function () {
            Phaser.GameObjectFactory.prototype.nineSlice = function (x, y, key, frame, width, height, group) {
                if (group === undefined) {
                    group = this.world;
                }
                var nineSliceObject = new PhaserNineSlice.NineSlice(this.game, x, y, key, frame, width, height);
                return group.add(nineSliceObject);
            };
            Phaser.GameObjectCreator.prototype.nineSlice = function (x, y, key, frame, width, height) {
                return new PhaserNineSlice.NineSlice(this.game, x, y, key, frame, width, height);
            };
        };
        Plugin.prototype.addNineSliceCache = function () {
            Phaser.Cache.prototype.nineSlice = {};
            Phaser.Cache.prototype.addNineSlice = function (key, data) {
                this.nineSlice[key] = data;
            };
            Phaser.Cache.prototype.getNineSlice = function (key) {
                var data = this.nineSlice[key];
                if (undefined === data) {
                    console.warn('Phaser.Cache.getNineSlice: Key "' + key + '" not found in Cache.');
                }
                return data;
            };
        };
        return Plugin;
    }(Phaser.Plugin));
    PhaserNineSlice.Plugin = Plugin;
})(PhaserNineSlice || (PhaserNineSlice = {}));
//# sourceMappingURL=puzzle.js.map
