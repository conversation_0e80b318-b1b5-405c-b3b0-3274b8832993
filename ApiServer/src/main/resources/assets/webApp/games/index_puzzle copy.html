<!DOCTYPE html>

<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

    <meta itemprop="image" content="http://test.monaxgames.com/kidjo/promo.png" />

    <title>Kidjo Puzzle Game</title>

    <style>
        html {
            overflow: hidden;
        }
        
        body {
            background-color: #37b4f3;
            font-family: 'Segoe UI', sans-serif;
            border: none;
            padding: 0;
            margin: 0;
        }
        
        #content {
            margin: 0;
            padding: 0;
        }
    </style>

    <script src="js/phaser.min.js"></script>
    <script src="js/game/puzzle.js"></script>
    <script>
        function pressedClose(p_cnt) {
            console.log('close pressed! puzzle pieces = ' + p_cnt);
            parent.postMessage("close","*");
        }
    </script>
</head>

<body>
    <div id="content"></div>
</body>

</html>