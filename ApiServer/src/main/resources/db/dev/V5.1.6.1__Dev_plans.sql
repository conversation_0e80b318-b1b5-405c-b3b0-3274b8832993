DELETE FROM plans_country;
DELETE FROM plans;
INSERT INTO plans(id, productId, planName, planType, active, bundle)
values (1, 10, 'default_one_product', 'monthly', true, false),
       (2, 10, 'default_one_product_yearly', 'yearly', true, false),
       (3, 10, 'europe_one_product', 'monthly', true, false),
       (4, 10, 'europe_one_product_yearly', 'yearly', true, false),
       (5, 10, 'swiss_one_product', 'monthly', true, false),
       (6, 10, 'swiss_one_product_yearly', 'yearly', true, false),
       -- Stories
       (7, 20, 'default_one_product', 'monthly', true, false),
       (8, 20, 'default_one_product_yearly', 'yearly', true, false),
       (9, 20, 'europe_one_product', 'monthly', true, false),
       (10, 20, 'europe_one_product_yearly', 'yearly', true, false),
       (11, 20, 'swiss_one_product', 'monthly', true, false),
       (12, 20, 'swiss_one_product_yearly', 'yearly', true, false),
       -- games
       (13, 30, 'default_one_product', 'monthly', true, false),
       (14, 30, 'default_one_product_yearly', 'yearly', true, false),
       (15, 30, 'europe_one_product', 'monthly', true, false),
       (16, 30, 'europe_one_product_yearly', 'yearly', true, false),
       (17, 30, 'swiss_one_product', 'monthly', true, false),
       (18, 30, 'swiss_one_product_yearly', 'yearly', true, false);
--  TODO added only for one product need to create plan for two product
-- EUROPE Plans
INSERT INTO plans_country(planId, countryId, price)
select 3, id, 6.00
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 4, id, 41.60
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 9, id, 6.00
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 10, id, 41.60
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 15, id, 6.00
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 16, id, 41.60
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

-- default plans
INSERT INTO plans_country(planId, countryId, price)
select 1, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 2, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 7, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 8, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 13, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 14, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

-- Swiss plan
INSERT INTO plans_country(planId, countryId, price)
select 5, id, 7.50
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 6, id, 53.80
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 11, id, 7.50
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 12, id, 53.80
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 17, id, 7.50
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 18, id, 53.80
from countries c
where short = 'CH';

delete from plans_addon;
delete from addOns;

INSERT INTO addOns(id, addOnName, price)
values (10, 'default_one_product_2', 3),
       (20, 'default_one_product_3', 5),
       (30, 'default_one_product_yearly_2', 21),
       (40, 'default_one_product_yearly_3', 36),
       (50, 'europe_one_product_2', 3.60),
       (60, 'europe_one_product_3', 6),
       (70, 'europe_one_product_yearly_2', 25),
       (80, 'europe_one_product_yearly_3', 42.90),
       (90, 'swiss_one_product_2', 4.20),
       (100, 'swiss_one_product_3', 7),
       (110, 'swiss_one_product_yearly_2', 32.20),
       (120, 'swiss_one_product_yearly_3', 55.20);

INSERT INTO plans_addon(planId, addOnId, active)
select id,10,true from plans where planName='default_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,20,true from plans where planName='default_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,30,true from plans where planName='default_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,40,true from plans where planName='default_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,50,true from plans where planName='europe_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,60,true from plans where planName='europe_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,70,true from plans where planName='europe_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,80,true from plans where planName='europe_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,90,true from plans where planName='swiss_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,100,true from plans where planName='swiss_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,110,true from plans where planName='swiss_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,120,true from plans where planName='swiss_one_product_yearly';
