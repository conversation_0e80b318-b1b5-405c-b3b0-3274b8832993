DELETE FROM plans_country;
DELETE FROM plans;
INSERT INTO plans(id, productId, planName, planType, active, bundle)
values (1, 10, 'default_one', 'monthly', true, false),
       (2, 10, 'default_one_yearly', 'yearly', true, false),
       (3, 10, 'europe_one', 'monthly', true, false),
       (4, 10, 'europe_one_yearly', 'yearly', true, false),
       (5, 10, 'swiss_one', 'monthly', true, false),
       (6, 10, 'swiss_one_yearly', 'yearly', true, false),
       -- Stories
       (7, 20, 'default_one', 'monthly', true, false),
       (8, 20, 'default_one_yearly', 'yearly', true, false),
       (9, 20, 'europe_one', 'monthly', true, false),
       (10, 20, 'europe_one_yearly', 'yearly', true, false),
       (11, 20, 'swiss_one', 'monthly', true, false),
       (12, 20, 'swiss_one_yearly', 'yearly', true, false),
       -- games
       (13, 30, 'default_one', 'monthly', true, false),
       (14, 30, 'default_one_yearly', 'yearly', true, false),
       (15, 30, 'europe_one', 'monthly', true, false),
       (16, 30, 'europe_one_yearly', 'yearly', true, false),
       (17, 30, 'swiss_one', 'monthly', true, false),
       (18, 30, 'swiss_one_yearly', 'yearly', true, false),
       -- Spanish Plan
       (19,10,'spanish_one','monthly',true,false),
       (20,10,'spanish_one_yearly','yearly',true,false),
       (21,20,'spanish_one','monthly',true,false),
       (22,20,'spanish_one_yearly','yearly',true,false),
       (23,30,'spanish_one','monthly',true,false),
       (24,30,'spanish_one_yearly','yearly',true,false);

-- EUROPE Plans
INSERT INTO plans_country(planId, countryId, price)
select 3, id, 4.99
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 4, id, 34.90
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 9, id, 4.99
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 10, id, 34.90
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 15, id, 4.99
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 16, id, 34.90
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');


-- default plans
INSERT INTO plans_country(planId, countryId, price)
select 1, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 2, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA','CH');

INSERT INTO plans_country(planId, countryId, price)
select 7, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA','CH');

INSERT INTO plans_country(planId, countryId, price)
select 8, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA','CH');

INSERT INTO plans_country(planId, countryId, price)
select 13, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA','CH');

INSERT INTO plans_country(planId, countryId, price)
select 14, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA','CH');


-- Swiss plan
INSERT INTO plans_country(planId, countryId, price)
select 5, id, 6.90
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 6, id, 49.90
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 11, id, 6.90
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 12, id, 49.90
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 17, id, 6.90
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 18, id, 49.90
from countries c
where short = 'CH';

-- Spanish plan
INSERT INTO plans_country(planId, countryId, price)
select 19, id, 6.90
from countries c
where short = 'ES';

INSERT INTO plans_country(planId, countryId, price)
select 20, id, 49.90
from countries c
where short = 'ES';

INSERT INTO plans_country(planId, countryId, price)
select 21, id, 6.90
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 22, id, 49.90
from countries c
where short = 'ES';

INSERT INTO plans_country(planId, countryId, price)
select 23, id, 6.90
from countries c
where short = 'ES';

INSERT INTO plans_country(planId, countryId, price)
select 24, id, 49.90
from countries c
where short = 'ES';

UPDATE plans_country
set plans_country.currency='EUR',plans_country.currencySymbol='€'
where planId in(3,4,9,10,15,16,19,20,21,22,23,24);

UPDATE plans_country
set plans_country.currency='CHF',plans_country.currencySymbol='CHF'
where planId in(5,6,11,12,17,18);
