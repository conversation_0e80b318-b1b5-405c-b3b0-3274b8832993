DELETE FROM plans_addon;
DELETE FROM addOns;
INSERT INTO addOns(id, addOnName, price)
values (10, 'default_two', 3),
       (20, 'default_three', 5),
       (30, 'default_two_yearly', 21),
       (40, 'default_three_yearly', 36),
       (50, 'europe_two', 3),
       (60, 'europe_three', 5),
       (70, 'europe_two_yearly', 21),
       (80, 'europe_three_yearly', 36),
       (90, 'swiss_two', 4),
       (100, 'swiss_three', 7),
       (110, 'swiss_two_yearly', 30),
       (120, 'swiss_three_yearly', 50),
       (130, 'spanish_two', 4),
       (140, 'spanish_three', 7),
       (150, 'spanish_two_yearly', 30),
       (160, 'spanish_three_yearly', 50);


INSERT INTO plans_addon(planId, addOnId, active)
select id, 10, true
from plans
where planName = 'default_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 20, true
from plans
where planName = 'default_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 30, true
from plans
where planName = 'default_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 40, true
from plans
where planName = 'default_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 50, true
from plans
where planName = 'europe_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 60, true
from plans
where planName = 'europe_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 70, true
from plans
where planName = 'europe_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 80, true
from plans
where planName = 'europe_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 90, true
from plans
where planName = 'swiss_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 100, true
from plans
where planName = 'swiss_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 110, true
from plans
where planName = 'swiss_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 120, true
from plans
where planName = 'swiss_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 130, true
from plans
where planName = 'spanish_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 140, true
from plans
where planName = 'spanish_one';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 150, true
from plans
where planName = 'spanish_one_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id, 160, true
from plans
where planName = 'spanish_one_yearly';



