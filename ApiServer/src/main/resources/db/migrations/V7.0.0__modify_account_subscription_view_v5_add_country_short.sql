-- Modify kidjo.account_subscription_view_V5

CREATE OR REPLACE VIEW `kidjo`.`account_subscription_view_V5` AS
select
    coalesce(`s`.`id`, `u`.`id`) AS `id_account`,
    ifnull(nullif(`u`.`email`, ''), NULL) AS `email`,
    ifnull(nullif(`u`.`id`, 0), NULL) AS `id_user`,
    `s`.`id` AS `id_sub`,
    ifnull(nullif(`u`.`created_at`, 0), NULL) AS `registered_date`,
    ifnull(nullif(`u`.`name`, ''), NULL) AS `name`,
    coalesce(`uc1`.`name`, `uc`.`name`, `c2`.`name`) AS `country`,
    coalesce(`uc1`.`short`, `uc`.`short`, `c2`.`short`) AS `country_code`,
    coalesce(`uc1`.`id`, `uc`.`id`, `c2`.`id`) AS `country_id`,
    `s`.`storeId` AS `storeId`,
    `s`.`subscriptionType` AS `subscriptionType`,
    (case
         when ((`s`.`id` is not null)
             and (`s`.`nextBillDate` >= curdate())
             and (`s`.`isActive` = 1)) then 1
         when ((`s`.`id` is not null)
             and ((`s`.`nextBillDate` < curdate())
                 or (`s`.`isActive` = 0))) then 0
         else NULL
        end) AS `subStatus`,
    `s`.`iapId` AS `iapId`,
    `s`.`nextBillDate` AS `nextBillDate`,
    `s`.`created_at` AS `createdDate`,
    `s`.`updated_at` AS `subUpdatedDate`,
    `u`.`updated_at` AS `userUpdatedDate`,
    ifnull(nullif(`s`.`accountCouponId`, 0), NULL) AS `id_coupon`,
    `s`.`isTest` AS `is_test`,
    `a`.`couponId` AS `couponId`,
    `a`.`startDate` AS `couponStartDate`,
    (case
         when ((`a`.`expireDate` >= curdate())
             and (`a`.`redeemedTimes` = 0)) then 'PENDING_ACTIVATION'
         when ((`a`.`expireDate` >= curdate())
             and (`a`.`redeemedTimes` > 0)) then 'ACTIVE'
         when ((`a`.`expireDate` < curdate())
             and (`a`.`redeemedTimes` > 0)) then 'EXPIRED'
         when ((`a`.`expireDate` < curdate())
             and (`a`.`redeemedTimes` = 0)) then 'INVALID'
        end) AS `couponStatus`,
    `at`.`id` AS `coupon_type_id`,
    `at`.`name` AS `type`,
    `at`.`description` AS `coupon_type_description`,
    `ap`.`name` AS `partner`,
    `ap`.`id` AS `id_partner`,
    `ap`.`description` AS `partner_description`,
    `uc2`.`is_tc_accepted` AS `is_tc_accepted`,
    `uc2`.`is_promo_accepted` AS `is_promo_accepted`
from
    (((((((((`kidjo`.`subscriptions_root` `s`
        left join (`kidjo`.`users` `u`
            left join `kidjo`.`countries` `uc` on
            ((`uc`.`id` = `u`.`country_id`))) on
        ((`u`.`id` = `s`.`userId`)))
        left join `kidjo`.`user_partner_subscription` `ups` on
        (((`ups`.`subscription_id` = `s`.`id`)
            and (`s`.`storeId` = 'twt'))))
        left join `kidjo`.`countries` `uc1` on
        ((`uc1`.`short` = `ups`.`country`)))
        left join `kidjo`.`account_coupons` `a` on
        ((`a`.`id` = `s`.`accountCouponId`)))
        left join `kidjo`.`account_coupon_partner` `ap` on
        ((`ap`.`id` = `a`.`id_partner`)))
        left join `kidjo`.`account_coupon_type` `at` on
        ((`at`.`id` = `a`.`id_type`)))
        left join `kidjo`.`users_consent` `uc2` on
        ((`uc2`.`user_id` = `u`.`id`)))
        left join `kidjo`.`mondia_packages` `mp` on
        (((`mp`.`mondia_product_id` = right(`s`.`subscriptionToken`,
            4))
            and (`s`.`storeId` = 'mondia'))))
        left join `kidjo`.`countries` `c2` on
        ((`c2`.`id` = `mp`.`country_id`)))
group by
    `s`.`id`,
    `u`.`id`
order by
    `s`.`created_at` desc;