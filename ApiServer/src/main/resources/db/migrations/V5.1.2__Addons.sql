DROP TABLE addons;

DROP TABLE plans_addon;


create table addOns
(
    id        int           not null auto_increment primary key,
    addOnName varchar(256)  not null,
    price     decimal(4, 2) not null,
    active    boolean default false
);


create table plans_addon
(
    id      int not null auto_increment primary key,
    planId  int not null,
    addOnId int not null,
    active  boolean default false
);

INSERT INTO addOns(id, addOnName, price)
values (10, 'default_one_product_2', 3),
       (20, 'default_one_product_3', 5),
       (30, 'default_one_product_yearly_2', 21),
       (40, 'default_one_product_yearly_3', 36),
       (50, 'europe_one_product_2', 3.60),
       (60, 'europe_one_product_3', 6),
       (70, 'europe_one_product_yearly_2', 25),
       (80, 'europe_one_product_yearly_3', 42.90),
       (90, 'swiss_one_product_2', 4.20),
       (100, 'swiss_one_product_3', 7),
       (110, 'swiss_one_product_yearly_2', 32.20),
       (120, 'swiss_one_product_yearly_3', 55.20);

INSERT INTO plans_addon(planId, addOnId, active)
select id,10,true from plans where planName='default_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,20,true from plans where planName='default_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,30,true from plans where planName='default_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,40,true from plans where planName='default_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,50,true from plans where planName='europe_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,60,true from plans where planName='europe_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,70,true from plans where planName='europe_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,80,true from plans where planName='europe_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,90,true from plans where planName='swiss_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,100,true from plans where planName='swiss_one_product';

INSERT INTO plans_addon(planId, addOnId, active)
select id,110,true from plans where planName='swiss_one_product_yearly';

INSERT INTO plans_addon(planId, addOnId, active)
select id,120,true from plans where planName='swiss_one_product_yearly';
