CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW `kidjo`.`account_subscription_view_V3` AS
SELECT
    (CASE
         WHEN (((`u`.`id` = 0) OR ISNULL(`u`.`id`)) AND (`s`.`id` IS NOT NULL)) THEN `s`.`id`
         WHEN (ISNULL(`s`.`id`) AND (`u`.`id` <> 0) AND (`u`.`id` IS NOT NULL)) THEN `u`.`id`
         ELSE `s`.`id`
        END) AS `id_account`,
    IF(((`u`.`id` = 0) OR (`u`.`email` = '')), NULL, `u`.`email`) AS `email`,
    IF((`u`.`id` = 0), NULL, `u`.`id`) AS `id_user`,
    `s`.`id` AS `id_sub`,
    IF((`u`.`id` = 0), NULL, `u`.`created_at`) AS `registered_date`,
    IF(((`u`.`id` = 0) OR (`u`.`name` = '')), NULL, `u`.`name`) AS `name`,

    -- Modify the country field to check for storeId and fetch from `user_partner_subscription`
    (CASE
         WHEN `s`.`storeId` = 'twt' THEN
             (SELECT `ups`.`country` FROM `user_partner_subscription` `ups`
              WHERE `ups`.`subscription_id` = `s`.`id` LIMIT 1)
    ELSE `uc`.`name`
END) AS `country`,
    `s`.`storeId`,
    `s`.`subscriptionType` AS `subscriptionType`,
    (CASE
        WHEN ((`s`.`id` IS NOT NULL) AND (CAST(`s`.`nextBillDate` AS DATE) >= CURDATE()) AND `s`.`isActive`) THEN 1
        WHEN (((`s`.`id` IS NOT NULL) AND (CAST(`s`.`nextBillDate` AS DATE) < CURDATE())) OR (NOT(`s`.`isActive`))) THEN 0
        ELSE NULL
    END) AS `subStatus`,
    `s`.`iapId` AS `iapId`,
    `s`.`nextBillDate` AS `nextBillDate`,
    `s`.`created_at` AS `createdDate`,
    IF((`s`.`accountCouponId` = 0), NULL, `s`.`accountCouponId`) AS `id_coupon`,
    `s`.`isTest` AS `is_test`,
    `a`.`couponId` AS `couponId`,
    `a`.`startDate` AS `couponStartDate`,
    (CASE
        WHEN ((CAST(`a`.`expireDate` AS DATE) >= CURDATE()) AND (CAST(`a`.`redeemedTimes` AS SIGNED) = 0)) THEN 'PENDING_ACTIVATION'
        WHEN ((CAST(`a`.`expireDate` AS DATE) >= CURDATE()) AND (CAST(`a`.`redeemedTimes` AS SIGNED) > 0)) THEN 'ACTIVE'
        WHEN ((CAST(`a`.`expireDate` AS DATE) < CURDATE()) AND (CAST(`a`.`redeemedTimes` AS SIGNED) > 0)) THEN 'EXPIRED'
        WHEN ((CAST(`a`.`expireDate` AS DATE) < CURDATE()) AND (CAST(`a`.`redeemedTimes` AS SIGNED) = 0)) THEN 'INVALID'
    END) AS `couponStatus`,
    `at`.`name` AS `type`,
    `ap`.`name` AS `partner`,
    `ap`.`id` AS `id_partner`,
    `uc2`.`is_tc_accepted` AS `is_tc_accepted`,
    `uc2`.`is_promo_accepted` AS `is_promo_accepted`
FROM
     `kidjo`.`users` `u`
LEFT JOIN `kidjo`.`countries` `uc` ON `uc`.`id` = `u`.`country_id`
LEFT JOIN `kidjo`.`subscriptions_root` `s` ON `u`.`id` = `s`.`userId`
LEFT JOIN `kidjo`.`account_coupons` `a` ON `a`.`id` = `s`.`accountCouponId`
LEFT JOIN `kidjo`.`account_coupon_partner` `ap` ON `ap`.`id` = `a`.`id_partner`
LEFT JOIN `kidjo`.`account_coupon_type` `at` ON `at`.`id` = `a`.`id_type`
LEFT JOIN `kidjo`.`users_consent` `uc2` ON `uc2`.`user_id` = `u`.`id` ;