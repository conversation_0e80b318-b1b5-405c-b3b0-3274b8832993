CREATE OR <PERSON><PERSON>LACE VIEW `kidjo`.`account_subscription_view_V5` AS
SELECT
    COALESCE(s.id, u.id) AS id_account,
    IFNULL(NULLIF(u.email, ''), NULL) AS email,
    IFNULL(NULLIF(u.id, 0), NULL) AS id_user,
    s.id AS id_sub,
    IFNULL(NULLIF(u.created_at, 0), NULL) AS registered_date,
    IFNULL(NULLIF(u.name, ''), NULL) AS name,
    COALESCE(ups.country, uc.name, c2.name) AS country, -- Now getting country name
    s.storeId,
    s.subscriptionType,
    CASE
        WHEN s.id IS NOT NULL AND s.nextBillDate >= CURDATE() AND s.isActive = 1 THEN 1
        WHEN s.id IS NOT NULL AND (s.nextBillDate < CURDATE() OR s.isActive = 0) THEN 0
        ELSE NULL
        END AS subStatus,
    s.iapId,
    s.nextBillDate,
    s.created_at AS createdDate,
    IFNULL(NULLIF(s.accountCouponId, 0), NULL) AS id_coupon,
    s.isTest AS is_test,
    a.couponId,
    a.startDate AS couponStartDate,
    CASE
        WHEN a.expireDate >= CURDATE() AND a.redeemedTimes = 0 THEN 'PENDING_ACTIVATION'
        WHEN a.expireDate >= CURDATE() AND a.redeemedTimes > 0 THEN 'ACTIVE'
        WHEN a.expireDate < CURDATE() AND a.redeemedTimes > 0 THEN 'EXPIRED'
        WHEN a.expireDate < CURDATE() AND a.redeemedTimes = 0 THEN 'INVALID'
        END AS couponStatus,
    at.name AS type,
    ap.name AS partner,
    ap.id AS id_partner,
    uc2.is_tc_accepted,
    uc2.is_promo_accepted
FROM `kidjo`.`users` `u`
         LEFT JOIN `kidjo`.`countries` `uc` ON `uc`.`id` = `u`.`country_id`
         LEFT JOIN `kidjo`.`subscriptions_root` `s`
                   ON `u`.`id` = `s`.`userId`   -- Moved filter to JOIN condition
         LEFT JOIN `kidjo`.`user_partner_subscription` `ups`
                   ON `ups`.`subscription_id` = `s`.`id` AND s.storeId = 'twt'
         LEFT JOIN `kidjo`.`account_coupons` `a` ON `a`.`id` = `s`.`accountCouponId`
         LEFT JOIN `kidjo`.`account_coupon_partner` `ap` ON `ap`.`id` = `a`.`id_partner`
         LEFT JOIN `kidjo`.`account_coupon_type` `at` ON `at`.`id` = `a`.`id_type`
         LEFT JOIN `kidjo`.`users_consent` `uc2` ON `uc2`.`user_id` = `u`.`id`
         LEFT JOIN `kidjo`.`mondia_packages` `mp`
                   ON mp.mondia_product_id = RIGHT(s.subscriptionToken, 4) AND s.storeId = 'mondia'  -- Optimized condition
    LEFT JOIN `kidjo`.`countries` `c2`
ON c2.id = mp.country_id