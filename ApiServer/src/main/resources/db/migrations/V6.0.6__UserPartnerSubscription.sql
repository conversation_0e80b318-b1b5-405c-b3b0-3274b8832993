CREATE TABLE `user_partner_subscription`
(
    `id`              int(6) NOT NULL auto_increment,
    `msidn`           int(50) NOT NULL DEFAULT 0,
    `subscription_id` bigint(20) NOT NULL,
    `active`          tinyint(1) NOT NULL DEFAULT '1',
    `created_at`      timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`      timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
)