DELETE FROM discounts;
INSERT INTO discounts (id, discountName, active) VALUES(10, 'default_1_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(20, 'default_2_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(30, 'default_3_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(40, 'europe_1_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(50, 'europe_2_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(60, 'europe_3_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(70, 'swiss_1_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(80, 'swiss_2_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(90, 'swiss_3_1m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(100, 'default_1_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(110, 'default_2_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(120, 'default_3_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(130, 'europe_1_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(140, 'europe_2_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(150, 'europe_3_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(160, 'swiss_1_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(170, 'swiss_2_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(180, 'swiss_3_2m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(190, 'default_1_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(200, 'default_2_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(210, 'default_3_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(220, 'europe_1_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(230, 'europe_2_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(240, 'europe_3_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(250, 'swiss_1_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(260, 'swiss_2_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(270, 'swiss_3_3m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(280, 'default_1_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(290, 'default_2_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(300, 'default_3_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(310, 'europe_1_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(320, 'europe_2_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(330, 'europe_3_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(340, 'swiss_1_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(350, 'swiss_2_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(360, 'swiss_3_4m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(370, 'default_1_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(380, 'default_2_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(390, 'default_3_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(400, 'europe_1_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(410, 'europe_2_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(420, 'europe_3_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(430, 'swiss_1_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(440, 'swiss_2_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(450, 'swiss_3_5m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(460, 'default_1_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(470, 'default_2_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(480, 'default_3_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(490, 'europe_1_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(500, 'europe_2_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(510, 'europe_3_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(520, 'swiss_1_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(530, 'swiss_2_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(540, 'swiss_3_6m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(550, 'default_1_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(560, 'default_2_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(570, 'default_3_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(580, 'europe_1_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(590, 'europe_2_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(600, 'europe_3_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(610, 'swiss_1_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(620, 'swiss_2_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(630, 'swiss_3_7m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(640, 'default_1_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(650, 'default_2_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(660, 'default_3_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(670, 'europe_1_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(680, 'europe_2_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(690, 'europe_3_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(700, 'swiss_1_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(710, 'swiss_2_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(720, 'swiss_3_8m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(730, 'default_1_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(740, 'default_2_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(750, 'default_3_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(760, 'europe_1_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(770, 'europe_2_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(780, 'europe_3_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(790, 'swiss_1_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(800, 'swiss_2_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(810, 'swiss_3_9m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(820, 'default_1_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(830, 'default_2_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(840, 'default_3_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(850, 'europe_1_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(860, 'europe_2_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(870, 'europe_3_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(880, 'swiss_1_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(890, 'swiss_2_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(900, 'swiss_3_10m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(910, 'default_1_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(920, 'default_2_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(930, 'default_3_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(940, 'europe_1_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(950, 'europe_2_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(960, 'europe_3_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(970, 'swiss_1_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(980, 'swiss_2_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(990, 'swiss_3_11m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1000, 'default_1_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1010, 'default_2_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1020, 'default_3_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1030, 'europe_1_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1040, 'europe_2_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1050, 'europe_3_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1060, 'swiss_1_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1070, 'swiss_2_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1080, 'swiss_3_12m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1090, 'default_1_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1100, 'default_2_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1110, 'default_3_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1120, 'europe_1_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1130, 'europe_2_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1140, 'europe_3_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1150, 'swiss_1_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1160, 'swiss_2_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1170, 'swiss_3_18m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1180, 'default_1_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1190, 'default_2_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1200, 'default_3_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1210, 'europe_1_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1220, 'europe_2_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1230, 'europe_3_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1240, 'swiss_1_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1250, 'swiss_2_24m', 1);
INSERT INTO discounts (id, discountName, active) VALUES(1260, 'swiss_3_24m', 1);

ALTER TABLE discounts ADD freeTrial BOOL DEFAULT false NOT NULL;
ALTER TABLE discounts ADD isPriceDiscount BOOL DEFAULT false NOT NULL;

Update discounts set freeTrial=true;


ALTER TABLE discounts_plans
    add column addonId int not null;
