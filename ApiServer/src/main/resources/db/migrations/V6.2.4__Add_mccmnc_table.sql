CREATE TABLE `mccmnc` (
                          `id` int(11) DEFAULT NULL,
                          `mcc` varchar(255) DEFAULT NULL,
                          `mnc` varchar(255) DEFAULT NULL,
                          `iso` varchar(50) DEFAULT NULL,
                          `country` varchar(50) DEFAULT NULL,
                          `country_code` int(11) DEFAULT NULL,
                          `network` varchar(50) DEFAULT NULL
);

    INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                                (1,'289','88','ge','Abkhazia',7,'A-Mobile'),
                                                                                (2,'289','68','ge','Abkhazia',7,'A-Mobile'),
                                                                                (3,'289','67','ge','Abkhazia',7,'Aquafon'),
                                                                                (4,'289','299','ge','Abkhazia',7,'Failed Calls'),
                                                                                (5,'289','999','ge','Abkhazia',7,'Fix Line'),
                                                                                (6,'412','01','af','Afghanistan',93,'AWCC'),
                                                                                (7,'412','50','af','Afghanistan',93,'Etisalat'),
                                                                                (8,'412','299','af','Afghanistan',93,'Failed Calls'),
                                                                                (9,'412','999','af','Afghanistan',93,'Fix Line'),
                                                                                (10,'412','88','af','Afghanistan',93,'Mobifone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (11,'412','80','af','Afghanistan',93,'Mobifone'),
                                                                           (12,'412','40','af','Afghanistan',93,'MTN'),
                                                                           (13,'412','20','af','Afghanistan',93,'Roshan'),
                                                                           (14,'276','03','al','Albania',355,'ALBtelecom Mobile / Eagle'),
                                                                           (15,'276','299','al','Albania',355,'Failed Calls'),
                                                                           (16,'276','999','al','Albania',355,'Fix Line'),
                                                                           (17,'276','01','al','Albania',355,'One / AMC'),
                                                                           (18,'276','02','al','Albania',355,'Vodafone'),
                                                                           (19,'603','02','dz','Algeria',213,'Djezzy'),
                                                                           (20,'603','299','dz','Algeria',213,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (21,'603','999','dz','Algeria',213,'Fix Line'),
                                                                           (22,'603','01','dz','Algeria',213,'Mobilis'),
                                                                           (23,'603','03','dz','Algeria',213,'Ooredoo'),
                                                                           (24,'544','780','as','American Samoa',684,'ASTCA Mobile'),
                                                                           (25,'544','11','as','American Samoa',684,'BlueSky'),
                                                                           (26,'544','299','as','American Samoa',684,'Failed Calls'),
                                                                           (27,'544','999','as','American Samoa',684,'Fix Line'),
                                                                           (28,'213','03','ad','Andorra',376,'Andorra Telecom / Mobiland'),
                                                                           (29,'213','299','ad','Andorra',376,'Failed Calls'),
                                                                           (30,'213','999','ad','Andorra',376,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (31,'631','299','ao','Angola',244,'Failed Calls'),
                                                                           (32,'631','999','ao','Angola',244,'Fix Line'),
                                                                           (33,'631','04','ao','Angola',244,'MoviCel'),
                                                                           (34,'631','02','ao','Angola',244,'Unitel'),
                                                                           (35,'365','850','ai','Anguilla',1264,'Digicel'),
                                                                           (36,'365','299','ai','Anguilla',1264,'Failed Calls'),
                                                                           (37,'365','999','ai','Anguilla',1264,'Fix Line'),
                                                                           (38,'365','840','ai','Anguilla',1264,'Flow'),
                                                                           (39,'344','930','ag','Antigua and Barbuda',1268,'Digicel'),
                                                                           (40,'344','93','ag','Antigua and Barbuda',1268,'Digicel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (41,'344','299','ag','Antigua and Barbuda',1268,'Failed Calls'),
                                                                           (42,'344','999','ag','Antigua and Barbuda',1268,'Fix Line'),
                                                                           (43,'344','92','ag','Antigua and Barbuda',1268,'Flow'),
                                                                           (44,'344','920','ag','Antigua and Barbuda',1268,'Flow'),
                                                                           (45,'344','030','ag','Antigua and Barbuda',1268,'imobile / APUA'),
                                                                           (46,'344','03','ag','Antigua and Barbuda',1268,'imobile / APUA'),
                                                                           (47,'722','031','ar','Argentina',54,'Claro'),
                                                                           (48,'722','320','ar','Argentina',54,'Claro'),
                                                                           (49,'722','310','ar','Argentina',54,'Claro'),
                                                                           (50,'722','330','ar','Argentina',54,'Claro');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (51,'722','299','ar','Argentina',54,'Express'),
                                                                           (52,'722','999','ar','Argentina',54,'Fix Line'),
                                                                           (53,'722','299','ar','Argentina',54,'Imowi'),
                                                                           (54,'722','299','ar','Argentina',54,'IPLAN'),
                                                                           (55,'722','299','ar','Argentina',54,'Kallofer'),
                                                                           (56,'722','070','ar','Argentina',54,'Movistar'),
                                                                           (57,'722','007','ar','Argentina',54,'Movistar'),
                                                                           (58,'722','010','ar','Argentina',54,'Movistar'),
                                                                           (59,'722','299','ar','Argentina',54,'Nuestro'),
                                                                           (60,'722','034','ar','Argentina',54,'Personal');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (61,'722','299','ar','Argentina',54,'Telecentro'),
                                                                           (62,'722','340','ar','Argentina',54,'Personal'),
                                                                           (63,'722','341','ar','Argentina',54,'Personal'),
                                                                           (64,'283','01','am','Armenia',374,'Beeline'),
                                                                           (65,'283','299','am','Armenia',374,'Failed Calls'),
                                                                           (66,'283','999','am','Armenia',374,'Fix Line'),
                                                                           (67,'283','04','am','Armenia',374,'KT'),
                                                                           (68,'283','10','am','Armenia',374,'Orange'),
                                                                           (69,'283','05','am','Armenia',374,'Viva-MTS'),
                                                                           (70,'363','02','aw','Aruba',297,'Digicel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (71,'363','299','aw','Aruba',297,'Failed Calls'),
                                                                           (72,'363','999','aw','Aruba',297,'Fix Line'),
                                                                           (73,'363','299','aw','Aruba',297,'MIO'),
                                                                           (74,'363','01','aw','Aruba',297,'SETAR'),
                                                                           (75,'505','299','au','Australia',61,'ACMA'),
                                                                           (76,'505','30','au','Australia',61,'Compatel'),
                                                                           (77,'505','999','au','Australia',61,'Fix Line'),
                                                                           (78,'505','299','au','Australia',61,'Get SIM'),
                                                                           (79,'505','19','au','Australia',61,'Lycamobile'),
                                                                           (80,'505','35','au','Australia',61,'MessageBird');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (81,'505','10','au','Australia',61,'Norfolk Telecom'),
                                                                           (82,'505','90','au','Australia',61,'Optus'),
                                                                           (83,'505','88','au','Australia',61,'Pivotel'),
                                                                           (84,'505','50','au','Australia',61,'Pivotel'),
                                                                           (85,'505','13','au','Australia',61,'RailCorp'),
                                                                           (86,'505','26','au','Australia',61,'Sinch'),
                                                                           (87,'505','02','au','Australia',61,'Optus'),
                                                                           (88,'505','299','au','Australia',61,'Symbio'),
                                                                           (89,'505','11','au','Australia',61,'Telstra'),
                                                                           (90,'505','72','au','Australia',61,'Telstra');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (91,'505','39','au','Australia',61,'Telstra'),
                                                                           (92,'505','71','au','Australia',61,'Telstra'),
                                                                           (93,'505','01','au','Australia',61,'Telstra'),
                                                                           (94,'505','16','au','Australia',61,'VicTrack'),
                                                                           (95,'505','06','au','Australia',61,'Vodafone'),
                                                                           (96,'505','12','au','Australia',61,'Vodafone'),
                                                                           (97,'505','07','au','Australia',61,'Vodafone'),
                                                                           (98,'505','03','au','Australia',61,'Vodafone'),
                                                                           (99,'232','19','at','Austria',43,'3'),
                                                                           (100,'232','05','at','Austria',43,'3');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (101,'232','16','at','Austria',43,'3'),
                                                                           (102,'232','10','at','Austria',43,'3'),
                                                                           (103,'232','14','at','Austria',43,'3'),
                                                                           (104,'232','11','at','Austria',43,'A1 Telekom'),
                                                                           (105,'232','02','at','Austria',43,'A1 Telekom'),
                                                                           (106,'232','09','at','Austria',43,'A1 Telekom'),
                                                                           (107,'232','01','at','Austria',43,'A1 Telekom'),
                                                                           (108,'232','12','at','Austria',43,'A1 Telekom'),
                                                                           (109,'232','299','at','Austria',43,'ArgoNET'),
                                                                           (110,'232','299','at','Austria',43,'DIALOG telekom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (111,'232','299','at','Austria',43,'Digital Privacy'),
                                                                           (112,'232','299','at','Austria',43,'DIMOCO'),
                                                                           (113,'232','299','at','Austria',43,'educom'),
                                                                           (114,'232','999','at','Austria',43,'Fix Line'),
                                                                           (115,'232','25','at','Austria',43,'Holding Graz'),
                                                                           (116,'232','299','at','Austria',43,'Innsbrucker Kommunalbetriebe'),
                                                                           (117,'232','299','at','Austria',43,'kabelplus'),
                                                                           (118,'232','299','at','Austria',43,'Lenovo Connect'),
                                                                           (119,'232','299','at','Austria',43,'LINK Mobility'),
                                                                           (120,'232','26','at','Austria',43,'LIWEST Mobil');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (121,'232','17','at','Austria',43,'MRS'),
                                                                           (122,'232','20','at','Austria',43,'Mtel'),
                                                                           (123,'232','91','at','Austria',43,'OBB Infrastruktur'),
                                                                           (124,'232','22','at','Austria',43,'Plintron'),
                                                                           (125,'232','299','at','Austria',43,'Simple SMS'),
                                                                           (126,'232','299','at','Austria',43,'Skymond Mobile'),
                                                                           (127,'232','24','at','Austria',43,'Smartel Services'),
                                                                           (128,'232','18','at','Austria',43,'smartspace'),
                                                                           (129,'232','04','at','Austria',43,'T-Mobile / Magenta'),
                                                                           (130,'232','03','at','Austria',43,'T-Mobile / Magenta');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (131,'232','23','at','Austria',43,'T-Mobile / Magenta'),
                                                                           (132,'232','13','at','Austria',43,'T-Mobile / Magenta'),
                                                                           (133,'232','299','at','Austria',43,'Telfoni'),
                                                                           (134,'232','27','at','Austria',43,'Tismi'),
                                                                           (135,'232','07','at','Austria',43,'Ventocom'),
                                                                           (136,'400','01','az','Azerbaijan',994,'Azercell'),
                                                                           (137,'400','02','az','Azerbaijan',994,'Bakcell'),
                                                                           (138,'400','299','az','Azerbaijan',994,'Failed Calls'),
                                                                           (139,'400','999','az','Azerbaijan',994,'Fix Line'),
                                                                           (140,'400','03','az','Azerbaijan',994,'FONEX');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (141,'400','04','az','Azerbaijan',994,'Nar Mobile'),
                                                                           (142,'400','06','az','Azerbaijan',994,'Naxtel'),
                                                                           (143,'364','490','bs','Bahamas',1242,'Aliv'),
                                                                           (144,'364','390','bs','Bahamas',1242,'Cybercell / BaTelCo'),
                                                                           (145,'364','30','bs','Bahamas',1242,'Cybercell / BaTelCo'),
                                                                           (146,'364','39','bs','Bahamas',1242,'Cybercell / BaTelCo'),
                                                                           (147,'364','299','bs','Bahamas',1242,'Failed Calls'),
                                                                           (148,'364','999','bs','Bahamas',1242,'Fix Line'),
                                                                           (149,'426','01','bh','Bahrain',973,'Batelco'),
                                                                           (150,'426','299','bh','Bahrain',973,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (151,'426','999','bh','Bahrain',973,'Fix Line'),
                                                                           (152,'426','05','bh','Bahrain',973,'Royal Court'),
                                                                           (153,'426','04','bh','Bahrain',973,'VIVA'),
                                                                           (154,'426','02','bh','Bahrain',973,'Zain'),
                                                                           (155,'470','02','bd','Bangladesh',880,'Airtel'),
                                                                           (156,'470','07','bd','Bangladesh',880,'Airtel'),
                                                                           (157,'470','03','bd','Bangladesh',880,'Banglalink'),
                                                                           (158,'470','299','bd','Bangladesh',880,'Failed Calls'),
                                                                           (159,'470','999','bd','Bangladesh',880,'Fix Line'),
                                                                           (160,'470','01','bd','Bangladesh',880,'GrameenPhone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (161,'470','04','bd','Bangladesh',880,'TeleTalk'),
                                                                           (162,'342','050','bb','Barbados',1246,'Digicel'),
                                                                           (163,'342','299','bb','Barbados',1246,'Failed Calls'),
                                                                           (164,'342','999','bb','Barbados',1246,'Fix Line'),
                                                                           (165,'342','600','bb','Barbados',1246,'Flow / Lime'),
                                                                           (166,'342','299','bb','Barbados',1246,'Ozone'),
                                                                           (167,'257','04','by','Belarus',375,'life:)'),
                                                                           (168,'257','299','by','Belarus',375,'Failed Calls'),
                                                                           (169,'257','999','by','Belarus',375,'Fix Line'),
                                                                           (170,'257','02','by','Belarus',375,'MTS');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (171,'257','01','by','Belarus',375,'velcom A1'),
                                                                           (172,'206','20','be','Belgium',32,'Base'),
                                                                           (173,'206','28','be','Belgium',32,'BICS'),
                                                                           (174,'206','25','be','Belgium',32,'Dense Air'),
                                                                           (175,'206','23','be','Belgium',32,'Dust Mobile'),
                                                                           (176,'206','33','be','Belgium',32,'Ericsson'),
                                                                           (177,'206','299','be','Belgium',32,'FEBO'),
                                                                           (178,'206','999','be','Belgium',32,'Fix Line'),
                                                                           (179,'206','299','be','Belgium',32,'GianCom'),
                                                                           (180,'206','02','be','Belgium',32,'Infrabel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (181,'206','299','be','Belgium',32,'interactive digital media / IDM'),
                                                                           (182,'206','299','be','Belgium',32,'L-mobi'),
                                                                           (183,'206','99','be','Belgium',32,'Lancelot'),
                                                                           (184,'206','299','be','Belgium',32,'Legos'),
                                                                           (185,'206','06','be','Belgium',32,'Lycamobile'),
                                                                           (186,'206','30','be','Belgium',32,'Mobile Vikings'),
                                                                           (187,'206','10','be','Belgium',32,'Mobistar / Orange'),
                                                                           (188,'206','299','be','Belgium',32,'Nord Connect'),
                                                                           (189,'206','34','be','Belgium',32,'onoff'),
                                                                           (190,'206','299','be','Belgium',32,'PM Factory');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (191,'206','00','be','Belgium',32,'Proximus'),
                                                                           (192,'206','01','be','Belgium',32,'Proximus'),
                                                                           (193,'206','04','be','Belgium',32,'Proximus'),
                                                                           (194,'206','05','be','Belgium',32,'Telenet'),
                                                                           (195,'206','07','be','Belgium',32,'Vectone Mobile'),
                                                                           (196,'206','08','be','Belgium',32,'VOOmobile'),
                                                                           (197,'206','299','be','Belgium',32,'Voxbone / Bandwidth'),
                                                                           (198,'702','67','bz','Belize',501,'DigiCell'),
                                                                           (199,'702','299','bz','Belize',501,'Failed Calls'),
                                                                           (200,'702','999','bz','Belize',501,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (201,'702','69','bz','Belize',501,'Smart'),
                                                                           (202,'702','099','bz','Belize',501,'Smart'),
                                                                           (203,'616','299','bj','Benin',229,'Failed Calls'),
                                                                           (204,'616','999','bj','Benin',229,'Fix Line'),
                                                                           (205,'616','02','bj','Benin',229,'Moov'),
                                                                           (206,'616','03','bj','Benin',229,'MTN'),
                                                                           (207,'350','01','bm','Bermuda',1441,'Digicel'),
                                                                           (208,'350','299','bm','Bermuda',1441,'Failed Calls'),
                                                                           (209,'350','999','bm','Bermuda',1441,'Fix Line'),
                                                                           (210,'350','00','bm','Bermuda',1441,'One');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (211,'402','17','bt','Bhutan',975,'B-Mobile'),
                                                                           (212,'402','11','bt','Bhutan',975,'B-Mobile'),
                                                                           (213,'402','999','bt','Bhutan',975,'Fix Line'),
                                                                           (214,'402','77','bt','Bhutan',975,'TashiCell'),
                                                                           (215,'736','02','bo','Bolivia',591,'Entel Movil'),
                                                                           (216,'736','299','bo','Bolivia',591,'Failed Calls'),
                                                                           (217,'736','999','bo','Bolivia',591,'Fix Line'),
                                                                           (218,'736','03','bo','Bolivia',591,'Tigo'),
                                                                           (219,'736','01','bo','Bolivia',591,'Viva'),
                                                                           (220,'218','90','ba','Bosnia and Herzegovina',387,'BH Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (221,'218','03','ba','Bosnia and Herzegovina',387,'Eronet'),
                                                                           (222,'218','299','ba','Bosnia and Herzegovina',387,'Failed Calls'),
                                                                           (223,'218','999','ba','Bosnia and Herzegovina',387,'Fix Line'),
                                                                           (224,'218','05','ba','Bosnia and Herzegovina',387,'m:tel'),
                                                                           (225,'652','04','bw','Botswana',267,'beMobile'),
                                                                           (226,'652','299','bw','Botswana',267,'Failed Calls'),
                                                                           (227,'652','999','bw','Botswana',267,'Fix Line'),
                                                                           (228,'652','01','bw','Botswana',267,'Mascom'),
                                                                           (229,'652','02','bw','Botswana',267,'Orange'),
                                                                           (230,'724','33','br','Brazil',55,'Algar Telecom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (231,'724','34','br','Brazil',55,'Algar Telecom'),
                                                                           (232,'724','32','br','Brazil',55,'Algar Telecom'),
                                                                           (233,'724','26','br','Brazil',55,'Americanet'),
                                                                           (234,'724','299','br','Brazil',55,'ARQIA'),
                                                                           (235,'724','299','br','Brazil',55,'Bbs Options'),
                                                                           (236,'724','299','br','Brazil',55,'Cinco'),
                                                                           (237,'724','38','br','Brazil',55,'Claro'),
                                                                           (238,'724','05','br','Brazil',55,'Claro'),
                                                                           (239,'724','299','br','Brazil',55,'Failed Calls'),
                                                                           (240,'724','999','br','Brazil',55,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (241,'724','21','br','Brazil',55,'Ligue'),
                                                                           (242,'724','39','br','Brazil',55,'Nextel'),
                                                                           (243,'724','00','br','Brazil',55,'Nextel'),
                                                                           (244,'724','299','br','Brazil',55,'NLT'),
                                                                           (245,'724','31','br','Brazil',55,'Oi Movel'),
                                                                           (246,'724','16','br','Brazil',55,'Oi Movel'),
                                                                           (247,'724','15','br','Brazil',55,'Sercomtel'),
                                                                           (248,'724','17','br','Brazil',55,'Surf'),
                                                                           (249,'724','299','br','Brazil',55,'Telecall'),
                                                                           (250,'724','04','br','Brazil',55,'TIM');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (251,'724','03','br','Brazil',55,'TIM'),
                                                                           (252,'724','02','br','Brazil',55,'TIM'),
                                                                           (253,'724','54','br','Brazil',55,'TIM'),
                                                                           (254,'724','299','br','Brazil',55,'Vecto Mobile'),
                                                                           (255,'724','23','br','Brazil',55,'Vivo'),
                                                                           (256,'724','06','br','Brazil',55,'Vivo'),
                                                                           (257,'724','10','br','Brazil',55,'Vivo'),
                                                                           (258,'724','11','br','Brazil',55,'Vivo'),
                                                                           (259,'348','570','vg','British Virgin Islands',284,'CCT'),
                                                                           (260,'348','770','vg','British Virgin Islands',284,'Digicel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (261,'348','299','vg','British Virgin Islands',284,'Failed Calls'),
                                                                           (262,'348','999','vg','British Virgin Islands',284,'Fix Line'),
                                                                           (263,'348','170','vg','British Virgin Islands',284,'Flow'),
                                                                           (264,'528','02','bn','Brunei',673,'B-Mobile'),
                                                                           (265,'528','11','bn','Brunei',673,'DST'),
                                                                           (266,'528','03','bn','Brunei',673,'DST'),
                                                                           (267,'528','299','bn','Brunei',673,'Failed Calls'),
                                                                           (268,'528','999','bn','Brunei',673,'Fix Line'),
                                                                           (269,'284','01','bg','Bulgaria',359,'A1'),
                                                                           (270,'284','11','bg','Bulgaria',359,'Bulsatcom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (271,'284','299','bg','Bulgaria',359,'Failed Calls'),
                                                                           (272,'284','999','bg','Bulgaria',359,'Fix Line'),
                                                                           (273,'284','13','bg','Bulgaria',359,'T.com'),
                                                                           (274,'284','05','bg','Bulgaria',359,'Telenor'),
                                                                           (275,'284','03','bg','Bulgaria',359,'Vivacom'),
                                                                           (276,'613','299','bf','Burkina Faso',226,'Failed Calls'),
                                                                           (277,'613','999','bf','Burkina Faso',226,'Fix Line'),
                                                                           (278,'613','02','bf','Burkina Faso',226,'Orange'),
                                                                           (279,'613','03','bf','Burkina Faso',226,'Telecel'),
                                                                           (280,'613','01','bf','Burkina Faso',226,'Telmob');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (281,'642','999','bi','Burundi',257,'Fix Line'),
                                                                           (282,'642','82','bi','Burundi',257,'Leo'),
                                                                           (283,'642','01','bi','Burundi',257,'Leo'),
                                                                           (284,'642','08','bi','Burundi',257,'Lumitel'),
                                                                           (285,'642','03','bi','Burundi',257,'ONAMOB'),
                                                                           (286,'642','07','bi','Burundi',257,'Smart'),
                                                                           (287,'456','04','kh','Cambodia',855,'QB'),
                                                                           (288,'456','01','kh','Cambodia',855,'Cellcard'),
                                                                           (289,'456','299','kh','Cambodia',855,'CooTel'),
                                                                           (290,'456','299','kh','Cambodia',855,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (291,'456','999','kh','Cambodia',855,'Fix Line'),
                                                                           (292,'456','08','kh','Cambodia',855,'Metfone'),
                                                                           (293,'456','299','kh','Cambodia',855,'MPTC'),
                                                                           (294,'456','11','kh','Cambodia',855,'Seatel'),
                                                                           (295,'456','05','kh','Cambodia',855,'Smart'),
                                                                           (296,'456','02','kh','Cambodia',855,'Smart'),
                                                                           (297,'456','06','kh','Cambodia',855,'Smart'),
                                                                           (298,'624','299','cm','Cameroon',237,'Failed Calls'),
                                                                           (299,'624','999','cm','Cameroon',237,'Fix Line'),
                                                                           (300,'624','01','cm','Cameroon',237,'MTN');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (301,'624','04','cm','Cameroon',237,'Nextel'),
                                                                           (302,'624','02','cm','Cameroon',237,'Orange'),
                                                                           (303,'302','630','ca','Canada',1,'Bell Mobility'),
                                                                           (304,'302','660','ca','Canada',1,'Bell Mobility'),
                                                                           (305,'302','64','ca','Canada',1,'Bell Mobility'),
                                                                           (306,'302','690','ca','Canada',1,'Bell Mobility'),
                                                                           (307,'302','69','ca','Canada',1,'Bell Mobility'),
                                                                           (308,'302','640','ca','Canada',1,'Bell Mobility'),
                                                                           (309,'302','610','ca','Canada',1,'Bell Mobility'),
                                                                           (310,'302','61','ca','Canada',1,'Bell Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (311,'302','66','ca','Canada',1,'Bell Mobility'),
                                                                           (312,'302','63','ca','Canada',1,'Bell Mobility'),
                                                                           (313,'302','270','ca','Canada',1,'eastlink'),
                                                                           (314,'302','299','ca','Canada',1,'Failed Calls'),
                                                                           (315,'302','37','ca','Canada',1,'Fido'),
                                                                           (316,'302','370','ca','Canada',1,'Fido'),
                                                                           (317,'302','999','ca','Canada',1,'Fix Line'),
                                                                           (318,'302','49','ca','Canada',1,'Freedom Mobile'),
                                                                           (319,'302','491','ca','Canada',1,'Freedom Mobile'),
                                                                           (320,'302','490','ca','Canada',1,'Freedom Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (321,'302','620','ca','Canada',1,'Ice Wireless'),
                                                                           (322,'302','62','ca','Canada',1,'Ice Wireless'),
                                                                           (323,'302','53','ca','Canada',1,'K-Net Mobile'),
                                                                           (324,'302','380','ca','Canada',1,'K-Net Mobile'),
                                                                           (325,'302','530','ca','Canada',1,'K-Net Mobile'),
                                                                           (326,'302','38','ca','Canada',1,'K-Net Mobile'),
                                                                           (327,'302','32','ca','Canada',1,'Rogers'),
                                                                           (328,'302','82','ca','Canada',1,'Rogers'),
                                                                           (329,'302','320','ca','Canada',1,'Rogers'),
                                                                           (330,'302','820','ca','Canada',1,'Rogers');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (331,'302','72','ca','Canada',1,'Rogers'),
                                                                           (332,'302','720','ca','Canada',1,'Rogers'),
                                                                           (333,'302','721','ca','Canada',1,'Rogers'),
                                                                           (334,'302','75','ca','Canada',1,'SaskTel Mobility'),
                                                                           (335,'302','78','ca','Canada',1,'SaskTel Mobility'),
                                                                           (336,'302','681','ca','Canada',1,'SaskTel Mobility'),
                                                                           (337,'302','780','ca','Canada',1,'SaskTel Mobility'),
                                                                           (338,'302','781','ca','Canada',1,'SaskTel Mobility'),
                                                                           (339,'302','750','ca','Canada',1,'SaskTel Mobility'),
                                                                           (340,'302','68','ca','Canada',1,'SaskTel Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (341,'302','680','ca','Canada',1,'SaskTel Mobility'),
                                                                           (342,'302','220','ca','Canada',1,'Telus Mobility'),
                                                                           (343,'302','760','ca','Canada',1,'Telus Mobility'),
                                                                           (344,'302','22','ca','Canada',1,'Telus Mobility'),
                                                                           (345,'302','22','ca','Canada',1,'Telus Mobility'),
                                                                           (346,'302','860','ca','Canada',1,'Telus Mobility'),
                                                                           (347,'302','76','ca','Canada',1,'Telus Mobility'),
                                                                           (348,'302','360','ca','Canada',1,'Telus Mobility'),
                                                                           (349,'302','221','ca','Canada',1,'Telus Mobility'),
                                                                           (350,'302','86','ca','Canada',1,'Telus Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (351,'302','36','ca','Canada',1,'Telus Mobility'),
                                                                           (352,'302','50','ca','Canada',1,'Videotron'),
                                                                           (353,'302','510','ca','Canada',1,'Videotron'),
                                                                           (354,'302','500','ca','Canada',1,'Videotron'),
                                                                           (355,'302','520','ca','Canada',1,'Videotron'),
                                                                           (356,'302','51','ca','Canada',1,'Videotron'),
                                                                           (357,'302','299','ca','Canada',1,'Xplore Mobile'),
                                                                           (358,'625','01','cv','Cape Verde',238,'CVMovel'),
                                                                           (359,'625','299','cv','Cape Verde',238,'Failed Calls'),
                                                                           (360,'625','999','cv','Cape Verde',238,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (361,'625','02','cv','Cape Verde',238,'Unitel T+'),
                                                                           (362,'346','050','ky','Cayman Islands',1345,'Digicel'),
                                                                           (363,'346','299','ky','Cayman Islands',1345,'Failed Calls'),
                                                                           (364,'346','999','ky','Cayman Islands',1345,'Fix Line'),
                                                                           (365,'346','140','ky','Cayman Islands',1345,'Flow / Lime'),
                                                                           (366,'346','001','ky','Cayman Islands',1345,'Logic'),
                                                                           (367,'623','04','cf','Central African Republic',236,'Azur'),
                                                                           (368,'623','299','cf','Central African Republic',236,'Failed Calls'),
                                                                           (369,'623','999','cf','Central African Republic',236,'Fix Line'),
                                                                           (370,'623','01','cf','Central African Republic',236,'Moov');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (371,'623','03','cf','Central African Republic',236,'Orange'),
                                                                           (372,'623','299','cf','Central African Republic',236,'Socatel'),
                                                                           (373,'623','02','cf','Central African Republic',236,'Telecel'),
                                                                           (374,'622','01','td','Chad',235,'Airtel'),
                                                                           (375,'622','299','td','Chad',235,'Failed Calls'),
                                                                           (376,'622','999','td','Chad',235,'Fix Line'),
                                                                           (377,'622','03','td','Chad',235,'Moov'),
                                                                           (378,'622','04','td','Chad',235,'Salam'),
                                                                           (379,'730','299','cl','Chile',56,'Boby'),
                                                                           (380,'730','299','cl','Chile',56,'CellOPS');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (381,'730','22','cl','Chile',56,'Cellplus'),
                                                                           (382,'730','11','cl','Chile',56,'Celupago'),
                                                                           (383,'730','15','cl','Chile',56,'Cibeles Telecom'),
                                                                           (384,'730','27','cl','Chile',56,'Cibeles Telecom'),
                                                                           (385,'730','03','cl','Chile',56,'Claro'),
                                                                           (386,'730','23','cl','Chile',56,'Claro'),
                                                                           (387,'730','17','cl','Chile',56,'Compatel'),
                                                                           (388,'730','299','cl','Chile',56,'Dotcom'),
                                                                           (389,'730','18','cl','Chile',56,'Empresas Bunker'),
                                                                           (390,'730','10','cl','Chile',56,'Entel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (391,'730','01','cl','Chile',56,'Entel'),
                                                                           (392,'730','299','cl','Chile',56,'Failed Calls'),
                                                                           (393,'730','999','cl','Chile',56,'Fix Line'),
                                                                           (394,'730','299','cl','Chile',56,'GTD Movil'),
                                                                           (395,'730','299','cl','Chile',56,'Industel'),
                                                                           (396,'730','06','cl','Chile',56,'INNET'),
                                                                           (397,'730','20','cl','Chile',56,'Inversiones Santa Fe'),
                                                                           (398,'730','299','cl','Chile',56,'Mavi'),
                                                                           (399,'730','299','cl','Chile',56,'Mobilink'),
                                                                           (400,'730','19','cl','Chile',56,'Movil Falabella');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (401,'730','07','cl','Chile',56,'Movistar'),
                                                                           (402,'730','02','cl','Chile',56,'Movistar'),
                                                                           (403,'730','299','cl','Chile',56,'Mundo Pacifico'),
                                                                           (404,'730','14','cl','Chile',56,'Netline'),
                                                                           (405,'730','299','cl','Chile',56,'NetUno'),
                                                                           (406,'730','16','cl','Chile',56,'Nomade Telecomunicaciones'),
                                                                           (407,'730','299','cl','Chile',56,'Quantax'),
                                                                           (408,'730','299','cl','Chile',56,'RedVoiss'),
                                                                           (409,'730','299','cl','Chile',56,'Simple'),
                                                                           (410,'730','299','cl','Chile',56,'Swedcom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (411,'730','299','cl','Chile',56,'Telcomax'),
                                                                           (412,'730','299','cl','Chile',56,'Telefonica Uno Uno Cuatro'),
                                                                           (413,'730','12','cl','Chile',56,'Telestar'),
                                                                           (414,'730','299','cl','Chile',56,'Television Interactiva'),
                                                                           (415,'730','13','cl','Chile',56,'Virgin Mobile'),
                                                                           (416,'730','299','cl','Chile',56,'VOIP Analysis'),
                                                                           (417,'730','08','cl','Chile',56,'VTR Movil'),
                                                                           (418,'730','09','cl','Chile',56,'WOM'),
                                                                           (419,'730','05','cl','Chile',56,'WOM'),
                                                                           (420,'730','04','cl','Chile',56,'WOM');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (421,'460','04','cn','China',86,'China Mobile'),
                                                                           (422,'460','02','cn','China',86,'China Mobile'),
                                                                           (423,'460','07','cn','China',86,'China Mobile'),
                                                                           (424,'460','00','cn','China',86,'China Mobile'),
                                                                           (425,'460','08','cn','China',86,'China Mobile'),
                                                                           (426,'460','03','cn','China',86,'China Telecom'),
                                                                           (427,'460','11','cn','China',86,'China Telecom'),
                                                                           (428,'460','12','cn','China',86,'China Telecom'),
                                                                           (429,'460','06','cn','China',86,'China Unicom'),
                                                                           (430,'460','01','cn','China',86,'China Unicom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (431,'460','09','cn','China',86,'China Unicom'),
                                                                           (432,'460','10','cn','China',86,'China Unicom'),
                                                                           (433,'460','299','cn','China',86,'Failed Calls'),
                                                                           (434,'460','999','cn','China',86,'Fix Line'),
                                                                           (435,'732','299','co','Colombia',57,'ATnet'),
                                                                           (436,'732','130','co','Colombia',57,'Avantel'),
                                                                           (437,'732','666','co','Colombia',57,'Claro'),
                                                                           (438,'732','101','co','Colombia',57,'Claro'),
                                                                           (439,'732','002','co','Colombia',57,'Edatel'),
                                                                           (440,'732','187','co','Colombia',57,'eTb');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (441,'732','299','co','Colombia',57,'Eztalk Mobile'),
                                                                           (442,'732','999','co','Colombia',57,'Fix Line'),
                                                                           (443,'732','240','co','Colombia',57,'Flash Mobile'),
                                                                           (444,'732','299','co','Colombia',57,'Gomobile'),
                                                                           (445,'732','220','co','Colombia',57,'Libre Tecnologias'),
                                                                           (446,'732','299','co','Colombia',57,'Movil Exito'),
                                                                           (447,'732','001','co','Colombia',57,'Movistar'),
                                                                           (448,'732','123','co','Colombia',57,'Movistar'),
                                                                           (449,'732','299','co','Colombia',57,'Plintron'),
                                                                           (450,'732','230','co','Colombia',57,'Setroc Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (451,'732','199','co','Colombia',57,'SUMA movil'),
                                                                           (452,'732','103','co','Colombia',57,'Tigo'),
                                                                           (453,'732','165','co','Colombia',57,'Tigo'),
                                                                           (454,'732','111','co','Colombia',57,'Tigo'),
                                                                           (455,'732','299','co','Colombia',57,'TuCEL'),
                                                                           (456,'732','142','co','Colombia',57,'UNE'),
                                                                           (457,'732','20','co','Colombia',57,'UNE'),
                                                                           (458,'732','299','co','Colombia',57,'Vilacom Mobile'),
                                                                           (459,'732','154','co','Colombia',57,'Virgin Mobile'),
                                                                           (460,'732','360','co','Colombia',57,'WOM');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (461,'654','299','km','Comoros',269,'Failed Calls'),
                                                                           (462,'654','999','km','Comoros',269,'Fix Line'),
                                                                           (463,'654','01','km','Comoros',269,'HURI'),
                                                                           (464,'654','02','km','Comoros',269,'Telma'),
                                                                           (465,'629','01','cg','Congo',242,'Airtel'),
                                                                           (466,'629','299','cg','Congo',242,'Failed Calls'),
                                                                           (467,'629','999','cg','Congo',242,'Fix Line'),
                                                                           (468,'629','10','cg','Congo',242,'MTN'),
                                                                           (469,'629','07','cg','Congo',242,'Warid'),
                                                                           (470,'548','01','ck','Cook Islands',682,'Vodafone / Bluesky');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (471,'548','299','ck','Cook Islands',682,'Failed Calls'),
                                                                           (472,'548','999','ck','Cook Islands',682,'Fix Line'),
                                                                           (473,'712','03','cr','Costa Rica',506,'Claro'),
                                                                           (474,'712','299','cr','Costa Rica',506,'Failed Calls'),
                                                                           (475,'712','999','cr','Costa Rica',506,'Fix Line'),
                                                                           (476,'712','01','cr','Costa Rica',506,'Kolbi / ICE'),
                                                                           (477,'712','02','cr','Costa Rica',506,'Kolbi / ICE'),
                                                                           (478,'712','04','cr','Costa Rica',506,'Movistar'),
                                                                           (479,'712','299','cr','Costa Rica',506,'MultiCom'),
                                                                           (480,'712','299','cr','Costa Rica',506,'racsa');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (481,'712','299','cr','Costa Rica',506,'Tuyo Movil'),
                                                                           (482,'219','10','hr','Croatia',385,'A1 / VIP'),
                                                                           (483,'219','299','hr','Croatia',385,'Failed Calls'),
                                                                           (484,'219','999','hr','Croatia',385,'Fix Line'),
                                                                           (485,'219','01','hr','Croatia',385,'T-Mobile'),
                                                                           (486,'219','12','hr','Croatia',385,'TELE FOCUS'),
                                                                           (487,'219','02','hr','Croatia',385,'Telemach / Tele2'),
                                                                           (488,'368','01','cu','Cuba',53,'Cubacel'),
                                                                           (489,'368','299','cu','Cuba',53,'Failed Calls'),
                                                                           (490,'368','999','cu','Cuba',53,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (491,'368','299','cu','Cuba',53,'MoviTel'),
                                                                           (492,'280','22','cy','Cyprus',357,'Cablenet / Lemontel'),
                                                                           (493,'280','01','cy','Cyprus',357,'Cytamobile-Vodafone'),
                                                                           (494,'280','02','cy','Cyprus',357,'Cytamobile-Vodafone'),
                                                                           (495,'280','10','cy','Cyprus',357,'Epic / MTN'),
                                                                           (496,'280','299','cy','Cyprus',357,'Failed Calls'),
                                                                           (497,'280','999','cy','Cyprus',357,'Fix Line'),
                                                                           (498,'280','20','cy','Cyprus',357,'PrimeTel'),
                                                                           (499,'230','299','cz','Czech Republic',420,'+4U Mobile'),
                                                                           (500,'230','299','cz','Czech Republic',420,'3ton');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (501,'230','299','cz','Czech Republic',420,'CEZ'),
                                                                           (502,'230','08','cz','Czech Republic',420,'Compatel'),
                                                                           (503,'230','299','cz','Czech Republic',420,'Dragon'),
                                                                           (504,'230','299','cz','Czech Republic',420,'EriMobile'),
                                                                           (505,'230','299','cz','Czech Republic',420,'Failed Calls'),
                                                                           (506,'230','299','cz','Czech Republic',420,'Fayn'),
                                                                           (507,'230','999','cz','Czech Republic',420,'Fix Line'),
                                                                           (508,'230','299','cz','Czech Republic',420,'GoMobil'),
                                                                           (509,'230','299','cz','Czech Republic',420,'ha-loo mobil'),
                                                                           (510,'230','299','cz','Czech Republic',420,'Laudatio');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (511,'230','299','cz','Czech Republic',420,'METRONET'),
                                                                           (512,'230','299','cz','Czech Republic',420,'MOBIL21'),
                                                                           (513,'230','299','cz','Czech Republic',420,'Nej Mobil'),
                                                                           (514,'230','299','cz','Czech Republic',420,'NETBOX Mobil'),
                                                                           (515,'230','04','cz','Czech Republic',420,'Nordic Telecom'),
                                                                           (516,'230','02','cz','Czech Republic',420,'O2'),
                                                                           (517,'230','299','cz','Czech Republic',420,'Odorik'),
                                                                           (518,'230','05','cz','Czech Republic',420,'PODA'),
                                                                           (519,'230','299','cz','Czech Republic',420,'SAZKA'),
                                                                           (520,'230','98','cz','Czech Republic',420,'SZDC');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (521,'230','299','cz','Czech Republic',420,'SZDC'),
                                                                           (522,'230','01','cz','Czech Republic',420,'T-Mobile'),
                                                                           (523,'230','07','cz','Czech Republic',420,'T-Mobile'),
                                                                           (524,'230','299','cz','Czech Republic',420,'Tesco Mobile'),
                                                                           (525,'230','299','cz','Czech Republic',420,'TOPefekt'),
                                                                           (526,'230','299','cz','Czech Republic',420,'TT Quality'),
                                                                           (527,'230','299','cz','Czech Republic',420,'Uniphone'),
                                                                           (528,'230','09','cz','Czech Republic',420,'Uniphone'),
                                                                           (529,'230','03','cz','Czech Republic',420,'Vodafone'),
                                                                           (530,'630','90','cd','Democratic Republic of Congo',243,'Africell');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (531,'630','02','cd','Democratic Republic of Congo',243,'Airtel'),
                                                                           (532,'630','299','cd','Democratic Republic of Congo',243,'Failed Calls'),
                                                                           (533,'630','999','cd','Democratic Republic of Congo',243,'Fix Line'),
                                                                           (534,'630','86','cd','Democratic Republic of Congo',243,'Orange'),
                                                                           (535,'630','05','cd','Democratic Republic of Congo',243,'Supercell'),
                                                                           (536,'630','299','cd','Democratic Republic of Congo',243,'Tatem'),
                                                                           (537,'630','01','cd','Democratic Republic of Congo',243,'Vodacom'),
                                                                           (538,'630','88','cd','Democratic Republic of Congo',243,'Yozma Timeturns'),
                                                                           (539,'238','06','dk','Denmark',45,'3'),
                                                                           (540,'238','299','dk','Denmark',45,'42 Telecom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (541,'238','299','dk','Denmark',45,'5Tel'),
                                                                           (542,'238','23','dk','Denmark',45,'Banedanmark'),
                                                                           (543,'238','299','dk','Denmark',45,'Benemen'),
                                                                           (544,'238','299','dk','Denmark',45,'Bolignet-Aarhus'),
                                                                           (545,'238','299','dk','Denmark',45,'CBB Mobil'),
                                                                           (546,'238','299','dk','Denmark',45,'celfon'),
                                                                           (547,'238','88','dk','Denmark',45,'Cobira'),
                                                                           (548,'238','299','dk','Denmark',45,'Companymobile'),
                                                                           (549,'238','13','dk','Denmark',45,'Compatel'),
                                                                           (550,'238','299','dk','Denmark',45,'Comtalk');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (551,'238','299','dk','Denmark',45,'Evercall'),
                                                                           (552,'238','299','dk','Denmark',45,'Failed Calls'),
                                                                           (553,'238','299','dk','Denmark',45,'Firmafon'),
                                                                           (554,'238','299','dk','Denmark',45,'Firstcom'),
                                                                           (555,'238','999','dk','Denmark',45,'Fix Line'),
                                                                           (556,'238','17','dk','Denmark',45,'Gotanet'),
                                                                           (557,'238','42','dk','Denmark',45,'Greenwave'),
                                                                           (558,'238','299','dk','Denmark',45,'ipnordic'),
                                                                           (559,'238','299','dk','Denmark',45,'Ipvision'),
                                                                           (560,'238','299','dk','Denmark',45,'Lancelot');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (561,'238','299','dk','Denmark',45,'Lebara'),
                                                                           (562,'238','28','dk','Denmark',45,'LINK Mobility'),
                                                                           (563,'238','12','dk','Denmark',45,'Lycamobile'),
                                                                           (564,'238','299','dk','Denmark',45,'Maxtel.dk'),
                                                                           (565,'238','299','dk','Denmark',45,'MI Carrier Services'),
                                                                           (566,'238','299','dk','Denmark',45,'Mitto'),
                                                                           (567,'238','299','dk','Denmark',45,'Mobiweb'),
                                                                           (568,'238','14','dk','Denmark',45,'Monty Mobile'),
                                                                           (569,'238','15','dk','Denmark',45,'Net 1'),
                                                                           (570,'238','04','dk','Denmark',45,'Nexcon.io');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (571,'238','299','dk','Denmark',45,'NextM2M'),
                                                                           (572,'238','73','dk','Denmark',45,'Onomondo'),
                                                                           (573,'238','30','dk','Denmark',45,'Pareteum'),
                                                                           (574,'238','299','dk','Denmark',45,'SimService'),
                                                                           (575,'238','03','dk','Denmark',45,'Syniverse'),
                                                                           (576,'238','10','dk','Denmark',45,'TDC'),
                                                                           (577,'238','01','dk','Denmark',45,'TDC'),
                                                                           (578,'238','77','dk','Denmark',45,'Telenor'),
                                                                           (579,'238','02','dk','Denmark',45,'Telenor'),
                                                                           (580,'238','299','dk','Denmark',45,'Telenor Connexion');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (581,'238','20','dk','Denmark',45,'Telia'),
                                                                           (582,'238','96','dk','Denmark',45,'Telia'),
                                                                           (583,'238','16','dk','Denmark',45,'Tismi'),
                                                                           (584,'238','299','dk','Denmark',45,'Tripple Track'),
                                                                           (585,'238','299','dk','Denmark',45,'Uni-tel'),
                                                                           (586,'238','25','dk','Denmark',45,'Viahub'),
                                                                           (587,'238','299','dk','Denmark',45,'Viptel'),
                                                                           (588,'238','08','dk','Denmark',45,'Voxbone / Bandwidth'),
                                                                           (589,'310','299','dg','Diego Garcia',246,'Failed Calls'),
                                                                           (590,'310','999','dg','Diego Garcia',246,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (591,'310','299','dg','Diego Garcia',246,'Sure'),
                                                                           (592,'638','01','dj','Djibouti',253,'Evatis'),
                                                                           (593,'638','299','dj','Djibouti',253,'Failed Calls'),
                                                                           (594,'638','999','dj','Djibouti',253,'Fix Line'),
                                                                           (595,'366','050','dm','Dominica',1767,'Digicel'),
                                                                           (596,'366','299','dm','Dominica',1767,'Failed Calls'),
                                                                           (597,'366','999','dm','Dominica',1767,'Fix Line'),
                                                                           (598,'366','110','dm','Dominica',1767,'Flow'),
                                                                           (599,'370','02','do','Dominican Republic',1809,'Claro'),
                                                                           (600,'370','299','do','Dominican Republic',1809,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (601,'370','999','do','Dominican Republic',1809,'Fix Line'),
                                                                           (602,'370','01','do','Dominican Republic',1809,'Orange / Altice'),
                                                                           (603,'370','299','do','Dominican Republic',1809,'Satel'),
                                                                           (604,'370','04','do','Dominican Republic',1809,'Viva'),
                                                                           (605,'514','299','tl','East Timor',670,'Failed Calls'),
                                                                           (606,'514','999','tl','East Timor',670,'Fix Line'),
                                                                           (607,'514','03','tl','East Timor',670,'Telemor'),
                                                                           (608,'514','01','tl','East Timor',670,'Telkomcel'),
                                                                           (609,'514','02','tl','East Timor',670,'Timor Telecom'),
                                                                           (610,'740','01','ec','Ecuador',593,'Claro');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (611,'740','02','ec','Ecuador',593,'CNT movil'),
                                                                           (612,'740','299','ec','Ecuador',593,'Failed Calls'),
                                                                           (613,'740','999','ec','Ecuador',593,'Fix Line'),
                                                                           (614,'740','00','ec','Ecuador',593,'Movistar'),
                                                                           (615,'602','03','eg','Egypt',20,'Etisalat'),
                                                                           (616,'602','299','eg','Egypt',20,'Failed Calls'),
                                                                           (617,'602','999','eg','Egypt',20,'Fix Line'),
                                                                           (618,'602','01','eg','Egypt',20,'Orange'),
                                                                           (619,'602','02','eg','Egypt',20,'Vodafone'),
                                                                           (620,'602','04','eg','Egypt',20,'WE');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (621,'706','01','sv','El Salvador',503,'Claro'),
                                                                           (622,'706','02','sv','El Salvador',503,'Digicel'),
                                                                           (623,'706','299','sv','El Salvador',503,'Failed Calls'),
                                                                           (624,'706','999','sv','El Salvador',503,'Fix Line'),
                                                                           (625,'706','04','sv','El Salvador',503,'Movistar'),
                                                                           (626,'706','05','sv','El Salvador',503,'RED'),
                                                                           (627,'706','03','sv','El Salvador',503,'Tigo'),
                                                                           (628,'627','299','gq','Equatorial Guinea',240,'Failed Calls'),
                                                                           (629,'627','999','gq','Equatorial Guinea',240,'Fix Line'),
                                                                           (630,'627','03','gq','Equatorial Guinea',240,'Muni');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (631,'627','01','gq','Equatorial Guinea',240,'Orange'),
                                                                           (632,'657','01','er','Eritrea',291,'Eritel'),
                                                                           (633,'657','299','er','Eritrea',291,'Failed Calls'),
                                                                           (634,'657','999','er','Eritrea',291,'Fix Line'),
                                                                           (635,'248','02','ee','Estonia',372,'Elisa'),
                                                                           (636,'248','299','ee','Estonia',372,'Failed Calls'),
                                                                           (637,'248','999','ee','Estonia',372,'Fix Line'),
                                                                           (638,'248','03','ee','Estonia',372,'Tele2'),
                                                                           (639,'248','13','ee','Estonia',372,'Telia'),
                                                                           (640,'248','01','ee','Estonia',372,'Telia');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (641,'248','04','ee','Estonia',372,'TravelSim'),
                                                                           (642,'636','01','et','Ethiopia',251,'Ethio Mobile'),
                                                                           (643,'636','299','et','Ethiopia',251,'Failed Calls'),
                                                                           (644,'636','999','et','Ethiopia',251,'Fix Line'),
                                                                           (645,'750','299','fk','Falkland Islands',500,'Failed Calls'),
                                                                           (646,'750','999','fk','Falkland Islands',500,'Fix Line'),
                                                                           (647,'750','001','fk','Falkland Islands',500,'Sure'),
                                                                           (648,'288','299','fo','Faroe Islands',298,'Failed Calls'),
                                                                           (649,'288','01','fo','Faroe Islands',298,'Faroese Telecom'),
                                                                           (650,'288','999','fo','Faroe Islands',298,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (651,'288','02','fo','Faroe Islands',298,'Hey / Kall'),
                                                                           (652,'288','03','fo','Faroe Islands',298,'Tosa'),
                                                                           (653,'542','02','fj','Fiji',679,'DigiCell'),
                                                                           (654,'542','299','fj','Fiji',679,'Failed Calls'),
                                                                           (655,'542','999','fj','Fiji',679,'Fix Line'),
                                                                           (656,'542','01','fj','Fiji',679,'Vodafone'),
                                                                           (657,'244','14','fi','Finland',358,'Alcom'),
                                                                           (658,'244','299','fi','Finland',358,'Benemen'),
                                                                           (659,'244','26','fi','Finland',358,'Compatel'),
                                                                           (660,'244','299','fi','Finland',358,'Cuuma');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (661,'244','13','fi','Finland',358,'DNA'),
                                                                           (662,'244','03','fi','Finland',358,'DNA'),
                                                                           (663,'244','12','fi','Finland',358,'DNA'),
                                                                           (664,'244','04','fi','Finland',358,'DNA'),
                                                                           (665,'244','06','fi','Finland',358,'Elisa'),
                                                                           (666,'244','21','fi','Finland',358,'Elisa'),
                                                                           (667,'244','05','fi','Finland',358,'Elisa'),
                                                                           (668,'244','299','fi','Finland',358,'Failed Calls'),
                                                                           (669,'244','999','fi','Finland',358,'Fix Line'),
                                                                           (670,'244','299','fi','Finland',358,'interactive digital media / IDM');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (671,'244','299','fi','Finland',358,'IPIFY'),
                                                                           (672,'244','299','fi','Finland',358,'Lancelot'),
                                                                           (673,'244','299','fi','Finland',358,'MI Carrier Services'),
                                                                           (674,'244','299','fi','Finland',358,'MobiWeb'),
                                                                           (675,'244','24','fi','Finland',358,'Nord Connect'),
                                                                           (676,'244','38','fi','Finland',358,'NSN'),
                                                                           (677,'244','39','fi','Finland',358,'NSN'),
                                                                           (678,'244','09','fi','Finland',358,'NSN'),
                                                                           (679,'244','41','fi','Finland',358,'NSN'),
                                                                           (680,'244','40','fi','Finland',358,'NSN');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (681,'244','07','fi','Finland',358,'NSN'),
                                                                           (682,'244','08','fi','Finland',358,'NSN'),
                                                                           (683,'244','299','fi','Finland',358,'RAILI'),
                                                                           (684,'244','43','fi','Finland',358,'Telavox'),
                                                                           (685,'244','91','fi','Finland',358,'Telia'),
                                                                           (686,'244','36','fi','Finland',358,'Telia'),
                                                                           (687,'244','15','fi','Finland',358,'Telit'),
                                                                           (688,'244','37','fi','Finland',358,'Tismi'),
                                                                           (689,'244','299','fi','Finland',358,'TravelSim'),
                                                                           (690,'244','299','fi','Finland',358,'Twilio');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (691,'244','35','fi','Finland',358,'Ukko Mobile'),
                                                                           (692,'244','42','fi','Finland',358,'Viahub'),
                                                                           (693,'244','47','fi','Finland',358,'VIRVE'),
                                                                           (694,'244','46','fi','Finland',358,'VIRVE'),
                                                                           (695,'244','45','fi','Finland',358,'VIRVE'),
                                                                           (696,'244','33','fi','Finland',358,'VIRVE'),
                                                                           (697,'244','32','fi','Finland',358,'Voxbone / Bandwidth'),
                                                                           (698,'208','299','fr','France',33,'Add-On Multimedia'),
                                                                           (699,'208','299','fr','France',33,'Afone Mobile'),
                                                                           (700,'208','28','fr','France',33,'Airmob');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (701,'208','299','fr','France',33,'Alphalink'),
                                                                           (702,'208','299','fr','France',33,'Alphalink'),
                                                                           (703,'208','299','fr','France',33,'Annatel'),
                                                                           (704,'208','299','fr','France',33,'Atlas'),
                                                                           (705,'208','299','fr','France',33,'Auchan'),
                                                                           (706,'208','299','fr','France',33,'Auchan'),
                                                                           (707,'208','299','fr','France',33,'Axialys'),
                                                                           (708,'208','299','fr','France',33,'Bazile'),
                                                                           (709,'208','299','fr','France',33,'BJT'),
                                                                           (710,'208','299','fr','France',33,'BJT');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (711,'208','88','fr','France',33,'Bouygues Telecom'),
                                                                           (712,'208','21','fr','France',33,'Bouygues Telecom'),
                                                                           (713,'208','20','fr','France',33,'Bouygues Telecom'),
                                                                           (714,'208','299','fr','France',33,'Bretagne Telecom'),
                                                                           (715,'208','299','fr','France',33,'CAT'),
                                                                           (716,'208','299','fr','France',33,'CELESTE'),
                                                                           (717,'208','34','fr','France',33,'Cellhire'),
                                                                           (718,'208','299','fr','France',33,'Codepi'),
                                                                           (719,'208','299','fr','France',33,'Coolwave'),
                                                                           (720,'208','27','fr','France',33,'Coriolis');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (721,'208','299','fr','France',33,'CPRO'),
                                                                           (722,'208','299','fr','France',33,'CRH'),
                                                                           (723,'208','299','fr','France',33,'CRT'),
                                                                           (724,'208','299','fr','France',33,'CTExcel'),
                                                                           (725,'208','299','fr','France',33,'Doctolib'),
                                                                           (726,'208','299','fr','France',33,'Failed Calls'),
                                                                           (727,'208','999','fr','France',33,'Fix Line'),
                                                                           (728,'208','299','fr','France',33,'Foliateam'),
                                                                           (729,'208','16','fr','France',33,'Free Mobile'),
                                                                           (730,'208','35','fr','France',33,'Free Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (731,'208','15','fr','France',33,'Free Mobile'),
                                                                           (732,'208','36','fr','France',33,'Free Mobile'),
                                                                           (733,'208','94','fr','France',33,'Halys'),
                                                                           (734,'208','299','fr','France',33,'Happy Telecom'),
                                                                           (735,'208','299','fr','France',33,'Hexatel'),
                                                                           (736,'208','89','fr','France',33,'Hub One'),
                                                                           (737,'208','299','fr','France',33,'i-via'),
                                                                           (738,'208','37','fr','France',33,'IP Directions'),
                                                                           (739,'208','299','fr','France',33,'IP Telecom'),
                                                                           (740,'208','299','fr','France',33,'Iptis');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (741,'208','299','fr','France',33,'JOi'),
                                                                           (742,'208','299','fr','France',33,'Kertel'),
                                                                           (743,'208','299','fr','France',33,'Keyyo Mobile'),
                                                                           (744,'208','299','fr','France',33,'La Poste Mobile'),
                                                                           (745,'208','299','fr','France',33,'La Poste Mobile'),
                                                                           (746,'208','299','fr','France',33,'LASOTEL'),
                                                                           (747,'208','38','fr','France',33,'Lebara'),
                                                                           (748,'208','17','fr','France',33,'Legos'),
                                                                           (749,'208','299','fr','France',33,'Linkt'),
                                                                           (750,'208','299','fr','France',33,'LITEYEAR');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (751,'208','25','fr','France',33,'Lycamobile'),
                                                                           (752,'208','24','fr','France',33,'MobiquiThings'),
                                                                           (753,'208','03','fr','France',33,'MobiquiThings'),
                                                                           (754,'208','299','fr','France',33,'Netcom'),
                                                                           (755,'208','39','fr','France',33,'Networth Telecom'),
                                                                           (756,'208','299','fr','France',33,'Nordnet'),
                                                                           (757,'208','26','fr','France',33,'NRJ'),
                                                                           (758,'208','299','fr','France',33,'onoff'),
                                                                           (759,'208','299','fr','France',33,'OpenIP'),
                                                                           (760,'208','02','fr','France',33,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (761,'208','32','fr','France',33,'Orange'),
                                                                           (762,'208','01','fr','France',33,'Orange'),
                                                                           (763,'208','91','fr','France',33,'Orange'),
                                                                           (764,'208','299','fr','France',33,'Paritel'),
                                                                           (765,'208','299','fr','France',33,'Prixtel'),
                                                                           (766,'208','299','fr','France',33,'Prixtel'),
                                                                           (767,'208','299','fr','France',33,'SCT TELECOM'),
                                                                           (768,'208','299','fr','France',33,'SCT TELECOM'),
                                                                           (769,'208','299','fr','France',33,'Sewan Communications'),
                                                                           (770,'208','13','fr','France',33,'SFR');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (771,'208','08','fr','France',33,'SFR'),
                                                                           (772,'208','11','fr','France',33,'SFR'),
                                                                           (773,'208','10','fr','France',33,'SFR'),
                                                                           (774,'208','09','fr','France',33,'SFR'),
                                                                           (775,'208','299','fr','France',33,'SIMbioz'),
                                                                           (776,'208','30','fr','France',33,'Syma Mobile'),
                                                                           (777,'208','299','fr','France',33,'TDF'),
                                                                           (778,'208','299','fr','France',33,'Tismi'),
                                                                           (779,'208','22','fr','France',33,'Transatel'),
                                                                           (780,'208','299','fr','France',33,'Trunkline');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (781,'208','12','fr','France',33,'Truphone'),
                                                                           (782,'208','299','fr','France',33,'unyc'),
                                                                           (783,'208','299','fr','France',33,'VA SOLUTIONS'),
                                                                           (784,'208','31','fr','France',33,'Vectone Mobile'),
                                                                           (785,'208','299','fr','France',33,'VOIP Telecom'),
                                                                           (786,'547','299','pf','French Polynesia',689,'Failed Calls'),
                                                                           (787,'547','999','pf','French Polynesia',689,'Fix Line'),
                                                                           (788,'547','299','pf','French Polynesia',689,'Ora'),
                                                                           (789,'547','20','pf','French Polynesia',689,'Vini'),
                                                                           (790,'547','15','pf','French Polynesia',689,'Vodafone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (791,'628','03','ga','Gabon',241,'Airtel'),
                                                                           (792,'628','299','ga','Gabon',241,'Failed Calls'),
                                                                           (793,'628','999','ga','Gabon',241,'Fix Line'),
                                                                           (794,'628','01','ga','Gabon',241,'Libertis'),
                                                                           (795,'607','02','gm','Gambia',220,'Africel'),
                                                                           (796,'607','03','gm','Gambia',220,'Comium'),
                                                                           (797,'607','299','gm','Gambia',220,'Failed Calls'),
                                                                           (798,'607','999','gm','Gambia',220,'Fix Line'),
                                                                           (799,'607','01','gm','Gambia',220,'Gamcel'),
                                                                           (800,'607','04','gm','Gambia',220,'QCell');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (801,'282','04','ge','Georgia',995,'Beeline'),
                                                                           (802,'282','299','ge','Georgia',995,'Failed Calls'),
                                                                           (803,'282','999','ge','Georgia',995,'Fix Line'),
                                                                           (804,'282','01','ge','Georgia',995,'Geocell'),
                                                                           (805,'282','07','ge','Georgia',995,'GlobalCell'),
                                                                           (806,'282','02','ge','Georgia',995,'MagtiCom'),
                                                                           (807,'282','11','ge','Georgia',995,'Mobilive'),
                                                                           (808,'282','22','ge','Georgia',995,'MyPhone'),
                                                                           (809,'282','10','ge','Georgia',995,'Premium Net'),
                                                                           (810,'282','08','ge','Georgia',995,'Silknet');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (811,'282','12','ge','Georgia',995,'Telecom 1'),
                                                                           (812,'262','23','de','Germany',49,'1&1'),
                                                                           (813,'262','299','de','Germany',49,'Argon Networks'),
                                                                           (814,'262','10','de','Germany',49,'DB Netz'),
                                                                           (815,'262','20','de','Germany',49,'Telefonica / E-Plus'),
                                                                           (816,'262','999','de','Germany',49,'Fix Line'),
                                                                           (817,'262','14','de','Germany',49,'Lebara'),
                                                                           (818,'262','43','de','Germany',49,'Lycamobile'),
                                                                           (819,'262','21','de','Germany',49,'Multiconnect'),
                                                                           (820,'262','12','de','Germany',49,'sipgate');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (821,'262','22','de','Germany',49,'sipgate'),
                                                                           (822,'262','06','de','Germany',49,'T-mobile'),
                                                                           (823,'262','01','de','Germany',49,'T-mobile'),
                                                                           (824,'262','78','de','Germany',49,'T-mobile'),
                                                                           (825,'262','24','de','Germany',49,'TelcoVillage'),
                                                                           (826,'262','05','de','Germany',49,'Telefonica / E-Plus'),
                                                                           (827,'262','17','de','Germany',49,'Telefonica / E-Plus'),
                                                                           (828,'262','03','de','Germany',49,'Telefonica / E-Plus'),
                                                                           (829,'262','16','de','Germany',49,'Telefonica / O2'),
                                                                           (830,'262','11','de','Germany',49,'Telefonica / O2');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (831,'262','08','de','Germany',49,'Telefonica / O2'),
                                                                           (832,'262','07','de','Germany',49,'Telefonica / O2'),
                                                                           (833,'262','299','de','Germany',49,'Tismi'),
                                                                           (834,'262','299','de','Germany',49,'Truphone'),
                                                                           (835,'262','02','de','Germany',49,'Vodafone'),
                                                                           (836,'262','09','de','Germany',49,'Vodafone'),
                                                                           (837,'262','04','de','Germany',49,'Vodafone'),
                                                                           (838,'262','42','de','Germany',49,'Vodafone'),
                                                                           (839,'620','03','gh','Ghana',233,'Airtel'),
                                                                           (840,'620','06','gh','Ghana',233,'Airtel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (841,'620','299','gh','Ghana',233,'Comsys'),
                                                                           (842,'620','299','gh','Ghana',233,'Failed Calls'),
                                                                           (843,'620','999','gh','Ghana',233,'Fix Line'),
                                                                           (844,'620','07','gh','Ghana',233,'Glo'),
                                                                           (845,'620','01','gh','Ghana',233,'MTN'),
                                                                           (846,'620','05','gh','Ghana',233,'National Security'),
                                                                           (847,'620','08','gh','Ghana',233,'Surfline'),
                                                                           (848,'620','02','gh','Ghana',233,'Vodafone'),
                                                                           (849,'266','299','gi','Gibraltar',350,'Failed Calls'),
                                                                           (850,'266','999','gi','Gibraltar',350,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (851,'266','299','gi','Gibraltar',350,'GibFibreSpeed'),
                                                                           (852,'266','01','gi','Gibraltar',350,'Gibtel'),
                                                                           (853,'202','299','gr','Greece',30,'AMD Telecom'),
                                                                           (854,'202','299','gr','Greece',30,'Apifon'),
                                                                           (855,'202','15','gr','Greece',30,'BWS'),
                                                                           (856,'202','02','gr','Greece',30,'Cosmote'),
                                                                           (857,'202','01','gr','Greece',30,'Cosmote'),
                                                                           (858,'202','999','gr','Greece',30,'Fix Line'),
                                                                           (859,'202','16','gr','Greece',30,'Inter Telecom'),
                                                                           (860,'202','299','gr','Greece',30,'Interconnect');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (861,'202','299','gr','Greece',30,'M-STAT'),
                                                                           (862,'202','299','gr','Greece',30,'Nova'),
                                                                           (863,'202','04','gr','Greece',30,'OSE'),
                                                                           (864,'202','03','gr','Greece',30,'OTE'),
                                                                           (865,'202','299','gr','Greece',30,'OTEGLOBE'),
                                                                           (866,'202','299','gr','Greece',30,'Premium Net'),
                                                                           (867,'202','05','gr','Greece',30,'Vodafone'),
                                                                           (868,'202','10','gr','Greece',30,'Wind'),
                                                                           (869,'202','09','gr','Greece',30,'Wind'),
                                                                           (870,'202','12','gr','Greece',30,'Yuboto');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (871,'290','299','gl','Greenland',299,'Failed Calls'),
                                                                           (872,'290','999','gl','Greenland',299,'Fix Line'),
                                                                           (873,'290','01','gl','Greenland',299,'Tele Greenland'),
                                                                           (874,'352','050','gd','Grenada',1473,'Digicel'),
                                                                           (875,'352','299','gd','Grenada',1473,'Failed Calls'),
                                                                           (876,'352','999','gd','Grenada',1473,'Fix Line'),
                                                                           (877,'352','110','gd','Grenada',1473,'Flow'),
                                                                           (878,'352','299','gd','Grenada',1473,'Spice Mobile'),
                                                                           (879,'340','08','gf','Guadeloupe and Martinique and French Guiana',594,'Amigo'),
                                                                           (880,'340','20','gf','Guadeloupe and Martinique and French Guiana',594,'Digicel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (881,'340','299','gf','Guadeloupe and Martinique and French Guiana',594,'Failed Calls'),
                                                                           (882,'340','999','gf','Guadeloupe and Martinique and French Guiana',594,'Fix Line'),
                                                                           (883,'340','04','gf','Guadeloupe and Martinique and French Guiana',594,'Free'),
                                                                           (884,'340','01','gf','Guadeloupe and Martinique and French Guiana',594,'Orange'),
                                                                           (885,'340','02','gf','Guadeloupe and Martinique and French Guiana',594,'SFR'),
                                                                           (886,'310','370','gu','Guam',1671,'Docomo Pacific'),
                                                                           (887,'310','470','gu','Guam',1671,'Docomo Pacific'),
                                                                           (888,'310','299','gu','Guam',1671,'Failed Calls'),
                                                                           (889,'310','999','gu','Guam',1671,'Fix Line'),
                                                                           (890,'310','140','gu','Guam',1671,'GTA');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (891,'310','110','gu','Guam',1671,'IT&E'),
                                                                           (892,'704','01','gt','Guatemala',502,'Claro'),
                                                                           (893,'704','299','gt','Guatemala',502,'Failed Calls'),
                                                                           (894,'704','999','gt','Guatemala',502,'Fix Line'),
                                                                           (895,'704','03','gt','Guatemala',502,'Movistar'),
                                                                           (896,'704','02','gt','Guatemala',502,'Tigo'),
                                                                           (897,'611','05','gn','Guinea',224,'Cellcom'),
                                                                           (898,'611','299','gn','Guinea',224,'Failed Calls'),
                                                                           (899,'611','999','gn','Guinea',224,'Fix Line'),
                                                                           (900,'611','04','gn','Guinea',224,'MTN');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (901,'611','01','gn','Guinea',224,'Orange'),
                                                                           (902,'632','299','gw','Guinea-Bissau',245,'Failed Calls'),
                                                                           (903,'632','999','gw','Guinea-Bissau',245,'Fix Line'),
                                                                           (904,'632','01','gw','Guinea-Bissau',245,'Guinetel'),
                                                                           (905,'632','02','gw','Guinea-Bissau',245,'MTN'),
                                                                           (906,'632','03','gw','Guinea-Bissau',245,'Orange'),
                                                                           (907,'738','02','gy','Guyana',592,'Cellink'),
                                                                           (908,'738','01','gy','Guyana',592,'Digicel'),
                                                                           (909,'738','299','gy','Guyana',592,'Failed Calls'),
                                                                           (910,'738','999','gy','Guyana',592,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (911,'372','02','ht','Haiti',509,'Digicel'),
                                                                           (912,'372','299','ht','Haiti',509,'Failed Calls'),
                                                                           (913,'372','999','ht','Haiti',509,'Fix Line'),
                                                                           (914,'372','03','ht','Haiti',509,'Natcom'),
                                                                           (915,'708','001','hn','Honduras',504,'Claro'),
                                                                           (916,'708','01','hn','Honduras',504,'Claro'),
                                                                           (917,'708','299','hn','Honduras',504,'Failed Calls'),
                                                                           (918,'708','999','hn','Honduras',504,'Fix Line'),
                                                                           (919,'708','030','hn','Honduras',504,'HonduTel'),
                                                                           (920,'708','30','hn','Honduras',504,'HonduTel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (921,'708','02','hn','Honduras',504,'Tigo'),
                                                                           (922,'708','002','hn','Honduras',504,'Tigo'),
                                                                           (923,'454','14','hk','Hong Kong',852,'3'),
                                                                           (924,'454','05','hk','Hong Kong',852,'3'),
                                                                           (925,'454','04','hk','Hong Kong',852,'3'),
                                                                           (926,'454','03','hk','Hong Kong',852,'3'),
                                                                           (927,'454','12','hk','Hong Kong',852,'China Mobile'),
                                                                           (928,'454','32','hk','Hong Kong',852,'HKBN'),
                                                                           (929,'454','30','hk','Hong Kong',852,'China Mobile'),
                                                                           (930,'454','13','hk','Hong Kong',852,'China Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (931,'454','07','hk','Hong Kong',852,'China Unicom'),
                                                                           (932,'454','11','hk','Hong Kong',852,'CHKTL'),
                                                                           (933,'454','01','hk','Hong Kong',852,'Citic'),
                                                                           (934,'454','31','hk','Hong Kong',852,'CTExcel'),
                                                                           (935,'454','36','hk','Hong Kong',852,'Eascotel'),
                                                                           (936,'454','999','hk','Hong Kong',852,'Fix Line'),
                                                                           (937,'454','391','hk','Hong Kong',852,'Government'),
                                                                           (938,'454','25','hk','Hong Kong',852,'Government'),
                                                                           (939,'454','395','hk','Hong Kong',852,'Government'),
                                                                           (940,'454','26','hk','Hong Kong',852,'Government');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (941,'454','390','hk','Hong Kong',852,'Government'),
                                                                           (942,'454','394','hk','Hong Kong',852,'Government'),
                                                                           (943,'454','398','hk','Hong Kong',852,'Government'),
                                                                           (944,'454','393','hk','Hong Kong',852,'Government'),
                                                                           (945,'454','397','hk','Hong Kong',852,'Government'),
                                                                           (946,'454','392','hk','Hong Kong',852,'Government'),
                                                                           (947,'454','28','hk','Hong Kong',852,'Government'),
                                                                           (948,'454','27','hk','Hong Kong',852,'Government'),
                                                                           (949,'454','396','hk','Hong Kong',852,'Government'),
                                                                           (950,'454','24','hk','Hong Kong',852,'Multibyte');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (951,'454','15','hk','Hong Kong',852,'SmarTone'),
                                                                           (952,'454','17','hk','Hong Kong',852,'SmarTone'),
                                                                           (953,'454','06','hk','Hong Kong',852,'SmarTone'),
                                                                           (954,'454','16','hk','Hong Kong',852,'SUN Mobile / CSL'),
                                                                           (955,'454','02','hk','Hong Kong',852,'SUN Mobile / CSL'),
                                                                           (956,'454','00','hk','Hong Kong',852,'SUN Mobile / CSL'),
                                                                           (957,'454','10','hk','Hong Kong',852,'SUN Mobile / CSL'),
                                                                           (958,'454','20','hk','Hong Kong',852,'SUN Mobile / CSL'),
                                                                           (959,'454','18','hk','Hong Kong',852,'SUN Mobile / CSL'),
                                                                           (960,'454','19','hk','Hong Kong',852,'SUN Mobile / CSL');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (961,'454','08','hk','Hong Kong',852,'Tru'),
                                                                           (962,'454','35','hk','Hong Kong',852,'Webbing'),
                                                                           (963,'216','299','hu','Hungary',36,'Antenna'),
                                                                           (964,'216','03','hu','Hungary',36,'Digi'),
                                                                           (965,'216','999','hu','Hungary',36,'Fix line'),
                                                                           (966,'216','299','hu','Hungary',36,'Invitech'),
                                                                           (967,'216','299','hu','Hungary',36,'Mobil4'),
                                                                           (968,'216','02','hu','Hungary',36,'MVM NET'),
                                                                           (969,'216','299','hu','Hungary',36,'Netfone'),
                                                                           (970,'216','299','hu','Hungary',36,'TARR');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (971,'216','30','hu','Hungary',36,'Telekom'),
                                                                           (972,'216','01','hu','Hungary',36,'Telenor'),
                                                                           (973,'216','299','hu','Hungary',36,'Vidanet'),
                                                                           (974,'216','71','hu','Hungary',36,'Vodafone'),
                                                                           (975,'216','70','hu','Hungary',36,'Vodafone'),
                                                                           (976,'274','299','is','Iceland',354,'Failed Calls'),
                                                                           (977,'274','999','is','Iceland',354,'Fix Line'),
                                                                           (978,'274','11','is','Iceland',354,'NOVA'),
                                                                           (979,'274','31','is','Iceland',354,'Siminn'),
                                                                           (980,'274','08','is','Iceland',354,'Siminn');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (981,'274','01','is','Iceland',354,'Siminn'),
                                                                           (982,'274','16','is','Iceland',354,'Tismi'),
                                                                           (983,'274','04','is','Iceland',354,'Viking Wireless'),
                                                                           (984,'274','12','is','Iceland',354,'Vodafone'),
                                                                           (985,'274','03','is','Iceland',354,'Vodafone'),
                                                                           (986,'274','02','is','Iceland',354,'Vodafone'),
                                                                           (987,'404','41','in','India',91,'Aircel'),
                                                                           (988,'404','28','in','India',91,'Aircel'),
                                                                           (989,'404','25','in','India',91,'Aircel'),
                                                                           (990,'404','37','in','India',91,'Aircel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (991,'404','35','in','India',91,'Aircel'),
                                                                           (992,'404','33','in','India',91,'Aircel'),
                                                                           (993,'404','17','in','India',91,'Aircel'),
                                                                           (994,'404','91','in','India',91,'Aircel'),
                                                                           (995,'404','42','in','India',91,'Aircel'),
                                                                           (996,'404','29','in','India',91,'Aircel'),
                                                                           (997,'404','94','in','India',91,'Airtel'),
                                                                           (998,'404','98','in','India',91,'Airtel'),
                                                                           (999,'404','06','in','India',91,'Airtel'),
                                                                           (1000,'404','93','in','India',91,'Airtel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1001,'404','49','in','India',91,'Airtel'),
                                                                           (1002,'404','97','in','India',91,'Airtel'),
                                                                           (1003,'404','03','in','India',91,'Airtel'),
                                                                           (1004,'404','92','in','India',91,'Airtel'),
                                                                           (1005,'404','31','in','India',91,'Airtel'),
                                                                           (1006,'404','96','in','India',91,'Airtel'),
                                                                           (1007,'404','02','in','India',91,'Airtel'),
                                                                           (1008,'404','90','in','India',91,'Airtel'),
                                                                           (1009,'404','16','in','India',91,'Airtel'),
                                                                           (1010,'404','95','in','India',91,'Airtel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1011,'404','70','in','India',91,'Airtel'),
                                                                           (1012,'404','10','in','India',91,'Airtel'),
                                                                           (1013,'404','72','in','India',91,'BSNL Mobile'),
                                                                           (1014,'404','80','in','India',91,'BSNL Mobile'),
                                                                           (1015,'404','59','in','India',91,'BSNL Mobile'),
                                                                           (1016,'404','76','in','India',91,'BSNL Mobile'),
                                                                           (1017,'404','53','in','India',91,'BSNL Mobile'),
                                                                           (1018,'404','71','in','India',91,'BSNL Mobile'),
                                                                           (1019,'404','58','in','India',91,'BSNL Mobile'),
                                                                           (1020,'404','75','in','India',91,'BSNL Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1021,'404','51','in','India',91,'BSNL Mobile'),
                                                                           (1022,'404','66','in','India',91,'BSNL Mobile'),
                                                                           (1023,'404','57','in','India',91,'BSNL Mobile'),
                                                                           (1024,'404','74','in','India',91,'BSNL Mobile'),
                                                                           (1025,'404','38','in','India',91,'BSNL Mobile'),
                                                                           (1026,'404','64','in','India',91,'BSNL Mobile'),
                                                                           (1027,'404','55','in','India',91,'BSNL Mobile'),
                                                                           (1028,'404','73','in','India',91,'BSNL Mobile'),
                                                                           (1029,'404','34','in','India',91,'BSNL Mobile'),
                                                                           (1030,'404','81','in','India',91,'BSNL Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1031,'404','62','in','India',91,'BSNL Mobile'),
                                                                           (1032,'404','77','in','India',91,'BSNL Mobile'),
                                                                           (1033,'404','54','in','India',91,'BSNL Mobile'),
                                                                           (1034,'404','69','in','India',91,'Dolphin'),
                                                                           (1035,'404','299','in','India',91,'Failed Calls'),
                                                                           (1036,'404','999','in','India',91,'Fix Line'),
                                                                           (1037,'404','68','in','India',91,'Garuda'),
                                                                           (1038,'404','14','in','India',91,'Idea'),
                                                                           (1039,'404','846','in','India',91,'Idea'),
                                                                           (1040,'404','44','in','India',91,'Idea');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1041,'404','852','in','India',91,'Idea'),
                                                                           (1042,'404','12','in','India',91,'Idea'),
                                                                           (1043,'404','845','in','India',91,'Idea'),
                                                                           (1044,'404','24','in','India',91,'Idea'),
                                                                           (1045,'404','850','in','India',91,'Idea'),
                                                                           (1046,'404','07','in','India',91,'Idea'),
                                                                           (1047,'404','82','in','India',91,'Idea'),
                                                                           (1048,'404','89','in','India',91,'Idea'),
                                                                           (1049,'404','22','in','India',91,'Idea'),
                                                                           (1050,'404','849','in','India',91,'Idea');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1051,'404','04','in','India',91,'Idea'),
                                                                           (1052,'404','78','in','India',91,'Idea'),
                                                                           (1053,'404','87','in','India',91,'Idea'),
                                                                           (1054,'404','19','in','India',91,'Idea'),
                                                                           (1055,'404','848','in','India',91,'Idea'),
                                                                           (1056,'404','56','in','India',91,'Idea'),
                                                                           (1057,'404','853','in','India',91,'Idea'),
                                                                           (1058,'404','862','in','India',91,'Jio'),
                                                                           (1059,'404','855','in','India',91,'Jio'),
                                                                           (1060,'404','867','in','India',91,'Jio');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1061,'404','858','in','India',91,'Jio'),
                                                                           (1062,'404','872','in','India',91,'Jio'),
                                                                           (1063,'404','863','in','India',91,'Jio'),
                                                                           (1064,'404','868','in','India',91,'Jio'),
                                                                           (1065,'404','859','in','India',91,'Jio'),
                                                                           (1066,'404','873','in','India',91,'Jio'),
                                                                           (1067,'404','864','in','India',91,'Jio'),
                                                                           (1068,'404','856','in','India',91,'Jio'),
                                                                           (1069,'404','869','in','India',91,'Jio'),
                                                                           (1070,'404','860','in','India',91,'Jio');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1071,'404','865','in','India',91,'Jio'),
                                                                           (1072,'404','857','in','India',91,'Jio'),
                                                                           (1073,'404','870','in','India',91,'Jio'),
                                                                           (1074,'404','861','in','India',91,'Jio'),
                                                                           (1075,'404','854','in','India',91,'Jio'),
                                                                           (1076,'404','874','in','India',91,'Jio'),
                                                                           (1077,'404','866','in','India',91,'Jio'),
                                                                           (1078,'404','871','in','India',91,'Jio'),
                                                                           (1079,'404','21','in','India',91,'Loop Mobile'),
                                                                           (1080,'404','09','in','India',91,'Reliance Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1081,'404','23','in','India',91,'Reliance Mobile'),
                                                                           (1082,'404','83','in','India',91,'Reliance Mobile'),
                                                                           (1083,'404','36','in','India',91,'Reliance Mobile'),
                                                                           (1084,'404','85','in','India',91,'Reliance Mobile'),
                                                                           (1085,'404','50','in','India',91,'Reliance Mobile'),
                                                                           (1086,'404','52','in','India',91,'Reliance Mobile'),
                                                                           (1087,'404','08','in','India',91,'Reliance Mobile'),
                                                                           (1088,'404','18','in','India',91,'Reliance Mobile'),
                                                                           (1089,'404','67','in','India',91,'Reliance Mobile'),
                                                                           (1090,'404','15','in','India',91,'Vodafone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1091,'404','88','in','India',91,'Vodafone'),
                                                                           (1092,'404','43','in','India',91,'Vodafone'),
                                                                           (1093,'404','13','in','India',91,'Vodafone'),
                                                                           (1094,'404','86','in','India',91,'Vodafone'),
                                                                           (1095,'404','30','in','India',91,'Vodafone'),
                                                                           (1096,'404','11','in','India',91,'Vodafone'),
                                                                           (1097,'404','84','in','India',91,'Vodafone'),
                                                                           (1098,'404','27','in','India',91,'Vodafone'),
                                                                           (1099,'404','05','in','India',91,'Vodafone'),
                                                                           (1100,'404','60','in','India',91,'Vodafone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1101,'404','20','in','India',91,'Vodafone'),
                                                                           (1102,'404','01','in','India',91,'Vodafone'),
                                                                           (1103,'404','46','in','India',91,'Vodafone'),
                                                                           (1104,'510','89','id','Indonesia',62,'3'),
                                                                           (1105,'510','299','id','Indonesia',62,'Failed Calls'),
                                                                           (1106,'510','999','id','Indonesia',62,'Fix Line'),
                                                                           (1107,'510','01','id','Indonesia',62,'Indosat Ooredoo'),
                                                                           (1108,'510','21','id','Indonesia',62,'Indosat Ooredoo'),
                                                                           (1109,'510','62','id','Indonesia',62,'Indosat Ooredoo'),
                                                                           (1110,'510','27','id','Indonesia',62,'Net1');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1111,'510','09','id','Indonesia',62,'Smartfren'),
                                                                           (1112,'510','28','id','Indonesia',62,'Smartfren'),
                                                                           (1113,'510','99','id','Indonesia',62,'Smartfren'),
                                                                           (1114,'510','20','id','Indonesia',62,'Telkomsel'),
                                                                           (1115,'510','10','id','Indonesia',62,'Telkomsel'),
                                                                           (1116,'510','11','id','Indonesia',62,'XL'),
                                                                           (1117,'510','08','id','Indonesia',62,'XL'),
                                                                           (1118,'901','67','n/a','International Networks',882,'1NCE'),
                                                                           (1119,'901','76','n/a','International Networks',882,'A1'),
                                                                           (1120,'901','14','n/a','International Networks',882,'AeroMobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1121,'901','299','n/a','International Networks',882,'Afinna One'),
                                                                           (1122,'901','81','n/a','International Networks',882,'Airnity'),
                                                                           (1123,'901','82','n/a','International Networks',882,'AnyNet'),
                                                                           (1124,'901','24','n/a','International Networks',882,'Bandwidth'),
                                                                           (1125,'901','55','n/a','International Networks',882,'Beezz'),
                                                                           (1126,'901','58','n/a','International Networks',882,'BICS'),
                                                                           (1127,'901','70','n/a','International Networks',882,'Bubbletone'),
                                                                           (1128,'901','18','n/a','International Networks',882,'Cingular Wireless Network'),
                                                                           (1129,'901','16','n/a','International Networks',882,'Cisco Jasper'),
                                                                           (1130,'901','48','n/a','International Networks',882,'Com4 Sweden');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1131,'901','73','n/a','International Networks',882,'Cubic'),
                                                                           (1132,'901','42','n/a','International Networks',882,'DCN Hub'),
                                                                           (1133,'901','40','n/a','International Networks',882,'Deutsche Telekom'),
                                                                           (1134,'901','299','n/a','International Networks',882,'DIDWW'),
                                                                           (1135,'901','50','n/a','International Networks',882,'EchoStar Mobile'),
                                                                           (1136,'901','299','n/a','International Networks',882,'Ellipsat'),
                                                                           (1137,'901','43','n/a','International Networks',882,'EMnify'),
                                                                           (1138,'901','299','n/a','International Networks',882,'EMSAT'),
                                                                           (1139,'901','299','n/a','International Networks',882,'Failed Calls'),
                                                                           (1140,'901','999','n/a','International Networks',882,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1141,'901','299','n/a','International Networks',882,'Global Access Services'),
                                                                           (1142,'901','299','n/a','International Networks',882,'Globalstar'),
                                                                           (1143,'901','13','n/a','International Networks',882,'GSM.AQ'),
                                                                           (1144,'901','22','n/a','International Networks',882,'i-lincc'),
                                                                           (1145,'901','11','n/a','International Networks',882,'Inmarsat'),
                                                                           (1146,'901','20','n/a','International Networks',882,'Intermatica'),
                                                                           (1147,'901','03','n/a','International Networks',882,'Iridium'),
                                                                           (1148,'901','69','n/a','International Networks',882,'Legos'),
                                                                           (1149,'901','66','n/a','International Networks',882,'Limitless'),
                                                                           (1150,'901','52','n/a','International Networks',882,'Manx Telecom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1151,'901','19','n/a','International Networks',882,'Maritime Roaming Service'),
                                                                           (1152,'901','299','n/a','International Networks',882,'MCNtelecom'),
                                                                           (1153,'901','12','n/a','International Networks',882,'MCP'),
                                                                           (1154,'901','59','n/a','International Networks',882,'MessageBird'),
                                                                           (1155,'901','27','n/a','International Networks',882,'Monaco Telecom'),
                                                                           (1156,'901','84','n/a','International Networks',882,'Movistar'),
                                                                           (1157,'901','38','n/a','International Networks',882,'MTT'),
                                                                           (1158,'901','39','n/a','International Networks',882,'MTX Connect'),
                                                                           (1159,'901','17','n/a','International Networks',882,'Navitas Telecom'),
                                                                           (1160,'901','79','n/a','International Networks',882,'Nokia');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1161,'901','85','n/a','International Networks',882,'O2'),
                                                                           (1162,'901','15','n/a','International Networks',882,'OnAir'),
                                                                           (1163,'901','41','n/a','International Networks',882,'One Network'),
                                                                           (1164,'901','47','n/a','International Networks',882,'Ooredoo'),
                                                                           (1165,'901','31','n/a','International Networks',882,'Orange'),
                                                                           (1166,'901','299','n/a','International Networks',882,'Oration Technologies'),
                                                                           (1167,'901','299','n/a','International Networks',882,'Phonegroup'),
                                                                           (1168,'901','65','n/a','International Networks',882,'Plintron'),
                                                                           (1169,'901','75','n/a','International Networks',882,'Pod'),
                                                                           (1170,'901','299','n/a','International Networks',882,'republic wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1171,'901','57','n/a','International Networks',882,'SAP'),
                                                                           (1172,'901','299','n/a','International Networks',882,'Sipme'),
                                                                           (1173,'901','33','n/a','International Networks',882,'SMART'),
                                                                           (1174,'901','78','n/a','International Networks',882,'Sparkle'),
                                                                           (1175,'901','64','n/a','International Networks',882,'Syniverse'),
                                                                           (1176,'901','72','n/a','International Networks',882,'Tele2'),
                                                                           (1177,'901','299','n/a','International Networks',882,'Telecom26'),
                                                                           (1178,'901','299','n/a','International Networks',882,'Telekom Malaysia'),
                                                                           (1179,'901','29','n/a','International Networks',882,'Telenor Connexion'),
                                                                           (1180,'901','05','n/a','International Networks',882,'Thuraya RMSS');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1181,'901','37','n/a','International Networks',882,'Transatel'),
                                                                           (1182,'901','62','n/a','International Networks',882,'Twilio'),
                                                                           (1183,'901','34','n/a','International Networks',882,'tyntec'),
                                                                           (1184,'901','51','n/a','International Networks',882,'VisionNG'),
                                                                           (1185,'901','21','n/a','International Networks',882,'Wins'),
                                                                           (1186,'901','299','n/a','International Networks',882,'Zain'),
                                                                           (1187,'432','299','ir','Iran',98,'Anarestan'),
                                                                           (1188,'432','299','ir','Iran',98,'ApTel'),
                                                                           (1189,'432','299','ir','Iran',98,'Arian Tel'),
                                                                           (1190,'432','299','ir','Iran',98,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1191,'432','999','ir','Iran',98,'Fix Line'),
                                                                           (1192,'432','299','ir','Iran',98,'LotusTel'),
                                                                           (1193,'432','11','ir','Iran',98,'MCI'),
                                                                           (1194,'432','19','ir','Iran',98,'MTCE'),
                                                                           (1195,'432','35','ir','Iran',98,'MTN IranCell'),
                                                                           (1196,'432','20','ir','Iran',98,'RighTel'),
                                                                           (1197,'432','01','ir','Iran',98,'SamanTel'),
                                                                           (1198,'432','08','ir','Iran',98,'Shatel Mobile'),
                                                                           (1199,'432','32','ir','Iran',98,'Taliya'),
                                                                           (1200,'432','14','ir','Iran',98,'Telekish');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1201,'418','05','iq','Iraq',964,'Asia Cell'),
                                                                           (1202,'418','299','iq','Iraq',964,'Failed Calls'),
                                                                           (1203,'418','999','iq','Iraq',964,'Fix Line'),
                                                                           (1204,'418','40','iq','Iraq',964,'Korek'),
                                                                           (1205,'418','45','iq','Iraq',964,'Mobitel'),
                                                                           (1206,'418','20','iq','Iraq',964,'Zain'),
                                                                           (1207,'418','30','iq','Iraq',964,'Zain'),
                                                                           (1208,'272','02','ie','Ireland',353,'3'),
                                                                           (1209,'272','17','ie','Ireland',353,'3'),
                                                                           (1210,'272','05','ie','Ireland',353,'3');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1211,'272','299','ie','Ireland',353,'BT'),
                                                                           (1212,'272','299','ie','Ireland',353,'Cubic Telecom'),
                                                                           (1213,'272','299','ie','Ireland',353,'Failed Calls'),
                                                                           (1214,'272','999','ie','Ireland',353,'Fix Line'),
                                                                           (1215,'272','13','ie','Ireland',353,'Lycamobile'),
                                                                           (1216,'272','03','ie','Ireland',353,'Meteor'),
                                                                           (1217,'272','07','ie','Ireland',353,'Meteor'),
                                                                           (1218,'272','08','ie','Ireland',353,'Meteor'),
                                                                           (1219,'272','299','ie','Ireland',353,'Net Feasa'),
                                                                           (1220,'272','299','ie','Ireland',353,'OGCIO');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1221,'272','11','ie','Ireland',353,'Tesco Mobile'),
                                                                           (1222,'272','15','ie','Ireland',353,'Virgin Media'),
                                                                           (1223,'272','01','ie','Ireland',353,'Vodafone'),
                                                                           (1224,'425','19','il','Israel',972,'019 Mobile'),
                                                                           (1225,'425','299','il','Israel',972,'Annatel Mobile'),
                                                                           (1226,'425','23','il','Israel',972,'Beezz'),
                                                                           (1227,'425','299','il','Israel',972,'Bynet'),
                                                                           (1228,'425','299','il','Israel',972,'Cellact'),
                                                                           (1229,'425','02','il','Israel',972,'Cellcom'),
                                                                           (1230,'425','299','il','Israel',972,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1231,'425','999','il','Israel',972,'Fix Line'),
                                                                           (1232,'425','08','il','Israel',972,'Golan Telecom'),
                                                                           (1233,'425','15','il','Israel',972,'Home Cellular'),
                                                                           (1234,'425','77','il','Israel',972,'Hot Mobile'),
                                                                           (1235,'425','07','il','Israel',972,'Hot Mobile'),
                                                                           (1236,'425','13','il','Israel',972,'Ituran'),
                                                                           (1237,'425','22','il','Israel',972,'Maskyoo'),
                                                                           (1238,'425','01','il','Israel',972,'Orange'),
                                                                           (1239,'425','03','il','Israel',972,'Pelephone'),
                                                                           (1240,'425','12','il','Israel',972,'Pelephone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1241,'425','16','il','Israel',972,'Rami Levy Communications'),
                                                                           (1242,'425','299','il','Israel',972,'T2T'),
                                                                           (1243,'425','17','il','Israel',972,'Von waves'),
                                                                           (1244,'425','09','il','Israel',972,'We4G'),
                                                                           (1245,'425','14','il','Israel',972,'YouPhone'),
                                                                           (1246,'222','299','it','Italy',39,'1Mobile'),
                                                                           (1247,'222','299','it','Italy',39,'A-Tono'),
                                                                           (1248,'222','40','it','Italy',39,'Agile Telecom'),
                                                                           (1249,'222','34','it','Italy',39,'BT mobile'),
                                                                           (1250,'222','299','it','Italy',39,'Esendex');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1251,'222','299','it','Italy',39,'Compatel'),
                                                                           (1252,'222','53','it','Italy',39,'CoopVoce'),
                                                                           (1253,'222','299','it','Italy',39,'Daily Telecom Mobile'),
                                                                           (1254,'222','36','it','Italy',39,'Digi'),
                                                                           (1255,'222','299','it','Italy',39,'DireQ'),
                                                                           (1256,'222','42','it','Italy',39,'Enel'),
                                                                           (1257,'222','299','it','Italy',39,'Failed Calls'),
                                                                           (1258,'222','08','it','Italy',39,'Fastweb'),
                                                                           (1259,'222','999','it','Italy',39,'Fix Line'),
                                                                           (1260,'222','99','it','Italy',39,'WindTre / Hi3G');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1261,'222','50','it','Italy',39,'Iliad'),
                                                                           (1262,'222','299','it','Italy',39,'Kaleyra'),
                                                                           (1263,'222','39','it','Italy',39,'SMS.it / LINK Mobility'),
                                                                           (1264,'222','35','it','Italy',39,'Lycamobile'),
                                                                           (1265,'222','07','it','Italy',39,'Noverca Italia'),
                                                                           (1266,'222','54','it','Italy',39,'Plintron'),
                                                                           (1267,'222','33','it','Italy',39,'Poste Mobile'),
                                                                           (1268,'222','999','it','Italy',39,'Premium Numbers'),
                                                                           (1269,'222','58','it','Italy',39,'rdcom'),
                                                                           (1270,'222','30','it','Italy',39,'RFI');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1271,'222','56','it','Italy',39,'spusu'),
                                                                           (1272,'222','01','it','Italy',39,'TIM'),
                                                                           (1273,'222','48','it','Italy',39,'Telecom Italia Mobile'),
                                                                           (1274,'222','43','it','Italy',39,'Telecom Italia Mobile'),
                                                                           (1275,'222','299','it','Italy',39,'Sparkle'),
                                                                           (1276,'222','299','it','Italy',39,'Tiscali'),
                                                                           (1277,'222','44','it','Italy',39,'Mundio'),
                                                                           (1278,'222','51','it','Italy',39,'ho.'),
                                                                           (1279,'222','49','it','Italy',39,'Vianova Mobile'),
                                                                           (1280,'222','10','it','Italy',39,'Vodafone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1281,'222','06','it','Italy',39,'Vodafone'),
                                                                           (1282,'222','299','it','Italy',39,'Vola'),
                                                                           (1283,'222','299','it','Italy',39,'Webcom'),
                                                                           (1284,'222','88','it','Italy',39,'WindTre / WIND'),
                                                                           (1285,'222','37','it','Italy',39,'WindTre / Hi3G'),
                                                                           (1286,'612','299','ci','Ivory Coast',225,'Failed Calls'),
                                                                           (1287,'612','999','ci','Ivory Coast',225,'Fix Line'),
                                                                           (1288,'612','02','ci','Ivory Coast',225,'Moov'),
                                                                           (1289,'612','05','ci','Ivory Coast',225,'MTN'),
                                                                           (1290,'612','03','ci','Ivory Coast',225,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1291,'338','050','jm','Jamaica',1876,'Digicel'),
                                                                           (1292,'338','299','jm','Jamaica',1876,'Failed Calls'),
                                                                           (1293,'338','999','jm','Jamaica',1876,'Fix Line'),
                                                                           (1294,'338','110','jm','Jamaica',1876,'Flow'),
                                                                           (1295,'338','180','jm','Jamaica',1876,'Flow'),
                                                                           (1296,'440','299','jp','Japan',81,'Failed Calls'),
                                                                           (1297,'440','999','jp','Japan',81,'Fix Line'),
                                                                           (1298,'440','75','jp','Japan',81,'KDDI'),
                                                                           (1299,'440','53','jp','Japan',81,'KDDI'),
                                                                           (1300,'440','71','jp','Japan',81,'KDDI');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1301,'440','76','jp','Japan',81,'KDDI'),
                                                                           (1302,'440','54','jp','Japan',81,'KDDI'),
                                                                           (1303,'440','72','jp','Japan',81,'KDDI'),
                                                                           (1304,'440','50','jp','Japan',81,'KDDI'),
                                                                           (1305,'440','70','jp','Japan',81,'KDDI'),
                                                                           (1306,'440','73','jp','Japan',81,'KDDI'),
                                                                           (1307,'440','51','jp','Japan',81,'KDDI'),
                                                                           (1308,'440','74','jp','Japan',81,'KDDI'),
                                                                           (1309,'440','52','jp','Japan',81,'KDDI'),
                                                                           (1310,'440','10','jp','Japan',81,'NTT DoCoMo');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1311,'440','78','jp','Japan',81,'Okinawa Cellular'),
                                                                           (1312,'440','11','jp','Japan',81,'Rakuten Mobile'),
                                                                           (1313,'440','20','jp','Japan',81,'SoftBank'),
                                                                           (1314,'440','21','jp','Japan',81,'SoftBank'),
                                                                           (1315,'440','00','jp','Japan',81,'SoftBank'),
                                                                           (1316,'440','05','jp','Japan',81,'SoftBank'),
                                                                           (1317,'440','01','jp','Japan',81,'SoftBank'),
                                                                           (1318,'416','299','jo','Jordan',962,'Failed Calls'),
                                                                           (1319,'416','999','jo','Jordan',962,'Fix Line'),
                                                                           (1320,'416','77','jo','Jordan',962,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1321,'416','03','jo','Jordan',962,'Umniah'),
                                                                           (1322,'416','01','jo','Jordan',962,'Zain'),
                                                                           (1323,'401','07','kz','Kazakhstan',7,'Altel'),
                                                                           (1324,'401','299','kz','Kazakhstan',7,'Arna'),
                                                                           (1325,'401','01','kz','Kazakhstan',7,'Beeline'),
                                                                           (1326,'401','299','kz','Kazakhstan',7,'Failed Call(s)'),
                                                                           (1327,'401','999','kz','Kazakhstan',7,'Fix Line'),
                                                                           (1328,'401','08','kz','Kazakhstan',7,'Kazakhstan Online'),
                                                                           (1329,'401','02','kz','Kazakhstan',7,'Kcell'),
                                                                           (1330,'401','77','kz','Kazakhstan',7,'Tele2');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1331,'639','03','ke','Kenya',254,'Airtel'),
                                                                           (1332,'639','05','ke','Kenya',254,'Airtel'),
                                                                           (1333,'639','299','ke','Kenya',254,'eferio'),
                                                                           (1334,'639','299','ke','Kenya',254,'Failed Calls'),
                                                                           (1335,'639','06','ke','Kenya',254,'Finserve Africa'),
                                                                           (1336,'639','999','ke','Kenya',254,'Fix Line'),
                                                                           (1337,'639','09','ke','Kenya',254,'Homeland Media'),
                                                                           (1338,'639','12','ke','Kenya',254,'Infura'),
                                                                           (1339,'639','11','ke','Kenya',254,'Jambo Telcoms'),
                                                                           (1340,'639','10','ke','Kenya',254,'Jamii Telecommunications');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1341,'639','04','ke','Kenya',254,'Mobile Pay'),
                                                                           (1342,'639','02','ke','Kenya',254,'Safaricom'),
                                                                           (1343,'639','01','ke','Kenya',254,'Safaricom'),
                                                                           (1344,'639','07','ke','Kenya',254,'Telkom'),
                                                                           (1345,'545','299','ki','Kiribati',686,'Failed Calls'),
                                                                           (1346,'545','999','ki','Kiribati',686,'Fix Line'),
                                                                           (1347,'545','01','ki','Kiribati',686,'FrigateNet'),
                                                                           (1348,'545','09','ki','Kiribati',686,'FrigateNet'),
                                                                           (1349,'545','299','ki','Kiribati',686,'Ocean Link'),
                                                                           (1350,'221','07','xk','Kosovo',383,'D3 mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1351,'221','299','xk','Kosovo',383,'Failed Calls'),
                                                                           (1352,'221','999','xk','Kosovo',383,'Fix Line'),
                                                                           (1353,'221','02','xk','Kosovo',383,'IPKO'),
                                                                           (1354,'221','299','xk','Kosovo',383,'MTS'),
                                                                           (1355,'221','01','xk','Kosovo',383,'Vala'),
                                                                           (1356,'419','299','kw','Kuwait',965,'Failed Calls'),
                                                                           (1357,'419','999','kw','Kuwait',965,'Fix Line'),
                                                                           (1358,'419','03','kw','Kuwait',965,'Ooredoo'),
                                                                           (1359,'419','299','kw','Kuwait',965,'Virgin Mobile'),
                                                                           (1360,'419','04','kw','Kuwait',965,'Viva');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1361,'419','02','kw','Kuwait',965,'Zain'),
                                                                           (1362,'437','01','kg','Kyrgyzstan',996,'Beeline'),
                                                                           (1363,'437','299','kg','Kyrgyzstan',996,'Failed Calls'),
                                                                           (1364,'437','999','kg','Kyrgyzstan',996,'Fix Line'),
                                                                           (1365,'437','02','kg','Kyrgyzstan',996,'KT Mobile'),
                                                                           (1366,'437','05','kg','Kyrgyzstan',996,'MegaCom'),
                                                                           (1367,'437','09','kg','Kyrgyzstan',996,'O!'),
                                                                           (1368,'437','10','kg','Kyrgyzstan',996,'Saima'),
                                                                           (1369,'437','03','kg','Kyrgyzstan',996,'Sem Mobile'),
                                                                           (1370,'457','08','la','Laos',856,'Beeline');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1371,'457','02','la','Laos',856,'ETL Mobile'),
                                                                           (1372,'457','299','la','Laos',856,'Failed Calls'),
                                                                           (1373,'457','999','la','Laos',856,'Fix Line'),
                                                                           (1374,'457','01','la','Laos',856,'LTC'),
                                                                           (1375,'457','03','la','Laos',856,'Unitel'),
                                                                           (1376,'247','05','lv','Latvia',371,'Bite'),
                                                                           (1377,'247','299','lv','Latvia',371,'Failed Calls'),
                                                                           (1378,'247','999','lv','Latvia',371,'Fix Line'),
                                                                           (1379,'247','01','lv','Latvia',371,'LMT'),
                                                                           (1380,'247','10','lv','Latvia',371,'LMT');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1381,'247','299','lv','Latvia',371,'Premium Numbers'),
                                                                           (1382,'247','02','lv','Latvia',371,'Tele2'),
                                                                           (1383,'247','04','lv','Latvia',371,'Tet'),
                                                                           (1384,'247','03','lv','Latvia',371,'TRIATEL'),
                                                                           (1385,'247','08','lv','Latvia',371,'VENTA Mobile'),
                                                                           (1386,'247','09','lv','Latvia',371,'XOmobile'),
                                                                           (1387,'415','01','lb','Lebanon',961,'Alfa'),
                                                                           (1388,'415','299','lb','Lebanon',961,'Failed Calls'),
                                                                           (1389,'415','999','lb','Lebanon',961,'Fix Line'),
                                                                           (1390,'415','03','lb','Lebanon',961,'Touch');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1391,'651','02','ls','Lesotho',266,'Econet'),
                                                                           (1392,'651','299','ls','Lesotho',266,'Failed Calls'),
                                                                           (1393,'651','999','ls','Lesotho',266,'Fix Line'),
                                                                           (1394,'651','01','ls','Lesotho',266,'Vodacom'),
                                                                           (1395,'618','299','lr','Liberia',231,'Failed Calls'),
                                                                           (1396,'618','999','lr','Liberia',231,'Fix Line'),
                                                                           (1397,'618','01','lr','Liberia',231,'MTN / Lonestar'),
                                                                           (1398,'618','04','lr','Liberia',231,'Novafone'),
                                                                           (1399,'618','07','lr','Liberia',231,'Orange'),
                                                                           (1400,'606','01','ly','Libya',218,'Al-Madar');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1401,'606','299','ly','Libya',218,'Failed Calls'),
                                                                           (1402,'606','999','ly','Libya',218,'Fix Line'),
                                                                           (1403,'606','00','ly','Libya',218,'Libyana'),
                                                                           (1404,'606','03','ly','Libya',218,'LibyaPhone Mobile'),
                                                                           (1405,'295','02','li','Liechtenstein',423,'7acht'),
                                                                           (1406,'295','06','li','Liechtenstein',423,'CUBIC'),
                                                                           (1407,'295','299','li','Liechtenstein',423,'Datamobile'),
                                                                           (1408,'295','299','li','Liechtenstein',423,'Dimoco'),
                                                                           (1409,'295','09','li','Liechtenstein',423,'EMnify'),
                                                                           (1410,'295','299','li','Liechtenstein',423,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1411,'295','999','li','Liechtenstein',423,'Fix Line'),
                                                                           (1412,'246','999','li','Liechtenstein',423,'Fix Line'),
                                                                           (1413,'295','01','li','Liechtenstein',423,'FL GSM'),
                                                                           (1414,'295','05','li','Liechtenstein',423,'FL1'),
                                                                           (1415,'295','299','li','Liechtenstein',423,'SORACOM'),
                                                                           (1416,'295','299','li','Liechtenstein',423,'Telna'),
                                                                           (1417,'246','02','lt','Lithuania',370,'Bite'),
                                                                           (1418,'246','299','lt','Lithuania',370,'Failed Calls'),
                                                                           (1419,'246','05','lt','Lithuania',370,'LTG'),
                                                                           (1420,'246','06','lt','Lithuania',370,'Mediafon');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1421,'246','299','lt','Lithuania',370,'SkyCall'),
                                                                           (1422,'246','03','lt','Lithuania',370,'Tele2'),
                                                                           (1423,'246','299','lt','Lithuania',370,'Teletel'),
                                                                           (1424,'246','01','lt','Lithuania',370,'Telia'),
                                                                           (1425,'270','10','lu','Luxembourg',352,'Blue Communications'),
                                                                           (1426,'270','299','lu','Luxembourg',352,'Bouygues Telecom'),
                                                                           (1427,'270','81','lu','Luxembourg',352,'e-LUX Mobile'),
                                                                           (1428,'270','299','lu','Luxembourg',352,'Eltrona'),
                                                                           (1429,'270','299','lu','Luxembourg',352,'Failed Calls'),
                                                                           (1430,'270','999','lu','Luxembourg',352,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1431,'270','05','lu','Luxembourg',352,'Luxembourg Online'),
                                                                           (1432,'270','299','lu','Luxembourg',352,'MTX Connect'),
                                                                           (1433,'270','99','lu','Luxembourg',352,'Orange'),
                                                                           (1434,'270','01','lu','Luxembourg',352,'Post'),
                                                                           (1435,'270','77','lu','Luxembourg',352,'Tango'),
                                                                           (1436,'455','05','mo','Macao',853,'3'),
                                                                           (1437,'455','03','mo','Macao',853,'3'),
                                                                           (1438,'455','07','mo','Macao',853,'China Telecom'),
                                                                           (1439,'455','02','mo','Macao',853,'China Telecom'),
                                                                           (1440,'455','01','mo','Macao',853,'CTM');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1441,'455','04','mo','Macao',853,'CTM'),
                                                                           (1442,'455','299','mo','Macao',853,'Failed Calls'),
                                                                           (1443,'455','999','mo','Macao',853,'Fix Line'),
                                                                           (1444,'455','06','mo','Macao',853,'SmarTone'),
                                                                           (1445,'455','00','mo','Macao',853,'SmarTone'),
                                                                           (1446,'646','01','mg','Madagascar',261,'Airtel'),
                                                                           (1447,'646','299','mg','Madagascar',261,'Bip'),
                                                                           (1448,'646','299','mg','Madagascar',261,'Failed Calls'),
                                                                           (1449,'646','999','mg','Madagascar',261,'Fix Line'),
                                                                           (1450,'646','02','mg','Madagascar',261,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1451,'646','04','mg','Madagascar',261,'Telma'),
                                                                           (1452,'650','10','mw','Malawi',265,'Airtel'),
                                                                           (1453,'650','299','mw','Malawi',265,'Failed Calls'),
                                                                           (1454,'650','999','mw','Malawi',265,'Fix Line'),
                                                                           (1455,'650','01','mw','Malawi',265,'TNM'),
                                                                           (1456,'502','156','my','Malaysia',60,'Altel Communications'),
                                                                           (1457,'502','14','my','Malaysia',60,'Telekom Malaysia'),
                                                                           (1458,'502','11','my','Malaysia',60,'Telekom Malaysia'),
                                                                           (1459,'502','19','my','Malaysia',60,'Celcom'),
                                                                           (1460,'502','13','my','Malaysia',60,'Celcom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1461,'502','10','my','Malaysia',60,'DiGi'),
                                                                           (1462,'502','16','my','Malaysia',60,'DiGi'),
                                                                           (1463,'502','299','my','Malaysia',60,'Failed Calls'),
                                                                           (1464,'502','999','my','Malaysia',60,'Fix Line'),
                                                                           (1465,'502','299','my','Malaysia',60,'MKN'),
                                                                           (1466,'502','17','my','Malaysia',60,'Maxis'),
                                                                           (1467,'502','12','my','Malaysia',60,'Maxis'),
                                                                           (1468,'502','299','my','Malaysia',60,'Maxis Broadband'),
                                                                           (1469,'502','299','my','Malaysia',60,'OCESB'),
                                                                           (1470,'502','299','my','Malaysia',60,'REDtone Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1471,'502','299','my','Malaysia',60,'REDtone'),
                                                                           (1472,'502','299','my','Malaysia',60,'TT dotCom'),
                                                                           (1473,'502','150','my','Malaysia',60,'Tune Talk'),
                                                                           (1474,'502','18','my','Malaysia',60,'U Mobile'),
                                                                           (1475,'502','153','my','Malaysia',60,'Webe Digital'),
                                                                           (1476,'502','195','my','Malaysia',60,'XOX Com'),
                                                                           (1477,'502','299','my','Malaysia',60,'Y-Max'),
                                                                           (1478,'502','152','my','Malaysia',60,'Yes'),
                                                                           (1479,'472','01','mv','Maldives',960,'DhiMobile'),
                                                                           (1480,'472','299','mv','Maldives',960,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1481,'472','999','mv','Maldives',960,'Fix Line'),
                                                                           (1482,'472','02','mv','Maldives',960,'Ooredoo'),
                                                                           (1483,'610','299','ml','Mali',223,'Failed Calls'),
                                                                           (1484,'610','999','ml','Mali',223,'Fix Line'),
                                                                           (1485,'610','01','ml','Mali',223,'Malitel'),
                                                                           (1486,'610','02','ml','Mali',223,'Orange'),
                                                                           (1487,'610','03','ml','Mali',223,'Telecel'),
                                                                           (1488,'278','01','mt','Malta',356,'Vodafone'),
                                                                           (1489,'278','299','mt','Malta',356,'Failed Calls'),
                                                                           (1490,'278','999','mt','Malta',356,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1491,'278','30','mt','Malta',356,'GO Mobile'),
                                                                           (1492,'278','21','mt','Malta',356,'GO Mobile'),
                                                                           (1493,'278','77','mt','Malta',356,'Melita'),
                                                                           (1494,'551','299','mh','Marshall Islands',692,'Failed Calls'),
                                                                           (1495,'551','999','mh','Marshall Islands',692,'Fix Line'),
                                                                           (1496,'551','299','mh','Marshall Islands',692,'MINTA'),
                                                                           (1497,'609','02','mr','Mauritania',222,'Chinguitel'),
                                                                           (1498,'609','299','mr','Mauritania',222,'Failed Calls'),
                                                                           (1499,'609','999','mr','Mauritania',222,'Fix Line'),
                                                                           (1500,'609','01','mr','Mauritania',222,'Mattel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1501,'609','10','mr','Mauritania',222,'Mauritel'),
                                                                           (1502,'617','03','mu','Mauritius',230,'Chili'),
                                                                           (1503,'617','02','mu','Mauritius',230,'Chili'),
                                                                           (1504,'617','10','mu','Mauritius',230,'Emtel'),
                                                                           (1505,'617','299','mu','Mauritius',230,'Failed Calls'),
                                                                           (1506,'617','999','mu','Mauritius',230,'Fix Line'),
                                                                           (1507,'617','01','mu','Mauritius',230,'my.t mobile'),
                                                                           (1508,'647','299','yt','Mayotte',262,'Failed Calls'),
                                                                           (1509,'647','999','yt','Mayotte',262,'Fix Line'),
                                                                           (1510,'647','01','yt','Mayotte',262,'Maore Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1511,'647','10','yt','Mayotte',262,'SFR'),
                                                                           (1512,'334','140','mx','Mexico',52,'ALTAN Redes'),
                                                                           (1513,'334','299','mx','Mexico',52,'Altcel'),
                                                                           (1514,'334','050','mx','Mexico',52,'AT&T / IUSACell'),
                                                                           (1515,'334','299','mx','Mexico',52,'Failed Call(s)'),
                                                                           (1516,'334','999','mx','Mexico',52,'Fix Line'),
                                                                           (1517,'334','299','mx','Mexico',52,'FreedomPop'),
                                                                           (1518,'334','299','mx','Mexico',52,'Ibo Cell'),
                                                                           (1519,'334','299','mx','Mexico',52,'Maxcom'),
                                                                           (1520,'334','299','mx','Mexico',52,'MegaTel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1521,'334','03','mx','Mexico',52,'Movistar'),
                                                                           (1522,'334','030','mx','Mexico',52,'Movistar'),
                                                                           (1523,'334','299','mx','Mexico',52,'NEUS Mobile'),
                                                                           (1524,'334','01','mx','Mexico',52,'Nextel'),
                                                                           (1525,'334','090','mx','Mexico',52,'Nextel'),
                                                                           (1526,'334','010','mx','Mexico',52,'Nextel'),
                                                                           (1527,'334','299','mx','Mexico',52,'QBOcel'),
                                                                           (1528,'334','060','mx','Mexico',52,'SAI PCS'),
                                                                           (1529,'334','299','mx','Mexico',52,'Servitron'),
                                                                           (1530,'334','020','mx','Mexico',52,'Telcel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1531,'334','02','mx','Mexico',52,'Telcel'),
                                                                           (1532,'334','299','mx','Mexico',52,'Telecomunicaciones 360'),
                                                                           (1533,'334','080','mx','Mexico',52,'Unefon'),
                                                                           (1534,'334','070','mx','Mexico',52,'Unefon'),
                                                                           (1535,'550','299','fm','Micronesia',691,'Failed Calls'),
                                                                           (1536,'550','999','fm','Micronesia',691,'Fix Line'),
                                                                           (1537,'550','01','fm','Micronesia',691,'FSMTCeLL'),
                                                                           (1538,'259','299','md','Moldova',373,'Failed Calls'),
                                                                           (1539,'259','02','md','Moldova',373,'Moldcell'),
                                                                           (1540,'259','01','md','Moldova',373,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1541,'259','03','md','Moldova',373,'Unite'),
                                                                           (1542,'259','99','md','Moldova',373,'Unite'),
                                                                           (1543,'259','05','md','Moldova',373,'Unite'),
                                                                           (1544,'212','299','mc','Monaco',377,'Failed Calls'),
                                                                           (1545,'212','999','mc','Monaco',377,'Fix Line'),
                                                                           (1546,'212','10','mc','Monaco',377,'Monaco Telecom'),
                                                                           (1547,'212','01','mc','Monaco',377,'Monaco Telecom'),
                                                                           (1548,'428','299','mn','Mongolia',976,'Failed Calls'),
                                                                           (1549,'428','999','mn','Mongolia',976,'Fix Line'),
                                                                           (1550,'428','98','mn','Mongolia',976,'G-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1551,'428','99','mn','Mongolia',976,'Mobicom'),
                                                                           (1552,'428','00','mn','Mongolia',976,'Skytel'),
                                                                           (1553,'428','88','mn','Mongolia',976,'Unitel'),
                                                                           (1554,'297','299','me','Montenegro',382,'Failed Calls'),
                                                                           (1555,'297','999','me','Montenegro',382,'Fix Line'),
                                                                           (1556,'297','03','me','Montenegro',382,'Mtel'),
                                                                           (1557,'297','02','me','Montenegro',382,'Telekom / T-mobile'),
                                                                           (1558,'297','01','me','Montenegro',382,'Telenor'),
                                                                           (1559,'354','299','ms','Montserrat',1664,'Digicel'),
                                                                           (1560,'354','299','ms','Montserrat',1664,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1561,'354','999','ms','Montserrat',1664,'Fix Line'),
                                                                           (1562,'354','860','ms','Montserrat',1664,'Flow'),
                                                                           (1563,'604','299','ma','Morocco',212,'Failed Calls'),
                                                                           (1564,'604','999','ma','Morocco',212,'Fix Line'),
                                                                           (1565,'604','01','ma','Morocco',212,'IAM'),
                                                                           (1566,'604','02','ma','Morocco',212,'inwi'),
                                                                           (1567,'604','05','ma','Morocco',212,'inwi'),
                                                                           (1568,'604','00','ma','Morocco',212,'Orange'),
                                                                           (1569,'643','299','mz','Mozambique',258,'Failed Calls'),
                                                                           (1570,'643','999','mz','Mozambique',258,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1571,'643','03','mz','Mozambique',258,'Movitel'),
                                                                           (1572,'643','01','mz','Mozambique',258,'TMCEL'),
                                                                           (1573,'643','04','mz','Mozambique',258,'Vodacom'),
                                                                           (1574,'414','999','mm','Myanmar',95,'Failed Calls'),
                                                                           (1575,'414','999','mm','Myanmar',95,'Fix Line'),
                                                                           (1576,'414','03','mm','Myanmar',95,'MecTel'),
                                                                           (1577,'414','01','mm','Myanmar',95,'MPT'),
                                                                           (1578,'414','00','mm','Myanmar',95,'MPT'),
                                                                           (1579,'414','09','mm','Myanmar',95,'Mytel'),
                                                                           (1580,'414','05','mm','Myanmar',95,'Ooredoo');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1581,'414','06','mm','Myanmar',95,'Telenor'),
                                                                           (1582,'649','299','na','Namibia',264,'Demshi'),
                                                                           (1583,'649','299','na','Namibia',264,'Failed Calls'),
                                                                           (1584,'649','999','na','Namibia',264,'Fix Line'),
                                                                           (1585,'649','01','na','Namibia',264,'MTC'),
                                                                           (1586,'649','03','na','Namibia',264,'TN Mobile'),
                                                                           (1587,'536','02','nr','Nauru',674,'Digicel'),
                                                                           (1588,'536','299','nr','Nauru',674,'Failed Calls'),
                                                                           (1589,'536','999','nr','Nauru',674,'Fix Line'),
                                                                           (1590,'429','299','np','Nepal',977,'CG Telecom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1591,'429','299','np','Nepal',977,'Failed Calls'),
                                                                           (1592,'429','999','np','Nepal',977,'Fix Line'),
                                                                           (1593,'429','03','np','Nepal',977,'Hello Nepal'),
                                                                           (1594,'429','02','np','Nepal',977,'Ncell'),
                                                                           (1595,'429','01','np','Nepal',977,'Nepal Telecom'),
                                                                           (1596,'429','04','np','Nepal',977,'Smart'),
                                                                           (1597,'429','00','np','Nepal',977,'UTL'),
                                                                           (1598,'204','299','nl','Netherlands',31,'88 mobile'),
                                                                           (1599,'204','299','nl','Netherlands',31,'AGMS'),
                                                                           (1600,'204','30','nl','Netherlands',31,'ASPIDER Solutions');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1601,'204','299','nl','Netherlands',31,'Belcentrale'),
                                                                           (1602,'204','299','nl','Netherlands',31,'BodyTrace'),
                                                                           (1603,'204','299','nl','Netherlands',31,'Combird Mobile'),
                                                                           (1604,'204','299','nl','Netherlands',31,'Dean Mobile'),
                                                                           (1605,'204','299','nl','Netherlands',31,'Eazit'),
                                                                           (1606,'204','05','nl','Netherlands',31,'ElephantTalk'),
                                                                           (1607,'204','299','nl','Netherlands',31,'EziMobile'),
                                                                           (1608,'204','299','nl','Netherlands',31,'Failed Calls'),
                                                                           (1609,'204','999','nl','Netherlands',31,'Fix Line'),
                                                                           (1610,'204','999','nl','Netherlands',31,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1611,'204','299','nl','Netherlands',31,'interactive digital media / IDM'),
                                                                           (1612,'204','00','nl','Netherlands',31,'Intovoice'),
                                                                           (1613,'204','299','nl','Netherlands',31,'KeenMobile'),
                                                                           (1614,'204','23','nl','Netherlands',31,'KORE'),
                                                                           (1615,'204','12','nl','Netherlands',31,'KPN'),
                                                                           (1616,'204','08','nl','Netherlands',31,'KPN'),
                                                                           (1617,'204','10','nl','Netherlands',31,'KPN'),
                                                                           (1618,'204','69','nl','Netherlands',31,'KPN'),
                                                                           (1619,'204','27','nl','Netherlands',31,'L-mobi'),
                                                                           (1620,'204','28','nl','Netherlands',31,'Lancelot');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1621,'204','98','nl','Netherlands',31,'Lancelot'),
                                                                           (1622,'204','09','nl','Netherlands',31,'Lycamobile'),
                                                                           (1623,'204','63','nl','Netherlands',31,'MessageBird'),
                                                                           (1624,'204','299','nl','Netherlands',31,'mGage'),
                                                                           (1625,'204','299','nl','Netherlands',31,'Motto'),
                                                                           (1626,'204','07','nl','Netherlands',31,'Move / Teleena'),
                                                                           (1627,'204','299','nl','Netherlands',31,'Okta8'),
                                                                           (1628,'204','299','nl','Netherlands',31,'Premium Numbers'),
                                                                           (1629,'204','299','nl','Netherlands',31,'Premium Routing'),
                                                                           (1630,'204','24','nl','Netherlands',31,'Private Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1631,'204','21','nl','Netherlands',31,'ProRail'),
                                                                           (1632,'204','299','nl','Netherlands',31,'Redworks'),
                                                                           (1633,'204','26','nl','Netherlands',31,'SpeakUp'),
                                                                           (1634,'204','02','nl','Netherlands',31,'T-Mobile'),
                                                                           (1635,'204','20','nl','Netherlands',31,'T-Mobile'),
                                                                           (1636,'204','16','nl','Netherlands',31,'T-Mobile'),
                                                                           (1637,'204','29','nl','Netherlands',31,'Tismi'),
                                                                           (1638,'204','33','nl','Netherlands',31,'Truphone'),
                                                                           (1639,'204','299','nl','Netherlands',31,'Vectone Mobile'),
                                                                           (1640,'204','04','nl','Netherlands',31,'Vodafone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1641,'204','03','nl','Netherlands',31,'Voiceworks Mobile'),
                                                                           (1642,'204','15','nl','Netherlands',31,'Ziggo'),
                                                                           (1643,'204','18','nl','Netherlands',31,'Ziggo Services'),
                                                                           (1644,'362','91','an','Netherlands Antilles',599,'Chippie'),
                                                                           (1645,'362','69','an','Netherlands Antilles',599,'Digicel'),
                                                                           (1646,'362','299','an','Netherlands Antilles',599,'Failed Calls'),
                                                                           (1647,'362','999','an','Netherlands Antilles',599,'Fix Line'),
                                                                           (1648,'362','299','an','Netherlands Antilles',599,'Premium Numbers'),
                                                                           (1649,'362','51','an','Netherlands Antilles',599,'TelCell'),
                                                                           (1650,'546','299','nc','New Caledonia',687,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1651,'546','999','nc','New Caledonia',687,'Fix Line'),
                                                                           (1652,'546','01','nc','New Caledonia',687,'Mobilis'),
                                                                           (1653,'530','24','nz','New Zealand',64,'2degrees'),
                                                                           (1654,'530','299','nz','New Zealand',64,'Compass Mobile'),
                                                                           (1655,'530','299','nz','New Zealand',64,'Devoli'),
                                                                           (1656,'530','299','nz','New Zealand',64,'Failed Calls'),
                                                                           (1657,'530','999','nz','New Zealand',64,'Fix Line'),
                                                                           (1658,'530','299','nz','New Zealand',64,'LinkTel'),
                                                                           (1659,'530','06','nz','New Zealand',64,'Skinny Mobile'),
                                                                           (1660,'530','05','nz','New Zealand',64,'Spark Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1661,'530','02','nz','New Zealand',64,'Spark Mobile'),
                                                                           (1662,'530','299','nz','New Zealand',64,'Symbio'),
                                                                           (1663,'530','299','nz','New Zealand',64,'Vocus'),
                                                                           (1664,'530','01','nz','New Zealand',64,'Vodafone'),
                                                                           (1665,'530','299','nz','New Zealand',64,'Voxbone / Bandwidth'),
                                                                           (1666,'530','299','nz','New Zealand',64,'Voyager Internet'),
                                                                           (1667,'530','299','nz','New Zealand',64,'WXC'),
                                                                           (1668,'710','21','ni','Nicaragua',505,'Claro'),
                                                                           (1669,'710','73','ni','Nicaragua',505,'Claro'),
                                                                           (1670,'710','299','ni','Nicaragua',505,'CooTel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1671,'710','299','ni','Nicaragua',505,'Failed Calls'),
                                                                           (1672,'710','999','ni','Nicaragua',505,'Fix Line'),
                                                                           (1673,'710','30','ni','Nicaragua',505,'Movistar'),
                                                                           (1674,'614','02','ne','Niger',227,'Airtel'),
                                                                           (1675,'614','299','ne','Niger',227,'Failed Calls'),
                                                                           (1676,'614','999','ne','Niger',227,'Fix Line'),
                                                                           (1677,'614','03','ne','Niger',227,'Moov'),
                                                                           (1678,'614','01','ne','Niger',227,'Niger Telecoms'),
                                                                           (1679,'614','04','ne','Niger',227,'Orange'),
                                                                           (1680,'621','60','ng','Nigeria',234,'9mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1681,'621','20','ng','Nigeria',234,'Airtel'),
                                                                           (1682,'621','299','ng','Nigeria',234,'Alpha Technologies'),
                                                                           (1683,'621','299','ng','Nigeria',234,'Failed Calls'),
                                                                           (1684,'621','999','ng','Nigeria',234,'Fix Line'),
                                                                           (1685,'621','50','ng','Nigeria',234,'Glo Mobile'),
                                                                           (1686,'621','30','ng','Nigeria',234,'MTN'),
                                                                           (1687,'621','40','ng','Nigeria',234,'ntel'),
                                                                           (1688,'621','27','ng','Nigeria',234,'Smile'),
                                                                           (1689,'621','299','ng','Nigeria',234,'Zodafones'),
                                                                           (1690,'555','299','nu','Niue',683,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1691,'555','999','nu','Niue',683,'Fix Line'),
                                                                           (1692,'555','01','nu','Niue',683,'Telecom Niue'),
                                                                           (1693,'467','299','kp','North Korea',850,'Failed Calls'),
                                                                           (1694,'467','999','kp','North Korea',850,'Fix Line'),
                                                                           (1695,'467','299','kp','North Korea',850,'Kangsung Net'),
                                                                           (1696,'467','192','kp','North Korea',850,'Koryolink'),
                                                                           (1697,'294','03','mk','North Macedonia',389,'A1'),
                                                                           (1698,'294','02','mk','North Macedonia',389,'A1'),
                                                                           (1699,'294','299','mk','North Macedonia',389,'Failed Calls'),
                                                                           (1700,'259','999','mk','North Macedonia',389,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1701,'294','999','mk','North Macedonia',389,'Fix Line'),
                                                                           (1702,'294','299','mk','North Macedonia',389,'LATRON'),
                                                                           (1703,'294','04','mk','North Macedonia',389,'Lycamobile'),
                                                                           (1704,'294','11','mk','North Macedonia',389,'Mobik'),
                                                                           (1705,'294','299','mk','North Macedonia',389,'Telekabel'),
                                                                           (1706,'294','01','mk','North Macedonia',389,'Telekom'),
                                                                           (1707,'534','299','mp','Northern Mariana Islands',1670,'Failed Calls'),
                                                                           (1708,'534','999','mp','Northern Mariana Islands',1670,'Fix Line'),
                                                                           (1709,'242','22','no','Norway',47,'Altibox Mobil'),
                                                                           (1710,'242','21','no','Norway',47,'BANE NOR');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1711,'242','20','no','Norway',47,'BANE NOR'),
                                                                           (1712,'242','299','no','Norway',47,'bigblu'),
                                                                           (1713,'242','299','no','Norway',47,'Chilimobil'),
                                                                           (1714,'242','09','no','Norway',47,'Com4'),
                                                                           (1715,'242','15','no','Norway',47,'eRate'),
                                                                           (1716,'242','299','no','Norway',47,'Failed Calls'),
                                                                           (1717,'242','999','no','Norway',47,'Fix Line'),
                                                                           (1718,'242','299','no','Norway',47,'GlobalConnect'),
                                                                           (1719,'242','299','no','Norway',47,''),
                                                                           (1720,'242','14','no','Norway',47,'ICE');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1721,'242','299','no','Norway',47,'intility'),
                                                                           (1722,'242','16','no','Norway',47,'Iristel'),
                                                                           (1723,'242','299','no','Norway',47,'JetNett'),
                                                                           (1724,'242','23','no','Norway',47,'Lycamobile'),
                                                                           (1725,'242','05','no','Norway',47,'Network Norway'),
                                                                           (1726,'242','299','no','Norway',47,'NextGenTel'),
                                                                           (1727,'242','10','no','Norway',47,'Nkom'),
                                                                           (1728,'242','299','no','Norway',47,'Nodnett'),
                                                                           (1729,'242','06','no','Norway',47,'ICE'),
                                                                           (1730,'242','299','no','Norway',47,'Puzzel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1731,'242','299','no','Norway',47,'Sierra Wireless'),
                                                                           (1732,'242','299','no','Norway',47,'Svea'),
                                                                           (1733,'242','299','no','Norway',47,'Telavox'),
                                                                           (1734,'242','12','no','Norway',47,'Telenor'),
                                                                           (1735,'242','01','no','Norway',47,'Telenor'),
                                                                           (1736,'242','08','no','Norway',47,'Telia / NetCom'),
                                                                           (1737,'242','02','no','Norway',47,'Telia / NetCom'),
                                                                           (1738,'242','299','no','Norway',47,'unifon'),
                                                                           (1739,'422','299','om','Oman',968,'Failed Calls'),
                                                                           (1740,'422','999','om','Oman',968,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1741,'422','02','om','Oman',968,'Omantel Mobile'),
                                                                           (1742,'422','03','om','Oman',968,'Ooredoo / Nawras'),
                                                                           (1743,'410','299','pk','Pakistan',92,'Failed Calls'),
                                                                           (1744,'410','999','pk','Pakistan',92,'Fix Line'),
                                                                           (1745,'410','07','pk','Pakistan',92,'Jazz'),
                                                                           (1746,'410','01','pk','Pakistan',92,'Jazz'),
                                                                           (1747,'410','05','pk','Pakistan',92,'SCOM'),
                                                                           (1748,'410','06','pk','Pakistan',92,'Telenor'),
                                                                           (1749,'410','03','pk','Pakistan',92,'Ufone'),
                                                                           (1750,'410','299','pk','Pakistan',92,'Warid');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1751,'410','04','pk','Pakistan',92,'Zong'),
                                                                           (1752,'552','299','pw','Palau',680,'Failed Calls'),
                                                                           (1753,'552','999','pw','Palau',680,'Fix Line'),
                                                                           (1754,'552','99','pw','Palau',680,'Palau Mobile'),
                                                                           (1755,'552','01','pw','Palau',680,'PalauCel'),
                                                                           (1756,'552','02','pw','Palau',680,'PT Waves'),
                                                                           (1757,'425','299','ps','Palestinian Territory',970,'Failed Calls'),
                                                                           (1758,'425','999','ps','Palestinian Territory',970,'Fix Line'),
                                                                           (1759,'425','05','ps','Palestinian Territory',970,'Jawwal'),
                                                                           (1760,'425','06','ps','Palestinian Territory',970,'Ooredoo');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1761,'714','01','pa','Panama',507,'+Movil'),
                                                                           (1762,'714','03','pa','Panama',507,'Claro'),
                                                                           (1763,'714','04','pa','Panama',507,'Digicel'),
                                                                           (1764,'714','299','pa','Panama',507,'Failed Calls'),
                                                                           (1765,'714','999','pa','Panama',507,'Fix Line'),
                                                                           (1766,'714','02','pa','Panama',507,'Movistar'),
                                                                           (1767,'714','020','pa','Panama',507,'Movistar'),
                                                                           (1768,'537','01','pg','Papua New Guinea',675,'BeMobile Vodafone'),
                                                                           (1769,'537','02','pg','Papua New Guinea',675,'Citifon'),
                                                                           (1770,'537','03','pg','Papua New Guinea',675,'Digicel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1771,'537','299','pg','Papua New Guinea',675,'DIGIVOIP'),
                                                                           (1772,'537','299','pg','Papua New Guinea',675,'Failed Calls'),
                                                                           (1773,'537','999','pg','Papua New Guinea',675,'Fix Line'),
                                                                           (1774,'744','02','py','Paraguay',595,'Claro'),
                                                                           (1775,'744','299','py','Paraguay',595,'Failed Calls'),
                                                                           (1776,'744','999','py','Paraguay',595,'Fix Line'),
                                                                           (1777,'744','03','py','Paraguay',595,'Personal'),
                                                                           (1778,'744','04','py','Paraguay',595,'Tigo'),
                                                                           (1779,'744','01','py','Paraguay',595,'VOX'),
                                                                           (1780,'716','70','pe','Peru',51,'Claro');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1781,'716','10','pe','Peru',51,'Claro'),
                                                                           (1782,'716','20','pe','Peru',51,'Claro'),
                                                                           (1783,'716','299','pe','Peru',51,'Dolphin'),
                                                                           (1784,'716','17','pe','Peru',51,'Entel'),
                                                                           (1785,'716','299','pe','Peru',51,'Failed Calls'),
                                                                           (1786,'716','999','pe','Peru',51,'Fix Line'),
                                                                           (1787,'716','06','pe','Peru',51,'Movistar'),
                                                                           (1788,'716','07','pe','Peru',51,'Entel'),
                                                                           (1789,'716','30','pe','Peru',51,'Tuyo'),
                                                                           (1790,'716','15','pe','Peru',51,'Viettel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1791,'515','66','ph','Philippines',63,'DITO'),
                                                                           (1792,'515','299','ph','Philippines',63,'Failed Calls'),
                                                                           (1793,'515','999','ph','Philippines',63,'Fix Line'),
                                                                           (1794,'515','02','ph','Philippines',63,'Globe'),
                                                                           (1795,'515','01','ph','Philippines',63,'Globe'),
                                                                           (1796,'515','03','ph','Philippines',63,'Smart'),
                                                                           (1797,'515','05','ph','Philippines',63,'Sun Cellular'),
                                                                           (1798,'260','299','pl','Poland',48,'3S'),
                                                                           (1799,'260','17','pl','Poland',48,'Aero2'),
                                                                           (1800,'260','16','pl','Poland',48,'Aero2');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1801,'260','15','pl','Poland',48,'Aero2'),
                                                                           (1802,'260','04','pl','Poland',48,'Aero2'),
                                                                           (1803,'260','48','pl','Poland',48,'Agile Telecom'),
                                                                           (1804,'260','18','pl','Poland',48,'AMD Telecom'),
                                                                           (1805,'260','299','pl','Poland',48,'Benemen'),
                                                                           (1806,'260','299','pl','Poland',48,'BSG'),
                                                                           (1807,'260','299','pl','Poland',48,'Caritas Laczy'),
                                                                           (1808,'260','299','pl','Poland',48,'Cludo'),
                                                                           (1809,'260','32','pl','Poland',48,'Compatel'),
                                                                           (1810,'260','12','pl','Poland',48,'Cyfrowy Polsat');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1811,'260','299','pl','Poland',48,'e-Telko'),
                                                                           (1812,'260','41','pl','Poland',48,'EZ Mobile'),
                                                                           (1813,'260','299','pl','Poland',48,'Failed Calls'),
                                                                           (1814,'260','999','pl','Poland',48,'Fix Line'),
                                                                           (1815,'260','299','pl','Poland',48,'I.M. Consulting'),
                                                                           (1816,'260','299','pl','Poland',48,'Inea'),
                                                                           (1817,'260','299','pl','Poland',48,'IZZI'),
                                                                           (1818,'260','299','pl','Poland',48,'JMDI J. Maleszko'),
                                                                           (1819,'260','299','pl','Poland',48,'Klucz Mobile'),
                                                                           (1820,'260','299','pl','Poland',48,'lajt mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1821,'260','299','pl','Poland',48,'LoVo'),
                                                                           (1822,'260','09','pl','Poland',48,'Lycamobile'),
                                                                           (1823,'260','49','pl','Poland',48,'Messagebird'),
                                                                           (1824,'260','299','pl','Poland',48,'Metro Mobile'),
                                                                           (1825,'260','299','pl','Poland',48,'Mobile Vikings'),
                                                                           (1826,'260','299','pl','Poland',48,'Mobiledata'),
                                                                           (1827,'260','42','pl','Poland',48,'MobiWeb'),
                                                                           (1828,'260','299','pl','Poland',48,'Moja GSM'),
                                                                           (1829,'260','13','pl','Poland',48,'Move'),
                                                                           (1830,'260','299','pl','Poland',48,'multiMOBILE');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1831,'260','299','pl','Poland',48,'Nasza Wizja'),
                                                                           (1832,'260','299','pl','Poland',48,'NAU Mobile'),
                                                                           (1833,'260','299','pl','Poland',48,'nc+ Mobile'),
                                                                           (1834,'260','19','pl','Poland',48,'NetBalt'),
                                                                           (1835,'260','07','pl','Poland',48,'Netia'),
                                                                           (1836,'260','299','pl','Poland',48,'Next Mobile'),
                                                                           (1837,'260','299','pl','Poland',48,'NIMBOW'),
                                                                           (1838,'260','27','pl','Poland',48,'Ntel Solutions'),
                                                                           (1839,'260','05','pl','Poland',48,'Orange'),
                                                                           (1840,'260','03','pl','Poland',48,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1841,'260','35','pl','Poland',48,'PKP'),
                                                                           (1842,'260','98','pl','Poland',48,'Play'),
                                                                           (1843,'260','06','pl','Poland',48,'Play'),
                                                                           (1844,'260','11','pl','Poland',48,'Plus'),
                                                                           (1845,'260','01','pl','Poland',48,'Plus'),
                                                                           (1846,'260','97','pl','Poland',48,'Politechnika Lodzka Uczelniane'),
                                                                           (1847,'260','90','pl','Poland',48,'Polska Spolka Gazownictwa'),
                                                                           (1848,'260','299','pl','Poland',48,'Polvoice'),
                                                                           (1849,'260','299','pl','Poland',48,'Pomagacz'),
                                                                           (1850,'260','299','pl','Poland',48,'Premium Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1851,'260','299','pl','Poland',48,'Premium Numbers'),
                                                                           (1852,'260','299','pl','Poland',48,'SAT FILM'),
                                                                           (1853,'260','14','pl','Poland',48,'Move'),
                                                                           (1854,'260','299','pl','Poland',48,'SGT'),
                                                                           (1855,'260','47','pl','Poland',48,'SMSHIGHWAY'),
                                                                           (1856,'260','299','pl','Poland',48,'Softelnet'),
                                                                           (1857,'260','34','pl','Poland',48,'T-Mobile'),
                                                                           (1858,'260','02','pl','Poland',48,'T-Mobile'),
                                                                           (1859,'260','10','pl','Poland',48,'T-Mobile'),
                                                                           (1860,'260','14','pl','Poland',48,'Telco Leaders');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1861,'260','299','pl','Poland',48,'Tele GO'),
                                                                           (1862,'260','299','pl','Poland',48,'TeleCube'),
                                                                           (1863,'260','299','pl','Poland',48,'Telenabler'),
                                                                           (1864,'260','299','pl','Poland',48,'TELGAM'),
                                                                           (1865,'260','20','pl','Poland',48,'Tismi'),
                                                                           (1866,'260','299','pl','Poland',48,'TOYAmobilna'),
                                                                           (1867,'260','22','pl','Poland',48,'Twilio'),
                                                                           (1868,'260','299','pl','Poland',48,'UPC'),
                                                                           (1869,'260','299','pl','Poland',48,'Vectra'),
                                                                           (1870,'260','45','pl','Poland',48,'Virgin Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1871,'260','45','pl','Poland',48,'Virgin Mobile'),
                                                                           (1872,'260','299','pl','Poland',48,'Vonage'),
                                                                           (1873,'260','39','pl','Poland',48,'Voxbone / Bandwidth'),
                                                                           (1874,'260','299','pl','Poland',48,'w naszej Rodzinie'),
                                                                           (1875,'268','299','pt','Portugal',351,'Failed Calls'),
                                                                           (1876,'268','999','pt','Portugal',351,'Fix Line'),
                                                                           (1877,'268','04','pt','Portugal',351,'Lycamobile'),
                                                                           (1878,'268','06','pt','Portugal',351,'MEO'),
                                                                           (1879,'268','80','pt','Portugal',351,'MEO'),
                                                                           (1880,'268','08','pt','Portugal',351,'MEO');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1881,'268','03','pt','Portugal',351,'NOS'),
                                                                           (1882,'268','93','pt','Portugal',351,'NOS'),
                                                                           (1883,'268','299','pt','Portugal',351,'NOWO'),
                                                                           (1884,'268','299','pt','Portugal',351,'Oni'),
                                                                           (1885,'410','299','pt','Portugal',351,'Premium Numbers'),
                                                                           (1886,'268','01','pt','Portugal',351,'Vodafone'),
                                                                           (1887,'268','91','pt','Portugal',351,'Vodafone'),
                                                                           (1888,'330','30','pr','Puerto Rico',NULL,'AT&T Mobility'),
                                                                           (1889,'330','11','pr','Puerto Rico',NULL,'Claro'),
                                                                           (1890,'330','110','pr','Puerto Rico',NULL,'Claro');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1891,'330','299','pr','Puerto Rico',NULL,'Failed Calls'),
                                                                           (1892,'330','999','pr','Puerto Rico',NULL,'Fix Line'),
                                                                           (1893,'330','490','pr','Puerto Rico',NULL,'T-Mobile'),
                                                                           (1894,'427','299','qa','Qatar',974,'Failed Calls'),
                                                                           (1895,'427','999','qa','Qatar',974,'Fix Line'),
                                                                           (1896,'427','06','qa','Qatar',974,'Ooredoo'),
                                                                           (1897,'427','01','qa','Qatar',974,'Ooredoo'),
                                                                           (1898,'427','02','qa','Qatar',974,'Vodafone'),
                                                                           (1899,'647','299','re','Reunion',262,'Failed Calls'),
                                                                           (1900,'647','999','re','Reunion',262,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1901,'647','02','re','Reunion',262,'Only'),
                                                                           (1902,'647','03','re','Reunion',262,'Only'),
                                                                           (1903,'647','00','re','Reunion',262,'Orange'),
                                                                           (1904,'647','04','re','Reunion',262,'ZEOP Mobile'),
                                                                           (1905,'226','05','ro','Romania',40,'Digi Mobil'),
                                                                           (1906,'226','299','ro','Romania',40,'Failed Calls'),
                                                                           (1907,'226','999','ro','Romania',40,'Fix Line'),
                                                                           (1908,'226','299','ro','Romania',40,'Iristel'),
                                                                           (1909,'226','16','ro','Romania',40,'Lycamobile'),
                                                                           (1910,'226','10','ro','Romania',40,'Orange');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1911,'226','02','ro','Romania',40,'Telekom'),
                                                                           (1912,'226','03','ro','Romania',40,'Telekom'),
                                                                           (1913,'226','01','ro','Romania',40,'Vodafone'),
                                                                           (1914,'250','299','ru','Russia',79,'Antares'),
                                                                           (1915,'250','299','ru','Russia',79,'Arktur'),
                                                                           (1916,'250','299','ru','Russia',79,'Astran'),
                                                                           (1917,'250','299','ru','Russia',79,'ASVT'),
                                                                           (1918,'250','299','ru','Russia',79,'Aurora Telecom'),
                                                                           (1919,'250','99','ru','Russia',79,'Beeline'),
                                                                           (1920,'250','299','ru','Russia',79,'Beliton');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1921,'250','299','ru','Russia',79,'BIT-CENTR'),
                                                                           (1922,'250','299','ru','Russia',79,'Center 2M'),
                                                                           (1923,'250','299','ru','Russia',79,'Cifra 1'),
                                                                           (1924,'250','299','ru','Russia',79,'CountryCom'),
                                                                           (1925,'250','299','ru','Russia',79,'ECO Networks'),
                                                                           (1926,'250','299','ru','Russia',79,'Elemte'),
                                                                           (1927,'250','299','ru','Russia',79,'ER-Telecom'),
                                                                           (1928,'250','299','ru','Russia',79,'Failed Calls'),
                                                                           (1929,'250','999','ru','Russia',79,'Fix Line'),
                                                                           (1930,'250','299','ru','Russia',79,'Gazprom Telecom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1931,'250','48','ru','Russia',79,'Global Telecom'),
                                                                           (1932,'250','55','ru','Russia',79,'Glonass'),
                                                                           (1933,'250','299','ru','Russia',79,'GLONASS MOBILE'),
                                                                           (1934,'250','299','ru','Russia',79,'Integral'),
                                                                           (1935,'250','299','ru','Russia',79,'Internod'),
                                                                           (1936,'250','299','ru','Russia',79,'Intersvyaz-2'),
                                                                           (1937,'250','34','ru','Russia',79,'Krymtelecom'),
                                                                           (1938,'250','299','ru','Russia',79,'KvatroPlus'),
                                                                           (1939,'250','299','ru','Russia',79,'Lardex'),
                                                                           (1940,'250','54','ru','Russia',79,'Letai Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1941,'250','299','ru','Russia',79,'Lycamobile'),
                                                                           (1942,'250','57','ru','Russia',79,'Matrix Mobile'),
                                                                           (1943,'250','299','ru','Russia',79,'Media-Market'),
                                                                           (1944,'250','02','ru','Russia',79,'Megafon'),
                                                                           (1945,'250','299','ru','Russia',79,'Metro-Pei'),
                                                                           (1946,'250','299','ru','Russia',79,'MGTS'),
                                                                           (1947,'250','299','ru','Russia',79,'Miatel'),
                                                                           (1948,'250','299','ru','Russia',79,'MOSTELECOM'),
                                                                           (1949,'250','35','ru','Russia',79,'Motiv'),
                                                                           (1950,'250','299','ru','Russia',79,'MSN Telekom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1951,'250','01','ru','Russia',79,'MTS'),
                                                                           (1952,'250','42','ru','Russia',79,'MTT'),
                                                                           (1953,'250','299','ru','Russia',79,'NCI'),
                                                                           (1954,'250','299','ru','Russia',79,'NETBYNET'),
                                                                           (1955,'250','299','ru','Russia',79,'New Mobile Communications'),
                                                                           (1956,'250','299','ru','Russia',79,'OBIT'),
                                                                           (1957,'250','299','ru','Russia',79,'Orange Business Services'),
                                                                           (1958,'250','299','ru','Russia',79,'PIN'),
                                                                           (1959,'250','299','ru','Russia',79,'Plintron'),
                                                                           (1960,'250','299','ru','Russia',79,'Quantech');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1961,'250','299','ru','Russia',79,'RECONN'),
                                                                           (1962,'250','299','ru','Russia',79,'Reteyl Innovatsii'),
                                                                           (1963,'250','299','ru','Russia',79,'Sberbank-Telecom'),
                                                                           (1964,'250','33','ru','Russia',79,'SEVTELECOM'),
                                                                           (1965,'250','299','ru','Russia',79,'Sim Sim'),
                                                                           (1966,'250','299','ru','Russia',79,'Sintonik'),
                                                                           (1967,'250','299','ru','Russia',79,'Sky Networks'),
                                                                           (1968,'250','09','ru','Russia',79,'Skylink'),
                                                                           (1969,'250','299','ru','Russia',79,'SkyNet'),
                                                                           (1970,'250','299','ru','Russia',79,'Sonet');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1971,'250','299','ru','Russia',79,'Sprint'),
                                                                           (1972,'250','299','ru','Russia',79,'Start'),
                                                                           (1973,'250','299','ru','Russia',79,'SunSIM'),
                                                                           (1974,'250','299','ru','Russia',79,'Surgutneftegaz'),
                                                                           (1975,'250','299','ru','Russia',79,'Svyazresurs-Mobile'),
                                                                           (1976,'250','299','ru','Russia',79,'Tander'),
                                                                           (1977,'250','20','ru','Russia',79,'Tele2'),
                                                                           (1978,'250','12','ru','Russia',79,'Tele2'),
                                                                           (1979,'250','299','ru','Russia',79,'Tinkoff Mobile'),
                                                                           (1980,'250','299','ru','Russia',79,'TMT');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1981,'250','299','ru','Russia',79,'TransMobilCom'),
                                                                           (1982,'250','299','ru','Russia',79,'TRASTEL'),
                                                                           (1983,'250','299','ru','Russia',79,'TRN-telecom'),
                                                                           (1984,'250','299','ru','Russia',79,'TTK'),
                                                                           (1985,'250','299','ru','Russia',79,'TTK-Svyaz'),
                                                                           (1986,'250','299','ru','Russia',79,'TVE'),
                                                                           (1987,'250','299','ru','Russia',79,'UnitTelecom'),
                                                                           (1988,'250','299','ru','Russia',79,'Unycel'),
                                                                           (1989,'250','299','ru','Russia',79,'Vainah Telecom'),
                                                                           (1990,'250','299','ru','Russia',79,'ViKom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (1991,'250','299','ru','Russia',79,'Virgin Connect'),
                                                                           (1992,'250','299','ru','Russia',79,'Voentelecom'),
                                                                           (1993,'250','77','ru','Russia',79,'Glonass'),
                                                                           (1994,'250','60','ru','Russia',79,'Volna Mobile'),
                                                                           (1995,'250','299','ru','Russia',79,'VTB Mobile'),
                                                                           (1996,'250','32','ru','Russia',79,'Win Mobile'),
                                                                           (1997,'250','11','ru','Russia',79,'Yota'),
                                                                           (1998,'635','13','rw','Rwanda',250,'Airtel'),
                                                                           (1999,'635','299','rw','Rwanda',250,'Failed Calls'),
                                                                           (2000,'635','999','rw','Rwanda',250,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2001,'635','10','rw','Rwanda',250,'MTN'),
                                                                           (2002,'658','299','sh','Saint Helena and Ascension and Tristan da Cunha',290,'Failed Calls'),
                                                                           (2003,'658','999','sh','Saint Helena and Ascension and Tristan da Cunha',290,'Fix Line'),
                                                                           (2004,'356','70','kn','Saint Kitts and Nevis',1869,'Chippie'),
                                                                           (2005,'356','070','kn','Saint Kitts and Nevis',1869,'Chippie'),
                                                                           (2006,'356','50','kn','Saint Kitts and Nevis',1869,'Digicel'),
                                                                           (2007,'356','050','kn','Saint Kitts and Nevis',1869,'Digicel'),
                                                                           (2008,'356','299','kn','Saint Kitts and Nevis',1869,'Failed Calls'),
                                                                           (2009,'356','999','kn','Saint Kitts and Nevis',1869,'Fix Line'),
                                                                           (2010,'356','11','kn','Saint Kitts and Nevis',1869,'Flow');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2011,'356','110','kn','Saint Kitts and Nevis',1869,'Flow'),
                                                                           (2012,'356','299','kn','Saint Kitts and Nevis',1869,'The Cable'),
                                                                           (2013,'358','05','lc','Saint Lucia',1758,'Digicel'),
                                                                           (2014,'358','50','lc','Saint Lucia',1758,'Digicel'),
                                                                           (2015,'358','299','lc','Saint Lucia',1758,'Failed Calls'),
                                                                           (2016,'358','999','lc','Saint Lucia',1758,'Fix Line'),
                                                                           (2017,'358','110','lc','Saint Lucia',1758,'Flow'),
                                                                           (2018,'358','11','lc','Saint Lucia',1758,'Flow'),
                                                                           (2019,'308','01','pm','Saint Pierre and Miquelon',508,'Ameris'),
                                                                           (2020,'308','03','pm','Saint Pierre and Miquelon',508,'Ameris');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2021,'308','299','pm','Saint Pierre and Miquelon',508,'Failed Calls'),
                                                                           (2022,'308','999','pm','Saint Pierre and Miquelon',508,'Fix Line'),
                                                                           (2023,'308','02','pm','Saint Pierre and Miquelon',508,'Globaltel'),
                                                                           (2024,'360','050','vc','Saint Vincent and the Grenadines',1784,'Digicel'),
                                                                           (2025,'360','299','vc','Saint Vincent and the Grenadines',1784,'Failed Calls'),
                                                                           (2026,'360','999','vc','Saint Vincent and the Grenadines',1784,'Fix Line'),
                                                                           (2027,'360','110','vc','Saint Vincent and the Grenadines',1784,'Flow'),
                                                                           (2028,'549','27','ws','Samoa',685,'BlueSky'),
                                                                           (2029,'549','01','ws','Samoa',685,'Digicel'),
                                                                           (2030,'549','299','ws','Samoa',685,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2031,'549','999','ws','Samoa',685,'Fix Line'),
                                                                           (2032,'292','299','sm','San Marino',378,'Failed Calls'),
                                                                           (2033,'292','999','sm','San Marino',378,'Fix Line'),
                                                                           (2034,'292','01','sm','San Marino',378,'Prima'),
                                                                           (2035,'292','299','sm','San Marino',378,'TeleneT'),
                                                                           (2036,'626','01','st','Sao Tome and Principe',239,'CSTmovel'),
                                                                           (2037,'626','299','st','Sao Tome and Principe',239,'Failed Calls'),
                                                                           (2038,'626','999','st','Sao Tome and Principe',239,'Fix Line'),
                                                                           (2039,'626','02','st','Sao Tome and Principe',239,'Unitel'),
                                                                           (2040,'901','299','n/a','Satellite Networks',870,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2041,'901','999','n/a','Satellite Networks',870,'Fix Line'),
                                                                           (2042,'420','299','sa','Saudi Arabia',966,'Failed Calls'),
                                                                           (2043,'420','999','sa','Saudi Arabia',966,'Fix Line'),
                                                                           (2044,'420','06','sa','Saudi Arabia',966,'Lebara Mobile'),
                                                                           (2045,'420','03','sa','Saudi Arabia',966,'Mobily'),
                                                                           (2046,'420','299','sa','Saudi Arabia',966,'Red Bull MOBILE'),
                                                                           (2047,'420','299','sa','Saudi Arabia',966,'Salam'),
                                                                           (2048,'420','01','sa','Saudi Arabia',966,'STC / Al Jawal'),
                                                                           (2049,'420','05','sa','Saudi Arabia',966,'Virgin Mobile'),
                                                                           (2050,'420','04','sa','Saudi Arabia',966,'Zain');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2051,'608','299','sn','Senegal',221,'2s Mobile'),
                                                                           (2052,'608','03','sn','Senegal',221,'Expresso'),
                                                                           (2053,'608','299','sn','Senegal',221,'Failed Calls'),
                                                                           (2054,'608','999','sn','Senegal',221,'Fix Line'),
                                                                           (2055,'608','02','sn','Senegal',221,'Free'),
                                                                           (2056,'608','04','sn','Senegal',221,'HAYO'),
                                                                           (2057,'608','01','sn','Senegal',221,'Orange'),
                                                                           (2058,'608','299','sn','Senegal',221,'Promobile'),
                                                                           (2059,'220','299','rs','Serbia',381,'Failed Calls'),
                                                                           (2060,'220','999','rs','Serbia',381,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2061,'220','11','rs','Serbia',381,'Globaltel'),
                                                                           (2062,'220','03','rs','Serbia',381,'MTS'),
                                                                           (2063,'220','01','rs','Serbia',381,'Telenor'),
                                                                           (2064,'220','05','rs','Serbia',381,'VIP'),
                                                                           (2065,'220','20','rs','Serbia',381,'VIP'),
                                                                           (2066,'633','10','sc','Seychelles',248,'Airtel'),
                                                                           (2067,'633','01','sc','Seychelles',248,'Cable & Wireless'),
                                                                           (2068,'633','299','sc','Seychelles',248,'Failed Calls'),
                                                                           (2069,'633','999','sc','Seychelles',248,'Fix Line'),
                                                                           (2070,'633','05','sc','Seychelles',248,'Intelvision');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2071,'619','03','sl','Sierra Leone',232,'Africell'),
                                                                           (2072,'619','299','sl','Sierra Leone',232,'Failed Calls'),
                                                                           (2073,'619','999','sl','Sierra Leone',232,'Fix Line'),
                                                                           (2074,'619','299','sl','Sierra Leone',232,'IPTel'),
                                                                           (2075,'619','299','sl','Sierra Leone',232,'Onlime'),
                                                                           (2076,'619','01','sl','Sierra Leone',232,'Orange'),
                                                                           (2077,'619','07','sl','Sierra Leone',232,'Qcell'),
                                                                           (2078,'619','299','sl','Sierra Leone',232,'SierraTel'),
                                                                           (2079,'525','09','sg','Singapore',65,'Circles.Life'),
                                                                           (2080,'525','299','sg','Singapore',65,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2081,'525','999','sg','Singapore',65,'Fix Line'),
                                                                           (2082,'525','12','sg','Singapore',65,'GRID'),
                                                                           (2083,'525','03','sg','Singapore',65,'M1'),
                                                                           (2084,'525','11','sg','Singapore',65,'M1'),
                                                                           (2085,'525','299','sg','Singapore',65,'MyRepublic'),
                                                                           (2086,'525','299','sg','Singapore',65,'Nexwave'),
                                                                           (2087,'525','299','sg','Singapore',65,'redONE'),
                                                                           (2088,'525','07','sg','Singapore',65,'SingTel'),
                                                                           (2089,'525','02','sg','Singapore',65,'SingTel'),
                                                                           (2090,'525','01','sg','Singapore',65,'SingTel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2091,'525','299','sg','Singapore',65,'Smart Pinoy'),
                                                                           (2092,'525','08','sg','Singapore',65,'Starhub'),
                                                                           (2093,'525','06','sg','Singapore',65,'Starhub'),
                                                                           (2094,'525','05','sg','Singapore',65,'Starhub'),
                                                                           (2095,'525','10','sg','Singapore',65,'TPG Telecom'),
                                                                           (2096,'525','299','sg','Singapore',65,'VivoBee'),
                                                                           (2097,'525','299','sg','Singapore',65,'Zero1'),
                                                                           (2098,'231','299','sk','Slovakia',421,'Failed Calls'),
                                                                           (2099,'231','999','sk','Slovakia',421,'Fix Line'),
                                                                           (2100,'231','06','sk','Slovakia',421,'O2');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2101,'231','01','sk','Slovakia',421,'Orange'),
                                                                           (2102,'231','07','sk','Slovakia',421,'Orange'),
                                                                           (2103,'231','05','sk','Slovakia',421,'Orange'),
                                                                           (2104,'231','03','sk','Slovakia',421,'Swan / 4ka'),
                                                                           (2105,'231','02','sk','Slovakia',421,'Telekom'),
                                                                           (2106,'231','04','sk','Slovakia',421,'Telekom'),
                                                                           (2107,'231','50','sk','Slovakia',421,'Telekom'),
                                                                           (2108,'231','08','sk','Slovakia',421,'Uniphone'),
                                                                           (2109,'231','299','sk','Slovakia',421,'Vonage'),
                                                                           (2110,'231','99','sk','Slovakia',421,'ZSR');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2111,'293','40','si','Slovenia',386,'A1 / Si.mobil'),
                                                                           (2112,'293','20','si','Slovenia',386,'Compatel'),
                                                                           (2113,'293','86','si','Slovenia',386,'Elektro Gorenjska'),
                                                                           (2114,'293','299','si','Slovenia',386,'Failed Calls'),
                                                                           (2115,'293','999','si','Slovenia',386,'Fix Line'),
                                                                           (2116,'293','299','si','Slovenia',386,'HOT mobil'),
                                                                           (2117,'293','299','si','Slovenia',386,'Me2'),
                                                                           (2118,'293','41','si','Slovenia',386,'Mobitel'),
                                                                           (2119,'293','299','si','Slovenia',386,'Novatel'),
                                                                           (2120,'293','10','si','Slovenia',386,'Slovenske zeleznice');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2121,'293','299','si','Slovenia',386,'SoftNET'),
                                                                           (2122,'293','64','si','Slovenia',386,'T-2'),
                                                                           (2123,'293','70','si','Slovenia',386,'Telemach / Tusmobil'),
                                                                           (2124,'540','02','sb','Solomon Islands',677,'bemobile'),
                                                                           (2125,'540','01','sb','Solomon Islands',677,'Breeze'),
                                                                           (2126,'540','299','sb','Solomon Islands',677,'Failed Calls'),
                                                                           (2127,'540','999','sb','Solomon Islands',677,'Fix Line'),
                                                                           (2128,'540','299','sb','Solomon Islands',677,'Satsol'),
                                                                           (2129,'540','299','sb','Solomon Islands',677,'Smile'),
                                                                           (2130,'637','299','so','Somalia',252,'AirSom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2131,'637','299','so','Somalia',252,'Failed Calls'),
                                                                           (2132,'637','999','so','Somalia',252,'Fix Line'),
                                                                           (2133,'637','30','so','Somalia',252,'Golis'),
                                                                           (2134,'637','19','so','Somalia',252,'Hormuud'),
                                                                           (2135,'637','10','so','Somalia',252,'Nationlink'),
                                                                           (2136,'637','299','so','Somalia',252,'NETCO'),
                                                                           (2137,'637','70','so','Somalia',252,'Onkod'),
                                                                           (2138,'637','04','so','Somalia',252,'Somafone'),
                                                                           (2139,'637','299','so','Somalia',252,'SomNetworks'),
                                                                           (2140,'637','71','so','Somalia',252,'Somtel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2141,'637','299','so','Somalia',252,'STG'),
                                                                           (2142,'637','82','so','Somalia',252,'Telcom Mobile'),
                                                                           (2143,'637','01','so','Somalia',252,'Telesom'),
                                                                           (2144,'655','07','za','South Africa',27,'Cell C'),
                                                                           (2145,'655','299','za','South Africa',27,'Failed Calls'),
                                                                           (2146,'655','999','za','South Africa',27,'Fix Line'),
                                                                           (2147,'655','299','za','South Africa',27,'Lycamobile'),
                                                                           (2148,'655','10','za','South Africa',27,'MTN'),
                                                                           (2149,'655','12','za','South Africa',27,'MTN'),
                                                                           (2150,'655','19','za','South Africa',27,'Rain');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2151,'655','74','za','South Africa',27,'Rain'),
                                                                           (2152,'655','73','za','South Africa',27,'Rain'),
                                                                           (2153,'655','38','za','South Africa',27,'Rain'),
                                                                           (2154,'655','05','za','South Africa',27,'Telkom'),
                                                                           (2155,'655','02','za','South Africa',27,'Telkom'),
                                                                           (2156,'655','299','za','South Africa',27,'Virgin Mobile'),
                                                                           (2157,'655','01','za','South Africa',27,'Vodacom'),
                                                                           (2158,'450','299','kr','South Korea',82,'Failed Calls'),
                                                                           (2159,'450','999','kr','South Korea',82,'Fix Line'),
                                                                           (2160,'450','07','kr','South Korea',82,'KT Powertel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2161,'450','06','kr','South Korea',82,'LG U+'),
                                                                           (2162,'450','04','kr','South Korea',82,'olleh / KT'),
                                                                           (2163,'450','08','kr','South Korea',82,'olleh / KT'),
                                                                           (2164,'450','02','kr','South Korea',82,'olleh / KT'),
                                                                           (2165,'450','11','kr','South Korea',82,'SK Telecom'),
                                                                           (2166,'450','12','kr','South Korea',82,'SK Telecom'),
                                                                           (2167,'450','05','kr','South Korea',82,'SK Telecom'),
                                                                           (2168,'659','299','ss','South Sudan',NULL,'Digitel'),
                                                                           (2169,'659','299','ss','South Sudan',NULL,'Failed Calls'),
                                                                           (2170,'659','999','ss','South Sudan',NULL,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2171,'659','02','ss','South Sudan',NULL,'MTN'),
                                                                           (2172,'659','06','ss','South Sudan',NULL,'Zain'),
                                                                           (2173,'214','299','es','Spain',34,'ACN'),
                                                                           (2174,'214','299','es','Spain',34,'Adamo Telecom'),
                                                                           (2175,'214','36','es','Spain',34,'Alai'),
                                                                           (2176,'214','02','es','Spain',34,'Alta Tecnologia en Comunicacions'),
                                                                           (2177,'214','299','es','Spain',34,'Aurea'),
                                                                           (2178,'214','14','es','Spain',34,'Avatel Movil'),
                                                                           (2179,'214','299','es','Spain',34,'Billing Financial'),
                                                                           (2180,'214','299','es','Spain',34,'Bluephone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2181,'214','299','es','Spain',34,'CloudComms'),
                                                                           (2182,'214','299','es','Spain',34,'Dialoga'),
                                                                           (2183,'214','22','es','Spain',34,'Digi.Mobil'),
                                                                           (2184,'214','299','es','Spain',34,'Dragonet'),
                                                                           (2185,'214','08','es','Spain',34,'Euskaltel Movil'),
                                                                           (2186,'214','299','es','Spain',34,'Evolutio'),
                                                                           (2187,'214','299','es','Spain',34,'Failed Calls'),
                                                                           (2188,'214','999','es','Spain',34,'Fix Line'),
                                                                           (2189,'214','299','es','Spain',34,'Global'),
                                                                           (2190,'214','299','es','Spain',34,'GNET');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2191,'214','34','es','Spain',34,'ION MOBILE'),
                                                                           (2192,'214','299','es','Spain',34,'Jetnet'),
                                                                           (2193,'214','299','es','Spain',34,'Lemonvil'),
                                                                           (2194,'214','25','es','Spain',34,'Lycamobile'),
                                                                           (2195,'214','17','es','Spain',34,'mobil R'),
                                                                           (2196,'214','38','es','Spain',34,'Movistar'),
                                                                           (2197,'214','07','es','Spain',34,'Movistar'),
                                                                           (2198,'214','05','es','Spain',34,'Movistar'),
                                                                           (2199,'214','299','es','Spain',34,'Olephone'),
                                                                           (2200,'214','299','es','Spain',34,'On Movil');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2201,'214','299','es','Spain',34,'Oniti Telecom'),
                                                                           (2202,'214','299','es','Spain',34,'OperadorsCat'),
                                                                           (2203,'214','09','es','Spain',34,'Orange'),
                                                                           (2204,'214','21','es','Spain',34,'Orange'),
                                                                           (2205,'214','03','es','Spain',34,'Orange'),
                                                                           (2206,'214','299','es','Spain',34,'Pepephone'),
                                                                           (2207,'214','299','es','Spain',34,'Premium Numbers'),
                                                                           (2208,'214','299','es','Spain',34,'PTV Telecom movil'),
                                                                           (2209,'214','299','es','Spain',34,'Quattre'),
                                                                           (2210,'214','299','es','Spain',34,'Sarenet');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2211,'214','299','es','Spain',34,'SEWAN'),
                                                                           (2212,'214','19','es','Spain',34,'Simyo'),
                                                                           (2213,'214','35','es','Spain',34,'SUMA movil'),
                                                                           (2214,'214','299','es','Spain',34,'Suop'),
                                                                           (2215,'214','299','es','Spain',34,'Syma'),
                                                                           (2216,'214','16','es','Spain',34,'mobil R'),
                                                                           (2217,'214','299','es','Spain',34,'Telsome'),
                                                                           (2218,'214','299','es','Spain',34,'The Telecom Boutique'),
                                                                           (2219,'214','27','es','Spain',34,'Truphone'),
                                                                           (2220,'214','12','es','Spain',34,'Venus Movil');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2221,'214','06','es','Spain',34,'Vodafone'),
                                                                           (2222,'214','01','es','Spain',34,'Vodafone'),
                                                                           (2223,'214','37','es','Spain',34,'Vodafone'),
                                                                           (2224,'214','33','es','Spain',34,'Yoigo'),
                                                                           (2225,'214','23','es','Spain',34,'Yoigo'),
                                                                           (2226,'214','29','es','Spain',34,'Yoigo'),
                                                                           (2227,'214','04','es','Spain',34,'Yoigo'),
                                                                           (2228,'214','299','es','Spain',34,'You Mobile'),
                                                                           (2229,'214','10','es','Spain',34,'Zinnia'),
                                                                           (2230,'413','05','lk','Sri Lanka',94,'Airtel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2231,'413','02','lk','Sri Lanka',94,'Dialog'),
                                                                           (2232,'413','299','lk','Sri Lanka',94,'Failed Calls'),
                                                                           (2233,'413','999','lk','Sri Lanka',94,'Fix Line'),
                                                                           (2234,'413','08','lk','Sri Lanka',94,'Hutch'),
                                                                           (2235,'413','01','lk','Sri Lanka',94,'Mobitel'),
                                                                           (2236,'634','299','sd','Sudan',249,'Failed Calls'),
                                                                           (2237,'634','999','sd','Sudan',249,'Fix Line'),
                                                                           (2238,'634','03','sd','Sudan',249,'MTN'),
                                                                           (2239,'634','02','sd','Sudan',249,'MTN'),
                                                                           (2240,'634','07','sd','Sudan',249,'Sudani One');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2241,'634','06','sd','Sudan',249,'Zain'),
                                                                           (2242,'634','01','sd','Sudan',249,'Zain'),
                                                                           (2243,'746','03','sr','Suriname',597,'Digicel'),
                                                                           (2244,'746','299','sr','Suriname',597,'Failed Calls'),
                                                                           (2245,'746','999','sr','Suriname',597,'Fix Line'),
                                                                           (2246,'746','02','sr','Suriname',597,'Telesur'),
                                                                           (2247,'653','02','sz','Swaziland',268,'Eswatini Mobile'),
                                                                           (2248,'653','01','sz','Swaziland',268,'EswatiniTelecom'),
                                                                           (2249,'653','299','sz','Swaziland',268,'Failed Calls'),
                                                                           (2250,'653','999','sz','Swaziland',268,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2251,'653','10','sz','Swaziland',268,'Swazi MTN'),
                                                                           (2252,'240','16','se','Sweden',46,''),
                                                                           (2253,'240','35','se','Sweden',46,'42 Telecom'),
                                                                           (2254,'240','13','se','Sweden',46,'A3'),
                                                                           (2255,'240','299','se','Sweden',46,'Advoco'),
                                                                           (2256,'240','299','se','Sweden',46,''),
                                                                           (2257,'240','299','se','Sweden',46,'Allo Telecom'),
                                                                           (2258,'240','299','se','Sweden',46,'Bahnhof'),
                                                                           (2259,'240','299','se','Sweden',46,'Benemen'),
                                                                           (2260,'240','299','se','Sweden',46,'Biztel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2261,'240','299','se','Sweden',46,'BM Sverige'),
                                                                           (2262,'240','299','se','Sweden',46,'Bredband 2'),
                                                                           (2263,'240','299','se','Sweden',46,'Bredbandsson'),
                                                                           (2264,'240','299','se','Sweden',46,'BSG'),
                                                                           (2265,'240','299','se','Sweden',46,'Chilimobil'),
                                                                           (2266,'240','11','se','Sweden',46,'Com Hem'),
                                                                           (2267,'240','09','se','Sweden',46,'Com4'),
                                                                           (2268,'240','32','se','Sweden',46,'Compatel'),
                                                                           (2269,'240','299','se','Sweden',46,'Connectel'),
                                                                           (2270,'240','299','se','Sweden',46,'Core Telecom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2271,'240','299','se','Sweden',46,'easy pbx'),
                                                                           (2272,'240','299','se','Sweden',46,'EasyTelecom'),
                                                                           (2273,'240','22','se','Sweden',46,'EUtel'),
                                                                           (2274,'240','299','se','Sweden',46,'Failed Calls'),
                                                                           (2275,'240','299','se','Sweden',46,'Fastcom'),
                                                                           (2276,'240','299','se','Sweden',46,'Febo Telecom'),
                                                                           (2277,'240','299','se','Sweden',46,'Fello'),
                                                                           (2278,'240','299','se','Sweden',46,'Fibio'),
                                                                           (2279,'240','63','se','Sweden',46,'Fink Telecom'),
                                                                           (2280,'240','299','se','Sweden',46,'Firstcom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2281,'240','999','se','Sweden',46,'Fix Line'),
                                                                           (2282,'240','299','se','Sweden',46,'Fonia'),
                                                                           (2283,'240','18','se','Sweden',46,'Messit / Minicall'),
                                                                           (2284,'240','299','se','Sweden',46,'Global Telefoni'),
                                                                           (2285,'240','299','se','Sweden',46,'GlobalConnect'),
                                                                           (2286,'240','27','se','Sweden',46,'Globetouch'),
                                                                           (2287,'240','17','se','Sweden',46,'Gotanet'),
                                                                           (2288,'240','02','se','Sweden',46,'3'),
                                                                           (2289,'240','299','se','Sweden',46,'Horisen'),
                                                                           (2290,'240','299','se','Sweden',46,'iCentrex');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2291,'240','23','se','Sweden',46,'Infobip'),
                                                                           (2292,'240','299','se','Sweden',46,'InfraCom'),
                                                                           (2293,'240','36','se','Sweden',46,'interactive digital media / IDM'),
                                                                           (2294,'240','299','se','Sweden',46,'Ipify'),
                                                                           (2295,'240','299','se','Sweden',46,'Junyverse'),
                                                                           (2296,'240','299','se','Sweden',46,'Karma Mobil'),
                                                                           (2297,'240','299','se','Sweden',46,'Lancelot'),
                                                                           (2298,'240','28','se','Sweden',46,'LINK Mobility'),
                                                                           (2299,'240','299','se','Sweden',46,'Loxysoft'),
                                                                           (2300,'240','12','se','Sweden',46,'Lycamobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2301,'240','299','se','Sweden',46,'Maingate'),
                                                                           (2302,'240','299','se','Sweden',46,'Megaphone'),
                                                                           (2303,'240','299','se','Sweden',46,'MessageBird'),
                                                                           (2304,'240','29','se','Sweden',46,'MI Carrier Services'),
                                                                           (2305,'240','299','se','Sweden',46,'miniTel'),
                                                                           (2306,'240','299','se','Sweden',46,'Mitto'),
                                                                           (2307,'240','33','se','Sweden',46,'Mobile Arts'),
                                                                           (2308,'240','43','se','Sweden',46,'MobiWeb'),
                                                                           (2309,'240','299','se','Sweden',46,'Molnstruktur'),
                                                                           (2310,'240','25','se','Sweden',46,'Monty Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2311,'240','299','se','Sweden',46,'My Beat'),
                                                                           (2312,'240','299','se','Sweden',46,'Net at Once'),
                                                                           (2313,'240','40','se','Sweden',46,'Netmore'),
                                                                           (2314,'240','299','se','Sweden',46,'Netsize'),
                                                                           (2315,'240','299','se','Sweden',46,'onoff'),
                                                                           (2316,'240','299','se','Sweden',46,'Bixia AB'),
                                                                           (2317,'240','299','se','Sweden',46,'Premium Numbers'),
                                                                           (2318,'240','39','se','Sweden',46,'Primlight'),
                                                                           (2319,'240','31','se','Sweden',46,'Rebtel'),
                                                                           (2320,'240','20','se','Sweden',46,'Sierra Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2321,'240','15','se','Sweden',46,''),
                                                                           (2322,'240','37','se','Sweden',46,'Sinch'),
                                                                           (2323,'240','299','se','Sweden',46,'soatso'),
                                                                           (2324,'240','299','se','Sweden',46,'Soluno'),
                                                                           (2325,'240','45','se','Sweden',46,'Spirius'),
                                                                           (2326,'240','299','se','Sweden',46,'SSTNet'),
                                                                           (2327,'240','299','se','Sweden',46,'Svea Billing Services'),
                                                                           (2328,'240','299','se','Sweden',46,'Svensk Konsumentmobil'),
                                                                           (2329,'240','299','se','Sweden',46,'TelaVox'),
                                                                           (2330,'240','07','se','Sweden',46,'Tele2');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2331,'240','14','se','Sweden',46,'Tele2'),
                                                                           (2332,'240','05','se','Sweden',46,'Tele2'),
                                                                           (2333,'240','299','se','Sweden',46,'Telecom3 Sverige AB'),
                                                                           (2334,'240','299','se','Sweden',46,'Teledigit Scandinavia AB'),
                                                                           (2335,'240','299','se','Sweden',46,'Telekometen'),
                                                                           (2336,'240','44','se','Sweden',46,'Telenabler'),
                                                                           (2337,'240','42','se','Sweden',46,'Telenor Connexion'),
                                                                           (2338,'240','08','se','Sweden',46,'Telenor'),
                                                                           (2339,'240','04','se','Sweden',46,'Telenor'),
                                                                           (2340,'240','01','se','Sweden',46,'Telia');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2341,'240','299','se','Sweden',46,'Tell it to Mi'),
                                                                           (2342,'240','299','se','Sweden',46,'Telness'),
                                                                           (2343,'240','03','se','Sweden',46,'Net 1'),
                                                                           (2344,'240','48','se','Sweden',46,'Tismi'),
                                                                           (2345,'240','299','se','Sweden',46,'Tolv Mobil'),
                                                                           (2346,'240','21','se','Sweden',46,'Trafikverket'),
                                                                           (2347,'240','26','se','Sweden',46,'Twilio'),
                                                                           (2348,'240','299','se','Sweden',46,'Unicorn Telecom'),
                                                                           (2349,'240','19','se','Sweden',46,'Vectone Mobile'),
                                                                           (2350,'240','299','se','Sweden',46,'Verbal');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2351,'240','46','se','Sweden',46,'Viahub'),
                                                                           (2352,'240','47','se','Sweden',46,'Viatel'),
                                                                           (2353,'240','299','se','Sweden',46,'Vobbiz'),
                                                                           (2354,'240','299','se','Sweden',46,'Voice Integrate'),
                                                                           (2355,'240','299','se','Sweden',46,'Voicetech'),
                                                                           (2356,'240','38','se','Sweden',46,'Voxbone / Bandwidth'),
                                                                           (2357,'240','299','se','Sweden',46,'Weelia'),
                                                                           (2358,'240','299','se','Sweden',46,'Wifi.se'),
                                                                           (2359,'240','299','se','Sweden',46,'Wifog Sverige'),
                                                                           (2360,'240','299','se','Sweden',46,'XPLORA');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2361,'228','58','ch','Switzerland',41,'Beeone'),
                                                                           (2362,'228','05','ch','Switzerland',41,'Comfone'),
                                                                           (2363,'228','09','ch','Switzerland',41,'Comfone'),
                                                                           (2364,'228','999','ch','Switzerland',41,'Fix Line'),
                                                                           (2365,'228','66','ch','Switzerland',41,'Inovia'),
                                                                           (2366,'228','54','ch','Switzerland',41,'Lycamobile'),
                                                                           (2367,'228','69','ch','Switzerland',41,'MTEL'),
                                                                           (2368,'228','65','ch','Switzerland',41,'Nexphone'),
                                                                           (2369,'228','51','ch','Switzerland',41,'relario'),
                                                                           (2370,'228','03','ch','Switzerland',41,'Salt Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2371,'228','06','ch','Switzerland',41,'SBB'),
                                                                           (2372,'228','60','ch','Switzerland',41,'Sunrise'),
                                                                           (2373,'228','07','ch','Switzerland',41,'Sunrise'),
                                                                           (2374,'228','08','ch','Switzerland',41,'Sunrise'),
                                                                           (2375,'228','53','ch','Switzerland',41,'Sunrise'),
                                                                           (2376,'228','12','ch','Switzerland',41,'Sunrise'),
                                                                           (2377,'228','02','ch','Switzerland',41,'Sunrise'),
                                                                           (2378,'228','01','ch','Switzerland',41,'Swisscom'),
                                                                           (2379,'228','62','ch','Switzerland',41,'Telecom26'),
                                                                           (2380,'228','70','ch','Switzerland',41,'Tismi');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2381,'228','59','ch','Switzerland',41,'Vectone Mobile'),
                                                                           (2382,'417','299','sy','Syria',963,'Failed Calls'),
                                                                           (2383,'417','999','sy','Syria',963,'Fix Line'),
                                                                           (2384,'417','02','sy','Syria',963,'MTN'),
                                                                           (2385,'417','01','sy','Syria',963,'Syriatel'),
                                                                           (2386,'466','12','tw','Taiwan',886,'APT'),
                                                                           (2387,'466','05','tw','Taiwan',886,'APT'),
                                                                           (2388,'466','92','tw','Taiwan',886,'Chunghwa Telecom'),
                                                                           (2389,'466','11','tw','Taiwan',886,'Chunghwa Telecom'),
                                                                           (2390,'466','299','tw','Taiwan',886,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2391,'466','01','tw','Taiwan',886,'Far EasTone'),
                                                                           (2392,'466','03','tw','Taiwan',886,'Far EasTone'),
                                                                           (2393,'466','88','tw','Taiwan',886,'Far EasTone'),
                                                                           (2394,'466','999','tw','Taiwan',886,'Fix Line'),
                                                                           (2395,'466','89','tw','Taiwan',886,'T Star'),
                                                                           (2396,'466','97','tw','Taiwan',886,'Taiwan Mobile'),
                                                                           (2397,'436','04','tj','Tajikistan',992,'Babilon-Mobile'),
                                                                           (2398,'436','05','tj','Tajikistan',992,'Beeline'),
                                                                           (2399,'436','299','tj','Tajikistan',992,'Failed Calls'),
                                                                           (2400,'436','999','tj','Tajikistan',992,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2401,'436','03','tj','Tajikistan',992,'Megafon'),
                                                                           (2402,'436','299','tj','Tajikistan',992,'Premium Numbers'),
                                                                           (2403,'436','01','tj','Tajikistan',992,'Tcell'),
                                                                           (2404,'436','02','tj','Tajikistan',992,'Tcell'),
                                                                           (2405,'640','05','tz','Tanzania',255,'Airtel'),
                                                                           (2406,'640','299','tz','Tanzania',255,'Failed Calls'),
                                                                           (2407,'640','999','tz','Tanzania',255,'Fix Line'),
                                                                           (2408,'640','09','tz','Tanzania',255,'Halotel / Viettel'),
                                                                           (2409,'640','99','tz','Tanzania',255,'Mkulima African Telecommunication'),
                                                                           (2410,'640','14','tz','Tanzania',255,'MO Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2411,'640','11','tz','Tanzania',255,'Smile Communications'),
                                                                           (2412,'640','07','tz','Tanzania',255,'Tanzania Telecommunication Corporation'),
                                                                           (2413,'640','02','tz','Tanzania',255,'Tigo / MIC'),
                                                                           (2414,'640','04','tz','Tanzania',255,'Vodacom'),
                                                                           (2415,'640','13','tz','Tanzania',255,'WiAfrica'),
                                                                           (2416,'640','03','tz','Tanzania',255,'Zanzibar Telecom / Zantel'),
                                                                           (2417,'520','03','th','Thailand',66,'AIS'),
                                                                           (2418,'520','01','th','Thailand',66,'AIS'),
                                                                           (2419,'520','18','th','Thailand',66,'dtac'),
                                                                           (2420,'520','05','th','Thailand',66,'dtac TriNet');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2421,'520','299','th','Thailand',66,'Failed Calls'),
                                                                           (2422,'520','999','th','Thailand',66,'Fix Line'),
                                                                           (2423,'520','00','th','Thailand',66,'my'),
                                                                           (2424,'520','47','th','Thailand',66,'TOTmobile'),
                                                                           (2425,'520','15','th','Thailand',66,'TOTmobile'),
                                                                           (2426,'520','04','th','Thailand',66,'TrueMove H'),
                                                                           (2427,'615','03','tg','Togo',228,'Atlantique Telecom / Moov'),
                                                                           (2428,'615','299','tg','Togo',228,'Failed Calls'),
                                                                           (2429,'615','999','tg','Togo',228,'Fix Line'),
                                                                           (2430,'615','01','tg','Togo',228,'Togo Cellulaire / TogoCel');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2431,'539','43','to','Tonga',676,'Digicel'),
                                                                           (2432,'539','88','to','Tonga',676,'Digicel'),
                                                                           (2433,'539','299','to','Tonga',676,'Failed Calls'),
                                                                           (2434,'539','999','to','Tonga',676,'Fix Line'),
                                                                           (2435,'539','01','to','Tonga',676,'U-Call'),
                                                                           (2436,'539','299','to','Tonga',676,'WanTok'),
                                                                           (2437,'374','12','tt','Trinidad and Tobago',1868,'bmobile'),
                                                                           (2438,'374','130','tt','Trinidad and Tobago',1868,'Digicel'),
                                                                           (2439,'374','299','tt','Trinidad and Tobago',1868,'Failed Calls'),
                                                                           (2440,'374','999','tt','Trinidad and Tobago',1868,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2441,'605','299','tn','Tunisia',216,'Failed Calls'),
                                                                           (2442,'605','999','tn','Tunisia',216,'Fix Line'),
                                                                           (2443,'605','06','tn','Tunisia',216,'Lycamobile'),
                                                                           (2444,'605','03','tn','Tunisia',216,'Ooredoo'),
                                                                           (2445,'605','01','tn','Tunisia',216,'Orange'),
                                                                           (2446,'605','02','tn','Tunisia',216,'TT Mobile'),
                                                                           (2447,'286','299','tr','Turkiye',90,'Asistan Telekom'),
                                                                           (2448,'286','03','tr','Turkiye',90,'Avea'),
                                                                           (2449,'286','04','tr','Turkiye',90,'Avea'),
                                                                           (2450,'286','299','tr','Turkiye',90,'BasakCell');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2451,'286','299','tr','Turkiye',90,'Compatel'),
                                                                           (2452,'286','299','tr','Turkiye',90,'Fenix Telekom'),
                                                                           (2453,'286','999','tr','Turkiye',90,'Fix Line'),
                                                                           (2454,'286','299','tr','Turkiye',90,'Foniva'),
                                                                           (2455,'286','299','tr','Turkiye',90,'IsNet'),
                                                                           (2456,'286','299','tr','Turkiye',90,'Maxiphone'),
                                                                           (2457,'286','299','tr','Turkiye',90,'Medium Telekom'),
                                                                           (2458,'286','299','tr','Turkiye',90,'Mobilisim'),
                                                                           (2459,'286','299','tr','Turkiye',90,'Netgsm'),
                                                                           (2460,'286','299','tr','Turkiye',90,'Nida');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2461,'286','299','tr','Turkiye',90,'Oris'),
                                                                           (2462,'286','299','tr','Turkiye',90,'Pelicell'),
                                                                           (2463,'286','299','tr','Turkiye',90,'Plus Telekom'),
                                                                           (2464,'286','299','tr','Turkiye',90,'Roitel'),
                                                                           (2465,'286','299','tr','Turkiye',90,'SesNet'),
                                                                           (2466,'286','299','tr','Turkiye',90,'TCDD'),
                                                                           (2467,'286','299','tr','Turkiye',90,'TTM'),
                                                                           (2468,'286','01','tr','Turkiye',90,'Turkcell'),
                                                                           (2469,'286','02','tr','Turkiye',90,'Vodafone'),
                                                                           (2470,'438','01','tm','Turkmenistan',993,'MTS');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2471,'438','299','tm','Turkmenistan',993,'Failed Calls'),
                                                                           (2472,'438','999','tm','Turkmenistan',993,'Fix Line'),
                                                                           (2473,'438','02','tm','Turkmenistan',993,'TMCELL'),
                                                                           (2474,'376','360','tc','Turks and Caicos Islands',NULL,'Digicel'),
                                                                           (2475,'376','050','tc','Turks and Caicos Islands',NULL,'Digicel'),
                                                                           (2476,'376','299','tc','Turks and Caicos Islands',NULL,'Failed Calls'),
                                                                           (2477,'376','999','tc','Turks and Caicos Islands',NULL,'Fix Line'),
                                                                           (2478,'376','350','tc','Turks and Caicos Islands',NULL,'Flow'),
                                                                           (2479,'553','299','tv','Tuvalu',NULL,'Failed Calls'),
                                                                           (2480,'553','999','tv','Tuvalu',NULL,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2481,'553','01','tv','Tuvalu',NULL,'Tuvalu Telecom'),
                                                                           (2482,'641','22','ug','Uganda',256,'Airtel'),
                                                                           (2483,'641','01','ug','Uganda',256,'Airtel'),
                                                                           (2484,'641','299','ug','Uganda',256,'Failed Calls'),
                                                                           (2485,'641','999','ug','Uganda',256,'Fix Line'),
                                                                           (2486,'641','04','ug','Uganda',256,'Lycamobile'),
                                                                           (2487,'641','11','ug','Uganda',256,'Mango'),
                                                                           (2488,'641','10','ug','Uganda',256,'MTN'),
                                                                           (2489,'641','33','ug','Uganda',256,'Smile'),
                                                                           (2490,'255','07','ua','Ukraine',380,'3Mob');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2491,'255','299','ua','Ukraine',380,'Failed Calls'),
                                                                           (2492,'255','999','ua','Ukraine',380,'Fix Line'),
                                                                           (2493,'255','04','ua','Ukraine',380,'IT'),
                                                                           (2494,'255','02','ua','Ukraine',380,'Kyivstar'),
                                                                           (2495,'255','03','ua','Ukraine',380,'Kyivstar'),
                                                                           (2496,'255','06','ua','Ukraine',380,'lifecell'),
                                                                           (2497,'255','21','ua','Ukraine',380,'PEOPLEnet'),
                                                                           (2498,'255','99','ua','Ukraine',380,'Phoenix'),
                                                                           (2499,'255','01','ua','Ukraine',380,'Vodafone'),
                                                                           (2500,'424','03','ae','United Arab Emirates',971,'DU');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2501,'424','02','ae','United Arab Emirates',971,'Etisalat'),
                                                                           (2502,'424','299','ae','United Arab Emirates',971,'Failed Calls'),
                                                                           (2503,'424','999','ae','United Arab Emirates',971,'Fix Line'),
                                                                           (2504,'234','78','gb','United Kingdom',44,'Airwave'),
                                                                           (2505,'234','29','gb','United Kingdom',44,'aql'),
                                                                           (2506,'234','76','gb','United Kingdom',44,'BT Group'),
                                                                           (2507,'234','00','gb','United Kingdom',44,'BT Group'),
                                                                           (2508,'234','08','gb','United Kingdom',44,'BT OnePhone'),
                                                                           (2509,'234','18','gb','United Kingdom',44,'Cloud9'),
                                                                           (2510,'234','30','gb','United Kingdom',44,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2511,'234','32','gb','United Kingdom',44,'T-Mobile'),
                                                                           (2512,'234','31','gb','United Kingdom',44,'T-Mobile'),
                                                                           (2513,'234','999','gb','United Kingdom',44,'Fix Line'),
                                                                           (2514,'234','17','gb','United Kingdom',44,'FlexTel'),
                                                                           (2515,'234','04','gb','United Kingdom',44,'FMS Solutions'),
                                                                           (2516,'234','39','gb','United Kingdom',44,'Gamma Mobile'),
                                                                           (2517,'234','24','gb','United Kingdom',44,'Greenfone'),
                                                                           (2518,'234','72','gb','United Kingdom',44,'Hanhaa Mobile'),
                                                                           (2519,'234','71','gb','United Kingdom',44,'Home Office'),
                                                                           (2520,'234','20','gb','United Kingdom',44,'3');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2521,'234','94','gb','United Kingdom',44,'3'),
                                                                           (2522,'234','23','gb','United Kingdom',44,'Icron Network'),
                                                                           (2523,'234','03','gb','United Kingdom',44,'Jersey Airtel'),
                                                                           (2524,'234','35','gb','United Kingdom',44,'JSC Ingenicum'),
                                                                           (2525,'234','50','gb','United Kingdom',44,'JT Mobile'),
                                                                           (2526,'234','14','gb','United Kingdom',44,'LINK Mobility'),
                                                                           (2527,'234','26','gb','United Kingdom',44,'Lycamobile'),
                                                                           (2528,'234','58','gb','United Kingdom',44,'Manx Telecom Mobile'),
                                                                           (2529,'234','28','gb','United Kingdom',44,'Marathon Telecom'),
                                                                           (2530,'234','75','gb','United Kingdom',44,'Mass Response Service GmbH');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2531,'234','56','gb','United Kingdom',44,'NCSC'),
                                                                           (2532,'234','95','gb','United Kingdom',44,'Network Rail'),
                                                                           (2533,'234','12','gb','United Kingdom',44,'Network Rail'),
                                                                           (2534,'234','13','gb','United Kingdom',44,'Network Rail'),
                                                                           (2535,'234','51','gb','United Kingdom',44,'now broadband'),
                                                                           (2536,'234','34','gb','United Kingdom',44,'Orange'),
                                                                           (2537,'234','33','gb','United Kingdom',44,'Orange'),
                                                                           (2538,'234','74','gb','United Kingdom',44,'Pareteum'),
                                                                           (2539,'234','57','gb','United Kingdom',44,'Sky'),
                                                                           (2540,'234','40','gb','United Kingdom',44,'spusu');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2541,'234','55','gb','United Kingdom',44,'Sure Guernsey'),
                                                                           (2542,'234','36','gb','United Kingdom',44,'Sure Isle of Man'),
                                                                           (2543,'234','99','gb','United Kingdom',44,'Sure Jersey'),
                                                                           (2544,'234','37','gb','United Kingdom',44,'Synectiv'),
                                                                           (2545,'234','16','gb','United Kingdom',44,'Talk Talk'),
                                                                           (2546,'234','27','gb','United Kingdom',44,'Tata Communications Ltd'),
                                                                           (2547,'234','11','gb','United Kingdom',44,'O2'),
                                                                           (2548,'234','10','gb','United Kingdom',44,'O2'),
                                                                           (2549,'234','02','gb','United Kingdom',44,'O2'),
                                                                           (2550,'234','22','gb','United Kingdom',44,'Telesign Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2551,'234','19','gb','United Kingdom',44,'TeleWare'),
                                                                           (2552,'234','09','gb','United Kingdom',44,'Tismi'),
                                                                           (2553,'234','25','gb','United Kingdom',44,'Truphone'),
                                                                           (2554,'234','01','gb','United Kingdom',44,'Vectone Mobile'),
                                                                           (2555,'234','38','gb','United Kingdom',44,'Virgin Mobile'),
                                                                           (2556,'234','91','gb','United Kingdom',44,'Vodafone'),
                                                                           (2557,'234','07','gb','United Kingdom',44,'Vodafone'),
                                                                           (2558,'234','15','gb','United Kingdom',44,'Vodafone'),
                                                                           (2559,'234','92','gb','United Kingdom',44,'Vodafone'),
                                                                           (2560,'234','89','gb','United Kingdom',44,'Vodafone');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2561,'234','77','gb','United Kingdom',44,'Vodafone'),
                                                                           (2562,'310','130','us','United States of America',1,'Appalachian Wireless'),
                                                                           (2563,'310','750','us','United States of America',1,'Appalachian Wireless'),
                                                                           (2564,'312','130','us','United States of America',1,'Appalachian Wireless'),
                                                                           (2565,'312','750','us','United States of America',1,'Appalachian Wireless'),
                                                                           (2566,'310','710','us','United States of America',1,'ASTAC Cellular'),
                                                                           (2567,'310','760','us','United States of America',1,'ASTAC Cellular'),
                                                                           (2568,'312','760','us','United States of America',1,'ASTAC Cellular'),
                                                                           (2569,'312','710','us','United States of America',1,'ASTAC Cellular'),
                                                                           (2570,'312','016','us','United States of America',1,'AT&T Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2571,'311','170','us','United States of America',1,'AT&T Mobility'),
                                                                           (2572,'310','180','us','United States of America',1,'AT&T Mobility'),
                                                                           (2573,'313','210','us','United States of America',1,'AT&T Mobility'),
                                                                           (2574,'312','950','us','United States of America',1,'AT&T Mobility'),
                                                                           (2575,'312','150','us','United States of America',1,'AT&T Mobility'),
                                                                           (2576,'311','410','us','United States of America',1,'AT&T Mobility'),
                                                                           (2577,'310','410','us','United States of America',1,'AT&T Mobility'),
                                                                           (2578,'313','950','us','United States of America',1,'AT&T Mobility'),
                                                                           (2579,'311','070','us','United States of America',1,'AT&T Mobility'),
                                                                           (2580,'310','080','us','United States of America',1,'AT&T Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2581,'313','090','us','United States of America',1,'AT&T Mobility'),
                                                                           (2582,'310','030','us','United States of America',1,'AT&T Mobility'),
                                                                           (2583,'312','380','us','United States of America',1,'AT&T Mobility'),
                                                                           (2584,'312','030','us','United States of America',1,'AT&T Mobility'),
                                                                           (2585,'311','180','us','United States of America',1,'AT&T Mobility'),
                                                                           (2586,'313','280','us','United States of America',1,'AT&T Mobility'),
                                                                           (2587,'313','380','us','United States of America',1,'AT&T Mobility'),
                                                                           (2588,'313','016','us','United States of America',1,'AT&T Mobility'),
                                                                           (2589,'312','170','us','United States of America',1,'AT&T Mobility'),
                                                                           (2590,'311','670','us','United States of America',1,'AT&T Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2591,'310','670','us','United States of America',1,'AT&T Mobility'),
                                                                           (2592,'311','080','us','United States of America',1,'AT&T Mobility'),
                                                                           (2593,'310','090','us','United States of America',1,'AT&T Mobility'),
                                                                           (2594,'313','150','us','United States of America',1,'AT&T Mobility'),
                                                                           (2595,'312','410','us','United States of America',1,'AT&T Mobility'),
                                                                           (2596,'312','070','us','United States of America',1,'AT&T Mobility'),
                                                                           (2597,'311','210','us','United States of America',1,'AT&T Mobility'),
                                                                           (2598,'310','210','us','United States of America',1,'AT&T Mobility'),
                                                                           (2599,'313','410','us','United States of America',1,'AT&T Mobility'),
                                                                           (2600,'313','030','us','United States of America',1,'AT&T Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2601,'312','180','us','United States of America',1,'AT&T Mobility'),
                                                                           (2602,'311','680','us','United States of America',1,'AT&T Mobility'),
                                                                           (2603,'310','680','us','United States of America',1,'AT&T Mobility'),
                                                                           (2604,'311','090','us','United States of America',1,'AT&T Mobility'),
                                                                           (2605,'310','150','us','United States of America',1,'AT&T Mobility'),
                                                                           (2606,'310','016','us','United States of America',1,'AT&T Mobility'),
                                                                           (2607,'313','170','us','United States of America',1,'AT&T Mobility'),
                                                                           (2608,'312','670','us','United States of America',1,'AT&T Mobility'),
                                                                           (2609,'312','080','us','United States of America',1,'AT&T Mobility'),
                                                                           (2610,'311','280','us','United States of America',1,'AT&T Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2611,'310','280','us','United States of America',1,'AT&T Mobility'),
                                                                           (2612,'313','670','us','United States of America',1,'AT&T Mobility'),
                                                                           (2613,'311','016','us','United States of America',1,'AT&T Mobility'),
                                                                           (2614,'313','070','us','United States of America',1,'AT&T Mobility'),
                                                                           (2615,'312','210','us','United States of America',1,'AT&T Mobility'),
                                                                           (2616,'311','950','us','United States of America',1,'AT&T Mobility'),
                                                                           (2617,'310','950','us','United States of America',1,'AT&T Mobility'),
                                                                           (2618,'311','150','us','United States of America',1,'AT&T Mobility'),
                                                                           (2619,'310','170','us','United States of America',1,'AT&T Mobility'),
                                                                           (2620,'313','180','us','United States of America',1,'AT&T Mobility');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2621,'312','680','us','United States of America',1,'AT&T Mobility'),
                                                                           (2622,'312','090','us','United States of America',1,'AT&T Mobility'),
                                                                           (2623,'311','380','us','United States of America',1,'AT&T Mobility'),
                                                                           (2624,'310','380','us','United States of America',1,'AT&T Mobility'),
                                                                           (2625,'313','680','us','United States of America',1,'AT&T Mobility'),
                                                                           (2626,'311','030','us','United States of America',1,'AT&T Mobility'),
                                                                           (2627,'310','070','us','United States of America',1,'AT&T Mobility'),
                                                                           (2628,'313','080','us','United States of America',1,'AT&T Mobility'),
                                                                           (2629,'312','280','us','United States of America',1,'AT&T Mobility'),
                                                                           (2630,'310','600','us','United States of America',1,'Cellcom');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2631,'310','630','us','United States of America',1,'Choice Wireless'),
                                                                           (2632,'310','700','us','United States of America',1,'cv cellular'),
                                                                           (2633,'310','880','us','United States of America',1,'DTC Wireless'),
                                                                           (2634,'310','35','us','United States of America',1,'ETEX'),
                                                                           (2635,'310','990','us','United States of America',1,'Evolve'),
                                                                           (2636,'310','299','us','United States of America',1,'Failed Calls'),
                                                                           (2637,'310','999','us','United States of America',1,'Fix Line'),
                                                                           (2638,'310','430','us','United States of America',1,'GCI Wireless'),
                                                                           (2639,'311','430','us','United States of America',1,'GCI Wireless'),
                                                                           (2640,'311','370','us','United States of America',1,'GCI Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2641,'310','690','us','United States of America',1,'Limitless'),
                                                                           (2642,'311','340','us','United States of America',1,'Limitless'),
                                                                           (2643,'311','690','us','United States of America',1,'Limitless'),
                                                                           (2644,'312','340','us','United States of America',1,'Limitless'),
                                                                           (2645,'310','340','us','United States of America',1,'Limitless'),
                                                                           (2646,'312','690','us','United States of America',1,'Limitless'),
                                                                           (2647,'310','299','us','United States of America',1,'Northstar Technology'),
                                                                           (2648,'315','299','us','United States of America',1,'Northstar Technology'),
                                                                           (2649,'311','299','us','United States of America',1,'Northstar Technology'),
                                                                           (2650,'316','299','us','United States of America',1,'Northstar Technology');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2651,'312','299','us','United States of America',1,'Northstar Technology'),
                                                                           (2652,'313','299','us','United States of America',1,'Northstar Technology'),
                                                                           (2653,'314','299','us','United States of America',1,'Northstar Technology'),
                                                                           (2654,'310','540','us','United States of America',1,'OWTC'),
                                                                           (2655,'311','570','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2656,'313','280','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2657,'313','360','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2658,'310','280','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2659,'311','280','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2660,'310','570','us','United States of America',1,'Pioneer Cellular');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2661,'313','570','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2662,'311','360','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2663,'310','360','us','United States of America',1,'Pioneer Cellular'),
                                                                           (2664,'312','870','us','United States of America',1,'T-Mobile'),
                                                                           (2665,'311','190','us','United States of America',1,'T-Mobile'),
                                                                           (2666,'310','870','us','United States of America',1,'T-Mobile'),
                                                                           (2667,'312','240','us','United States of America',1,'T-Mobile'),
                                                                           (2668,'310','250','us','United States of America',1,'T-Mobile'),
                                                                           (2669,'311','880','us','United States of America',1,'T-Mobile'),
                                                                           (2670,'316','490','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2671,'311','260','us','United States of America',1,'T-Mobile'),
                                                                           (2672,'316','120','us','United States of America',1,'T-Mobile'),
                                                                           (2673,'312','530','us','United States of America',1,'T-Mobile'),
                                                                           (2674,'310','530','us','United States of America',1,'T-Mobile'),
                                                                           (2675,'312','160','us','United States of America',1,'T-Mobile'),
                                                                           (2676,'316','870','us','United States of America',1,'T-Mobile'),
                                                                           (2677,'310','010','us','United States of America',1,'T-Mobile'),
                                                                           (2678,'311','660','us','United States of America',1,'T-Mobile'),
                                                                           (2679,'316','240','us','United States of America',1,'T-Mobile'),
                                                                           (2680,'312','880','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2681,'311','200','us','United States of America',1,'T-Mobile'),
                                                                           (2682,'310','880','us','United States of America',1,'T-Mobile'),
                                                                           (2683,'312','250','us','United States of America',1,'T-Mobile'),
                                                                           (2684,'310','260','us','United States of America',1,'T-Mobile'),
                                                                           (2685,'311','882','us','United States of America',1,'T-Mobile'),
                                                                           (2686,'316','530','us','United States of America',1,'T-Mobile'),
                                                                           (2687,'311','270','us','United States of America',1,'T-Mobile'),
                                                                           (2688,'316','160','us','United States of America',1,'T-Mobile'),
                                                                           (2689,'312','660','us','United States of America',1,'T-Mobile'),
                                                                           (2690,'311','010','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2691,'310','660','us','United States of America',1,'T-Mobile'),
                                                                           (2692,'312','190','us','United States of America',1,'T-Mobile'),
                                                                           (2693,'316','880','us','United States of America',1,'T-Mobile'),
                                                                           (2694,'310','200','us','United States of America',1,'T-Mobile'),
                                                                           (2695,'311','800','us','United States of America',1,'T-Mobile'),
                                                                           (2696,'316','250','us','United States of America',1,'T-Mobile'),
                                                                           (2697,'312','882','us','United States of America',1,'T-Mobile'),
                                                                           (2698,'311','230','us','United States of America',1,'T-Mobile'),
                                                                           (2699,'310','882','us','United States of America',1,'T-Mobile'),
                                                                           (2700,'312','260','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2701,'310','270','us','United States of America',1,'T-Mobile'),
                                                                           (2702,'311','940','us','United States of America',1,'T-Mobile'),
                                                                           (2703,'316','660','us','United States of America',1,'T-Mobile'),
                                                                           (2704,'310','190','us','United States of America',1,'T-Mobile'),
                                                                           (2705,'311','311','us','United States of America',1,'T-Mobile'),
                                                                           (2706,'316','190','us','United States of America',1,'T-Mobile'),
                                                                           (2707,'312','800','us','United States of America',1,'T-Mobile'),
                                                                           (2708,'311','120','us','United States of America',1,'T-Mobile'),
                                                                           (2709,'310','800','us','United States of America',1,'T-Mobile'),
                                                                           (2710,'312','200','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2711,'316','882','us','United States of America',1,'T-Mobile'),
                                                                           (2712,'310','230','us','United States of America',1,'T-Mobile'),
                                                                           (2713,'311','830','us','United States of America',1,'T-Mobile'),
                                                                           (2714,'316','260','us','United States of America',1,'T-Mobile'),
                                                                           (2715,'312','940','us','United States of America',1,'T-Mobile'),
                                                                           (2716,'311','240','us','United States of America',1,'T-Mobile'),
                                                                           (2717,'310','940','us','United States of America',1,'T-Mobile'),
                                                                           (2718,'312','270','us','United States of America',1,'T-Mobile'),
                                                                           (2719,'310','310','us','United States of America',1,'T-Mobile'),
                                                                           (2720,'312','010','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2721,'316','800','us','United States of America',1,'T-Mobile'),
                                                                           (2722,'310','160','us','United States of America',1,'T-Mobile'),
                                                                           (2723,'311','490','us','United States of America',1,'T-Mobile'),
                                                                           (2724,'316','200','us','United States of America',1,'T-Mobile'),
                                                                           (2725,'312','830','us','United States of America',1,'T-Mobile'),
                                                                           (2726,'311','160','us','United States of America',1,'T-Mobile'),
                                                                           (2727,'310','830','us','United States of America',1,'T-Mobile'),
                                                                           (2728,'312','230','us','United States of America',1,'T-Mobile'),
                                                                           (2729,'316','940','us','United States of America',1,'T-Mobile'),
                                                                           (2730,'310','240','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2731,'311','870','us','United States of America',1,'T-Mobile'),
                                                                           (2732,'316','270','us','United States of America',1,'T-Mobile'),
                                                                           (2733,'311','250','us','United States of America',1,'T-Mobile'),
                                                                           (2734,'316','010','us','United States of America',1,'T-Mobile'),
                                                                           (2735,'312','490','us','United States of America',1,'T-Mobile'),
                                                                           (2736,'310','490','us','United States of America',1,'T-Mobile'),
                                                                           (2737,'312','120','us','United States of America',1,'T-Mobile'),
                                                                           (2738,'316','830','us','United States of America',1,'T-Mobile'),
                                                                           (2739,'310','120','us','United States of America',1,'T-Mobile'),
                                                                           (2740,'311','530','us','United States of America',1,'T-Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2741,'316','230','us','United States of America',1,'T-Mobile'),
                                                                           (2742,'310','020','us','United States of America',1,'Union Wireless'),
                                                                           (2743,'310','287','us','United States of America',1,'Verizon Wireless'),
                                                                           (2744,'311','481','us','United States of America',1,'Verizon Wireless'),
                                                                           (2745,'312','487','us','United States of America',1,'Verizon Wireless'),
                                                                           (2746,'310','276','us','United States of America',1,'Verizon Wireless'),
                                                                           (2747,'310','895','us','United States of America',1,'Verizon Wireless'),
                                                                           (2748,'312','012','us','United States of America',1,'Verizon Wireless'),
                                                                           (2749,'310','598','us','United States of America',1,'Verizon Wireless'),
                                                                           (2750,'311','892','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2751,'312','898','us','United States of America',1,'Verizon Wireless'),
                                                                           (2752,'311','284','us','United States of America',1,'Verizon Wireless'),
                                                                           (2753,'312','350','us','United States of America',1,'Verizon Wireless'),
                                                                           (2754,'310','012','us','United States of America',1,'Verizon Wireless'),
                                                                           (2755,'310','489','us','United States of America',1,'Verizon Wireless'),
                                                                           (2756,'311','595','us','United States of America',1,'Verizon Wireless'),
                                                                           (2757,'312','820','us','United States of America',1,'Verizon Wireless'),
                                                                           (2758,'311','275','us','United States of America',1,'Verizon Wireless'),
                                                                           (2759,'312','281','us','United States of America',1,'Verizon Wireless'),
                                                                           (2760,'310','480','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2761,'311','486','us','United States of America',1,'Verizon Wireless'),
                                                                           (2762,'312','592','us','United States of America',1,'Verizon Wireless'),
                                                                           (2763,'310','281','us','United States of America',1,'Verizon Wireless'),
                                                                           (2764,'310','910','us','United States of America',1,'Verizon Wireless'),
                                                                           (2765,'312','272','us','United States of America',1,'Verizon Wireless'),
                                                                           (2766,'310','283','us','United States of America',1,'Verizon Wireless'),
                                                                           (2767,'311','289','us','United States of America',1,'Verizon Wireless'),
                                                                           (2768,'312','483','us','United States of America',1,'Verizon Wireless'),
                                                                           (2769,'310','272','us','United States of America',1,'Verizon Wireless'),
                                                                           (2770,'310','891','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2771,'311','897','us','United States of America',1,'Verizon Wireless'),
                                                                           (2772,'310','594','us','United States of America',1,'Verizon Wireless'),
                                                                           (2773,'311','770','us','United States of America',1,'Verizon Wireless'),
                                                                           (2774,'312','894','us','United States of America',1,'Verizon Wireless'),
                                                                           (2775,'311','280','us','United States of America',1,'Verizon Wireless'),
                                                                           (2776,'312','286','us','United States of America',1,'Verizon Wireless'),
                                                                           (2777,'310','485','us','United States of America',1,'Verizon Wireless'),
                                                                           (2778,'311','591','us','United States of America',1,'Verizon Wireless'),
                                                                           (2779,'312','597','us','United States of America',1,'Verizon Wireless'),
                                                                           (2780,'311','271','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2781,'312','277','us','United States of America',1,'Verizon Wireless'),
                                                                           (2782,'310','288','us','United States of America',1,'Verizon Wireless'),
                                                                           (2783,'311','482','us','United States of America',1,'Verizon Wireless'),
                                                                           (2784,'312','488','us','United States of America',1,'Verizon Wireless'),
                                                                           (2785,'310','277','us','United States of America',1,'Verizon Wireless'),
                                                                           (2786,'310','896','us','United States of America',1,'Verizon Wireless'),
                                                                           (2787,'312','013','us','United States of America',1,'Verizon Wireless'),
                                                                           (2788,'310','599','us','United States of America',1,'Verizon Wireless'),
                                                                           (2789,'311','893','us','United States of America',1,'Verizon Wireless'),
                                                                           (2790,'312','899','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2791,'311','285','us','United States of America',1,'Verizon Wireless'),
                                                                           (2792,'312','390','us','United States of America',1,'Verizon Wireless'),
                                                                           (2793,'310','013','us','United States of America',1,'Verizon Wireless'),
                                                                           (2794,'310','590','us','United States of America',1,'Verizon Wireless'),
                                                                           (2795,'311','596','us','United States of America',1,'Verizon Wireless'),
                                                                           (2796,'312','890','us','United States of America',1,'Verizon Wireless'),
                                                                           (2797,'311','276','us','United States of America',1,'Verizon Wireless'),
                                                                           (2798,'312','282','us','United States of America',1,'Verizon Wireless'),
                                                                           (2799,'310','481','us','United States of America',1,'Verizon Wireless'),
                                                                           (2800,'311','487','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2801,'311','288','us','United States of America',1,'Verizon Wireless'),
                                                                           (2802,'312','482','us','United States of America',1,'Verizon Wireless'),
                                                                           (2803,'310','271','us','United States of America',1,'Verizon Wireless'),
                                                                           (2804,'310','890','us','United States of America',1,'Verizon Wireless'),
                                                                           (2805,'310','593','us','United States of America',1,'Verizon Wireless'),
                                                                           (2806,'311','599','us','United States of America',1,'Verizon Wireless'),
                                                                           (2807,'312','893','us','United States of America',1,'Verizon Wireless'),
                                                                           (2808,'311','279','us','United States of America',1,'Verizon Wireless'),
                                                                           (2809,'312','285','us','United States of America',1,'Verizon Wireless'),
                                                                           (2810,'310','484','us','United States of America',1,'Verizon Wireless');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2811,'311','590','us','United States of America',1,'Verizon Wireless'),
                                                                           (2812,'312','596','us','United States of America',1,'Verizon Wireless'),
                                                                           (2813,'312','276','us','United States of America',1,'Verizon Wireless'),
                                                                           (2814,'310','450','us','United States of America',1,'Viaero Wireless'),
                                                                           (2815,'310','740','us','United States of America',1,'Viaero Wireless'),
                                                                           (2816,'310','860','us','United States of America',1,'West Central Wireless'),
                                                                           (2817,'748','01','uy','Uruguay',598,'Antel Movil'),
                                                                           (2818,'748','10','uy','Uruguay',598,'Claro'),
                                                                           (2819,'748','299','uy','Uruguay',598,'Failed Calls'),
                                                                           (2820,'748','999','uy','Uruguay',598,'Fix Line');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2821,'748','07','uy','Uruguay',598,'Movistar'),
                                                                           (2822,'434','04','uz','Uzbekistan',998,'Beeline'),
                                                                           (2823,'434','299','uz','Uzbekistan',998,'Failed Calls'),
                                                                           (2824,'434','999','uz','Uzbekistan',998,'Fix Line'),
                                                                           (2825,'434','06','uz','Uzbekistan',998,'Perfectum Mobile'),
                                                                           (2826,'434','05','uz','Uzbekistan',998,'Ucell'),
                                                                           (2827,'434','07','uz','Uzbekistan',998,'UMS'),
                                                                           (2828,'434','03','uz','Uzbekistan',998,'UzMobile'),
                                                                           (2829,'541','05','vu','Vanuatu',678,'Digicel'),
                                                                           (2830,'541','299','vu','Vanuatu',678,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2831,'541','999','vu','Vanuatu',678,'Fix Line'),
                                                                           (2832,'541','01','vu','Vanuatu',678,'Smile'),
                                                                           (2833,'225','299','va','Vatican',NULL,'Failed Calls'),
                                                                           (2834,'225','999','va','Vatican',NULL,'Fix Line'),
                                                                           (2835,'734','02','ve','Venezuela',58,'Digitel'),
                                                                           (2836,'734','299','ve','Venezuela',58,'Failed Calls'),
                                                                           (2837,'734','999','ve','Venezuela',58,'Fix Line'),
                                                                           (2838,'734','06','ve','Venezuela',58,'Movilnet'),
                                                                           (2839,'734','04','ve','Venezuela',58,'Movistar'),
                                                                           (2840,'452','299','vn','Vietnam',84,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2841,'452','999','vn','Vietnam',84,'Fix Line'),
                                                                           (2842,'452','07','vn','Vietnam',84,'Gmobile'),
                                                                           (2843,'452','08','vn','Vietnam',84,'I-Telecom'),
                                                                           (2844,'452','01','vn','Vietnam',84,'MobiFone'),
                                                                           (2845,'452','09','vn','Vietnam',84,'Reddi'),
                                                                           (2846,'452','05','vn','Vietnam',84,'Vietnamobile'),
                                                                           (2847,'452','06','vn','Vietnam',84,'Viettel'),
                                                                           (2848,'452','04','vn','Vietnam',84,'Viettel'),
                                                                           (2849,'452','02','vn','Vietnam',84,'VinaPhone'),
                                                                           (2850,'376','299','vi','Virgin Islands',1340,'Failed Calls');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2851,'376','999','vi','Virgin Islands',1340,'Fix Line'),
                                                                           (2852,'543','299','wf','Wallis and Futuna',NULL,'Failed Calls'),
                                                                           (2853,'543','999','wf','Wallis and Futuna',NULL,'Fix Line'),
                                                                           (2854,'543','01','wf','Wallis and Futuna',NULL,'Manuia'),
                                                                           (2855,'421','299','ye','Yemen',967,'Failed Calls'),
                                                                           (2856,'421','999','ye','Yemen',967,'Fix Line'),
                                                                           (2857,'421','01','ye','Yemen',967,'SabaFon'),
                                                                           (2858,'421','02','ye','Yemen',967,'Spacetel'),
                                                                           (2859,'421','04','ye','Yemen',967,'Y'),
                                                                           (2860,'421','03','ye','Yemen',967,'Yemen Mobile');
INSERT INTO kidjo.mccmnc (id,mcc,mnc,iso,country,country_code,network) VALUES
                                                                           (2861,'645','01','zm','Zambia',260,'Airtel'),
                                                                           (2862,'645','299','zm','Zambia',260,'Failed Calls'),
                                                                           (2863,'645','999','zm','Zambia',260,'Fix Line'),
                                                                           (2864,'645','02','zm','Zambia',260,'MTN'),
                                                                           (2865,'645','03','zm','Zambia',260,'Zamtel'),
                                                                           (2866,'648','04','zw','Zimbabwe',263,'Econet'),
                                                                           (2867,'648','299','zw','Zimbabwe',263,'Failed Calls'),
                                                                           (2868,'648','999','zw','Zimbabwe',263,'Fix Line'),
                                                                           (2869,'648','01','zw','Zimbabwe',263,'NetOne'),
                                                                           (2870,'648','03','zw','Zimbabwe',263,'Telecel');
