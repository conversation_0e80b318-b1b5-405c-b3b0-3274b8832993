CREATE TABLE `cafeyn_users_info`
(
    `id`           int          NOT NULL AUTO_INCREMENT,
    `user_id`      int          NOT NULL,
    `email`        varchar(255) NOT NULL,
    `partner_name` varchar(255) NOT NULL,
    `partner_id`   int          NOT NULL,
    `service_name` varchar(255) NOT NULL,
    `request_id`   int          NOT NULL,
    `event_type`   varchar(255) NOT NULL,
    `signature`    varchar(255) NOT NULL,
    `timestamp`    datetime     NOT NULL,
    `created_at`   timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`email`,`service_name`)
)