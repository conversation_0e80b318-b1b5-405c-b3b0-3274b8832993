-- kidjo.virgo_templates definition

CREATE TABLE `virgo_templates` (
                                   `id` smallint(6) NOT NULL AUTO_INCREMENT,
                                   `country_code` text NOT NULL,
                                   `template_id` bigint(20) NOT NULL DEFAULT '0',
                                   `is_active` tinyint(1) NOT NULL DEFAULT '1',
                                   `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                   `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `idx_virgo_templates_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;