CREATE OR REPLACE
    ALGORITHM = UNDEFINED VIEW `kidjo`.`account_subscription_view_V3` AS
SELECT
    CASE
        WHEN (u.id = 0 OR u.id IS NULL) AND s.id IS NOT NULL THEN s.id
        WHEN s.id IS NULL AND u.id <> 0 AND u.id IS NOT NULL THEN u.id
        ELSE s.id
        END AS id_account,
    IF(u.id = 0 OR u.email = '', NULL, u.email) AS email,
    IF(u.id = 0, NULL, u.id) AS id_user,
    s.id AS id_sub,
    IF(u.id = 0, NULL, u.created_at) AS registered_date,
    IF(u.id = 0 OR u.name = '', NULL, u.name) AS name,
    uc.name AS country,
    s.storeId,
    s.subscriptionType,
    CASE
        WHEN s.id IS NOT NULL AND DATE(s.nextBillDate) >= CURDATE() AND s.isActive THEN 1
        WHEN (s.id IS NOT NULL AND DATE(s.nextBillDate) < CURDATE()) OR NOT s.isActive THEN 0
        ELSE NULL
        END AS subStatus,
    s.iapId,
    s.nextBillDate,
    s.created_at AS createdDate,
    IF(s.accountCouponId = 0, NULL, s.accountCouponId) AS id_coupon,
    s.isTest AS is_test,
    a.couponId,
    a.startDate AS couponStartDate,
    CASE
        WHEN DATE(a.expireDate) >= CURDATE() AND CAST(a.redeemedTimes AS SIGNED) = 0 THEN 'PENDING_ACTIVATION'
        WHEN DATE(a.expireDate) >= CURDATE() AND CAST(a.redeemedTimes AS SIGNED) > 0 THEN 'ACTIVE'
        WHEN DATE(a.expireDate) < CURDATE() AND CAST(a.redeemedTimes AS SIGNED) > 0 THEN 'EXPIRED'
        WHEN DATE(a.expireDate) < CURDATE() AND CAST(a.redeemedTimes AS SIGNED) = 0 THEN 'INVALID'
        END AS couponStatus,
    at.name AS type,
    ap.name AS partner,
    ap.id AS id_partner,
    uc2.is_tc_accepted,
    uc2.is_promo_accepted
FROM kidjo.users u
         LEFT JOIN kidjo.countries uc ON uc.id = u.country_id
         LEFT JOIN kidjo.subscriptions_root s ON u.id = s.userId
         LEFT JOIN kidjo.account_coupons a ON a.id = s.accountCouponId
         LEFT JOIN kidjo.account_coupon_partner ap ON ap.id = a.id_partner
         LEFT JOIN kidjo.account_coupon_type at ON at.id = a.id_type
         LEFT JOIN kidjo.users_consent uc2 ON uc2.user_id = u.id ;