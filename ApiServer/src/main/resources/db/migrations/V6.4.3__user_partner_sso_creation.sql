CREATE TABLE users_partner_sso
(
    id                          BIGINT(20) PRIMARY KEY AUTO_INCREMENT,
    user_id                     BIGINT(20) UNSIGNED,
    partner_id                  BIGINT(20) UNSIGNED,
    is_active                   TINYINT(1) NOT NULL DEFAULT 1,

    partner_user_id             BIGINT(20) UNSIGNED,
    partner_partner_name        VA<PERSON>HA<PERSON>(255),
    partner_partner_description VARCHAR(255),
    partner_is_active           TINYINT(1),
    partner_is_sso_activated    TINYINT(1),
    partner_bundles             VARCHAR(1024),
    partner_subscribed_services VARCHAR(1024),
    email                       VARCHAR(255),

    UNIQUE KEY user_id_unique (user_id),
    FOREIG<PERSON> KEY (user_id) REFERENCES users (id),
    FOREI<PERSON><PERSON> KEY (partner_id) REFERENCES account_coupon_partner (id)
);
