DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS upsert_subscriptions()
BEGIN
INSERT INTO subscriptions_report_materialized_view (
    id_account,
    email,
    id_user,
    id_sub,
    registered_date,
    name,
    country,
    country_code,
    country_id,
    storeId,
    subscriptionType,
    subStatus,
    iapId,
    nextBillDate,
    createdDate,
    id_coupon,
    is_test,
    couponId,
    couponStartDate,
    couponStatus,
    coupon_type_id,
    type,
    coupon_type_description,
    partner,
    id_partner,
    partner_description,
    is_tc_accepted,
    is_promo_accepted
)
SELECT
    id_account,
    email,
    id_user,
    id_sub,
    registered_date,
    name,
    country,
    country_code,
    country_id,
    storeId,
    subscriptionType,
    subStatus,
    iapId,
    nextBillDate,
    createdDate,
    id_coupon,
    is_test,
    couponId,
    couponStartDate,
    couponStatus,
    coupon_type_id,
    type,
    coupon_type_description,
    partner,
    id_partner,
    partner_description,
    is_tc_accepted,
    is_promo_accepted
FROM account_subscription_view_V5 WHERE createdDate > (DATE_SUB(NOW(), INTERVAL 1 DAY)) OR subUpdatedDate > (DATE_SUB(NOW(), INTERVAL 1 DAY)) OR userUpdatedDate > (DATE_SUB(NOW(), INTERVAL 1 DAY))
ON DUPLICATE KEY UPDATE
    id_account = VALUES(id_account),
    email = VALUES(email),
    id_user = VALUES(id_user),
    registered_date = VALUES(registered_date),
    name = VALUES(name),
    country = VALUES(country),
    country_code = VALUES(country_code),
    country_id = VALUES(country_id),
    storeId = VALUES(storeId),
    subscriptionType = VALUES(subscriptionType),
    subStatus = VALUES(subStatus),
    iapId = VALUES(iapId),
    nextBillDate = VALUES(nextBillDate),
    createdDate = VALUES(createdDate),
    id_coupon = VALUES(id_coupon),
    is_test = VALUES(is_test),
    couponId = VALUES(couponId),
    couponStartDate = VALUES(couponStartDate),
    couponStatus = VALUES(couponStatus),
    coupon_type_id = VALUES(coupon_type_id),
    type = VALUES(type),
    coupon_type_description = VALUES(coupon_type_description),
    partner = VALUES(partner),
    id_partner = VALUES(id_partner),
    partner_description = VALUES(partner_description),
    is_tc_accepted = VALUES(is_tc_accepted),
    is_promo_accepted = VALUES(is_promo_accepted);
END $$

DELIMITER ;