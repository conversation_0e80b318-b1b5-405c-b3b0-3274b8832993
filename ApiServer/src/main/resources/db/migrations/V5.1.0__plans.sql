CREATE TABLE product
(
    id      int          not null auto_increment primary key,
    product varchar(256) not null
);

ALTER TABLE product
    AUTO_INCREMENT = 10;

CREATE TABLE plans
(
    id        int                       not null auto_increment primary key,
    productId int                       not null,
    planName  varchar(256)              not null,
    planType  enum ('monthly','yearly') not null default 'monthly',
    active    boolean                   not null default false,
    bundle    boolean                            default false,
    foreign key (productId) references product (id)
);

CREATE TABLE plans_country
(
    id        int auto_increment primary key,
    planId    int                  not null,
    countryId smallint(6) unsigned not null,
    price     decimal(4, 2)        not null,
    foreign key (countryId) references countries (id),
    foreign key (planId) references plans (id)

);

create table addons
(
    id        int           not null auto_increment primary key,
    add_on_id varchar(256)  not null,
    price     decimal(4, 2) not null,
    active    boolean default false
);


create table plans_addon
(
    id      int not null auto_increment primary key ,
    planId  int not null,
    addOnId int not null,
    active  boolean default false
);



INSERT INTO product(id, product)
values (10, 'KIDJO_TV'),
       (20, 'KIDJO_BOOKS'),
       (30, 'KIDJO_GAMES'),
       (40, 'KIDJ<PERSON>_TV_BOOKS'),
       (50, 'KIDJO_TV_GAMES'),
       (60, 'KIDJO_BOOKS_TV'),
       (70, 'KIDJO_BOOKS_GAMES'),
       (80, 'KIDJO_GAMES_TV'),
       (90, 'KIDJO_GAMES_BOOKS'),
       (100, 'KIDJO_TV_BOOKS_GAMES');

INSERT INTO plans(id, productId, planName, planType, active, bundle)
values (1, 10, 'default_one_product', 'monthly', true, false),
       (2, 10, 'default_one_product_yearly', 'yearly', true, false),
       (3, 10, 'europe_one_product', 'monthly', true, false),
       (4, 10, 'europe_one_product_yearly', 'yearly', true, false),
       (5, 10, 'swiss_one_product', 'monthly', true, false),
       (6, 10, 'swiss_one_product_yearly', 'yearly', true, false),
       -- Stories
       (7, 20, 'default_one_product', 'monthly', true, false),
       (8, 20, 'default_one_product_yearly', 'yearly', true, false),
       (9, 20, 'europe_one_product', 'monthly', true, false),
       (10, 20, 'europe_one_product_yearly', 'yearly', true, false),
       (11, 20, 'swiss_one_product', 'monthly', true, false),
       (12, 20, 'swiss_one_product_yearly', 'yearly', true, false),
       -- games
       (13, 30, 'default_one_product', 'monthly', true, false),
       (14, 30, 'default_one_product_yearly', 'yearly', true, false),
       (15, 30, 'europe_one_product', 'monthly', true, false),
       (16, 30, 'europe_one_product_yearly', 'yearly', true, false),
       (17, 30, 'swiss_one_product', 'monthly', true, false),
       (18, 30, 'swiss_one_product_yearly', 'yearly', true, false);
--  TODO added only for one product need to create plan for two product
-- EUROPE Plans
INSERT INTO plans_country(planId, countryId, price)
select 3, id, 6.00
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 4, id, 41.60
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 9, id, 6.00
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 10, id, 41.60
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 15, id, 6.00
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

INSERT INTO plans_country(planId, countryId, price)
select 16, id, 41.60
from countries c
where short in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA');

-- default plans
INSERT INTO plans_country(planId, countryId, price)
select 1, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 2, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 7, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 8, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 13, id, 4.99
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

INSERT INTO plans_country(planId, countryId, price)
select 14, id, 34.90
from countries c
where short not in
      ('AT', 'BE', 'BA', 'HR', 'CY', 'EE', 'FI', 'FR', 'DE', 'GR', 'IS', 'IE', 'IT', 'XK', 'LV', 'LT', 'LU', 'MT', 'ME',
       'NL', 'PT', 'SM', 'RS', 'SK', 'SI', 'VA', 'ES', 'CH');

-- Swiss plan
INSERT INTO plans_country(planId, countryId, price)
select 5, id, 7.50
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 6, id, 53.80
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 11, id, 7.50
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 12, id, 53.80
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 17, id, 7.50
from countries c
where short = 'CH';

INSERT INTO plans_country(planId, countryId, price)
select 18, id, 53.80
from countries c
where short = 'CH';
