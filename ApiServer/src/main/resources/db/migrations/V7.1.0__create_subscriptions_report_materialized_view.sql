-- kidjo.subscriptions_report_materialized_view definition

CREATE TABLE `subscriptions_report_materialized_view` (
    `id_account` bigint unsigned NOT NULL DEFAULT '0',
    `email` varchar(336) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
    `id_user` bigint unsigned DEFAULT NULL,
    `id_sub` bigint unsigned NOT NULL DEFAULT '0',
    `registered_date` varchar(19) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
    `country` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
    `country_code` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
    `country_id` int unsigned DEFAULT NULL,
    `storeId` enum('coupon','ios','playstore','amazon','samsung','kidjo','mondia_media','swisscom','docomo','orange','virgo','huawei','twt','cafeyn','android_tv','switch','mondia','salt','salt2') DEFAULT NULL,
    `subscriptionType` enum('kidjo_books','kidjo_tv','kidjo_tv_books','kidjo_games','kidjo_tv_books_games','kidjo_tv_games','kidjo_books_games') NOT NULL DEFAULT 'kidjo_books',
    `subStatus` int DEFAULT NULL,
    `iapId` varchar(255) NOT NULL,
    `nextBillDate` datetime NOT NULL,
    `createdDate` timestamp NOT NULL,
    `id_coupon` bigint unsigned DEFAULT NULL,
    `is_test` tinyint(1) NOT NULL DEFAULT '0',
    `couponId` varchar(30) DEFAULT NULL,
    `couponStartDate` datetime DEFAULT NULL,
    `couponStatus` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `coupon_type_id` bigint unsigned DEFAULT NULL,
    `type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
    `coupon_type_description` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
    `partner` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
    `id_partner` bigint unsigned DEFAULT NULL,
    `partner_description` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
    `is_tc_accepted` tinyint(1) DEFAULT NULL,
    `is_promo_accepted` tinyint(1) DEFAULT NULL,
    KEY `idx_id_sub_index` (`id_sub`),
    KEY `idx_subStatus_index` (`subStatus`),
    KEY `idx_id_partner_index` (`id_partner`),
    KEY `idx_storeId_index` (`storeId`),
    KEY `idx_country_id_index` (`country_id`),
    KEY `idx_coupon_type_id_index` (`coupon_type_id`),
    KEY `idx_subscriptionType_index` (`subscriptionType`),
    KEY `idx_createdDate_index` (`createdDate`),
    KEY `idx_nextBillDate_index` (`nextBillDate`),
    KEY `idx_storeId_createdDate` (`createdDate`,`storeId`),
    KEY `idx_storeId_nextBillDate` (`nextBillDate`,`storeId`),
    KEY `idx_subType_createdDate` (`createdDate`,`subscriptionType`),
    KEY `idx_subType_nextBillDate` (`nextBillDate`,`subscriptionType`),
    KEY `idx_id_partner_createdDate` (`createdDate`,`id_partner`),
    KEY `idx_id_partner_nextBillDate` (`nextBillDate`,`id_partner`),
    KEY `idx_coupon_type_id_createdDate` (`createdDate`,`coupon_type_id`),
    KEY `idx_coupon_type_id_nextBillDate` (`nextBillDate`,`coupon_type_id`),
    KEY `idx_type_id_createdDate` (`createdDate`,`type`),
    KEY `idx_type_id_nextBillDate` (`nextBillDate`,`type`),
    KEY `idx_country_code_createdDate` (`createdDate`,`country_code`),
    KEY `idx_country_code_nextBillDate` (`nextBillDate`,`country_code`),
    KEY `idx_country_id_createdDate` (`createdDate`,`country_id`),
    KEY `idx_country_id_nextBillDate` (`nextBillDate`,`country_id`),
    KEY `idx_subStatus_createdDate` (`createdDate`,`subStatus`),
    KEY `idx_subStatus_nextBillDate` (`nextBillDate`,`subStatus`),
    KEY `idx_createdDate_email_name_coupon_id` (`createdDate`,`email`,`name`,`couponId`),
    KEY `idx_nextBillDate_email_name_coupon_id` (`nextBillDate`,`email`,`name`,`couponId`),
    KEY `idx_createdDate_storeId_subType` (`createdDate`,`storeId`,`subscriptionType`),
    KEY `idx_nextBillDate_storeId_subType` (`nextBillDate`,`storeId`,`subscriptionType`),
    KEY `idx_createdDate_country_code_subscriptionType` (`createdDate`,`country_code`,`subscriptionType`),
    KEY `idx_nextBillDate_country_code_subscriptionType` (`nextBillDate`,`country_code`,`subscriptionType`),
    KEY `idx_createdDate_country_code_storeId` (`createdDate`,`country_code`,`storeId`),
    KEY `idx_nextBillDate_country_code_storeId` (`nextBillDate`,`country_code`,`storeId`)
);