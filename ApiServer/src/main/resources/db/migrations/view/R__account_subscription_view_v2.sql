-- kidjo.account_subscription_view_V2 source

CREATE OR REPLACE
    ALGORITHM = UNDEFINED VIEW `account_subscription_view_V2` AS
select
    (case
         when (((`u`.`id` = 0)
             or isnull((`u`.`id` = 0)))
             and (`s`.`id` is not null)) then `s`.`id`
         when (isnull(`s`.`id`)
             and (`u`.`id` <> 0)
             and (`u`.`id` is not null)) then `u`.`id`
         else `s`.`id`
        end) AS `id_account`,
    if(((`u`.`id` = 0)
        or (`u`.`email` = '')),
       NULL,
       `u`.`email`) AS `email`,
    if((`u`.`id` = 0),
       NULL,
       `u`.`id`) AS `id_user`,
    `s`.`id` AS `id_sub`,
    if((`u`.`id` = 0),
       NULL,
       `u`.`created_at`) AS `registered_date`,
    if(((`u`.`id` = 0)
        or (`u`.`name` = '')),
       NULL,
       `u`.`name`) AS `name`,
    `uc`.`name` AS `country`,
    `s`.`storeId` AS `storeId`,
    `s`.`subscriptionType` AS `subscriptionType`,
    (case
         when ((`s`.`id` is not null)
             and (cast(`s`.`nextBillDate` as date) >= cast(now() as date))
             and (`s`.`isActive` is true)) then 1
         when (((`s`.`id` is not null)
             and (cast(`s`.`nextBillDate` as date) < cast(now() as date)))
             or (`s`.`isActive` is false)) then 0
         else NULL
        end) AS `subStatus`,
    `s`.`iapId` AS `iapId`,
    `s`.`nextBillDate` AS `nextBillDate`,
    `s`.`created_at` AS `createdDate`,
    if((`s`.`accountCouponId` = 0),
       NULL,
       `s`.`accountCouponId`) AS `id_coupon`,
    `s`.`isTest` AS `is_test`,
    `a`.`couponId` AS `couponId`,
    `a`.`startDate` AS `couponStartDate`,
    (case
         when ((cast(`a`.`expireDate` as date) >= cast(now() as date))
             and (cast(`a`.`redeemedTimes` as signed) = 0)) then 'PENDING_ACTIVATION'
         when ((cast(`a`.`expireDate` as date) >= cast(now() as date))
             and (cast(`a`.`redeemedTimes` as signed) > 0)) then 'ACTIVE'
         when ((cast(`a`.`expireDate` as date) < cast(now() as date))
             and (cast(`a`.`redeemedTimes` as signed) > 0)) then 'EXPIRED'
         when ((cast(`a`.`expireDate` as date) < cast(now() as date))
             and (cast(`a`.`redeemedTimes` as signed) = 0)) then 'INVALID'
        end) AS `couponStatus`,
    `at`.`name` AS `type`,
    `ap`.`name` AS `partner`,
    `ap`.`id` AS `id_partner`
from
    (((((`users` `u`
        left join `countries` `uc` on
        ((`uc`.`id` = `u`.`country_id`)))
        left join `subscriptions_root` `s` on
        ((`u`.`id` = `s`.`userId`)))
        left join `account_coupons` `a` on
        ((`a`.`id` = `s`.`accountCouponId`)))
        left join `account_coupon_partner` `ap` on
        ((`ap`.`id` = `a`.`id_partner`)))
        left join `account_coupon_type` `at` on
        ((`at`.`id` = `a`.`id_type`)));
