CREATE TABLE partner_sms
(
    id           INT AUTO_INCREMENT PRIMARY KEY,
    partner_name VARCHAR(255) NOT NULL,
    telecom      VARCHAR(255) NOT NULL,
    package_id   INT          NOT NULL,
    subscription VARCHAR(50),
    message      TEXT         NOT NULL,
    created      DATE         NOT NULL,
    updated      DATE         NOT NULL,
    description  TEXT,
    isActive     BOOLEAN      NOT NULL
);

INSERT INTO partner_sms (telecom, package_id, subscription, message, created, updated, partner_name, description,
                         isActive)
VALUES ('orange_senegal', 26045, 'Daily',
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 200F/jour. Désabo : envoyez STOP KIDJO au 21302',
        CURDATE(), CURDATE(), 'Digital Virgo',
        null,
        TRUE),
       ('orange_senegal', 0, null,
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 200F/jour. Désabo : envoyez STOP KIDJO au 21302',
        CURDATE(), CURDATE(), 'Digital Virgo',
        'If not these packages above we will send this message',
        TRUE),

       ('orange_ivory_coast', 26044, 'Daily',
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 150F/jour. Désabo : envoyez STOP KID au 7720',
        CURDATE(), CURDATE(), 'Digital Virgo',
        null,
        TRUE),
       ('orange_ivory_coast', 0, null,
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 150F/jour. Désabo : envoyez STOP KID au 7720',
        CURDATE(), CURDATE(), 'Digital Virgo',
        'If not these packages above we will send this message',
        TRUE),

       ('orange_morocco', 30779, 'Weekly',
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 10dh/Semaine. Desabo : envoyez STOP KJ7 au 7018',
        CURDATE(), CURDATE(), 'Digital Virgo',
        null,
        TRUE),
       ('orange_morocco', 30780, 'Monthly',
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 35dh/Mois. Desabo : envoyez STOP KJ30 au 7018',
        CURDATE(), CURDATE(), 'Digital Virgo',
        null,
        TRUE),
       ('orange_morocco', 0, null,
        'Bienvenue sur Kidjo! Voici vos accès: Login _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 10dh/Semaine. Desabo : envoyez STOP KJ7 au 7018',
        CURDATE(), CURDATE(), 'Digital Virgo',
        'If not these packages above we will send this message',
        TRUE),


       ('Morocco Telecom (IAM)', 30970, 'Weekly',
        'Bienvenue sur Kidjo! Voici vos accès: Login: _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 10dh/Semaine. Désabo sur : https://www.mes-abonnements.ma/',
        CURDATE(), CURDATE(), 'Digital Virgo',
        null, TRUE),
       ('Morocco Telecom (IAM)', 30971, 'Monthly',
        'Bienvenue sur Kidjo! Voici vos accès: Login: _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 35dh/mois. Désabo sur : https://www.mes-abonnements.ma/',
        CURDATE(), CURDATE(), 'Digital Virgo',
        null, TRUE),
       ('Morocco Telecom (IAM)', 0, null,
        'Bienvenue sur Kidjo! Voici vos accès: Login: _{UserLogin} Password: _{UserPwd} RDV sur: _{UserUrl} 10dh/Semaine. Désabo sur : https://www.mes-abonnements.ma/',
        CURDATE(), CURDATE(), 'Digital Virgo',
        'If not these packages above we will send this message',
        TRUE)
;
