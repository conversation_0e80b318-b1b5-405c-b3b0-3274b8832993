CREATE TABLE discounts
(
    id           int          not null auto_increment primary key,
    discountName varchar(256) not null,
    active       boolean default false
);

CREATE TABLE discounts_plans
(
    id         int not null auto_increment primary key,
    planId     int not null,
    discountId int not null,
    FOREIGN KEY (discountId) references discounts (id),
    FOREIGN KEY (planId) references plans (id)
);
CREATE TABLE discount_country
(
    id            int                  not null auto_increment primary key,
    discountId    int                  not null,
    countryId     smallint(6) unsigned not null,
    discountPrice decimal(4, 2)        not null,
    foreign key (discountId) references discounts (id),
    foreign key (countryId) references countries (id)
);

CREATE TABLE discounts_coupons
(
    id         int not null auto_increment primary key,
    couponId   int not null,
    discountId int not null
);
