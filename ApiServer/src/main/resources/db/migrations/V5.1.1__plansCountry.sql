ALTER TABLE plans_country
    add column currency varchar(10) DEFAULT 'USD';

ALTER TABLE plans_country
    add column currencySymbol varchar(10) DEFAULT '$';


UPDATE plans_country
set plans_country.currency='EUR',plans_country.currencySymbol='€'
where planId in(3,4,9,10,15,16);

UPDATE plans_country
set plans_country.currency='CHF',plans_country.currencySymbol='CHF'
where planId in(5,6,11,12,17,18);
