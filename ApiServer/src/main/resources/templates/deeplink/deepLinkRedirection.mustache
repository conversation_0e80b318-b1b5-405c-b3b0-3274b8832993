<script>
    function getMobileOperatingSystem() {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/android/i.test(userAgent)) {
            return "android";
        }

        // iOS detection from: http://stackoverflow.com/a/9039885/177710
        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            return "ios";
        }

        return null;
    }

    const mobileOs = getMobileOperatingSystem();
    const redirectionAndroid = "{{&g_redirectionAndroid}}";
    const redirectionIos = "{{&g_redirectionIos}}";
    const redirectionFallback = "{{&g_redirectionFallback}}";
    document.location = mobileOs === "android" ? redirectionAndroid : mobileOs === "ios" ? redirectionIos : redirectionFallback;
</script>
