<!DOCTYPE html>
<html lang="{{& g_languageShort }}" dir="auto">
<head>
    <!-- Basic Page Needs
    ================================================== -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta content="text/html; charset=UTF-8" name="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Mobile Specific Metas
    ================================================== -->
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <meta name="description" content="{{& g_pageDescription }}">
    <meta name="author" content="kidjo.tv" />
    <meta name="distribution" content="global" />
    <meta name="resource-type" content="document" />
    <meta name="language" content="{{& g_languageShort}}" />

    <meta name="application-name" content="Kidjo"/>
    <link rel="apple-touch-icon" sizes="128x128" href="{{& g_assetRoot}}webApp/images/app_icon/app_icon_128.png">
    <link rel="apple-touch-icon" sizes="256x256" href="{{& g_assetRoot}}webApp/images/app_icon/app_icon_256.png">
    <link rel="shortcut icon" type="image/x-icon" href="{{& g_assetRoot}}webApp/images/app_icon/app_icon.ico"/>
    <link rel="icon" type="image/svg" href="{{& g_assetRoot}}webApp/images/app_icon/app_icon.svg">
    <link rel="mask-icon" href="{{& g_assetRoot}}webApp/images/app_icon/app_pinned_tab.svg" color="#fff">
    <meta name="msapplication-TileColor" content="#3dc6b4"/>
    <meta name="theme-color" content="#3bb696" />

    <title>{{& g_headerTitle}}</title>
    <meta name="copyright" content="kidjo.tv" />
    <link href="{{& g_cssUrl}}" rel="stylesheet">

    <!-- Consent Management Platform -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/cookieconsent@3/build/cookieconsent.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/cookieconsent@3/build/cookieconsent.min.js"></script>
    <script>
        window.cookieconsent.initialise({
            palette: {
                popup: {
                    background: '#4f4f4f',
                    text: '#ffffff'
                },
                button: {
                    background: '#3dc6b4',
                    text: '#ffffff'
                },
            },
            type: 'opt-out',
            theme: 'edgeless',
            content: {
                message: "{{& cmpText}}",
                dismiss: 'Ok!',
                allow: "{{& cmpAllow}}",
                deny: "{{& cmpDeny}}",
                link: "{{& cmpMore}}",
                href: '{{& g_languageShort }}' != 'en' ? 'https://www.kidjo.tv/' + '{{& g_languageShort }}' + '/cookies' : 'https://www.kidjo.tv/cookies',
            },
            cookie: {
                name: 'cookieconsent_status',
            },
            onStatusChange: function(status) {
                var type = this.options.type;
                var didConsent = this.hasConsented();
                if (type == 'opt-out' && !didConsent) {
                    // Disable analytics and related cookies for user if needed
                }
            },
            law: {
                regionalLaw: false,
            },
            location: {
                serviceDefinitions: {
                    kidjoApiService: function(options) {
                        return {
                            url: 'https://api.kidjo.tv/v2/country',
                            callback: function(done, response) {
                                // This function must parse the 'response' and return the country code, or fail.
                                // If this function doesn't fail correctly, then the next service will not run.
                                // Therefore, it's generally best to add a <em>try {...} catch () {...}</em> block
                                try {
                                    var json = JSON.parse(response);
                                    if (json.geoplugin_countryCode) {
                                        return {code: json.geoplugin_countryCode}
                                    }
                                    throw 'Could not find a country code in the response';
                                } catch (err) {
                                    return new Error('Invalid response (' + err + ')');
                                }
                            },
                        };
                    },
                },
                services: [
                    'kidjoApiService'
                ]
            }
        });
    </script>
</head>
