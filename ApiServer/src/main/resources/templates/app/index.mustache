<!DOCTYPE html>
<html lang="{{& g_languageShort }}" dir="auto">
<head>
    <!-- Base
   ================================================== -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta content="text/html; charset=UTF-8" name="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Mobile
    ================================================== -->
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Meta
    ================================================== -->
    <meta name="description" content="{{& g_pageDescription }}">
    <meta name="author" content="kidjo.tv" />
    <meta name="copyright" content="kidjo.tv" />
    <meta name="distribution" content="global" />
    <meta name="resource-type" content="document" />


    <!-- App
    ================================================== -->
    <title>Kidjo</title>
    <link rel="manifest" href="/app/manifest">
    <meta name="language" content="{{& g_languageShort }}" />
    <meta name="application-name" content="Kidjo"/>

    <!-- icons
    ================================================== -->
    <link rel="apple-touch-icon" sizes="128x128" href="{{& g_assetRoot}}images/app_icon/app_icon_128.png">
    <link rel="apple-touch-icon" sizes="256x256" href="{{& g_assetRoot}}images/app_icon/app_icon_256.png">
    <link rel="shortcut icon" type="image/x-icon" href="{{& g_assetRoot}}images/app_icon/app_icon.ico"/>
    <link rel="icon" type="image/svg" href="{{& g_assetRoot}}images/app_icon/app_icon.svg">
    <link rel="mask-icon" href="{{& g_assetRoot}}images/app_icon/app_pinned_tab.svg" color="#fff">
    <meta name="msapplication-TileColor" content="#3dc6b4"/>
    <meta name="theme-color" content="#3bb696" />

    <!-- CSS
    ================================================== -->
    <link href="{{& g_cssUrl}}?v={{& g_webAppVersion}}" rel="stylesheet">
</head>
    <body>
        <div id="app-container" class="app-container">
        </div>
        <div id="loading-container" class="loading-container hidden">
            <div class="loading-card">
                <div class="loading-text">
                    {{& accountLoadingMessage}}
                </div>
            </div>
        </div>
        <div id="boot-overlay" class="boot-overlay"></div>
        <script>
         // Fetch country and set splash screen
         const setSplashScreenImage = (image) => {
            const imageHostUrl = "https://s3.amazonaws.com/kidjo/public/webApp/images/"
            const width = window.innerWidth;
            const height = window.innerHeight;

            // if it's a phone in portrait mode, else if it's a desktop/tablet/phone
            if (height / width > 1.2) {
                document.getElementById("boot-overlay").style.backgroundImage = "url('" + imageHostUrl + "phone-p/" + image + "')";
            } else {
                // check screen size and set appropiate image size for background
                if (width > 1000) {
                    document.getElementById("boot-overlay").style.backgroundImage = "url('" + imageHostUrl + "phone-m/" + image + "')";
                } else {
                    document.getElementById("boot-overlay").style.backgroundImage = "url('" + imageHostUrl + "phone-s/" + image + "')";
                }
            }

            document.getElementById("boot-overlay").style.backgroundSize = "100% 100%";
            document.getElementById("boot-overlay").style.backgroundRepeat = "no-repeat";
         };

         var xhttp;
         if (window.XMLHttpRequest) {
           // code for modern browsers
           xhttp = new XMLHttpRequest();
           } else {
           // code for IE6, IE5
           xhttp = new ActiveXObject("Microsoft.XMLHTTP");
         }
         xhttp.onreadystatechange = function() {

            if (this.readyState == 4) {
                 document.getElementById("boot-overlay").style.display = "block";
                 setTimeout(() => document.getElementById("boot-overlay").style.display = "none", 2500);
            }

           if (this.readyState == 4 && this.status == 200) {
             const tunisiaCountryCode = "TN";
             try {
                 const response = JSON.parse(this.response);

                 if (response.geoplugin_countryCode === tunisiaCountryCode) {
                    console.log("Country is Tunisia!");

                    setSplashScreenImage("byOrange.png");
                 } else {
                    console.log("Country is not Tunisia!");
                 }
             } catch (e) {
                console.log(e);
             }

           }
         };

         xhttp.open("GET", "https://api.kidjo.tv/v2/country", true);
         xhttp.send();

         setSplashScreenImage("regular.png");
        </script>
        <script>
            // Check if old local storage is in place, if it is, clear it
            var favs = localStorage.getItem("favs_numberOf");
            if (favs && typeof favs === "string" && parseInt(favs, 10) >= 0) {
                console.log("Favorites are good, continue ...");
            } else {
                console.log("Favorites are bad, reseting local storage ...");
                localStorage.clear();
            }
        </script>
        <script>var bootData={{&g_embeddedData}};</script>
        {{#g_isProd}}
            {{#g_includeShakaPlayer}}
                <script src="{{& g_assetRoot}}js/shaka.js.gz?v={{& g_webAppVersion}}"></script>
            {{/g_includeShakaPlayer}}
            <script src="{{& g_assetRoot}}js/kotlin_app.js.gz?v={{& g_webAppVersion}}"></script>
        {{/g_isProd}}{{^g_isProd}}
            {{#g_includeShakaPlayer}}
                <script src="{{& g_assetRoot}}js/shaka.js?v={{& g_webAppVersion}}"></script>
            {{/g_includeShakaPlayer}}
            <script src="{{& g_assetRoot}}js/animations.js?v={{& g_webAppVersion}}"></script>
            <script src="{{& g_assetRoot}}js/sha1.js?v={{& g_webAppVersion}}"></script>
            <script src="{{& g_assetRoot}}js/kotlin.js?v={{& g_webAppVersion}}"></script>
            <script src="{{& g_assetRoot}}js/app.js?v={{& g_webAppVersion}}"></script>
        {{/g_isProd}}
    </body>
</html>
