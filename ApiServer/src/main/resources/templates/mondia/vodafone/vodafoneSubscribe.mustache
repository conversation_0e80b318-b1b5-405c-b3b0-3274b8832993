<head>
<!-- Google Tag Manager -->
<script>
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NN3JVG3');
</script>
<!-- End Google Tag Manager -->
</head>

<script async=""
        src="https://js.ad-score.com/score.min.js?pid=1000217#tid=null&amp;l1=promo.mondiamedia.com&amp;l2=\&amp;l3=null&amp;l4=null&amp;l5=null&amp;pub_app=duPortal&amp;utid=null&amp;pub_domain=promo.mondiamedia.com&amp;uip=null&amp;ref=null"></script>
<meta name="viewport" content="width=device-width,initial-scale=1">
<style>
    html, body {
        background-color: rgb(255, 255, 255);
    }

    html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
        margin: 0;
        padding: 0;
        border: 0;
        box-sizing: border-box
    }

    .ico-cross {
        position: absolute;
        top: 0;
        right: 0;
        padding: 15px;
        z-index: 999;
    }
    .logo {
        padding: 0.2em .8em;
    }

    [role=button],
    input[type=submit],
    input[type=reset],
    input[type=button],
    button {
        -webkit-box-sizing: content-box;
        -moz-box-sizing: content-box;
        box-sizing: content-box
    }

    input[type=submit],
    input[type=reset],
    input[type=button],
    button {
        background: 0;
        border: 0;
        color: inherit;
        font: inherit;
        line-height: normal;
        overflow: visible;
        padding: 0;
        -webkit-appearance: button;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none
    }

    input::-moz-focus-inner,
    button::-moz-focus-inner {
        border: 0;
        padding: 0
    }

    input,
    label,
    select,
    button,
    textarea {
        margin: 0;
        border: 0;
        padding: 0;
        display: inline-block;
        vertical-align: middle;
        white-space: normal;
        background: 0;
        line-height: 1;
        font-size: 13px;
        font-family: Arial
    }

    input:focus {
        outline: 0
    }

    input,
    textarea {
        -webkit-box-sizing: content-box;
        -moz-box-sizing: content-box;
        box-sizing: content-box
    }

    button,
    input[type=reset],
    input[type=button],
    input[type=submit],
    input[type=checkbox],
    input[type=radio],
    select {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box
    }

    input[type=checkbox],
    input[type=radio] {
        width: 13px;
        height: 13px
    }

    input[type=search] {
        -webkit-appearance: textfield;
        -webkit-box-sizing: content-box
    }

    ::-webkit-search-decoration {
        display: none
    }

    button,
    input[type=reset],
    input[type=button],
    input[type=submit] {
        overflow: visible;
        width: auto
    }

    ::-webkit-file-upload-button {
        padding: 0;
        border: 0;
        background: 0
    }

    textarea {
        vertical-align: top;
        overflow: auto
    }

    select[multiple] {
        vertical-align: top
    }

    .center-text {
        text-align: center;
        text-align: -webkit-center
    }

    .bold {
        font-weight: 800
    }

    .light {
        font-weight: 400
    }

    .no-padding {
        padding: 0 !important
    }

    ::placeholder {
        color: #fff;
        opacity: 1
    }

    :-ms-input-placeholder {
        color: #fff
    }

    ::-ms-input-placeholder {
        color: #fff
    }

    html,
    body {
        width: 100%;
        font-family: sans-serif
    }

    body {
        max-width: 500px;
        height: auto;
        margin-right: auto;
        margin-left: auto;
        padding-bottom: 2em;
        min-height: 100vh
    }

    .wrapper {
        width: 100%;
        position: relative;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        text-align: center;
        text-align: -moz-center;
        text-align: -webkit-center;
        font-weight: 400
    }

    .content {
        padding: .2em 1em
    }

    .content .backdrop-image {
        width: 100%
    }

    .content .backdrop-image img {
        width: 100%
    }

    .content .header {
        text-transform: uppercase;
        line-height: 1.2em;
        color: #fff;
        font-size: 1.3em;
        font-weight: 700
    }

    .content .header.header-error {
        text-transform: initial;
        font-weight: 500
    }

    .content .sub-text {
        font-size: .92em;
        text-transform: normal;
        color: #cac2c2;
        font-weight: 100
    }

    .content .txtbx {
        border: 2px solid #fff;
        width: 95%;
        border-radius: 1em;
        margin-bottom: .6em;
        margin-left: auto;
        margin-right: auto
    }

    .content .txtbx .txt1,
    .content .txtbx .txt2 {
        color: #fff;
        background-color: transparent;
        border: 0;
        height: 5.25em;
        box-sizing: border-box;
        text-align: center;
        font-size: 1em;
        border-radius: 0
    }

    .content .txtbx .txt1 {
        border-right: 2px solid #fff;
        width: 24%
    }

    .content .txtbx .txt2 {
        width: 73%
    }

    .content .price {
        color: #cac2c2;
        font-size: 1em;
        font-weight: inherit
    }

    .content #countdown_container {
        font-size: 1.3em;
        font-weight: 400;
        color: #fff
    }

    .content .Sub-Button {
        display: block;
        line-height: 2.3em;
        width: 95%;
        color: #fff;
        border: 0;
        border-radius: 1em;
        font-size: 1.3em;
        font-weight: 400;
        background-color: #2ea25e;
        text-decoration: none
    }

    .content #confirm-btn {
        color: #fff;
        background-color: #2ea25e
    }

    .content .disclaimer {
        color: #cac2c2;
        font-size: 8pt
    }

    .content .disclaimer p {
        width: 80%;
        text-align: left;
        margin-left: auto;
        margin-right: auto
    }

    body.kidjo1 {
        background-color: #ffffff;
        padding-bottom: 20px
    }

    body.kidjo1 .wrapper .content .header {
        color: #212121;
        font-size: 0.9em;
        padding: 6px 20px 6px;
        height: auto;
    }

    body.kidjo1 .wrapper .content .header2 {
        color: #212121;
        font-size: 1em;
    }

    body.kidjo1 .wrapper .content .header3 {
        color: #212121;
        font-size: 0.75em;
        font-weight: 300;
    }

    body.kidjo1 .wrapper .content .Sub-Button {
        background-color: #2ea25e;
        border: 0;
        color: #FFF
    }

    body.kidjo1 .wrapper .content .sub-text {
        color: #fff
    }

    body.kidjo1 .wrapper .content .price {
        color: #212121;
        text-align: -webkit-right;
    }

    .terms {
        font-size: 14px
    }


    @media only screen and (min-device-width: 375px) and (max-device-width: 413px) {
        html,
        body {
            width: 100%;
            font-family: sans-serif
        }

        body {
            max-width: 500px;
            height: auto;
            margin-right: auto;
            margin-left: auto;
            padding-bottom: 1.8em;
            min-height: 100vh
        }

        .wrapper {
            width: 100%;
            position: relative;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            text-align: center;
            text-align: -moz-center;
            text-align: -webkit-center;
            font-weight: 400
        }

        .content .backdrop-image {
            width: 100%
        }

        .content .backdrop-image img {
            width: 100%
        }

        .content .header {
            text-transform: uppercase;
            line-height: 1.2em;
            color: #fff;
            font-size: 1.1700000000000002em;
            font-weight: 700;
        }

        .content .header.header-error {
            text-transform: initial;
            font-weight: 500
        }

        .content .sub-text {
            font-size: .8280000000000001em;
            text-transform: normal;
            color: #cac2c2;
            font-weight: 100
        }

        .content .txtbx {
            border: 2px solid #fff;
            width: 95%;
            margin-bottom: .54em;
            margin-left: auto;
            margin-right: auto
        }

        .content .txtbx .txt1,
        .content .txtbx .txt2 {
            color: #fff;
            background-color: transparent;
            border: 0;
            height: 5.25em;
            box-sizing: border-box;
            text-align: center;
            font-size: .9em;
            border-radius: 0
        }

        .content .txtbx .txt1 {
            border-right: 2px solid #fff;
            width: 24%
        }

        .content .txtbx .txt2 {
            width: 73%
        }

        .content .price {
            color: #cac2c2;
            font-size: .85em;
            font-weight: inherit
        }

        .content #countdown_container {
            font-size: 1.1700000000000002em;
            font-weight: 400;
            color: #fff
        }

        .content .Sub-Button {
            display: block;
            line-height: 2.3em;
            width: 95%;
            color: #fff;
            border: 0;
            font-size: 1.2em;
            font-weight: 400;
            background-color: #2ea25e;
            text-decoration: none
        }

        .content #confirm-btn {
            color: #fff;
            background-color: #2ea25e
        }

        .content .disclaimer {
            color: #cac2c2;
            font-size: 8pt
        }

        .content .disclaimer p {
            width: 80%;
            text-align: left;
            margin-left: auto;
            margin-right: auto
        }

    }

    @media only screen and (max-device-width: 374px) {
        html,
        body {
            width: 100%;
            font-family: sans-serif
        }

        body {
            max-width: 500px;
            height: auto;
            margin-right: auto;
            margin-left: auto;
            padding-bottom: 1.6em;
            min-height: 100vh
        }

        .wrapper {
            width: 100%;
            position: relative;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            text-align: center;
            text-align: -moz-center;
            text-align: -webkit-center;
            font-weight: 400
        }

        .content .backdrop-image {
            width: 100%
        }

        .content .backdrop-image img {
            width: 100%
        }

        .content .header {
            text-transform: uppercase;
            line-height: 1.2em;
            color: #fff;
            font-size: 1.04em;
            font-weight: 700
        }

        .content .header.header-error {
            text-transform: initial;
            font-weight: 500
        }

        .content .sub-text {
            font-size: .7360000000000001em;
            text-transform: normal;
            color: #cac2c2;
            font-weight: 100
        }

        .content .txtbx {
            border: 2px solid #fff;
            width: 95%;
            margin-bottom: .48em;
            margin-left: auto;
            margin-right: auto
        }

        .content .txtbx .txt1,
        .content .txtbx .txt2 {
            color: #fff;
            background-color: transparent;
            border: 0;
            height: 5.25em;
            box-sizing: border-box;
            text-align: center;
            font-size: .8em;
            border-radius: 0
        }

        .content .txtbx .txt1 {
            border-right: 2px solid #fff;
            width: 24%
        }

        .content .txtbx .txt2 {
            width: 73%
        }

        .content .price {
            color: #cac2c2;
            font-size: .85em;
            font-weight: inherit
        }

        .content #countdown_container {
            font-size: 1.04em;
            font-weight: 400;
            color: #fff
        }

        .content .Sub-Button {
            display: block;
            line-height: 2.3em;
            width: 95%;
            color: #fff;
            border: 0;
            border-radius: 1em;
            font-size: 1em;
            font-weight: 400;
            background-color: #2ea25e;
            text-decoration: none
        }

        .content #confirm-btn {
            color: #fff;
            background-color: #2ea25e
        }

    }

</style>

<body class="kidjo1">
<!-- Google Tag Manager (noscript) -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NN3JVG3" height="0" width="0"
            style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
<section class="wrapper">
    <a href="https://www.google.com"><img src="{{& g_assetRoot}}images/ico-cross.png" alt="cerrar" class="ico-cross"></a>
    <div class="content no-padding">
        <img class="backdrop-image small"
             src="{{& g_assetRoot}}images/vodafone_bg.jpg"
             alt="kidjo game background image">
    </div>
</section>
<main id="main-content">
    <section class="wrapper sub-text-price">
        <div class="content">
            <p class="price"><span style="font-size: 20px; font-weight: 900;">4€/semana</span> IVA inc</p>
            <p class="price">Renovación automática</p>
        </div>
    </section>

    <section class="wrapper logo-Header">
        <div class="content">
            <h1 class="header3">Kidjo by Kidjo Inc<br>Kidjo es una aplicación certificada de educación y entretenimiento para niños, creada para mamás y papás que desean que sus hijos pasen tiempo de calidad frente a la pantalla. A los niños les encantará la interfaz fácil de usar de Kidjo, su perfil individual y, sobre todo, la gran selección de canciones, tutoriales, clips y dibujos animados que se renuevan semanalmente.</h1>
        </div>
    </section>

    <section class="wrapper" id="defaultWrapper">
        <div class="content">
            <a class="Sub-Button btn-Second-3G" id="confirm-btn"
               href="{{& g_redirectUrl}}" data-charge="true"
               data-serviceid="mm-internal-subscription" data-errorurl="error.html" data-assetid="#"
               data-contentname="Kidjo" data-oneclick="true" data-billingprovider="mm">LO QUIERO</a>
        </div>

        <div class="terms">
            <h4><a href="https://www.kidjo.tv/es/terms" style="color:#000000">Términos&amp;Condiciones</a></h4>
        </div>
    </section>
</main>
</body>
