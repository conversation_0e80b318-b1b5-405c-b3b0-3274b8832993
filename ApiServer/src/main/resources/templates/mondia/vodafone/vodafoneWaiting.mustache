<head>
    <!-- Google Tag Manager -->
    <script>
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NN3JVG3');
</script>
    <!-- End Google Tag Manager -->
</head>

<div id="loading-view" class="loading-card-container" style="display: block;">
    <div class="loading-card" style="width: 180px; margin-top: 30px;">
        <div class="loading-text">
            {{& g_accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>

</body>
<!-- Google Tag Manager (noscript) -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NN3JVG3" height="0" width="0"
            style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
<script>
    var customerId = {{& g_customerId}};
    var times = 0;

    function check() {
        var request = new XMLHttpRequest();
        request.open("POST", "/app/api/3/mondia/check");
        request.setRequestHeader("Content-type", "application/json");
        request.onreadystatechange = function () {
            if (request.readyState === XMLHttpRequest.DONE) {
                times++;
                var again = true;
                if (request.responseText != null && request.responseText != "") {
                    var responseJSON = JSON.parse(request.responseText);
                    if (responseJSON["success"] === true) {
                        again = false;
                        location.pathname = "/mondiaVodafone/success";
                        //redirect
                    }
                }
                if (times > 200) { //only waits up to 10 minutes
                    location.pathname = "/mondiaVodafone/error";
                } else if (again) {
                    setTimeout(function () {
                        check();
                    }, 3000);
                }
            }
        };
        request.send(JSON.stringify({"customerId": customerId}))
    }

    check();
</script>
</html>
