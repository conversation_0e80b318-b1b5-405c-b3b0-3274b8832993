<style>
    .app-card {
        padding: 22px 28px 22px 28px;
        text-align: center;
    }
    .section-header-1 {
        font-size: 28px;
        margin-bottom: 40px;
    }
    a.button {
        background: #884b95;
        border-radius: 4px;
        border: none;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
        color: #fff;
        display: block;
        flex: 1;
        font-size: 22px;
        height: auto;
        margin: 0 5px;
        padding: 8px;
        text-decoration: none;
    }
    a.button-store img {
        background-color: transparent;
        max-width: 100%;
        width: 200px;
    }
    .links {
        margin-top: 60px;
    }
    .links a {
        color: #666;
        font-size: 14px;
        display: block;
        flex: 1;
        text-decoration: none;
    }
</style>
<body>
<div class="page">
    <div class="header card-width">
        <div class="left-box"></div>
        <div class="kidjo-logo"></div>
        <div class="right-box"></div>
    </div>
    <div class="sign-up-card-container account-card card-width">
        <div class="sign-up-card account-card">
            <div class="app-card">
                <div class="section-header-1">Descarga la aplicación Kidjo TV</div>

                <div class="app-buttons">
                    <a id="app-store-button" class="button" href="{{&g_linkUrl}}">Aplicación movil</a>
                </div>
                <div class="links">
                    <a class="unsubscribe-button" href="/app">app.kidjo.tv</a>
                    <a class="unsubscribe-button" href="/es/terms">Términos y Condiciones</a>
                    <a class="unsubscribe-button" href="/es/privacy">Privacidad</a>
                    <a class="unsubscribe-button" href="/es/cookies">Cookies</a>
                    <a class="unsubscribe-button" href="/es/dispositivos">Dispositivos compatibles</a>
                    <a class="unsubscribe-button" href="/account/cancel">Darse de baja</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function getMobileOperatingSystem() {
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/android/i.test(userAgent)) {
            return "android";
        }

        // iOS detection from: http://stackoverflow.com/a/9039885/177710
        if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
            return "ios";
        }

        return null;
    }

    var appStoreButton = document.getElementById('app-store-button');
    var mobileOs = getMobileOperatingSystem();
    if (mobileOs === 'android') {
        appStoreButton.innerHTML = '<img src="https://d3aod987c9rl70.cloudfront.net/images/google-play-badge.png"/>';
        appStoreButton.className = 'button-store';
    } else if (mobileOs === 'ios') {
        appStoreButton.innerHTML = '<img src="https://d3aod987c9rl70.cloudfront.net/images/app_store.png"/>';
        appStoreButton.className = 'button-store';
    }
</script>
</body>
