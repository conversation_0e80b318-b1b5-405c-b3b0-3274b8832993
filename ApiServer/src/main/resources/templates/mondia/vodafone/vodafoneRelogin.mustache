<script>
    const autoAuth = "{{&g_autoAuth}}";
    const sxid = "{{&g_sxid}}";
    if (autoAuth === "true") {
        // Start redirection to mondia API to check existing customerId from the user network
        console.log("Redirecting to try login with existing customerId from Mondia VF");
        const redirectionUrl = encodeURIComponent(document.location.origin + "/mondiaVodafone/auth?sxid=" + sxid + "&from=" + document.location.pathname);
        document.location = "http://login.mondiamediamena.com/billinggw-lcm/billing?method=getcustomer&merchantId=142&redirect=" + redirectionUrl + "&operatorId=10";
    }
</script>
