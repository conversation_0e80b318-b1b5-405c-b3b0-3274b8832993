<head>
    <!-- Google Tag Manager -->
    <script>
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NN3JVG3');
</script>
    <!-- End Google Tag Manager -->
</head>

<style>
    a.button {
        background: #884b95;
        border-radius: 4px;
        border: none;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
        color: #fff;
        cursor: pointer;
        display: block;
        flex: 1;
        font-size: 22px;
        font-weight: 700;
        height: auto;
        padding: 8px;
        text-align: center;
        text-decoration: none;
        text-transform: uppercase;
    }

    .links {
        margin-top: 60px;
        text-align: center;
    }

    .links a {
        color: #666;
        font-size: 14px;
        display: block;
        flex: 1;
        text-decoration: none;
    }
</style>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NN3JVG3" height="0" width="0"
            style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div id="header-logo" class="header card-width">
        <div class="left-box"></div>
        <div id="kidjo-logo" class="kidjo-logo"></div>
        <div class="right-box"></div>
    </div>
    <div id="sign-up-container" class="sign-up-card-container coupon-card card-width">
        <div class="sign-up-card payment-card">
            <div class="sign-up-card-inner payment-card">
                <div class="sign-up-card-normal payment-card">
                    <div class="coupon-enter-container">
                        <div class="section-header-1">{{&mondiaRedeemCredentialsTitle}}</div>
                        <div class="section-header-3 minor-spacer">{{&mondiaRedeemCredentialsSubtitle}}</div>
                        <div id="input-error" class="error-message"></div>
                        <input id="input-email" class="input-div coupon-input" type="email"
                               placeholder="{{&mondiaRedeemCredentialsInput}}">
                        <a class="minor-spacer button" onclick="onValidate()">
                            <div class="text">{{&mondiaRedeemCredentialsValidate}}</div>
                        </a>
                    </div>
                    <div class="links">
                        <a class="unsubscribe-button" href="/app">app.kidjo.tv</a>
                        <a class="unsubscribe-button" href="{{&g_languagePath}}/terms">Términos y Condiciones</a>
                        <a class="unsubscribe-button" href="{{&g_languagePath}}/privacy">Privacidad</a>
                        <a class="unsubscribe-button" href="{{&g_languagePath}}/cookies">Cookies</a>
                        <a class="unsubscribe-button" href="{{&g_languagePath}}/dispositivos">Dispositivos compatibles</a>
                        <a class="unsubscribe-button" href="/account/cancel">Darse de baja</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function onValidate() {
        const input = document.getElementById("input-email");
        const error = document.getElementById("input-error");
        if (input.value !== "") {
            // Remove error
            error.innerText = "";

            // Send validation mail
            const xhr = new XMLHttpRequest();
            const url = "/mondiaVodafone/success?email=" + input.value;
            xhr.open("POST", url);
            xhr.onload = function () {
                if (xhr.status === 200) {
                    // Redirect user
                    window.location = "{{&g_dynamicLinkUrl}}";
                }
            };
            xhr.send();
        } else {
            // Display error
            error.innerText = "{{&mondiaRedeemCredentialsInputError}}";
        }
    }
</script>
</body>
</html>
