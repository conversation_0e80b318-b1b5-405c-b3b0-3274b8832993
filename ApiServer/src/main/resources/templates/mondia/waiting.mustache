<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
</body>
<script>
    var customerId = {{& g_customerId}};
    var times = 0;
    function check() {
        var request = new XMLHttpRequest();
        request.open("POST", "/app/api/3/mondia/check");
        request.setRequestHeader("Content-type", "application/json");
        request.onreadystatechange = function () {
            if (request.readyState === XMLHttpRequest.DONE) {
                times++;
                var again = true;
                if (request.responseText != null && request.responseText != "") {
                    var responseJSON = JSON.parse(request.responseText);
                    if (responseJSON["success"] === true) {
                        again = false;
                        location.pathname = "/app";
                        //redirect
                    }
                }
                if (times > 100) { //only waits up to 5 minutes
                    location.pathname = "/login";
                } else if (again) {
                    setTimeout(function () {
                        check();
                    },3000);
                }
            }
        };
        request.send(JSON.stringify({"customerId": customerId}))
    }
    check();
</script>
</html>