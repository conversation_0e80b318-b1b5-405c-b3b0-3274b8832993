<body>
<section id="landing-yearly-subscription">
    <svg id="shapes" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <ellipse class="fadeInLeftBig" id="path-1" cx="28.6792453" cy="124.286353" rx="2.88679245" ry="2.89038031"></ellipse>
            <mask class="trex" id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-2.72" y="-2.72" width="11.2135849" height="11.2207606">
                <rect x="23.0724528" y="118.675973" width="11.2135849" height="11.2207606" fill="white"></rect>
                <use xlink:href="#path-1" fill="black"></use>
            </mask>
            <ellipse class="fadeInLeftBig" id="path-3" cx="70.2830189" cy="56.787472" rx="6.79245283" ry="6.80089485"></ellipse>
            <mask class="trex" id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-5.1" y="-5.1" width="23.7849057" height="23.8017897">
                <rect x="58.390566" y="44.8865772" width="23.7849057" height="23.8017897" fill="white"></rect>
                <use xlink:href="#path-3" fill="black"></use>
            </mask>
            <rect class="trex" id="path-5" x="63.0444831" y="112.108172" width="14.2641509" height="14.2818792" rx="1.7"></rect>
            <mask class="fadeInLeftBig" id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.2641509" height="14.2818792" fill="white">
                <use xlink:href="#path-5"></use>
            </mask>
            <ellipse class="fadeInLeftBig" id="path-7" cx="28.6792453" cy="124.286353" rx="2.88679245" ry="2.89038031"></ellipse>
            <mask id="mask-8" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-2.72" y="-2.72" width="11.2135849" height="11.2207606">
                <rect x="23.0724528" y="118.675973" width="11.2135849" height="11.2207606" fill="white"></rect>
                <use xlink:href="#path-7" fill="black"></use>
            </mask>
            <ellipse class="trex" id="path-9" cx="70.2830189" cy="56.787472" rx="6.79245283" ry="6.80089485"></ellipse>
            <mask class="fadeInRightBig" id="mask-10" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-5.1" y="-5.1" width="23.7849057" height="23.8017897">
                <rect x="58.390566" y="44.8865772" width="23.7849057" height="23.8017897" fill="white"></rect>
                <use xlink:href="#path-9" fill="black"></use>
            </mask>
            <rect class="trex" id="path-11" x="63.0444831" y="112.108172" width="14.2641509" height="14.2818792" rx="1.7"></rect>
            <mask class="fadeInLeftBig" id="mask-12" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.2641509" height="14.2818792" fill="white">
                <use xlink:href="#path-11"></use>
            </mask>
            <ellipse class="trex" id="path-13" cx="28.6792453" cy="124.286353" rx="2.88679245" ry="2.89038031"></ellipse>
            <mask class="fadeInLeftBig" id="mask-14" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-2.72" y="-2.72" width="11.2135849" height="11.2207606">
                <rect x="23.0724528" y="118.675973" width="11.2135849" height="11.2207606" fill="white"></rect>
                <use xlink:href="#path-13" fill="black"></use>
            </mask>
            <ellipse class="hinge" id="path-15" cx="70.2830189" cy="56.787472" rx="6.79245283" ry="6.80089485"></ellipse>
            <mask class="fadeInLeftBig" id="mask-16" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-5.1" y="-5.1" width="23.7849057" height="23.8017897">
                <rect x="58.390566" y="44.8865772" width="23.7849057" height="23.8017897" fill="white"></rect>
                <use xlink:href="#path-15" fill="black"></use>
            </mask>
            <rect class="hinge" id="path-17" x="63.0444831" y="112.108172" width="14.2641509" height="14.2818792" rx="1.7"></rect>
            <mask class="fadeInLeftBig" id="mask-18" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.2641509" height="14.2818792" fill="white">
                <use xlink:href="#path-17"></use>
            </mask>
            <ellipse class="fadeInLeftBig" id="path-19" cx="28.6792453" cy="124.286353" rx="2.88679245" ry="2.89038031"></ellipse>
            <mask class="zoomOutDown" id="mask-20" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-2.72" y="-2.72" width="11.2135849" height="11.2207606">
                <rect x="23.0724528" y="118.675973" width="11.2135849" height="11.2207606" fill="white"></rect>
                <use xlink:href="#path-19" fill="black"></use>
            </mask>
            <ellipse class="hinge" id="path-21" cx="70.2830189" cy="56.787472" rx="6.79245283" ry="6.80089485"></ellipse>
            <mask class="hinge" id="mask-22" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-5.1" y="-5.1" width="23.7849057" height="23.8017897">
                <rect x="58.390566" y="44.8865772" width="23.7849057" height="23.8017897" fill="white"></rect>
                <use xlink:href="#path-21" fill="black"></use>
            </mask>
            <rect class="fadeInLeftBig" id="path-23" x="63.0444831" y="112.108172" width="14.2641509" height="14.2818792" rx="1.7"></rect>
            <mask class="hinge" id="mask-24" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14.2641509" height="14.2818792" fill="white">
                <use xlink:href="#path-23"></use>
            </mask>
        </defs>
        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Kidjo-modal-abonnement---yearly---v1---TABLET">
                <rect id="bg" fill="#FFFFFF" x="0" y="0" width="1024" height="768"></rect>
                <g id="IMG_decorations_tablet" transform="translate(-53.000000, -225.000000)">
                    <g id="Geometric_shapes_section4">
                        <g transform="translate(446.774921, 233.036135) rotate(67.000000) translate(-446.774921, -233.036135) translate(327.688528, 122.402711)">
                            <g id="Geometric_shapes" >
                                <path class="zoomOutDown" d="M-7.50657683,17.8200764 C-7.50657683,17.8200764 -6.19120255,18.8524209 -4.56888844,20.1256608 L-0.912278196,22.9954763 C0.710162303,24.2688154 3.3510636,24.2765477 4.98229947,23.0158695 L8.74842953,20.1052676 C10.3814744,18.8431914 13.0029572,18.876052 14.6140502,20.1871066 L17.9896151,22.9340305 C19.5960631,24.2413052 22.2299441,24.2921419 23.8875465,23.0362172 L27.7827462,20.0849199 C29.4336357,18.8340814 32.0804298,18.876052 33.679554,20.166581 L37.1341981,22.9545562" id="zigzag" stroke-opacity="0.197322237" stroke="#B7ED6A" stroke-width="5.78" stroke-linecap="round" transform="translate(14.813811, 20.888018) rotate(-300.000000) translate(-14.813811, -20.888018) "></path>
                                <ellipse class="trex" id="Oval-3" fill="#F6DEFC" cx="9.66037736" cy="47.0961969" rx="2.88679245" ry="2.89038031"></ellipse>
                                <polyline class="fadeInRightBig" id="zigzag" stroke="#D867F0" stroke-width="1.36" stroke-linecap="round" stroke-linejoin="round" transform="translate(49.763062, 87.295478) rotate(-300.000000) translate(-49.763062, -87.295478) " points="41.4423073 85.5952541 44.5991561 88.9957016 48.0267247 85.5952541 51.4718713 88.9957016 54.8415647 85.5952541 58.0838167 88.9957016"></polyline>
                                <use class="fadeInLeftBig" id="Oval-2" stroke="#7BF5DD" mask="url(#mask-2)" stroke-width="5.44" xlink:href="#path-1"></use>
                                <use class="zoomOutDown" id="Oval" stroke-opacity="0.196331522" stroke="#4DCDF7" mask="url(#mask-4)" stroke-width="10.2" xlink:href="#path-3"></use>
                                <path class="hinge" d="M113.754717,70.7278291 L117.673214,70.7278291 C118.316247,70.7278291 118.856865,70.1956393 118.856865,69.5391499 C118.856865,68.8780835 118.326927,68.3504706 117.673214,68.3504706 L113.754717,68.3504706 L113.754717,64.4319737 C113.754717,63.7889402 113.222527,63.2483221 112.566038,63.2483221 C111.904971,63.2483221 111.377358,63.778261 111.377358,64.4319737 L111.377358,68.3504706 L107.458862,68.3504706 C106.815828,68.3504706 106.27521,68.8826605 106.27521,69.5391499 C106.27521,70.2002162 106.805149,70.7278291 107.458862,70.7278291 L111.377358,70.7278291 L111.377358,74.646326 C111.377358,75.2893596 111.909548,75.8299776 112.566038,75.8299776 C113.227104,75.8299776 113.754717,75.3000388 113.754717,74.646326 L113.754717,70.7278291 Z" id="Plus" fill="#7BF5DD"></path>
                                <path class="zoomOutDown" d="M123.759816,148.414706 L126.513355,148.414706 C126.965216,148.414706 127.34511,148.040735 127.34511,147.579418 C127.34511,147.114885 126.97272,146.74413 126.513355,146.74413 L123.759816,146.74413 L123.759816,143.990592 C123.759816,143.53873 123.385845,143.158837 122.924528,143.158837 C122.459995,143.158837 122.08924,143.531226 122.08924,143.990592 L122.08924,146.74413 L119.335702,146.74413 C118.88384,146.74413 118.503947,147.118101 118.503947,147.579418 C118.503947,148.043951 118.876336,148.414706 119.335702,148.414706 L122.08924,148.414706 L122.08924,151.168245 C122.08924,151.620106 122.463211,152 122.924528,152 C123.389061,152 123.759816,151.627611 123.759816,151.168245 L123.759816,148.414706 Z" id="Plus" fill="#45CCF9"></path>
                                <path class="hinge" d="M147.377358,122.073108 L157.581655,122.073108 L157.581655,117.318391 L147.377358,117.318391 L147.377358,107.114094 L142.622642,107.114094 L142.622642,117.318391 L132.418345,117.318391 L132.418345,122.073108 L142.622642,122.073108 L142.622642,132.277405 L147.377358,132.277405 L147.377358,122.073108 Z" id="cross" fill="#D66AED" opacity="0.209125906" transform="translate(145.000000, 119.695749) rotate(-315.000000) translate(-145.000000, -119.695749) "></path>
                                <path class="zoomOutDown" d="M111.811743,126.282963 C110.821565,126.81979 110.018868,126.346499 110.018868,125.214837 L110.018868,116.940352 C110.018868,115.813615 110.820084,115.334596 111.811743,115.872226 L119.620171,120.105583 C120.610349,120.64241 120.61183,121.511976 119.620171,122.049605 L111.811743,126.282963 Z" id="Triangle" fill="#FFD400"></path>
                                <path class="hinge" d="M5.91046019,93.393897 C3.59567414,94.6488635 1.71916929,93.5426193 1.71916929,90.9146757 L1.71916929,74.9974791 C1.71916929,72.3732765 3.59804854,71.2645786 5.91046019,72.5182578 L20.9717303,80.6837596 C23.2865163,81.9387261 23.2841419,83.9747161 20.9717303,85.2283953 L5.91046019,93.393897 Z" id="Triangle" fill="#FFD400" transform="translate(12.213049, 82.956673) rotate(-270.000000) translate(-12.213049, -82.956673) "></path>
                                <use class="fadeInLeftBig" id="square" stroke="#FDACB4" mask="url(#mask-6)" stroke-width="4.76" transform="translate(70.176559, 119.249112) rotate(-30.000000) translate(-70.176559, -119.249112) " xlink:href="#path-5"></use>
                            </g>
                        </g>
                        <g transform="translate(942.503797, 181.009967) rotate(-49.000000) translate(-942.503797, -181.009967) translate(809.951899, 57.754153)">
                            <g id="Geometric_shapes">
                                <path class="hinge" d="M-7.50657683,17.8200764 C-7.50657683,17.8200764 -6.19120255,18.8524209 -4.56888844,20.1256608 L-0.912278196,22.9954763 C0.710162303,24.2688154 3.3510636,24.2765477 4.98229947,23.0158695 L8.74842953,20.1052676 C10.3814744,18.8431914 13.0029572,18.876052 14.6140502,20.1871066 L17.9896151,22.9340305 C19.5960631,24.2413052 22.2299441,24.2921419 23.8875465,23.0362172 L27.7827462,20.0849199 C29.4336357,18.8340814 32.0804298,18.876052 33.679554,20.166581 L37.1341981,22.9545562" id="zigzag" stroke-opacity="0.197322237" stroke="#B7ED6A" stroke-width="5.78" stroke-linecap="round" transform="translate(14.813811, 20.888018) rotate(-300.000000) translate(-14.813811, -20.888018) "></path>
                                <ellipse class="fadeInLeftBig" id="Oval-3" fill="#F6DEFC" cx="9.66037736" cy="47.0961969" rx="2.88679245" ry="2.89038031"></ellipse>
                                <polyline class="fadeInRightBig" id="zigzag" stroke="#D867F0" stroke-width="1.36" stroke-linecap="round" stroke-linejoin="round" transform="translate(49.763062, 87.295478) rotate(-300.000000) translate(-49.763062, -87.295478) " points="41.4423073 85.5952541 44.5991561 88.9957016 48.0267247 85.5952541 51.4718713 88.9957016 54.8415647 85.5952541 58.0838167 88.9957016"></polyline>
                                <use class="fadeInLeftBig" id="Oval-2" stroke="#7BF5DD" mask="url(#mask-8)" stroke-width="5.44" xlink:href="#path-7"></use>
                                <use class="trex" id="Oval" stroke-opacity="0.196331522" stroke="#4DCDF7" mask="url(#mask-10)" stroke-width="10.2" xlink:href="#path-9"></use>
                                <path class="hinge" d="M113.754717,70.7278291 L117.673214,70.7278291 C118.316247,70.7278291 118.856865,70.1956393 118.856865,69.5391499 C118.856865,68.8780835 118.326927,68.3504706 117.673214,68.3504706 L113.754717,68.3504706 L113.754717,64.4319737 C113.754717,63.7889402 113.222527,63.2483221 112.566038,63.2483221 C111.904971,63.2483221 111.377358,63.778261 111.377358,64.4319737 L111.377358,68.3504706 L107.458862,68.3504706 C106.815828,68.3504706 106.27521,68.8826605 106.27521,69.5391499 C106.27521,70.2002162 106.805149,70.7278291 107.458862,70.7278291 L111.377358,70.7278291 L111.377358,74.646326 C111.377358,75.2893596 111.909548,75.8299776 112.566038,75.8299776 C113.227104,75.8299776 113.754717,75.3000388 113.754717,74.646326 L113.754717,70.7278291 Z" id="Plus" fill="#7BF5DD"></path>
                                <path class="trex" d="M123.759816,148.414706 L126.513355,148.414706 C126.965216,148.414706 127.34511,148.040735 127.34511,147.579418 C127.34511,147.114885 126.97272,146.74413 126.513355,146.74413 L123.759816,146.74413 L123.759816,143.990592 C123.759816,143.53873 123.385845,143.158837 122.924528,143.158837 C122.459995,143.158837 122.08924,143.531226 122.08924,143.990592 L122.08924,146.74413 L119.335702,146.74413 C118.88384,146.74413 118.503947,147.118101 118.503947,147.579418 C118.503947,148.043951 118.876336,148.414706 119.335702,148.414706 L122.08924,148.414706 L122.08924,151.168245 C122.08924,151.620106 122.463211,152 122.924528,152 C123.389061,152 123.759816,151.627611 123.759816,151.168245 L123.759816,148.414706 Z" id="Plus" fill="#45CCF9"></path>
                                <path class="fadeInLeftBig"  d="M147.377358,122.073108 L157.581655,122.073108 L157.581655,117.318391 L147.377358,117.318391 L147.377358,107.114094 L142.622642,107.114094 L142.622642,117.318391 L132.418345,117.318391 L132.418345,122.073108 L142.622642,122.073108 L142.622642,132.277405 L147.377358,132.277405 L147.377358,122.073108 Z" id="cross" fill="#D66AED" opacity="0.209125906" transform="translate(145.000000, 119.695749) rotate(-315.000000) translate(-145.000000, -119.695749) "></path>
                                <path class="hinge" d="M111.811743,126.282963 C110.821565,126.81979 110.018868,126.346499 110.018868,125.214837 L110.018868,116.940352 C110.018868,115.813615 110.820084,115.334596 111.811743,115.872226 L119.620171,120.105583 C120.610349,120.64241 120.61183,121.511976 119.620171,122.049605 L111.811743,126.282963 Z" id="Triangle" fill="#FFD400"></path>
                                <path class="hinge" d="M5.91046019,93.393897 C3.59567414,94.6488635 1.71916929,93.5426193 1.71916929,90.9146757 L1.71916929,74.9974791 C1.71916929,72.3732765 3.59804854,71.2645786 5.91046019,72.5182578 L20.9717303,80.6837596 C23.2865163,81.9387261 23.2841419,83.9747161 20.9717303,85.2283953 L5.91046019,93.393897 Z" id="Triangle" fill="#FFD400" transform="translate(12.213049, 82.956673) rotate(-270.000000) translate(-12.213049, -82.956673) "></path>
                                <use class="zoomOutDown" id="square" stroke="#FDACB4" mask="url(#mask-12)" stroke-width="4.76" transform="translate(70.176559, 119.249112) rotate(-30.000000) translate(-70.176559, -119.249112) " xlink:href="#path-11"></use>
                            </g>
                        </g>
                        <g transform="translate(180.906329, 252.990033) rotate(-221.000000) translate(-180.906329, -252.990033) translate(48.354430, 129.734219)">
                            <g id="Geometric_shapes">
                                <path class="fadeInRightBig" d="M-7.50657683,17.8200764 C-7.50657683,17.8200764 -6.19120255,18.8524209 -4.56888844,20.1256608 L-0.912278196,22.9954763 C0.710162303,24.2688154 3.3510636,24.2765477 4.98229947,23.0158695 L8.74842953,20.1052676 C10.3814744,18.8431914 13.0029572,18.876052 14.6140502,20.1871066 L17.9896151,22.9340305 C19.5960631,24.2413052 22.2299441,24.2921419 23.8875465,23.0362172 L27.7827462,20.0849199 C29.4336357,18.8340814 32.0804298,18.876052 33.679554,20.166581 L37.1341981,22.9545562" id="zigzag" stroke-opacity="0.197322237" stroke="#B7ED6A" stroke-width="5.78" stroke-linecap="round" transform="translate(14.813811, 20.888018) rotate(-300.000000) translate(-14.813811, -20.888018) "></path>
                                <ellipse class="fadeInRightBig" id="Oval-3" fill="#F6DEFC" cx="9.66037736" cy="47.0961969" rx="2.88679245" ry="2.89038031"></ellipse>
                                <polyline class="fadeInLeftBig" id="zigzag" stroke="#D867F0" stroke-width="1.36" stroke-linecap="round" stroke-linejoin="round" transform="translate(49.763062, 87.295478) rotate(-300.000000) translate(-49.763062, -87.295478) " points="41.4423073 85.5952541 44.5991561 88.9957016 48.0267247 85.5952541 51.4718713 88.9957016 54.8415647 85.5952541 58.0838167 88.9957016"></polyline>
                                <use class="hinge" id="Oval-2" stroke="#7BF5DD" mask="url(#mask-14)" stroke-width="5.44" xlink:href="#path-13"></use>
                                <use class="fadeInLeftBig" id="Oval" stroke-opacity="0.196331522" stroke="#4DCDF7" mask="url(#mask-16)" stroke-width="10.2" xlink:href="#path-15"></use>
                                <path class="trex" d="M113.754717,70.7278291 L117.673214,70.7278291 C118.316247,70.7278291 118.856865,70.1956393 118.856865,69.5391499 C118.856865,68.8780835 118.326927,68.3504706 117.673214,68.3504706 L113.754717,68.3504706 L113.754717,64.4319737 C113.754717,63.7889402 113.222527,63.2483221 112.566038,63.2483221 C111.904971,63.2483221 111.377358,63.778261 111.377358,64.4319737 L111.377358,68.3504706 L107.458862,68.3504706 C106.815828,68.3504706 106.27521,68.8826605 106.27521,69.5391499 C106.27521,70.2002162 106.805149,70.7278291 107.458862,70.7278291 L111.377358,70.7278291 L111.377358,74.646326 C111.377358,75.2893596 111.909548,75.8299776 112.566038,75.8299776 C113.227104,75.8299776 113.754717,75.3000388 113.754717,74.646326 L113.754717,70.7278291 Z" id="Plus" fill="#7BF5DD"></path>
                                <path class="fadeInRightBig" d="M123.759816,148.414706 L126.513355,148.414706 C126.965216,148.414706 127.34511,148.040735 127.34511,147.579418 C127.34511,147.114885 126.97272,146.74413 126.513355,146.74413 L123.759816,146.74413 L123.759816,143.990592 C123.759816,143.53873 123.385845,143.158837 122.924528,143.158837 C122.459995,143.158837 122.08924,143.531226 122.08924,143.990592 L122.08924,146.74413 L119.335702,146.74413 C118.88384,146.74413 118.503947,147.118101 118.503947,147.579418 C118.503947,148.043951 118.876336,148.414706 119.335702,148.414706 L122.08924,148.414706 L122.08924,151.168245 C122.08924,151.620106 122.463211,152 122.924528,152 C123.389061,152 123.759816,151.627611 123.759816,151.168245 L123.759816,148.414706 Z" id="Plus" fill="#45CCF9"></path>
                                <path class="hinge" d="M147.377358,122.073108 L157.581655,122.073108 L157.581655,117.318391 L147.377358,117.318391 L147.377358,107.114094 L142.622642,107.114094 L142.622642,117.318391 L132.418345,117.318391 L132.418345,122.073108 L142.622642,122.073108 L142.622642,132.277405 L147.377358,132.277405 L147.377358,122.073108 Z" id="cross" fill="#D66AED" opacity="0.209125906" transform="translate(145.000000, 119.695749) rotate(-315.000000) translate(-145.000000, -119.695749) "></path>
                                <path class="trex"  d="M111.811743,126.282963 C110.821565,126.81979 110.018868,126.346499 110.018868,125.214837 L110.018868,116.940352 C110.018868,115.813615 110.820084,115.334596 111.811743,115.872226 L119.620171,120.105583 C120.610349,120.64241 120.61183,121.511976 119.620171,122.049605 L111.811743,126.282963 Z" id="Triangle" fill="#FFD400"></path>
                                <path class="fadeInLeftBig" d="M5.91046019,93.393897 C3.59567414,94.6488635 1.71916929,93.5426193 1.71916929,90.9146757 L1.71916929,74.9974791 C1.71916929,72.3732765 3.59804854,71.2645786 5.91046019,72.5182578 L20.9717303,80.6837596 C23.2865163,81.9387261 23.2841419,83.9747161 20.9717303,85.2283953 L5.91046019,93.393897 Z" id="Triangle" fill="#FFD400" transform="translate(12.213049, 82.956673) rotate(-270.000000) translate(-12.213049, -82.956673) "></path>
                                <use class="hinge" id="square" stroke="#FDACB4" mask="url(#mask-18)" stroke-width="4.76" transform="translate(70.176559, 119.249112) rotate(-30.000000) translate(-70.176559, -119.249112) " xlink:href="#path-17"></use>
                            </g>
                        </g>
                        <g transform="translate(745.086076, 238.764120) scale(-1, -1) rotate(-38.000000) translate(-745.086076, -238.764120) translate(612.534177, 115.508306)">
                            <g id="Geometric_shapes">
                                <path class="fadeInRightBig" d="M-7.50657683,17.8200764 C-7.50657683,17.8200764 -6.19120255,18.8524209 -4.56888844,20.1256608 L-0.912278196,22.9954763 C0.710162303,24.2688154 3.3510636,24.2765477 4.98229947,23.0158695 L8.74842953,20.1052676 C10.3814744,18.8431914 13.0029572,18.876052 14.6140502,20.1871066 L17.9896151,22.9340305 C19.5960631,24.2413052 22.2299441,24.2921419 23.8875465,23.0362172 L27.7827462,20.0849199 C29.4336357,18.8340814 32.0804298,18.876052 33.679554,20.166581 L37.1341981,22.9545562" id="zigzag" stroke-opacity="0.197322237" stroke="#B7ED6A" stroke-width="5.78" stroke-linecap="round" transform="translate(14.813811, 20.888018) rotate(-300.000000) translate(-14.813811, -20.888018) "></path>
                                <ellipse class="trex" id="Oval-3" fill="#F6DEFC" cx="9.66037736" cy="47.0961969" rx="2.88679245" ry="2.89038031"></ellipse>
                                <polyline class="trex" id="zigzag" stroke="#D867F0" stroke-width="1.36" stroke-linecap="round" stroke-linejoin="round" transform="translate(49.763062, 87.295478) rotate(-300.000000) translate(-49.763062, -87.295478) " points="41.4423073 85.5952541 44.5991561 88.9957016 48.0267247 85.5952541 51.4718713 88.9957016 54.8415647 85.5952541 58.0838167 88.9957016"></polyline>
                                <use class="hinge" id="Oval-2" stroke="#7BF5DD" mask="url(#mask-20)" stroke-width="5.44" xlink:href="#path-19"></use>
                                <use  class="fadeInRightBig"id="Oval" stroke-opacity="0.196331522" stroke="#4DCDF7" mask="url(#mask-22)" stroke-width="10.2" xlink:href="#path-21"></use>
                                <path class="hinge" d="M113.754717,70.7278291 L117.673214,70.7278291 C118.316247,70.7278291 118.856865,70.1956393 118.856865,69.5391499 C118.856865,68.8780835 118.326927,68.3504706 117.673214,68.3504706 L113.754717,68.3504706 L113.754717,64.4319737 C113.754717,63.7889402 113.222527,63.2483221 112.566038,63.2483221 C111.904971,63.2483221 111.377358,63.778261 111.377358,64.4319737 L111.377358,68.3504706 L107.458862,68.3504706 C106.815828,68.3504706 106.27521,68.8826605 106.27521,69.5391499 C106.27521,70.2002162 106.805149,70.7278291 107.458862,70.7278291 L111.377358,70.7278291 L111.377358,74.646326 C111.377358,75.2893596 111.909548,75.8299776 112.566038,75.8299776 C113.227104,75.8299776 113.754717,75.3000388 113.754717,74.646326 L113.754717,70.7278291 Z" id="Plus" fill="#7BF5DD"></path>
                                <path class="hinge" d="M123.759816,148.414706 L126.513355,148.414706 C126.965216,148.414706 127.34511,148.040735 127.34511,147.579418 C127.34511,147.114885 126.97272,146.74413 126.513355,146.74413 L123.759816,146.74413 L123.759816,143.990592 C123.759816,143.53873 123.385845,143.158837 122.924528,143.158837 C122.459995,143.158837 122.08924,143.531226 122.08924,143.990592 L122.08924,146.74413 L119.335702,146.74413 C118.88384,146.74413 118.503947,147.118101 118.503947,147.579418 C118.503947,148.043951 118.876336,148.414706 119.335702,148.414706 L122.08924,148.414706 L122.08924,151.168245 C122.08924,151.620106 122.463211,152 122.924528,152 C123.389061,152 123.759816,151.627611 123.759816,151.168245 L123.759816,148.414706 Z" id="Plus" fill="#45CCF9"></path>
                                <path class="trex" d="M147.377358,122.073108 L157.581655,122.073108 L157.581655,117.318391 L147.377358,117.318391 L147.377358,107.114094 L142.622642,107.114094 L142.622642,117.318391 L132.418345,117.318391 L132.418345,122.073108 L142.622642,122.073108 L142.622642,132.277405 L147.377358,132.277405 L147.377358,122.073108 Z" id="cross" fill="#D66AED" opacity="0.209125906" transform="translate(145.000000, 119.695749) rotate(-315.000000) translate(-145.000000, -119.695749) "></path>
                                <path class="fadeInLeftBig"  d="M111.811743,126.282963 C110.821565,126.81979 110.018868,126.346499 110.018868,125.214837 L110.018868,116.940352 C110.018868,115.813615 110.820084,115.334596 111.811743,115.872226 L119.620171,120.105583 C120.610349,120.64241 120.61183,121.511976 119.620171,122.049605 L111.811743,126.282963 Z" id="Triangle" fill="#FFD400"></path>
                                <path class="hinge" d="M5.91046019,93.393897 C3.59567414,94.6488635 1.71916929,93.5426193 1.71916929,90.9146757 L1.71916929,74.9974791 C1.71916929,72.3732765 3.59804854,71.2645786 5.91046019,72.5182578 L20.9717303,80.6837596 C23.2865163,81.9387261 23.2841419,83.9747161 20.9717303,85.2283953 L5.91046019,93.393897 Z" id="Triangle" fill="#FFD400" transform="translate(12.213049, 82.956673) rotate(-270.000000) translate(-12.213049, -82.956673) "></path>
                                <use class="fadeInRightBig" id="square" stroke="#FDACB4" mask="url(#mask-24)" stroke-width="4.76" transform="translate(70.176559, 119.249112) rotate(-30.000000) translate(-70.176559, -119.249112) " xlink:href="#path-23"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
    <div id="yearly-subscription-top">
        <img src="{{& assetRoot}}images/logo.svg" alt=Logo" id="logo-img" />
        <img src="{{& assetRoot}}images/icon-gift-tablet.png" alt=Gift" id="gift-packs" />
    </div>
    <div id="yearly-subscription-middle">
        <h1>{{& title}}</h1>
        <h3 style="padding-left: 5%;padding-right: 5%;">{{& subtitle}}</h3>

        <a href="#" id="yearly-sub" class="blinking_background shake" onclick="purchase('{{& iapId}}',{{& freeTrialInDays}});">
            {{& buttonTitle}}
            <svg id="button-icon" width="1.7vw" viewBox="0 0 9 13" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
                    <g id="Kidjo-modal-abonnement---yearly---v1---TABLET" transform="translate(-755.000000, -462.000000)" stroke-width="3" stroke="#FFFFFF">
                        <g id="CTA" transform="translate(242.000000, 432.000000)">
                            <polyline id="icon_arrow_white_tablet" points="515 32 520 36.8046795 515 41"></polyline>
                        </g>
                    </g>
                </g>
            </svg>
        </a>

        <div class="prices">
            <div class="price current-price">{{& freeTrialText}}</div>
        </div>
    </div>
    {{#showRestore}}
        <div id="yearly-subscription-bottom">
            <a href="#" title="Restore Subscription" id="restore-subscription" onclick="restore();">
                {{& restoreText}}
            </a>
        </div>
    {{/showRestore}}
</section>
</body>
</html>