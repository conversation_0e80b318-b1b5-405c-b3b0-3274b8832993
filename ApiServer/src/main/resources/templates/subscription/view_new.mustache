<body style="background-image: url({{& assetRoot}}images/rays_bg.png?v=1); background-repeat: no-repeat; background-size: 100% auto;">
<div id="trees" style="background-image: url({{& assetRoot}}images/trees.png?v=1)"></div>
<button class="close" onclick="window.history.back()"><img src="{{& assetRoot}}images/close.svg?v=1" alt=""></button>
<div class="background">
    <img id="image_1" src="{{& assetRoot}}images/frame-1.png?v=1" alt="">
    <img id="image_2" src="{{& assetRoot}}images/frame-2.png?v=1" alt="">
    <img id="image_3" src="{{& assetRoot}}images/frame-3.png?v=1" alt="">
    <img id="image_4" src="{{& assetRoot}}images/frame-4.png?v=1" alt="">
    <img id="image_5" src="{{& assetRoot}}images/frame-5.png?v=1" alt="">
    <img id="image_6" src="{{& assetRoot}}images/frame-6.png?v=1" alt="">
</div>
<div class="catchphrases">
    <img id="logo" src="{{& assetRoot}}images/kidjo_logo.png?v=1" alt="Kidjo"/>
    <div class="phrase first">
        <span class="circle"><img src="{{& assetRoot}}images/dot.png?v=1" alt="Kidjo"/></span>
        <span class="text"><img src="{{& assetRoot}}images/media.png?v=1" alt="Kidjo"/>{{&fact1}}</span>
    </div>
    <div class="phrase second">
        <span class="circle"><img src="{{& assetRoot}}images/dot.png?v=1" alt="Kidjo"/></span>
        <span class="text"><img class="promo-icon{{#hasPromotion}} has-promo{{/hasPromotion}}" src="{{& assetRoot}}images/tag.svg?v=1" alt="Kidjo"/>{{&fact2}}</span>
    </div>
</div>
<div class="box {{#hasDescription}}has-description{{/hasDescription}}">
    <button class="button" onclick="purchase('{{&iapId}}',{{&freeTrialInDays}});">{{&button}}</button>
    <div class="links">
        <a href="#" class="restore" onclick="restore();">{{&buttonRestore}}</a>
        {{#hasDescription}}
            <div class="description">{{&description}}</div>
        {{/hasDescription}}
        <a href="#" class="link" onclick="openURL('https://www.kidjo.tv/terms')">{{& terms}}</a>
        <a href="#" class="link" onclick="openURL('https://www.kidjo.tv/privacy')">{{& privacy}}</a>
    </div>
</div>
<script src="https://s0.2mdn.net/ads/studio/cached_libs/tweenmax_2.1.2_min.js"></script>
<script src="https://s0.2mdn.net/ads/studio/cached_libs/easepack_2.1.2_min.js"></script>
<script src="https://code.jquery.com/jquery-3.4.1.min.js"
        integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo="
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FitText.js/1.2.0/jquery.fittext.min.js"></script>
<script>
    $(document).ready(function () {
        let index = 1;
        setInterval(function() {
            $('#image_' + (index === 1 ? '6' : index - 1)).css('opacity', 0);
            $('#image_' + index).css('opacity', 1);
            index++;
            if (index > 6)
                index = 1;
        }, 180);

        updateContainer();
        $(window).resize(function() {
            updateContainer();
        });

        // Temporary: display promo always for iOS
        if (checkIos()) {
            $('.promo-icon').addClass('has-promo');
        }
    });

    function updateContainer() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        applyRatioFromHeight($(".background"), window.innerHeight * .2);
        applyRatio($(".catchphrases"), 0, false);

        $("#logo").width("auto");
        $("#logo").height("16vh");

        $(".phrase .circle").width("5.2vh");
        $(".phrase .circle").height("5.2vh");

        if (width >= 1000 && height >= 700) {
            applyFontSize($(".phrase .text"), "3.2vh")
        } else {
            applyFontSize($(".phrase .text"), "4.2vh")
        }

        // Display close on desktop only
        if (checkMobile()) {
            $('.close').hide();
        } else {
            $('.close').show();
        }
    }

    const ratio = 1440.0 / 615.0;
    function applyRatio(container, sidePadding, keepWidth) {
        const width = window.innerWidth;
        const height = window.innerHeight;
        const windowRatio = width / height;
        const padding = sidePadding || 0;
        const keep = keepWidth || false;
        if (windowRatio >= ratio) {
            container.width(keep ? width : (height - padding) * ratio);
            container.height(height - padding);
        } else {
            container.width(width - padding);
            container.height((width - padding) / ratio);
        }
    }

    function applyRatioFromHeight(container, heightPadding) {
        const width = window.innerWidth;
        const minHeight = width > 1300 ? 800 : 600;
        const height = window.innerHeight < minHeight ? window.innerHeight : minHeight;
        container.width((height - heightPadding) * ratio);
        container.height(height - heightPadding);
    }

    function applyFontSize(element, size) {
        element.css({ "font-size": size });
    }

    function checkMobile() {
        return /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
                || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0, 4));
    }

    function checkIos() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    }
</script>
</body>
