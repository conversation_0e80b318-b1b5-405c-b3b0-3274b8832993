<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, viewport-fit=cover" >
    <meta name="application-name" content="kidjo.tv "/>
    <meta name="msapplication-TileColor" content="#ffffff"/>
    <meta name="mobile-web-app-capable" content="yes">
    <title>Kidjo</title>
    <meta name="copyright" content="kidjo.tv" />
    <link href="{{& cssUrl}}" rel="stylesheet">
    <style>
        {{& fonts}}
        #webview-bg{background: url({{& landscapeBackground}}) no-repeat scroll center top; background-size: cover;}
        @media screen and (max-width : 1460px) and (orientation: portrait) {
            #webview-bg {  background: url({{& backgroundPortrait}}) no-repeat scroll 35% top;  }
        }
        .background2-image {
            background: url({{& landscapeBackground2}}) no-repeat scroll center top; background-size: cover;
        }
    </style>
    <script>{{& javascript}}</script>
</head>