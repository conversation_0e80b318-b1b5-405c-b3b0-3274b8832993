<style>
    #input-message {
        padding: 16px 0;
        text-align: center;
    }

    .error-message {
        color: #d40200;
    }

    .success-message {
        color: #00a227;
    }

    a.button {
        background: #884b95;
        border-radius: 4px;
        border: none;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
        color: #fff;
        cursor: pointer;
        display: block;
        flex: 1;
        font-size: 22px;
        font-weight: 700;
        height: auto;
        padding: 8px;
        text-align: center;
        text-decoration: none;
        text-transform: uppercase;
    }
</style>

<body>
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div id="header-logo" class="header card-width">
        <div class="left-box"></div>
        <div id="kidjo-logo" class="kidjo-logo"></div>
        <div class="right-box"></div>
    </div>
    <div id="sign-up-container" class="sign-up-card-container coupon-card card-width">
        <div class="sign-up-card payment-card">
            <div class="sign-up-card-inner payment-card">
                <div class="sign-up-card-normal payment-card">
                    <div class="coupon-enter-container">
                        <div class="section-header-1">{{&accountForgottenPasswordTitle}}</div>
                        <div class="section-header-3 minor-spacer">{{&accountForgottenPasswordSubtitle}}</div>
                        <div id="input-message" class="error-message"></div>
                        <input id="input-email" class="input-div coupon-input" type="email"
                               placeholder="{{&accountForgottenPasswordInput}}">
                        <a class="minor-spacer button" onclick="onValidate()">
                            <div class="text">{{&accountForgottenPasswordValidate}}</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function onValidate() {
        const input = document.getElementById("input-email");
        const message = document.getElementById("input-message");
        if (input.value !== "") {
            // Remove error
            message.innerText = "";

            // Send validation mail
            const xhr = new XMLHttpRequest();
            const email = input.value;
            const url = "/account/password?email=" + email;
            xhr.open("POST", url);
            xhr.onload = function () {
                if (xhr.status === 200) {
                    message.innerText = "{{&accountForgottenPasswordConfirm}}".replace('%1$s', email);
                    message.className = "success-message"
                } else if (xhr.status === 400) {
                    // Display error
                    message.innerText = "{{&mondiaRedeemCredentialsInputError}}";
                    message.className = "error-message"
                } else if (xhr.status === 500) {
                    // Display error
                    message.innerText = "{{&errorGeneric}}";
                    message.className = "error-message"
                }
            };
            xhr.send();
        } else {
            // Display error
            message.innerText = "{{&mondiaRedeemCredentialsInputError}}";
            message.className = "error-message"
        }
    }
</script>
</body>
</html>
