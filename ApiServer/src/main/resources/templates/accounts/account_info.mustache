<body dir="auto">
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div class="header card-width">
        <div class="left-box"></div>
        <div class="kidjo-logo"></div>
        <div class="right-box">
            <button class="right-box-text" onclick="openLogout();">
                {{&accountNavigationLogout}}
            </button>
        </div>
    </div>
    <div class="sign-up-card-container account-card card-width">
        <div class="sign-up-card account-info-card">
            <div class="sign-up-card-inner account-info-card">
                <div class="sign-up-card-normal account-info-card">
                    <div class="section-header">{{&accountInfoTitle}}</div>
                    <div class="account-info-box main-spacer">
                        <div class="account-info-header">{{&accountInfoInfoSectionTitle}}</div>
                        <div class="account-info-box-text minor-spacer">
                            <div class="item-header">{{&accountInfoEmailTitle}}</div>
                            <div class="item-text">{{&g_email}}</div>
                        </div>
                    </div>
                    <div class="account-info-box main-spacer">
                        <div class="account-info-header">{{&accountInfoSubscriptionTitle}}</div>
                        {{#^g_isMondia}}
                            <div class="account-info-box-text minor-spacer">
                                <div class="item-header">{{&accountInfoPlanTitle}}</div>
                                <div class="item-text">{{&g_planTextFull}}</div>
                            </div>
                        {{/^g_isMondia}}
                        <div class="account-info-box-text minor-spacer">
                            <div class="item-header">{{&g_paymentBillDateText}}</div>
                            <div class="item-text">{{&g_nextBillDate}}</div>
                        </div>
                        {{#^g_isMondia}}
                            <div class="account-info-box-text minor-spacer">
                                <div class="item-header">{{&accountInfoPaymentTitle}}</div>
                                <div class="item-icon {{&g_paymentTypeIcon}}"></div>
                                <div class="item-text">{{&g_paymentIdText}}</div>
                            </div>
                        {{/^g_isMondia}}

                        {{#g_subIsRenewing}}
                            {{#g_hasCoupon}}
                                <div class="account-info-box-text large-spacer">
                                    <div class="item-header">{{&accountInfoCouponTitle}}</div>
                                    <div class="item-icon coupon-icon-black"></div>
                                    <div class="item-text">{{&g_couponCode}}</div>
                                </div>
                            {{/g_hasCoupon}}
                            <button class="account-info-box-button cancel-button minor-spacer" onclick="openAccountCancel()"><u>{{&accountInfoCancelSubscription}}</u></button>
                        {{/g_subIsRenewing}}{{^g_subIsRenewing}}
                            <button class="account-info-box-button resubscribe-button minor-spacer" style="padding: 8px 12px 8px 12px; width: auto" onclick="redirectToProperPayments()">{{& accountInfoResubscribeButtonTitle}}</button>
                        {{/g_subIsRenewing}}
                    </div>

                    <div class="account-info-box main-spacer">
                        <div class="account-info-header">{{& accountInfoAppSectionTitle}}</div>
                        <div class="account-info-request minor-spacer">
                            <div class="account-info-request-text">{{& accountInfoLinkSubText}}</div>
                            <button class="account-info-request-button" onclick="openAccountLink();">{{& accountInfoLinkSubButton}}</button>
                        </div>
                        <div class="account-info-request minor-spacer">
                            <div class="account-info-request-text">{{& accountInfoLinkWebappText}}</div>
                            <button class="account-info-request-button" onclick="openWebAppLink();">{{& accountInfoLinkWebappButton}}</button>
                        </div>
                        <div class="account-info-request minor-spacer">
                            <div class="account-info-request-text">{{& accountInfoLinkPinText}}</div>
                            <button class="account-info-request-button" onclick="openPinLink();">{{& accountInfoLinkPinButton}}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>
    function redirectToProperPayments() {
        const operator = "{{&g_mondiaOperator}}";
        if (operator === "Vodafone Spain") {
            location.pathname = "/mondiaVodafone"
        } else {
            openPayments();
        }
    }
</script>
<script>{{& g_embeddedJS }}</script>
{{#g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js.gz?v={{& g_assetVersion }}"></script>
{{/g_isProd}}{{^g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js?v={{& g_assetVersion }}"></script>
{{/g_isProd}}
</html>
