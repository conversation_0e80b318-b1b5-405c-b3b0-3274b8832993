<body dir="auto">
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div class="header card-width">
        <div class="left-box"></div>
        <div class="kidjo-logo"></div>
        <div class="right-box">
            <button class="right-box-text" onclick="openLogout();">
                {{&accountNavigationLogout}}
            </button>
        </div>
    </div>
    <div class="sign-up-card-container cc-payment-card card-width">
        <div class="sign-up-card cc-payment-card">
            <div class="sign-up-card-inner cc-payment-card">
                <div class="sign-up-card-normal cc-payment-card">
                    <div class="section-header"{{&accountPaymentCcTitle}}</div>
                    {{#g_hasCoupon}}
                        {{& g_couponBadge}}
                    {{/g_hasCoupon}}
                <div id="error-box" class="hidden"></div>
                <div class="section-subheader main-spacer cc-container">
                    <input class="cc-name-input input-div" placeholder="{{&accountPaymentCcName}}">
                    <div class="line-spacer"></div>
                    <div class="cc-num-input input-div"></div>
                    <div class="line-spacer"></div>
                    <div class="expire-and-cvv">
                        <div class="cc-ccv-input input-div"></div>
                        <div class="width-spacer"></div>
                        <div class="cc-expire input-div"></div>
                    </div>
                </div>
                <div class="plan-container main-spacer">
                    <div class="plan-label">{{&accountPlanTitle}}</div>
                    <div class="plan-description-container">
                        <div class="plan-time">{{&planMonthlyTitle}}</div>
                        <div class="plan-price">{{&g_planCostString}}</div>
                    </div>
                </div>

                <div class="g-recaptcha" data-sitekey="6LeX2M0UAAAAAPmq_h6p4sAV4wwBQpFdH6JQhQMs"></div>

                <button id="cc-button" class="main-spacer account-button">
                    {{&accountPaymentCcComplete}}
                </button>
                <div class="subscript-footer minor-spacer">
                    {{&accountPlanNote}}
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="https://js.braintreegateway.com/web/{{&g_brainTreeLibVersion}}/js/client.min.js"></script>
<script src="https://js.braintreegateway.com/web/{{&g_brainTreeLibVersion}}/js/hosted-fields.min.js"></script>
<script src="https://js.braintreegateway.com/web/{{&g_brainTreeLibVersion}}/js/data-collector.min.js"></script>
<script>{{& g_embeddedJS }}</script>
{{#g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js.gz?v={{& g_assetVersion }}"></script>
{{/g_isProd}}{{^g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js?v={{& g_assetVersion }}"></script>
{{/g_isProd}}
<script>setupCC();</script>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
</html>
