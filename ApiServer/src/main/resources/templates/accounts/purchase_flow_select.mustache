<body dir="auto">
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div class="header card-width">
        <div class="left-box"></div>
        <div class="kidjo-logo"></div>
        <div class="right-box">
            <button class="right-box-text" onclick="openLogout();">
                {{&accountNavigationLogout}}
            </button>
        </div>
    </div>
    <div class="sign-up-card-container payment-card card-width">
        <div class="sign-up-card payment-card">
            <div class="sign-up-card-inner payment-card">
                <div class="sign-up-card-normal payment-card">
                    {{#g_hasCoupon}}
                        <div class="section-header">{{&couponPaymentSelectTitle}}</div>
                        <div class="section-subheader minor-spacer">{{&couponPaymentSelectSubtitle1}}</div>
                        {{& g_couponBadge}}
                        <div class="section-subheader minor-spacer">{{&couponPaymentSelectSubtitle2}}</div>
                    {{/g_hasCoupon}}{{^g_hasCoupon}}
                        <div class="section-header">{{&accountPaymentSelectTitle}}</div>
                        <div class="section-subheader minor-spacer">{{&accountPaymentSelectSubtitle1}}</div>
                        <div class="section-subheader minor-spacer">{{&accountPaymentSelectSubtitle2}}</div>
                    {{/g_hasCoupon}}

                    <button class="main-spacer account-button-icon payment-button credit-card" onclick="checkConsent(this, event)">
                        <div class="icon credit-card-icon"></div>
                        <div class="text">{{&accountPaymentSelectCard}}</div>
                    </button>

                    {{^g_hasCoupon}}
                    <button class="main-spacer account-button-icon payment-button coupon" onclick="checkConsent(this, event)">
                        <div class="icon coupon-icon"></div>
                        <div class="text">{{&accountPaymentSelectCoupon}}</div>
                    </button>
                    {{/g_hasCoupon}}

                    <div id="paypal-button" class="main-spacer paypal-button" onclick="checkConsent(this, event)"></div>

                    <div class="gsc-confirm main-spacer">
                        <input id="gsc-confirm-checkbox" type="checkbox" name="consent" style="margin-right: 8px;">
                        <span id="gsc-confirm-text" style="padding: 8px;">{{&accountSubscriptionTermsApproval}}</span>
                        <div class="error" style="display: none;"></div>
                    </div>

                    <div class="plan-container main-spacer">
                        <div class="plan-label">{{&accountPlanTitle}}</div>
                        <div class="plan-description-container">
                            <div class="plan-time">{{&planMonthlyTitle}}</div>
                            <div class="plan-price">{{&g_planCostString}}</div>
                        </div>
                    </div>
                    <div class="subscript-footer minor-spacer">
                        {{&accountPlanNote}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="https://www.paypalobjects.com/api/checkout.js" data-version-4 log-level="warn"></script>
<script src="https://js.braintreegateway.com/web/{{&g_brainTreeLibVersion}}/js/client.min.js"></script>
<script src="https://js.braintreegateway.com/web/{{&g_brainTreeLibVersion}}/js/paypal-checkout.min.js"></script>
<script src="https://js.braintreegateway.com/web/{{&g_brainTreeLibVersion}}/js/data-collector.min.js"></script>
<script>{{& g_embeddedJS }}</script>
{{#g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js.gz?v={{& g_assetVersion }}"></script>
{{/g_isProd}}{{^g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js?v={{& g_assetVersion }}"></script>
{{/g_isProd}}
<script>setupPaypal();</script>
<script>
    function checkConsent(element, event) {
        var checkbox = document.getElementById('gsc-confirm-checkbox');
        if (checkbox.checked) {
            if (element.classList.contains('credit-card')) {
                openCreditCard();
            } else if (element.classList.contains('coupon')) {
                openCoupon();
            } else if (element.classList.contains('paypal-button')) {
                return;
            }
        } else {
            var text = document.getElementById('gsc-confirm-text');
            checkbox.style.marginRight = '6px';
            text.style.border = '2px solid red';
        }

        event.preventDefault();
    }
</script>
</html>
