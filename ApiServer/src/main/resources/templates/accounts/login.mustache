<body dir="auto">
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div class="header card-width">
        <div class="left-box"></div>
        <div class="kidjo-logo"></div>
        <div class="right-box"></div>
    </div>
    <div class="sign-up-card-container account-card card-width">
        <div class="sign-up-card account-card">
            <div class="sign-up-card-inner account-card">
                <div class="sign-up-card-normal account-card">
                    {{#g_hasCoupon}}
                        <div class="section-header">{{&couponRegisterTitle}}</div>
                        <div class="section-subheader-1 minor-spacer">{{&couponRegisterSubtitle}}</div>
                        {{& g_couponBadge}}
                    {{/g_hasCoupon}}{{^g_hasCoupon}}
                    <div class="section-header-1">{{&accountRegisterTitle1}}</div>
                    <div class="section-header-2">{{&accountRegisterTitle2}}</div>
                    <div class="section-header-3">{{&accountRegisterTitle3}}</div>
                    <div class="section-subheader minor-spacer">{{&accountRegisterSubtitle1}}</div>
                {{/g_hasCoupon}}
                    <button class="account-button-icon fb-sign-in large-spacer" onclick="fbLogin();">
                        <div class="icon facebook-icon"></div>
                        <div class="text">{{&accountFacebookLogin}}</div>
                    </button>
                    <button class="account-button-icon google-sign-in minor-spacer" onclick="gLogin();">
                        <div class="icon google-icon"></div>
                        <div class="text">{{&accountGoogleLogin}}</div>
                    </button>
                    <div id="input-error" class="error-message"></div>
                    <div class="email-pass-input-box">
                        <input class="email" type="email" placeholder="{{&accountPlaceholdersEmail}}" />
                        <div class="input-line-spacer"></div>
                        <input class="password" type="password" placeholder="{{&accountPlaceholdersPassword}}" />
                    </div>
                    <a class="forgotten-password" href="/account/password">{{&accountForgottenPassword}}</a>
                    <div class="email-button-row minor-spacer">
                        <button class="account-button" onclick="loginAccount('email', undefined, '{{&errorGeneric}}', '{{&errorWrongCredentials}}');">{{&accountLoginLoginButton}}</button>
                        <button class="account-button" onclick="createAccount('email', undefined, '{{&errorGeneric}}');">{{&accountRegisterRegisterButton}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>{{& g_embeddedJS }}</script>
{{#g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js.gz?v={{& g_assetVersion }}"></script>
{{/g_isProd}}{{^g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js?v={{& g_assetVersion }}"></script>
{{/g_isProd}}
<script src="https://apis.google.com/js/platform.js" onload="gLoaded();" async defer ></script>
<script>setUpOauth();</script>
</html>
