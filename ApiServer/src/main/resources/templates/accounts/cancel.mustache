<body dir="auto">
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div class="page">
    <div class="header card-width">
        <div class="left-box"></div>
        <div class="kidjo-logo"></div>
        <div class="right-box">
            <button class="right-box-text" onclick="openLogout();">
                {{&accountNavigationLogout}}
            </button>
        </div>
    </div>
    <div class="sign-up-card-container card-width">
        <div class="sign-up-card">
            <div class="sign-up-card-inner">
                <div class="sign-up-card-normal">
                    <div class="coupon-enter-container">
                        <div class="section-header-1">{{&accountCancelTitle}}</div>
                        <div class="section-header-3 minor-spacer">{{&accountCancelSubtitle}}</div>
                        <div class="section-subheader main-spacer cancel-message">{{&g_cancelMessage1}} {{&g_cancelMessage2}}</div>
                        <!--<div class="section-subheader cancel-message"></div>-->
                        <button class="account-button do-not-cancel large-spacer" onclick="openAccountInfo();">
                            {{&accountCancelGoBack}}
                        </button>
                        <button class="account-button cancel-button main-spacer" onclick="cancelSubscription();">
                            {{&accountCancelActuallyCancel}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>{{& g_embeddedJS }}</script>
{{#g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js.gz?v={{& g_assetVersion }}"></script>
{{/g_isProd}}{{^g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js?v={{& g_assetVersion }}"></script>
{{/g_isProd}}
</html>
