<body dir="auto">
<div id="loading-view" class="loading-card-container">
    <div class="loading-card">
        <div class="loading-text">
            {{&accountLoadingMessage}}
        </div>
        <div class="loading-spinner"></div>
    </div>
</div>
<div style="max-width: 1000px; margin: 0 auto;">
    <div class="header card-width" style="height: 100px">
        <div class="left-box"></div>
        <div class="kidjo-logo" style="width: 200px"></div>
        <div class="right-box"></div>
    </div>
    <div class="sign-up-card-container account-card">
        <div class="sign-up-card account-card">
            <div class="sign-up-card-inner account-card">
                <div class="sign-up-card-normal account-card">
                    <div class="section-header-1">{{&accountRegisterTitle1}}</div>
                    <div class="section-header-3">{{&accountRegisterTitle3}}</div>
                    <div id="input-error" class="error-message"></div>
                    <div class="email-pass-input-box">
                        <input class="email" type="email" placeholder="{{&accountPlaceholdersEmail}}" />
                        <div class="input-line-spacer"></div>
                        <input class="password" type="password" placeholder="{{&accountPlaceholdersPassword}}" />
                    </div>
                    <div class="large-spacer">
                        <button class="account-button"  style="width: 100%;" onclick="loginAccount('email', undefined, '{{&errorGeneric}}', '{{&errorWrongCredentials}}');">{{&accountLoginLoginButton}}</button>
                    </div>
                </div>

                <div style="display: flex; width: 1px; background-color: black; margin: 0 20px; opacity: 0.2">
                </div>

                <div class="sign-up-card-normal account-card">
                    <div class="section-header-1">{{&accountLoginRegister}}</div>
                    <div class="large-spacer" style="text-align: center;">
                        <img src="{{& g_assetRoot}}images/account_qrcode.png" alt="Kidjo Account"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
{{#g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js.gz?v={{& g_assetVersion }}"></script>
{{/g_isProd}}{{^g_isProd}}
    <script type="text/javascript" src="{{& g_assetRoot}}js/account_coupon.js?v={{& g_assetVersion }}"></script>
{{/g_isProd}}
</html>
