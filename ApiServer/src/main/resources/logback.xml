<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{YYYY-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>
  <root level="info">
    <appender-ref ref="STDOUT"/>
  </root>
  <logger name="ktor.application" level="INFO"/>
  <logger name="org.eclipse.jetty" level="INFO"/>
  <logger name="io.netty" level="INFO"/>
  <logger name="com.zaxxer.hikari.pool.PoolBase" level="ERROR"/>
  <logger name="com.zaxxer.hikari.pool.HikariPool" level="ERROR"/>
  <logger name="com.zaxxer.hikari.HikariDataSource" level="ERROR"/>
  <logger name="io.lettuce" level="ERROR"/>
  <logger name="reactor" level="INFO"/>
  <logger name="net.kidjo.plugins.FlywayFeature" level="ERROR"/>
  <logger name="net.kidjo.*" level="INFO"/>
</configuration>
