package net.kidjo.server.models

data class Video(
    val id: String,
    val title: String,
    val type: String = "video",
    val minAge: Int,
    val maxAge: Int,
    val isPremium: Boolean,
    val thumbnail: String,
    val language: String?,
    val order: Int,
    val duration: Int,
    val formats: List<Format>,
)

data class Format(
    val fileSize: Long,
    val href: String,
    val height: Int,
)
