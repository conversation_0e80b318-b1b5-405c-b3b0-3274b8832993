package net.kidjo.server.models.cafeyn

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import kotlinx.serialization.Serializable

@JsonIgnoreProperties(ignoreUnknown = true)
@Serializable
data class CafeynLoginResponse(
    val token: String,
    val email: String,
    val user_id: Int,
    val partner_name: String,
    val partner_description: String,
    val partner_id: Int,
    val is_active: Boolean,
    val is_sso_activated: Boolean,
    val bundles: List<String>,
    val subscribed_services: List<String>
)

