package net.kidjo.server.models.mondia

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConsentUpdateBean(
    @JsonProperty("consentId") val consentId: String,
    @JsonProperty("userUuid") val userUuid: String?,
    @JsonProperty("forcesSubscriptionCancellation") val forcesSubscriptionCancellation: Boolean?,
    @JsonProperty("dateTimeOfAcknowledgement") val dateTimeOfAcknowledgement: String?,
    @JsonProperty("entitlements") val entitlements: List<Entitlement>?,
    @JsonProperty("consentWithdrawalRecordCreated") val consentWithdrawalRecordCreated: String?,
    @JsonProperty("signature") val signature: String?,
    @JsonProperty("eventId") val eventId: String?,
    @JsonProperty("customerAcknowledgedCancellation") val customerAcknowledgedCancellation: Boolean?
)