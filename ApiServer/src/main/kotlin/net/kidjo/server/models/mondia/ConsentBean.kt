package net.kidjo.server.models.mondia

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConsentBean(
    @JsonProperty("consentId") val consentId: String,
    @JsonProperty("userUuid") val userUuid: String?,
    @JsonProperty("flow") val flow: String?,
    @JsonProperty("fields") val fields: List<String>?,
    @JsonProperty("entitlements") val entitlements: List<Entitlement>?,
    @JsonProperty("consentRecordCreated") val consentRecordCreated: String?,
    @JsonProperty("signature") val signature: String?,
    @JsonProperty("eventId") val eventId: String?
)