package net.kidjo.server.models

import net.kidjo.server.models.enums.Event
import net.kidjo.server.shared.models.Product
import java.time.LocalDateTime

data class PartnerSubscriptionRequest(
    val event: Event,
    val email: String?,
    val msisdn: String?,
    val partnerUserId: String?,
    val countryCode: String,
    val product: List<Product>,
    val isTrail: Boolean? = false,
    val nextBillingDate: LocalDateTime,
    val firstBillingDate: LocalDateTime?,
    val token: String,
    val test: Boolean? = false,
    val params: Map<String, Any>?,
)
