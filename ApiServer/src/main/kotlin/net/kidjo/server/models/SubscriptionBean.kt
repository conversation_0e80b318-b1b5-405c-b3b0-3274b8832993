package net.kidjo.server.models

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import net.kidjo.server.service.UUID_EMAIL_LENGTH
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


@JsonIgnoreProperties(ignoreUnknown = true)

data class SubscriptionBean @JsonCreator constructor(
    @JsonProperty("productid") val productId: Int = 0,
    @JsonProperty("userUuid") val userUuid: String = "",
    @JsonProperty("packageId") val packageId: Int = 0,
    @JsonProperty("event") val event: String = "",
    @JsonProperty("state") val state: String = "",
    @JsonProperty("subState") val subState: String = "",
    @JsonProperty("startDate") val startDate: String = "",
    @JsonProperty("endDate") var endDate: String? = "",
    @JsonProperty("trialEndDate") var trialEndDate: String? = "",
    //@JsonProperty("consentId") val consentId: Int = 0,
    @JsonProperty("consentId") val consentId: String? = "",
    @JsonProperty("userDetails") var userDetails: List<UserDetails>?,
    @JsonProperty("eventId") val eventId: String = "",

    @JsonProperty("subscriptionId") val subscriptionId: Long = 0,
    @JsonProperty("userId") val userId: Long = 0,
    @JsonProperty("externalIdentifier") val externalIdentifier: String? = "",
    @JsonProperty("lastModifiedDate") val lastModifiedDate: String = ""
) {


    init {
        if (userDetails.isNullOrEmpty()) {
            val shortUserUuid = userUuid.take(UUID_EMAIL_LENGTH)
            userDetails = listOf(
                UserDetails(name = "FirstName", value = userUuid),
                UserDetails(name = "email", value = "$<EMAIL>"),
            )
        }
        if (trialEndDate.isNullOrEmpty()) {
            trialEndDate = ""
        }
    }


    fun updateEndDateIfNull(mondiaPackage: String) {

        if (endDate.isNullOrEmpty()) {
            endDate = getLastDate()?.let { lastDate ->
                val dateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ISO_DATE_TIME
                val parsedLastDate =
                    lastDate.takeIf { it.isNotEmpty() }.let { LocalDateTime.parse(it, dateTimeFormatter) }
                when (mondiaPackage) {
                    "DAILY" -> parsedLastDate.plusDays(1)
                    "WEEKLY" -> parsedLastDate.plusWeeks(1)
                    "MONTHLY" -> parsedLastDate.plusMonths(1)
                    "HALF_YEARLY" -> parsedLastDate.plusMonths(6)
                    "YEARLY" -> parsedLastDate.plusYears(1)
                    else -> parsedLastDate // For unknown package, return lastDate without modification
                }.toString()
            } ?: ""
        }
    }

    private fun getLastDate(): String {
        val dateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ISO_DATE_TIME
        val startDateDate = startDate.takeIf { it.isNotEmpty() }?.let { LocalDateTime.parse(it, dateTimeFormatter) }
        val trialEndDateDate =
            trialEndDate.takeIf { it!!.isNotEmpty() }?.let { LocalDateTime.parse(it, dateTimeFormatter) }
        // Compare dates and return the last one
        return listOfNotNull(startDateDate, trialEndDateDate).maxOrNull()?.toString() ?: ""
    }
}