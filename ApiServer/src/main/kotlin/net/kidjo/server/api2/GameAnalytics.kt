package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveParameters
import net.kidjo.common.models.Game
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.extensions.*


suspend fun Api2Controller.a_gamePlayed(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    databaseController.device_checkAndGetDeviceInfo(device)

    val query = call.parameters
    val args = call.receiveParameters()

    if (!device.isDeviceIdValid) {
        call.respondDatabaseIssue()
        return
    } else if (!query.contains("id") || !args.contains("folderId") || !args.contains("dif")
            || !args.contains("completion") || !args.contains("avatarId") || !args.contains("timeSpent")) {
        call.respondBadParameters()
        return
    }


    val id = query.getLong("id")
    val folderId = args.getLong("folderId")
    val avatarId = args.getDecodedNormalId("avatarId",encryptionController)
    val percentComplete = args.getInt("completion")
    val timeSpent = args.getInt("timeSpent")

//    val complete = databaseController.game_playedAnalytics(device.serverId,avatarId,id,folderId,difficulty,percentComplete,timeSpent)
    call.respondOK()
//    if (complete) call.respondOK()
//    else call.respondDatabaseIssue()
}
