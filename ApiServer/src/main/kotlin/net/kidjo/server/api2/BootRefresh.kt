package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.server.plugins.origin
import io.ktor.server.request.receiveParameters
import net.kidjo.common.models.Language
import net.kidjo.server.api2.json.kid_toJSON
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.User
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

private val timezoneRegex = Regex("^UTC([+-])([01]?[0-9]):([0-9]{2})H")
private val API_VERSION_2_ID = "v2.0"
suspend fun Api2Controller.bootRefresh(call: ApplicationCall) {
    val checkDevice = call.getDeviceFromHeader(encryptionController)
    val encodedDeviceId: Long
    val languageId: Int
    val kids: List<Kid>
    val args = call.receiveParameters()

    if (!checkDevice.isDeviceIdValid) {
        if (!args.contains("deviceManufacturer") || !args.contains("deviceModel") || !args.contains("deviceLocale") || !args.contains(
                "localTimeZone"
            )
        ) {
            call.respondBadParameters()
            return
        }

        val deviceManufacturer = args.getString("deviceManufacturer").toLowerCase()
        val deviceModel = args.getString("deviceModel").toLowerCase()
        val deviceLocale = args.getString("deviceLocale")
        val localTimeZone = args.getString("localTimeZone")
        val store = args.getString("store").toLowerCase()

        //timezone area
        var databaseTimeZone: Int
        try {
            val timezoneSearch = timezoneRegex.findAll(localTimeZone).first().groupValues
            val zoneStringElement1 = if (timezoneSearch[1] == "+") 1 else -1
            val zoneStringElement2 = timezoneSearch[2].toInt() * 60
            val zoneStringElement3 = timezoneSearch[3].toInt()

            databaseTimeZone = zoneStringElement1 * (zoneStringElement2 + zoneStringElement3)
        } catch (e: Exception) {
            println("Error timezonning: databaseTimeZone ${e.message}" )
            databaseTimeZone = -999
        }

        //locale
        var locale = validator.createLocale(deviceLocale)
        var response: Response? = null
        var geoPluginJSON: JSONObject? = null
        try {
            // Get country over ip
            val client = OkHttpClient()
            val url =
                "https://api.kidjo.tv/v2/country${if (call.request.origin.remoteHost == "0:0:0:0:0:0:0:1") "" else "?ip=${call.request.origin.remoteHost}"}"
            val request = Request.Builder().url(url).build()
            response = client.newCall(request).execute()
            if (response != null) {
                val geoPluginResponse = response.body()!!.string()
                geoPluginJSON = JSONObject(geoPluginResponse)
            }
        } catch (e: Exception) {
            println("Error invoke <<https://api.kidjo.tv/v2/country>> ${e.message}")
        } finally {
            if (response != null) response.close()
        }

        val countryIp: String = if (geoPluginJSON != null) geoPluginJSON.getString("geoplugin_countryCode") else ""

        // Check the locale.country is valid
        if (locale.country.contains("@")) {
            locale = Locale(locale.language, locale.country.substring(0, locale.country.indexOf("@")))
        }

        // Construct localString (localeString)
        val localString = "${locale.language}_${if (countryIp != "") countryIp else locale.country}"

        var countryId: Int =
            databaseController.user_getCountryIdFromShortName(if (countryIp != "") countryIp else locale.country)
        if (countryId < 1 || countryId > 249) {
            countryId = 237 // US
            println("issue finding country")
            println("locale: $localString")
            println("device: $deviceModel")
            println("info: $args")
        }

        var usingLanguageId: Int =
            languageCache.getLanguageIdWithShortName(if (countryIp == "TN") "fr" else locale.language)
        if (usingLanguageId == Language.NO_ID) usingLanguageId = Language.ID_ENGLISH

        val databaseIdForDevice = databaseController.device_new(
            deviceManufacturer,
            User.NO_SERVER_ID,
            deviceModel,
            localString,
            countryId,
            databaseTimeZone,
            usingLanguageId,
            API_VERSION_2_ID,
            store,
            checkDevice.userAgent
        )
        if (databaseIdForDevice == User.NO_SERVER_ID) {
            call.respondBadDevice()
            return
        }

        kids = databaseController.kid_createThreeDefaultKids(databaseIdForDevice)
        var encodedDeviceIdTest = Device.NO_SERVER_ID
        try {
            encodedDeviceIdTest = encryptionController.encodeNormalId(databaseIdForDevice).toLong()
        } catch (e: Exception) {
            println("Error encryption encodedDeviceIdTest ${e.message}")
        }
        encodedDeviceId = encodedDeviceIdTest

        languageId = usingLanguageId
    } else {
        databaseController.device_checkAndGetDeviceInfo(checkDevice)
        if (!checkDevice.isDeviceIdRegistered) {
            call.respondBadDevice()
            return
        }
        encodedDeviceId = checkDevice.getLongUserId()
        languageId = checkDevice.languageId
        kids = databaseController.kid_getByDeviceId(checkDevice.serverId)
    }
    val userToken = args.getString("token")
    var isSubscribedFromApi: Boolean = false
    var isSubscribedFromAccount: Boolean
    if (userToken != "") {
        isSubscribedFromAccount = databaseController.user_getSubscriptionFromSession(userToken)
    } else {
        isSubscribedFromAccount = false
    }

    if (checkDevice.isDeviceIdRegistered) {
        isSubscribedFromApi = databaseController.getActiveDeviceSubscription(checkDevice.serverId)
    }

    val json: JSONObject = JSONObject()
    val jsonArray = JSONArray()
    kids.forEach { jsonArray.put(jsonCreator.kid_toJSON(it)) }

    json.put("avatars", jsonArray)
    json.put("deviceId", encodedDeviceId)
    json.put("subActiveFromApi", isSubscribedFromApi)
    json.put("subActiveFromAccount", isSubscribedFromAccount)
    json.put("languages", languageCache.getLanguageJSON(languageId, jsonCreator))

    return call.respondJSON(json)
}
