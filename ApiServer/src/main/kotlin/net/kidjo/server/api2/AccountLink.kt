package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.server.plugins.origin
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import net.kidjo.server.shared.cachedatabase.getCountryInfoByIp
import net.kidjo.server.shared.cachedatabase.getDeivceUserPairingToken
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.database.user_getById
import net.kidjo.server.shared.extensions.*
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.util.concurrent.TimeUnit


suspend fun Api2Controller.account_link(call: ApplicationCall) {
    val args = call.receiveParameters()

    val token = args.getString("token")
    if (token == "") {
        call.respondBadParameters()
        return
    }

    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respond404()
        return
    }
    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.infoFromServer) {
        call.respond404()
        return
    }


    val deviceLink = cacheDatabase.getDeivceUserPairingToken(token)
    if (deviceLink == null || !deviceLink.isRequestingDevicePair || deviceLink.code != token) {
        call.respond404()
        return
    }
    val user = databaseController.user_getById(deviceLink.userId)
    if (user == null) {
        call.respond404()
        return
    }

    val userToken = userManager.setUserLegacy(user)
    val json = JSONObject()
    json.put("subActiveFromAccount",user.isSubscribed)
    json.put("token",userToken)
    call.respondJSON(json)
}

suspend fun Api2Controller.getCountryByIp(call: ApplicationCall) {
    val query = call.parameters
    val queryIp = query.getString("ip")
    println("Ip: ${queryIp}")

    val ip: String? = if (queryIp != "") queryIp else call.request.origin.remoteHost

    // Use cached version with automatic API fallback
    val countryInfo = cacheDatabase.getCountryInfoByIp(ip)

    if (countryInfo.isNotEmpty()) {
        call.respond(countryInfo)
    } else {
        call.respond("")
    }
}
