package net.kidjo.server.api2

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.common.models.Language
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.database.language_setActiveForDevice
import net.kidjo.server.shared.extensions.*
import org.json.JSONObject


suspend fun Api2Controller.languages_get(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }
    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return
    }

    val json = JSONObject()
    json.put("languages", languageCache.getLanguageJSON(device.languageId, jsonCreator))

    call.respondText(json.toString(), ContentType.Application.Json)
}
suspend fun Api2Controller.languages_set(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }
    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return
    }

    var languageId: Int = Language.NO_ID

    // Accept String from MobileApp
    var requestString: String = ""

    try {
        requestString = call.receiveText()
        println(requestString)
        if (requestString != "" && requestString.contains("=")) {
            languageId = requestString.substring(requestString.indexOf("=") + 1, requestString.length).toInt()
        }

    } catch (e: Exception) {
        println("Failed to retrieve String: ${e.localizedMessage}")
    }

    // Accept JSON from WebApp
    var requestJSON: JSONObject? = null

    println(requestString)

    if (requestString == "" || requestString.contains(":")) {

        try {
            requestJSON = JSONObject(requestString)
            println(requestJSON)
            languageId = requestJSON!!.optInt("languageId", Language.NO_ID)
        } catch (e: Exception) {
            println("Failed to retrieve JSON: ${e.localizedMessage}")
        }
    }

    if (requestJSON == null && requestString == "") {
        call.respondBadJSON()
        return
    }

    println("setLanguage: ${languageId}")

    if (languageId == Language.NO_ID) {
        call.respondBadRequest("bad id")
        return
    } else if (languageId == device.languageId) {
        call.respond(HttpStatusCode.OK)
        return
    } else if (!languageCache.languageIsSupported(languageId)) {
        call.respondBadRequest("language inactive or not found")
        return
    }

    val results = databaseController.language_setActiveForDevice(device.serverId, languageId)

    if (results) call.respond(HttpStatusCode.OK)
    else call.respondDatabaseIssue()
}
