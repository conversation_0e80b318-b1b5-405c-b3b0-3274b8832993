package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveParameters
import net.kidjo.common.models.SubscriptionEvent
import net.kidjo.server.shared.database.wv_analyticsEvent
import net.kidjo.server.shared.extensions.*

suspend fun Api2Controller.a_subscriptionWVButton(call: ApplicationCall) {
    return call.respondOK()
}

suspend fun Api2Controller.a_subscriptionWVOffline(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)

    val args = call.receiveParameters()

    if (!device.isDeviceIdValid) {
        call.respondDatabaseIssue()
        return
    } else if (!args.contains("sessionId")) {
        call.respondBadParameters()
        return
    }

    val sessionId = args.getString("sessionId").shortenTo(32)

    val timeStamp = args.getLong("timeStamp",System.currentTimeMillis())

    val success = databaseController.wv_analyticsEvent(sessionId,SubscriptionEvent.Type.WENT_OFFLINE,timeStamp)

    return if (success) call.respondOK()
    else call.respondDatabaseIssue()
}

suspend fun Api2Controller.a_subscriptionWVFinished(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)

    val args = call.receiveParameters()

    if (!device.isDeviceIdValid) {
        call.respondDatabaseIssue()
        return
    } else if (!args.contains("didSubscribe")) {
        call.respondBadParameters()
        return
    }

    val timeStamp = args.getLong("timeStamp",System.currentTimeMillis())
    val sessionId = args.getString("sessionId").shortenTo(32)
    val didSubscribe = args.getString("didSubscribe") == "true"
    val event = if (didSubscribe) SubscriptionEvent.Type.DID_SUBSCRIBE else SubscriptionEvent.Type.NOT_SUBSCRIBE

    val success = databaseController.wv_analyticsEvent(sessionId,event,timeStamp)

    return if (success) call.respondOK()
    else call.respondDatabaseIssue()
}
