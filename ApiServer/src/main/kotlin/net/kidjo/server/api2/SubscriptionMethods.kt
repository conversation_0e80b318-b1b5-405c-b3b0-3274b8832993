package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveParameters
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.tools.payments.LEGACY_startTrackingPlatformSubscription
import org.json.JSONObject

suspend fun Api2Controller.subscription_setInfo(call: ApplicationCall) {
    val args = call.receiveParameters()

    if (!args.contains("receipt") || !args.contains("iap")) {
        call.respondBadParameters()
        return
    }
    val device = call.getDeviceFromHeader(encryptionController)
    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.infoFromServer) {
        call.respondBadDevice()
        return
    }

    val receipt = args.getString("receipt")
    val iapId = args.getString("iap")
    val attributionId = args.getString("link")
    val orderId = args.getString("orderId")
    val sessionId = args.getString("sessionId","NONE")

    var store = device.store
    if (args.get("deviceType")?.toLowerCase() == "ios") {
        store = Device.StorePlatform.IOS
    } else if (store == Device.StorePlatform.KIDJO_BRAINTREE) {
        store = Device.StorePlatform.PLAYSTORE
    }

    val iap = iapManager.getIAP(store, iapId)

    if (iap == null) {
        call.respondBadRequest("can't find iap for ${store.raw} with iap: $iapId")
        return
    }



    val success = paymentManager.LEGACY_startTrackingPlatformSubscription(device.serverId,iap,receipt,orderId,sessionId,attributionId)

    if (success) call.respondOK()
    else call.respondBadRequest()
}
suspend fun Api2Controller.subscription_getIAPForSettings(call: ApplicationCall) {
    val args = call.receiveParameters()
    val storeType = Device.StorePlatform.fromRow(args.getString("deviceType"))
    val build = args.getInt("buildNumber")
    val defaultIap = iapManager.getDefaultIAP(storeType,true,true,build)

    val json = JSONObject()
    json.put("iap",defaultIap.id)
    json.put("freeTrial",defaultIap.freeTrialInDays)

    call.respondJSON(json)
}
