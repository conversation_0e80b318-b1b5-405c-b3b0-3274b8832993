package net.kidjo.server.api2.json

import net.kidjo.server.shared.models.User
import org.json.JSONObject


fun JsonObjectCreatorV2.user_toJSON(user: User, token: String): JSONObject {
    val json = JSONObject()
    json.put("id", user.id)
    json.put("email", user.email)
    json.put("oAuthType", user.oAuthType.raw)
    json.put("subscribed", user.isSubscribed)
    json.put("token",token)
    return json
}