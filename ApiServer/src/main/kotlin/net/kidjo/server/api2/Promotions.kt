package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.ContentType
import io.ktor.server.response.respondText
import net.kidjo.server.shared.database.promotion_getByDeepLinkId
import net.kidjo.server.shared.extensions.*
import org.json.JSONObject


suspend fun Api2Controller.promotions_get(call: ApplicationCall) {
    val query = call.parameters

    if (!query.contains("promotion")) {
        call.respondBadParameters()
        return
    }

    val promotionId = query.getString("promotion")
    val promotion = databaseController.promotion_getByDeepLinkId(promotionId)

    if (promotion == null) {
        call.respond404("Promotion is not valid")
        return
    }

    val json = JSONObject()
    json.put("promotion", promotion.deepLinkId)
    call.respondText(json.toString(), ContentType.Application.Json)
}
