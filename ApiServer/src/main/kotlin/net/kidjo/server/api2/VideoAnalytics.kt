package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import net.kidjo.server.shared.cachedatabase.recentVideos_add
import net.kidjo.server.shared.database.video_playedAnalytics
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User

suspend fun Api2Controller.a_videoPlayed(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val query = call.parameters
    val args = call.receiveParameters()

    if (!device.isDeviceIdValid) {
        call.respondDatabaseIssue()
        return
    } else if (!query.contains("videoId") || !args.contains("avatarId") || !args.contains("endedAtSeconds")) {
        call.respondBadParameters()
        return
    }

    val videoIdEncoded = query.getString("videoId")
    val kidId = args.getDecodedNormalId("avatarId", encryptionController)
    val endAtSeconds = args.getInt("endedAtSeconds")

    if (videoIdEncoded == "" || kidId == User.NO_SERVER_ID) {
        call.respondBadParameters()
        return
    } else if (endAtSeconds <= 0) {
        call.respondOK()
        return
    }
    val videoId = encryptionController.decodeVideoId(videoIdEncoded)

    GlobalScope.launch { cacheDatabase.recentVideos_add(kidId, videoIdEncoded) }

    val success = databaseController.video_playedAnalytics(device.serverId,kidId,videoId,endAtSeconds)


    if (success) call.respond(HttpStatusCode.OK)
    else call.respondDatabaseIssue()
}
