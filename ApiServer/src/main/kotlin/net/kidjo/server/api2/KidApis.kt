package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.decodeURLQueryComponent
import io.ktor.server.request.receiveText
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import net.kidjo.server.api2.json.cards_filterFolderToJSON
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.User
import org.json.JSONArray
import org.json.JSONObject
import java.net.URLDecoder

suspend fun Api2Controller.kid_delete(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }
    val query = call.parameters
    val oldKidId = query.getDecodedNormalId("kidId", encryptionController)
    if (oldKidId == Kid.NO_ID_SERVER) {
        call.respondBadParameters()
        return
    }

    val oldKid = databaseController.kid_get(oldKidId)

    if (oldKid == null) {
        call.respondBadRequest("kid doesn't exist")
        return
    } else if (oldKid.serverParentAccountId != device.serverId) {
        call.respondBadRequest("yaa...")
        return
    }

    val newId = databaseController.kid_deleteAndGetNewOne(oldKid.serverId,device.serverId)
    if (newId != Kid.NO_ID_SERVER) {
        val json = JSONObject()
        json.put("avatarId", newId)
        call.respondText(json.toString(), ContentType.Application.Json)
    } else {
        call.respondBadRequest("no")
    }

}

private val regexForName = kotlin.text.Regex("(folderIsActive)([\\[])([0-9]{1,})([\\]])", RegexOption.IGNORE_CASE)
private val regexForId = kotlin.text.Regex("([0-9]{1,})")
suspend fun Api2Controller.kid_update(call: ApplicationCall) {
    val query = call.parameters
    val kidId = query.getDecodedNormalId("kidId", encryptionController)

    var name: String = ""
    var age: Int = 0

    var requestString = call.receiveText()
    var requestJSON: JSONObject? = JSONObject()

    if (requestString.isNotEmpty()) {
        requestString = URLDecoder.decode(requestString.decodeURLQueryComponent(), Charsets.UTF_8.name())
        requestString.split("&").forEach {
            val keyValue = it.split("=")
            if (keyValue.size > 1) {
                requestJSON?.put(keyValue[0], keyValue[1])
            }
        }
    } else {
        requestJSON = call.receiveJSON()
    }

    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    if (requestJSON.has("name")) {
        name = requestJSON.optString("name", Kid.NO_NAME)
        if (!Kid.CheckName(name)) {
            call.respondBadParameters()
            return
        }
    } else {
        name = Kid.NO_NAME
    }

    if (requestJSON.has("age")) {
        age = requestJSON.optInt("age", Kid.NO_AGE)
        if (!Kid.CheckAge(age)) {
            call.respondBadParameters()
            return
        }
    } else {
        age = Kid.NO_AGE
    }

    val ageAndNameOk = Kid.CheckAge(age) || Kid.CheckName(name)
    val updateKidResults: Boolean?

    if (ageAndNameOk) {
        updateKidResults = databaseController.kid_update(kidId,age,name)
    } else {
        updateKidResults = null
    }

    var okFromFolders = true
    val filterChanges = HashMap<Long, Boolean>()
    val argNames = requestJSON.keys()
    argNames?.forEach {
        val name: String = it?.toString() ?: ""
        if (regexForName.matches(name)) {
            val idString = regexForId.find(name)?.value
            if (idString != null) {
                try {
                    val id = idString.toLong()
                    filterChanges[id] = requestJSON.get(name) == "true"
                } catch (e: Exception) {}
            }
        }
    }
    if (filterChanges.size > 0) {
        okFromFolders = databaseController.kid_updateFilters(kidId, filterChanges)
    }


    val okFromUpdate = updateKidResults ?: true

    println("Update kid: ${okFromUpdate}")

    return if (okFromFolders && okFromUpdate) call.respond(HttpStatusCode.OK)
    else call.respondDatabaseIssue()
}
suspend fun Api2Controller.kid_getFolderFilter(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }

    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.infoFromServer) {
        call.respondBadDevice()
        return
    }
    val args = call.parameters
    val kidId = args.getDecodedNormalId("kidId", encryptionController)

    if (kidId == User.NO_SERVER_ID) {
        call.respondBadRequest()
        return
    }

    val kid = databaseController.kid_get(kidId)

    if (kid == null || kid.serverParentAccountId != device.serverId) {
        call.respondBadRequest("user doesn't exist")
        return
    }

    val usingAge = if (Kid.CheckAge(kid.age)) kid.age else Kid.DEFAULT_AGE
    val folders = databaseController.folders_getFilterList(kid.serverId, usingAge, device.languageId, device.countryId)

    val json = JSONObject()
    json.put("folderImageURL", config.folderThumbnailImageUrl)
    val folderJSONArray = JSONArray()
    folders.forEach { folderJSONArray.put(jsonCreator.cards_filterFolderToJSON(it)) }
    json.put("folders", folderJSONArray)

   return call.respondText(json.toString(), ContentType.Application.Json)
}
