package net.kidjo.server.api2

import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import net.kidjo.server.api2.json.JsonObjectCreatorV2
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector

class Api2Controller(platformInjector: PlatformInjector): BaseServerController(platformInjector) {
    internal val jsonCreator = JsonObjectCreatorV2(config)

    override fun installRoutes(route: Route) {

        //Boot Refresh
        route.post("bootRefresh") { <EMAIL>(call) }

        //getting cards
        route.get("getCards") { <EMAIL>(call) }

        //kid
        route.post("avatars/{kidId}/delete") { this@Api2Controller.kid_delete(call) }
        route.post("kid/{kidId}/delete") { this@Api2Controller.kid_delete(call) }
        route.post("avatars/{kidId}/update") { this@Api2Controller.kid_update(call) }
        route.post("kid/{kidId}/update") { this@Api2Controller.kid_update(call) }
        route.get("avatars/{kidId}/folders") { this@Api2Controller.kid_getFolderFilter(call) }
        route.get("kid/{kidId}/folders") { this@Api2Controller.kid_getFolderFilter(call) }

        //languages
        route.get("getLanguages") { this@Api2Controller.languages_get(call) }

        //device options
        route.post("devices/{deviceId}/setLanguage") { this@Api2Controller.languages_set(call) }
        route.post("devices/{deviceId}/setEmail") { this@Api2Controller.device_setEmail(call) }

        //Subscription stuff
        route.post("subscriptions") { this@Api2Controller.subscription_setInfo(call) }
        route.post("settingsSubscribe") { this@Api2Controller.subscription_getIAPForSettings(call) }

        //coupons
        route.post("coupons/{couponId}") { this@Api2Controller.oldCoupon_check(call) }
        route.post("coupons/{couponId}/check") { this@Api2Controller.oldCoupon_check(call) }

        //promotions
        route.get("promotion") { this@Api2Controller.promotions_get(call) }

        //user hacks
        route.post("/account/link") { this@Api2Controller.account_link(call) }

        //country ip
        route.get("country") { <EMAIL>(call) }

        //=============
        //analytics
        //=============

        //attribution
        route.post("attribution") { <EMAIL>(call) }
        route.post("linkAttribution") { <EMAIL>(call) }

        //subscription
        route.post("subview2/pressed") { this@Api2Controller.a_subscriptionWVButton(call) }
        route.post("subview2/offline") { this@Api2Controller.a_subscriptionWVOffline(call) }
        route.post("subview2/finished") { this@Api2Controller.a_subscriptionWVFinished(call) }

        //videos
        route.post("videos/{videoId}/played") { this@Api2Controller.a_videoPlayed(call) }

        //games
        route.post("game/{gameId}/played") { this@Api2Controller.a_gamePlayed(call) }
    }
}
