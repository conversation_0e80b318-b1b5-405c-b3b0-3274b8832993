package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import net.kidjo.server.shared.database.attribution_addLinkAttributionEvent
import net.kidjo.server.shared.database.attribution_getLinkAttributionByDeeplink
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.LinkAttributionEvent

suspend fun Api2Controller.setAttribution(call: ApplicationCall) {
    call.respond(HttpStatusCode.OK)
}
suspend fun Api2Controller.setLinkAttribution(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)

    val args = call.receiveParameters()

    if (!device.isDeviceIdValid || !args.contains("link")) {
        call.respondBadParameters()
        return
    }

    val linkId = args.getString("link")

    val attributionLink = databaseController.attribution_getLinkAttributionByDeeplink(linkId)
    if (attributionLink == null || !attributionLink.isActive) {
        call.respond404()
        return
    }

    databaseController.attribution_addLinkAttributionEvent(attributionLink.id,LinkAttributionEvent.EventType.INSTALL,device.serverId)
    call.respond(HttpStatusCode.OK)
}
