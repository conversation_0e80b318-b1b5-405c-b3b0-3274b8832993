package net.kidjo.server.api2.json

import net.kidjo.common.models.Language
import net.kidjo.server.shared.cache.LanguageJsonCreator
import net.kidjo.server.shared.tools.Config
import org.json.JSONObject

class JsonObjectCreatorV2(config: Config): LanguageJsonCreator {

    //=========
    //LanguageJsonCreator
    //=========
    override fun language_toJSON(language: Language, isActive: Boolean) : JSONObject {
        val json = JSONObject()
        json.put("id",language.id)
        json.put("name",language.nativeName)
        json.put("isActive",isActive)
        return json
    }
}