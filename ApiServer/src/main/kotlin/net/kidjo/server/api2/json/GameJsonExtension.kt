package net.kidjo.server.api2.json

import net.kidjo.common.models.Game
import org.json.JSONObject


fun JsonObjectCreatorV2.game_toJSon(game: Game): JSONObject {
    val json = JSONObject()
    json.put("variant", "game")
    json.put("id", game.id.toString())
    json.put("isLocked", game.isLocked)
    json.put(
        "gameType", when (game.type) {
            Game.Type.FIND_THE_WAY -> "findTheWay"
            Game.Type.HIDE -> "hideGame"
            else -> game.type.raw
        }
    )
    json.put("dif", game.difficulty.raw)
    return json
}