package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import net.kidjo.server.shared.database.accountCoupon_getByCouponId
import net.kidjo.server.shared.database.couponSet
import net.kidjo.server.shared.extensions.*
import java.sql.Timestamp
import java.time.LocalDateTime


suspend fun Api2Controller.oldCoupon_check(call: ApplicationCall) {
    val query = call.parameters
    val couponId = query.getString("couponId")
    val couponIdUpper = couponId.toUpperCase()

    val accountCoupon = databaseController.accountCoupon_getByCouponId(couponIdUpper)
    if (accountCoupon != null) {
        call.respondOK()
        return
    }

    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }

    val results = databaseController.generic_singleRowQuery("SELECT id, redeemed, validUntil, validCountLeft, duration, substring_index(group_concat(coupons_devices.activation), ',', 10000) as activations, substring_index(group_concat(coupons_devices.deviceId), ',', 10000) as devices FROM coupons LEFT JOIN coupons_devices ON coupons.id=coupons_devices.couponId WHERE value = ? GROUP BY coupons_devices.couponId, id") { statement ->
        statement.setString(1, couponId)
    }

    if (results == null) {
        call.respond404("Invalid coupon")
        return
    }

    val deviceArray: List<String> = (results["devices"] as? String)?.split(",") ?: ArrayList()
    val activationArray: List<Timestamp?> = (results["activations"] as? String)?.split(",")?.map{ Timestamp.valueOf(it) } ?: ArrayList()
    if (deviceArray.contains(device.serverId.toString())) {
        val validUntilSqlDate = (results["validUntil"] as? java.sql.Timestamp)
        if (validUntilSqlDate == null) {
            call.respond404()
            return
        }

        val index = deviceArray.indexOf(device.serverId.toString())
        val activation = activationArray[index]
        val duration = (results["duration"] as? Int)
        val validUntilDate = validUntilSqlDate.toLocalDateTime()
        if ((activation != null && duration != null && activation.toLocalDateTime().plusDays(duration.toLong()).isBefore(LocalDateTime.now()))
                || (activation == null && duration == null && validUntilDate.isBefore(LocalDateTime.now()))) {
            call.respond(HttpStatusCode.Gone)
            return
        }

        call.respondOK()
        return
    }

    // Register device with coupon
    val couponAmountLeft = results["validCountLeft"] as? Int ?: 0
    val serverCouponId = (results["id"] as? Int)?.toLong() ?: -1L
    if (couponAmountLeft <= 0 || serverCouponId < 0L) {
        call.respond(HttpStatusCode.Gone)
        return
    }

    val validUntilSqlDate = (results["validUntil"] as? java.sql.Timestamp)
    if (validUntilSqlDate == null) {
        call.respond404()
        return
    }

    val validUntilDate = validUntilSqlDate.toLocalDateTime()
    if (validUntilDate.isBefore(LocalDateTime.now())) {
        call.respond(HttpStatusCode.Gone)
        return
    }

    // Coupon is valid
    val success = databaseController.couponSet(serverCouponId, device.serverId)

    if (success) call.respondOK()
    else call.respond(HttpStatusCode.BadRequest)
}
