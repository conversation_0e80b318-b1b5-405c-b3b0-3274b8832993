package net.kidjo.server.api2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.database.device_setEmail
import net.kidjo.server.shared.extensions.*

suspend fun Api2Controller.device_setEmail(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }
    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return
    }
    val args = call.receiveParameters()
    val email = args.getString("email")
    val optIn = args.getString("optIn","true") == "true"

    if (!validator.isEmailValid(email)) {
        call.respondBadParameters()
        return
    }

    val results = databaseController.device_setEmail(device.serverId,email,optIn)

    if (results) call.respond(HttpStatusCode.OK)
    else call.respondDatabaseIssue()
}
