package net.kidjo.server.core

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.receive
import net.kidjo.common.models.Language
import net.kidjo.server.api5.extension.subscription.kidjoDomain
import net.kidjo.server.models.PartnerResponse
import net.kidjo.server.models.PartnerSubscriptionRequest
import net.kidjo.server.models.PartnerUser
import net.kidjo.server.models.SubscriptionBean
import net.kidjo.server.models.UserDetails
import net.kidjo.server.models.enums.Event
import net.kidjo.server.models.enums.PartnerSubscription
import net.kidjo.server.service.GENERATED_ACCOUNT_CHARACTERS_TO_USE
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.Countries
import net.kidjo.server.shared.models.entity.PartnerSubscriptionLogs
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.generatePassword
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.utils.*
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


private const val LENGTH_OF_GENERATED_DV_ACCOUNT_NAME = 6

abstract class PartnerBaseService(
    val config: Config,
    val utility: Utility,
    val encryptionController: EncryptionController,
    private val emailManager: EmailManager,
    val databaseController: DatabaseController,
    val languageCache: LanguageCache,
) {
    val logger: Logger = LoggerFactory.getLogger("PartnerBaseService - Partner Subscription")

    /**
     *  Partner based request processing Implementation
     */
    abstract suspend fun partnerProcess(
        call: ApplicationCall,
    )

    open fun handlePartnerRequest(request: String): PartnerSubscriptionRequest {
        TODO("Implement this method for each partner")
    }

    /**
     *  Partner Subscription processing
     */
    suspend fun process(call: ApplicationCall, partner: PartnerSubscription): PartnerResponse {
        val incomingRequest = call.receive<String>()
        logger.debug("Incoming Request for  partner ${partner.name} - ${incomingRequest}")
        insertLogs(incomingRequest, partner.name)
        logger.debug("Saved Incoming Request for  partner ${partner.name} - PartnerSubscription Logs")

        logger.debug("Started Incoming Request processing for  partner ${partner.name}")
        val subscriptionRequest = handlePartnerRequest(incomingRequest)
        logger.debug("Completed Incoming Request processing for  partner ${partner.name}")

        var user = PartnerUser(User.getEmptyUser().getLongId(), null, null)
        val subscriptionId: Long =
            when (subscriptionRequest.event) {
                Event.SUBSCRIPTION -> {
                    user = createKidjoUser(subscriptionRequest, partner)
                    createKidjoSubscription(subscriptionRequest, user)
                }

                Event.CANCELLATION -> {
                    cancelKIDJOSubscription(subscriptionRequest)
                }

                Event.TERMINATION -> {
                    terminateKidjoSubscription(subscriptionRequest)
                }

                Event.RENEWAL -> {
                    renewKidjoSubscription(subscriptionRequest)
                }

                Event.SUSPENSION -> {
                    suspendKidjoSubscription(subscriptionRequest)
                }

                Event.OTHER -> {
                    return PartnerResponse(HttpStatusCode.OK.value.toString(), "Success", null, null)
                }
            }

        logger.debug("Updating Incoming Request processing for  partner ${partner.name} to subscriptionId -${subscriptionId}")
        updateLogs(subscriptionId)
        logger.debug("Updated Incoming Request processing for  partner ${partner.name} to subscriptionId -${subscriptionId}")

        return PartnerResponse(
            HttpStatusCode.OK.value.toString(),
            "Successfully ${subscriptionRequest.event.message}",
            user.userName, user.password
        )
    }

    private fun suspendKidjoSubscription(subscriptionRequest: PartnerSubscriptionRequest): Long {
        TODO("Not yet implemented")
    }

    private fun renewKidjoSubscription(subscriptionRequest: PartnerSubscriptionRequest): Long {
        TODO("Not yet implemented")
    }

    private fun terminateKidjoSubscription(subscriptionRequest: PartnerSubscriptionRequest): Long {
        TODO("Not yet implemented")
    }

    private fun createKidjoUser(
        subscriptionRequest: PartnerSubscriptionRequest,
        partner: PartnerSubscription,
    ): PartnerUser {
        TODO("Not yet implemented")
    }

    private fun createKidjoSubscription(subscriptionRequest: PartnerSubscriptionRequest, user: PartnerUser): Long {
        TODO("Not yet implemented")
    }

    private fun cancelKIDJOSubscription(nextBillDate: PartnerSubscriptionRequest): Long {
        TODO("Not yet implemented")
    }


    private fun updateLogs(subscriptionId: Long) {

    }

    /**
     * Create Subscriptions for partner
     */

    fun createSubscription(
        subscriptionBean: SubscriptionBean,
        config: Config,
        utility: Utility,
        encryptionController: EncryptionController,
        language: Language,
        partnerName: String,
    ): Boolean {
        checkMandatoryFields(subscriptionBean)
        val email = subscriptionBean.userDetails!!.find { it.name == "email" }?.value.toString()
        val subPartner = subscriptionBean.userDetails!!.find { it.name == "billingCarrier" }?.value.toString()

        val token = subscriptionBean.userUuid + "|" + subscriptionBean.productId
        val checkSubscription: Pair<Long, LocalDateTime>?
        var newSubId = 0L

        var triggerEmail = false
        if (email.isNotEmpty()) {
            triggerEmail = true
        }
        val user = createUser(
            subscriptionBean.userDetails!!,
            config,
            utility,
            encryptionController
        )
        if (user != null) {

            checkSubscription = validateUserSubscription(subscriptionBean.startDate, user, config)

            if (checkSubscription == null) {
                newSubId = transaction {

                    net.kidjo.server.shared.models.entity.SubscriptionRoot.insert {
                        it[userId] = user.id.toInt()
                        it[stillInFreeTrial] = false
                        it[paymentType] =
                            SubscriptionRoot.PaymentType.fromRaw(partnerName.lowercase())
                        it[storeId] =
                            Device.StorePlatform.fromRow(partnerName.lowercase())
                        it[isRenewing] = true
                        it[subscriptionType] =
                            SubscriptionRoot.SubscriptionType.fromRaw(config.subscriptionPartnerProduct)
                        it[paymentId] = partnerName.lowercase()
                        it[platformPurchaseId] =
                            Device.StorePlatform.fromRow(partnerName.lowercase())
                                .toString()
                        it[iapId] = config.subscriptionPartnerProduct
                        it[purchasingSessionId] = "NOT_SET"
                        it[subscriptionToken] = token
                        it[subscriptionTokenHash] = encryptionController.sha256Hash(token)
                        it[nextBillDate] = isNextBillingDate(
                            convertMiliSecondDateV2(subscriptionBean.endDate),
                            convertMiliSecondDate(subscriptionBean.startDate)
                        )
                        it[isActive] = true
                        it[createdAt] =
                            convertMiliSecondDate(subscriptionBean.startDate)//isFutureOrTodayDate(convertMiliSecondDate(subscriptionBean.startDate))
                        it[updatedAt] = convertMiliSecondDate(subscriptionBean.startDate)
                        it[lastCheckDate] = convertMiliSecondDate(subscriptionBean.startDate)
                    }[net.kidjo.server.shared.models.entity.SubscriptionRoot.id]

                }
                if (newSubId != 0L) {
                    if (user != null && triggerEmail) {
                        emailManager.sendCredentialsEmail(email, user, config.userPassword, language)
                    }

                    if (partnerName.uppercase() == PartnerSubscription.MONDIA.toString()) {
                        insertPartnerSubscriptionMondia(subscriptionBean, subPartner, newSubId)
                    } else {
                        insertPartnerSubscription(subscriptionBean, subPartner)
                    }

                } else {
                    throw BadRequestException("Failed to create Subscription")
                }
            } else if (LocalDateTime.now().plusDays(1) > checkSubscription.second) {
                transaction {
                    net.kidjo.server.shared.models.entity.SubscriptionRoot.update({
                        net.kidjo.server.shared.models.entity.SubscriptionRoot.id.eq(checkSubscription.first)
                    }) {
                        it[nextBillDate] = isNextBillingDate(
                            convertMiliSecondDateV2(subscriptionBean.endDate),
                            convertMiliSecondDate(subscriptionBean.startDate)
                        )
                        it[isActive] = true
                        it[updatedAt] = convertMiliSecondDate(subscriptionBean.startDate)
                    }
                }

            } else {
                throw BadRequestException("User subscription exists for ${config.subscriptionPartnerProduct}")
            }

        }
        return newSubId != 0L
    }


    fun updateLogs(token: String) {

        logger.info("[PartnerBaseService.updateLogs] Updating logs for token $token ...")

        var subscriptionId = 0
        var logId = 0
        transaction {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.select {
                net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionTokenHash.eq(
                    encryptionController.sha256Hash(token)
                )
            }
                .mapNotNull { row ->
                    subscriptionId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt()
                }
            PartnerSubscriptionLogs.selectAll().orderBy(PartnerSubscriptionLogs.requestId, SortOrder.DESC).limit(1)
                .mapNotNull { row -> logId = row[PartnerSubscriptionLogs.requestId] }
            if (logId != 0) {
                PartnerSubscriptionLogs.update({ PartnerSubscriptionLogs.requestId eq logId }) {
                    it[PartnerSubscriptionLogs.subscriptionId] = subscriptionId
                }
            }
        }
    }


    private fun createUser(
        user: List<UserDetails>,
        config: Config,
        utility: Utility,
        encryptionController: EncryptionController,
    ): User? {
        var email = user.find { it.name == "email" }?.value ?: ""
        val msIdn = user.find { it.name == "msisdn" }?.value ?: ""
        val country = user.find { it.name == "billingCountry" }?.value ?: ""
        val firstName = user.find { it.name == "FirstName" }?.value ?: ""
        val lastName = user.find { it.name == "lastName" }?.value ?: ""
        val name = firstName + " " + lastName

        if (email.isEmpty() && msIdn.isEmpty()) {
            throw BadRequestException("Email or msisdn is missing")
        }
        val returnResult: Boolean
        /*var triggerEmail = false
        if (email.isNotEmpty()) {
            triggerEmail = true
        }*/

        if (msIdn.isNotEmpty() && email.isEmpty()) {

            if (validateMobileNumber(msIdn)) {
                email = msIdn.replace("+", "") + kidjoDomain
            } else {
                throw BadRequestException("Invalid number format e.g +92310XXXX897")
            }
        }

        if (!emailValidation(email)) {
            throw BadRequestException("Invalid email address")
        }
        if (!checkExistingEmail(email)) {
            returnResult = transaction {
                var countryId = 0;
                Countries.select { Countries.short.eq(country) }.mapNotNull { countryId = it[Countries.id].toInt() }
                val (password, hashedPassword) = generatePassword(
                    randomString = utility.randomString(
                        LENGTH_OF_GENERATED_DV_ACCOUNT_NAME, GENERATED_ACCOUNT_CHARACTERS_TO_USE
                    ), encryptionController = encryptionController, salt = config.clientSideHashV1Salt
                )
                config.userPassword = password
                Users.insert {
                    it[Users.email] = email
                    it[Users.password] = hashedPassword
                    it[Users.countryId] = countryId
                    it[Users.name] = name
                    //if (triggerEmail) {
                    //   it[authType] = User.AuthType.EMAIL
                    // } else {
                    it[authType] = User.AuthType.FAKE_EMAIL
                    //  }
                }.insertedCount > 0
            }
        } else {
            returnResult = updateUser(user, config, utility, encryptionController)
        }

        if (returnResult) {
            return getUserByUserId(getUserIdByUserEmail(email).toInt())
        }

        throw NullPointerException("NullPointerException: Missing User")
    }


    private fun updateUser(
        user: List<UserDetails>,
        config: Config,
        utility: Utility,
        encryptionController: EncryptionController,
    ): Boolean {
        val email = user.find { it.name == "email" }?.value.toString()
        return transaction {
            try {
                val (password, hashedPassword) = generatePassword(
                    randomString = utility.randomString(
                        net.kidjo.server.service.LENGTH_OF_GENERATED_DV_ACCOUNT_NAME,
                        GENERATED_ACCOUNT_CHARACTERS_TO_USE
                    ), encryptionController = encryptionController, salt = config.clientSideHashV1Salt
                )
                config.userPassword = password
                Users.update({ Users.email eq email }) {
                    it[Users.password] = hashedPassword
                }
                true
            } catch (e: Exception) {
                logger.info("Unable to update user details")
                false

            }
        }
    }

    private fun validateUserSubscription(startDate: String, user: User, config: Config): Pair<Long, LocalDateTime>? {

        var validSubs: Pair<Long, LocalDateTime>? = null
        transaction {

            validSubs = net.kidjo.server.shared.models.entity.SubscriptionRoot.select {
                (net.kidjo.server.shared.models.entity.SubscriptionRoot.userId eq user.id.toInt()) and
                        (net.kidjo.server.shared.models.entity.SubscriptionRoot.iapId eq config.subscriptionPartnerProduct) and
                        (net.kidjo.server.shared.models.entity.SubscriptionRoot.nextBillDate greater convertMiliSecondDate(
                            startDate
                        )) and (net.kidjo.server.shared.models.entity.SubscriptionRoot.isActive eq true)
            }.firstNotNullOfOrNull {
                Pair(
                    it[net.kidjo.server.shared.models.entity.SubscriptionRoot.id],
                    it[net.kidjo.server.shared.models.entity.SubscriptionRoot.nextBillDate]
                )
            }

        }
        return validSubs

    }

    private fun checkExistingEmail(email: String): Boolean {
        return transaction {
            Users.select { Users.email eq email }.count() > 0
        }
    }

    fun updateSubscription(
        subscriptionBean: SubscriptionBean,
        config: Config,
        utility: Utility,
        encryptionController: EncryptionController,
        language: Language,
    ): Boolean {

        val email = subscriptionBean.userDetails!!.find { it.name == "email" }?.value.toString()
        val token = subscriptionBean.userUuid + "|" + subscriptionBean.productId
        val checkSubscription: Pair<Long, LocalDateTime>?
        var isSubscriped = false
        var triggerEmail = false
        if (email.isNotEmpty()) {
            triggerEmail = true
        }
        val user = createUser(
            subscriptionBean.userDetails!!,
            config,
            utility,
            encryptionController
        )
        if (user != null) {

            checkSubscription = validateUserSubscription(subscriptionBean.startDate, user, config)

            if (checkSubscription != null) {
                try {
                    transaction {

                        net.kidjo.server.shared.models.entity.SubscriptionRoot.update({ net.kidjo.server.shared.models.entity.SubscriptionRoot.userId eq user.id.toInt() }) {
                            it[stillInFreeTrial] = false
                            it[subscriptionType] =
                                SubscriptionRoot.SubscriptionType.fromRaw(config.subscriptionPartnerProduct)
                            it[iapId] = config.subscriptionPartnerProduct
                            it[subscriptionToken] = token
                            it[subscriptionTokenHash] = encryptionController.sha256Hash(token)
                            it[nextBillDate] = convertMiliSecondDateV2(subscriptionBean.endDate)
                            it[updatedAt] = convertMiliSecondDate(subscriptionBean.startDate)
                            it[lastCheckDate] = convertMiliSecondDate(subscriptionBean.startDate)
                            if (subscriptionBean.event.lowercase() == "activate") {
                                it[isActive] = true
                                it[isRenewing] = true
                            } else if (subscriptionBean.event.lowercase() == "deactivate") {
                                it[isActive] = true
                                it[isRenewing] = false
                            } else if (subscriptionBean.event.lowercase() == "cancel") {
                                it[isActive] = false//true
                                it[isRenewing] = false
                            }
                        }
                    }
                    isSubscriped = true

                } catch (e: Exception) {
                    logger.info("Unable to update user subscription")
                }

                if (isSubscriped) {
                    if (subscriptionBean.event.lowercase() == "activate") {

                        if (user != null && triggerEmail) {
                            emailManager.sendCredentialsEmail(email, user, config.userPassword, language)
                        }
                    }

                } else {
                    throw BadRequestException("Failed to ${subscriptionBean.event} Subscription")
                }
            } else {
                throw BadRequestException("No such subscription exists for ${email}")
            }
        }

        return isSubscriped
    }

    private fun insertPartnerSubscription(subscriptionBean: SubscriptionBean, subPartner: String) {

        logger.info(
            "[PartnerBaseService.insertPartnerSubscription] Partner $subPartner" +
                    "Inserting subscription: $subscriptionBean"
        )
        var subscriptionId = 0
        val msisdn = subscriptionBean.userDetails!!.find { it.name == "msisdn" }?.value.toString()
        var userId = 0
        transaction {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.selectAll()
                .orderBy(net.kidjo.server.shared.models.entity.SubscriptionRoot.id, SortOrder.DESC).limit(1)
                .mapNotNull { row ->
                    subscriptionId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt()
                    userId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.userId]
                }
            UserPartnerSubscription.insert {
                it[msidn] = msisdn
                it[UserPartnerSubscription.subscriptionId] = subscriptionId
                it[UserPartnerSubscription.userId] = userId
                it[UserPartnerSubscription.subPartner] = subPartner
                it[userUuid] = subscriptionBean.userUuid
            }.insertedCount
        }
    }

    /**
     * The V2 method is utilized to store the additional parameters for the Mondia partner.
     * TODO optimize the method (duplicate code + move it to another class)
     */
    private fun insertPartnerSubscriptionMondia(subscriptionBean: SubscriptionBean, subPartner: String, newSubId:Long) {

        logger.info(
            "[PartnerBaseService.insertPartnerSubscriptionV2] Partner $subPartner" +
                    "Inserting subscription: $subscriptionBean"
        )
        var subscriptionId = 0
        val msisdn = subscriptionBean.userDetails!!.find { it.name == "msisdn" }?.value.toString()

        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        val lastModifiedDateString = LocalDateTime.parse(subscriptionBean.lastModifiedDate, formatter)

        var userId = 0
        transaction {
            net.kidjo.server.shared.models.entity.SubscriptionRoot.select({net.kidjo.server.shared.models.entity.SubscriptionRoot.id.eq(newSubId)}).limit(1)
                .mapNotNull { row ->
                    subscriptionId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt()
                    userId = row[net.kidjo.server.shared.models.entity.SubscriptionRoot.userId]
                }
            if (checkDuplicateUserUuid(subscriptionBean.userUuid)) {
                UserPartnerSubscription.update({ UserPartnerSubscription.userUuid.eq(subscriptionBean.userUuid) }) {
                    it[UserPartnerSubscription.subscriptionId] = subscriptionId
                    it[UserPartnerSubscription.userId] = userId
                    it[msidn] = msisdn
                    it[UserPartnerSubscription.subscriptionId] = subscriptionId
                    it[UserPartnerSubscription.userId] = userId
                    it[UserPartnerSubscription.subPartner] = subPartner
                }
            } else {
                UserPartnerSubscription.insert {
                    it[msidn] = msisdn
                    it[UserPartnerSubscription.subscriptionId] = subscriptionId
                    it[UserPartnerSubscription.userId] = userId
                    it[UserPartnerSubscription.subPartner] = subPartner
                    it[userUuid] = subscriptionBean.userUuid
                    it[partnerSubscriptionId] = subscriptionBean.subscriptionId
                    it[partnerUserId] = subscriptionBean.userId
                    it[partnerExternalIdentifier] = subscriptionBean.externalIdentifier ?: ""
                    it[partnerLastModifiedDate] = lastModifiedDateString
                }.insertedCount
            }
        }
    }

    private fun checkDuplicateUserUuid(userUuid: String): Boolean {
        return transaction {
            UserPartnerSubscription.select {
                UserPartnerSubscription.userUuid eq userUuid
            }.count() > 0
        }
    }


    private fun checkMandatoryFields(subscriptionBean: SubscriptionBean) {

        if (subscriptionBean.productId == 0) {
            throw BadRequestException("Product ID is missing")
        } else if (subscriptionBean.userUuid.isEmpty()) {
            throw BadRequestException("userUuid is missing")
        } else if (subscriptionBean.event.isEmpty()) {
            throw BadRequestException("event is missing")
        } else if (subscriptionBean.startDate.isEmpty()) {
            throw BadRequestException("startDate is missing")
        } else if (subscriptionBean.eventId.isEmpty()) {
            throw BadRequestException("eventId is missing")
        }
    }


    fun getSubscriptionType(product: String): SubscriptionRoot.SubscriptionType {
        val type = StringBuilder("kidjo_")
        product.split(",").forEach {
            when (it.lowercase()) {
                "tv" -> type.append("tv")
                "stories" -> type.append("books")
                "games" -> type.append("games")
            }
            type.append("_")
        }
        return SubscriptionRoot.SubscriptionType.fromRaw(type.substring(0, type.lastIndexOf("_")))
    }
}
