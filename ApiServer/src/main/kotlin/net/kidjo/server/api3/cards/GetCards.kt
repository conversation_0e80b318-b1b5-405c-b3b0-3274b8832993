package net.kidjo.server.api3.cards

import io.ktor.server.application.ApplicationCall
import io.ktor.http.ContentType
import io.ktor.server.response.respondText
import net.kidjo.common.models.Folder
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.shared.cachedatabase.recentVideos_get
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.json.v3.*
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.Video
import org.json.JSONArray
import org.json.JSONObject

private const val CARDS_PER_SECTION = 12
private const val MAX_CARDS_PER_CALL = 100
private const val DEFAULT_CARDS_PER_CALL = 24

suspend fun Api3CardsController.cards_getList(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)

    val json = JSONObject()

    val query = call.parameters

    val kidId = query.getDecodedNormalId("kidId", encryptionController)

    val folderType = Folder.ContentType.FromRaw(query.getString("contentType"))
    val cardOffset = query.getInt("offset", 0)
    val cardLimit = kotlin.math.min(query.getInt("limit", DEFAULT_CARDS_PER_CALL), MAX_CARDS_PER_CALL)

    // 0 = all
    // 1 = only folders, with games
    // 2 = all without games
    // 3 = only folders, without games
    val filter = query.getInt("filterMask", 0b10)
    val flag_onlyFolders = filter.getBit(0)
    val flag_filterOutGames = filter.getBit(1)
    val isPremium = filter.getBit(2)

    val languageId: Int
    val countryId: Int
    val kidAge: Int
    val filterFolders: String
    val recentVideos: ArrayList<Video>?

    if (kidId != Kid.NO_ID_SERVER) {
        if (cardOffset == 0) recentVideos = cacheDatabase.recentVideos_get(kidId)
        else recentVideos = null

        val info =
            databaseController.generic_singleRowQuery("SELECT avatars.id, avatars.age,languageId,devices.countryId,GROUP_CONCAT(avatars_folders.folderId) as filteredFolders " +
                    "FROM devices JOIN avatars on devices.id = avatars.deviceId " +
                    "LEFT JOIN avatars_folders ON avatars_folders.avatarId = avatars.id " +
                    "WHERE avatars.id = ? " +
                    "AND avatars.age IS NOT null " +
                    "GROUP BY avatars.id", { statement ->
                statement.setLong(1, kidId)
            })
        languageId = info?.get("languageId") as? Int ?: config.defaultLanguageId
        countryId = info?.get("countryId") as? Int ?: config.defaultCountryId
        kidAge = info?.get("age") as? Int ?: Kid.DEFAULT_AGE
        filterFolders = info?.get("filteredFolders") as? String ?: ""
    } else {
        recentVideos = null
        databaseController.device_checkAndGetDeviceInfo(device)

        kidAge = Kid.DEFAULT_AGE
        filterFolders = ""
        if (device.infoFromServer) {
            languageId = device.languageId
            countryId = device.countryId
        } else {
            languageId = config.defaultLanguageId
            countryId = config.defaultCountryId
        }
    }

    val cardJSON = JSONArray()

    val soundPerSection = if (kidAge < 4) 2 else 1
    val benefitsPerSection = if (isPremium) 1 else 2

    //sound card at position 1 and at 8 if there are two
    //benefits card at position 9 and 4 if there are two
    var requestCardsAmount = cardLimit
    var requestCardOffset = cardOffset
    if (flag_onlyFolders == false) {
        val numberOfSoundCardsSoFar = NumberOfSoundCardsUpTo(cardOffset, soundPerSection)
        val numberOfBenefitsCardsSoFar = NumberOfBenefitCardsUpTo(cardOffset, benefitsPerSection)
        val extraCardsSoFar = numberOfSoundCardsSoFar + numberOfBenefitsCardsSoFar
        requestCardOffset -= extraCardsSoFar

        val numberOfSoundCardsAdded =
            NumberOfSoundCardsUpTo(cardOffset + cardLimit, soundPerSection) - numberOfSoundCardsSoFar
        val numberOfBenefitsCardsAdded =
            NumberOfBenefitCardsUpTo(cardOffset + cardLimit, benefitsPerSection) - numberOfBenefitsCardsSoFar
        val otherCardsToBeAdded = numberOfSoundCardsAdded + numberOfBenefitsCardsAdded
        requestCardsAmount -= otherCardsToBeAdded
    }

    val folders = databaseController.folders_getList(
        requestCardsAmount,
        requestCardOffset,
        languageId,
        countryId,
        kidAge,
        folderType,
        filterFolders,
        flag_filterOutGames
    )

    if (flag_onlyFolders) {
        var index = 0
        if (cardOffset == 0 && recentVideos != null && recentVideos.size > 0) {
            index++
            cardJSON.put(jsonCreator.cards_folderHistoryToJSON(recentVideos))
        }
        for (folder in folders) {
            index++
            if (index > MAX_CARDS_PER_CALL) break
            cardJSON.put(CreateV3FolderCardJson(folder, jsonCreator, databaseController))
        }
    } else {
        var folderIndex = 0
        for (i in 0 until cardLimit) {
            val sectionPosition = (i + cardOffset) % CARDS_PER_SECTION
            val section = (i + cardOffset) / CARDS_PER_SECTION

            if (cardOffset == 0 && sectionPosition == 0 && recentVideos != null && recentVideos.size > 0) {
                cardJSON.put(jsonCreator.cards_folderHistoryToJSON(recentVideos))
            } else if (sectionPosition == 1)
                cardJSON.put(jsonCreator.cards_soundToJSON(soundPerSection * section))
            else if (sectionPosition == 8 && soundPerSection > 1)
                cardJSON.put(jsonCreator.cards_soundToJSON(soundPerSection * section + 1))
            else if (sectionPosition == 9)
                cardJSON.put(jsonCreator.cards_benefitsToJSON(benefitsPerSection * section + 1, isPremium, languageId))
            else if (sectionPosition == 4 && benefitsPerSection > 1)
                cardJSON.put(jsonCreator.cards_benefitsToJSON(benefitsPerSection * section, isPremium, languageId))
            else if (folderIndex < folders.size) {
                val folder = folders[folderIndex]
                cardJSON.put(CreateV3FolderCardJson(folder, jsonCreator, databaseController))
                folderIndex++
            } else
                break
        }
    }
    json.put("cards", cardJSON)
    return call.respondText(json.toString(), ContentType.Application.Json)
}

fun CreateV3FolderCardJson(
    folder: Folder,
    jsonCreator: JsonObjectCreatorV3,
    databaseController: DatabaseController
): JSONObject {
    if (folder.mediaType == Folder.MediaType.VIDEO) {
        val videos = databaseController.videos_getListFromFolder(folder.getLongId())
        return jsonCreator.cards_folderNormalToJSON(folder, videos)
    } else {
        val games = databaseController.game_getByFolder(folder.getLongId())
        return jsonCreator.new_cards_gameFolderToJSON(folder, games)
    }
}

fun NumberOfSoundCardsUpTo(position: Int, numberOfSoundCardsPerSection: Int): Int {
    val sectionIndex = position / CARDS_PER_SECTION
    val relativePosition = position % CARDS_PER_SECTION

    var numberOfSoundCardsSoFar = numberOfSoundCardsPerSection * sectionIndex
    if (relativePosition > 1) numberOfSoundCardsSoFar++
    if (relativePosition > 8 && numberOfSoundCardsPerSection > 1) numberOfSoundCardsSoFar++

    return numberOfSoundCardsSoFar
}

fun NumberOfBenefitCardsUpTo(position: Int, numberOfBenefitsCardsPerSection: Int): Int {
    val sectionIndex = position / CARDS_PER_SECTION
    val relativePosition = position % CARDS_PER_SECTION

    var numberOfSoundCardsSoFar = numberOfBenefitsCardsPerSection * sectionIndex
    if (relativePosition > 9) numberOfSoundCardsSoFar++
    if (relativePosition > 4 && numberOfBenefitsCardsPerSection > 1) numberOfSoundCardsSoFar++

    return numberOfSoundCardsSoFar
}
