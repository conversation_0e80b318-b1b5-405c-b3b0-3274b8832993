package net.kidjo.server.api3.user.helpers

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.user_register
import net.kidjo.server.shared.extensions.respondDatabaseIssue
import net.kidjo.server.shared.models.User


suspend fun Api3Controller.userHelper_createUser(user: User, call: ApplicationCall): <PERSON><PERSON>an {
    val id = databaseController.user_register(user).toString()
    if (id == User.NO_ID) {
        call.respondDatabaseIssue()
        return true
    }

    user.id = id
    paymentManager.createCustomer(user)
    if (validator.isEmailValid(user.email)) {
        //emailManager.sendConfirmationEmail(user.email, "aaaaaaa", languageCache.get(call))
    }

    return false
}
