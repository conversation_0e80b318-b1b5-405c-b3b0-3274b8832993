package net.kidjo.server.api3.device

import io.ktor.server.application.ApplicationCall
import net.kidjo.common.models.Language
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.user_getCountryIdFromShortName
import net.kidjo.server.shared.extensions.receiveJSON
import net.kidjo.server.shared.extensions.respondBadJSON
import net.kidjo.server.shared.extensions.respondBadParameters
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.User
import java.util.*

private val timezoneRegex = Regex("^UTC([+-])([01]?[0-9]):([0-9]{2})H")

suspend fun Api3Controller.device_refreshDeviceGetRegisterInfo(user: User, device: Device, kids: ArrayList<Kid>, call: ApplicationCall): Boolean {
    val json = call.receiveJSON()
    if (json == null) {
        call.respondBadJSON()
        return true
    }

    if (!json.has("deviceManufacturer") || !json.has("deviceModel") || !json.has("language") ||
            !json.has("country") || !json.has("localTimeZone")) {
        call.respondBadParameters()
        return true
    }

    val deviceManufacturer = json.optString("deviceManufacturer").toLowerCase()
    val deviceModel = json.optString("deviceModel").toLowerCase()
    val language = json.optString("language")
    val country = json.optString("country")
    val locale = Locale(language,country)

    val localTimeZone = json.optString("localTimeZone")

    //timezone area
    val databaseTimeZone: Int
    try {
        val timezoneSearch = timezoneRegex.findAll(localTimeZone).first().groupValues
        val zoneStringElement1 = if (timezoneSearch[1] == "+") 1 else -1
        val zoneStringElement2 = timezoneSearch[2].toInt() * 60
        val zoneStringElement3 = timezoneSearch[3].toInt()

        databaseTimeZone = zoneStringElement1 * (zoneStringElement2 + zoneStringElement3)
    } catch (e: Exception) {
        println("Warning | Api3Controller.device_refreshDeviceGetRegisterInfo() | Issue parsing timezone")
        println("-- timezoneString: $localTimeZone")
        println(e)
        call.respondBadRequest("issue with timezone")
        return true
    }

    var countryId: Int = databaseController.user_getCountryIdFromShortName(locale.country)
    if (countryId <= 0) countryId = 237 // US

    var usingLanguageId: Int = languageCache.getLanguageIdWithShortName(locale.language)
    if (usingLanguageId == Language.NO_ID) usingLanguageId = Language.ID_ENGLISH

    return device_refreshRegister(user,device,kids,deviceManufacturer,deviceModel,locale,countryId,databaseTimeZone,usingLanguageId,call)
}
