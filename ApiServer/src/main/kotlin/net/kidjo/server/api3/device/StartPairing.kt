package net.kidjo.server.api3.device

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.cachedatabase.setDeivceUserPairingToken
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.extensions.getDeviceFromHeader
import net.kidjo.server.shared.extensions.respondBadDevice
import net.kidjo.server.shared.extensions.respondJSON
import net.kidjo.server.shared.models.DeviceUserPairing
import org.json.JSONObject

private val PAIRING_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray()

suspend fun Api3Controller.device_startPairing(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }

    databaseController.device_checkAndGetDeviceInfo(device)

    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return
    }


    val randomString = utility.randomString(5, PAIRING_CHARS)

    val pairing = DeviceUserPairing(randomString, device.serverId, DeviceUserPairing.OPEN_REQUEST, DeviceUserPairing.PairingStep.SET_UP)
    cacheDatabase.setDeivceUserPairingToken(randomString,pairing)

    val json = JSONObject()
    json.put("pairing", pairing.toJSON(encryptionController))
    json.put("link", config.getPairingUrl(device.languageId))
    call.respondJSON(json)
}
