package net.kidjo.server.api3

import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receiveParameters
import io.ktor.server.routing.Route
import io.ktor.server.routing.post
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.cachedatabase.linkPinDelete
import net.kidjo.server.shared.cachedatabase.linkPinGetUserId
import net.kidjo.server.shared.database.deviceSetUserId
import net.kidjo.server.shared.database.deviceUnlinkUserId
import net.kidjo.server.shared.extensions.getDeviceFromHeader
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK
import org.slf4j.LoggerFactory

class Api3LinkController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    private val logger = LoggerFactory.getLogger("Api3LinkController")

    override fun installRoutes(route: Route) {
        route.post("/pin/validate") { <EMAIL>(call) }
        route.post("/device/unlink") { <EMAIL>(call) }
    }

    private suspend fun validatePin(call: ApplicationCall) {
        val device = call.getDeviceFromHeader(encryptionController)
        val parameters = call.receiveParameters()
        parameters["pin"]?.let { pin ->
            val userId = cacheDatabase.linkPinGetUserId(pin)?.takeIf { it.isNotBlank() }?.toLongOrNull()
            userId?.let {
                cacheDatabase.linkPinDelete(pin)
                databaseController.deviceSetUserId(device, it)
                logger.debug("validatePin() | pin ($pin) is correct for userId: $it")
                return@validatePin call.respondOK()
            }
        }

        logger.debug("validatePin() | pin is incorrect for device: ${device.userId}")
        call.respondError(HttpStatusCode.Forbidden, "Bad link pin")
    }

    private suspend fun unlinkDevice(call: ApplicationCall) {
        val device = call.getDeviceFromHeader(encryptionController)
        return if (databaseController.deviceUnlinkUserId(device)) {
            logger.debug("unlinkDevice() | device (${device.serverId}) is now unlinked from a user")
            call.respondOK()
        } else {
            logger.debug("unlinkDevice() | cannot unlink device (${device.serverId}) from its user")
            call.respondError(HttpStatusCode.InternalServerError, "Cannot unlink device")
        }
    }

}
