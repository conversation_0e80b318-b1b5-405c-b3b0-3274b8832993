package net.kidjo.server.api3.kid

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.favorites_add
import net.kidjo.server.shared.database.favorites_remove
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Kid


suspend fun Api3Controller.kid_favorites_add(call: ApplicationCall) {
    val query = call.parameters
    val kidId = query.getDecodedNormalId("kidId", encryptionController)

    if (kidId == Kid.NO_ID_SERVER) {
        call.respond404("no kid")
        return
    }

    val json = call.receiveJSON()
    if (json == null) {
        call.respondBadJSON()
        return
    }

    val videoId = json.optString("videoId")
    if (videoId == "") {
        call.respondBadParameters()
        return
    }

    val decodedVideoId = encryptionController.decodeVideoId(videoId)
    val success = databaseController.favorites_add(kidId, decodedVideoId)

    if (success) call.respondOK()
    else call.respondDatabaseIssue()
}
suspend fun Api3Controller.kid_favorites_remove(call: ApplicationCall) {
    val query = call.parameters
    val kidId = query.getDecodedNormalId("kidId", encryptionController)

    if (kidId == Kid.NO_ID_SERVER) {
        call.respond404("no kid")
        return
    }

    val json = call.receiveJSON()
    if (json == null) {
        call.respondBadJSON()
        return
    }

    val videoId = json.optString("videoId")
    if (videoId == "") {
        call.respondBadParameters()
        return
    }

    val decodedVideoId = encryptionController.decodeVideoId(videoId)
    val success = databaseController.favorites_remove(kidId, decodedVideoId)

    if (success) call.respondOK()
    else call.respondDatabaseIssue()
}
