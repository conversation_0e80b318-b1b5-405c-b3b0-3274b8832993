package net.kidjo.server.api3

import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.http.HttpStatusCode
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.post
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.getDeviceFromHeader
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.Device
import org.slf4j.LoggerFactory

class Api3CouponController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    data class LogicomCheckResponse(val check: String, val couponId: String)

    private val logger = LoggerFactory.getLogger("Api3CouponController")

    override fun installRoutes(route: Route) {
        route.post("/logicom/check") { <EMAIL>(call) }
    }

    private suspend fun checkLogicom(call: ApplicationCall) {
        val device = call.getDeviceFromHeader(encryptionController)
        if (device.serverId == Device.NO_SERVER_ID) {
            logger.debug("checkLogicom() | device id is not valid")
            return call.respondError(HttpStatusCode.BadRequest, "Device id is not valid")
        }

        val parameters = call.receiveParameters()
        val serialNumber = parameters["serialNumber"]
        val macAddress = parameters["macAddress"]

        // Check last Logicom coupon
        val lastLogicomCoupon = databaseController.logicomGetFirstWithRemaining()
        if (lastLogicomCoupon == null) {
            logger.debug("checkLogicom() | there is no Logicom coupon available")
            return call.respondError(HttpStatusCode.BadRequest, "No logicom coupon available")
        }

        // Check device serial number or mac address and link device identifier
        val logicomSerialBundle = databaseController.logicomCheckDevice(serialNumber, macAddress, device.serverId)
        if (logicomSerialBundle.check == LogicomSerialCheck.UNKNOWN) {
            logger.debug("checkLogicom() | device serial number '$serialNumber' or mac address '$macAddress' is not valid")
            return call.respondError(HttpStatusCode.BadRequest, "Invalid device serial number or mac address")
        } else if (logicomSerialBundle.check == LogicomSerialCheck.ERROR) {
            logger.debug("checkLogicom() | an error happened while checking the device serial number or mac address")
            return call.respondError(HttpStatusCode.BadRequest, "Error while checking device serial number or mac address")
        }

        val oldDeviceId = logicomSerialBundle.oldDeviceId
        if (logicomSerialBundle.check == LogicomSerialCheck.VALID) {
            logger.debug("checkLogicom() | set coupon with id '$lastLogicomCoupon.id' for device id '${device.serverId}'")
            // Enable coupon for device
            databaseController.couponSet(lastLogicomCoupon.id, device.serverId)
        } else if (logicomSerialBundle.check == LogicomSerialCheck.UPDATED && oldDeviceId != null) {
            logger.debug("checkLogicom() | update coupon link with id '$lastLogicomCoupon.id' and old device id '$oldDeviceId' for device id '${device.serverId}'")
            // Enable device holder for coupon
            databaseController.couponUpdateDevice(lastLogicomCoupon.id, device.serverId, oldDeviceId)
        }

        return call.respond(LogicomCheckResponse(logicomSerialBundle.check.toString(), lastLogicomCoupon.value))
    }

}
