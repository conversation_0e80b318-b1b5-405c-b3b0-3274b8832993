package net.kidjo.server.api3.publishers

import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.licensesGetList

class TclController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    data class PromotedFolder(val id: Long,
                              val name: String,
                              val description: String,
                              val category: String = "Videos",
                              val genre: String,
                              val images: List<PromotedFolderImage>,
                              val deeplink: String,
                              val releaseDate: String,
                              val year: String,
                              val language: String,
                              val areas: String = "",
                              val avareas: List<String>,
                              val sourceUpdateTime: String,
                              val isPay: String = "SVOD",
                              val online: String = "Online")

    data class PromotedFolderImage(val url: String,
                                   val format: String,
                                   val width: Int,
                                   val height: Int)

    override fun installRoutes(route: Route) {
        // Miscellaneous routes to return data
        route.get("/promoted") { <EMAIL>(call) }
    }

    /**
     * Retrieve the Kidjo promoted content for TCL to display in the proper section of their services
     */
    private suspend fun getPromotedContent(call: ApplicationCall) {
        val promotedContent = databaseController.licensesGetList(swisscom = true, getCountry = true, getLanguage = true)
        val list = promotedContent.map {
            val images = mutableListOf<PromotedFolderImage>()
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/350_525/${it.folderId}.png", "2:3", 350, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/525_525/${it.folderId}.png", "1:1", 525, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/934_525/${it.folderId}.png", "16:9", 934, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/700_525/${it.folderId}.png", "4:3", 700, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/525_350/${it.folderId}.png", "3:2", 525, 350))
            PromotedFolder(it.id, it.title, it.description ?: "",
                    genre = it.genre?.capitalize() ?: "", images = images,
                    deeplink = "https://www.kidjo.tv/promo/tclpromo?source=tcl&folderId=${it.folderId}", releaseDate = "2019-11-15",
                    year = "2019", sourceUpdateTime = "2019-11-18", language = it.language ?: "",
                    avareas = it.countries)
        }
        call.respond(list)
    }

}
