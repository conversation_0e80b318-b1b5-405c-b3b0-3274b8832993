package net.kidjo.server.api3.subscription

import io.ktor.server.application.ApplicationCall
import net.kidjo.common.models.SubscriptionEvent
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.wv_analyticsEvent
import net.kidjo.server.shared.extensions.*

suspend fun Api3Controller.webViewAnalytics_event(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)

    val json = call.receiveJSON()

    if (!device.isDeviceIdValid) {
        call.respondDatabaseIssue()
        return
    } else if (json == null) {
        call.respondBadJSON()
        return
    } else if (!json.has("sessionId") || !json.has("timeStamp") || !json.has("type")) {
        call.respondBadParameters()
        return
    }

    val event = SubscriptionEvent.Type.FromRawOptional(json.getString("type"))
    if (event == null) {
        call.respondBadParameters()
        return
    }

    val sessionIdHolding = json.optString("sessionId")
    val session = sessionIdHolding.substring(0,kotlin.math.min(32,sessionIdHolding.length))

    val timeStamp = json.optLong("timeStamp")

    val success = databaseController.wv_analyticsEvent(session,event,timeStamp)

    if (success) call.respondOK()
    else call.respondDatabaseIssue()
}
