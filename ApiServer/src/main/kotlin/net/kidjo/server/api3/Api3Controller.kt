package net.kidjo.server.api3

import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import net.kidjo.server.api3.accountcoupon.accountCoupon_removeCouponFromCookie
import net.kidjo.server.api3.accountcoupon.accountCoupon_setToCookies
import net.kidjo.server.api3.device.device_checkPairing
import net.kidjo.server.api3.device.device_refresh
import net.kidjo.server.api3.device.device_refreshWeb
import net.kidjo.server.api3.device.device_startPairing
import net.kidjo.server.api3.kid.kid_favorites_add
import net.kidjo.server.api3.kid.kid_favorites_list
import net.kidjo.server.api3.kid.kid_favorites_remove
import net.kidjo.server.api3.kid.kid_update
import net.kidjo.server.api3.language.language_set
import net.kidjo.server.api3.mondia.mondia_checkPairingStatus
import net.kidjo.server.api3.mondia.mondia_checkPairingStatusMobile
import net.kidjo.server.api3.payments.payments_cancel
import net.kidjo.server.api3.payments.payments_process
import net.kidjo.server.api3.subscription.subscription_set
import net.kidjo.server.api3.subscription.webViewAnalytics_event
import net.kidjo.server.api3.publishers.SwisscomController
import net.kidjo.server.api3.publishers.TclController
import net.kidjo.server.api3.user.user_login
import net.kidjo.server.api3.user.user_logout
import net.kidjo.server.api3.user.user_register
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import org.slf4j.LoggerFactory

class Api3Controller(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)
    internal val logger = LoggerFactory.getLogger("Api3Controller")

    private val api3CardsController = Api3CardsController(platformInjector)
    private val api3LinkController = Api3LinkController(platformInjector)
    private val api3CouponController = Api3CouponController(platformInjector)

    private val swisscomController = SwisscomController(platformInjector)
    private val tclController = TclController(platformInjector)

    override fun installRoutes(route: Route) {
        // Account coupon
        route.post("accountCoupon/set") { this@Api3Controller.accountCoupon_setToCookies(call) }
        route.post("accountCoupon/remove") { this@Api3Controller.accountCoupon_removeCouponFromCookie(call) }

        // Device
        route.post("device/refresh") { this@Api3Controller.device_refresh(call) }
        route.post("device/refreshWeb") { this@Api3Controller.device_refreshWeb(call) }
        route.post("device/pair/start") { this@Api3Controller.device_startPairing(call) }
        route.post("device/pair/status") { this@Api3Controller.device_checkPairing(call) }

        // User
        route.post("user/login") { this@Api3Controller.user_login(call) }
        route.post("user/register") { this@Api3Controller.user_register(call) }
        route.post("user/logout") { this@Api3Controller.user_logout(call) }

        // Kid
        route.post("kid/{kidId}/update") { this@Api3Controller.kid_update(call) }

        // Favorites
        route.get("kid/{kidId}/favorites/getList") { this@Api3Controller.kid_favorites_list(call) }
        route.post("kid/{kidId}/favorites/add") { this@Api3Controller.kid_favorites_add(call) }
        route.post("kid/{kidId}/favorites/remove") { this@Api3Controller.kid_favorites_remove(call) }

        // Languages
        route.post("language/set") { this@Api3Controller.language_set(call) }

        // Cards
        route.route("/cards") { api3CardsController.installRoutes(this) }

        // Subscription
        route.post("subscription/set") { this@Api3Controller.subscription_set(call) }
        route.post("subscription/buy") { this@Api3Controller.payments_process(call) }
        route.post("subscription/cancel") { this@Api3Controller.payments_cancel(call) }
        route.post("a/wv/e") { this@Api3Controller.webViewAnalytics_event(call) }

        // Mondia
        route.post("mondia/check") { this@Api3Controller.mondia_checkPairingStatus(call) }
        route.post("mondia/check/mobile") { this@Api3Controller.mondia_checkPairingStatusMobile(call) }

        // Swisscom
        route.route("/swisscom") { swisscomController.installRoutes(this) }

        // TCL
        route.route("/tcl") { tclController.installRoutes(this) }

        // Link
        route.route("/link") { api3LinkController.installRoutes(this) }

        // Coupon links Not in use anymore
        route.route("/coupon") { api3CouponController.installRoutes(this) }
    }
}
