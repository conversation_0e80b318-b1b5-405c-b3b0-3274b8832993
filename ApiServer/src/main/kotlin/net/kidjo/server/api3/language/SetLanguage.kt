package net.kidjo.server.api3.language

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import net.kidjo.common.models.Language
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.database.language_setActiveForDevice
import net.kidjo.server.shared.extensions.*

suspend fun Api3Controller.language_set(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }
    databaseController.device_checkAndGetDeviceInfo(device)
    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return
    }
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val languageId = requestJSON.optInt("languageId", Language.NO_ID)
    if (languageId == Language.NO_ID) {
        call.respondBadRequest("bad id")
        return
    } else if (languageId == device.languageId) {
        call.respond(HttpStatusCode.OK)
        return
    } else if (!languageCache.languageIsSupported(languageId)) {
        call.respondBadRequest("language inactive or not found")
        return
    }

    val results = databaseController.language_setActiveForDevice(device.serverId, languageId)

    if (results) call.respond(HttpStatusCode.OK)
    else call.respondDatabaseIssue()
}
