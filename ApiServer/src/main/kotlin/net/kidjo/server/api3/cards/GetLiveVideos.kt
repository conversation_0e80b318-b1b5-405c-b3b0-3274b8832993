package net.kidjo.server.api3.cards

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.shared.database.folder_videos_getListWithProcess
import net.kidjo.server.shared.database.getVideoListFromFolder
import net.kidjo.server.shared.extensions.getInt
import net.kidjo.server.shared.json.v3.cards_folderNormalToJSON
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.Video

const val amountOfFoldersPerFetch = 10
const val amountOfVideos = 2
suspend fun Api3CardsController.cards_getLiveVideos(call: ApplicationCall) {
    val query = call.parameters

    var countryId = countryCodeByGoogle.getCountryIdByIP(call)
    val language = languageCache.get(call)

    var kidAge = query.getInt("age")

    if (kidAge == 0) {
        kidAge = Kid.DEFAULT_AGE
    }
    if (countryId == 0) {
        countryId = config.defaultCountryId
    }
    val videosList = arrayListOf<Video>()

    databaseController.folder_videos_getListWithProcess(
        amountOfFoldersPerFetch,
        language.id,
        countryId,
        kidAge
    )
    { folders ->
        folders.shuffled().forEach {
            val randomVideos = databaseController.getVideoListFromFolder(it.getLongId())
            videosList.addAll(randomVideos.shuffled().asSequence().take(amountOfVideos).toList())
        }
    }
    return call.respondText(jsonCreator.cards_folderNormalToJSON(videosList).toString(), ContentType.Application.Json)
}
