package net.kidjo.server.api3.cards

import io.ktor.server.application.ApplicationCall
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.shared.cachedatabase.recentVideos_add
import net.kidjo.server.shared.database.video_playedAnalytics
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Kid


suspend fun Api3CardsController.card_videoPlayed(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val query = call.parameters
    val json = call.receiveJSON()

    val videoIdEncoded = query.getString("videoId")

    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    } else if (videoIdEncoded == "") {
        call.respondBadParameters()
        return
    } else if (json == null) {
        call.respondBadJSON()
        return
    }


    val kidId = json.getDecodedNormalId("kidId", encryptionController)
    val endAtSeconds = json.optInt("endedAtSeconds")

    if (videoIdEncoded == "" || kidId == Kid.NO_ID_SERVER || endAtSeconds <= 0) {
        call.respondBadParameters()
        return
    }
    val videoId = encryptionController.decodeVideoId(videoIdEncoded)

    GlobalScope.launch { cacheDatabase.recentVideos_add(kidId, videoIdEncoded) }

    val success = databaseController.video_playedAnalytics(device.serverId, kidId, videoId, endAtSeconds)


    if (success) call.respondOK()
    else call.respondDatabaseIssue()
}

suspend fun Api3CardsController.card_videoStarted(call: ApplicationCall) {
    call.respondOK()
}
