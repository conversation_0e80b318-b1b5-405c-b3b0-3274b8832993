package net.kidjo.server.api3.device

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.shared.models.Device
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.json.v3.kid_toJSON
import net.kidjo.server.shared.json.v3.user_toJSON
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.User
import org.json.JSONArray
import org.json.JSONObject
import java.util.*
import kotlin.collections.ArrayList

//todo document

private const val API_3_VERSION_ID = "v3"

suspend fun Api3Controller.device_refresh(call: ApplicationCall) {
    device_refreshGeneric(false, call)
}

suspend fun Api3Controller.device_refreshGeneric(isWebRefresh: Boolean, call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val user = userManager.getUser(call.request.cookies)

    val kids: ArrayList<Kid> = ArrayList<Kid>()

    if (!device.isDeviceIdValid) {
        val handled: Boolean
        if (isWebRefresh) handled = device_refreshWebGetRegisterInfo(user,device,kids,call)
        else handled = device_refreshDeviceGetRegisterInfo(user,device,kids,call)

        if (handled) return
    } else {
        val handled = device_refreshGetData(user,device,kids, call)
        if (handled) return
    }

    val json: JSONObject = JSONObject()
    json.put("videoUrl", config.videoUrl)
    json.put("videoImageUrl", config.videoThumbnailsUrl)
    json.put("folderImageUrl", config.folderUrl)

    val kidJSONArray = JSONArray()
    kids.forEach { kidJSONArray.put(jsonCreator.kid_toJSON(it)) }

    json.put("user", jsonCreator.user_toJSON(user, true))
    json.put("kids", kidJSONArray)

    println(device)
    json.put("activeLanguage",device.languageId)

    json.put("deviceId", device.userId)
    if (!user.isSubscribed) {
        val deviceIsSubscribed = databaseController.device_isSubscribed(device.serverId)
        json.put("isSubscribed",deviceIsSubscribed)
    } else {
        json.put("isSubscribed",user.isSubscribed)
    }
    call.respondJSON(json)
}

//returns if it handles the response
suspend fun Api3Controller.device_refreshRegister(user: User, device: Device, kids: ArrayList<Kid>, manufacturer: String, model: String, locale: Locale, countryId: Int, localTimeZone: Int, languageId: Int, call: ApplicationCall): Boolean {
    val databaseIdForDevice = databaseController.device_new(manufacturer, user.getLongId(), model, "${locale.language}_${locale.country}", countryId, localTimeZone,languageId, API_3_VERSION_ID,device.store.raw,device.userAgent)
    if (databaseIdForDevice == User.NO_SERVER_ID) {
        call.respondDatabaseIssue()
        return true
    }
    device.setServerId(databaseIdForDevice, encryptionController)

    val kid = databaseController.kid_createKidWithDevice(databaseIdForDevice)
    if (kid == null) {
        call.respondBadRequest("issue with kid")
        return true
    }
    kids.add(kid)
    device.languageId = languageId

    return false
}

//returns if it handles the response
suspend fun Api3Controller.device_refreshGetData(user: User, device: Device, kids: ArrayList<Kid>, call: ApplicationCall): Boolean {
    databaseController.device_checkAndGetDeviceInfo(device)
    println(device.serverId)
    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return true
    }

    if (user.isSignedIn) {
        databaseController.user_refresh(user)
    }

    val usingKids = databaseController.kid_getByDeviceId(device.serverId)
    kids.addAll(usingKids)
    return false
}

