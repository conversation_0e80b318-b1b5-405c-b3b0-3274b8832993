package net.kidjo.server.api3.device

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.cachedatabase.getDeivceUserPairingToken
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.DeviceUserPairing
import org.json.JSONObject


suspend fun Api3Controller.device_checkPairing(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    if (!device.isDeviceIdValid) {
        call.respondBadDevice()
        return
    }

    databaseController.device_checkAndGetDeviceInfo(device)

    if (!device.isDeviceIdRegistered) {
        call.respondBadDevice()
        return
    }

    val recievedJSON = call.receiveJSON()
    if (recievedJSON == null) {
        call.respondBadJSON()
        return
    }
    val linkId = recievedJSON.optString("code")
    val lastUpdateStep = DeviceUserPairing.PairingStep.FromRaw(recievedJSON.optInt("lastStep"))

    if (linkId == "") {
        call.respondBadParameters()
        return
    }

    val pairing = cacheDatabase.getDeivceUserPairingToken(linkId)
    if (pairing == null) {
        call.respondBadRequest("no pairing")
        return
    } else if (pairing.serverDeviceId != device.serverId) {
        call.respondBadRequest("bad pairing")
        return
    }



    val json = JSONObject()
    json.put("pairing", pairing.toJSON(encryptionController))
    json.put("link", config.getPairingUrl(device.languageId))
    call.respondJSON(json)
}
