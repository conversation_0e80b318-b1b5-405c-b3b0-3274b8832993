package net.kidjo.server.api3.user.helpers

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.json.v3.user_toJSON
import net.kidjo.server.shared.cachedatabase.getDeivceUserPairingToken
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.UserSession


suspend fun Api3Controller.userHelper_loginAndRegister(isLogin: <PERSON><PERSON><PERSON>, call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val user = User.getEmptyUser()
    userManager.getUser(call.request.cookies, user)

    if (!user.isSignedIn) {
        if (!requestJSON.has("type")) {
            call.respondBadParameters()
            return
        }
        val loginType = requestJSON.optString("type")
        if (loginType == "MIXED") {
            call.respondBadRequest("dont use mixed")
            return
        }

        when (loginType) {
            "email"-> {
                user.authType = User.AuthType.EMAIL
                if (!requestJSON.has("email") || !requestJSON.has("password")) {
                    call.respondBadParameters()
                    return
                }

                val email = requestJSON.optString("email")

                if (!validator.isEmailValid(email)) {
                    call.respondBadRequest("bad email")
                    return
                }

                val alreadyExistUser = databaseController.user_getByEmail(email)
                if (isLogin) {
                    if (alreadyExistUser == null) {
                        call.respondBadRequest("nope")
                        return
                    }

                    val password = requestJSON.optString("password")
                    val passwordMatch = encryptionController.checkPassword(password, alreadyExistUser.hashedPassword)

                    if (!passwordMatch) {
                        call.respondBadRequest("wrong pass")
                        return
                    }
                    user.copy(alreadyExistUser)
                } else {
                    if (alreadyExistUser != null) {
                        call.respondBadRequest("bad email")
                        return
                    }
                    user.email = email

                    val password = requestJSON.optString("password")
                    val hashedPassword = encryptionController.hashPassword(password)
                    user.hashedPassword = hashedPassword

                    val error = userHelper_createUser(user, call)
                    if (error) return
                }
            }
           "facebook","google" -> {
               user.authType = User.AuthType.OAUTH
               user.oAuthType = User.OAuthType.fromRaw(loginType)

               if (!requestJSON.has("token")) {
                    call.respondBadRequest("no token")
                    return
                }
                val token = requestJSON.optString("token")
                user.authToken = token
                val handled = userHelper_oauth(user, call)
                if (handled) return

            }
        }

    }

    val accountLink = requestJSON.optString("link")
    val deviceUserPairing = cacheDatabase.getDeivceUserPairingToken(accountLink)


    if (device.isDeviceIdValid) {
        //todo register device with user account
    }
    if (deviceUserPairing != null) {
        userHelper_pairToUser(user, deviceUserPairing)
    }

    //todo send confirmation email
    userManager.setUser(user, UserSession.LoginType.DIRECT, call.response.cookies)
    call.respondJSON(jsonCreator.user_toJSON(user,true))
}
