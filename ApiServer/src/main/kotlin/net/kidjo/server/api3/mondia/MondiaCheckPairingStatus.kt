package net.kidjo.server.api3.mondia

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveText
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.cachedatabase.mondia_deletePairing
import net.kidjo.server.shared.cachedatabase.mondia_getPairingUserId
import net.kidjo.server.shared.database.device_checkAndGetDeviceInfo
import net.kidjo.server.shared.database.subscription_linkDeviceWithUserSubscription
import net.kidjo.server.shared.database.user_getById
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.UserSession
import org.json.JSONObject


suspend fun Api3Controller.mondia_checkPairingStatus(call: ApplicationCall) {
    val json = call.receiveJSON()
    val customerId = json?.getString("customerId") ?: ""
    var ok = false

    if (customerId != "") {
        val userId = cacheDatabase.mondia_getPairingUserId(customerId)
        if (userId != null) {
            cacheDatabase.mondia_deletePairing(customerId)
            val user = databaseController.user_getById(userId.optLong())
            if (user != null) {
                ok = userManager.setUser(user,UserSession.LoginType.LINK,call.response.cookies)
            }
        }
    }
    val response = JSONObject()
    response.put("success",ok)
    call.respondJSON(response)
}

suspend fun Api3Controller.mondia_checkPairingStatusMobile(call: ApplicationCall) {

    var json: JSONObject? = null
    try {
        json = call.receiveJSON()
    } catch (e: Error) {
        println("Exception | Api3Controller.mondia_checkPairingStatusMobile() | Cannot receive request JSON")
        println(e)
    }
    var text = call.receiveText()
    val query = call.parameters

    println("Text: " + text)
    println("JSON: " + json)
    println("Query: " + query)

    if (json == null && text != "") {
        json = JSONObject(call.receiveText())
    }
    val customerId = json?.getString("customerId") ?: ""
    var ok = false
    var user: String? = null

    if (customerId != "") {
        val userId = cacheDatabase.mondia_getPairingUserId(customerId)
        if (userId != null) {
            // Get device
            val device = call.getDeviceFromHeader(encryptionController)
            if (!device.isDeviceIdValid) {
                call.respondBadDevice()
                return
            }
            databaseController.device_checkAndGetDeviceInfo(device)
            if (!device.isDeviceIdRegistered) {
                call.respondBadDevice()
                return
            }

            // link device with subscription / user account
            val linkDevice = databaseController.subscription_linkDeviceWithUserSubscription(userId.optLong(), device.serverId);
            if (linkDevice) {
                // remove user from cache database
                cacheDatabase.mondia_deletePairing(customerId)

                ok = true
                user = userId
            } else {
                logger.error("Couldn't link user subscription with device: ${device.serverId} | ${userId}")
            }

        }
    } else {
        logger.error("Invalid customer id: ${customerId}")
    }
    val response = JSONObject()
    response.put("success",ok)

    if (user != null) {
        response.put("userId", user)
    }
    call.respondJSON(response)
}
