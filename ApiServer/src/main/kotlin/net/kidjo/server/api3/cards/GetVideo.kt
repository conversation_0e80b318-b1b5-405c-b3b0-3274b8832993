package net.kidjo.server.api3.cards

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.shared.database.videos_get
import net.kidjo.server.shared.database.videos_getAll
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.json.v3.cards_videoToJSON
import org.json.JSONObject


suspend fun Api3CardsController.cards_getVideo(call: ApplicationCall) {
//    val device = call.getDeviceFromHeader(encryptionController)
    val query = call.parameters

    val encodedVideoId = query.getString("videoId")
    if (encodedVideoId == "") {
        call.respond404("no video id")
        return
    }
    val videoId = encryptionController.decodeVideoId(encodedVideoId)
    val video = databaseController.videos_get(videoId)
    if (video == null) {
        call.respond404("no video")
        return
    }

    val json: JSONObject = jsonCreator.cards_videoToJSO<PERSON>(video)
    call.respondJSON(json)
}

suspend fun Api3CardsController.cards_allVideos(call: ApplicationCall) {

    val videos = databaseController.videos_getAll()

    call.respondOK(videos)
}
