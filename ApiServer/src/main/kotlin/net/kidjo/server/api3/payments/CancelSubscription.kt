package net.kidjo.server.api3.payments

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveParameters
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.extensions.*


suspend fun Api3Controller.payments_cancel(call: ApplicationCall) {
    var token: String? = null
    try {
        token = call.receiveParameters().getString("token")
    } catch (exception: Exception) {
        print("Unable to get parameters")
    }

    var userId: String? = null
    try {
        userId = call.receiveJSON()?.getString("userId")
    } catch (exception: Exception) {
        print("Unable to get body")
    }

    val user = when {
        token != null && token != "" -> userManager.getUserByToken(token)
        userId != null && userId != "" -> userManager.getUser(userId)
        else -> userManager.getUser(call.request.cookies)
    }

    when {
        user.isSignedIn.not() -> call.respondBadUser()
        user.isSubscribed.not() -> call.respondBadRequest("user is not subscribed")
        else -> {
            val subscription = databaseController.subscription_getRecentActive(user.getLongId())
            when {
                subscription == null -> {
                    call.respond404("Does not exist")
                }
                subscription.userId != user.getLongId() -> {
                    call.respondBadRequest("Not your subscription")
                }
                subscription.isRenewing.not() -> {
                    call.respondBadRequest(ServerErrors.ERROR_SUBSCRIPTION_IS_NOT_RENEWING, "Already cancelled")
                }
                else -> {
                    paymentManager.cancelSubscription(user, subscription, true).also { success ->
                        if (success) {
                            call.respondOK()
                        } else {
                            call.respondBadRequest("Could not cancel")
                        }
                    }
                }
            }
        }
    }
}
