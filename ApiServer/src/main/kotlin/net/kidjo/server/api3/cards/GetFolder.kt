package net.kidjo.server.api3.cards

import io.ktor.server.application.ApplicationCall
import net.kidjo.common.models.Folder
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.shared.cachedatabase.recentVideos_get
import net.kidjo.server.shared.database.folders_get
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.json.v3.cards_folderHistoryToJSON
import net.kidjo.server.shared.models.Kid
import org.json.JSONObject

suspend fun Api3CardsController.cards_getFolder(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val query = call.parameters

    val folderId = query.getStringToLongId("folderId", Folder.NO_ID_LONG)
    val namedId = query.getString("folderId")
    if (folderId == Folder.NO_ID_LONG && namedId != Folder.NAMED_ID_HISTORY) {
        call.respond404("no folder")
        return
    }

    val json: JSONObject

    if (namedId == Folder.NAMED_ID_HISTORY) {
        val kidId = query.getDecodedNormalId("kidId", encryptionController)

        if (kidId == Kid.NO_ID_SERVER) {
            call.respond404("no folder")
            return
        }
        var videos = cacheDatabase.recentVideos_get(kidId)
        if (videos == null) videos = ArrayList()

        json = jsonCreator.cards_folderHistoryToJSON(videos)
    } else {
        val folder = databaseController.folders_get(folderId)
        if (folder != null) {
            json = CreateV3FolderCardJson(folder, jsonCreator, databaseController)
        } else {
            call.respondDatabaseIssue()
            return
        }
    }

    call.respondJSON(json)
}
