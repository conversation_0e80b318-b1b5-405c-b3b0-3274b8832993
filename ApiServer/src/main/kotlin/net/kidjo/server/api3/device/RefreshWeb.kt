package net.kidjo.server.api3.device

import io.ktor.server.application.ApplicationCall
import io.ktor.server.plugins.origin
import io.ktor.server.request.acceptLanguage
import io.ktor.server.request.userAgent
import net.kidjo.server.shared.models.Device
import net.kidjo.common.models.Language
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.User
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.util.*

// Return boolean if call is handled or not
suspend fun Api3Controller.device_refreshWeb(call: ApplicationCall) {
    device_refreshGeneric(true, call)
}

suspend fun Api3Controller.device_refreshWebGetRegisterInfo(user: User, device: Device, kids: ArrayList<Kid>, call: ApplicationCall): <PERSON>olean {
    val userAgent = call.request.userAgent() ?: ""
    val acceptLanguageRaw = call.request.acceptLanguage() ?: ""

    val json = call.receiveJSON()
    if (json == null) {
        call.respondBadJSON()
        return true
    } else if (!json.has("timeZoneOffsetInMinutes")) {
        call.respondBadRequest("No timeZoneOffsetInMinutes")
    }

    if (userAgent == "" || acceptLanguageRaw == "") {
        logger.error("User agent was blank or accept language was blank, userAgent $userAgent, acceptLanguage: $acceptLanguageRaw") //todo
        call.respondBadRequest("No user agent or accept language header")
        return true
    }

    val deviceManufacturer = "web"
    val deviceModel = call.request.userAgent()?.getBrowserFromUA()?.takeIf { it.length <= 100 } ?: "none"

    // Get country over ip
    val client = OkHttpClient()
    val url = "https://api.kidjo.tv/v2/country${if (call.request.origin.remoteHost == "0:0:0:0:0:0:0:1") "" else "?ip=${call.request.origin.remoteHost}"}"
    val request = Request.Builder().url(url).build()

    var response: Response? = null

    try {
        response = client.newCall(request).execute()
    } catch(e: Exception) {
        println("Error ${e.message}")
    }

    var geoPluginJSON: JSONObject? = null

    if (response != null) {
        val geoPluginResponse = response.body()!!.string()
        geoPluginJSON = JSONObject(geoPluginResponse)
        response.close()
    }

    val countryIp: String = if (geoPluginJSON != null) geoPluginJSON.getString("geoplugin_countryCode") else ""

    val localesStrings = acceptLanguageRaw.split(",")
    val usersLocale: Locale
    var languageId: Int

    if (localesStrings.size > 0) {
        usersLocale = Locale.forLanguageTag(localesStrings[0])
        languageId = languageCache.getLanguageIdFromAcceptLanguageHeader(acceptLanguageRaw)
    } else {
        logger.info("Something went wrong with finding the locale, Locale String: $acceptLanguageRaw")
        usersLocale = config.locale_default
        languageId = languageCache.getLanguageIdWithShortName(usersLocale.language)
        if (languageId == Language.NO_ID) languageId = config.defaultLanguageId
    }

    if (countryIp == "TN") {
        languageId = languageCache.getLanguageIdWithShortName("fr")
    }


    var countryId: Int = databaseController.user_getCountryIdFromShortName(if (countryIp != "") countryIp else usersLocale.country)
    if (countryId < 0) {
        countryId = config.defaultCountryId
    }


    println("IP: " + call.request.origin.remoteHost)
    println("GeoPluginResponse: " + geoPluginJSON)
    println("LanguageID: " + languageId)
    println("CountryID: " + countryId)
    val databaseTimeZone: Int = json.optInt("timeZoneOffsetInMinutes")
    val handled = device_refreshRegister(user,device,kids,deviceManufacturer,deviceModel,usersLocale,countryId,databaseTimeZone,languageId,call)
    if (countryId < 0) {
        logger.info("Could not find country with locale: $usersLocale, for device: ${device.serverId}")
    }
    return handled
}
