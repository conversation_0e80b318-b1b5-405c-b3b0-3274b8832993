package net.kidjo.server.api3.kid

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.json.v3.cards_videoToJSON
import net.kidjo.server.shared.database.favorites_getList
import net.kidjo.server.shared.extensions.getDecodedNormalId
import net.kidjo.server.shared.extensions.getInt
import net.kidjo.server.shared.extensions.respond404
import net.kidjo.server.shared.extensions.respondJSON
import net.kidjo.server.shared.models.Kid
import org.json.JSONArray
import org.json.JSONObject

private const val MAX_LIMIT = 16

suspend fun Api3Controller.kid_favorites_list(call: ApplicationCall) {
    val query = call.parameters
    val kidId = query.getDecodedNormalId("kidId", encryptionController)

    if (kidId == Kid.NO_ID_SERVER) {
        call.respond404("no kid")
        return
    }
    val offset = query.getInt("offset",0)
    val limit = kotlin.math.min(MAX_LIMIT,query.getInt("limit",MAX_LIMIT))

    val favorites = databaseController.favorites_getList(kidId, offset, limit)
    val json = JSONObject()
    val videoJson = JSONArray()
    favorites.forEach { videoJson.put(jsonCreator.cards_videoToJSON(it)) }
    json.put("favorites", videoJson)
    call.respondJSON(json)
}
