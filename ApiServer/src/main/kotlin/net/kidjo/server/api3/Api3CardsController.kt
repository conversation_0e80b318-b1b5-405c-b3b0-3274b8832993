package net.kidjo.server.api3

import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.http.ContentType
import io.ktor.server.response.respondText
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import net.kidjo.common.models.Folder
import net.kidjo.server.api3.cards.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.getDecodedNormalId
import net.kidjo.server.shared.extensions.getDeviceFromHeader
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.Video
import net.kidjo.server.utils.getInternalData
import org.json.JSONArray
import org.json.JSONObject
import org.slf4j.LoggerFactory

class Api3CardsController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    companion object {

        fun createFolderJson(folder: Folder, videos: List<Video>? = null, details: Boolean = false): JSONObject {
            val json = JSONObject()
            json.put("id", folder.id)
            if (folder.namedId != "") json.put("namedId", folder.namedId)
            if (folder.title != "") json.put("title", folder.title)
            json.put("type", "folder")
            json.put("contentType", folder.contentType.raw)
            json.put("mediaType", folder.mediaType.raw)

            videos?.let {
                val subCards = JSONArray()
                it.forEach { video -> subCards.put(createVideoJson(video, details)) }
                json.put("subcards", subCards)
            }

            return json
        }

         fun createVideoJson(video: Video, details: Boolean = false): JSONObject {
            val json = JSONObject()
            json.put("id", video.userId)
            json.put("title", video.title)
            json.put("ageMin", video.ageMin)
            json.put("ageMax", video.ageMax)
            json.put("duration", video.duration)
            json.put("isLocked", video.isPremium)
            json.put("type", "video")

            if (details) {
                json.put("formats", JSONArray(List(video.formats.size) { i ->
                    val format = video.formats[i]
                    val size = if (video.formatSizes.size > i) video.formatSizes[i] else 0L

                    val formatJson = JSONObject()
                    formatJson.put("id", format.id)
                    formatJson.put("height", format.height)
                    formatJson.put("sd", format.delivery)
                    formatJson.put("href", Video.Format.GetVideoUrlWithFormat(video.userId, format))

                    if (size > 0L)
                        formatJson.put("fileSize", size)

                    formatJson
                }))
            }

            return json
        }

    }

    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)
    internal val logger = LoggerFactory.getLogger("Api3CardsController")

    override fun installRoutes(route: Route) {
        route.get("/getList") { this@Api3CardsController.cards_getList(call) }
        route.get("/getLiveVideos") { this@Api3CardsController.cards_getLiveVideos(call) }
        route.get("/folder/{folderId}") { this@Api3CardsController.cards_getFolder(call) }
        route.get("/video/{videoId}") { this@Api3CardsController.cards_getVideo(call) }
        route.get("/videos") { this@Api3CardsController.cards_allVideos(call) }
        route.get("/search") { search(call) }

        route.post("/video/{videoId}/played") { this@Api3CardsController.card_videoPlayed(call) }
        route.post("/video/{videoId}/end") { this@Api3CardsController.card_videoStarted(call) }

        // internal usage APIs
        route.get("/internal/data") {<EMAIL>(call)}
    }

    suspend fun search(call: ApplicationCall) {
        val device = call.getDeviceFromHeader(encryptionController)
        val queryParameters = call.parameters

        val kidId = queryParameters.getDecodedNormalId("kidId", encryptionController)

        val folderType = Folder.ContentType.FromRaw(queryParameters.getString("contentType"))

        val filters = queryParameters.getString("filters", "")
        val noFilters = filters == ""
        val filtersList = if (noFilters) emptyList() else filters.split(",")
        val hasFolders = noFilters || filtersList.contains("folder")
        val hasVideos = noFilters || filtersList.contains("video")
        val hasGames = noFilters || filtersList.contains("game")

        val search = queryParameters.getString("search", "")
        val details = queryParameters.getString("details", "").toBoolean()

        val languageId: Int
        val countryId: Int
        val kidAge: Int
        val filterFolders: String

        if (kidId != Kid.NO_ID_SERVER) {
            val info = databaseController.generic_singleRowQuery("SELECT avatars.id, avatars.age, languageId, devices.countryId, GROUP_CONCAT(avatars_folders.folderId) as filteredFolders "
                    + " FROM devices JOIN avatars on devices.id = avatars.deviceId"
                    + " LEFT JOIN avatars_folders ON avatars_folders.avatarId = avatars.id"
                    + " WHERE avatars.id = ?"
                    + " AND avatars.age IS NOT null"
                    + " GROUP BY avatars.id") { statement ->
                statement.setLong(1, kidId)
            }
            languageId = info?.get("languageId") as? Int ?: config.defaultLanguageId
            countryId = info?.get("countryId") as? Int ?: config.defaultCountryId
            kidAge = info?.get("avatars.age") as? Int ?: Kid.DEFAULT_AGE
            filterFolders = info?.get("filteredFolders") as? String ?: ""
        } else {
            databaseController.device_checkAndGetDeviceInfo(device)

            kidAge = Kid.DEFAULT_AGE
            filterFolders = ""
            if (device.infoFromServer) {
                languageId = device.languageId
                countryId = device.countryId
            } else {
                languageId = config.defaultLanguageId
                countryId = config.defaultCountryId
            }
        }

        val json = JSONObject()

        if (search.length >= 3) {
            if (hasFolders || hasVideos) {
                val foldersJson = JSONArray()
                val folders = databaseController.folderSearch(search, languageId, countryId, kidAge, folderType, filterFolders, hasGames)
                for (folder in folders) {
                    val videos = if (details) databaseController.videos_getListFromFolder(folder.getLongId())
                    else null
                    foldersJson.put(createFolderJson(folder, videos, details))
                }

                if (hasFolders) {
                    json.put("folders", foldersJson)
                }

                if (hasVideos) {
                    val videosJson = JSONArray()
                    val videos = databaseController.videoSearch(search, languageId, countryId, kidAge, folderType, filterFolders)
                    val videosIds = videos.map { it.userId }
                    for (video in videos) {
                        videosJson.put(createVideoJson(video, details))
                    }

                    // Add videos from folders
                    for (i in 0 until foldersJson.length()) {
                        val folderVideosJson = (foldersJson[i] as JSONObject).get("subcards") as JSONArray
                        for (j in 0 until folderVideosJson.length()) {
                            if (!videosIds.contains((folderVideosJson[j] as JSONObject).get("id"))) {
                                videosJson.put(folderVideosJson[j])
                            }
                        }
                    }

                    json.put("videos", videosJson)
                }
            }
        } else {
            // Return suggestions if the search is not long enough
            if (hasFolders) {
                val foldersJson = JSONArray()
                val folders = databaseController.folders_getList(5, 0, languageId, countryId, kidAge, folderType, filterFolders, !hasGames)
                for (folder in folders) {
                    val videos = if (details) databaseController.videos_getListFromFolder(folder.getLongId())
                    else null
                    foldersJson.put(createFolderJson(folder, videos, details))
                }
                json.put("folders", foldersJson)
            }

            if (hasVideos) {
                val videosJson = JSONArray()
                val folders = databaseController.folders_getList(5, 5, languageId, countryId, kidAge, folderType, filterFolders, !hasGames)
                for (folder in folders) {
                    databaseController.videosGetFirstFromFolder(folder.getLongId(), false)?.let { video ->
                        videosJson.put(createVideoJson(video, details))
                    }
                }
                json.put("videos", videosJson)
            }
        }

        call.respondText(json.toString(), ContentType.Application.Json)
    }

}
