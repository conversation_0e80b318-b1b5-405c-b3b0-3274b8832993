package net.kidjo.server.api3.publishers

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import net.kidjo.common.models.Language
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.payments.SwisscomApiManager
import org.slf4j.LoggerFactory
import java.time.LocalDate
import java.time.Month
import java.time.format.DateTimeFormatter

class SwisscomController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    data class PromotedFolder(val name: String,
                              val description: String,
                              val genre: String,
                              val image: String,
                              val deeplink: String)

    companion object {

        private const val ERROR_ABID_UPDATE = "A problem occurred while updating the ABID"
        private const val ERROR_CARRIER_BILLING_NOT_AVAILABLE = "Carrier billing is not available"
        private const val ERROR_INVALID_USER = "Invalid device identifier"
        private const val ERROR_USER_ALREADY_SUBSCRIBED = "User is already subscribed"
        private const val ERROR_USER_NOT_SUBSCRIBED = "User is not subscribed"
        private const val ERROR_USER_SUBSCRIPTION_ALREADY_CANCELLED = "User has already cancelled his subscription"
        private const val ERROR_USER_DEVICE_NOT_REGISTERED = "User device has not been registered"
        private const val ERROR_SUBSCRIPTION_UPDATE = "A problem occurred while creating the subscription"
        private const val ERROR_SUBSCRIPTION_CANCEL = "A problem occurred while cancelling the subscription"


        private const val SWISSCOM_FAKE_ACCOUNT_PASSWORD_LENGTH = 8
        private const val SWISSCOM_FAKE_ACCOUNT_EMAIL_PREFIX_LENGTH = 8
        private const val SWISSCOM_FAKE_ACCOUNT_EMAIL_SUFFIX = "@swisscom.kidjo.net"
        private val SWISSCOM_FAKE_ACCOUNT_CHARACTERS_TO_USE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()

    }

    private val logger = LoggerFactory.getLogger("SwisscomController")

    private val dateFormatter = DateTimeFormatter.ofPattern(SwisscomApiManager.SWISSCOM_DATE_FORMAT)

    override fun installRoutes(route: Route) {
        // Swisscom API and billing specific routes
        route.post("/availability") { <EMAIL>(call) }
        route.post("/register") { <EMAIL>(call) }
        route.post("/subscribe") { <EMAIL>(call) }
        route.post("/unsubscribe") { <EMAIL>(call) }

        // Miscellaneous routes to return data
        route.get("/promoted") { <EMAIL>(call) }
    }

    //region Route methods

    /**
     * Request new anonymous billing identifier (ABID) for session
     */
    private suspend fun availability(call: ApplicationCall) {
        // Retrieve session identifier and merchant identifier from body
        val identification = checkIdentification(call) ?: return

        // Request availability to server
        val isAvailable = false //= SwisscomApiManager.sendAvailabilityRequest(identification)
        if (isAvailable) {
            logger.debug("availability() | sessionId '${identification.sessionId}' | carrier billing is available")
            call.respond(HttpStatusCode.OK)
        } else {
            logger.error("availability() | sessionId '${identification.sessionId}' | carrier billing not available")
            call.respondError(HttpStatusCode.Conflict, SwisscomController.ERROR_CARRIER_BILLING_NOT_AVAILABLE)
        }
    }

    /**
     * Request new anonymous billing identifier (ABID) for session
     */
    private suspend fun register(call: ApplicationCall) {
        val today: LocalDate = LocalDate.now()
        val endDate: LocalDate = LocalDate.of(2021, Month.MARCH, 23)
        if(today.isBefore(endDate)) {
            return call.respond(HttpStatusCode.OK, SwisscomApiManager.SwisscomRegisterResponse(true))
        } else {
            call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_INVALID_USER)
        }

        // Retrieve requesting device information
        /*val device = call.getDeviceFromHeader(encryptionController)
        if (!device.isDeviceIdValid) {
            logger.error("register() | deviceId '${device.serverId}' is not valid")
            call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_INVALID_USER)
            return
        }

        // Retrieve session identifier and merchant identifier from body
        val identification = checkIdentification(call) ?: return

        // Check if the device is already registered
        val existingAbid = databaseController.swisscomGetAbidWithDeviceId(device.serverId)
        if (existingAbid != null) {
            logger.debug("register() | sessionId '${device.serverId}' has an already linked ABID: '${existingAbid}'")
            // Do not save again the ABID if it is already registered
            return call.respond(HttpStatusCode.NoContent, "{}")
        }

        // Request ABID for the specified session
        val deviceAbid = SwisscomApiManager.sendAbidRequest(device, identification, call) ?: return
        val existingUserId = databaseController.swisscomGetUserIdWithAbid(deviceAbid) ?: -1L

        if (existingUserId != -1L)
        {
            // -- Link device to the existing user
            databaseController.subscription_linkDeviceWithUserSubscription(existingUserId, device.serverId)

            // Save retrieved ABID
            val result = databaseController.swisscomLinkWithAbid(device.serverId, deviceAbid, existingUserId)
            if (result) {
                logger.info("register() | deviceId '${device.serverId}' updated properly through Swisscom API with ABID: $deviceAbid")
                return call.respond(HttpStatusCode.OK, SwisscomApiManager.SwisscomRegisterResponse(databaseController.getActiveDeviceSubscription(device.serverId)))
            } else {
                logger.error("register() | failed to update the ABID ($deviceAbid) in the database for deviceId: '${device.serverId}'")
                return call.respondError(HttpStatusCode.InternalServerError, SwisscomController.ERROR_ABID_UPDATE)
            }
        }

        // Create a fake user to link the device to it and then the subscription to it
        // -- Create fake email
        var fakeEmail = ""
        while (fakeEmail == "") {
            fakeEmail = utility.randomString(SWISSCOM_FAKE_ACCOUNT_EMAIL_PREFIX_LENGTH, SWISSCOM_FAKE_ACCOUNT_CHARACTERS_TO_USE) + SWISSCOM_FAKE_ACCOUNT_EMAIL_SUFFIX
            val user = databaseController.user_getByEmail(fakeEmail)
            if (user != null) fakeEmail = ""
        }
        // -- Create random password
        val password = utility.randomString(SWISSCOM_FAKE_ACCOUNT_PASSWORD_LENGTH, SWISSCOM_FAKE_ACCOUNT_CHARACTERS_TO_USE)
        val hashedPassword = encryptionController.hashPassword(password)
        // -- Create the user and attach its id to the model object
        val insertingUser = User(User.NO_ID, fakeEmail, false, "", hashedPassword)
        val userId = databaseController.user_register(insertingUser)
        if (userId == User.NO_SERVER_ID) {
            logger.error("register() | failed to create a fake user in the database for deviceId: '${device.serverId}'")
            return call.respondError(HttpStatusCode.InternalServerError, SwisscomController.ERROR_ABID_UPDATE)
        }
        // -- Link device to the newly created user
        databaseController.subscription_linkDeviceWithUserSubscription(userId, device.serverId)

        // Save retrieved ABID
        val result = databaseController.swisscomLinkWithAbid(device.serverId, deviceAbid, userId)
        if (result) {
            logger.info("register() | deviceId '${device.serverId}' registered properly through Swisscom API as fake user '$fakeEmail' ($userId) with ABID: $deviceAbid")
            call.respond(HttpStatusCode.OK, "{}")
        } else {
            logger.error("register() | failed to register the ABID ($deviceAbid) in the database for deviceId: '${device.serverId}'")
            call.respondError(HttpStatusCode.InternalServerError, SwisscomController.ERROR_ABID_UPDATE)
        }*/
    }

    /**
     * Subscribe the Swisscom user and handle the several Swisscom billing API calls
     */
    private suspend fun subscribe(call: ApplicationCall) {
        // Retrieve requesting device information
        /*val device = call.getDeviceFromHeader(encryptionController)
        if (!device.isDeviceIdValid) {
            logger.error("subscribe() | deviceId '${device.serverId}' is not valid")
            return call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_INVALID_USER)
        }

        // Check if there is an existing subscription for the device's user
        val existingSubscription = databaseController.subscription_getRecentActive(device.getLongUserId())
        if (existingSubscription?.isActive == true) {
            logger.error("subscribe() | deviceId '${device.getLongUserId()}' is already subscribed")
            return call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_USER_ALREADY_SUBSCRIBED)
        }

        // Check existing ABID registration for the device
        val deviceAbid = databaseController.swisscomGetAbidWithDeviceId(device.serverId)
        if (deviceAbid == null) {
            logger.error("subscribe() | deviceId '${device.serverId}' is not registered")
            return call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_USER_DEVICE_NOT_REGISTERED)
        }

        // Send billing request to the Swisscom billing API
        val subscriptionData = SwisscomApiManager.sendSubscriptionRequest(deviceAbid, call)
        if (subscriptionData != null) {
            // Create subscription for the user
            val userId = databaseController.deviceGetUserId(device)
            val userIdLong = userId.optLong()
            val subscriptionRootInsert = SubscriptionRootInsert(userIdLong, device.serverId, false, SwisscomApiManager.SWISSCOM_SUBSCRIPTION_PRICE.toFloat(),
                    SubscriptionRoot.PaymentType.NATIVE, SubscriptionRoot.SubscriptionType.KIDJO_TV, SwisscomApiManager.SWISSCOM_KIDJO_PAYMENT_ID, deviceAbid, Device.StorePlatform.SWISSCOM, null,
                    SwisscomApiManager.SWISSCOM_KIDJO_IAP_ID, SwisscomApiManager.SWISSCOM_KIDJO_PURCHASE_SESSION_ID, subscriptionData.requestId,
                    encryptionController.sha256Hash(subscriptionData.requestId), 0L, true, subscriptionData.endDate, false)
            val subscriptionId = databaseController.subscriptionRoot_create(subscriptionRootInsert)
            return if (subscriptionId == SubscriptionRoot.NO_ID) {
                logger.error("subscribe() | /!\\ CRITICAL: user ($userIdLong) with deviceId '${device.serverId}' was charged via Swisscom but the subscription failed to create in the database "
                        + "(price: ${SwisscomApiManager.SWISSCOM_SUBSCRIPTION_PRICE}, ABID: $deviceAbid, requestId: ${subscriptionData.requestId}, endDate: ${subscriptionData.endDate.format(dateFormatter)}) /!\\")
                call.respondError(HttpStatusCode.InternalServerError, SwisscomController.ERROR_SUBSCRIPTION_UPDATE)
            } else {
                logger.info("subscribe() | user ($userIdLong) with deviceId '${device.serverId}' has successfully subscribed to Kidjo via Swisscom billing service "
                        + "(price: ${SwisscomApiManager.SWISSCOM_SUBSCRIPTION_PRICE}, ABID: $deviceAbid, requestId: ${subscriptionData.requestId}, endDate: ${subscriptionData.endDate.format(dateFormatter)})")
                call.respondOK()
            }
        }

        logger.error("subscribe() | failed to update subscription for deviceId '${device.serverId}' and ABID '$deviceAbid'")
        call.respondError(HttpStatusCode.InternalServerError, SwisscomController.ERROR_SUBSCRIPTION_UPDATE)*/

        call.respondOK()
    }

    /**
     * Unsubscribe the Swisscom user
     */
    private suspend fun unsubscribe(call: ApplicationCall) {
        /*// Retrieve requesting device information
        val device = call.getDeviceFromHeader(encryptionController)
        if (!device.isDeviceIdValid) {
            logger.error("unsubscribe() | deviceId '${device.serverId}' is not valid")
            return call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_INVALID_USER)
        }

        // Check if there is an existing subscription for the device's user
        val userId = databaseController.deviceGetUserId(device)
        val userIdLong = userId.optLong()
        val existingSubscription = databaseController.subscription_getRecentActive(userIdLong)
        if (existingSubscription?.isActive != true) {
            logger.error("unsubscribe() | userId '$userId' with deviceId '${device.serverId}' is not subscribed")
            return call.respondError(HttpStatusCode.BadRequest, SwisscomController.ERROR_USER_NOT_SUBSCRIBED)
        } else if (!existingSubscription.isRenewing) {
            logger.debug("unsubscribe() | userId '$userId' with deviceId '${device.serverId}' has already cancelled his subscription")
            return call.respond(HttpStatusCode.NoContent, SwisscomController.ERROR_USER_SUBSCRIPTION_ALREADY_CANCELLED)
        }

        // Cancel the subscription
        val result = databaseController.subscription_cancel(existingSubscription.id)
        if (!result) {
            logger.error("unsubscribe() | user ($userIdLong) with deviceId '${device.serverId}' was not able to unsubscribe properly from Kidjo")
            return call.respondError(HttpStatusCode.InternalServerError, SwisscomController.ERROR_SUBSCRIPTION_CANCEL)
        }

        logger.info("unsubscribe() | user ($userIdLong) with deviceId '${device.serverId}' has successfully unsubscribed from Kidjo, the renewal is now cancelled")*/
        call.respondOK()
    }

    /**
     * Retrieve the Kidjo promoted content for Swisscom to display in their 'Kids' section on the Swisscom box
     */
    private suspend fun getPromotedContent(call: ApplicationCall) {
        val languageParam = call.parameters["language"]
        val language = languageParam?.let { Language.fromShortName(it) } ?: Language.ENGLISH
        val countryId = 216 // Switzerland country identifier
        val promotedContent = databaseController.licensesGetList(25, language.id, countryId, swisscom = true)
        val list = promotedContent.map {
            val image = it.image ?: "${config.folderUrl}folderImage/1920_1080/${it.folderId}.png"
            val deeplink = "kidjo://swisscom/folder/${it.folderId}?languageId=${language.id}"
            PromotedFolder(it.title, it.description ?: "", it.genre?.capitalize() ?: "", image, deeplink)
        }
        call.respond(list)
    }

    //endregion

    //region Utils

    private suspend fun checkIdentification(call: ApplicationCall): SwisscomApiManager.SwisscomIdentification? {
        val identification = call.receiveOrNull<SwisscomApiManager.SwisscomIdentificationJson>()
        val requestEndpoint = identification?.requestEndpoint
        val sessionId = identification?.sessionId
        return if (requestEndpoint == null || sessionId == null) {
            logger.warn("checkIdentification() | identification failed, missing parameters: requestEndpoint '${identification?.requestEndpoint}' or sessionId '${identification?.sessionId}'")
            call.respondError(HttpStatusCode.BadRequest, "Missing parameters: requestEndpoint or sessionId")
            null
        } else {
            logger.info("checkIdentification() | identification received: requestEndpoint => ${identification.requestEndpoint}, sessionId => ${identification.sessionId}")
            SwisscomApiManager.SwisscomIdentification(requestEndpoint, sessionId)
        }
    }

    //endregion

}
