package net.kidjo.server.api3.accountcoupon

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.json.v3.accountCoupon_toJson
import net.kidjo.server.shared.database.accountCoupon_getByCouponId
import net.kidjo.server.shared.extensions.*


suspend fun Api3Controller.accountCoupon_setToCookies(call: ApplicationCall) {
    val requestJson = call.receiveJSON()
    if (requestJson == null) {
        call.respondErrorWithLocalMessage(HttpStatusCode.BadRequest,"issue with coupon")
        return
    }

    val couponId = requestJson.optString("couponId")
    if (couponId == "") {
        call.respondErrorWithLocalMessage(HttpStatusCode.BadRequest,"issue with coupon")
        return
    }

    val accountCoupon = databaseController.accountCoupon_getByCouponId(couponId)
    if (accountCoupon == null) {
        call.respondErrorWithLocalMessage(HttpStatusCode.BadRequest,"The coupon is invalid")
        return
    } else if (!accountCoupon.isValid()){
        call.respondErrorWithLocalMessage(HttpStatusCode.BadRequest,"The coupon is invalid")
        return
    }
    couponManager.setToCall(accountCoupon, call)
    val json = jsonCreator.accountCoupon_toJson(accountCoupon)
    call.respondJSON(json)
}


suspend fun Api3Controller.accountCoupon_removeCouponFromCookie(call: ApplicationCall) {
    //todo invalidate token
    couponManager.removeFromCall(call)
    call.respondOK()
}
