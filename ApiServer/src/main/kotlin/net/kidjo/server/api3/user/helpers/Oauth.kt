package net.kidjo.server.api3.user.helpers

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.User


suspend fun Api3Controller.userHelper_oauth(user: User, call: ApplicationCall): Boolean {
    val success: Boolean
    if (user.oAuthType == User.OAuthType.GOOGLE) {
        success = googleApiManager.getVerifyAndUserInfo(user)
    } else if (user.oAuthType == User.OAuthType.FACEBOOK) {
        success = userManager.getVerifyAndFacebookUserInfo(user)
    } else {
        call.respondBadRequest("not oauth")
        return true
    }

    if (!success) {
        call.respondBadRequest("something went wrong")
        return true
    }
    //first see if the user exists
    val oauthUser = databaseController.user_getByOauthId(user.oAuthType,user.authId)
    if (oauthUser == null) { // new user
        //if a new user check to see if an account already exists
        var foundAccount = false
        if (validator.isEmailValid(user.email)) {
            val pairingUser = databaseController.user_getByEmail(user.email) //check and see if user exists by email first
            if (pairingUser != null) {
                //found an account by its email
                foundAccount = true

                //pair this account
                //set OAUTH id and update name if name is empty
                if (pairingUser.name == "") pairingUser.name = user.name
                pairingUser.authId = user.authId
                pairingUser.authToken = user.authToken

                user.copy(pairingUser)
                user.authType = User.AuthType.MIXED
                databaseController.user_updateWithOauth(user) //update user
            }
        }
        //create user
        if (!foundAccount) return userHelper_createUser(user, call)
    } else {
        //user already exists copy the information and end
        oauthUser.authToken = user.authToken
        user.copy(oauthUser)
    }
    return false
}
