package net.kidjo.server.api3.subscription

import io.ktor.server.application.ApplicationCall
import net.kidjo.common.models.SubscriptionEvent
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.wv_analyticsEvent
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.tools.payments.startTrackingPlatformSubscription


suspend fun Api3Controller.subscription_set(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val user = userManager.getUser(call.request.cookies)

    val json = call.receiveJSON()

    if (!device.isDeviceIdValid) {
        call.respondDatabaseIssue()
        return
    } else if (json == null) {
        call.respondBadJSON()
        return
    } else if (!json.has("sessionId") || !json.has("timeStamp") || !json.has("iapId") ||
            !json.has("subscriptionToken")) {
        call.respondBadParameters()
        return
    }

    val iapId = json.optString("iapId")
    val iap = iapManager.getIAP(device.store,iapId)
    if (iap == null) {
        call.respondBadRequest("Bad Iap")
        return
    }
    val subscriptionToken = json.optString("subscriptionToken")
    val timeStamp = json.optLong("timeStamp")
    val orderId = json.optString("orderId")
    val attributionDeepLink = json.optString("link")
    val sessionIdHolding = json.optString("sessionId")
    val session = sessionIdHolding.substring(0,kotlin.math.min(32,sessionIdHolding.length))


    var success = paymentManager.startTrackingPlatformSubscription(device.serverId,iap,subscriptionToken,orderId,session,attributionDeepLink)

    if (!success) {
        call.respondBadRequest("Something went wrong tracking the subscription")
        return
    }

    success = databaseController.wv_analyticsEvent(session, SubscriptionEvent.Type.DID_SUBSCRIBE, timeStamp)

    if (success) call.respondOK()
    else call.respondDatabaseIssue()
}
