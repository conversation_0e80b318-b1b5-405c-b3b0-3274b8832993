package net.kidjo.server.api3.payments

import io.ktor.server.application.ApplicationCall
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.shared.tools.LanguageTerms
import net.kidjo.server.shared.tools.payments.PaymentManager
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.time.LocalDate
import java.time.LocalDateTime

suspend fun Api3Controller.payments_process(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)
    val user = userManager.getUser(call.request.cookies)
    var activeSubscriptionBillDate: LocalDateTime? = null
    var shouldForceNoFreeTrial = false

    if (user.isSignedIn == false) {
        call.respondBadUser()
        return
    } else if (user.isSubscribed) {
        val mostRecentSub = databaseController.subscription_getRecentActive(user.getLongId())
        if (mostRecentSub != null) {
            if (mostRecentSub.isRenewing) {
                call.respondBadRequest("user is subscribed")
                return
            }

            //todo once we integrate other payment methods in
            activeSubscriptionBillDate = mostRecentSub.nextBillDate
            shouldForceNoFreeTrial = true //was subscribed
        }
    }

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val nonce = requestJSON.optString("nonce")
    val recaptchaToken = requestJSON.optString("recaptcha")
    val billingName = requestJSON.optString("name")
    val deviceData = requestJSON.optString("deviceData")

    // Check recaptcha
    val recaptchaSecretKey = "6LeX2M0UAAAAAJ6hxJfkPQ11VrfNjdYkHToMxawp"
    val client = OkHttpClient()
    val url = "https://www.google.com/recaptcha/api/siteverify"
    val body = FormBody.Builder()
            .add("secret", recaptchaSecretKey)
            .add("response", recaptchaToken)
            .build()
    val request = Request.Builder().url(url).post(body).build()
    var response: Response? = null
    try {
        response = client.newCall(request).execute()
    } catch(e: Exception) {
        println("Error getting data from recaptcha service: ${e.message}")
    }

    var isSuccessful = false
    if (response != null) {
        val stringResponse = withContext(Dispatchers.IO) { response.body()?.string() }
        var jsonObject: JSONObject? = null
        try {
            jsonObject = JSONObject(stringResponse)
        } catch(e: Exception) {
            println("Error parsing data from recaptcha service: ${e.message}")
        }
        response.close()
        isSuccessful = jsonObject?.getBoolean("success") ?: false
    }

    if (!isSuccessful) {
        call.respondBadRequest("Invalid recaptcha")
        return
    }

    val isMainCountry = true
    val iap = iapManager.getDefaultIAP(Device.StorePlatform.KIDJO_BRAINTREE,true,isMainCountry)
    val accountCoupon = couponManager.getFromCall(call)
    //iap id kidjo_server_plan_5_monthly_7f

    val addingPaymentError = paymentManager.setPaymentDefaultMethod(user, billingName, nonce, deviceData)
    if (!addingPaymentError.noError) {
        call.respondBadRequest(GetLocalizedErrorMessage(addingPaymentError, LanguageManager.getLanguageTerms(languageCache.get(call))))
        return
    }

    val subscribingError = paymentManager.startSubscription(device, user,"NONE", iap,shouldForceNoFreeTrial, activeSubscriptionBillDate, accountCoupon)
    if (!subscribingError.noError) {
        call.respondBadRequest(GetLocalizedErrorMessage(subscribingError, LanguageManager.getLanguageTerms(languageCache.get(call))))
        return
    }

    call.respondOK()
}

fun GetLocalizedErrorMessage(paymentError: PaymentManager.PaymentError, languageTerms: LanguageTerms): String {
    return when (paymentError) {
        PaymentManager.PaymentError.NONE -> ""
        PaymentManager.PaymentError.ISSUE_ADDING_CARD -> languageTerms.paymentErrorIssueAddingCc
        PaymentManager.PaymentError.ISSUE_CHARGING_CARD -> languageTerms.paymentErrorIssueChargingCc
    }
}
