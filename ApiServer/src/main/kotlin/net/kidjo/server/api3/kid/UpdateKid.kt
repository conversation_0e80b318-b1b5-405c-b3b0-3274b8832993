package net.kidjo.server.api3.kid

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.shared.database.kid_update
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.User


suspend fun Api3Controller.kid_update(call: ApplicationCall) {
    val query = call.parameters
    val kidId = query.getDecodedNormalId("kidId", encryptionController)

    if (kidId == User.NO_SERVER_ID) {
        call.respond404("invalid kid")
        return
    }

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }


    val name: String
    if (requestJSON.has("name")) {
        name = requestJSON.optString("name", Kid.NO_NAME)
        if (!Kid.CheckName(name)) {
            call.respondBadParameters()
            return
        }
    } else {
        name = Kid.NO_NAME
    }

    val age: Int
    if (requestJSON.has("age")) {
        age = requestJSON.optInt("age", Kid.NO_AGE)
        if (!Kid.CheckAge(age)) {
            call.respondBadParameters()
            return
        }
    } else {
        age = Kid.NO_AGE
    }

    val updateOk = databaseController.kid_update(kidId,age,name)

    if (updateOk) call.respondOK()
    else call.respondDatabaseIssue()
}
