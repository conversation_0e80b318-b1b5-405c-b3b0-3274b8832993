package net.kidjo.server.security

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCallPipeline
import io.ktor.server.application.call
import io.ktor.server.auth.principal
import io.ktor.server.response.respond
import io.ktor.server.routing.Route

fun Route.requireRole(role: String, build: Route.() -> Unit) {
    intercept(ApplicationCallPipeline.Call) {
        val principal = call.principal<RBACPrincipal>()
        if (principal == null || !principal.hasRole(role)) {
            call.respond(HttpStatusCode.Forbidden, "Unauthorized!")
            return@intercept finish()
        }
    }
    build()
}

fun Route.requireAnyRole(vararg roles: String, build: Route.() -> Unit) {
    intercept(ApplicationCallPipeline.Call) {
        val principal = call.principal<RBACPrincipal>()
        if (principal == null || !principal.hasAnyRole(*roles)) {
            call.respond(HttpStatusCode.Forbidden, "Unauthorized!")
            return@intercept finish()
        }
    }
    build()
}