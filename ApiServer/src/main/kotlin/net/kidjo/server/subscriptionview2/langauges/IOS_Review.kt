package net.kidjo.server.subscriptionview2.langauges

import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.subscriptionview2.SubscriptionTemplateEngine
import net.kidjo.server.subscriptionview2.SubscriptionViewConfig

fun SubscriptionTemplateEngine.languageSetMap_iosReview(map: HashMap<String, Any>, inAppPurchase: InAppPurchase, data: SubscriptionViewConfig) {
    map["title"] = "Designed for kids"
    map["subtitle"] = "Watch anywhere. Cancel anytime."
    map["buttonTitle"] = "Join for ${inAppPurchase.getInAppPriceDisplay(data.countryShortName)} + a ${inAppPurchase.freeTrialInDays} day${if (inAppPurchase.freeTrialInDays > 1) "s" else ""} free trial"
    map["restoreText"] = "Restore my subscription"
    map["iapInfo"] = "This service is an auto-renewable subscription for ${inAppPurchase.getInAppPriceDisplay(data.countryShortName)}.\n" +
            "Payment will be charged to iTunes Account at confirmation of purchase\n" +
            "Subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period\n" +
            "Account will be charged for renewal within 24-hours prior to the end of the current period, and identify the cost of the renewal\n" +
            "Subscriptions may be managed by the user and auto-renewal may be turned off by going to the user's Account Settings after purchase\n" +
            "Any unused portion of a free trial period, if offered, will be forfeited when the user purchases a subscription to that publication, where applicable"
    map["terms"] = "Terms and conditions"
    map["privacy"] = "Privacy policies"
}
