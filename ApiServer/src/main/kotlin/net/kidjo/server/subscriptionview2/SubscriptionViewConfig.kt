package net.kidjo.server.subscriptionview2

import net.kidjo.server.main.PlatformConfig
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.models.IAPRule
import net.kidjo.server.shared.models.Promotion
import org.intellij.lang.annotations.Language


class SubscriptionViewConfig(private val config: Config,
                             val platformConfig: PlatformConfig,
                             val isDebug: Boolean,
                             val storePlatform: Device.StorePlatform,
                             val serverDeviceId: Long) {

    var shouldOutputDebug = false
    @Language("HTML")
    var debugOutput = ""

    var sessionId = ""
    var bootSession = ""
    var subscriptionViews = 0
    var videoViews = 0
    var kidId = 0L
    var videoId = 0L
    var promoId = ""
    var allowFreeTrial = true
    var location = "video_popup"

    var rule: IAPRule? = null
    val ruleIsSet: Boolean
        get() = rule != null

    var promotion: Promotion? = null

    var languageShortName: String = ""
    var countryShortName: String = ""
    var isMainCountry = true
    var isTV = false

    var inAppId: String? = null

    //
    var build = 0
    //asset stuff
    val assetDirRoot: String = config.subscriptionViewAssetRoot
    val imageExtension: String

    init {
        if (isDebug || storePlatform == Device.StorePlatform.IOS) {
            imageExtension = ".jpg"
        } else {
            imageExtension = ".webp"
        }
    }


    fun isIOSReview(): Boolean {
        return storePlatform == Device.StorePlatform.IOS && build == platformConfig.iOSReviewBuild
    }
    fun isBuildNewEnough(): Boolean {
        return if (storePlatform == Device.StorePlatform.IOS) build >= OLDEST_SUPPORTED_IOS_BUILD else build >= OLDEST_SUPPORTED_PLAYSTORE_BUILD
    }
    fun generateJSHTML(): String {
        if (isDebug) return ""
        else if (storePlatform == Device.StorePlatform.IOS) return "function purchase(iapId, freeTrial) { webkit.messageHandlers.kidjo.postMessage({action:'requestSubscription',iap:iapId,freeTrial:freeTrial}); }\n" +
                "function restore(){ webkit.messageHandlers.kidjo.postMessage({action:'requestRestore'}); }\n" +
                "function openURL(url){ webkit.messageHandlers.kidjo.postMessage({action:'requestOpenURLString', url: url}); }\n" +
                "webkit.messageHandlers.kidjo.postMessage({action:'load',label:label,iap:iap,view:view,rid:rid});"
        return "function purchase(iapId, freeTrial) { kidjo.requestSubscription(iap,freeTrial); }\n" +
                "function goToLogin() { kidjo.goToLogin(); }\n" +
                "function openURL(url){ kidjo.requestUrl(url); }\n" +
                "kidjo.load(JSON.stringify({action:'load',label:label,iap:iap,view:view,rid:rid}));"
    }
    fun generateFontsHTML(): String {
        if (isDebug) {
            return "@font-face {\n" +
                    "    font-family: BreeRegular;\n" +
                    "    src: url(\"/v2.0/subscription/assets/fonts/BreeRegular.otf\") format(\"opentype\");\n" +
                    "}\n" +
                    "@font-face {\n" +
                    "    font-family: BreeBold;\n" +
                    "    src: url(\"/v2.0/subscription/assets/fonts/BreeBold.otf\") format(\"opentype\");\n" +
                    "}"
        } else if (storePlatform == Device.StorePlatform.IOS) {
            return ""
        } else {
            return "@font-face {\n" +
                    "    font-family: BreeRegular;\n" +
                    "    src: url(\"/v2.0/subscription/assets/fonts/BreeRegular.otf\") format(\"opentype\");\n" +
                    "}\n" +
                    "@font-face {\n" +
                    "    font-family: BreeBold;\n" +
                    "    src: url(\"/v2.0/subscription/assets/fonts/BreeBold.otf\") format(\"opentype\");\n" +
                    "}"
        }
    }
    companion object {
        const val OLDEST_SUPPORTED_IOS_BUILD = 84

        const val OLDEST_SUPPORTED_PLAYSTORE_BUILD = 103
    }
}
