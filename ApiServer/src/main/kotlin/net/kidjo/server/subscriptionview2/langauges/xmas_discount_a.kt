package net.kidjo.server.subscriptionview2.langauges

import net.kidjo.common.models.Language
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.subscriptionview2.SubscriptionTemplateEngine
import net.kidjo.server.subscriptionview2.SubscriptionViewConfig

fun SubscriptionTemplateEngine.languageSetMap_xmasDiscountA(notDiscountedPrice: String, map: HashMap<String, Any>, inAppPurchase: InAppPurchase, data: SubscriptionViewConfig) {
    when (data.languageShortName) {
        Language.SHORT_FRENCH -> {
            val euroNotDiscountedPrice = notDiscountedPrice.replace(".",".")
            map["title"] = "<span class='pink'>Offre de fin d'année ! </span> <span class='striked'>€$euroNotDiscountedPrice/an</span><br/><span class='actual-price'>€${inAppPurchase.price}/an</span>"
            map["subtitle"] = "Tous les héros préférés de vos enfants avec ou sans connexion Internet !"
            map["buttonTitle"] = "Rejoignez Kidjo aujourd'hui"
            map["freeTrialText"] = if (inAppPurchase.noFreeTrial) "Aucun engagement de durée" else "${inAppPurchase.freeTrialInDays} jours gratuits - Aucun engagement de durée"
            map["restoreText"] = "Restaurer mon abonnement"
        }
        Language.SHORT_SPANISH -> {
            map["title"] = "<span class='pink'>¡Especial de Navidad! </span> <span class='striked'>$$notDiscountedPrice/año</span><br/> $${inAppPurchase.price}/año"
            map["subtitle"] = "Aprovado por mamás. Cancele en cualquier momento."
            map["buttonTitle"] = "Únete a Kidjo hoy"
            map["freeTrialText"] = if (inAppPurchase.noFreeTrial) "" else "Suscríbase gratis durante ${inAppPurchase.freeTrialInDays} días"
            map["restoreText"] = "Restaurar mi subscripción"
        }
        Language.SHORT_PORTUGUESE -> {
            map["title"] = "<span class='pink'>Holiday Special!</span> <span class='striked'>$$notDiscountedPrice/year</span><br/><span class='actual-price'>$${inAppPurchase.price}/year</span>"
            map["subtitle"] = "Your kids' favorite shows available everywhere, even offline!"
            map["buttonTitle"] = "Join Kidjo Today"
            map["freeTrialText"] = if (inAppPurchase.noFreeTrial) "Cancel anytime" else "Free for ${inAppPurchase.freeTrialInDays} days, cancel anytime"
            map["restoreText"] = "Restore my subscription"
        }
        else /*Language.SHORT_ENGLISH*/ -> {
            map["title"] = "<span class='pink'>Holiday Special!</span> <span class='striked'>$$notDiscountedPrice/year</span><br/><span class='actual-price'>$${inAppPurchase.price}/year</span>"
            map["subtitle"] = "Your kids' favorite shows available everywhere, even offline!"
            map["buttonTitle"] = "Join Kidjo Today"
            map["freeTrialText"] = if (inAppPurchase.noFreeTrial) "Cancel anytime" else "Free for ${inAppPurchase.freeTrialInDays} days, cancel anytime"
            map["restoreText"] = "Restore my subscription"
        }
    }
}