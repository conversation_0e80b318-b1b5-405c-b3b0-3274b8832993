package net.kidjo.server.subscriptionview2.langauges

import net.kidjo.common.models.Language
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.Promotion
import net.kidjo.server.subscriptionview2.SubscriptionTemplateEngine
import net.kidjo.server.subscriptionview2.SubscriptionViewConfig


//only FR and EN are supported for promotions
fun SubscriptionTemplateEngine.languageSetMap_promotion(map: HashMap<String, Any>, inAppPurchase: InAppPurchase, promotion: Promotion, data: SubscriptionViewConfig) {
    if (promotion.deepLinkId == "Mfreeze" && data.languageShortName == Language.SHORT_FRENCH) {
        promotionText_mFreeze(map,inAppPurchase,promotion,data)
        return
    }

    when (data.languageShortName) {
        Language.SHORT_FRENCH -> {
            if (promotion.price > 0) {
                val pricePerText: String
                if (promotion.pricePer == "year") pricePerText = "/an"
                else pricePerText = "/mo"

                map["title"] = "<span class='pink'>Offre Spéciale ${promotion.title}</span> <span class='striked'>${promotion.oldPrice}<span class='euro'> €</span>$pricePerText</span><br/>${promotion.price}<span class='euro'> €</span>$pricePerText"
            } else
                map["title"] = "Offre Spéciale ${promotion.title}"

            map["subtitle"] = "Tous leurs héros préférés, partout, sans pub et en illimité !"
            if (inAppPurchase.noFreeTrial)
                map["buttonTitle"] = "Rejoignez Kidjo !"
            else
                map["buttonTitle"] = "Rejoignez Kidjo, ${inAppPurchase.freeTrialInDays} jours gratuits"

            map["freeTrialText"] = ""
            map["restoreText"] = "Restaurer mon abonnement"
        }
        else /*Language.SHORT_ENGLISH*/ -> {
            if (promotion.price > 0) {
                val pricePerText: String
                if (promotion.pricePer == "year") pricePerText = "/year"
                else pricePerText = "/mo"

                map["title"] = "<span class='pink'>Special offer</span> <span class='striked'>${promotion.oldPrice}$pricePerText</span><br/>${promotion.price}$pricePerText"
            } else
                map["title"] = "Special offer,"

            map["subtitle"] = "Kidjo : design for Kids, approved by Mums!"
            map["buttonTitle"] = if (inAppPurchase.freeTrialInDays == 0) "Join Kidjo Today" else "Join free for ${inAppPurchase.freeTrialInDays} days"

            map["freeTrialText"] = ""
            map["restoreText"] = "Restore my subscription"
        }
    }
}


private fun promotionText_mFreeze(map: HashMap<String, Any>, inAppPurchase: InAppPurchase, promotion: Promotion, data: SubscriptionViewConfig) {
    map["title"] = "<span class='pink'>Offre spéciale M Freeze</span> - 3 MOIS GRATUITS*"
    map["subtitle"] = "Tous leurs héros préférés, de partout, sans pub et en illimité !"
    map["buttonTitle"] = "Rejoignez Kidjo, 3 mois gratuits"
    map["freeTrialText"] = "* après 29,99€/an annulable à tout moment durant la période de gratuité"
    map["restoreText"] = "Restaurer mon abonnement"
}