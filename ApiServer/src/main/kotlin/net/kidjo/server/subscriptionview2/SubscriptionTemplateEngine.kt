package net.kidjo.server.subscriptionview2

import com.samskivert.mustache.Mustache
import net.kidjo.common.models.Language
import net.kidjo.server.main.PlatformConfig
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.HotReloadTemplate
import net.kidjo.server.shared.tools.IAPManager
import net.kidjo.server.shared.tools.RulesManager
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.models.IAPRule
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.subscriptionview2.langauges.*
import java.io.StringWriter
import java.io.Writer

class SubscriptionTemplateEngine(private val config: Config,
                                 private val platformConfig: PlatformConfig,
                                 private val rulesManager: RulesManager,
                                 private val iapManager: IAPManager
) {

    private val compiler = Mustache.compiler()

    private val template_header = HotReloadTemplate("templates/subscription/header.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)
    private val template_view1 = HotReloadTemplate("templates/subscription/view_1.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)
    private val template_view2 = HotReloadTemplate("templates/subscription/view_2.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)
    private val template_viewDiscountA = HotReloadTemplate("templates/subscription/view_discount_a.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)
    private val template_iosReview = HotReloadTemplate("templates/subscription/ios_review.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)
    private val template_promotion = HotReloadTemplate("templates/subscription/promotion.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)

    private val templateViewNew = HotReloadTemplate("templates/subscription/view_new.mustache",config.websiteHotReloadDir,config.websiteHotReload,compiler)

    private fun attachHeader(iapRule: IAPRule, data: SubscriptionViewConfig, writer: Writer){
        val headerMap = HashMap<String, String>()
        headerMap["cssUrl"] = "${data.assetDirRoot}css/${IAPRule.ViewId.GetCSSName(iapRule.viewId)}.css?v=${platformConfig.subscriptionViewVersionId}"
        headerMap["fonts"] = data.generateFontsHTML()
        headerMap["javascript"] = "var label='${iapRule.label}';var iap='${iapRule.iapKey}';var view='${iapRule.viewId.raw}';var rid='${iapRule.groupId}';" + data.generateJSHTML()


        headerMap["landscapeBackground"] = "${data.assetDirRoot}images/landscape-${data.languageShortName + data.imageExtension}"
        headerMap["backgroundPortrait"] = "${data.assetDirRoot}images/portrait-${data.languageShortName + data.imageExtension}"

        if (!data.isMainCountry) headerMap["landscapeBackground2"] = "${data.assetDirRoot}images/background2_row${data.imageExtension}"
        else if (data.languageShortName.toLowerCase() == "fr") headerMap["landscapeBackground2"] = "${data.assetDirRoot}images/background2_fr${data.imageExtension}"
        else headerMap["landscapeBackground2"] = "${data.assetDirRoot}images/background2_us${data.imageExtension}"

        template_header.execute(headerMap,writer)
    }
    private fun createDefaultMap(inAppPurchase: InAppPurchase, rule: IAPRule, data: SubscriptionViewConfig): HashMap<String, Any> {
        val map = HashMap<String, Any>()
        map["assetRoot"] = data.assetDirRoot
        map["showRestore"] = data.storePlatform == Device.StorePlatform.IOS
        map["showAccount"] = data.isTV && data.storePlatform == Device.StorePlatform.PLAYSTORE
        map["iapId"] = inAppPurchase.id
        map["freeTrialInDays"] = inAppPurchase.freeTrialInDays
        val currency = if (inAppPurchase.store == Device.StorePlatform.SWISSCOM) " CHF" else ""
        if (inAppPurchase.store == Device.StorePlatform.SWISSCOM) map["price"] = "${inAppPurchase.price.format(2)}$currency"
        return map
    }
    private fun attachBody(inAppPurchase: InAppPurchase, rule: IAPRule, data: SubscriptionViewConfig, writer: Writer) {
        val map = createDefaultMap(inAppPurchase, rule, data)
        when(rule.viewId) {
            IAPRule.ViewId.VIEW_1 -> {
                languageSetMap_view1(map, inAppPurchase,data)
                template_view1.execute(map,writer)
            }
            IAPRule.ViewId.VIEW_2 -> {
                val languageTerms = LanguageManager.getLanguageTerms(Language.fromShortName(data.languageShortName) ?: Language.fromId(config.defaultLanguageId))
                val defaultLanguageTerms = LanguageManager.getDefaultLanguageTerms()
                map["title"] = languageTerms.subscriptionViewTitle.ifBlank { defaultLanguageTerms.subscriptionViewTitle }
                map["subtitle"] = languageTerms.subscriptionViewSubtitle.ifBlank { defaultLanguageTerms.subscriptionViewSubtitle }
                map["buttonTitle"] = languageTerms.subscriptionViewButtonText.ifBlank { defaultLanguageTerms.subscriptionViewButtonText }
                map["restoreText"] = languageTerms.subscriptionViewRestore.ifBlank { defaultLanguageTerms.subscriptionViewRestore }
                map["alreadyHaveAccountText"] = languageTerms.subscriptionViewAccountLogin.ifBlank { defaultLanguageTerms.subscriptionViewAccountLogin }
                template_view2.execute(map,writer)
            }
            IAPRule.ViewId.VIEW_DISCOUNT_A_5 -> {
                languageSetMap_viewDiscountA("4.99",map, inAppPurchase,data)
                template_viewDiscountA.execute(map,writer)
            }
            IAPRule.ViewId.XMAS_DISCOUNT_A_50 -> {
                languageSetMap_xmasDiscountA("59.99",map, inAppPurchase,data)
                template_viewDiscountA.execute(map,writer)
            }
            IAPRule.ViewId.IOS_REVIEW -> {
                languageSetMap_iosReview(map, inAppPurchase,data)
                template_iosReview.execute(map,writer)
            }
            IAPRule.ViewId.PROMOTION -> {
                val promotion = data.promotion
                if (promotion == null) { //default back to generic view
                    languageSetMap_view1(map, inAppPurchase,data)
                    template_view1.execute(map,writer)
                } else {
                    languageSetMap_promotion(map,inAppPurchase,promotion,data)
                    template_promotion.execute(map,writer)
                }
            }
            IAPRule.ViewId.VIEW_NEW -> {
                languageSetMapViewNew(map, inAppPurchase, config, data)
                templateViewNew.execute(map, writer)
            }
        }
    }

    fun generateHtml(data: SubscriptionViewConfig): String {
        val rule = data.rule ?: rulesManager.getGenericRule(data.storePlatform,data.build,data.allowFreeTrial,data.isMainCountry)
        val iap = iapManager.getIAPWithDefaults(data.storePlatform,rule.iapKey)
        val writer = StringWriter()
        attachHeader(rule,data,writer)
        attachBody(iap,rule,data,writer)
        return writer.toString()
    }

    fun Double.format(digits: Int): String = String.format("%.${digits}f", this)

}
