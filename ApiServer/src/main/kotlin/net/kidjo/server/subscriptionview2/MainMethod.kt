package net.kidjo.server.subscriptionview2

import io.ktor.server.application.ApplicationCall
import io.ktor.http.ContentType
import io.ktor.server.response.respondText
import net.kidjo.common.models.SubscriptionEvent
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.database.promotion_getByDeepLinkId
import net.kidjo.server.shared.database.rules_getList
import net.kidjo.server.shared.database.wv_analyticsEvent
import net.kidjo.server.shared.database.wv_analyticsOpen
import java.util.*


suspend fun SubscriptionView2Controller.mainMethod(call: ApplicationCall) {
    // First get data from call
    val data = getViewConfig(call) //get the configuration

    //data sets some information automatically
    //if its IOS review, DEBUG with rules, Amazon, or Samsung
    //otherwise we need to find this information

    //Promo
    //=============
    // PROMOTIONS
    //=============
    if (!data.ruleIsSet && data.promoId != "" && (data.languageShortName == "en" || data.languageShortName == "fr")) {
        val promotion = databaseController.promotion_getByDeepLinkId(data.promoId)
        if (promotion != null) {
            val requiredBuild = if (data.storePlatform == Device.StorePlatform.IOS) promotion.ios_oldestBuild else promotion.playstore_oldestBuild

            if (data.build >= requiredBuild) {
                data.promotion = promotion
                data.rule = ruleManager.getPromoRule(data.storePlatform, promotion)
            }
        }
    }

    //=============
    // RULE BASED IAP FINDING SYSTEM
    // BUILD IS NEW ENOUGH
    //=============
    if (!data.ruleIsSet && data.isBuildNewEnough()) {
        if (!data.allowFreeTrial) {

        } else {
            val rules = databaseController.rules_getList(data.storePlatform,data.isMainCountry)
            val chanceValue = Random().nextInt(100) + 1

            if (data.shouldOutputDebug) {
                data.debugOutput += "<div> <h1> DEBUG OUTPUT: </h1> <h3>Got chance value of: $chanceValue</h3> <p>List of rules:</p>"
            }

            for (rule in rules) {
                if (!rule.isDefault) {

                    if (data.shouldOutputDebug) {
                        data.debugOutput += "<p>${rule.chance} - ${rule.label}</p>"
                    }
                    if (rule.chance >= chanceValue) {
                        data.rule = rule
                        break
                    }
                }
            }

            if (data.rule == null) {
                for (rule in rules) {
                    if (rule.isDefault && rule.allowsFreeTrial == data.allowFreeTrial) {
                        data.rule = rule
                        if (data.shouldOutputDebug) {
                            data.debugOutput += "<p>Falling back to default rule: ${rule.label} </p>"
                        }
                        break
                    }
                }
            }


            if (data.shouldOutputDebug) {
                data.debugOutput += "</div>"
            }
        }
    }

    //set CSS
    val html: String

    if (data.shouldOutputDebug) {
        html = data.debugOutput
    } else {
        html = templateEngine.generateHtml(data)
    }



    if (!(config.subscriptionViewDebug || data.isDebug)) {
        val rule = data.rule ?: ruleManager.getGenericRule(data.storePlatform,data.build,data.allowFreeTrial,data.isMainCountry)
        databaseController.wv_analyticsOpen(data.serverDeviceId,data.build,data.videoId,data.kidId,data.videoViews,
                data.subscriptionViews,data.location,data.storePlatform.raw,data.countryShortName,data.languageShortName,
                rule.groupId,rule.label,rule.iapKey,rule.viewId.raw,data.sessionId,data.bootSession)
        databaseController.wv_analyticsEvent(data.sessionId, SubscriptionEvent.Type.OPEN,System.currentTimeMillis())
    }

    return call.respondText(html,ContentType.Text.Html)
}
