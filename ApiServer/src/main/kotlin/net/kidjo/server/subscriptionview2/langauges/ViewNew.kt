package net.kidjo.server.subscriptionview2.langauges

import net.kidjo.common.models.Language
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.Promotion
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.subscriptionview2.SubscriptionTemplateEngine
import net.kidjo.server.subscriptionview2.SubscriptionViewConfig

fun SubscriptionTemplateEngine.languageSetMapViewNew(map: HashMap<String, Any>, inAppPurchase: InAppPurchase, config: Config, data: SubscriptionViewConfig) {
    val languageTerms = LanguageManager.getLanguageTerms(Language.fromShortName(data.languageShortName)
            ?: Language.fromId(config.defaultLanguageId))
    val defaultLanguageTerms = LanguageManager.getDefaultLanguageTerms()
    map["fact1"] = languageTerms.subscriptionViewNewFact1.ifBlank { defaultLanguageTerms.subscriptionViewNewFact1 }
    val fact2 = languageTerms.subscriptionViewNewFact2.ifBlank { defaultLanguageTerms.subscriptionViewNewFact2 }
    map["button"] = languageTerms.subscriptionViewNewButton.ifBlank { defaultLanguageTerms.subscriptionViewNewButton }
    map["buttonRestore"] = languageTerms.subscriptionViewNewButtonRestore.ifBlank { defaultLanguageTerms.subscriptionViewNewButtonRestore }
    map["terms"] = languageTerms.subscriptionViewNewTerms.ifBlank { defaultLanguageTerms.subscriptionViewNewTerms }
    map["privacy"] = languageTerms.subscriptionViewNewPrivacy.ifBlank { defaultLanguageTerms.subscriptionViewNewPrivacy }

    val count = inAppPurchase.getDurationCount()
    val type = inAppPurchase.getDurationType(languageTerms, count)
    val price = inAppPurchase.getInAppPriceDisplay(data.countryShortName, languageTerms)
    map["hasDescription"] = inAppPurchase.store == Device.StorePlatform.IOS
    map["hasPromotion"] = data.promotion != null
    map["hasPromotionClassic"] = data.promotion != null && data.promotion?.oldPrice != null
    if (data.promotion != null) {
        if (data.promotion?.oldPrice == null) {
            // Free trial case
            val freeTrial = data.promotion?.getFreeTrial(languageTerms) ?: "none"
            val promotion = languageTerms.format(languageTerms.subscriptionViewNewOfferFree, freeTrial, price, defaultTerm = defaultLanguageTerms.subscriptionViewNewOfferFree)
            map["fact2"] = "$promotion - $fact2"
        } else {
            // Price offer case
            val newPrice = Promotion.formatPrice(data.promotion?.price, data.countryShortName) ?: "none"
            val newPricePer = "$newPrice/${data.promotion?.getPricePer(languageTerms)}"
            val oldPrice = Promotion.formatPrice(data.promotion?.oldPrice, data.countryShortName) ?: "none"
            val oldPricePer = "$oldPrice/${data.promotion?.getPricePer(languageTerms)}"
            val promotion = languageTerms.format(languageTerms.subscriptionViewNewOffer, newPricePer, oldPricePer, defaultTerm = defaultLanguageTerms.subscriptionViewNewOffer)
            map["oldPrice"] = oldPricePer
            map["fact2"] = "$promotion - $fact2"
        }
        val description = languageTerms.format(languageTerms.subscriptionViewNewDescription, count.toString(), type, price, defaultTerm = defaultLanguageTerms.subscriptionViewNewDescription)
        map["description"] = description
    } else {
        val freeTrial = inAppPurchase.getFreeTrial(languageTerms)
        val promo = freeTrial?.let { languageTerms.format(languageTerms.subscriptionViewNewFact3, freeTrial, price, defaultTerm = defaultLanguageTerms.subscriptionViewNewFact3) }
        val description = freeTrial?.let { languageTerms.format(languageTerms.subscriptionViewNewDescription, freeTrial, "", price, defaultTerm = defaultLanguageTerms.subscriptionViewNewDescription) }
        map["description"] = description ?: ""
        map["fact2"] = if (promo != null) "$promo - $fact2" else fact2
    }
}
