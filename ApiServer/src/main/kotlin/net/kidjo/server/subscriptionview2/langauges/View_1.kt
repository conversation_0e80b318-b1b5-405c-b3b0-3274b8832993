package net.kidjo.server.subscriptionview2.langauges

import net.kidjo.common.models.Language
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.subscriptionview2.SubscriptionTemplateEngine
import net.kidjo.server.subscriptionview2.SubscriptionViewConfig

fun SubscriptionTemplateEngine.languageSetMap_view1(map: HashMap<String, Any>, inAppPurchase: InAppPurchase, data: SubscriptionViewConfig) {
    when (data.languageShortName) {
        Language.SHORT_FRENCH -> {
            map["title"] = "Tous leurs héros."
            map["subtitle"] = "Partout, sans engagement de durée."
            map["buttonTitle"] = if (inAppPurchase.freeTrialInDays == 0) "Rejoignez Kidjo aujourd'hui" else "Rejoignez Kidjo, ${inAppPurchase.freeTrialInDays} jours gratuits"
            map["restoreText"] = "Restaurer mon abonnement"
        }
        Language.SHORT_SPANISH -> {
            map["title"] = "Diseñado para niños"
            map["subtitle"] = "Aprovado por mamás. Cancele en cualquier momento."
            map["buttonTitle"] = if (inAppPurchase.freeTrialInDays == 0) "Únete a Kidjo hoy" else "Suscríbase gratis durante ${inAppPurchase.freeTrialInDays} días"
            map["restoreText"] = "Restaurar mi subscripción"
        }
        Language.SHORT_PORTUGUESE -> {
            map["title"] = "Designed for kids"
            map["subtitle"] = "Watch anywhere. Cancel anytime."
            map["buttonTitle"] = if (inAppPurchase.freeTrialInDays == 0) "Join Kidjo Today" else "Join free for ${inAppPurchase.freeTrialInDays} days"
            map["restoreText"] = "Restore my subscription"
        }
        else /*Language.SHORT_ENGLISH*/ -> {
            map["title"] = "Designed for kids"
            map["subtitle"] = "Watch anywhere. Cancel anytime."
            map["buttonTitle"] = if (inAppPurchase.freeTrialInDays == 0) "Join Kidjo Today" else "Join free for ${inAppPurchase.freeTrialInDays} days"
            map["restoreText"] = "Restore my subscription"
        }
    }
}