package net.kidjo.server.subscriptionview2

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.database.rules_getById
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Video

suspend fun SubscriptionView2Controller.getViewConfig(call: ApplicationCall): SubscriptionViewConfig {
    val query = call.parameters

    // TODO Comment this for iOS Review
      val isDebug = query.getString("debug") == config.subscriptionViewDebugCode

    // TODO Uncomment this for iOS Review
//    val isDebug = true

    val storeType = Device.StorePlatform.fromRow(query.getString("store"))

    val deviceId = query.getDecodedNormalId("deviceId",encryptionController)
    val kidId = query.getDecodedNormalId("kidId",encryptionController)

    val videoIdString = query.getString("videoId","NONE")
    val videoId = if (videoIdString != "NONE") encryptionController.decodeVideoId(videoIdString) else Video.NO_SERVER_ID

    val location = query.getString("location", "video_popup")

    val allowFreeTrial = !(query.getString("noFreeTrial","false") == "true")
    val promo = query.getString("promo")

    val subscriptionViews = query.getInt("sv")
    val videoViews = query.getInt("viewed")
    val sessionId = query.getString("sessionId").shortenTo(32)
    val bootSession = query.getString("bs").shortenTo(32)
    val isTV: Boolean = query.getString("isTV", "false") == "true"

    val build: Int
    val languageShort: String
    val countryShort: String

    val inAppId: String? = if (query.getString("inAppId").isNotBlank()) query.getString("inAppId") else null

    val data = SubscriptionViewConfig(config, platformConfig, isDebug, storeType, deviceId)

    if (data.isDebug) {

        if (query.contains("iosReview")) {
            build = platformConfig.iOSReviewBuild
        } else {

            // TODO Uncomment this for iOS Review
//            build = platformConfig.iOSReviewBuild

            // TODO Comment this for iOS Review
             build = if (storeType == Device.StorePlatform.IOS) SubscriptionViewConfig.OLDEST_SUPPORTED_IOS_BUILD else SubscriptionViewConfig.OLDEST_SUPPORTED_PLAYSTORE_BUILD
        }

        languageShort = query.getString("l","en")
        countryShort = query.getString("c","us")

        if (query.contains("ruleId")) {
            val ruleId = query.getLong("ruleId")
            val rule = databaseController.rules_getById(ruleId)
            data.rule = rule
        } else if (query.contains("displayDebug")) {
            data.shouldOutputDebug = true
        }
    } else {
        build = query.getInt("buildNumber")

        val deviceLocaleStringe = query.getString("deviceLocale", "en_US")
        val locale = validator.createLocale(deviceLocaleStringe)
        languageShort = locale.language
        countryShort = locale.country
    }


    data.kidId = kidId
    data.videoId = videoId

    data.location = location

    data.allowFreeTrial = allowFreeTrial
    data.promoId = promo

    data.subscriptionViews = subscriptionViews
    data.videoViews = videoViews
    data.sessionId = sessionId
    data.build = build
    data.languageShortName = languageShort
    data.countryShortName = countryShort
    data.isMainCountry = validator.isCountryMainCountry(countryShort)
    data.isTV = isTV

    data.inAppId = inAppId

    data.sessionId = sessionId
    data.bootSession = bootSession
    data.subscriptionViews = subscriptionViews

    if (!data.ruleIsSet) {
        if (data.isIOSReview()) data.rule = ruleManager.getIOSReviewRule(data.inAppId)
        else if (data.storePlatform == Device.StorePlatform.SAMSUNG) data.rule = ruleManager.getDefaultSamsungRule(data.isMainCountry)
        else if (data.storePlatform == Device.StorePlatform.AMAZON) data.rule = ruleManager.getDefaultAmazonRule(data.allowFreeTrial)
    }

    return data
}
