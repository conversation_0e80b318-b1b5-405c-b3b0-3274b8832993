package net.kidjo.server.subscriptionview2

import io.ktor.server.application.call
import io.ktor.server.http.content.resources
import io.ktor.server.http.content.static
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector

class SubscriptionView2Controller(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val templateEngine = SubscriptionTemplateEngine(config, platformConfig, ruleManager, iapManager)

    override fun installRoutes(route: Route) {
        route.get("subview2") { <EMAIL>(call) }

        route.static("subscription/assets") {
            resources("assets/subscription")
        }
    }
}
