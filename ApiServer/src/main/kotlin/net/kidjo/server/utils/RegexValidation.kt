package net.kidjo.server.utils

fun validateMobileNumber(mobileNumber: String): <PERSON><PERSON><PERSON> {
    val regex = """^\+\d{12}$""".toRegex()
    return regex.matches(mobileNumber)
}

fun emailValidation(email:String):<PERSON><PERSON><PERSON>{
    val regex= Regex(
        "^(([\\w-]+\\.)+[\\w-]+|([a-zA-Z]|[\\w-]{2,}))@"
                + "((([0-1]?[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\.([0-1]?"
                + "[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\."
                + "([0-1]?[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\.([0-1]?"
                + "[0-9]{1,2}|25[0-5]|2[0-4][0-9]))|"
                + "([a-zA-Z0-9]+[\\w-]+\\.)+[a-zA-Z]{2,4})$"
    )
    return regex.matches(email)
}

fun removeSpecialCharacters(input: String): String {
    // Define a regular expression pattern to match special characters
    val regex = Regex("[^A-Za-z0-9 ]")

    // Use the replace function to replace special characters with an empty string
    return input.replace(regex, "")
}
