package net.kidjo.server.utils

import io.ktor.server.plugins.BadRequestException
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.models.PartnerSubscriptionResponse
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.generatePassword
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.sha256
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update


fun getUserByUserId(userId: Int): User? {
    return transaction {
        Users.select { Users.id eq userId.toLong() }
            .mapNotNull {
                User(
                    it[Users.id].toString(),
                    it[Users.email],
                    it[Users.emailIsConfimed],
                    it[Users.name],
                    it[Users.countryId],
                    "",
                    it[Users.password]
                )
            }
            .singleOrNull()
    }
}

fun getUserIdByUserEmail(email: String): Long {
    var userId = 0L
    transaction {
        Users.select { Users.email eq email }.mapNotNull { row -> userId = row[Users.id] }
            .singleOrNull()
    }
    return userId
}

fun updateUserPassword(
    email: String, encryptionController: EncryptionController, config: Config,
    oldPassword: String, newPassword: String, databaseController: DatabaseController
): Int {
    var success = 0
    val user = databaseController.user_getByEmail(email)
    val preHashedPassword = sha256(oldPassword + config.clientSideHashV1Salt)

    val passwordMatch = encryptionController.checkPassword(preHashedPassword, user?.hashedPassword.toString())

    if (!passwordMatch) {
        throw BadRequestException("Old password is incorrect")
    }
    val (password, hashedPassword) = generatePassword(
        randomString = newPassword, encryptionController = encryptionController, salt = config.clientSideHashV1Salt
    )
    transaction {

        success = Users.update({ Users.email eq email }) {
            it[Users.password] = hashedPassword
        }
    }
    if (success ==0) {
        throw BadRequestException("Unable to reset password")
    }
    return success
}
