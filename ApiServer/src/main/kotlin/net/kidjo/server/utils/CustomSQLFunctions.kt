package net.kidjo.server.utils

import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.QueryBuilder

fun customGroupConcatBool(column: ExpressionAlias<Boolean>): Expression<String> {
    return object : Expression<String>() {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder.append("GROUP_CONCAT( $column )")
        }
    }
}
fun customGroupConcat(column: ExpressionAlias<String>): Expression<String> {
    return object : Expression<String>() {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder.append("GROUP_CONCAT( $column )")
        }
    }
}