package net.kidjo.server.utils

import net.kidjo.server.shared.models.Partner
import net.kidjo.server.shared.models.entity.PartnerAccess
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import java.security.MessageDigest


fun getPartner(apiKey: String): Partner? {
    return transaction {
        PartnerAccess.select {
            PartnerAccess.apiKey eq apiKey and
                    PartnerAccess.isKeyExpired.eq(false) and
                    PartnerAccess.isActive.eq(true)

        }
            .mapNotNull { Partner(it[PartnerAccess.id], it[PartnerAccess.partnerName]) }
            .singleOrNull()
    }
}

fun getAdditional1(partnerName: String): String? {
    return transaction {
        PartnerAccess.select {
            PartnerAccess.partnerName.lowerCase() eq partnerName.lowercase() and
                    PartnerAccess.isActive.eq(true)
        }
            .mapNotNull { it[PartnerAccess.additional1] }
            .singleOrNull()
    }
}

fun validateUserCredentials(username: String, password: String): Partner? {
    return transaction {

        PartnerAccess.select {
            (PartnerAccess.username eq username) and
                    (PartnerAccess.password eq hashPassword(password)) and
                    PartnerAccess.isKeyExpired.eq(false) and
                    PartnerAccess.isActive.eq(true)
        }.mapNotNull { Partner(it[PartnerAccess.id], it[PartnerAccess.partnerName]) }
            .singleOrNull()
    }
}

fun hashPassword(password: String): String {
    val messageDigest = MessageDigest.getInstance("SHA-256")
    val bytes = messageDigest.digest(password.toByteArray())
    return bytes.joinToString("") { "%02x".format(it) }
}

