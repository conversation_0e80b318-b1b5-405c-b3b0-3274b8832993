package net.kidjo.server.utils

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.acceptLanguage
import io.ktor.server.response.*
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.shared.json.v3.internal_data_toJson
import net.kidjo.server.shared.models.InternalData
import net.kidjo.server.shared.tools.RequestUtil


suspend fun Api3CardsController.getInternalData(call: ApplicationCall) {

    val data = InternalData(
        countryId = RequestUtil.getCountryFromHeader(databaseController, call.request.acceptLanguage() ?: "us") ?: 284,
        languageId = languageCache.getLanguageIdFromAcceptLanguageHeader(call.request.acceptLanguage() ?: "us"),
        // Add more pairs as needed
    )
    call.respondText(jsonCreator.internal_data_toJson(data).toString(), ContentType.Application.Json)
}