package net.kidjo.server.utils

import io.ktor.server.plugins.BadRequestException
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.regex.Pattern

fun convertStringToDate(date: String): LocalDateTime {

    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm[:ss]")

    // Parse the string into a LocalDateTime object
    val localDateTime = LocalDateTime.parse(date, formatter)

    return localDateTime
}

fun convertMiliSecondDate(date: String): LocalDateTime {
    val dateWithoutMiliSecond = date.substringBefore(".")
    if (!checkDateFormat(dateWithoutMiliSecond)){
        throw BadRequestException("Invalid date format, pass your respective format")
    }
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm[:ss]")

    // Parse the string into a LocalDateTime object
    val localDateTime = LocalDateTime.parse(dateWithoutMiliSecond, formatter)

    return localDateTime
}

/**
 * V2 of convertMiliSecondDate method that accept null date
 */
fun convertMiliSecondDateV2(date: String?): LocalDateTime {
    val dateWithoutMiliSecond = date!!.substringBefore(".")
    if (!checkDateFormat(dateWithoutMiliSecond)){
        throw BadRequestException("Invalid date format, pass your respective format")
    }
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm[:ss]")

    // Parse the string into a LocalDateTime object
    val localDateTime = LocalDateTime.parse(dateWithoutMiliSecond, formatter)

    return localDateTime
}

fun isFutureOrTodayDate(startDate: LocalDateTime):LocalDateTime {

    val currentDateTime = LocalDateTime.now()

    if(currentDateTime.isAfter(startDate) || currentDateTime.isEqual(startDate)){
        throw BadRequestException("Subscription creation failed due to past date i.e start date")
    }

    return startDate;
}
fun isNextBillingDate(endDate: LocalDateTime,startDate: LocalDateTime):LocalDateTime {

    if(endDate.isBefore(startDate) || endDate.isEqual(startDate)){
        throw BadRequestException("End date can't be the same or before start date")
    }
    return endDate;
}



fun checkDateFormat(dateFormat: String): Boolean {
    val pattern = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$")

    // Use the pattern to match the string
    val matcher = pattern.matcher(dateFormat)

    // Check if the string matches the pattern
    if (matcher.matches()) {
        return true
    }
    return false;
}

fun dateFormatter(date: String): LocalDateTime {
    val timestamp = date.toLong()
    val instant = Instant.ofEpochSecond(timestamp)
    val zoneId = ZoneId.of("Asia/Dubai") // Replace "YourTimeZone" with the desired time zone

    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm[:ss]") // Define the desired date format
    val formattedDate = instant.atZone(zoneId).format(formatter)
    return LocalDateTime.parse(formattedDate, formatter)
}

fun getEndOfDay(date: Calendar): Instant {
    val zonedDateTime: ZonedDateTime = ZonedDateTime.ofInstant(
        date.toInstant(),
        date.timeZone.toZoneId()
    )
        .withHour(23)
        .withMinute(59)
        .withSecond(0)
    return zonedDateTime.toInstant()
}