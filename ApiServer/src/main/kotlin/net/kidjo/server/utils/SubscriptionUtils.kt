package net.kidjo.server.utils

import net.kidjo.server.shared.models.entity.PartnerSubscriptionLogs
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction


fun insertLogs(logs: String, partnerName: String): Boolean {
    return transaction {
        PartnerSubscriptionLogs.insert {
            it[request] = logs
            it[PartnerSubscriptionLogs.partnerName] = partnerName
        }.insertedCount > 0
    }
}

