package net.kidjo.server.api5.extension.subscription

import io.ktor.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.aws.AwsParameterStore
import net.kidjo.server.api5.extension.subscription.mondia.MondiaPushEventType
import net.kidjo.server.api5.extension.subscription.mondia.MondiaPushNotificationClient
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.delete_user_redirect_key
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.entity.SubscriptionRoot
import net.kidjo.server.utils.getAdditional1
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.Logger
import java.time.LocalDateTime
import net.kidjo.server.shared.tools.Config

suspend fun mondiaPushEvent(
    logger: Logger,
    config: Config,
    databaseController: DatabaseController,
    correlationId: String?,
    eventType: MondiaPushEventType,
    packageId: Long,
    url: String,
    userUuid: String,
    key: String,
    userId: Int
) {

    withContext(Dispatchers.IO) {
        val isPushEnabled = getAdditional1("mondia") ?: "N"

        if (isPushEnabled == "Y" && isMondiaUser(userId)) {
            logger.info("[MondiaPushEvent] [$correlationId] Event: $eventType - Pushing event...")
            try {
                val baseUrl =
                    AwsParameterStore.getParameter("/kidjo-server/${config.env.name.toLowerCasePreservingASCIIRules()}/mondia/push/baseurl")
                val authToken =
                    AwsParameterStore.getParameter("/kidjo-server/${config.env.name.toLowerCasePreservingASCIIRules()}/mondia/push/authtoken")
                val partnerCode =
                    AwsParameterStore.getParameter("/kidjo-server/${config.env.name.toLowerCasePreservingASCIIRules()}/mondia/push/partnercode")

                val client = MondiaPushNotificationClient(
                    baseUrl = baseUrl,
                    authorizationToken = authToken,
                    partnerCode = partnerCode
                )

                val finalUrl = if (eventType == MondiaPushEventType.CUSTOMER_ONBOARDING_COMPLETE) "" else url
                val response = client.sendPushNotification(
                    eventType = eventType,
                    datetime = LocalDateTime.now().toString(),
                    tenantId = packageId.toString(),
                    url = finalUrl,
                    user = userUuid,
                    token = key
                )

                if (response.isSuccessful) {
                    logger.info(
                        "[MondiaPushEvent] [$correlationId] Event: $eventType - API call successful: ${
                            response.body()?.string()
                        }"
                    )
                    deleteUserRedirectKey(databaseController, logger, correlationId, userId, eventType)
                } else {
                    logger.error(
                        "[MondiaPushEvent] [$correlationId] Event: $eventType - " +
                                "API call failed with code ${response.code()}: ${response.message()}"
                    )
                }
            } catch (e: Exception) {
                logger.error(
                    "[MondiaPushEvent] [$correlationId] Event: $eventType - " +
                            "Error occurred: ${e.message}"
                )
            }
        } else {
            logger.info(
                "[MondiaPushEvent] [$correlationId] Event: $eventType - " +
                        " User does not belong to Mondia or Mondia Push API is Disabled (You can enable it by setting partner_access.additional1 column to Y)."
            )
        }
    }
}

fun deleteUserRedirectKey(
    databaseController: DatabaseController,
    logger: Logger,
    correlationId: String?,
    userId: Int,
    eventType: MondiaPushEventType
) {
    if (eventType == MondiaPushEventType.CUSTOMER_ONBOARDING_COMPLETE) {
        logger.info("[MondiaPushEvent] [$correlationId] Event: $eventType - Deleting Redirection Key...")
        databaseController.delete_user_redirect_key(userId.toLong())
        logger.info("[MondiaPushEvent] [$correlationId] Event: $eventType - Redirection Key Deleted.")
    }
}

fun isMondiaUser(userId: Int): Boolean {
    return transaction {
        SubscriptionRoot.select {
            (SubscriptionRoot.userId eq userId) and (SubscriptionRoot.storeId eq Device.StorePlatform.MONDIA)
        }.empty().not()
    }
}