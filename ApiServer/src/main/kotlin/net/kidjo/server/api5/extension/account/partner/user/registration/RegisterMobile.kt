package net.kidjo.server.api5.extension.account.partner.user.registration

import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api4.UserDtoOut
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.api5.RegisterMobileDtoIn
import net.kidjo.server.api5.extension.account.partner.user.helpers.createUser
import net.kidjo.server.api5.extension.account.partner.user.helpers.createUserConsent
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.User


suspend fun Api5UserController.registerMobile(call: ApplicationCall) {

    val logPrefix = "[Api5UserController.registerMobile]"

    val registerMobileIn = call.receive<RegisterMobileDtoIn>()

    if (registerMobileIn.email.isNullOrEmpty() || registerMobileIn.password.isNullOrEmpty()) {
        return call.respondBadRequest("Bad Params")
    }

    if (!validator.isEmailValid(registerMobileIn.email)) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
    }

    val currentUser = databaseController.user_getByEmail(registerMobileIn.email)
    if (currentUser != null) {
        return call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, "There is already such a User")
    }
    val user = User.getEmptyUser()

    user.authType = User.AuthType.EMAIL
    user.email = registerMobileIn.email
    registerMobileIn.displayName?.takeIf { it.isNotBlank() }?.let {
        user.name = it
    }

    user.hashedPassword = encryptionController.hashPassword(registerMobileIn.password)
    user.countryId = countryCodeByGoogle.getCountryIdByIP(call)

    val isTcAccepted = if (registerMobileIn.isTcAccepted) 1 else 0
    val isPromoAccepted = if (registerMobileIn.isPromoAccepted) 1 else 0

    user.id = createUser(user, call)

    logger.info("$logPrefix DB User created with ID: ${user.id} ,User: $user.")
    createUserConsent(user.id, isTcAccepted, isPromoAccepted)

    user.authToken = userManager.generateAndSetUpUserAccessToken(user.getLongId())

    return call.respondOK(
        UserDtoOut(
            user.id,
            null,
            false,
            user.authToken
        )
    )

}