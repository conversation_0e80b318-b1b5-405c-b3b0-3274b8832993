package net.kidjo.server.api5.extension.subscription

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.api.client.googleapis.notifications.NotificationUtils
import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api5.extension.subscription.mondia.MondiaPushEventType
import net.kidjo.server.api5.publichers.Api5PartnerSubscriptionController
import net.kidjo.server.models.RedirectionBean
import net.kidjo.server.shared.database.insert_user_redirect_key
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.entity.MondiaPackages
import net.kidjo.server.shared.models.entity.SubscriptionRoot
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.utils.insertLogs
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import java.util.*

suspend fun Api5PartnerSubscriptionController.generateRedirectionUrl(call: ApplicationCall) {

    var correlationId = call.getCorrelationId()
    logger.info(
        "[Api5PartnerSubscriptionController.generateRedirectionUrl] " +
                "Mondia redirection flow START, CorrelationID: $correlationId"
    )
    if (correlationId.isNullOrEmpty()) {
        correlationId = UUID.randomUUID().toString()
        logger.info(
            "[Api5PartnerSubscriptionController.generateRedirectionUrl] " +
                    "Mondia CorrelationID NULL, generating internal ID: $correlationId"
        )
    }

    val logs = call.receive<String>()
    logger.info(
        "[Api5PartnerSubscriptionController.generateRedirectionUrl] [$correlationId] " +
                "ConsentBean Json $logs"
    )
    insertLogs(logs, "MONDIA")
    logger.debug(
        "[Api5PartnerSubscriptionController.generateRedirectionUrl] [$correlationId] " +
                "Logs inserted into DB."
    )
    val objectMapper = ObjectMapper()
    val redirectionBean = objectMapper.readValue(logs, RedirectionBean::class.java)

    if (redirectionBean == null) {
        logger.error(
            "[Api5PartnerSubscriptionController.generateRedirectionUrl] " +
                    "[$correlationId] Failed to find redirectionBean"
        )
        return call.respondNotFound(
            UsersErrors.ERROR_NOT_EXIST_USER,
            "[$correlationId] Problems finding redirectionBean"
        )
    }

    val userId = getUserIdByUserUuid(redirectionBean.userUuid)
    val isSubscribed = isActiveSubscription(redirectionBean)

    logger.info(
        "[Api5PartnerSubscriptionController.generateRedirectionUrl] " +
                "[$correlationId] ${redirectionBean.userUuid} isSubscribed: $isSubscribed"
    )
    if (userId != 0 && isSubscribed) {
        val generatedKey = NotificationUtils.randomUuidString()
        return try {

            val key = databaseController.insert_user_redirect_key(userId.toLong(), generatedKey)
            var mondiaOperator=getOperatorByPackageId(redirectionBean.packageId.toInt())
            var url = "https://${config.redirectionBaseUrl}/products/?key=$key"
            if(mondiaOperator?.lowercase()=="vodacom"){
                url="https://${config.vodacomRedirectionBaseUrl}?key=$key"
                logger.info("[Api5PartnerSubscriptionController.generateRedirectionUrl] mondiaPartner :" +
                        " $mondiaOperator")
            }

            call.respondOK(
                mapOf(
                    "redirectKey" to key,
                    "redirectionUrl" to url
                )
            )

            mondiaPushEvent(
                logger,
                config,
                databaseController,
                correlationId,
                MondiaPushEventType.CUSTOMER_ONBOARDING_INITIAL,
                redirectionBean.productId,
                url,
                redirectionBean.userUuid,
                key,
                userId
            )

        } catch (e: Throwable) {
            logger.error(
                "[Api5PartnerSubscriptionController.generateRedirectionUrl] " +
                        "[$correlationId] Failed to insert redirect key", e
            )
            call.respondBadRequest(
                ServerErrors.ERROR_BAD_PARAMS,
                "[$correlationId] Problems creating redirect key"
            )
        }
    } else {
        return call.respondNotFound(
            UsersErrors.ERROR_NOT_EXIST_USER,
            "[$correlationId] No any subscription found with userUuid " +
                    "${redirectionBean.userUuid}"
        )
    }

}

fun isActiveSubscription(redirectionBean: RedirectionBean): Boolean {

    val subscriptionToken = "${redirectionBean.userUuid}|${redirectionBean.productId}"
    var isSubscribed = false
    transaction {
        SubscriptionRoot.select { SubscriptionRoot.subscriptionToken eq subscriptionToken }
            .mapNotNull { row ->
                isSubscribed = row[SubscriptionRoot.isActive]
            }.singleOrNull()
    }
    return isSubscribed;
}

private fun getUserIdByUserUuid(userUuid: String): Int {
    var userId = 0
    transaction {
        UserPartnerSubscription.slice(UserPartnerSubscription.userId).select { UserPartnerSubscription.userUuid eq userUuid }.mapNotNull { row ->
            userId = row[UserPartnerSubscription.userId]
        }.singleOrNull()
    }
    return userId
}

private fun getOperatorByPackageId(packageId: Int): String? {
    return transaction {
        MondiaPackages
            .slice(MondiaPackages.operator)
            .select { MondiaPackages.mondiaPackageId eq packageId }
            .mapNotNull { it[MondiaPackages.operator] }
            .firstOrNull()
    }
}
