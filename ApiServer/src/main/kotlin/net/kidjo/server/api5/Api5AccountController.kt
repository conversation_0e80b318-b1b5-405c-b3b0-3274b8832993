package net.kidjo.server.api5

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api5.extension.account.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api5AccountController(platformInjector: PlatformInjector) :
    BaseServerController(platformInjector) {

    internal val logger = LoggerFactory.getLogger(Api5AccountController::class.java)

    override fun installRoutes(route: Route) {
        route.get("/plans") { <EMAIL>(call) }
        route.post("/braintree/subscription") { <EMAIL>(call) }
        route.get("/subscriptions"){
            <EMAIL>(call)
        }

        route.get("/users/info") { <EMAIL>(call) }
        route.get("/books/users/info") { <EMAIL>(call) }
        route.get("/games/users/info") { <EMAIL>(call) }


    }
}
