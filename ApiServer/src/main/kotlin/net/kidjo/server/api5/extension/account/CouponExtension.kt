package net.kidjo.server.api5.extension.account

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.api4.extension.subscription.helper.createFreeAccessSubscription
import net.kidjo.server.api5.Api5BackofficeController
import net.kidjo.server.api5.Api5CouponController
import net.kidjo.server.api5.extension.account.request.SubscriptionUpdateData
import net.kidjo.server.shared.database.getUserSubscriptions
import net.kidjo.server.shared.database.subscription_type_getList
import net.kidjo.server.shared.database.v5.getDiscountDetails
import net.kidjo.server.shared.extensions.SubscriptionErrors
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondCreated
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.utils.removeSpecialCharacters
import java.io.File
import java.nio.file.Files
import java.util.*


suspend fun Api5CouponController.validateCoupon(call: ApplicationCall) {
    val coupon = call.parameters["couponCode"]
    val user = call.principal<User>()

    if (coupon.isNullOrEmpty()) {
        return call.respondBadRequest("This 'coupon' parameter is required.")
    }
    val accountCoupon = couponManager.getFromDatabase(coupon)

    if (accountCoupon == null || !accountCoupon.isValid()) {
        return call.respondBadRequest(
            UsersErrors.ERROR_COUPON_ALL_READY_USED,
            "This coupon is invalid or expired."
        )
    }

    if (user != null) {
        // Prevent the user to use same coupon one more time
        if (accountCoupon.couponType == AccountCouponType.UNIQUE_DISCOUNT_COUPON.name) {
            val subscriptionRoot = databaseController.getUserSubscriptions(user.getLongId()).find { sub ->
                sub.accountCouponId == accountCoupon.id
            }
            if (subscriptionRoot != null) {
                return call.respondBadRequest(UsersErrors.ERROR_INVALID_COUPON, "This coupon is already used.")
            }
        }

        if (accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name ||
            accountCoupon.couponType == AccountCouponType.UNIQUE_ACCESS_COUPON.name
        ) {

            val subscriptions = databaseController.subscription_type_getList(user.getLongId())
            subscriptions.forEach { it ->
                if (!utility.isBillingDateExpired(it.nextBillDate) &&
                    it.subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_TV &&
                    accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name
                ) {
                    return call.respond(
                        HttpStatusCode.BadRequest,
                        mapOf(
                            "couponProduct" to SubscriptionRoot.SubscriptionType.KIDJO_TV,
                            "code" to SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_TV.code,
                            "errorMessage" to "This user already has a subscription for KIDJO-TV."
                        )
                    )
                }
                if (!utility.isBillingDateExpired(it.nextBillDate) &&
                    it.subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_BOOKS &&
                    accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name
                ) {
                    return call.respond(
                        HttpStatusCode.BadRequest,
                        mapOf(
                            "couponProduct" to SubscriptionRoot.SubscriptionType.KIDJO_BOOKS,
                            "code" to SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_BOOKS.code,
                            "errorMessage" to "This user already has a subscription for KIDJO-BOOKS."
                        )
                    )
                }
                if (!utility.isBillingDateExpired(it.nextBillDate) &&
                    it.subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS &&
                    accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name
                ) {
                    return call.respond(
                        HttpStatusCode.BadRequest,
                        mapOf(
                            "couponProduct" to SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS,
                            "code" to SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_TV_BOOKS.code,
                            "errorMessage" to "This user already has a subscription for KIDJO-TV AND KIDJO-BOOKS."
                        )
                    )
                }
            }

            api4SubscriptionController.createFreeAccessSubscription(accountCoupon, user.getLongId(), call)
        }
    }

    return call.respondCreated(
        CouponDTO(
            accountCoupon.id,
            accountCoupon.couponId,
            accountCoupon.durationCode,
            accountCoupon.groupId,
            accountCoupon.couponType,
            accountCoupon.productType,
            accountCoupon.partnerType,
            when (accountCoupon.couponType) {
                AccountCouponType.DISCOUNT_COUPON.name, AccountCouponType.UNIQUE_DISCOUNT_COUPON.name ->
                    getDiscountDetails(coupon)

                else -> null
            }
        )
    )
}

suspend fun Api5BackofficeController.updateBrainTreeSubscription(call: ApplicationCall) {
    val contentType = call.request.contentType()
    if (contentType.withoutParameters().match(ContentType.Text.Plain) ||
        contentType.withoutParameters().match(ContentType.parse("text/csv"))
    ) {
        val csvRequestBody = call.receiveText()
        val subscriptionFile = createTempCSVFile(csvRequestBody.toByteArray())

        val csvData = readFile(subscriptionFile)

        if (csvData.isNotEmpty()) {
            val results = brainTreeManager.updateBrainTreeSubscriptions(csvData)
            call.respond(HttpStatusCode.OK, results)
        } else {
            call.respond(HttpStatusCode.BadRequest, "Failed to process CSV file")
        }
    } else {
        call.respond(HttpStatusCode.UnsupportedMediaType, "Unsupported Content Type")
    }
}

suspend fun readFile(file: File): List<SubscriptionUpdateData> = withContext(Dispatchers.IO) {
    val csvLines = Files.readAllLines(file.toPath())
    val csvData = mutableListOf<SubscriptionUpdateData>()

    for ((index, line) in csvLines.withIndex()) {
        if (index == 0) continue
        val columns = line.split(",")
        if (columns.size >= 3) {
            val csvRow = SubscriptionUpdateData(columns[0], removeSpecialCharacters(columns[1]), columns[2])
            csvData.add(csvRow)
        }
    }
    csvData
}

fun createTempCSVFile(bytes: ByteArray): File {
    val tempDir = System.getProperty("java.io.tmpdir")
    val tempFile = File(tempDir, "${UUID.randomUUID()}.csv")

    tempFile.outputStream().use { it.write(bytes) }

    return tempFile
}
