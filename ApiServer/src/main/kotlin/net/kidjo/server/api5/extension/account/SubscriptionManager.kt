package net.kidjo.server.api5.extension.account

import net.kidjo.server.api5.extension.account.request.SubscriptionRequest
import net.kidjo.server.api5.extension.account.request.UnSubscriptionRequest
import net.kidjo.server.api5.extension.account.response.SubscriptionResponse
import net.kidjo.server.shared.models.User

class SubscriptionManager(private val user:User, private val countryId: Int?, private val strategy: SubscriptionStrategy) {

    fun createSubscription(subscriptionRequest: SubscriptionRequest): SubscriptionResponse {
       return strategy.subscribe(user,subscriptionRequest,countryId)
    }

    fun removeSubscription(unSubscriptionRequest: UnSubscriptionRequest){
        strategy.unSubscribe(user,unSubscriptionRequest)
    }
}
