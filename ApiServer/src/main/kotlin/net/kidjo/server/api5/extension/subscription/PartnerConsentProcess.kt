package net.kidjo.server.api5.extension.subscription

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import net.kidjo.server.api5.publichers.Api5PartnerSubscriptionController
import net.kidjo.server.shared.models.Partner
import net.kidjo.server.models.enums.PartnerSubscription
import net.kidjo.server.service.MondiaIntegrationService
import net.kidjo.server.shared.extensions.getCorrelationId
import net.kidjo.server.shared.extensions.respondError
import java.util.*

suspend fun Api5PartnerSubscriptionController.partnerConsentProcess(
    consentOperation: String,
    call: ApplicationCall
) {
    var correlationId = call.getCorrelationId()
    logger.info(
        "[Api5PartnerSubscriptionController.partnerConsentProcess] " +
                "Getting partner CorrelationID: $correlationId"
    )
    if (correlationId.isNullOrEmpty()) {
        correlationId = UUID.randomUUID().toString()
        logger.info(
            "[Api5PartnerSubscriptionController.partnerConsentProcess] " +
                    "Partner CorrelationID NULL, generating internal ID: $correlationId"
        )
    }

    val partner = call.principal<Partner>()
    val partnerName = partner?.name?.uppercase()
    logger.info(
        "[Api5PartnerSubscriptionController.partnerConsentProcess][$correlationId] Receiving new " +
                "$partnerName consent $consentOperation request: $call"
    )

    if (partnerName == PartnerSubscription.MONDIA.toString()) {
        val partnerService = MondiaIntegrationService(
            config, utility, encryptionController,
            emailManager, databaseController,
            languageCache
        )
        partnerService.mondiaConsentProcess(consentOperation, call, correlationId)
    } else {
        logger.warn(
            "[Api5PartnerSubscriptionController.partnerConsentProcess] [$correlationId] " +
                    "No partner exists with this key: $partnerName, Operation: " +
                    "$consentOperation, and request: $call"
        )
        return call.respondError(
            HttpStatusCode.BadRequest,
            "[$correlationId] No partner exists with the key $partnerName"
        )
    }
}