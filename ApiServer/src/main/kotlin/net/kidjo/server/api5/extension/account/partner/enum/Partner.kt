package net.kidjo.server.api5.extension.account.partner.enum

enum class Partner(
    val id: Int,
    val partnerName: String
) {
    ORANGE(
        21,
        "orange"
    ),
    NONE(0,
        "NONE")
    ;

    companion object {
        fun getByName(partnerName: String?): Partner {
            val lower = partnerName?.toLowerCase()
            return when (lower) {
                ORANGE.partnerName -> ORANGE
                else -> NONE
            }
        }
    }
}