package net.kidjo.server.api5.extension.subscription

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.request.receive
import net.kidjo.server.api5.publichers.Api6SubscriptionController
import net.kidjo.server.shared.database.getHuaweiSubscriptionByOrderId
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.respondBadParameters
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Subscription
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.PartnerSubscriptionCountry
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction


suspend fun Api6SubscriptionController.kidjoTvSubscription(
    subscriptionType: SubscriptionRoot.SubscriptionType, call: ApplicationCall
) {

    var userId = 0L

    var subscription = call.receive<Subscription>()
    val authenticatedUser = call.principal<User>()
    val receipt = subscription.receipt
    val iapId = subscription.iapId
    val deviceType = subscription.deviceType
    val sessionId = subscription.sessionId ?: "NONE"
    val forceUpdate = subscription.forceUpdate
    val manufacturer = subscription.deviceInfo.manufacturer
    val type = subscription.deviceInfo.type
    val model = subscription.deviceInfo.model
    val country = subscription.country
    config.isDeviceType=iapId.toString()

    // Only for Huawei
    val subscriptionId = subscription.subscriptionId
    // Only for Huawei
    val orderId = subscription.orderId
    // Only for Amazon
    val amazonUserId = subscription.amazonUserId

    var paymentsStore = Unit

    if (receipt.isNullOrEmpty() || iapId.isNullOrEmpty()) {
        return call.respondBadParameters()
    }

    if (authenticatedUser != null) {
        userId = authenticatedUser.getLongId()
    } else {
        val existingSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(receipt.toString())
        if (existingSubscription != null) {
            userId = existingSubscription.userId
        }
    }

    when (deviceType?.lowercase()) {
        Device.StorePlatform.IOS.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.IOS, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.IOS.raw} with iap: $iapId")
            }

            paymentsStore = paymentAppStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.PLAYSTORE.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.PLAYSTORE, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.PLAYSTORE.raw} with iap: $iapId")

            }

            paymentsStore = paymentPlayStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.SAMSUNG.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.SAMSUNG, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.SAMSUNG.raw} with iap: $iapId")

            }

            paymentsStore = paymentSamsungStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.ORANGE.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.ORANGE, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.ORANGE.raw} with iap: $iapId")
            }

            paymentsStore = paymentOrangeStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.HUAWEI.raw -> {
            if (subscriptionId.isNullOrEmpty()) {
                return call.respondBadRequest("The 'subscriptionId' is required!")

            }

            val iap = iapManager.getIAP(Device.StorePlatform.HUAWEI, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.HUAWEI.raw} with iap: $iapId")

            }

            if (userId == 0L) {
                val huaweiSubscription: SubscriptionRoot? = databaseController.getHuaweiSubscriptionByOrderId(orderId!!)
                if (huaweiSubscription != null) {
                    userId = huaweiSubscription.userId
                }
            }

            paymentsStore = paymentHuaweiStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, subscriptionId, orderId, call
            )
        }

        Device.StorePlatform.AMAZON.raw -> {
            if (amazonUserId.isNullOrEmpty()) {
                return call.respondBadRequest("The 'amazonUserId' is required!")

            }
            val iap = iapManager.getIAP(Device.StorePlatform.AMAZON, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.AMAZON.raw} with iap: $iapId")

            }

            paymentsStore = paymentAmazonStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, amazonUserId, null, call
            )
        }

        Device.StorePlatform.JIO.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.JIO, iapId)
            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.JIO.raw} with iap: $iapId")

            }

            paymentsStore = paymentJioStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }


    }
    if (!type.isNullOrEmpty()) {
        if ("TV".equals(type.uppercase())) {
            paymentPlayStoreManager.insertDeviceInfo(type, manufacturer, model);
        }
    }
    if (paymentsStore != null) {
        if (country.isNotEmpty()) {
            insertPartnerSubscriptionCountry(country)
        }
    }

    return paymentsStore
}

fun insertPartnerSubscriptionCountry(country: String) {
    var subscriptionId = 0
    return transaction {
        net.kidjo.server.shared.models.entity.SubscriptionRoot.selectAll()
            .orderBy(net.kidjo.server.shared.models.entity.SubscriptionRoot.id, SortOrder.DESC).limit(1)
            .mapNotNull { subscriptionId = it[net.kidjo.server.shared.models.entity.SubscriptionRoot.id].toInt() }

        val isExists =
            PartnerSubscriptionCountry.select { PartnerSubscriptionCountry.subscriptionId.eq(subscriptionId) }
                .count() > 0
        if (!isExists) {
            PartnerSubscriptionCountry.insert {
                it[PartnerSubscriptionCountry.subscriptionId] = subscriptionId
                it[PartnerSubscriptionCountry.country] = country
            }
        }

    }
}