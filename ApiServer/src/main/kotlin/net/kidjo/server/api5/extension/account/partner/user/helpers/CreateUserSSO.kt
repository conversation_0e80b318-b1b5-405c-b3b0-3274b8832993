package net.kidjo.server.api5.extension.account.partner.user.helpers

import io.ktor.server.application.*
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.shared.database.user_register
import net.kidjo.server.shared.extensions.respondDatabaseIssue
import net.kidjo.server.shared.models.User

suspend fun Api5UserController.createUserSSO(user: User, call: <PERSON>Call): String {

    logger.info("[Api5UserController.createUser] New user creation: $user")
    val id = databaseController.user_register(user).toString()
    if (id == User.NO_ID) {
        logger.error("[Api5UserController.createUser] DB Issue - User creation failed, no ID found: $user")
        call.respondDatabaseIssue()
        return id
    }
    user.id = id
    return id
}