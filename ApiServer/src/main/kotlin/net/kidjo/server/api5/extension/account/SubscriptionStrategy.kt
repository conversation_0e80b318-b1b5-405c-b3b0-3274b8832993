package net.kidjo.server.api5.extension.account

import net.kidjo.server.api5.extension.account.request.SubscriptionRequest
import net.kidjo.server.api5.extension.account.request.UnSubscriptionRequest
import net.kidjo.server.api5.extension.account.response.SubscriptionResponse
import net.kidjo.server.shared.models.User


interface SubscriptionStrategy {
    fun unSubscribe(user: User, unSubscriptionRequest: UnSubscriptionRequest)
    fun subscribe(user: User, subscription: SubscriptionRequest, countryId: Int?): SubscriptionResponse
}
