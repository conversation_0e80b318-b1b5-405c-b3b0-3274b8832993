package net.kidjo.server.api5.publichers

import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import net.kidjo.server.api5.extension.subscription.cafeyn.connectRequest
import net.kidjo.server.api5.extension.subscription.cafeyn.connectRequestAllProducts
import net.kidjo.server.api5.extension.subscription.cafeyn.saveCafeynUserInfo
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

/**
 * Cafeyn Integration controller for TV and Stories
 *
 */
class Api5CafeynController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api5CafeynController - ")
    override fun installRoutes(route: Route) {
        route.get("/cafeyn/tv") { <EMAIL>(call, Products.TV) }
        route.get("/cafeyn/stories") { <EMAIL>(call, Products.STORIES) }
        route.post("/cafeyn/users") { <EMAIL>(call) }

        route.get("/cafeyn/products") { <EMAIL>(call) }


    }
}

/**
 * Enum class for the product
 */
enum class Products(val s: String) {
    TV("kidjo-tv"),
    STORIES("kidjo-stories"),
    GAMES("kidjo-games"),
    ACCOUNTS("kidjo-accounts")
}
