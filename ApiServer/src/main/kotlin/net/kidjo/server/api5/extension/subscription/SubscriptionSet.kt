package net.kidjo.server.api5.extension.subscription

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.request.receive
import net.kidjo.server.api5.publichers.Api5SubscriptionController
import net.kidjo.server.shared.database.getHuaweiSubscriptionByOrderId
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.respondBadParameters
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Subscription
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import java.math.BigInteger
import java.security.SecureRandom


internal val GENERATED_ACCOUNT_CHARACTERS_TO_USE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()
const val kidjoDomain = "@kidjo.tv"


suspend fun Api5SubscriptionController.kidjoTvSubscription(
    subscriptionType: SubscriptionRoot.SubscriptionType, call: ApplicationCall
) {

    var userId = 0L

    var subscription = call.receive<Subscription>()
    val authenticatedUser = call.principal<User>()
    val receipt = subscription.receipt
    val iapId = subscription.iapId
    val deviceType = subscription.deviceType
    val sessionId = subscription.sessionId ?: "NONE"
    val forceUpdate = subscription.forceUpdate
    val manufacturer = subscription.deviceInfo.manufacturer
    val type = subscription.deviceInfo.type
    val model = subscription.deviceInfo.model

    // Only for Huawei
    val subscriptionId = subscription.subscriptionId
    // Only for Huawei
    val orderId = subscription.orderId
    // Only for Amazon
    val amazonUserId = subscription.amazonUserId

    var paymentsStore: Unit = Unit

    if (receipt.isNullOrEmpty() || iapId.isNullOrEmpty()) {
        return call.respondBadParameters()
    }

    if (authenticatedUser != null) {
        userId = authenticatedUser.getLongId()
    } else {
        val existingSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(receipt.toString())
        if (existingSubscription != null) {
            userId = existingSubscription.userId
        }
    }

    when (deviceType?.lowercase()) {
        Device.StorePlatform.IOS.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.IOS, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.IOS.raw} with iap: $iapId")
            }

            paymentsStore = paymentAppStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.PLAYSTORE.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.PLAYSTORE, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.PLAYSTORE.raw} with iap: $iapId")

            }

            paymentsStore = paymentPlayStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.SAMSUNG.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.SAMSUNG, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.SAMSUNG.raw} with iap: $iapId")

            }

            paymentsStore = paymentSamsungStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.ORANGE.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.ORANGE, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.ORANGE.raw} with iap: $iapId")
            }

            paymentsStore = paymentOrangeStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.HUAWEI.raw -> {
            if (subscriptionId.isNullOrEmpty()) {
                return call.respondBadRequest("The 'subscriptionId' is required!")

            }

            val iap = iapManager.getIAP(Device.StorePlatform.HUAWEI, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.HUAWEI.raw} with iap: $iapId")

            }

            if (userId == 0L) {
                val huaweiSubscription: SubscriptionRoot? = databaseController.getHuaweiSubscriptionByOrderId(orderId!!)
                if (huaweiSubscription != null) {
                    userId = huaweiSubscription.userId
                }
            }

            paymentsStore = paymentHuaweiStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, subscriptionId, orderId, call
            )
        }

        Device.StorePlatform.AMAZON.raw -> {
            if (amazonUserId.isNullOrEmpty()) {
                return call.respondBadRequest("The 'amazonUserId' is required!")

            }
            val iap = iapManager.getIAP(Device.StorePlatform.AMAZON, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.AMAZON.raw} with iap: $iapId")

            }

            paymentsStore = paymentAmazonStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, amazonUserId, null, call
            )
        }

        Device.StorePlatform.JIO.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.JIO, iapId)
            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.JIO.raw} with iap: $iapId")

            }

            paymentsStore = paymentJioStoreManager.start(
                userId, iap, receipt, sessionId, subscriptionType, forceUpdate, null, null, call
            )
        }

    }
    if (!type.isNullOrEmpty()) {
        if ("TV".equals(type.uppercase())) {
            paymentPlayStoreManager.insertDeviceInfo(type, manufacturer, model);
        }
    }
    return paymentsStore
}


suspend fun Api5SubscriptionController.generateApiKey(call: ApplicationCall) {
    val random = SecureRandom()
    val apiKeyBytes = ByteArray(32)
    random.nextBytes(apiKeyBytes)
    var result = BigInteger(1, apiKeyBytes).toString(16)
    call.respondOK(result)
}




