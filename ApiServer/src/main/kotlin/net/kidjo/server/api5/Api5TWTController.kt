package net.kidjo.server.api5

import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.post
import net.kidjo.server.api5.extension.subscription.twt.subscriptionSet
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api5TWTController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4TWTController")
    override fun installRoutes(route: Route) {
        route.post("/twt/subscriptions") { <EMAIL>(call,config,encryptionController) }

    }

}