package net.kidjo.server.api5

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api5.extension.account.braintree.BrainTreeManager
import net.kidjo.server.api5.extension.account.updateBrainTreeSubscription
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector

class Api5BackofficeController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    val brainTreeManager=BrainTreeManager(config,utility,encryptionController, databaseController)
    override fun installRoutes(route: Route) {
        route.post("/updateSubscriptions"){
            <EMAIL>(call)
        }
    }
}
