package net.kidjo.server.api5.extension.subscription

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import net.kidjo.server.api5.publichers.Api5PartnerSubscriptionController
import net.kidjo.server.shared.extensions.getLong
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.CheckSubscriptionResponse
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Partner
import net.kidjo.server.shared.models.PasswordResetRequest
import net.kidjo.server.shared.models.Subscriptions
import net.kidjo.server.shared.models.SwitchUnsubscription
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.SubscriptionRoot
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.models.response.Detail
import net.kidjo.server.shared.models.response.UnSubscriptionResponse
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.utils.customGroupConcat
import net.kidjo.server.utils.customGroupConcatBool
import net.kidjo.server.utils.emailValidation
import net.kidjo.server.utils.getUserIdByUserEmail
import net.kidjo.server.utils.insertLogs
import net.kidjo.server.utils.updateUserPassword
import net.kidjo.server.utils.validateMobileNumber
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.innerJoin
import org.jetbrains.exposed.sql.leftJoin
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.json.JSONObject
import java.time.LocalDateTime

const val LENGTH_OF_GENERATED_DV_ACCOUNT_NAME = 6

suspend fun Api5PartnerSubscriptionController.checkSubscription(call: ApplicationCall) {
    val partnerName = call.principal<Partner>()
    val email = call.parameters.getString("email")
    val msidn = call.parameters.getString("msidn")


    val subscriptionDetails = transaction {
        val userSubscription = Users.innerJoin(SubscriptionRoot, { SubscriptionRoot.userId }, { Users.id })
            .leftJoin(UserPartnerSubscription, { UserPartnerSubscription.subscriptionId }, { SubscriptionRoot.id })
            .slice(
                customGroupConcat((SubscriptionRoot.iapId).alias("")).alias("iapId"),
                customGroupConcatBool((SubscriptionRoot.isActive).alias("")).alias(
                    "isActive"
                ),
                Users.email
            ).selectAll().andWhere { SubscriptionRoot.isActive eq true }
            .andWhere { SubscriptionRoot.platformPurchaseId.lowerCase() eq partnerName?.name.toString().lowercase() }

        if (!email.isNullOrEmpty()) {
            userSubscription.andWhere { Users.email.lowerCase() eq email.lowercase() }.singleOrNull()
        } else if (!msidn.isNullOrEmpty()) {
            userSubscription.andWhere { UserPartnerSubscription.msidn eq msidn }.singleOrNull()
        }
        userSubscription.groupBy(Users.email).mapNotNull {

            CheckSubscriptionResponse(
                it[Users.email], createSubscriptionList(
                    it[customGroupConcat(
                        (SubscriptionRoot.iapId).alias(
                            ""
                        )
                    ).alias("iapId")], active = it[customGroupConcatBool(
                        (SubscriptionRoot.isActive).alias(
                            ""
                        )
                    ).alias("isActive")]
                )
            )
        }
    }
    if ((subscriptionDetails.size != 0) && !(subscriptionDetails.isNullOrEmpty())) {
        call.respondOK(subscriptionDetails)
    } else {
        call.respondError(HttpStatusCode.NotFound, "User subscription data not found")
    }

}

suspend fun Api5PartnerSubscriptionController.unSubscriptionUser(call: ApplicationCall) {

    val partner = call.principal<Partner>()
    val partnerSubscription = call.receive<SwitchUnsubscription>()
    var isUnSubscribed = false
    var subscriptionId = 0L
    var userId = 0L
    val msisdn = partnerSubscription.msidn.toString()
    val email = partnerSubscription.email.toString()
    if (email.isEmpty() && msisdn.isEmpty()) {
        throw BadRequestException("msisdn or email is required")
    } else {
        if (email.isNotEmpty()) {
            if (!emailValidation(email)) {
                throw BadRequestException("Invalid email format")
            }
        } else {
            if (!validateMobileNumber(msisdn)) {
                throw BadRequestException("Invalid msisdn format")
            }
        }

        transaction {
            if (msisdn.isNotEmpty()) {
                UserPartnerSubscription.slice(UserPartnerSubscription.subscriptionId).select {
                    UserPartnerSubscription.subscriptionPackage.eq(partnerSubscription.subscriptionPackage.toString()) and UserPartnerSubscription.msidn.eq(
                        msisdn
                    )
                }.limit(1).mapNotNull { subscriptionId = it[UserPartnerSubscription.subscriptionId].toLong() }

            } else if (email.isNotEmpty()) {
                Users.slice(Users.id).select { Users.email.eq(email) }.mapNotNull { userId = it[Users.id] }
                SubscriptionRoot.slice(SubscriptionRoot.id).select {
                    SubscriptionRoot.isActive.eq(true) and SubscriptionRoot.storeId.eq(
                        Device.StorePlatform.fromRow(
                            partner?.name.toString()
                        )
                    ) and SubscriptionRoot.userId.eq(userId.toInt())
                }.mapNotNull { subscriptionId = it[SubscriptionRoot.id] }
            }

            val subscriptionExists =
                SubscriptionRoot.select { (SubscriptionRoot.id eq subscriptionId) and (SubscriptionRoot.isActive eq true) }
                    .count() > 0

            if (subscriptionExists) {
                SubscriptionRoot.update({ SubscriptionRoot.id.eq(subscriptionId) }) {
                    it[isActive] = false
                    it[isRenewing] = false
                    it[nextBillDate] = LocalDateTime.now().minusMinutes(5)
                }
                isUnSubscribed = true
            } else {
                throw BadRequestException("No active subscription with this user")
            }
        }
    }

    if (isUnSubscribed) {
        call.respondOK(
            UnSubscriptionResponse(
                status = 200, detail = Detail(
                    status = "Success", message = "User has been UnSubscribed"
                ),
                sub_partner = getTelcoBySubscriptionId(subscriptionId.toInt()).takeIf { it!!.isNotEmpty() } ?: ""
            )
        )
    }


}

suspend fun Api5PartnerSubscriptionController.unsubscriptionNotification(call: ApplicationCall) {

    val user = call.principal<User>() ?: return call.respondError(HttpStatusCode.BadRequest, "There is no such a user")
    val subscriptionId = call.parameters.getLong("id").takeIf { it.toInt() != 0 } ?: return call.respondError(
        HttpStatusCode.BadRequest, "Pass subscription id"
    )
    val success = unsubscribeUserBySubId(user, subscriptionId, config)

    if (success.isNotEmpty()) {
        call.respond(mapOf("sub_partner" to success))
    } else {
        throw BadRequestException("User Unsubscription failed")
    }


}

suspend fun Api5PartnerSubscriptionController.passwordReset(call: ApplicationCall) {
    logger.debug(" API v5/password-reset")

    val partner = call.principal<Partner>()
    logger.debug("Kidjo Partner {${partner?.name.toString()}}")
    val logs = call.receive<String>()
    insertLogs(logs, partner?.name.toString())
    logger.debug("Logs inserted into partner_subscription_logs ")
    logger.debug("SubscriptionBean Json $logs")
    val objectMapper = ObjectMapper()
    val request = objectMapper.readValue(logs, PasswordResetRequest::class.java)
    var email = request.email.takeIf { !it.isNullOrEmpty() } ?: ""
    val msIsdn = request.msIsdn.toString().takeIf { it.isNotEmpty() } ?: ""
    val oldPassword = request.oldPassword.takeIf { it.isNotEmpty() } ?: return call.respondError(
        HttpStatusCode.BadRequest,
        "oldPassword cannot be empty"
    )
    val newPassword = request.newPassword.takeIf { it.isNotEmpty() } ?: return call.respondError(
        HttpStatusCode.BadRequest,
        "newPassword cannot be empty"
    )


    if (email.isEmpty() && msIsdn.isEmpty()) {
        throw BadRequestException("Email or msIsdn is empty")
    }
    if (msIsdn.isNotEmpty()) {
        if (!validateMobileNumber(msIsdn)) {
            throw BadRequestException("Invalid msIsdn format")
        }
        email = msIsdn.replace("+", "") + kidjoDomain
    }
    if (!emailValidation(email)) {
        throw BadRequestException("Invalid email format")
    }

    var userId = getUserIdByUserEmail(email)
    if (userId.toInt() != 0) {
        updateUserPassword(email, encryptionController, config, oldPassword, newPassword, databaseController)
        call.respondOK()
    } else {
        throw BadRequestException("User does not exist")
    }
}

private fun unsubscribeUserBySubId(user: User, subscriptionId: Long, config: Config): String {
    val activeSubscription = getActiveSubsubscription(user.getLongId().toInt(), subscriptionId)
    var success: String
    if (activeSubscription) {
        val unSubscription = getMsIsdnAndPackage(subscriptionId)
        val okHttpClient = OkHttpClient()

        // Create a JSON media type
        val jsonMediaType = MediaType.parse("application/json; charset=utf-8")

        val jsonBody = JSONObject()
            .put("msisdn", unSubscription?.msidn)
            .put("email", unSubscription?.email)
            .put("package", unSubscription?.subscriptionPackage)
        val requestBody =
            Request.Builder().url(config.apiUrl)
                .post(RequestBody.create(jsonMediaType, jsonBody.toString().trimIndent()))
                .addHeader("Authorization", config.apiKey).build()
        val response = okHttpClient.newCall(requestBody).execute()
        if (response.code() == 200) {
            success = transaction {
                SubscriptionRoot.update({ SubscriptionRoot.id.eq(subscriptionId) }) {
                    it[isRenewing] = false
                    it[isActive] = false
                    it[nextBillDate] = LocalDateTime.now().minusMinutes(5)
                }.toString()
            }
            if (success.toInt() != 0) {
                success = getTelcoBySubscriptionId(subscriptionId.toInt())!!
            }
        } else {
            throw BadRequestException("Switch server is down")
        }

    } else throw BadRequestException("There is no subscription for this user")
    return success;
}

private fun createSubscriptionList(product: String, active: String): List<Subscriptions> {
    val productList = product.split(",")
    val activeList = active.split(",")
    val subscriptions = mutableListOf<Subscriptions>()

    productList.forEachIndexed { index, product ->
        val active = activeList.getOrNull(index)?.toInt() ?: 0
        subscriptions.add(Subscriptions(product, active == 1))
    }
    return subscriptions
}

fun getActiveSubsubscription(userId: Int, subscriptionId: Long): Boolean {
    return transaction {
        SubscriptionRoot.select {
            SubscriptionRoot.id.eq(subscriptionId) and SubscriptionRoot.userId.eq(userId) and SubscriptionRoot.isActive.eq(
                true
            )
        }.count() > 0
    }
}

fun getMsIsdnAndPackage(subscriptionId: Long): SwitchUnsubscription? {

    return transaction {
        UserPartnerSubscription.innerJoin(Users, { Users.id }, { UserPartnerSubscription.userId })
            .slice(Users.email, UserPartnerSubscription.msidn, UserPartnerSubscription.subscriptionPackage)
            .selectAll().andWhere {
                UserPartnerSubscription.subscriptionId.eq(subscriptionId.toInt())
            }
            .map {
                SwitchUnsubscription(
                    msidn = it[UserPartnerSubscription.msidn],
                    it[UserPartnerSubscription.subscriptionPackage],
                    it[Users.email]
                )
            }.firstOrNull()
    }
}

private fun getTelcoBySubscriptionId(subscriptionId: Int): String? {

    return transaction {
        UserPartnerSubscription.slice(UserPartnerSubscription.subPartner).select {
            UserPartnerSubscription.subscriptionId.eq(subscriptionId)
        }.mapNotNull { it[UserPartnerSubscription.subPartner] }.singleOrNull()
    }
}
