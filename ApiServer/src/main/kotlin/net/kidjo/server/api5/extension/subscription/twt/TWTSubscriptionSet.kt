package net.kidjo.server.api5.extension.subscription.twt

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respond
import net.kidjo.server.api5.Api5TWTController
import net.kidjo.server.api5.extension.subscription.kidjoDomain
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.subscriptionRoot_update
import net.kidjo.server.shared.database.subscriptionTWT_getRecentActive
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.epochMilliToLocalDateTime
import net.kidjo.server.shared.extensions.receiveJSON
import net.kidjo.server.shared.extensions.respondBadJSON
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondNoContent
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.generatePassword
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDateTime

suspend fun Api5TWTController.subscriptionSet(
    call: ApplicationCall,
    config: Config,
    encryptionController: EncryptionController
) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val subCode = requestJSON.optString("subscriptionCode")
    val customerId = requestJSON.optString("customerId")
    val expire = requestJSON.optLong("expire")
    val isSubscribed = requestJSON.optBoolean("isSubscribed")
    val productName = requestJSON.optString("productName")
    val carrierName = requestJSON.optString("carrierName")
    val deviceType = requestJSON.optString("deviceType")
    val subToken = customerId + carrierName
    var userEmail = ""
    var userId = 0L
    if (customerId.isNotEmpty()) {
        userEmail = customerId + kidjoDomain
    }

    if (!checkExistingEmail(userEmail)) {
        userId = createUser(userEmail, customerId, config, encryptionController)
    }else{
        userId = getUserIdByEmail(userEmail)
    }

    val sub = databaseController.subscriptionTWT_getRecentActive(userId, subToken)
    val subId = sub?.id ?: SubscriptionRoot.NO_ID

    val nextBillDate = expire.epochMilliToLocalDateTime()

    var subType = SubscriptionRoot.SubscriptionType.KIDJO_TV
    when (subCode) {
        "1" -> subType = SubscriptionRoot.SubscriptionType.KIDJO_BOOKS
        "2" -> subType = SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS
    }

    if (sub == null || (!sub.subscriptionType.raw.contains(subType.raw.substringAfter("_")) || sub.nextBillDate.isBefore(LocalDateTime.now()))) {
        val inserted = SubscriptionRootInsert(
            userId = userId,
            deviceId = Device.NO_SERVER_ID,
            isFreeTrail = false,
            priceToLogUSD = 0f,
            paymentType = SubscriptionRoot.PaymentType.NATIVE,
            subscriptionType = subType,
            paymentId = deviceType,
            platformPurchaseId = carrierName,
            storeId = Device.StorePlatform.TWT,
            paymentStateId = null,
            iapId = productName,
            purchasingSessionId = "NONE",
            subscriptionToken = subToken,
            subscriptionTokenHash = encryptionController.sha256Hash(subToken),
            accountCouponId = AccountCoupon.NO_SERVER_ID,
            isRenewing = isSubscribed,
            nextBillDate = nextBillDate,
            isTest = !config.env.isLive
        )

        if (databaseController.subscriptionRoot_create(inserted) > 0) {

            val responseData = mapOf(
                "username" to userEmail,
                "password" to customerId,
                "subscriptionStatus" to "created "
            )
            call.respond(HttpStatusCode.OK, responseData)
            return
        } else {
            call.respondBadRequest(
                ServerErrors.ERROR_BAD_PARAMS,
                "Error create user's Subscription with id: ${sub?.id}"
            )
            return
        }
    } else {
        // extend the subscription when
        if (LocalDateTime.now().plusDays(1) > sub.nextBillDate) {
            val updated =
                SubscriptionRootUpdate(
                    userId = userId,
                    isRenewing = isSubscribed,
                    nextBillDate = nextBillDate,
                    platformPurchaseId = carrierName,
                    isFreeTrail = false,
                    priceToLogUSD = 0f,
                    subId = subId,
                    paymentStateId = null
                )

            if (!databaseController.subscriptionRoot_update(updated)) {
                call.respondBadRequest(
                    ServerErrors.ERROR_BAD_JSON,
                    "Error UPDATING subscription with id: ${sub.id}"
                )
                return

            } else {
                val responseData = mapOf(
                    "username" to userEmail,
                    "password" to customerId,
                    "subscriptionStatus" to "updated id : ${sub.id}"
                )
                call.respond(HttpStatusCode.OK, responseData)
                return
            }
        } else {
            call.respondNoContent()
            return
        }
    }
}

private fun checkExistingEmail(email: String): Boolean {
    return transaction {
        Users.select { Users.email eq email }.count() > 0
    }
}

private fun createUser(
    email: String,
    customerId: String,
    config: Config,
    encryptionController: EncryptionController,
): Long {

    var userId = 0L;
    val (password, hashedPassword) = generatePassword(
        customerId, encryptionController = encryptionController, salt = config.clientSideHashV1Salt
    )

    transaction {

        val result = Users.insert {
            it[Users.email] = email
            it[Users.password] = hashedPassword
            it[authType] = User.AuthType.FAKE_EMAIL
        }.insertedCount

        if (result > 0) {
           userId= getUserIdByEmail(email)
        }
    }
    return userId
}

private fun getUserIdByEmail(email: String): Long {
    var userId = 0L
    transaction {
        Users.select { Users.email.eq(email) }.map { userId = it[Users.id] }
    }
    return userId
}
