package net.kidjo.server.api5.publichers

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api5.extension.subscription.digitalvirgo.dvHandleNotification
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api5DVController (platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api5DVController - ")
    override fun installRoutes(route: Route) {
        route.post("/digitalvirgo/notification-receiver") {<EMAIL>(call)}
    }
}
