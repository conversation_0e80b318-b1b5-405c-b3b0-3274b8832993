package net.kidjo.server.api5.extension.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.request.acceptLanguage
import io.ktor.server.request.receive
import net.kidjo.server.api4.extension.account.*
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.api4.extension.subscription.SubscriptionDTO
import net.kidjo.server.api4.extension.subscription.digitalvirgo.TEMPORARY_ACCOUNT_EMAIL_SUFFIX
import net.kidjo.server.api5.Api5AccountController
import net.kidjo.server.api5.extension.account.braintree.BrainTreeManager
import net.kidjo.server.api5.extension.account.request.SubscriptionRequest
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.extensions.toEpochMilli
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.MondiaPackages
import net.kidjo.server.shared.models.entity.SubscriptionsRoot
import net.kidjo.server.shared.tools.RequestUtil
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDateTime


/**
 * Subscription Handling Extension
 */
suspend fun Api5AccountController.subscribe(call: ApplicationCall) {
    val user = call.principal<User>() ?: User.getEmptyUser()
    val acceptLanguageRaw = call.request.acceptLanguage() ?: "en"
    val countryId = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw)
    val subscriptionRequest = call.receive<SubscriptionRequest>()

    logger.info("[Api5AccountController.subscribe] Receiving new Braintree Subscription request: $subscriptionRequest")

    val manager = SubscriptionManager(
        user,
        countryId,
        BrainTreeManager(
            config,
            utility,
            encryptionController,
            databaseController
        )
    )
    val result = manager.createSubscription(subscriptionRequest);
    call.respondOK(result)
}


/**
 * Get Subscription Information
 */

suspend fun Api5AccountController.getSubscriptionInfo(call: ApplicationCall) {
    val user = call.principal<User>()

    try {
        var subscriptions = listOf<SubscriptionRoot>()
        if (user != null) {
            subscriptions = databaseController.getUserSubscriptions(user.getLongId())
        }

        val braintree = BrainTreeManager(
            config,
            utility,
            encryptionController,
            databaseController
        );

        val subscriptionsDto = ArrayList<SubscriptionDTO>()
        subscriptions.forEach {
            var coupon: CouponDTO? = null
            val iap = iapManager.getIAP(it.storeId, it.iapId)
            val storeId = it.storeId.raw

            if (it.accountCouponId > 0L && it.stillInFreeTrial) {
                val accCoupon = databaseController.accountCoupon_get(it.accountCouponId)
                coupon = CouponDTO(
                    accCoupon?.id,
                    accCoupon?.couponId,
                    accCoupon?.durationCode,
                    accCoupon?.groupId,
                    accCoupon?.couponType,
                    accCoupon?.productType,
                    accCoupon?.partnerType
                )
            }

            val paymentMethod: String =
                when (storeId) {
                    Device.StorePlatform.KIDJO_BRAINTREE.raw -> {
                        val lastTransaction = user?.brainTreeId?.let { id ->
                            braintreeManager.getLastTransactionBraintreeCustomer(user.brainTreeId)
                        }
                        if (lastTransaction != null) {
                            "${lastTransaction.creditCard?.cardType} ${lastTransaction.creditCard?.maskedNumber}"
                        } else {
                            braintree.getPaymentMethod(user?.brainTreeId)
                        }
                    }

                    Device.StorePlatform.FREE_ACCESS_COUPON.raw -> {
                        "free"
                    }


                    else -> {
                        it.paymentId
                    }
                }
            subscriptionsDto.add(
                SubscriptionDTO(
                    id = it.id.toString(),
                    nextBillingDate = it.nextBillDate.toString(),
                    coupon = coupon,
                    durationCode = iap?.durationCode,
                    platformPurchaseId = it.platformPurchaseId,
                    paymentMethod = if(it.storeId==Device.StorePlatform.MONDIA) {
                        logger.info("IF True Payment mothod ${it.storeId}")
                        logger.info("${getMondiaPaymentOperator(user)} $paymentMethod ")
                        "${getMondiaPaymentOperator(user)} $paymentMethod"
                    } else {
                        logger.info("IF false Payment mothod ${it.storeId}")
                        paymentMethod
                    },
                    paymentPlanId = iap?.id,
                    paymentPlanPrice = iap?.price,
                    paymentStateId = it.paymentStateId,
                    isRenewing = it.isRenewing,
                    storeId = storeId,
                    subscriptionType = it.subscriptionType.raw,
                    isExpired = utility.isBillingDateExpired(it.nextBillDate)
                )
            )
        }
        return call.respondOK(
            UserSubscriptionInfoDTO(
                email = user?.email,
                emailIsConfirmed = user?.emailIsConfirmed,
                isEmailFake = if (user?.authType == User.AuthType.FAKE_EMAIL) true else false,
                name = user?.name,
                subscriptions = subscriptionsDto
            )
        )
    } catch (e: Throwable) {
        logger.error("Failed to find Subscription", e)
        return call.respondBadRequest("Problems finding Subscription")
    }
}

suspend fun Api5AccountController.bookUserInfo(call: ApplicationCall) {

    val bookUser = call.principal<User>() ?: return call.respondBadRequest("Problems finding BOOK User PRINCIPAL")
    logger.info(
        "[Api5AccountController.bookUserInfo] Receiving new User Info (User:$bookUser ) request: $call"
    )
    return try {
        val bookSubscription = databaseController.getRecentBOOKSubscription(bookUser.getLongId(),true)
        val isPromoAccepted = databaseController.getPromoForUser(bookUser.getLongId())
        val isTCAccepted = databaseController.getTCForUser(bookUser.getLongId())

        val bookUserDto = getUserDTO(bookUser, bookSubscription, isTCAccepted, isPromoAccepted)
        call.respondOK(mapOf("user" to bookUserDto))
    } catch (e: Throwable) {
        logger.error("[Api5AccountController.bookUserInfo] Failed to find BOOK Subscription $e")
        call.respondBadRequest("Problems finding BOOK Subscription")
    }
}

suspend fun Api5AccountController.tvUserInfo(call: ApplicationCall) {

    val tvUser = call.principal<User>() ?: return call.respondBadRequest("Problems finding TV User PRINCIPAL")
    return try {
        val tvSubscription = databaseController.getRecentTVSubscription(tvUser.getLongId(),true)
        val isPromoAccepted = databaseController.getPromoForUser(tvUser.getLongId())
        val isTCAccepted = databaseController.getTCForUser(tvUser.getLongId())
        val tvUserDto = getUserDTO(tvUser, tvSubscription, isTCAccepted, isPromoAccepted)
        call.respondOK(mapOf("user" to tvUserDto))

    } catch (e: Throwable) {
        logger.error("[Api5AccountController.tvUserInfo] Failed to TV find Subscription $e")
        call.respondBadRequest("Problems finding TV Subscription")
    }
}

suspend fun Api5AccountController.gameUserInfo(call: ApplicationCall) {

    val gameUser = call.principal<User>() ?: return call.respondBadRequest("Problems finding GAME User PRINCIPAL")

    return try {
        val gameSubscription = databaseController.getRecentGAMESubscription(gameUser.getLongId(),true)
        val isPromoAccepted = databaseController.getPromoForUser(gameUser.getLongId())
        val isTCAccepted = databaseController.getTCForUser(gameUser.getLongId())
        val gameUserDto = getUserDTO(gameUser, gameSubscription, isTCAccepted, isPromoAccepted)
        call.respondOK(mapOf("user" to gameUserDto))

    } catch (e: Throwable) {
        logger.error("[Api5AccountController.gameUserInfo] Failed to find GAME Subscription $e")
        call.respondBadRequest("Problems finding GAME Subscription")
    }
}

private fun Api5AccountController.getUserDTO(
    user: User,
    subscription: SubscriptionRoot?,
    isTCAccepted: Int,
    isPromoAccepted: Int
): UserDTO {
    val durationCode = getAccountCoupon(subscription)?.durationCode
    return createUserDto(
        user = user,
        expirationDateToMilli = getExpirationInMilliseconds(subscription?.nextBillDate),
        subscriptionDto = crateSubscriptionDto(subscription, durationCode),
        durationCode = durationCode,
        isTcAccepted = isTCAccepted,
        isPromoAccepted = isPromoAccepted
    )
}

private fun Api5AccountController.getAccountCoupon(subscription: SubscriptionRoot?): AccountCoupon? =
    if (subscription != null &&
        subscription.accountCouponId > 0L &&
        subscription.stillInFreeTrial
    )
        databaseController.accountCoupon_get(subscription.accountCouponId)
    else null

private fun getExpirationInMilliseconds(nextBillDate: LocalDateTime?): Long? = nextBillDate?.toEpochMilli()


private fun Api5AccountController.crateSubscriptionDto(
    subscription: SubscriptionRoot?,
    durationCode: String?
): SubscriptionDTO? =
    if (subscription != null)
        SubscriptionDTO(
            id = subscription.id.toString(),
            nextBillingDate = subscription.nextBillDate.toString(),
            coupon = null,
            durationCode = durationCode,
            platformPurchaseId = subscription.platformPurchaseId,
            paymentMethod = null,
            paymentPlanId = null,
            paymentPlanPrice = null,
            paymentStateId = subscription.paymentStateId,
            isRenewing = subscription.isRenewing,
            storeId = subscription.storeId.raw,
            subscriptionType = subscription.subscriptionType.raw,
            isExpired = utility.isBillingDateExpired(subscription.nextBillDate)
        )
    else null

private fun createUserDto(
    user: User,
    expirationDateToMilli: Long?,
    subscriptionDto: SubscriptionDTO?,
    durationCode: String?,
    isTcAccepted: Int?,
    isPromoAccepted: Int?
): UserDTO = UserDTO(
    id = user.id,
    name = user.name,
    email = user.email,
    expirationDate = expirationDateToMilli,
    subscription = subscriptionDto,
    couponDurationCode = durationCode,
    isEmailFake = user.email.contains(TEMPORARY_ACCOUNT_EMAIL_SUFFIX, ignoreCase = true),
    isTcAccepted = isTcAccepted,
    isPromoAccepted = isPromoAccepted
)

private fun getMondiaPaymentOperator(user: User?):String{

    var operator =""

    transaction {
        // Step 1: Define the subquery to extract the `secondValue` from `subscriptionToken`
        val secondValueSubquery = SubscriptionsRoot
            .slice(SubscriptionsRoot.subscriptionToken)
            .select { (SubscriptionsRoot.userId.eq(user!!.id.toLong()) and (SubscriptionsRoot.storeId eq "mondia")) }
            .map {
                val subscriptionToken = it[SubscriptionsRoot.subscriptionToken]
                subscriptionToken.split("|").getOrNull(1) // Extract the second part
            }
            .firstOrNull()

        // Step 2: Use `secondValueSubquery` to query `MondiaPackages`
        if (secondValueSubquery != null) {
            operator = MondiaPackages
                .slice(MondiaPackages.operator)
                .select { MondiaPackages.mondiaProductId eq secondValueSubquery.toInt() }
                .map { it[MondiaPackages.operator] }
                .firstOrNull() ?: ""

        }

    }

    return operator;
}

data class UserDTO(
    var id: String,
    var name: String,
    val email: String,
    val expirationDate: Long?,
    val subscription: SubscriptionDTO?,
    val couponDurationCode: String?,
    val isEmailFake: Boolean = false,
    val isTcAccepted: Int?,
    val isPromoAccepted: Int?
)