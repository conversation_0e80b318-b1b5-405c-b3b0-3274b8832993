package net.kidjo.server.api5

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.Api4SubscriptionController
import net.kidjo.server.api5.extension.account.validateCoupon
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector

class Api5CouponController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val api4SubscriptionController = Api4SubscriptionController(platformInjector)
    override fun installRoutes(route: Route) {
        route.get("/coupon/validate/{couponCode}") {
            <EMAIL>(call)
        }
    }
}
