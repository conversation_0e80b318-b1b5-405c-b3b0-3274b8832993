package net.kidjo.server.api5.extension.account.partner.user.helpers

import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.shared.database.registerUserConsent

suspend fun Api5UserController.createUserConsent(userId: String, isTcAccepted: Int, isPromoAccepted: Int) {

    logger.info("[Api5UserController.createUserConsent] Creating user consent for user_id: $userId")

    try {
        val id = databaseController.registerUserConsent(userId, isTcAccepted, isPromoAccepted).toString()
        logger.info("[Api5UserController.createUserConsent] User consent created successfully for user_id: $userId with ID: $id")
    } catch (e: Exception) {
        logger.error("[Api5UserController.createUserConsent] Failed to create user consent for user_id: $userId", e)
    }
}