package net.kidjo.server.api5.extension.account.user


import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api4.LoginDtoIn
import net.kidjo.server.api4.UserDtoOut
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.api5.extension.account.SubscriptionManager
import net.kidjo.server.api5.extension.account.partner.user.helpers.createUserSSO
import net.kidjo.server.api5.extension.account.request.Plan
import net.kidjo.server.api5.extension.account.request.SubscriptionRequest
import net.kidjo.server.api5.extension.subscription.cafeyn.CafeynSSOManager
import net.kidjo.server.models.cafeyn.CafeynLoginResponse
import net.kidjo.server.service.CafeynAuthService
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.UserPartnerSSO
import java.time.LocalDate
import java.time.ZoneId


const val logPrefix = "[Api5UserController.login]"

suspend fun Api5UserController.login(setJwt: Boolean? = true, call: ApplicationCall) {
    val userLoginIn = call.receive<LoginDtoIn>()
    config.deviceIdForTwt = call.request.header("X-Kidjo-DeviceId").orEmpty()

    if (userLoginIn.email.isNullOrEmpty() || userLoginIn.password.isNullOrEmpty()) {
        call.respondBadRequest("Bad Params")
        return
    }

    if (!validator.isEmailValid(userLoginIn.email)) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
        return
    }

    val user = databaseController.user_getByEmail(userLoginIn.email)

    if (user == null) {
        logger.info("$logPrefix User ${userLoginIn.email} is null, calling Cafeyn SSO API...")
        handleCafeynNewUserLogin(call, userLoginIn, setJwt)
    } else if (databaseController.belongsToCafeynPartner(user)) {
        logger.info("$logPrefix User $user is Cafeyn User")
        handleExistingCafeynUserLogin(call, user, userLoginIn, setJwt)
    } else {
        logger.info("$logPrefix User $user is not Cafeyn User")
        logger.info("$logPrefix User $user Encoding the pass and calling the internal login api...")
        val hashedPassword = encryptionController.sha256Hash(userLoginIn.password + config.clientSideHashV1Salt)
        logger.info("$logPrefix User $user Hashed Pass $hashedPassword")
        val updatedLoginDto = userLoginIn.copy(password = hashedPassword)
        logger.info("$logPrefix User $user Encrypted PASSWORD: ${updatedLoginDto.password}")

        handleInternalUserLogin(call, user, updatedLoginDto, setJwt)
    }
}

private suspend fun Api5UserController.handleCafeynNewUserLogin(
    call: ApplicationCall,
    userLoginIn: LoginDtoIn,
    setJwt: Boolean?
) {
    val loginResp = callCafeynPartnerLoginApi(userLoginIn)
    if (loginResp.is_active) {
        val newUser = insertNewUserInDb(userLoginIn, loginResp, call)
        val subId = manageSubscription(newUser, buildSubRequest(loginResp))
        logger.info("$logPrefix User ${userLoginIn.email} Subscription created $subId")
        handleSuccessfulLogin(call, newUser, setJwt)
    } else {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Login failed.")
    }
}

private suspend fun Api5UserController.handleExistingCafeynUserLogin(
    call: ApplicationCall,
    user: User,
    userLoginIn: LoginDtoIn,
    setJwt: Boolean?
) {
    val loginResp = callCafeynPartnerLoginApi(userLoginIn)
    if (loginResp.is_active) {
        updateUserInDb(userLoginIn, loginResp, user)
        val subId = manageSubscription(user, buildSubRequest(loginResp))
        logger.info("$logPrefix User ${userLoginIn.email} Subscription updated $subId")
        handleSuccessfulLogin(call, user, setJwt)
    } else {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Login failed.")
    }
}

fun buildSubRequest(
    cafeynLoginResponse: CafeynLoginResponse
): SubscriptionRequest {

    val subscribedServices = cafeynLoginResponse.subscribed_services.toSet()

    val product = when {
        subscribedServices.containsAll(setOf("kidjoStories", "kidjoTv", "kidjoGames")) -> "kidjo_tv_books_games"
        subscribedServices.containsAll(setOf("kidjoStories", "kidjoTv")) -> "kidjo_tv_books"
        subscribedServices.containsAll(setOf("kidjoStories", "kidjoGames")) -> "kidjo_books_games"
        subscribedServices.containsAll(setOf("kidjoTv", "kidjoGames")) -> "kidjo_tv_games"
        subscribedServices.contains("kidjoStories") -> "kidjo_books"
        subscribedServices.contains("kidjoTv") -> "kidjo_tv"
        subscribedServices.contains("kidjoGames") -> "kidjo_games"
        else -> throw IllegalArgumentException("Invalid subscribed services")
    }

    val plan = Plan(
        planId = "default_sso_product",
        product = product,
        addOns = null,
        discounts = null
    )

    return SubscriptionRequest(
        paymentNonce = "SSO_Cafeyn|PayNonce",
        plans = plan,
        isBundle = false,
        coupon = null,
        deviceData = "SSO_Cafeyn|DeviceData",
        billingName = null,
        subscriptionStartDate = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    )
}

private fun Api5UserController.manageSubscription(
    user: User,
    subscriptionRequest: SubscriptionRequest
): String {
    val manager = SubscriptionManager(
        user,
        user.countryId,
        CafeynSSOManager(
            config,
            databaseController
        )
    )
    val result = manager.createSubscription(subscriptionRequest)
    logger.info(
        "$logPrefix Cafeyn User ${user.id} | Subscription created with " +
                "id ${result.subscriptionId} : $result"
    )
    return result.subscriptionId
}

private suspend fun Api5UserController.handleInternalUserLogin(
    call: ApplicationCall,
    user: User,
    userLoginIn: LoginDtoIn,
    setJwt: Boolean?
) {
    when (validateAndLimitThePassword(userLoginIn.password!!, user)) {
        1 -> call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Wrong password.")
        2 -> call.respondBadRequest(
            UsersErrors.ERROR_LOGIN_LIMITED,
            "Wrong password was entered too many times. Try again in a couple of minutes."
        )

        else -> handleSuccessfulLogin(call, user, setJwt)
    }
}

private suspend fun Api5UserController.callCafeynPartnerLoginApi(userLoginIn: LoginDtoIn): CafeynLoginResponse {
    val authService = CafeynAuthService(config)
    val loginResponse = authService.authenticate(userLoginIn.email!!, userLoginIn.password!!)
    logger.info("$logPrefix User ${userLoginIn.email} | Cafeyn Login response: $loginResponse")
    return loginResponse
}

private suspend fun Api5UserController.insertNewUserInDb(
    userLoginIn: LoginDtoIn,
    loginResp: CafeynLoginResponse,
    call: ApplicationCall
): User {

    // Creating users in Kidjo DB
    val user = User.getEmptyUser()
    user.authType = User.AuthType.EMAIL
    user.email = userLoginIn.email!!
    user.name = userLoginIn.email!!
    user.hashedPassword = encryptionController.hashPassword("${userLoginIn.password!!}CafeynSalt")
    user.countryId = 0

    user.id = createUserSSO(user, call)
    logger.info("$logPrefix DB Cafeyn User created with ID: ${user.id} ,User: $user.")

    // Mark the user as Cafeyn User
    val userSSO = populateUserPartnerSSO(loginResp, user)
    val userPartSSO = databaseController.saveUsersPartnerSSO(userSSO)
    logger.info("$logPrefix DB Cafeyn User Linked with ID: $userPartSSO ,UserSSO: $userSSO.")

    return user
}

private fun Api5UserController.updateUserInDb(
    userLoginIn: LoginDtoIn,
    loginResp: CafeynLoginResponse,
    user: User
): Int {

    // updating users in Kidjo DB
    databaseController.userUpdateName(user.getLongId(), userLoginIn.email!!)
    databaseController.userUpdateEmail(user.getLongId(), userLoginIn.email!!)
    logger.info("$logPrefix DB Cafeyn User Updated with ID: ${user.id} ,User: $userLoginIn.")

    // Update cafeyn user
    val userSSO = populateUserPartnerSSO(loginResp, user)
    val userPartSSO = databaseController.updateUsersPartnerSSO(userSSO)
    logger.info("$logPrefix DB Cafeyn User Linked with ID: $userPartSSO ,UserSSO: $userSSO successfully updated.")
    return userPartSSO
}


private fun Api5UserController.populateUserPartnerSSO(loginResp: CafeynLoginResponse, user: User): UserPartnerSSO {
    return UserPartnerSSO(
        userId = user.id.toLong(),
        partnerId = databaseController.getAccountCouponPartnerId("CAFEYN"),
        isActive = true,

        partnerUserId = loginResp.user_id.toLong(),
        partnerName = loginResp.partner_name,
        partnerDescription = loginResp.partner_description,
        partnerIsActive = loginResp.is_active,
        partnerIsSsoActivated = loginResp.is_sso_activated,
        partnerBundles = loginResp.bundles.joinToString(","),
        partnerSubscribedServices = loginResp.subscribed_services.joinToString(","),
        email = loginResp.email
    )
}


private suspend fun Api5UserController.handleSuccessfulLogin(call: ApplicationCall, user: User, setJwt: Boolean?) {
    updateUserCountry(user, call)
    val jwt = if (setJwt == true) {
        userManager.generateAndSetUpUserAccessToken(user.getLongId())
    } else {
        jwtManager.generateAccessToken(user.id)
    }
    call.respondOK(
        UserDtoOut(
            user.id,
            null,
            false,
            jwt
        )
    )
}

