package net.kidjo.server.api5.extension.account

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.server.api5.Api5AccountController
import net.kidjo.server.shared.database.getUserActiveSubscriptionsType
import net.kidjo.server.shared.database.v5.getPlans
import net.kidjo.server.shared.database.v5.getUserCurrentPlans
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.extensions.respond404
import net.kidjo.server.shared.models.Plans
import net.kidjo.server.shared.models.Product
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.tools.RequestUtil


suspend fun Api5AccountController.getPlans(call: ApplicationCall) {
    val user = call.principal<User>();
    val countryCode = call.request.queryParameters.getString("countryCode", "us")
    var countryId = RequestUtil.getCountryFromCode(databaseController, countryCode)
    if (countryId == -1) {
        val acceptLanguageRaw = call.request.acceptLanguage() ?: "us"
        countryId = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw)
    }
    val showAllPlans = call.request.queryParameters.getString("showAll", "false").toBoolean()
    user?.id?.toLong()?.let { userId ->
        val subscriptionType = databaseController.getUserActiveSubscriptionsType(userId)
        if (showAllPlans) {
            call.respond(HttpStatusCode.OK, getUserCurrentPlans(Product.entries, countryId, subscriptionType))
        } else if (subscriptionType.isEmpty()) {
            call.respond(HttpStatusCode.OK, mapProducts(Product.entries, countryId))
        } else {
            call.respond(
                HttpStatusCode.OK,
                mapProducts(
                    Product.entries.filter {
                        it !in Product.getProduct(subscriptionType)
                    }, countryId
                )
            )
        }
    }
    call.respond404("User is doesnt have any plans")
}

private fun mapProducts(product: List<Product>, countryId: Int?): List<Plans> {
    return getPlans(product, countryId)
}
