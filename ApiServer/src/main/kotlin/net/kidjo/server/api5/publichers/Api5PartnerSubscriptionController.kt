package net.kidjo.server.api5.publichers

import io.ktor.server.application.*
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import net.kidjo.server.api5.extension.subscription.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api5PartnerSubscriptionController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {

    internal val logger = LoggerFactory.getLogger(Api5PartnerSubscriptionController::class.java)

    override fun installRoutes(route: Route) {
        //Subscribe Mobile kidjo-TV

        route.post("/partner-subscription") {
            <EMAIL>(call)
        }

        route.post("/notification/subscription") {
            <EMAIL>(call)
        }

        route.get("/check-subscription") {
            <EMAIL>(call)
        }

        route.post("/kidjo/unSubscribeUser") {
            <EMAIL>(call)
        }

        route.post("/subscription/notification") {
            <EMAIL>(call)
        }

        route.post("/notification/redirection") {
            <EMAIL>(call)
        }

        route.put("/notification/unsubscription") {
            <EMAIL>(call)
        }

        route.put("/reset-password") {
            <EMAIL>(call)
        }

        route.post("/notification/consent/given") {
            <EMAIL>("CREATE", call)
        }

        route.post("/notification/consent/update") {
            <EMAIL>("UPDATE", call)
        }
    }
}
