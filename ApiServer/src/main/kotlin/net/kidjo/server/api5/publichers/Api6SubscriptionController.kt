package net.kidjo.server.api5.publichers

import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.post
import net.kidjo.server.api5.extension.subscription.kidjoTvSubscription
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.models.SubscriptionRoot
import org.slf4j.LoggerFactory

class Api6SubscriptionController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api6SubscriptionController")
    override fun installRoutes(route: Route) {
        //Subscribe Mobile kidjo-TV
        route.post("/subscriptions") {
            <EMAIL>(
                SubscriptionRoot.SubscriptionType.KIDJO_TV,
                call
            )
        }

    }
}