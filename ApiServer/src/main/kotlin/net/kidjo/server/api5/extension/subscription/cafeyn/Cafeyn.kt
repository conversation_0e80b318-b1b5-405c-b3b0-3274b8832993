package net.kidjo.server.api5.extension.subscription.cafeyn

import com.google.api.client.googleapis.notifications.NotificationUtils
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.response.respondRedirect
import net.kidjo.server.api5.publichers.Api5CafeynController
import net.kidjo.server.api5.publichers.Products
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOk
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.models.entity.CafeynUsersInfo
import net.kidjo.server.shared.payments.publichers.v5.cafeyn.CafeynResponse
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.utils.dateFormatter
import net.kidjo.server.utils.emailValidation
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import java.time.LocalDateTime

/**
 * Cafeyn Connect Request Processing
 * When the redirect request is originated from cafeyn with jwt token validate the signature based on below logic
 * base64(hmac(“{request-id}|{cafeynHubToken}|{timestamp}|{3rdPartyName}”, KEY))
 *
 * if signature is invalid then redirect the user to kidjo home page
 *
 * <ul>if signature is valid then
 *   <li>
 *       then validate the user have subscription or not by calling refreshTokenApi based on isactive flag
 *       redirect the user to app site or else to home page
 *   </li>
 *  </ul>
 *
 *  if user is active user create the subscription for kidjo tv alone for now
 *
 *
 * This method is deprecated, @see connectRequestAllProducts
 */
suspend fun Api5CafeynController.connectRequest(call: ApplicationCall, product: Products) {

    val logPrefix = "[Api5CafeynController.connectRequest]"

    val cafeynHubToken = call.parameters["ssoCafeyn"]
    val requestId = call.parameters["request-id"]
    val timestamp = call.parameters["timestamp"]
    val signature = call.parameters["signature"]
    val noRedirect = call.parameters["noRedirect"]
    logger.info("Cafeyn Connect request for product ${product.name}")
    logger.info("Cafeyn Request Id-{}", requestId)
    // Verify the signature
    val computedSignature = generateSignature(requestId, cafeynHubToken, timestamp, config, product)
    //persist incoming request for reference
    databaseController.persistCafeynRequest(requestId, cafeynHubToken, timestamp, signature)
    if (signature == computedSignature) {
        // Signature is valid, process the request
        logger.info("Cafeyn request ${requestId} is valid making refresh token api request")
        databaseController.updateAsValidRequest(requestId, true)
        val cafeynResponse = cafeynApiManager.refreshTokenApi(cafeynHubToken)
        if (cafeynResponse.first == 200) {
            val currentUser = cafeynResponse.second?.email?.let { databaseController.user_getByEmail(it) }
            logger.info("Current User -> {}", currentUser)
            val generatedKey = NotificationUtils.randomUuidString()
            val subServices = cafeynResponse.second?.subscribedServices?.toSet()
            val productSub = subscriptionType(subServices)
            logger.info("$logPrefix Subscribed services for User -> $currentUser are: $productSub")

            if (cafeynResponse.second?.isActive == true) {
                cafeynResponse.second.let {
                    it?.let {
                        if (currentUser == null) {
                            val kidjoUserID = createUserForCafeyn(it)
                            val userSSO = populateUserPartnerSSO(kidjoUserID)// mark the new user as cafeyn user
                            val userPartSSO = databaseController.saveUsersPartnerSSOIfNotExists(userSSO)
                            logger.info("$logPrefix DB Cafeyn User Linked with ID: $userPartSSO ,UserSSO: $userSSO.")
                            createSubscription(it, generatedKey, kidjoUserID, product, productSub)

                        } else {
                            val isModified =
                                modifySubscription(currentUser, LocalDateTime.now().plusDays(1), product, it.token)
                            if (!isModified) {
                                createSubscription(it, generatedKey, currentUser.id.toLong(), product, productSub)
                            }
                            val userSSO = populateUserPartnerSSO(currentUser.getLongId())
                            val userPartSSO =
                                databaseController.saveUsersPartnerSSOIfNotExists(userSSO) // mark the old user as cafeyn user
                            logger.info("$logPrefix DB Cafeyn User Linked with ID: $userPartSSO ,UserSSO: $userSSO successfully updated.")
                        }
                    }
                }
            } else if (cafeynResponse.second?.isActive == false) {
                modifySubscription(
                    currentUser,
                    LocalDateTime.now().minusHours(1),
                    product,
                    cafeynResponse.second?.token
                )
            }
            currentUser?.let { databaseController.insert_user_redirect_key(currentUser.id.toLong(), generatedKey) }
            redirect(
                generatedKey, noRedirect, when (product) {
                    Products.TV -> config.tvAppSite
                    Products.STORIES -> config.storySite
                    Products.GAMES -> config.gamesSite
                    Products.ACCOUNTS -> config.accountsSite
                }, call
            )
        } else {
            logger.info("User is inactive requestId -{}. Redirecting to home page - {}", requestId, config.tvSite)
            databaseController.updateAsValidRequest(requestId, false)
            redirect(null, noRedirect, config.tvSite, call)
        }
    } else {
        logger.error("Signature Mismatch for the request id {}", requestId)
        databaseController.updateAsValidRequest(requestId, false)
        if (noRedirect?.toBoolean() == true) {
            call.respondBadRequest("Signature Not matching  redirect to home page - ${config.tvSite}")
        } else {
            call.respondRedirect(config.tvSite, false)
        }
    }
}

suspend fun Api5CafeynController.connectRequestAllProducts(call: ApplicationCall) {

    val cafeynHubToken = call.parameters["ssoCafeyn"]
    val requestId = call.parameters["request-id"]
    val timestamp = call.parameters["timestamp"]
    val signature = call.parameters["signature"]
    val noRedirect = call.parameters["noRedirect"]
    val productName = call.parameters["productName"]

    val logPrefix = "[Api5CafeynController.connectRequestAllProducts] RequestId - $requestId"
    logger.info("$logPrefix Cafeyn Connect request for product $productName")
    val product = getProductFromName(productName)
    logger.info("$logPrefix mapped product $product")
    val computedSignature = generateSignature(requestId, cafeynHubToken, timestamp, config, product)
    //persist incoming request for reference
    databaseController.persistCafeynRequest(requestId, cafeynHubToken, timestamp, signature)

    if (signature == computedSignature) {
        // Signature is valid, process the request
        logger.info("$logPrefix Cafeyn request is valid, making refresh token api request ...")
        databaseController.updateAsValidRequest(requestId, true)
        val cafeynResponse = cafeynApiManager.refreshTokenApi(cafeynHubToken)
        logger.info("$logPrefix Cafeyn refresh token response $cafeynResponse ")

        if (cafeynResponse.first == 200) {

            val currentUser = cafeynResponse.second?.email?.let { databaseController.user_getByEmail(it) }
            logger.info("$logPrefix Current User -> $currentUser")
            val generatedKey = NotificationUtils.randomUuidString()
            val subServices = cafeynResponse.second?.subscribedServices?.toSet()
            val productSub = subscriptionType(subServices)
            logger.info("$logPrefix Subscribed services for User -> $currentUser are: $productSub")

            if (cafeynResponse.second?.isActive == true) {
                cafeynResponse.second.let {
                    it?.let {
                        if (currentUser == null) {
                            val kidjoUserID = createUserForCafeyn(it)
                            val userSSO = populateUserPartnerSSO(kidjoUserID)// mark the new user as cafeyn user
                            val userPartSSO = databaseController.saveUsersPartnerSSOIfNotExists(userSSO)
                            logger.info("$logPrefix DB Cafeyn User Linked with ID: $userPartSSO ,UserSSO: $userSSO.")
                            createSubscription(it, generatedKey, kidjoUserID, product, productSub)
                        } else {
                            val isModified =
                                modifySubscriptionAllProducts(
                                    currentUser,
                                    LocalDateTime.now().plusDays(1),
                                    it.token,
                                    logPrefix,
                                    productSub
                                )
                            if (!isModified) {
                                createSubscription(it, generatedKey, currentUser.id.toLong(), product, productSub)
                            }
                            val userSSO = populateUserPartnerSSO(currentUser.getLongId())
                            val userPartSSO =
                                databaseController.saveUsersPartnerSSOIfNotExists(userSSO) // mark the old user as cafeyn user
                            logger.info("$logPrefix DB Cafeyn User Linked with ID: $userPartSSO ,UserSSO: $userSSO successfully updated.")
                        }
                    }
                }
            } else if (cafeynResponse.second?.isActive == false) {
                modifySubscriptionAllProducts(
                    currentUser,
                    LocalDateTime.now().minusHours(1),
                    cafeynResponse.second?.token,
                    logPrefix,
                    productSub
                )
            }
            currentUser?.let { databaseController.insert_user_redirect_key(currentUser.id.toLong(), generatedKey) }
            redirect(
                generatedKey, noRedirect, getProductSiteUrl(product, config), call
            )
        } else {
            logger.info(
                "${logPrefix} User is inactive. Redirecting to home page - " +
                        "${getProductSiteUrl(product, config)}"
            )
            databaseController.updateAsValidRequest(requestId, false)
            redirect(null, noRedirect, getProductSiteUrl(product, config), call)
        }
    } else {
        logger.error("$logPrefix Signature Mismatch.")
        databaseController.updateAsValidRequest(requestId, false)
        if (noRedirect?.toBoolean() == true) {
            call.respondBadRequest(
                "Signature Not matching  redirect to home page - " +
                        "${getProductSiteUrl(product, config)}"
            )
        } else {
            call.respondRedirect(getProductSiteUrl(product, config), false)
        }
    }
}

private fun subscriptionType(subServices: Set<String>?) = when {
    subServices!!.containsAll(
        setOf(
            "kidjoStories",
            "kidjoTv",
            "kidjoGames"
        )
    ) -> SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS_GAMES

    subServices.containsAll(setOf("kidjoStories", "kidjoTv")) -> SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS
    subServices.containsAll(setOf("kidjoStories", "kidjoGames")) -> SubscriptionRoot.SubscriptionType.KIDJO_BOOKS_GAMES
    subServices.containsAll(setOf("kidjoTv", "kidjoGames")) -> SubscriptionRoot.SubscriptionType.KIDJO_TV_GAMES
    subServices.contains("kidjoStories") -> SubscriptionRoot.SubscriptionType.KIDJO_BOOKS
    subServices.contains("kidjoTv") -> SubscriptionRoot.SubscriptionType.KIDJO_TV
    subServices.contains("kidjoGames") -> SubscriptionRoot.SubscriptionType.KIDJO_GAMES
    else -> throw IllegalArgumentException("Invalid subscribed services")
}

private fun getProductSiteUrl(product: Products, config: Config): String {
    return when (product) {
        Products.TV -> config.tvAppSite
        Products.STORIES -> config.storySite
        Products.GAMES -> config.gamesSite
        Products.ACCOUNTS -> config.accountsSite
    }
}

fun getProductFromName(productName: String?): Products {
    return productName?.let {
        Products.values().find { it.s == productName }
    } ?: Products.ACCOUNTS
}


private fun Api5CafeynController.populateUserPartnerSSO(userId: Long): UserPartnerSSO {
    return UserPartnerSSO(
        userId = userId,
        partnerId = databaseController.getAccountCouponPartnerId("CAFEYN"),
        isActive = true,
        partnerUserId = 0,
        partnerName = "unknown",
        partnerDescription = "unknown",
        partnerIsActive = true,
        partnerIsSsoActivated = true,
        partnerBundles = "unknown",
        partnerSubscribedServices = "unknown",
        email = "unknown"
    )
}

suspend fun Api5CafeynController.saveCafeynUserInfo(call: ApplicationCall) {

    val userInfo = call.receive<CafeynUserInfo>()
    val email = userInfo.email.takeIf { !it.isNullOrEmpty() } ?: return call.respondBadRequest("Email is required")
    val userId = userInfo.userId.takeIf { it != 0 } ?: return call.respondBadRequest("User id cannot be 0 or empty")
    val partnerName =
        userInfo.partnerName.takeIf { !it.isNullOrEmpty() } ?: return call.respondBadRequest("Partner name is required")
    val partnerId =
        userInfo.partnerId.takeIf { it != 0 } ?: return call.respondBadRequest("Partner id cannot be 0 or empty")
    val serviceName =
        userInfo.serviceName.takeIf { !it.isNullOrEmpty() } ?: return call.respondBadRequest("Service name is required")
    val eventType =
        userInfo.eventType.takeIf { !it.isNullOrEmpty() } ?: return call.respondBadRequest("Event type is required")
    val requestId = call.parameters["requestId"].takeIf { !it.isNullOrEmpty() }
        ?: return call.respondBadRequest("Request id cannot be 0 or empty")
    val timestamp = call.parameters["timestamp"].takeIf { !it.isNullOrEmpty() }
        ?: return call.respondBadRequest("Timestamp is required")
    val signature = call.parameters["signature"]?.replace(" ", "+").takeIf { !it.isNullOrEmpty() }
        ?: return call.respondBadRequest("Signature is required")

    logger.info("Cafeyn User Info Request : " + userInfo.toString())
    logger.info("Cafeyn Query Params -> requestId=" + requestId + "&timestamp=" + timestamp + "&" + "signature=" + signature)

    val generatedKey = generateSignatureKey(requestId, timestamp, config, serviceName)
    logger.info("Signature = " + generatedKey)
    if (signature.equals(generatedKey)) {
        val validateSignature = checkExistingSignature(signature);
        var result: Int
        if (!validateSignature) {

            if (checkEmailAndServiceName(email, serviceName)) {
                result = updateUserInfo(
                    email, userId, partnerName, partnerId, serviceName,
                    eventType, requestId, timestamp,
                    signature
                )
                logger.info("User update response " + result)
                if (result != 0) {
                    call.respondOk(HttpStatusCode.OK, 200, "User info updated successfully")

                } else {
                    call.respondBadRequest("Failed to update User")
                }
            } else {
                result = insertCafeynUserInfo(
                    email, userId, partnerName, partnerId, serviceName,
                    eventType, requestId, timestamp,
                    signature
                )
                logger.info("User added response " + result)
                if (result != 0) {
                    call.respondOk(HttpStatusCode.OK, 200, "User info added successfully")
                } else {
                    call.respondBadRequest("Failed to add User")
                }
            }
        } else {
            logger.info("Signature exists already !!! ")
            call.respondBadRequest("Signature Exists Already")
        }
    } else {
        logger.info("Signature is invalid !!! ")
        call.respondBadRequest("Signature is invalid")
    }

}

private fun insertCafeynUserInfo(
    email: String, userId: Int, partnerName: String, partnerId: Int, serviceName: String,
    eventType: String, requestId: String, timestamp: String, signature: String
): Int {
    return transaction {

        CafeynUsersInfo.insert {
            it[CafeynUsersInfo.email] = email
            it[CafeynUsersInfo.userId] = userId
            it[CafeynUsersInfo.partnerName] = partnerName
            it[CafeynUsersInfo.partnerId] = partnerId
            it[CafeynUsersInfo.eventType] = eventType
            it[CafeynUsersInfo.requestId] = requestId
            it[CafeynUsersInfo.timestamp] = dateFormatter(timestamp)
            it[CafeynUsersInfo.signature] = signature
            it[CafeynUsersInfo.serviceName] = serviceName
        }
    }.insertedCount
}

private fun updateUserInfo(
    email: String, userId: Int, partnerName: String, partnerId: Int, serviceName: String,
    eventType: String, requestId: String, timestamp: String, signature: String
): Int {
    return transaction {

        CafeynUsersInfo.update({
            (CafeynUsersInfo.email.lowerCase() eq email.lowercase()) and
                    (CafeynUsersInfo.serviceName.lowerCase() eq serviceName.lowercase())
        }) {
            it[CafeynUsersInfo.userId] = userId
            it[CafeynUsersInfo.partnerName] = partnerName
            it[CafeynUsersInfo.partnerId] = partnerId
            it[CafeynUsersInfo.eventType] = eventType
            it[CafeynUsersInfo.requestId] = requestId
            it[CafeynUsersInfo.timestamp] = dateFormatter(timestamp)
            it[CafeynUsersInfo.signature] = signature
        }
    }
}

private fun checkExistingSignature(signature: String): Boolean {

    return transaction {
        CafeynUsersInfo.select { CafeynUsersInfo.signature.lowerCase() eq signature.lowercase() }.count() > 0
    }
}

private fun checkEmailAndServiceName(email: String, serviceName: String): Boolean {
    if (!emailValidation(email)) {
        throw BadRequestException("Invalid email format")
    }
    return transaction {
        CafeynUsersInfo.select {
            (CafeynUsersInfo.email.lowerCase() eq email.lowercase()) and
                    (CafeynUsersInfo.serviceName eq serviceName)
        }.count() > 0
    }
}

private fun Api5CafeynController.modifySubscription(
    currentUser: User?,
    nextBillDate: LocalDateTime,
    product: Products,
    token: String?
): Boolean {

    val recentSubscription = when (product) {
        Products.TV -> currentUser?.id?.let { databaseController.getRecentTVSubscription(it.toLong()) }
        Products.STORIES -> currentUser?.id?.let { databaseController.getRecentBOOKSubscription(it.toLong()) }
        Products.GAMES -> currentUser?.id?.let { databaseController.getRecentGAMESubscription(it.toLong()) }
        Products.ACCOUNTS -> currentUser?.id?.let { databaseController.subscription_getRecentActive(it.toLong()) }
    }

    if (recentSubscription != null) {
        return databaseController.updateCafeynSubscription(
            token,
            nextBillDate,
            recentSubscription.id
        )
    }
    return false
}

private fun Api5CafeynController.modifySubscriptionAllProducts(
    currentUser: User?,
    nextBillDate: LocalDateTime,
    token: String?,
    logPrefix: String,
    subType: SubscriptionRoot.SubscriptionType
): Boolean {

    val recentSubscription =
        currentUser?.id?.let { databaseController.subscription_getRecentActive(it.toLong()) }

    logger.info("$logPrefix User: $currentUser ,recentSubscription: $recentSubscription")

    if (recentSubscription != null) {
        return databaseController.updateCafeynSubscriptionType(
            token,
            nextBillDate,
            recentSubscription.id,
            subType.name
        )
    }
    return false
}

private suspend fun Api5CafeynController.redirect(
    generatedKey: String?,
    noRedirect: String?,
    url: String,
    call: ApplicationCall
) {
    val redirectUrl = utility.buildUrl(
        url,
        "",
        generatedKey.let { mapOf("key" to it.toString()) }
    )
    if (noRedirect?.toBoolean() == true) {
        call.respond(HttpStatusCode.OK, redirectUrl)
    } else {
        call.respondRedirect(redirectUrl, false)
    }
}

private fun Api5CafeynController.createSubscription(
    cafeynResponse: CafeynResponse,
    generatedKey: String,
    kidjoUserID: Long,
    product: Products,
    productSub: SubscriptionRoot.SubscriptionType
) {
    cafeynResponse.let {
        val subscriptionToken = "${it.userId}-${it.partnerId}-${it.partnerName}"
        val subscriptionRootInsert = SubscriptionRootInsert(
            userId = kidjoUserID,
            deviceId = 0L,
            isFreeTrail = false,
            priceToLogUSD = 0F,
            paymentType = SubscriptionRoot.PaymentType.NATIVE,
            subscriptionType = productSub,
            paymentId = Device.StorePlatform.CAFEYN.raw,
            platformPurchaseId = Device.StorePlatform.CAFEYN.raw,
            storeId = Device.StorePlatform.CAFEYN,
            paymentStateId = it.token,
            iapId = product.name,
            purchasingSessionId = "NOT_SET",
            subscriptionToken = subscriptionToken,
            subscriptionTokenHash = encryptionController.sha256Hash(subscriptionToken),
            accountCouponId = AccountCoupon.NO_SERVER_ID,
            isRenewing = true,
            nextBillDate = LocalDateTime.now()
                .plusDays(1),
            isTest = !config.env.isLive,
            operatorName = SubscriptionRoot.OPERATOR_WEB
        )
        databaseController.subscriptionRoot_create(subscriptionRootInsert)
        databaseController.insert_user_redirect_key(kidjoUserID, generatedKey)
    }
}


private fun Api5CafeynController.createUserForCafeyn(cafeynResponse: CafeynResponse): Long {
    val user = User.getEmptyUser()
    cafeynResponse.let {
        user.authType = User.AuthType.OAUTH
        user.oAuthType = User.OAuthType.CAFEYN
        user.email = it.email.toString()
        user.name = it.email.toString()
        user.authId = it.token.toString()
        user.isSubscribed = it.isActive
        user.countryId = 76 // france
    }
    return databaseController.user_register(user)
}

private fun generateSignature(
    requestId: String?,
    cafeynHubToken: String?,
    timestamp: String?,
    config: Config,
    product: Products
): String {
    // Compute the signature using the provided parameters and the shared secret key
    val thirdPartyName = when (product) {
        Products.TV -> config.cafeynTvPartnerName
        Products.STORIES -> config.cafeynStoriesPartnerName
        Products.GAMES -> config.cafeynGamesPartnerName
        Products.ACCOUNTS -> config.cafeynAccountsPartnerName
    }
    val key = when (product) {
        Products.TV -> config.cafeynTVSecretKey
        Products.STORIES -> config.cafeynStoriesSecretKey
        Products.GAMES -> config.cafeynGamesSecretKey
        Products.ACCOUNTS -> config.cafeynAccountsSecretKey
    }
    val message = "$requestId|$cafeynHubToken|$timestamp|$thirdPartyName"
    val hmac = javax.crypto.Mac.getInstance("HmacSHA256")
    val secretKey = javax.crypto.spec.SecretKeySpec(key.toByteArray(), "HmacSHA256")
    hmac.init(secretKey)
    val signatureBytes = hmac.doFinal(message.toByteArray())
    return java.util.Base64.getEncoder().encodeToString(signatureBytes)
}

private fun generateSignatureKey(
    requestId: String?,
    timestamp: String?,
    config: Config,
    serviceName: String
): String {
    // Compute the signature using the provided parameters and the shared secret key
    val thirdPartyName = when (serviceName) {
        "kidjo-tv" -> config.cafeynTvPartnerName
        "kidjo-stories" -> config.cafeynStoriesPartnerName
        else -> null
    }
    val key = when (serviceName) {
        "kidjo-tv" -> config.cafeynTVSecretKey
        "kidjo-stories" -> config.cafeynStoriesSecretKey
        else -> null
    }
    val message = "$requestId|$timestamp|$thirdPartyName"
    val hmac = javax.crypto.Mac.getInstance("HmacSHA256")
    val secretKey = javax.crypto.spec.SecretKeySpec(key?.toByteArray(), "HmacSHA256")
    hmac.init(secretKey)
    val signatureBytes = hmac.doFinal(message.toByteArray())
    return java.util.Base64.getEncoder().encodeToString(signatureBytes)
}
