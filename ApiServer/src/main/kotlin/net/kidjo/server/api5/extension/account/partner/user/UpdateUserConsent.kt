package net.kidjo.server.api5.extension.account.partner.user

import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.api5.extension.account.request.UserConsentData
import net.kidjo.server.shared.database.createOrUpdateUserCosnent
import net.kidjo.server.shared.extensions.getTrxUuid
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.UserConsent

suspend fun Api5UserController.updateUserConsent(call: ApplicationCall) {

    val logPrefix = "[Api5UserController.updateUserConsent]"

    var trxId = call.getTrxUuid()
    val userConsentIn = call.receive<UserConsentData>()
    logger.info("$logPrefix [$trxId] New user consent update request: $userConsentIn")

    if (userConsentIn.userId == 0L || userConsentIn.isTcAccepted == null || userConsentIn.isPromoAccepted == null) {
        logger.error("$logPrefix [$trxId] Bad Params: UserConsentData = $userConsentIn")
        return call.respondBadRequest("[$trxId] Bad Params")
    }

    val userConsent = UserConsent(
        0,
        userConsentIn.userId,
        userConsentIn.isTcAccepted,
        userConsentIn.isPromoAccepted
    )
    val result = databaseController.createOrUpdateUserCosnent(userConsent)

    //return call.respondOK("[$trxId] User consent $result")
    return call.respond(
        mapOf(
            "code" to "200",
            "transactionId" to trxId,
            "userId" to userConsentIn.userId,
            "message" to "User consent $result"
        )
    )
}