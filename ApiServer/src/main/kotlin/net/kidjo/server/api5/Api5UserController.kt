package net.kidjo.server.api5

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.Api4SubscriptionController
import net.kidjo.server.api4.Partner
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.api5.extension.account.partner.user.registration.registerAccount
import net.kidjo.server.api5.extension.account.partner.user.registration.registerMobile
import net.kidjo.server.api5.extension.account.partner.user.updateUserConsent
import net.kidjo.server.api5.extension.account.partner.user.verifyPartner
import net.kidjo.server.api5.extension.account.user.forgottenPass
import net.kidjo.server.api5.extension.account.user.login
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.database.v5.getDiscountDetails
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.User
import org.slf4j.LoggerFactory

class Api5UserController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api5UserController")
    internal val api4SubscriptionController = Api4SubscriptionController(platformInjector)

    override fun installRoutes(route: Route) {
        route.post("partners/verify-user") { <EMAIL>(call) }

        route.post("/accounts") {
            <EMAIL>(call)
        }
        route.post("/accounts/login") { <EMAIL>(false, call) }

        route.post("/updateUserConsent") { <EMAIL>(call) }
        route.get("/users/forgotten-pass") { <EMAIL>(call) }

        // Mobile registration
        route.post("/users") { <EMAIL>(call) }
        route.post("/users/login") { <EMAIL>(call = call) }


    }

    fun getUserCouponDto(couponId: String?): CouponDTO? {
        try {
            if (couponId == null) return null
            var couponDto: CouponDTO? = null
            val coupon = couponManager.getFromDatabase(couponId)
            if (coupon != null) {
                couponDto = CouponDTO(
                    coupon.id,
                    coupon.couponId,
                    coupon.durationCode,
                    coupon.groupId,
                    coupon.couponType,
                    coupon.productType,
                    coupon.partnerType,
                    when (coupon.couponType) {
                        AccountCouponType.DISCOUNT_COUPON.name, AccountCouponType.UNIQUE_DISCOUNT_COUPON.name ->
                            getDiscountDetails(couponId)

                        else -> null
                    }
                )
            }
            return couponDto
        } catch (e: Throwable) {
            logger.error("[Api5UserController.getUserCouponDto] Failed to update user country", e)
            throw e
        }
    }

    fun linkExternalUserSubscription(externalPartner: Partner?, userId: Long?): Boolean {
        try {
            if (externalPartner == null || userId == null) return false
            var hasSubscription = true
            when (externalPartner.partnerName) {
                Device.StorePlatform.ORANGE.raw -> {
                    val subscriptions = databaseController.virgo_subscription_geAllByAlias(externalPartner.userAlias)
                    subscriptions.forEach { it ->
                        if (it.userId == User.NO_SERVER_ID) {
                            if (!databaseController.linkUserToKidjoSubscription(userId, it.id)) {
                                hasSubscription = false
                                logger.error(
                                    "[Api5UserController.linkExternalUserSubscription] Error " +
                                            "linking user to Kidjo sub"
                                )
                            }
                            if (!databaseController.linkUserToVirgoSubscription(userId, externalPartner.userAlias)) {
                                logger.error(
                                    "[Api5UserController.linkExternalUserSubscription] " +
                                            "Error linking user to Virgo sub"
                                )
                            }
                        }
                    }
                }
            }
            return hasSubscription
        } catch (e: Throwable) {
            logger.error("[Api5UserController.linkExternalUserSubscription] Failed to link external user ", e)
            throw e
        }
    }

    fun validateAndLimitThePassword(
        password: String,
        currentUser: User
    ): Int {
        try {
            val passwordMatch = encryptionController.checkPassword(password, currentUser.hashedPassword)
            if (!passwordMatch) {
                databaseController.incrementWrongPasswordCount(currentUser.id.toLong())
                databaseController.checkLimitLoginThreshold(currentUser.id.toLong())
                return 1
            }
            if (databaseController.isLoginLimited(currentUser.id.toLong())) {
                return 2
            } else {
                databaseController.resetWrongPasswordCountAndLimitLoginAt(currentUser.id.toLong())
            }
        } catch (e: Throwable) {
            logger.error("Failed to validate user password", e)
            throw e
        }
        return 0
    }

    fun updateUserCountry(
        currentUser: User,
        call: ApplicationCall
    ) {
        try {
            if (currentUser.countryId == 0) {
                val countryId = countryCodeByGoogle.getCountryIdByIP(call)
                if (countryId != -1) {
                    databaseController.userUpdateCountry(currentUser.id.toLong(), countryId)
                    logger.info("Updated user with id=${currentUser.id} with their country [login session]")
                }
            }
        } catch (e: Throwable) {
            logger.error("Failed to update user country", e)
            throw e
        }
    }
}


data class RegisterWebDtoIn(
    val displayName: String?,
    val email: String?,
    val password: String?,
    val coupon: String?,
    val countryCode: String?,
    val env: String?,
    val externalPartner: Partner?,
    val isTcAccepted: Boolean,
    val isPromoAccepted: Boolean
)


data class RegisterMobileDtoIn(
    val displayName: String?,
    val email: String?,
    val password: String?,
    val countryCode: String?,
    val isTcAccepted: Boolean,
    val isPromoAccepted: Boolean
)