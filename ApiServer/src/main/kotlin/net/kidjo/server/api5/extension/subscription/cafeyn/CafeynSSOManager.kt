package net.kidjo.server.api5.extension.subscription.cafeyn

import net.kidjo.exceptions.SubscriptionException
import net.kidjo.server.api5.extension.account.SubscriptionStrategy
import net.kidjo.server.api5.extension.account.request.SubscriptionRequest
import net.kidjo.server.api5.extension.account.request.UnSubscriptionRequest
import net.kidjo.server.api5.extension.account.response.SubscriptionResponse
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getLatestUserSubscriptions
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.updateSubscription
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.tools.Config
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

class CafeynSSOManager(
    val config: Config,
    //val utility: Utility,
    //val encryptionController: EncryptionController,
    val databaseController: DatabaseController
) : SubscriptionStrategy {

    internal val logger = LoggerFactory.getLogger(CafeynSSOManager::class.java)
    override fun unSubscribe(user: User, unSubscriptionRequest: UnSubscriptionRequest) {
        TODO("Not yet implemented")
    }

    override fun subscribe(user: User, subscription: SubscriptionRequest, countryId: Int?): SubscriptionResponse {
        logger.info("[CafeynSSOManager.subscribe] SSOCafeyn Subscription START | User $user")

        // Check User currentSubscriptions
        val latestSubscription = databaseController.getLatestUserSubscriptions(user.getLongId())
        logger.info("[CafeynSSOManager.subscribe] User: $user => latestSubscription: $latestSubscription")

        return if (latestSubscription == null) {
            createSSOCafeynSubscription(subscription, user)
        } else {
            updateSSOCafeynSubscription(subscription, user, latestSubscription)
        }
    }


    @Throws(SubscriptionException::class)
    private fun createSSOCafeynSubscription(
        subscription: SubscriptionRequest,
        user: User,
    ): SubscriptionResponse {

        logger.info(
            "[CafeynSSOManager.createSSOCafeynSubscription] Kidjo Subscription START | User: $user " +
                    "| Sub request: $subscription"
        )
        val kidjoSubscription = SubscriptionRootInsert(
            userId = user.id.toLong(),
            deviceId = Device.NO_SERVER_ID,
            isFreeTrail = false,
            priceToLogUSD = 0.00f,
            paymentType = SubscriptionRoot.PaymentType.FREE,
            subscriptionType = getSubscriptionType(subscription),
            paymentId = "SSO_CAFEYN",
            platformPurchaseId = "SSO_CAFEYN",
            storeId = Device.StorePlatform.CAFEYN,
            paymentStateId = null,
            iapId = subscription.plans.planId,
            purchasingSessionId = "",
            subscriptionToken = "SSO_Cafeyn|${generateRandomString(10)}",
            subscriptionTokenHash = "NONE_SSO",
            accountCouponId = 0L,
            isRenewing = true,
            nextBillDate = LocalDateTime.now().plusDays(1),
            !config.env.isLive
        )
        logger.info(
            "[CafeynSSOManager.createSSOCafeynSubscription] Kidjo Subscription END | User: $user " +
                    "| Sub request: $subscription"
        )
        return SubscriptionResponse(databaseController.subscriptionRoot_create(kidjoSubscription).toString())
    }


    private fun updateSSOCafeynSubscription(
        subscription: SubscriptionRequest,
        user: User,
        latestSubscription: SubscriptionRoot
    ): SubscriptionResponse {
        logger.info(
            "[CafeynSSOManager.createSSOCafeynSubscription] Kidjo Update Subscription START | User: $user " +
                    "| Sub request: $subscription"
        )

        val updateSubscription = SubscriptionRootUpdateV1(
            userId = user.getLongId(),
            isRenewing = true,
            nextBillDate = LocalDateTime.now().plusDays(1),
            platformPurchaseId = "SSO_CAFEYN",
            isFreeTrail = false,
            priceToLogUSD = 0.00f,
            subId = latestSubscription.id,
            paymentStateId = null,
            iapId = subscription.plans.planId,
            accountCouponId = 0L
        )
        databaseController.updateSubscription(
            updateSubscription,
            SubscriptionRoot.SubscriptionType.fromRaw(subscription.plans.product)
        )

        logger.info(
            "[CafeynSSOManager.createSSOCafeynSubscription] Kidjo Update Subscription END | User: $user " +
                    "| Sub request: $subscription"
        )
        return SubscriptionResponse(latestSubscription.id.toString())
    }

    private fun getSubscriptionType(subscription: SubscriptionRequest): SubscriptionRoot.SubscriptionType {
        return SubscriptionRoot.SubscriptionType.fromRaw(subscription.plans.product)
    }

    private fun generateRandomString(length: Int): String {
        val allowedChars = ('A'..'Z') + ('a'..'z') + ('0'..'9')
        return (1..length)
            .map { allowedChars.random() }
            .joinToString("")
    }
}