package net.kidjo.server.api5.extension.account.partner.user.registration

import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api4.UserDtoOut
import net.kidjo.server.api4.extension.subscription.helper.createFreeAccessSubscription
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.api5.RegisterWebDtoIn
import net.kidjo.server.api5.extension.account.partner.user.helpers.createUser
import net.kidjo.server.api5.extension.account.partner.user.helpers.createUserConsent
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.User



const val logPrefix = "[Api5UserController.registerAccount]"
suspend fun Api5UserController.registerAccount(call: ApplicationCall) {

    val registerWebIn = call.receive<RegisterWebDtoIn>()

    logger.info("$logPrefix New account registration: $registerWebIn")

    if (registerWebIn.email.isNullOrEmpty() || registerWebIn.password.isNullOrEmpty()) {
        logger.error("$logPrefix Bad Params: $registerWebIn")
        return call.respondBadRequest("Bad Params")
    }

    if (!validator.isEmailValid(registerWebIn.email)) {
        logger.error("$logPrefix Wrong email: $registerWebIn")
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
    }

    val currentUser = databaseController.user_getByEmail(registerWebIn.email)
    if (currentUser != null) {
        logger.error("$logPrefix There is already such a User: $registerWebIn")
        return call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, "There is already such a User")
    }
    val user = User.getEmptyUser()

    user.authType = User.AuthType.EMAIL
    user.email = registerWebIn.email
    registerWebIn.displayName?.takeIf { it.isNotBlank() }?.let {
        user.name = it
    }
    user.hashedPassword = encryptionController.hashPassword(registerWebIn.password)
    user.countryId = countryCodeByGoogle.getCountryIdByIP(call)

    val isTcAccepted = if (registerWebIn.isTcAccepted) 1 else 0
    val isPromoAccepted = if (registerWebIn.isPromoAccepted) 1 else 0

    val couponDto = getUserCouponDto(registerWebIn.coupon)
    if (registerWebIn.coupon!!.isNotBlank()) {
        val coupon = couponManager.getFromDatabase(registerWebIn.coupon)
        return if (coupon == null || !coupon.isValid()) {
            logger.error("$logPrefix Failed to register user: coupon invalid or expired !!")
            call.respondBadRequest(
                UsersErrors.ERROR_INVALID_COUPON,
                "This coupon is invalid or expired."
            )
        } else {
            user.id = createUser(user, call)
            logger.info("$logPrefix DB User created with ID: ${user.id} ,User: $user.")

            createUserConsent(user.id, isTcAccepted, isPromoAccepted)

            user.authToken = jwtManager.generateAccessToken(user.id)
            braintreeManager.createCustomer(user)
            api4SubscriptionController.createFreeAccessSubscription(coupon, user.getLongId(), call)

            call.respondOK(
                UserDtoOut(
                    user.id,
                    couponDto,
                    false,
                    user.authToken
                )
            )
        }
    } else {
        user.id = createUser(user, call)
        logger.info("$logPrefix DB User created with ID: ${user.id} ,User: $user.")

        createUserConsent(user.id, isTcAccepted, isPromoAccepted)

        user.authToken = jwtManager.generateAccessToken(user.id)
        braintreeManager.createCustomer(user)
        val hasSubscription = linkExternalUserSubscription(registerWebIn.externalPartner, user.getLongId())

        return call.respondOK(
            UserDtoOut(
                user.id,
                couponDto,
                hasSubscription,
                user.authToken
            )
        )
    }
}