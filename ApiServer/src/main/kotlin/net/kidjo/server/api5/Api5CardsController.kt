package net.kidjo.server.api5

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api5.extension.cards.listCards
import net.kidjo.server.api5.extension.cards.searchCardsV5
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import org.slf4j.LoggerFactory

class Api5CardsController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api5CardsController")
    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)
    override fun installRoutes(route: Route) {
        route.get("content/cards") { <EMAIL>(call) }
        route.get("content/cards/search") { this@Api5CardsController.searchCardsV5(call) }
    }
}
