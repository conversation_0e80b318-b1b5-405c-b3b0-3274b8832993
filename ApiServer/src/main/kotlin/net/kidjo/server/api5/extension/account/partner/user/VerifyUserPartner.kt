package net.kidjo.server.api5.extension.account.partner.user

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.server.application.*
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.api5.extension.account.partner.enum.Partner
import net.kidjo.server.shared.extensions.receiveJSON
import net.kidjo.server.shared.extensions.respondForbidden
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.SubscriptionStatusType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.subscription.update.modifyKIDJOSubscriptionByDVStatus
import net.kidjo.server.shared.payments.publichers.v5.enums.OrangeOperators

suspend fun Api5UserController.verifyPartner(call: ApplicationCall) {

     val requestJSON = call.receiveJSON()?.toString()

     val verifyPartnerRequest =
          ObjectMapper().
          disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).
          readValue(
               requestJSON,
               PartnerVerifyRequest::class.java
          )

     try {

      val oprInfoResponse =
           digitalvirgoApiManager.
           operation.getDVPASSInfo(
                   verifyPartnerRequest.user?.subscriptionId,
                   verifyPartnerRequest.user?.operationId
           ) ?:
           return call.respondForbidden("The DV Operation Subscription is not recognized - subId:${verifyPartnerRequest.user?.subscriptionId}, opeId:${verifyPartnerRequest.user?.operationId} ")

          val subIndent = oprInfoResponse.getSubIndent()
          logger.info("subIndent : $subIndent ")

          val userIndent = oprInfoResponse.getUserIndent()
          logger.info("userIndent : $userIndent ")

          val msisdn = oprInfoResponse.data?.user?.msisdn
          logger.info("msisdn: $msisdn ")

          val subCountry = oprInfoResponse.data?.country
          logger.info("subCountry: $subCountry ")

          val userLocale = oprInfoResponse.data?.user?.locale
          logger.info("userLocale: $userLocale ")

          val offerId = oprInfoResponse.data?.offer?.id
          logger.info("offerId: $offerId ")

          val packageId = oprInfoResponse.data?.dvPackage?.id
          logger.info("packageId: $packageId ")

          val correlationIdString = oprInfoResponse.data?.correlationId
          logger.info("correlationIdString: $correlationIdString ")

          val subStatus = oprInfoResponse.data?.subscription?.status
          logger.info("subStatus: $subStatus ")

         val subType = oprInfoResponse.data?.type
         logger.info("subType: $subType ")

         val nextBillingDate = oprInfoResponse.getNextBillingDate()
         logger.info("nextBillingDate: $nextBillingDate ")

         val mccmnc = oprInfoResponse.getMCCMNC()
         logger.info("mccmnc: $mccmnc ")

         var operatorName = oprInfoResponse.getOperatorName()
         if (mccmnc.toString().equals("60801")) {
             operatorName = "ORANGE_SENEGAL"
         } else if (mccmnc.toString().equals("61203")) {
             operatorName = "ORANGE_IVORY_COAST"
         }
         logger.info("operatorName: $operatorName ")

         if (
             subIndent.isNullOrBlank() ||
             subCountry.isNullOrBlank() ||
             userIndent.isNullOrBlank()) {
             logger.error("Validating Usert didn't have right information" +
                     "(type, operationId, user:msisdn,alias, subscription: subsCountry )," +
                     " $oprInfoResponse")
             return
         }
         if(verifyPartnerRequest.getPartner() != Partner.ORANGE){
             logger.info("EXIT UNSUPPORTED PARTNER: $verifyPartnerRequest.getPartner() ")
             return call.respondForbidden("The Partner:${verifyPartnerRequest.name} is Not ORANGE! ")
         }
//         if(operatorName != OrangeOperators.ORANGE_MOROCCO.name || operatorName != OrangeOperators.ORANGE_SENEGAL.name){
//             logger.info("EXIT UNSUPPORTED OPERATOR: $operatorName ")
//             return call.respondForbidden("The User Operator: $operatorName For Partner:${verifyPartnerRequest.name}  Not Found! ")
//         }

            val userId = modifyKIDJOSubscriptionByDVStatus(
                       subIndent = subIndent,
                       userIndent = userIndent,
                       msisdn = msisdn,
                       subCountryCode = subCountry,
                       operatorName = operatorName,
                       userLocale = userLocale?: "MA",
                       dvNextBillingDate = nextBillingDate,
                       offerId = offerId?: 0,
                       packageId = packageId ?: 0,
                       correlationId = correlationIdString,
                       subStatus = subStatus?: SubscriptionStatusType.NONE.raw,
                       dvSubscription = digitalvirgoApiManager.subscription,
                       databaseController = databaseController
                   ) ?: return call.respondForbidden("The End User for Partner: ${verifyPartnerRequest.name} is UNSUBSCRIBED: Subscription id:$subIndent Status is:$subStatus ")

                   val jwt = userManager.generateAndSetUpUserAccessToken(userId)

                   return call.respondOK(mapOf("token" to jwt,"message" to "The User for Partner:${verifyPartnerRequest.getPartner()} has a Subscription id:$subIndent with status: $subStatus"))
     } catch (e: Throwable) {
          logger.error("Failed to generate partner user JWT", e)
          return call.respondForbidden("There is a problem validating the partner user: $e ")
     }
}

@JsonIgnoreProperties(ignoreUnknown=true)
data class PartnerVerifyRequest (
     var name          : String?      = null,
     var operator      : String?       = null,
     var user          : UserPartner?  = UserPartner()
) {
     fun getPartner() = getPartnerByName(name)
          companion object {
          private fun getPartnerByName(name :String?): Partner {
              return Partner.getByName(name)
          }
     }
}
@JsonIgnoreProperties(ignoreUnknown=true)
data class UserPartner (
     var id    : String? = null,
     var subscriptionId   : String? = null,
     var operationId  : String? = null,
     var alias        : String? = null
)
