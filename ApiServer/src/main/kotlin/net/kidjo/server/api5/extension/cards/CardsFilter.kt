package net.kidjo.server.api5.extension.cards

import io.ktor.http.ContentType
import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.acceptLanguage
import io.ktor.server.response.respondText
import net.kidjo.common.models.Folder
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.api5.Api5CardsController
import net.kidjo.server.shared.database.videoSearchV5
import net.kidjo.server.shared.extensions.getInt
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.tools.RequestUtil
import org.json.JSONArray
import org.json.JSONObject


private const val CARDS_PER_SECTION = 20
private const val MAX_CARDS_PER_CALL = 100
private const val DEFAULT_CARDS_PER_CALL = 24

suspend fun Api5CardsController.searchCardsV5(call: ApplicationCall) {

    val json = JSONObject()

    val acceptLanguageRaw = call.request.acceptLanguage() ?: "en"
    val queryParameters = call.parameters
    val cardLimit = kotlin.math.min(queryParameters.getInt("limit", DEFAULT_CARDS_PER_CALL), MAX_CARDS_PER_CALL)
    val cardOffset = queryParameters.getInt("offset", 0)
    val folderType = Folder.ContentType.FromRaw(queryParameters.getString("contentType"))
    var search = queryParameters.getString("search", "")
    val kidAge = queryParameters.getInt("age")
    val filterFolders = queryParameters.getString("excludeFolderIds") as? String ?: ""
    val countryId: Int = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw) ?: 237
    val languageId = languageCache.getLanguageIdFromAcceptLanguageHeader(acceptLanguageRaw)
    if (search.contains(folderType.raw)) {
        search = search.replace(folderType.raw, "")
    }
    val flag_onlyFolders = true
    val videosJson = JSONArray()
    val soundPerSection = 2
    val mixedGamePerSection = 2
    //sound card at position 1 and at 8 if there are two
    //mixedGamePerSection card at position 9 and 4 if there are two
    var requestCardsAmount = cardLimit
    var requestCardOffset = cardOffset

    if (flag_onlyFolders) {
        val numberOfSoundCardsSoFar = numberOfSoundCardsUpTo(cardOffset, soundPerSection)
        val numberOfMixedGameCardsSoFar = numberOfMixedGameCardsUpTo(cardOffset, mixedGamePerSection)
        val extraCardsSoFar = numberOfSoundCardsSoFar + numberOfMixedGameCardsSoFar
        requestCardOffset -= extraCardsSoFar

        val numberOfSoundCardsAdded =
            numberOfSoundCardsUpTo(cardOffset + cardLimit, soundPerSection) - numberOfSoundCardsSoFar
        val numberOfMixedGameCardsAdded =
            numberOfMixedGameCardsUpTo(cardOffset + cardLimit, mixedGamePerSection) - numberOfMixedGameCardsSoFar
        val otherCardsToBeAdded = numberOfSoundCardsAdded + numberOfMixedGameCardsAdded
        requestCardsAmount -= otherCardsToBeAdded
    }
    val videos =
        databaseController.videoSearchV5(
            requestCardsAmount,
            requestCardOffset, search, languageId, countryId, kidAge, folderType, filterFolders, true
        )

    if (flag_onlyFolders) {
        var folderIndex = 0
        for (i in 0 until cardLimit) {
            if (folderIndex < videos.size) {
                            val video = videos[folderIndex]
                            videosJson.put(Api3CardsController.createVideoJson(video, true))
                            folderIndex++
                        } else
                            break
                    }
                }
                json.put("videos", videosJson)
                call.respondText(json.toString(), ContentType.Application.Json)
            }
