package net.kidjo.server.api5.extension.account.braintree

import com.braintreegateway.BraintreeGateway
import com.braintreegateway.Environment

object BrainTreeClient {
    private var useSandbox: Boolean = false
    private var merchantAccountId: String = ""
    private var publicKey: String = ""
    private var privateKey: String = ""

    val gateway: BraintreeGateway by lazy {
        if (useSandbox) {
            BraintreeGateway(
                Environment.SANDBOX,
                merchantAccountId,
                publicKey,
                privateKey
            )
        } else {
            BraintreeGateway(
                Environment.PRODUCTION,
                merchantAccountId,
                publicKey,
                privateKey
            )
        }
    }

    fun initialize(
        useSandbox: Boolean,
        merchantAccountId: String,
        publicKey: String,
        privateKey: String,
    ) {
        this.useSandbox = useSandbox
        this.merchantAccountId = merchantAccountId
        this.publicKey = publicKey
        this.privateKey = privateKey
    }
}
