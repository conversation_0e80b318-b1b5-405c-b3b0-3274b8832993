package net.kidjo.server.api5.extension.account.user

import io.ktor.server.application.*
import io.ktor.util.*
import net.kidjo.server.api5.Api5UserController
import net.kidjo.server.shared.database.getPartnerByUserId
import net.kidjo.server.shared.database.userUpdatePassword
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.*
import kotlin.text.toCharArray


suspend fun Api5UserController.forgottenPass(call: ApplicationCall) {

    val logPrefix = "[Api5UserController.forgottenPass]"

    val email = call.parameters.getString("email")
    return if (email.isBlank()) {
        logger.error("$logPrefix Empty email, call: $call")
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Empty email")

    } else if (!email.isEmail()) {
        logger.error("$logPrefix Bad email, call: $call")
        call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Bad email")

    } else {
        // Generate a new password
        val password = utility.randomString(8, "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray())
        val preHashedPassword = encryptionController.sha256Hash(password + config.clientSideHashV1Salt)
        val hashedPassword = encryptionController.hashPassword(preHashedPassword)

        // Save the new password for the corresponding user and send an email
        val currentUser = databaseController.user_getByEmail(email)
        return if (currentUser != null) {

            val userPartner =
                databaseController.getPartnerByUserId(currentUser.id.toLong()).toUpperCasePreservingASCIIRules()
            logger.info("$logPrefix User $currentUser, belongs to partner $userPartner")

            return if (!emailManager.sendResetPasswordEmailByPartner(
                    currentUser.email,
                    password,
                    languageCache.get(call),
                    userPartner
                )
                || (!databaseController.userUpdatePassword(currentUser.getLongId(), hashedPassword))
            ) {
                call.respondBadRequest(UsersErrors.ERROR_INVALID_COUPON, "Problem updating user's password")
            } else {
                call.respondOK(Success.SUCCESS, "An email has been sent to '${currentUser.email}' if it exists")
            }
        } else {
            call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "There is no such a User.")
        }
    }
}