package net.kidjo.server.api5.extension.account.braintree

import com.braintreegateway.*
import com.braintreegateway.Subscription
import com.braintreegateway.Subscription.Status
import kotlinx.coroutines.*
import net.kidjo.exceptions.CouponExpiredException
import net.kidjo.exceptions.SubscriptionException
import net.kidjo.server.api5.extension.account.SubscriptionStrategy
import net.kidjo.server.api5.extension.account.request.Plan
import net.kidjo.server.api5.extension.account.request.SubscriptionRequest
import net.kidjo.server.api5.extension.account.request.SubscriptionUpdateData
import net.kidjo.server.api5.extension.account.request.UnSubscriptionRequest
import net.kidjo.server.api5.extension.account.response.SubscriptionResponse
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.database.v5.getCouponDetails
import net.kidjo.server.shared.database.v5.getDiscountDetails
import net.kidjo.server.shared.database.v5.getPlanType
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.util.*
import net.kidjo.server.utils.getEndOfDay

private const val DISCOUNT_CODE = "DISCOUNT"

class BrainTreeManager(
    val config: Config,
    val utility: Utility,
    val encryptionController: EncryptionController,
    val databaseController: DatabaseController,
) : SubscriptionStrategy {
    internal val logger = LoggerFactory.getLogger(BrainTreeManager::class.java)

    companion object {
        const val MAX_NAME_LENGTH_BRAIN_TREE = 250
    }

    init {
        BrainTreeClient.initialize(
            config.brainTree_useSandbox,
            config.brainTree_merchandId,
            config.brainTree_publicKey,
            config.brainTree_privateKey
        )
    }

    private val braintreeGateway = BrainTreeClient.gateway


    override fun subscribe(user: User, subscription: SubscriptionRequest, countryId: Int?): SubscriptionResponse {

        logger.info("[BraintreeManager.subscribe] Braintree Subscription START | User $user")

        //Check if provided coupons is valid
        val couponsDetails = subscription.coupon?.let { getCouponDetails(it) }
        if (couponsDetails != null && LocalDateTime.now().isAfter(couponsDetails.expireDate)) {
            logger.error("[BraintreeManager.subscribe] Braintree Subscription Failed due to coupon expiration | User $user")
            throw CouponExpiredException("${subscription.coupon} Coupon is expired. Try with valid coupon")
        }
        val paymentStatus = setPaymentDefaultMethod(
            user, subscription
        )
        if (!paymentStatus) {
            logger.error("[BraintreeManager.subscribe] Braintree Subscription Failed due to Payment Error | User $user")
            throw SubscriptionException("Payment Error. Please try again")
        }
        val customer = braintreeGateway.customer().find(user.brainTreeId)
            ?: throw SubscriptionException("Customer is not exists in Braintree")

        logger.info("[BraintreeManager.subscribe] User: $user => Corresponding customer: ${customerToString(customer)}")

        // Check User currentSubscriptions
        val latestSubscription = databaseController.getLatestUserSubscriptions(user.getLongId())
        logger.info("[BraintreeManager.subscribe] User: $user => latestSubscription: $latestSubscription")
        if (latestSubscription == null) {
            return createBrainTreeSubscription(subscription, couponsDetails, customer, user)
        } else {
            val currentPlanType = getPlanType(latestSubscription.iapId)
            val requestedPlanType = getPlanType(subscription.plans.planId)
            // Changing Plan Type
            if (currentPlanType != requestedPlanType) {
                cancelBrainTreeSubscription(latestSubscription.platformPurchaseId, latestSubscription.id)
                return createBrainTreeSubscription(subscription, couponsDetails, customer, user)
            } else if (Device.StorePlatform.FREE_ACCESS_COUPON === latestSubscription.storeId) {
                return createBrainTreeSubscriptionWithFreeAccess(
                    latestSubscription,
                    subscription,
                    customer,
                    user,
                    couponsDetails
                )
            } else {
                latestSubscription.platformPurchaseId.let {
                    val allActiveSubscription = databaseController.getUserSubscriptions(user.getLongId())
                    if (allActiveSubscription.size > 1) {
                        val firstBillingDate = allActiveSubscription.maxByOrNull { it.nextBillDate }
                        allActiveSubscription.filter { Device.StorePlatform.KIDJO_BRAINTREE == it.storeId }.forEach {
                            cancelBrainTreeSubscription(it.platformPurchaseId, it.id)
                        }
                        return createBrainTreeSubscription(
                            subscription,
                            couponsDetails,
                            customer,
                            user,
                            firstBillingDate?.nextBillDate
                        )
                    } else {
                        val userSubscription = braintreeGateway.subscription().find(it)
                        if (Status.CANCELED == userSubscription.status || Status.EXPIRED == userSubscription.status) {
                            cancelBrainTreeSubscription(
                                latestSubscription.platformPurchaseId,
                                latestSubscription.id
                            )
                            return createBrainTreeSubscription(
                                subscription,
                                couponsDetails,
                                customer,
                                user,
                                latestSubscription.nextBillDate
                            )
                        }
                        return updateBrainTreeSubscription(
                            subscription,
                            couponsDetails,
                            customer,
                            it,
                            latestSubscription,
                            user
                        )
                    }
                }
            }
        }
    }

    /**
     * Update the braintree subscription
     */
    private fun updateBrainTreeSubscription(
        subscription: SubscriptionRequest,
        couponsDetails: AccountCoupon?,
        customer: Customer,
        it: String,
        latestSubscription: SubscriptionRoot,
        user: User,
    ): SubscriptionResponse {

        logger.info("[BraintreeManager.updateBrainTreeSubscription] Update Braintree Subscription START | User $user")
        val braintreeSubscriptionRequest = updateBrainTreeSubscriptionRequest(
            subscription.plans, couponsDetails, customer, it
        )
        val brainTreeSubscription = braintreeGateway.subscription().update(
            it, braintreeSubscriptionRequest
        )
        if (brainTreeSubscription.isSuccess) {
            logger.info(
                "[BraintreeManager.updateBrainTreeSubscription] braintree subscriptions {}",
                brainTreeSubscription.target
            )
            updateSubscription(
                latestSubscription,
                brainTreeSubscription.target,
                user,
                subscription,
                couponsDetails?.id
            )

        } else {
            logger.error(
                "Unable to find the subscription for user {} in brain tree {}",
                user.id,
                brainTreeSubscription.errors.allDeepValidationErrors.map {
                    "${it.code}-${it.message}"
                }.joinToString()
            )
            throw SubscriptionException(
                "Unable to find the subscription for user ${user.id}",
                Throwable(
                    brainTreeSubscription
                        .errors
                        .allDeepValidationErrors
                        .map {
                            "${it.code}-${it.attribute}-${it.message}"
                        }.joinToString(",")
                )
            )
        }
        return SubscriptionResponse(latestSubscription.id.toString())
    }

    private fun createBrainTreeSubscriptionWithFreeAccess(
        latestSubscription: SubscriptionRoot,
        subscription: SubscriptionRequest,
        customer: Customer,
        user: User,
        couponsDetails: AccountCoupon?,
    ): SubscriptionResponse {
        val firstBillingDate = Calendar.getInstance()
        firstBillingDate.set(Calendar.MILLISECOND, latestSubscription.nextBillDate.nano)
        val subscriptionRequest = SubscriptionRequest()
            .planId(subscription.plans.planId)
            .paymentMethodToken(customer.defaultPaymentMethod.token)
            .firstBillingDate(firstBillingDate)
        if (subscription.plans.addOns != null) {
            val addOns = subscriptionRequest.addOns()
            addOns.add().neverExpires(true).inheritedFromId(subscription.plans.addOns.addonName)
            addOns.done()
        }
        addDiscount(couponsDetails, subscriptionRequest)
        val result = braintreeGateway.subscription().create(subscriptionRequest)
        if (result.isSuccess) {
            databaseController.subscription_cancelFull(latestSubscription.id)
            val existingAccountCoupon = databaseController.getAccountCouponId(latestSubscription.accountCouponId)
            return createKidjoSubscription(existingAccountCoupon, result.target, customer, user, subscription)
        } else {
            throw SubscriptionException(
                result
                    .errors
                    .allDeepValidationErrors
                    .map {
                        "${it.code}-${it.attribute}-${it.message}"
                    }.joinToString(",")
            )
        }
    }

    @Throws(SubscriptionException::class)
    private fun createBrainTreeSubscription(
        subscription: SubscriptionRequest,
        couponsDetails: AccountCoupon?,
        customer: Customer,
        user: User,
        firstBillingDate: LocalDateTime? = null,
    ): SubscriptionResponse {
        val braintreeSubscriptionRequest = createBrainTreeSubscriptionRequest(
            subscription,
            couponsDetails,
            customer,
            firstBillingDate
        )
        val results = braintreeGateway
            .subscription()
            .create(braintreeSubscriptionRequest)
        if (results.isSuccess) {
            return createKidjoSubscription(
                couponsDetails, results.target, customer, user, subscription
            )
        } else {
            logger.error("Unable to create Subscription {}", results.errors.allDeepValidationErrors.map {
                "${it.code} -${it.message}"
            }.joinToString())
            throw SubscriptionException(results.errors.allDeepValidationErrors.map {
                "${it.code} -${it.message}"
            }.joinToString())

        }
    }


    override fun unSubscribe(user: User, unSubscriptionRequest: UnSubscriptionRequest) {
        val userSubscription = databaseController.getUserSubscriptions(user.getLongId())
        if (userSubscription.isEmpty()) {
            throw SubscriptionException("User doesnt not have any active subscription -${user.id}")
        } else {
            cancelBrainTreeSubscription(
                userSubscription.first().platformPurchaseId,
                userSubscription.first().id
            )
        }

    }

    /**
     * Create Payment method for subscriber in braintree
     */
    private fun setPaymentDefaultMethod(user: User, subscription: SubscriptionRequest): Boolean {
        val addPaymentRequest =
            PaymentMethodRequest().paymentMethodNonce(subscription.paymentNonce).customerId(user.brainTreeId).options()
                .failOnDuplicatePaymentMethod(false).verifyCard(true).makeDefault(true)
                .done()
        if (subscription.deviceData != "") addPaymentRequest.deviceData(subscription.deviceData)

        val name = if (subscription.billingName != null) {
            val size = kotlin.math.min(MAX_NAME_LENGTH_BRAIN_TREE, subscription.billingName.length)
            subscription.billingName.substring(0, size)
        } else {
            user.name
        }
        addPaymentRequest.cardholderName(name)

        val addPayment = braintreeGateway.paymentMethod().create(addPaymentRequest)
        if (!addPayment.isSuccess) {
            if (addPayment.creditCardVerification != null) {
                logger.error(addPayment.creditCardVerification.processorResponseText)
            } else {
                logger.error(addPayment.message)
            }
            return false
        }
        return true
    }

    /**
     * Create SubscriptionRequest based on the plan
     */

    private fun createBrainTreeSubscriptionRequest(
        subscription: SubscriptionRequest,
        couponsDetails: AccountCoupon?,
        customer: Customer,
        firstBillingDate: LocalDateTime? = null,
    ): com.braintreegateway.SubscriptionRequest {
        val subscriptionRequest = SubscriptionRequest().planId(subscription.plans.planId)
            .paymentMethodToken(customer.defaultPaymentMethod.token)
        if (firstBillingDate != null) {
            val billingDate = Calendar.getInstance()
            billingDate.set(Calendar.MILLISECOND, firstBillingDate.nano)
            subscriptionRequest.firstBillingDate(billingDate)
        }

        if (subscription.plans.addOns != null) {
            val addOns = subscriptionRequest.addOns()
            addOns.add().neverExpires(true).inheritedFromId(subscription.plans.addOns.addonName)
            addOns.done()
        }
        applyCoupon(couponsDetails, subscriptionRequest)
        return subscriptionRequest
    }

    private fun updateBrainTreeSubscriptionRequest(
        plan: Plan,
        couponsDetails: AccountCoupon?,
        customer: Customer,
        braintreeId: String,
    ): com.braintreegateway.SubscriptionRequest {
        val userSubscription = braintreeGateway.subscription().find(braintreeId)
        val request = SubscriptionRequest()
            .id(userSubscription.id)
            .planId(plan.planId)
            .paymentMethodToken(customer.defaultPaymentMethod.token)
        if (plan.addOns != null) {
            val addOn = request.addOns()
            var existing = false
            userSubscription.addOns.forEach {
                if (!it.id.equals(plan.addOns.addonName)) {
                    addOn.remove(it.id)
                } else {
                    existing = true
                }
            }
            if (!existing) {
                addOn.add().inheritedFromId(plan.addOns.addonName)
            }
            addOn.done()
        } else {
            val addOn = request.addOns()
            userSubscription.addOns.forEach {
                addOn.remove(it.id)
            }
        }
        // Apply Coupon
        applyCoupon(couponsDetails, request)
        return request
    }

    /**
     * Apply Coupon to subscription
     * in this only free trial coupon are applied
     */
    private fun applyCoupon(
        couponsDetails: AccountCoupon?,
        request: com.braintreegateway.SubscriptionRequest,
    ) {
        if (couponsDetails != null) {
            when (AccountCouponType.fromName(couponsDetails.couponType)) {
                AccountCouponType.UNIQUE_DISCOUNT_COUPON,
                AccountCouponType.DISCOUNT_COUPON,
                -> {
                    addDiscount(couponsDetails, request)
                }

                else -> {
                    val freeTrialTime = utility.getDayMonthFromFreeTrialISO8601Code(couponsDetails.durationCode)
                    request.trialDuration(freeTrialTime.toInt())
                    request.trialDurationUnit(
                        if (couponsDetails.durationCode.contains("M"))
                            Subscription.DurationUnit.MONTH
                        else Subscription.DurationUnit.DAY
                    )

                }
            }
        }
    }

    /**
     * Add Discounts coupon to the subscription request
     */
    private fun addDiscount(
        couponsDetails: AccountCoupon?,
        request: com.braintreegateway.SubscriptionRequest,
    ) {
        if (couponsDetails != null
            && (AccountCouponType.DISCOUNT_COUPON
                    == AccountCouponType.fromName(couponsDetails.couponType)
                    ||
                    AccountCouponType.UNIQUE_DISCOUNT_COUPON
                    == AccountCouponType.fromName(couponsDetails.couponType))
        ) {
            val discountCouponDetails = getDiscountDetails(couponsDetails.couponId)
            fun discountDetails() = discountCouponDetails

            discountDetails()?.let {
                val freeTrialTime = utility.getDayMonthFromFreeTrialISO8601Code(couponsDetails.durationCode)
                val discounts = request.discounts()
                    .add()
                    .inheritedFromId(it.brainTreeDiscountName)
                    .numberOfBillingCycles(freeTrialTime.toInt())
                    .amount(it.discountPrice)
                discounts.done()
            }
        }
    }

    /**
     * Create Kidjo Subscriptions after braintree subscriptions
     */
    private fun createKidjoSubscription(
        couponsDetails: AccountCoupon?,
        brainTreeSubscription: Subscription,
        customer: Customer,
        user: User,
        subscription: SubscriptionRequest,
    ): SubscriptionResponse {

        logger.info("[BraintreeManager.createKidjoSubscription] Kidjo Subscription START | User: $user | Sub request: $subscription")

        if (couponsDetails != null) {
            databaseController.accountCoupon_consumeCoupon(couponsDetails.id)
        }

        val nextBillingDate = getEndOfDay(brainTreeSubscription.nextBillingDate)
        logger.info(
            "[BraintreeManager.createKidjoSubscription] Customer ${customer.email} " +
                    "|| Setting nextBillingDate from ${brainTreeSubscription.nextBillingDate.toInstant()} to $nextBillingDate"
        )

        val kidjoSubscription = SubscriptionRootInsert(
            userId = user.id.toLong(),
            deviceId = Device.NO_SERVER_ID,
            isFreeTrail = couponsDetails != null,
            priceToLogUSD = 0.00f,
            paymentType = if (customer.defaultPaymentMethod is PayPalAccount) SubscriptionRoot.PaymentType.PAY_PAL
            else if (customer.defaultPaymentMethod is AndroidPayCard) SubscriptionRoot.PaymentType.GOOGLE_PAY
            else SubscriptionRoot.PaymentType.CREDIT_CARD,
            subscriptionType = getSubscriptionType(subscription),
            paymentId = if (customer.defaultPaymentMethod is PayPalAccount) {
                (customer.defaultPaymentMethod as PayPalAccount).email
            } else if (customer.defaultPaymentMethod is AndroidPayCard) {
                (customer.defaultPaymentMethod as AndroidPayCard).sourceCardLast4
            } else {
                (customer.defaultPaymentMethod as CreditCard).last4
            },
            platformPurchaseId = brainTreeSubscription.id,
            storeId = Device.StorePlatform.KIDJO_BRAINTREE,
            paymentStateId = null,
            iapId = subscription.plans.planId,
            purchasingSessionId = "",
            subscriptionToken = brainTreeSubscription.id,
            subscriptionTokenHash = encryptionController.sha256Hash(brainTreeSubscription.id),
            accountCouponId = couponsDetails?.id ?: 0L,
            isRenewing = true,
            nextBillDate = LocalDateTime.ofInstant(
                nextBillingDate,
                brainTreeSubscription.nextBillingDate.timeZone.toZoneId()
            ),
            !config.env.isLive
        )

        logger.info("[BraintreeManager.createKidjoSubscription] Kidjo Subscription END | User: $user | Sub request: $subscription")
        return SubscriptionResponse(databaseController.subscriptionRoot_create(kidjoSubscription).toString())
    }

    private fun getSubscriptionType(subscription: SubscriptionRequest): SubscriptionRoot.SubscriptionType {
        return SubscriptionRoot.SubscriptionType.fromRaw(subscription.plans.product)
    }


    private fun updateSubscription(
        latestSubscription: SubscriptionRoot,
        brainTreeSubscription: Subscription,
        user: User,
        subscription: SubscriptionRequest,
        couponId: Long?,
    ): SubscriptionResponse {

        logger.info("[BraintreeManager.updateSubscription] Kidjo Subscription Update START | User: $user | Sub request: $subscription")

        val nextBillingDate = getEndOfDay(brainTreeSubscription.nextBillingDate)
        logger.info(
            "[BraintreeManager.updateSubscription] User $user " +
                    "|| Setting nextBillingDate from ${brainTreeSubscription.nextBillingDate} to $nextBillingDate"
        )

        val updateSubscription = SubscriptionRootUpdateV1(
            userId = user.getLongId(),
            isRenewing = true,
            nextBillDate = LocalDateTime.ofInstant(
                //brainTreeSubscription.nextBillingDate.toInstant(),
                nextBillingDate,
                brainTreeSubscription.nextBillingDate.timeZone.toZoneId()
            ),
            platformPurchaseId = brainTreeSubscription.id,
            isFreeTrail = false,
            priceToLogUSD = 0.00f,
            subId = latestSubscription.id,
            paymentStateId = brainTreeSubscription.id,
            iapId = subscription.plans.planId,
            accountCouponId = if (couponId == null) latestSubscription.accountCouponId else couponId
        )
        databaseController.updateSubscription(
            updateSubscription,
            SubscriptionRoot.SubscriptionType.fromRaw(subscription.plans.product)
        )
        return SubscriptionResponse(latestSubscription.id.toString())
    }


    /**
     * Cancel subscription in brain tree
     */
    fun cancelBrainTreeSubscription(braintreeSubscriptionId: String, subscriptionId: Long) {
        val subscription = braintreeGateway.subscription().find(braintreeSubscriptionId)
        if (Status.ACTIVE === subscription.status || Status.PENDING === subscription.status) {
            val results = braintreeGateway.subscription().cancel(braintreeSubscriptionId)
            if (results.isSuccess) {
                databaseController.subscription_cancelFull(subscriptionId)
            } else {
                logger.error("Unable to Cancel the subscriptions in braintree -{}", results.errors)
                throw SubscriptionException(
                    "Unable to cancel the subscription for subscription id-$subscriptionId"
                            + results.errors.allDeepValidationErrors.map {
                        "${it.code} -${it.message}"
                    }.joinToString()
                )

            }
        } else {
            databaseController.subscription_cancelFull(subscriptionId)
        }
    }

    fun getPaymentMethod(customerId: String?): String {
        if (customerId != null) {
            val customer = braintreeGateway.customer().find(customerId)
            if (customer.defaultPaymentMethod != null) {
                return if (customer.defaultPaymentMethod is PayPalAccount) {
                    "Paypal- ${(customer.defaultPaymentMethod as PayPalAccount).email}"
                } else if (customer.defaultPaymentMethod is AndroidPayCard) {
                    "GPay- ${(customer.defaultPaymentMethod as AndroidPayCard).sourceCardLast4}"
                } else {
                    val creditCard = (customer.defaultPaymentMethod as CreditCard)
                    "${creditCard.cardType}-${creditCard.maskedNumber}"
                }
            }
        }
        return "free"
    }

    suspend fun updateBrainTreeSubscriptions(subscriptionList: List<SubscriptionUpdateData>): Map<String, MutableList<SubscriptionUpdateData>> {

        logger.info("[BraintreeManager.updateBrainTreeSubscriptions] Batch Subscription Update START | subscriptionList: $subscriptionList")

        val batchSize = 100 // Define your batch size
        val totalBatches = (subscriptionList.size + batchSize - 1) / batchSize
        val coroutineScope = CoroutineScope(Dispatchers.IO)
        val jobs = mutableListOf<Job>()
        val updatedList = mutableListOf<SubscriptionUpdateData>()
        val failureList = mutableListOf<SubscriptionUpdateData>()

        for (i in 0 until totalBatches) {
            val startIndex = i * batchSize
            val endIndex = minOf((i + 1) * batchSize, subscriptionList.size)

            val batch = subscriptionList.subList(startIndex, endIndex)

            val job = coroutineScope.launch {
                batch.forEach { subscription ->
                    try {
                        val brainTreeSubscription =
                            braintreeGateway.subscription().find(subscription.brainTreeSubscriptionId)
                        val durationPeriod = utility.getDayMonthFromFreeTrialISO8601Code(subscription.couponDuration)
                        // Free access only
                        val request =
                            if (brainTreeSubscription.discounts.isNotEmpty()) {
                                SubscriptionRequest()
                                    .discounts()
                                    .update().existingId(DISCOUNT_CODE)
                                    .amount(calculateSubscription(brainTreeSubscription))
                                    .numberOfBillingCycles(durationPeriod.toInt())
                                    .done().done()
                            } else {
                                SubscriptionRequest()
                                    .discounts()
                                    .add()
                                    .inheritedFromId(DISCOUNT_CODE)
                                    .amount(calculateSubscription(brainTreeSubscription))
                                    .numberOfBillingCycles(durationPeriod.toInt())
                                    .done().done()
                            }

                        val response =
                            braintreeGateway.subscription().update(subscription.brainTreeSubscriptionId, request)

                        if (response.isSuccess) {
                            databaseController.subscription_update_nextBillingDate(
                                LocalDateTime.ofInstant(
                                    response.target.nextBillingDate.toInstant(),
                                    response.target.nextBillingDate.timeZone.toZoneId()
                                ),
                                subscription.subscriptionId.toLong()
                            )
                            databaseController.subscriptionTransaction_add(
                                subscription.subscriptionId.toLong(),
                                SubscriptionTransaction.TransactionType.COUPON_DURATION_EXTENSION,
                                calculateSubscription(brainTreeSubscription)?.toFloat() ?: 0f
                            )
                            updatedList.add(subscription) // Successful updates
                        } else {
                            failureList.add(subscription)
                        }
                    } catch (e: Exception) {
                        failureList.add(subscription)
                        logger.error("Unable to Update the Subscription ${subscription.subscriptionId}", e)
                    }
                }
            }
            jobs.add(job)
        }

        // Throttling - Wait for a reasonable number of jobs to complete before launching more
        jobs.chunked(10).forEach { chunk ->
            chunk.forEach { it.join() }
            delay(1000) // Adjust delay as needed to control the rate of processing
        }

        coroutineScope.cancel() // Cancel the coroutine scope

        return mapOf(
            "updatedSubscription" to updatedList,
            "failedSubscription" to failureList
        )
    }

    private fun calculateSubscription(brainTreeSubscription: Subscription?): BigDecimal? {
        val addonsTotal = brainTreeSubscription?.addOns
            ?.fold(BigDecimal.ZERO) { total, addon ->
                total.add(addon.amount)
            } ?: BigDecimal.ZERO

        return brainTreeSubscription?.price?.add(addonsTotal) ?: BigDecimal.ZERO
    }

    private fun customerToString(customer: Customer): String {
        return "Customer(" +
                "createdAt=${customer.createdAt}, " +
                "updatedAt=${customer.updatedAt}, " +
                "company='${customer.company}', " +
                "email='${customer.email}', " +
                "fax='${customer.fax}', " +
                "firstName='${customer.firstName}', " +
                "graphqlId='${customer.graphQLId}', " +
                "id='${customer.id}', " +
                "lastName='${customer.lastName}', " +
                "phone='${customer.phone}', " +
                "website='${customer.website}', " +
                "addresses=${customer.addresses}, " +
                "amexExpressCheckoutCards=${customer.amexExpressCheckoutCards}, " +
                "androidPayCards=${customer.androidPayCards}, " +
                "applePayCards=${customer.applePayCards}, " +
                "creditCards=${customer.creditCards}, " +
                "customActionsPaymentMethods=${customer.customActionsPaymentMethods}, " +
                "masterpassCards=${customer.masterpassCards}, " +
                "paypalAccounts=${customer.payPalAccounts}, " +
                "samsungPayCards=${customer.samsungPayCards}, " +
                "sepaDirectDebitAccounts=${customer.sepaDirectDebitAccounts}, " +
                "usBankAccounts=${customer.usBankAccounts}, " +
                "venmoAccounts=${customer.venmoAccounts}, " +
                "visaCheckoutCards=${customer.visaCheckoutCards}, " +
                "customFields=${customer.customFields}" +
                ")"
    }
}
