package net.kidjo.server.api5.extension.subscription.mondia

import com.fasterxml.jackson.databind.ObjectMapper
import okhttp3.*

class MondiaPushNotificationClient(
    private val baseUrl: String,
    private val authorizationToken: String,
    private val partnerCode: String
) {
    private val client = OkHttpClient()
    private val mediaType = MediaType.parse("application/json")

    suspend fun sendPushNotification(
        eventType: MondiaPushEventType,
        datetime: String,
        tenantId: String,
        url: String,
        user: String,
        token: String
    ): Response {
        val requestPayload = MondiaPushNotificationRequest(
            eventType = eventType.value,
            datetime = datetime,
            tenantId = tenantId,
            url = url,
            user = user,
            token = token
        )

        val objectMapper = ObjectMapper()
        val jsonBody = objectMapper.writeValueAsString(requestPayload)
        val requestBody = RequestBody.create(mediaType, jsonBody)

        val request = Request.Builder()
            .url("$baseUrl/standard-push/send")
            .post(requestBody)
            .addHeader("Authorization", authorizationToken)
            .addHeader("partnerCode", partnerCode)
            .addHeader("Content-Type", "application/json")
            .build()

        return client.newCall(request).execute()
    }
}