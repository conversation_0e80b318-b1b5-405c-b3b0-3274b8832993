package net.kidjo.server.api5.extension.subscription.digitalvirgo

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import io.ktor.server.application.*
import io.ktor.server.plugins.*
import io.ktor.server.request.*
import net.kidjo.server.api5.publichers.Api5DVController
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.request.DVNotificationSubscriptionRequest
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.notification.insertLogs

/*
 * DVPASS route to handle the notification received after the event forwarding.
 */
suspend fun Api5DVController.dvHandleNotification(call: ApplicationCall) {
    logger.info("START DV NOTIFICATION RECEIVER")
    logger.info("IP: ${call.request.origin.remoteHost}")

    val notificationDto = call.receive<DVNotificationSubscriptionRequest>()

    logger.info(
        "DV NOTIFICATION RECEIVER  requestJSON request: ${
            ObjectMapper()
                .registerModule(JavaTimeModule()).writeValueAsString(notificationDto)
        }"
    )
//    val requestBody=ObjectMapper()
//        .registerModule(JavaTimeModule()).writeValueAsString(notificationDto)

    logger.info("DV NOTIFICATION RECEIVER  Jackson Mapper request: $notificationDto")
//    insertLogs(requestBody, "virgo")
    logger.info("Logs inserted into partner_subscription_logs ")
    digitalvirgoApiManager.notification.handleDVPASSEvent(notificationDto)

    return call.respondOK()
}
