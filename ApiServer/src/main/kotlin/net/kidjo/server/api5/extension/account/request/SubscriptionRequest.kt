package net.kidjo.server.api5.extension.account.request

import kotlinx.serialization.Serializable
import java.math.BigDecimal

@Serializable
data class SubscriptionRequest(
    val paymentNonce: String,
    val plans: Plan,
    val isBundle: Boolean = false,
    val coupon: String?= null,
    val deviceData: String,
    val billingName: String?= null,
    val subscriptionStartDate: Long?= null,
)

@Serializable
data class Plan(
    val planId: String,
    val product: String,
    val addOns:  AddOn?= null,
    val discounts:List<Discount>?= null
)

@Serializable
data class AddOn(val addonName: String, val price: BigDecimal)

@Serializable
data class Discount(val discountId:String)
