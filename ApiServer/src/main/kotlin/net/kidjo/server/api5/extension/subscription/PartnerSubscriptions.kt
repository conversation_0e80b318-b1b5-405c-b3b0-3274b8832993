package net.kidjo.server.api5.extension.subscription

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api5.publichers.Api5PartnerSubscriptionController
import net.kidjo.server.models.enums.PartnerSubscription
import net.kidjo.server.service.MondiaIntegrationService
import net.kidjo.server.service.SwitchIntegrationService
import net.kidjo.server.service.TwtIntegrationService
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.Partner


suspend fun Api5PartnerSubscriptionController.partnerIntegration(call: ApplicationCall) {

    val partner = call.principal<Partner>()

    logger.info(
        "[Api5PartnerSubscriptionController.partnerIntegration] Receiving new " +
                "${partner?.name?.uppercase()} Subscription request: $call"
    )

    if (PartnerSubscription.MONDIA.toString() == partner?.name?.uppercase()) {
        val partnerService = MondiaIntegrationService(
            config, utility, encryptionController,
            emailManager, databaseController,
            languageCache
        )
        partnerService.partnerProcess(call)
    }

    if (PartnerSubscription.SWITCH.toString() == partner?.name?.uppercase()) {
        val partnerService = SwitchIntegrationService(
            config, utility, encryptionController,
            emailManager, databaseController,
            languageCache
        )
        partnerService.partnerProcess(call)
    }

    if (PartnerSubscription.TWT.toString() == partner?.name?.uppercase()) {
        val partnerService = TwtIntegrationService(
            config, utility, encryptionController,
            emailManager, databaseController,
            languageCache
        )
        partnerService.partnerProcess(call)
    } else {
        logger.warn(
            "[Api5PartnerSubscriptionController.partnerIntegration] No partner exists with this " +
                    "key: ${partner?.name?.uppercase()} " +
                    ", and request: $call"
        )
        return call.respondError(HttpStatusCode.BadRequest, "No partner exists with this key")
    }
}

