package net.kidjo.server.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import net.kidjo.server.api5.extension.subscription.kidjoDomain
import net.kidjo.server.core.PartnerBaseService
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Partner
import net.kidjo.server.shared.models.PartnerSubscription
import net.kidjo.server.shared.models.PartnerSubscriptionResponse
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.Countries
import net.kidjo.server.shared.models.entity.SubscriptionRoot
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.generatePassword
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.utils.convertStringToDate
import net.kidjo.server.utils.emailValidation
import net.kidjo.server.utils.insertLogs
import net.kidjo.server.utils.isFutureOrTodayDate
import net.kidjo.server.utils.validateMobileNumber
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import java.time.LocalDateTime
import java.util.Optional
import net.kidjo.server.shared.models.SubscriptionRoot.SubscriptionType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq

private var userPassword = ""

class SwitchIntegrationService(
    config: Config,
    utility: Utility,
    encryptionController: EncryptionController,
    emailManager: EmailManager,
    databaseController: DatabaseController,
    languageCache: LanguageCache,
) : PartnerBaseService(
    config, utility,
    encryptionController, emailManager, databaseController, languageCache,
) {
    override suspend fun partnerProcess(call: ApplicationCall) {
        val partner = call.principal<Partner>()
        val logs = call.receive<String>()
        insertLogs(logs, partner?.name.toString())
        logger.info("Logs inserted into partner_subscription_logs ")
        logger.info("SubscriptionBean Json $logs")
        val objectMapper = ObjectMapper()
        val partnerDetails = objectMapper.readValue(logs, PartnerSubscription::class.java)
        val msIdn = partnerDetails.msidn.toString()
        var email =
            partnerDetails.email.takeIf { !it.isNullOrEmpty() } ?: ""
        val username = partnerDetails.username
        val startDate = partnerDetails.plan.startingDate.takeIf { !it.isNullOrEmpty() }
            ?: return call.respondBadRequest("start_date is required")
        val duration = partnerDetails.plan.duration.takeIf { !it.isNullOrEmpty() }
            ?: return call.respondBadRequest("duration is required")
        val product =
            partnerDetails.product.takeIf { !it.isNullOrEmpty() }
                ?: return call.respondBadRequest("product is required")

        val countryCode = partnerDetails.countryCode.takeIf { !it.isNullOrEmpty() }
            ?: return call.respondBadRequest("country_code is required")
        val subPartner = partnerDetails.subPartner.takeIf { !it.isNullOrEmpty() }
            ?: partner?.name
        val subscriptionPackage = partnerDetails.subscriptionPackage.takeIf { !it.isNullOrEmpty() }
            ?: ""
        var isEmailFake = false
        var userUpdated = 0

        if (email.isEmpty() && msIdn.isEmpty()) {
            call.respondBadRequest("provide email or msidn for subscription")
        } else {
            if (msIdn.isNotEmpty() && email.isEmpty()) {
                if (validateMobileNumber(msIdn)) {
                    email = msIdn.replace("+", "") + kidjoDomain
                    isEmailFake = true;
                } else {
                    return call.respondError(HttpStatusCode.BadRequest, "Invalid number format e.g +92310XXXX897")
                }
            }
            if (msIdn.isNotEmpty()) {
                if (!validateMobileNumber(msIdn)) {
                    throw BadRequestException("Invalid number format e.g +92310XXXX897 ")
                }
            }
            if (!emailValidation(email)) {
                return call.respondError(HttpStatusCode.BadRequest, "Invalid email address")
            }
            if (!checkExistingEmail(email)) {
                createUser(isEmailFake, email, username, countryCode, config, utility, encryptionController)
            } else {
                userUpdated = updateUser(email, username, countryCode)
            }

            val token = "$msIdn|$email"
            val subscription: Int;
            val subscriptionType = getSubscriptionType(product)
            var checkSubscription = false
            if (email.isNotEmpty()) {
                checkSubscription =
                    validateUserSubscription(
                        email,
                        subscriptionType,
                        partner?.name.toString(),
                        startDate,
                        duration
                    )
            }
            if (!checkSubscription) {
                subscription = createSubscription(
                    token, email, subscriptionType, startDate, partner?.name.toString(), duration, config
                )
                if (subscription != 0) {
                    insertPartnerSubscription(msIdn, subscriptionPackage, subPartner.toString())
                    updateLogs(token)
                } else {
                    call.respondBadRequest("Failed to create Subscription")
                }
            }
            if (userUpdated != 0) {
                call.respond(
                    HttpStatusCode.OK
                )
            } else {
                call.respond(
                    HttpStatusCode.OK, PartnerSubscriptionResponse(
                        email,
                        userPassword
                    )
                )
            }

        }
    }

    private fun checkExistingEmail(email: String): Boolean {
        return transaction {
            Users.select { Users.email eq email }.count() > 0
        }
    }

    private fun createUser(
        isEmailFake: Boolean,
        email: String,
        username: String,
        countryCode: String,
        config: Config,
        utility: Utility,
        encryptionController: EncryptionController,
    ): Int {
        var countryId = 0
        return transaction {
            val (password, hashedPassword) = generatePassword(
                randomString = utility.randomString(
                    net.kidjo.server.api5.extension.subscription.LENGTH_OF_GENERATED_DV_ACCOUNT_NAME,
                    net.kidjo.server.api5.extension.subscription.GENERATED_ACCOUNT_CHARACTERS_TO_USE
                ), encryptionController = encryptionController, salt = config.clientSideHashV1Salt
            )
            userPassword = password
            Countries.select { Countries.short eq countryCode }
                .mapNotNull { row -> countryId = row[Countries.id].toInt() }
                .singleOrNull()

            Users.insert {
                it[Users.email] = email
                it[name] = username
                it[Users.countryId] = countryId
                it[Users.password] = hashedPassword
                if (isEmailFake) {
                    it[authType] = User.AuthType.FAKE_EMAIL
                } else {
                    it[authType] = User.AuthType.EMAIL
                }

            }.insertedCount
        }

    }

    private fun updateUser(
        email: String,
        username: String,
        countryCode: String
    ): Int {
        var countryId = 0
        return transaction {

            Countries.select { Countries.short eq countryCode }
                .mapNotNull { row -> countryId = row[Countries.id].toInt() }
                .singleOrNull()

            Users.update({ Users.email eq email }) {
                it[name] = username
                it[Users.countryId] = countryId
            }
        }

    }

    private fun createSubscription(
        token: String,
        email: String,
        product: SubscriptionType,
        startingDate: String,
        storeId: String,
        duration: String,
        config: Config,
    ): Int {
        val encryptionController = EncryptionController(config)
        return transaction {
            var userId = 0L
            Users.select { Users.email eq email }.mapNotNull { row -> userId = row[Users.id] }.singleOrNull()

            SubscriptionRoot.insert {
                it[SubscriptionRoot.userId] = userId.toInt()
                it[stillInFreeTrial] = false
                it[SubscriptionRoot.storeId] = Device.StorePlatform.fromRow(storeId)
                it[isRenewing] = true
                it[paymentType] = net.kidjo.server.shared.models.SubscriptionRoot.PaymentType.fromRaw(storeId)
                it[subscriptionType] = product
                it[paymentId] = storeId
                it[platformPurchaseId] = storeId
                it[iapId] = product.toString()
                it[purchasingSessionId] = "NOT_SET"
                it[subscriptionToken] = token
                it[subscriptionTokenHash] = encryptionController.sha256Hash(token)
                it[nextBillDate] = durationToNextBillingDate(duration, startingDate)
                it[createdAt] = isFutureOrTodayDate(convertStringToDate(startingDate))
            }.insertedCount
        }

    }


    private fun insertPartnerSubscription(msidn: String, subscriptionPackage: String, subPartner: String) {
        var subscriptionId = 0
        var userId = 0
        transaction {
            SubscriptionRoot.selectAll().orderBy(SubscriptionRoot.id, SortOrder.DESC).limit(1).mapNotNull { row ->
                subscriptionId = row[SubscriptionRoot.id].toInt()
                userId = row[SubscriptionRoot.userId]
            }

            if (checkMsisdnExistence(msidn)) {
                UserPartnerSubscription.update({ UserPartnerSubscription.msidn.eq(msidn) }) {
                    it[UserPartnerSubscription.subscriptionId] = subscriptionId
                    it[UserPartnerSubscription.userId] = userId
                }
            } else {
                UserPartnerSubscription.insert {
                    it[UserPartnerSubscription.msidn] = msidn.takeIf { it.isNotEmpty() } ?: ""
                    it[UserPartnerSubscription.subscriptionId] = subscriptionId
                    it[UserPartnerSubscription.userId] = userId
                    it[UserPartnerSubscription.subPartner] = subPartner
                    it[UserPartnerSubscription.subscriptionPackage] = subscriptionPackage
                }.insertedCount
            }
        }
    }


    fun durationToNextBillingDate(duration: String, startDate: String): LocalDateTime {
        var extractDigit: Int
        var isExpired = ""
        if (duration.uppercase().contains("D")) {
            extractDigit = duration.replace("D", "").toInt()
            isExpired = convertStringToDate(startDate).plusDays(extractDigit.toLong()).toString()
        } else if (duration.uppercase().contains("W")) {
            extractDigit = duration.replace("W", "").toInt()
            isExpired = convertStringToDate(startDate).plusWeeks(extractDigit.toLong()).toString()
        } else if (duration.uppercase().contains("M")) {
            extractDigit = duration.replace("M", "").toInt()
            isExpired = convertStringToDate(startDate).plusMonths(extractDigit.toLong()).toString()
        } else if (duration.uppercase().contains("Y")) {
            extractDigit = duration.replace("Y", "").toInt()
            isExpired = convertStringToDate(startDate).plusYears(extractDigit.toLong()).toString()
        }
        return convertStringToDate(isExpired)
    }


    private fun validateUserSubscription(
        credentialId: String,
        product: SubscriptionType,
        partnerName: String,
        startDate: String,
        duration: String
    ): Boolean {
        var isExists = false
        transaction {
            var userId = 0
            Users.select { Users.email eq credentialId }.mapNotNull { row -> userId = row[Users.id].toInt() }

            SubscriptionRoot.select {
                SubscriptionRoot.userId.eq(userId) and SubscriptionRoot.storeId.eq(
                    Device.StorePlatform.fromRow(
                        partnerName.lowercase()
                    )) and (SubscriptionRoot.isActive eq true) and SubscriptionRoot.nextBillDate.greater(
                        convertStringToDate(startDate)
                    )
            }.filter { row ->
                row[SubscriptionRoot.subscriptionType].raw.contains(product.raw.substringAfter("_"))
            }.map { row ->
                //extend the nextBillDate only when the subscription expire in less than 24 hours
                if (LocalDateTime.now().plusDays(1) > row[SubscriptionRoot.nextBillDate]) {
                    SubscriptionRoot.update({
                        SubscriptionRoot.id.eq(row[SubscriptionRoot.id])
                    }) {
                        it[nextBillDate] = durationToNextBillingDate(duration, startDate)
                        it[isActive] = true
                        it[updatedAt] = LocalDateTime.now()
                    }
                }
                isExists = true
            }
        }
        return isExists
    }

    private fun checkMsisdnExistence(msisdn: String): Boolean {
        return transaction {
            UserPartnerSubscription.select { UserPartnerSubscription.msidn.eq(msisdn) }
                .count() > 0
        }

    }
}
