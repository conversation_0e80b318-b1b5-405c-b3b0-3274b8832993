package net.kidjo.server.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.server.application.*
import net.kidjo.exceptions.SubscriptionException
import net.kidjo.server.core.PartnerBaseService
import net.kidjo.server.models.PartnerSubscriptionRequest
import net.kidjo.server.models.enums.Event
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.models.Product
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.enums.EventType
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.api.data.request.DVNotificationSubscriptionRequest
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class DigitalVirgoIntegrationService(
    config: Config,
    utility: Utility,
    encryptionController: EncryptionController,
    emailManager: EmailManager,
    databaseController: DatabaseController,
    languageCache: LanguageCache,
) : PartnerBaseService(config, utility, encryptionController, emailManager, databaseController, languageCache) {
    /**
     *  Partner based request processing Implementation
     */
    override suspend fun partnerProcess(call: ApplicationCall) {
        TODO("Not yet implemented")
    }

    override fun handlePartnerRequest(request: String): PartnerSubscriptionRequest {
        val incomingRequest = ObjectMapper().readValue(request, DVNotificationSubscriptionRequest::class.java)
        logger.info("START DV Notification received: handleDigitalVirgoNotification()")

        logger.info("dv_eventType : ${incomingRequest.type}")
        logger.info("dv_subscriptionIdString  : ${incomingRequest.operationId}")
        logger.info("dv_subscriptionStatus  : ${incomingRequest.subscription?.status}")
        logger.info("dv_subscriptionCountry :${incomingRequest.subscription?.country}")
        logger.info("dv_expirationDateString : ${incomingRequest.subscription?.expirationDate}")
        logger.info("dv_cancellationDateString : ${incomingRequest.subscription?.cancellationDate}")
        logger.info("dv_suspensionDateString : ${incomingRequest.subscription?.suspensionDate}")
        logger.info("dv_terminationDateString : ${incomingRequest.subscription?.terminationDate}")
        logger.info("dv_subscriptionDateString : ${incomingRequest.subscription?.subscriptionDate}")
        logger.info("dv_user_msisdn : ${incomingRequest.user?.msisdn}")
        logger.info("dv_user_alias : ${incomingRequest.user?.alias}")
        //logger.info("dv_user_aliasGsm : ${incomingRequest.user?.aliasGsm}")
        logger.info("dv_userLocale : ${incomingRequest.user?.locale}")
        logger.info("dv_subscriptionId :${incomingRequest.subscription?.subscriptionId}")

        val subIndent = incomingRequest.getSubIndent()
        logger.info("subIndent : $subIndent ")

        val userIndent = incomingRequest.getUserIndent()
        logger.info("userIndent : $userIndent ")

        val msisdn = incomingRequest.getMSISDN()
        logger.info("msisdn: $msisdn ")

        val subCountry = incomingRequest.subscription?.country
        logger.info("subCountry: $subCountry ")

        val userLocale = incomingRequest.user?.locale
        logger.info("userLocale: $userLocale ")

        val offerId = incomingRequest.offer?.id
        logger.info("offerId: $offerId ")

        val packageId = incomingRequest.dvPackage?.id
        logger.info("packageId: $packageId ")

        val correlationIdString = incomingRequest.correlationId
        logger.info("correlationIdString: $correlationIdString ")

        val subStatus = incomingRequest.subscription?.status
        logger.info("subStatus: $subStatus ")

        val subType = incomingRequest.type
        logger.info("subType: $subType ")

        val mccmnc = incomingRequest.getMCCMNC()
        logger.info("mccmnc: $mccmnc ")

        val nextBillingDate = incomingRequest.getNextBillingDate()
        logger.info("nextBillingDate: $nextBillingDate ")

        val operatorName = incomingRequest.getOperatorName()
        logger.info("operatorName: $operatorName ")

        if (subType.isNullOrBlank() ||
            subIndent.isNullOrBlank() ||
            subCountry.isNullOrBlank() ||
            userIndent.isNullOrBlank()
        ) {
            logger.error(
                "notification didn't have right information" +
                        "(type, operationId, user:msisdn,alias,aliasGsm, subscription: subsCountry )," +
                        " $incomingRequest"
            )
            throw SubscriptionException("Unprocessable Entity")
        }

//        if (operatorName != OrangeOperators.ORANGE_MOROCCO.name) {
//            logger.info("EXIT UNSUPPORTED OPERATOR: $operatorName ")
//
//        }

        val event = when (subType) {
            EventType.SUBSCRIPTION.raw -> {
                Event.SUBSCRIPTION
            }

            EventType.TERMINATION.raw,
            EventType.EXPIRATION.raw,
            -> {
                Event.TERMINATION
            }

            EventType.CANCELLATION.raw -> Event.CANCELLATION
            EventType.SUSPENSION.raw -> Event.SUSPENSION
            else -> Event.OTHER
        }

        val params = mutableMapOf<String, String>(
            "offerId" to offerId.toString(),
            "packageId" to packageId.toString()
        )
        val operationId = incomingRequest?.getSubIndent()
        if (operationId != null) {
            params["operationId"] = operationId
        }

        return PartnerSubscriptionRequest(
            event = event,
            email = null,
            msisdn = msisdn,
            countryCode = subCountry,
            nextBillingDate = incomingRequest.subscription?.let {
                val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX")
                val zonedDateTime = ZonedDateTime.parse(it.nextCycleDate, formatter)
                val localDateTime = zonedDateTime.toLocalDateTime()
                localDateTime
            } ?: nextBillingDate,
            isTrail = false,
            partnerUserId = correlationIdString,
            product = mutableListOf(Product.KIDJO_TV),
            firstBillingDate = LocalDateTime.now(),
            token = incomingRequest?.getSubIndent() ?: "${msisdn}",
            test = false,
            params = params
        )
    }

}
