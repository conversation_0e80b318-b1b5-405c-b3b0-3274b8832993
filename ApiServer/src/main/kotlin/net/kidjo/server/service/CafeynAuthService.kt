package net.kidjo.server.service

import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.kotlinModule
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import net.kidjo.aws.AwsParameterStore
import net.kidjo.server.models.cafeyn.CafeynLoginRequest
import net.kidjo.server.models.cafeyn.CafeynLoginResponse
import net.kidjo.server.shared.tools.Config
import org.slf4j.LoggerFactory

class CafeynAuthService(val config: Config) {

    internal val logger = LoggerFactory.getLogger("CafeynAuthService")

    private val authUrl: String = AwsParameterStore.getCafeynLoginUrl(config.env.name)


    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            jackson {
                configure(SerializationFeature.INDENT_OUTPUT, true)
                registerModule(kotlinModule())
            }
        }
        /*install(HttpTimeout) { TODO add timeout later
            requestTimeoutMillis = AwsParameterStore.getParameter("/requestTimeoutMillis").toLong()
        }
        install(Logging) {
            level = LogLevel.INFO
        }*/
    }

    suspend fun authenticate(email: String, password: String): CafeynLoginResponse {
        val request = CafeynLoginRequest(email, password)

        val response: HttpResponse = try {
            client.post(authUrl) {
                contentType(ContentType.Application.Json)
                setBody(request)
            }
        } catch (e: ClientRequestException) {
            // handle 4xx errors
            if (e.response.status == HttpStatusCode.Unauthorized) {
                logger.error("Unauthorized: ${e.response.status}")
                return emptyCafeynLoginResponse()
            } else if (e.response.status == HttpStatusCode.BadRequest) {
                logger.error("Bad request: ${e.response.status}")
                return emptyCafeynLoginResponse()
            }
            throw e
        } catch (e: ServerResponseException) {
            // handle 5xx errors
            logger.error("5XX: $e")
            throw e
        } catch (e: HttpRequestTimeoutException) {
            // handle timeout
            logger.error("Timeout: $e")
            throw e
        } catch (e: Exception) {
            logger.error("Exception: $e")
            throw e
        }

        return if (response.status == HttpStatusCode.OK) {
            response.body<CafeynLoginResponse>()
        } else {
            logger.error("Unexpected status code: ${response.status}")
            return emptyCafeynLoginResponse()
        }
    }

    private fun emptyCafeynLoginResponse(): CafeynLoginResponse {
        return CafeynLoginResponse(
            token = "",
            email = "",
            user_id = 0,
            partner_name = "",
            partner_description = "",
            partner_id = 0,
            is_active = false,
            is_sso_activated = false,
            bundles = emptyList(),
            subscribed_services = emptyList()
        )
    }
}