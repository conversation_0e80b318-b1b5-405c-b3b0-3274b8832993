package net.kidjo.server.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.plugins.*
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import net.kidjo.server.models.SubscriptionBean
import net.kidjo.server.core.PartnerBaseService
import net.kidjo.server.models.mondia.ConsentBean
import net.kidjo.server.models.mondia.ConsentUpdateBean
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getMondiaUserPackageType
import net.kidjo.server.shared.models.Partner
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.MondiaConsent
import net.kidjo.server.shared.models.entity.Users
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.utils.*
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


private val logger = LoggerFactory.getLogger("MondiaIntegrationService - Mondia Integration Service ")
internal val GENERATED_ACCOUNT_CHARACTERS_TO_USE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()
const val LENGTH_OF_GENERATED_DV_ACCOUNT_NAME = 6
const val UUID_EMAIL_LENGTH: Int = 40

class MondiaIntegrationService(
    config: Config,
    utility: Utility,
    encryptionController: EncryptionController,
    emailManager: EmailManager,
    databaseController: DatabaseController,
    languageCache: LanguageCache
) : PartnerBaseService(
    config, utility,
    encryptionController, emailManager, databaseController, languageCache,
) {

    private val LOG_PREFIX_PROCESS = "[MondiaIntegrationService.partnerProcess]"

    override suspend fun partnerProcess(
        call: ApplicationCall
    ) {

        val partner = call.principal<Partner>()
        val logs = call.receive<String>()
        insertLogs(logs, partner?.name.toString())
        logger.info("$LOG_PREFIX_PROCESS Logs inserted into partner_subscription_logs ")
        logger.info("$LOG_PREFIX_PROCESS SubscriptionBean Json $logs")
        val objectMapper = ObjectMapper()
        val subscriptionBean = objectMapper.readValue(logs, SubscriptionBean::class.java)
        logger.debug("$LOG_PREFIX_PROCESS subscriptionBean: $subscriptionBean")

        val isSubscribed: Boolean
        val token = subscriptionBean.userUuid + "|" + subscriptionBean.productId

        val mondiaPackage = databaseController.getMondiaUserPackageType(
            subscriptionBean.productId,
            subscriptionBean.packageId
        )
        logger.debug("$LOG_PREFIX_PROCESS Mondia package: $mondiaPackage")
        subscriptionBean.updateEndDateIfNull(mondiaPackage)
        logger.info("$LOG_PREFIX_PROCESS subscriptionBean with the update endDate: $subscriptionBean")

        if (subscriptionBean.event.lowercase() == "create") {

            logger.info("$LOG_PREFIX_PROCESS Requested to create subscription")
            isSubscribed = createSubscription(
                subscriptionBean,
                config,
                utility,
                encryptionController,
                languageCache.get(call),
                partner?.name.toString()
            )
        } else {
            logger.info("$LOG_PREFIX_PROCESS Requested to update subscription")
            isSubscribed =
                updateSubscription(subscriptionBean, config, utility, encryptionController, languageCache.get(call))

        }
        updateLogs(token)
        //updateFakeEmail(subscriptionBean) // TODO the best solution is to refactor the full super class to handle the fake and Normal emails.
        if (isSubscribed) {
            return call.respond(HttpStatusCode.OK)
        }
    }

    private fun updateFakeEmail(subscriptionBean: SubscriptionBean): Int {

        val fakeEmail = "${subscriptionBean.userUuid.take(UUID_EMAIL_LENGTH)}@kidjo.tv"
        logger.info(
            "[MondiaIntegrationService.updateFakeEmail] Updating fakeEmail for mondia user: $fakeEmail"
        )
        return transaction {
            Users.update({ Users.email eq fakeEmail }) {
                it[authType] = User.AuthType.FAKE_EMAIL
            }
        }
    }


    suspend fun mondiaConsentProcess(consentOperation: String, call: ApplicationCall, trxUUID: String) {
        logger.info(
            "[MondiaIntegrationService.mondiaConsentProcess] [$trxUUID] " +
                    "consent $consentOperation MONDIA START"
        )
        when (consentOperation) {
            "CREATE" ->
                mondiaConsentCreate(call, trxUUID)

            "UPDATE" ->
                mondiaConsentUpdate(call, trxUUID)

            else -> {
                logger.info(
                    "[MondiaIntegrationService.mondiaConsentProcess] [$trxUUID] " +
                            "Undefined Operation: $consentOperation"
                )
            }
        }
        logger.info(
            "[MondiaIntegrationService.mondiaConsentProcess] [$trxUUID] " +
                    "consent $consentOperation MONDIA END"
        )
    }

    private suspend fun mondiaConsentCreate(call: ApplicationCall, trxUUID: String) {

        val logs = call.receive<String>()
        logger.info("[MondiaIntegrationService.mondiaConsentCreate] [$trxUUID] ConsentBean Json $logs")
        insertLogs(logs, "MONDIA")
        logger.debug("[MondiaIntegrationService.mondiaConsentCreate] [$trxUUID] Logs inserted into DB.")
        val objectMapper = ObjectMapper()
        val consentBean = objectMapper.readValue(logs, ConsentBean::class.java)
        checkConsentCreationFields(consentBean)
        val isConsentCreated = createConsent(consentBean, trxUUID)
        if (isConsentCreated) {
            return call.respond(HttpStatusCode.OK)
        }
    }

    private suspend fun mondiaConsentUpdate(call: ApplicationCall, trxUUID: String) {

        val logs = call.receive<String>()
        logger.info("[MondiaIntegrationService.mondiaConsentUpdate] [$trxUUID] ConsentBean Json $logs")
        insertLogs(logs, "MONDIA")
        logger.debug("[MondiaIntegrationService.mondiaConsentUpdate] [$trxUUID] Logs inserted into DB.")
        val objectMapper = ObjectMapper()
        val consentUpdateBean = objectMapper.readValue(logs, ConsentUpdateBean::class.java)
        checkConsentUpdateFields(consentUpdateBean)
        if (updateConsent(consentUpdateBean, trxUUID) > 0) {
            return call.respond(HttpStatusCode.OK)
        }
    }

    private fun createConsent(
        consentBean: ConsentBean,
        trxUUID: String
    ): Boolean {

        logger.info("[MondiaIntegrationService.createConsent] [$trxUUID] Creating consent...")
        return transaction {
            MondiaConsent.insert {
                it[consentId] = consentBean.consentId
                it[userUuid] = consentBean.userUuid
                it[flow] = consentBean.flow
                it[fields_] = consentBean.fields.toString()
                it[entitlements] = consentBean.entitlements.toString()
                it[consentRecordCreated] =
                    LocalDateTime.parse(consentBean.consentRecordCreated, DateTimeFormatter.ISO_DATE_TIME)
                it[signature] = consentBean.signature
                it[eventId] = consentBean.eventId
            }.insertedCount > 0
        }
    }

    private fun updateConsent(consentUpdate: ConsentUpdateBean, trxUUID: String): Int {

        logger.info("[MondiaIntegrationService.updateConsent] [$trxUUID] Updating consent ${consentUpdate.consentId}...")
        var success = 0
        transaction {
            success = MondiaConsent.update({ MondiaConsent.consentId eq consentUpdate.consentId }) {
                it[userUuid] = consentUpdate.userUuid
                it[entitlements] = consentUpdate.entitlements.toString()
                it[signature] = consentUpdate.signature
                it[eventId] = consentUpdate.eventId
                it[updatedAt] = LocalDateTime.now()
            }
        }

        if (success == 0) {
            throw BadRequestException("[$trxUUID] Unable to update mondia consent")
        }
        return success
    }

    private fun checkConsentCreationFields(consentBean: ConsentBean) {
        when {
            consentBean.consentId.isEmpty() ->
                throw BadRequestException("consentId is missing")
            /*consentBean.userUuid.isEmpty() ->
                throw BadRequestException("userUuid is missing")
            consentBean.consentRecordCreated.isEmpty() ->
                throw BadRequestException("consentRecordCreated is missing")
            consentBean.eventId.isEmpty() ->
                throw BadRequestException("eventId is missing")*/

        }
    }

    private fun checkConsentUpdateFields(consentUpdateBean: ConsentUpdateBean) {
        when {
            consentUpdateBean.consentId.isEmpty() ->
                throw BadRequestException("consentId is missing")
            /*consentUpdateBean.userUuid.isEmpty() ->
                throw BadRequestException("userUuid is missing")
            consentUpdateBean.eventId.isEmpty() ->
                throw BadRequestException("eventId is missing")
            consentUpdateBean.dateTimeOfAcknowledgement.isEmpty() ->
                throw BadRequestException("dateTimeOfAcknowledgement is missing")*/

        }
    }
}
