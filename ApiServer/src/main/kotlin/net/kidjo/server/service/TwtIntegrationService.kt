package net.kidjo.server.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import net.kidjo.server.core.PartnerBaseService
import net.kidjo.server.shared.cache.LanguageCache
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getActiveSubscription
import net.kidjo.server.shared.database.getExistingSubscriptionId
import net.kidjo.server.shared.database.insert_country_customer_details
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.subscriptionRoot_update
import net.kidjo.server.shared.database.subscriptionTWT_getRecentActive
import net.kidjo.server.shared.database.updateOldSubscription
import net.kidjo.server.shared.database.updateUserPartnerSubsDetails
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondNoContent
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.Partner
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import net.kidjo.server.shared.models.TwtNotification
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.models.entity.UserPartnerSubscription
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EmailManager
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.Utility
import net.kidjo.server.utils.convertMiliSecondDate
import net.kidjo.server.utils.insertLogs
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.joda.time.LocalDateTime

class TwtIntegrationService(
    config: Config,
    utility: Utility,
    encryptionController: EncryptionController,
    emailManager: EmailManager,
    databaseController: DatabaseController,
    languageCache: LanguageCache
) : PartnerBaseService(
    config, utility,
    encryptionController, emailManager, databaseController, languageCache,
) {
    override suspend fun partnerProcess(call: ApplicationCall) {
        val partner = call.principal<Partner>()
        val user = call.principal<User>()
        val logs = call.receive<String>()
        val objectMapper = ObjectMapper()
        val twt = objectMapper.readValue(logs, TwtNotification::class.java)

        insertLogs(logs, partner?.name.toString())
        logger.info("Logs inserted into partner_subscription_logs ")
        logger.info("SubscriptionBean Json $logs")

        val userId = user?.getLongId() ?: User.NO_SERVER_ID
        val customerId = twt.customerId.takeIf { it != 0 } ?: throw BadRequestException("Customer ID cannot be zero")
        val customerIso =
            twt.countryISO.takeIf { it!!.isNotEmpty() } ?: throw BadRequestException("Country ISO cannot be empty")
        val subToken = customerId.toString() + partner?.name
        val notifyType =
            twt.notifType.takeIf { it!!.isNotEmpty() } ?: throw BadRequestException("Notify type cannot be empty")
        var nextBilling = convertMiliSecondDate(
            LocalDateTime.now().plusDays(1).toString()
        )
        val subscriptionId = databaseController.getExistingSubscriptionId(token = subToken)
        if (notifyType.lowercase() == "subscription") {

            val sub = databaseController.subscriptionTWT_getRecentActive(userId, subToken)
            val subId = sub?.id ?: SubscriptionRoot.NO_ID


            var subType = net.kidjo.server.shared.models.SubscriptionRoot.SubscriptionType.KIDJO_TV
            val inserted = SubscriptionRootInsert(
                userId = userId,
                deviceId = Device.NO_SERVER_ID,
                isFreeTrail = false,
                priceToLogUSD = 0f,
                paymentType = SubscriptionRoot.PaymentType.NATIVE,
                subscriptionType = subType,
                paymentId = partner?.name.toString(),
                platformPurchaseId = partner?.name.toString(),
                storeId = Device.StorePlatform.TWT,
                paymentStateId = null,
                iapId = subType.toString(),
                purchasingSessionId = "NONE",
                subscriptionToken = subToken,
                subscriptionTokenHash = encryptionController.sha256Hash(subToken),
                accountCouponId = AccountCoupon.NO_SERVER_ID,
                isRenewing = true,
                nextBillDate = nextBilling,
                isTest = !config.env.isLive
            )

            if (subscriptionId == null) {

                if (databaseController.subscriptionRoot_create(inserted) > 0) {
                    updateLogs(subToken)
                    databaseController.insert_country_customer_details(twt);
                    call.respond(mapOf("code" to "200", "message" to "Successfully created Subscription"))
                    return
                } else {
                    call.respondBadRequest(
                        ServerErrors.ERROR_BAD_PARAMS,
                        "Error create user's Subscription"
                    )
                    return
                }
            } else {
                if (subscriptionId != null) {
                    if (databaseController.updateOldSubscription(inserted) > 0) {
                        updateLogs(subToken)
                        updateUserPartnerSubsDetails(subscriptionId, twt)
                        call.respond(mapOf("code" to "200", "message" to "Successfully updated Subscription"))

                        return
                    } else {
                        call.respondBadRequest(
                            ServerErrors.ERROR_BAD_PARAMS,
                            "Error create user's Subscription"
                        )
                        return
                    }
                }

                if (sub != null) {
                    if (sub.nextBillDate.isBefore(nextBilling)) {
                        val updated =
                            SubscriptionRootUpdate(
                                userId = userId,
                                isRenewing = true,
                                nextBillDate = nextBilling,
                                platformPurchaseId = partner?.name.toString(),
                                isFreeTrail = false,
                                priceToLogUSD = 0f,
                                subId = subId,
                                paymentStateId = null
                            )

                        if (!databaseController.subscriptionRoot_update(updated)) {
                            call.respondBadRequest(
                                ServerErrors.ERROR_BAD_JSON,
                                "Error UPDATING subscription "
                            )
                            return

                        } else {
                            updateLogs(subToken)
                            call.respond(
                                mapOf(
                                    "code" to "200", "message"
                                            to "Successfully updated subscription till:$nextBilling"
                                )
                            )

                            return
                        }

                    } else {
                        call.respondNoContent()
                        return
                    }
                }
            }
        } else if (notifyType.lowercase() == "unsubscription") {
            if (databaseController.getActiveSubscription(subToken)) {
                transaction {
                    //To unsubscribe
                    net.kidjo.server.shared.models.entity.SubscriptionRoot.update({
                        net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionTokenHash.eq(
                            encryptionController.sha256Hash(subToken)
                        )
                    }) {
                        it[isActive] = false
                        it[isRenewing] = false
                    }
                    //Get subscription ID by hashed token
                    var subscriptionId = 0L
                    net.kidjo.server.shared.models.entity.SubscriptionRoot.select {
                        net.kidjo.server.shared.models.entity.SubscriptionRoot.subscriptionTokenHash.eq(
                            encryptionController.sha256Hash(subToken)
                        )
                    }.mapNotNull { subscriptionId = it[net.kidjo.server.shared.models.entity.SubscriptionRoot.id] }

                    //To check user from user partner subscription by subscriptionId
                    val success =
                        UserPartnerSubscription.select { UserPartnerSubscription.subscriptionId eq subscriptionId.toInt() }
                            .count() > 0

                    //If no user exists in user partner subscription then it will insert new record in it.

                    if (!success) {
                        databaseController.insert_country_customer_details(twtNotification = twt)
                    }
                }
                updateLogs(subToken)
                call.respond(
                    mapOf(
                        "code" to "200", "message"
                                to "Unsubscribed successfully!!!"
                    )
                )
            } else {

                call.respondBadRequest("Subscription not exists with this customer id $customerId")
            }
        }
    }

}
