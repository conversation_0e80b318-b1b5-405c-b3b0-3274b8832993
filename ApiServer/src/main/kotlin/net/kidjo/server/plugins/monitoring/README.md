# Kidjo API Monitoring System

This monitoring system provides comprehensive API performance tracking using aspect-oriented programming (AOP) principles to monitor all API requests without modifying existing controller code.

## Features

- **Real-time Request Monitoring**: Track all API requests, responses, and response times
- **Performance Analytics**: Identify slow endpoints and performance bottlenecks
- **Error Tracking**: Monitor error rates and identify problematic endpoints
- **Non-intrusive Design**: Uses AOP to avoid code duplication and modifications
- **Web Dashboard**: Beautiful HTML dashboard for visualizing metrics
- **RESTful API**: Programmatic access to monitoring data

## Quick Start

The monitoring system is automatically enabled when you start the server. Access the dashboard at:

```
http://localhost:8080/monitoring/dashboard
```

## API Endpoints

### Dashboard
- `GET /monitoring/dashboard` - Web-based monitoring dashboard
- `GET /monitoring` - Redirects to dashboard

### Statistics
- `GET /monitoring/stats` - Overall API statistics
- `GET /monitoring/performance-summary` - Performance summary with top slow/error endpoints
- `GET /monitoring/health` - Monitoring system health check

### Endpoint Analysis
- `GET /monitoring/slow-endpoints?limit=10&threshold=1000` - Slow endpoints (>1000ms)
- `GET /monitoring/error-endpoints?limit=10&minRequests=10` - Error-prone endpoints
- `GET /monitoring/endpoint/{method}/{path}` - Statistics for specific endpoint

### Request History
- `GET /monitoring/requests?limit=100` - Recent API requests

### Configuration
- `GET /monitoring/config` - Current monitoring configuration
- `DELETE /monitoring/stats` - Clear all statistics (useful for testing)

## Configuration

The monitoring system can be configured in `ApiMonitoringConfigPlugin.kt`:

```kotlin
install(ApiMonitoringPlugin) {
    enabled = true
    maxRecentRequests = 1000
    slowRequestThresholdMs = 1000 // 1 second
    enableRequestBodyLogging = false
    enableResponseBodyLogging = false
    enableDetailedLogging = true
    enableStatisticsCollection = true
    
    excludePaths = setOf(
        "/healthCheck", 
        "/checkHealth", 
        "/server/report",
        "/monitoring"
    )
    
    includeHeaders = setOf(
        "User-Agent", 
        "Content-Type", 
        "Authorization", // Will be masked for security
        "X-Forwarded-For",
        "X-Real-IP"
    )
}
```

## Key Metrics

### Overall Statistics
- **Total Requests**: Total number of API requests processed
- **Average Response Time**: Mean response time across all requests
- **Error Rate**: Percentage of requests that resulted in errors (4xx/5xx)
- **Slow Request Rate**: Percentage of requests exceeding the slow threshold

### Endpoint Statistics
- **Request Count**: Number of requests to the endpoint
- **Success/Error Counts**: Breakdown of successful vs failed requests
- **Response Time Metrics**: Min, max, and average response times
- **Error Rate**: Percentage of failed requests for the endpoint

## Identifying Performance Issues

### Slow Endpoints
The system automatically identifies endpoints with:
- Average response time > 1 second (configurable)
- Individual requests > slow threshold

### Error-Prone Endpoints
Tracks endpoints with:
- High error rates (4xx/5xx responses)
- Frequent failures
- Authentication/authorization issues

### Recent Issues
Monitor recent slow requests to identify:
- Sudden performance degradation
- Specific problematic requests
- Patterns in slow responses

## Security Considerations

- **Authorization Headers**: Automatically masked in logs for security
- **Request/Response Bodies**: Disabled by default to prevent sensitive data logging
- **Access Control**: Consider restricting access to monitoring endpoints in production

## Performance Impact

The monitoring system is designed to have minimal performance impact:
- Uses efficient in-memory storage
- Asynchronous data collection
- Configurable data retention limits
- Optional detailed logging

## Troubleshooting

### High Memory Usage
If monitoring data consumes too much memory:
1. Reduce `maxRecentRequests` in configuration
2. Disable detailed logging: `enableDetailedLogging = false`
3. Clear statistics periodically: `DELETE /monitoring/stats`

### Missing Data
If monitoring data is not appearing:
1. Check that monitoring is enabled: `GET /monitoring/config`
2. Verify endpoints are not in `excludePaths`
3. Check application logs for monitoring errors

### Slow Dashboard
If the dashboard loads slowly:
1. Reduce the number of recent requests displayed
2. Implement pagination for large datasets
3. Consider external monitoring solutions for high-traffic APIs

## Integration with External Tools

The monitoring API can be integrated with:
- **Grafana**: Create dashboards using the REST API
- **Prometheus**: Export metrics in Prometheus format
- **ELK Stack**: Send monitoring data to Elasticsearch
- **Custom Tools**: Use the REST API for custom monitoring solutions

## Example API Responses

### Statistics Response
```json
{
  "totalRequests": 1250,
  "totalSuccessfulRequests": 1180,
  "totalErrorRequests": 70,
  "averageResponseTimeMs": 245.5,
  "slowRequestsCount": 15,
  "overallErrorRate": 5.6,
  "overallSlowRequestRate": 1.2,
  "lastUpdated": "2025-09-15T10:30:45.123"
}
```

### Performance Summary Response
```json
{
  "overview": {
    "totalRequests": 1250,
    "averageResponseTime": "245.50 ms",
    "errorRate": "5.60%",
    "slowRequestRate": "1.20%"
  },
  "topSlowEndpoints": [
    {
      "endpoint": "GET /v4/videos/search",
      "averageResponseTime": "1250.30 ms",
      "totalRequests": 45,
      "slowRequestRate": "15.56%"
    }
  ]
}
```

## Future Enhancements

- Database persistence for historical data
- Real-time WebSocket updates
- Alert system for performance thresholds
- Distributed tracing integration
- Custom metric collection
- Performance regression detection
