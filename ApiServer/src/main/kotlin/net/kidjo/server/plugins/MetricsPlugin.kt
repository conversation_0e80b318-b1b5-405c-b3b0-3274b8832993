package net.kidjo.server.plugins

import io.ktor.server.application.*
import io.ktor.server.metrics.micrometer.*
import io.ktor.server.plugins.callid.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.micrometer.core.instrument.Clock
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.Timer
import io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics
import io.micrometer.core.instrument.binder.system.ProcessorMetrics
import io.micrometer.core.instrument.binder.system.UptimeMetrics
import io.micrometer.prometheus.PrometheusConfig
import io.micrometer.prometheus.PrometheusMeterRegistry
import org.slf4j.LoggerFactory

fun Application.configureMetrics() {
    val logger = LoggerFactory.getLogger("MetricsPlugin")
    
    // Create Prometheus registry
    val prometheusRegistry = PrometheusMeterRegistry(PrometheusConfig.DEFAULT, Clock.SYSTEM)
    
    // Add JVM and system metrics
    ClassLoaderMetrics().bindTo(prometheusRegistry)
    JvmMemoryMetrics().bindTo(prometheusRegistry)
    JvmGcMetrics().bindTo(prometheusRegistry)
    JvmThreadMetrics().bindTo(prometheusRegistry)
    ProcessorMetrics().bindTo(prometheusRegistry)
    UptimeMetrics().bindTo(prometheusRegistry)
    
    // Install Micrometer plugin
    install(MicrometerMetrics) {
        registry = prometheusRegistry
        
        // Configure request metrics with detailed tags
        timers { call, exception ->
            val path = call.request.path()
            val method = call.request.httpMethod.value
            val status = call.response.status()?.value?.toString() ?: "unknown"
            val endpoint = extractEndpoint(path)
            
            tag("method", method)
            tag("endpoint", endpoint)
            tag("status", status)
            tag("path_template", getPathTemplate(path))
            
            // Add exception information if present
            exception?.let { 
                tag("exception", it.javaClass.simpleName)
            }
        }
        
        // Configure distribution metrics for latency percentiles
        distributionStatisticConfig = io.micrometer.core.instrument.distribution.DistributionStatisticConfig.builder()
            .percentiles(0.5, 0.75, 0.90, 0.95, 0.99)
            .percentilesHistogram(true)
            .build()
    }
    
    // Custom metrics for API monitoring
    val apiRequestCounter = Counter.builder("kidjo_api_requests_total")
        .description("Total number of API requests")
        .register(prometheusRegistry)
    
    val apiLatencyTimer = Timer.builder("kidjo_api_request_duration")
        .description("API request latency")
        .register(prometheusRegistry)
    
    // Intercept requests for custom metrics
    intercept(ApplicationCallPipeline.Monitoring) {
        val startTime = System.nanoTime()
        val path = call.request.path()
        val method = call.request.httpMethod.value
        
        try {
            proceed()
        } finally {
            val duration = System.nanoTime() - startTime
            val status = call.response.status()?.value?.toString() ?: "unknown"
            val endpoint = extractEndpoint(path)
            
            // Record custom metrics
            apiRequestCounter.increment(
                io.micrometer.core.instrument.Tags.of(
                    "method", method,
                    "endpoint", endpoint,
                    "status", status
                )
            )
            
            apiLatencyTimer.record(duration, java.util.concurrent.TimeUnit.NANOSECONDS)
            
            // Log high latency requests (> 1 second)
            val durationMs = duration / 1_000_000
            if (durationMs > 1000) {
                logger.warn("High latency request: $method $path - ${durationMs}ms")
            }
        }
    }
    
    // Add metrics endpoint
    routing {
        get("/metrics") {
            call.respond(prometheusRegistry.scrape())
        }
        
        get("/metrics/health") {
            val metrics = mapOf(
                "status" to "healthy",
                "timestamp" to System.currentTimeMillis(),
                "uptime" to java.lang.management.ManagementFactory.getRuntimeMXBean().uptime,
                "total_requests" to apiRequestCounter.count(),
                "average_latency_ms" to String.format("%.2f", apiLatencyTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS))
            )
            call.respond(metrics)
        }
    }
    
    logger.info("Metrics plugin configured with Prometheus registry")
    logger.info("Metrics available at /metrics endpoint")
    logger.info("Health metrics available at /metrics/health endpoint")
}

/**
 * Extract meaningful endpoint name from path for metrics grouping
 */
private fun extractEndpoint(path: String): String {
    return when {
        path.startsWith("/api/v4/") -> {
            val segments = path.split("/")
            if (segments.size >= 4) {
                "/api/v4/${segments[3]}"
            } else {
                "/api/v4/unknown"
            }
        }
        path.startsWith("/api/v3/") -> {
            val segments = path.split("/")
            if (segments.size >= 4) {
                "/api/v3/${segments[3]}"
            } else {
                "/api/v3/unknown"
            }
        }
        path.startsWith("/api/v2/") -> {
            val segments = path.split("/")
            if (segments.size >= 4) {
                "/api/v2/${segments[3]}"
            } else {
                "/api/v2/unknown"
            }
        }
        path == "/checkHealth" || path == "/healthCheck" -> "/health"
        path.startsWith("/metrics") -> "/metrics"
        path.startsWith("/assets/") -> "/assets"
        path.startsWith("/static/") -> "/static"
        else -> path.split("/").take(3).joinToString("/").ifEmpty { "/" }
    }
}

/**
 * Get path template for better grouping (replace IDs with placeholders)
 */
private fun getPathTemplate(path: String): String {
    return path.replace(Regex("\\d+"), "{id}")
        .replace(Regex("[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"), "{uuid}")
}
