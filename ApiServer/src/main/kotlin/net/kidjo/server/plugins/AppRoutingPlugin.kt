package net.kidjo.server.plugins

import io.ktor.http.*
import io.ktor.http.auth.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import net.kidjo.server.api2.Api2Controller
import net.kidjo.server.api3.Api3Controller
import net.kidjo.server.api4.*
import net.kidjo.server.api4.extension.subscription.digitalvirgo.unsubscribe
import net.kidjo.server.api4.extension.subscription.logicom.check
import net.kidjo.server.api4.publichers.*
import net.kidjo.server.api5.*
import net.kidjo.server.api5.publichers.*
import net.kidjo.server.api6.partnersController.Api6UserController
import net.kidjo.server.main.PlatformConfig
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.cachedatabase.getUserJwtFromBlackList
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.injector.SharedInjector
import net.kidjo.server.subscriptionview2.SubscriptionView2Controller
import net.kidjo.server.utils.VersionReader
import net.kidjo.server.webserver3.WebServer3Controller
import org.json.JSONObject
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.util.*
import kotlin.collections.set

fun Application.configureRouting(args: Array<String>) {
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))

    val logger = LoggerFactory.getLogger("MainServer")

    val envString = args[0]
    val config = Config(Config.AppTypeId.SERVER, Config.Env.FromString(envString))

    val platformInjector = PlatformInjector.Build(SharedInjector.Build(config), PlatformConfig(config))
    val platformConfig = platformInjector.platformConfig
    val platformJwtManager = platformInjector.sharedInjector.jwtManager

    //api2
    val api2Controller = Api2Controller(platformInjector)
    val subscriptionView2Controller = SubscriptionView2Controller(platformInjector)

    //api3
    val api3Controller = Api3Controller(platformInjector)
    val webserver3Controller = WebServer3Controller(platformInjector)

    //api4
    val api4AccountController = Api4AccountController(platformInjector)
    val api4SubscriptionController = Api4SubscriptionController(platformInjector)
    val api4BraintreeController = Api4BraintreeController(platformInjector)
    val api4UserLoginController = Api4UserController(platformInjector)

    val api4BackofficeController = Api4BackofficeController(platformInjector)
    val api4CardsController = Api4CardsController(platformInjector)
    val api4LicensesController = Api4LicensesController(platformInjector)
    val api4CountryController = Api4CountryController(platformInjector)
    val api4CouponController = Api4CouponController(platformInjector)
    val api4NewsLetterController = Api4NewsLetterController(platformInjector)
    val api4ForceUpdateController = Api4ApkForceUpdateController(platformInjector)

    val api4DigitalvirgoController = Api4DigitalVirgoController(platformInjector)
    val api4SevamediaController = Api4SevamediaController(platformInjector)
    val api4OrangeController = Api4OrangeController(platformInjector)
    val api4LogicomController = Api4LogicomController(platformInjector)
    val api4DocomoController = Api4DocomoController(platformInjector)
    val api4VewdController = Api4VewdController(platformInjector)
    val api4ZattooController = Api4ZattooController(platformInjector)
    val api4TclController = Api4TclController(platformInjector)
    val api4VideoController = Api4VideoController(platformInjector)

    //api5
    val api5DigitalvirgoController = Api5DVController(platformInjector)
    val api4TWTController = Api4TWTController(platformInjector)
    val api5UserController = Api5UserController(platformInjector)
    val api5CardsController = Api5CardsController(platformInjector)
    val api5CafeynController = Api5CafeynController(platformInjector)
    val api5AccountController = Api5AccountController(platformInjector)
    val api5SubscriptionController = Api5SubscriptionController(platformInjector)
    val api5CouponController = Api5CouponController(platformInjector)
    val api5PartnerSubscriptionController = Api5PartnerSubscriptionController(platformInjector)
    val api5BackofficeController=Api5BackofficeController(platformInjector)
    val api5TWTController = Api5TWTController(platformInjector)

    //api6
    val api6SubscriptionController = Api6SubscriptionController(platformInjector)
    val api6UserLoginController = Api6UserController(platformInjector)

    configureAuthentication(platformJwtManager, api4AccountController)

    install(Routing) {
        // New routing system
        route("/api") {

            //Version 6
            route("/v6"){
                api6UserLoginController.installRoutes(this)
                authenticate("jwt") {
                    intercept(ApplicationCallPipeline.Call) {
                        val authHeader = call.request.parseAuthorizationHeader()
                        if (!(authHeader == null || authHeader !is HttpAuthHeader.Single || authHeader.authScheme != "Bearer")) {
                            try {
                                val isTokenPersist =
                                    platformInjector.sharedInjector.cacheDatabase.getUserJwtFromBlackList(authHeader.blob)
                                if (isTokenPersist) {
                                    call.respond(
                                        UnauthorizedResponse(
                                            HttpAuthHeader.Parameterized(
                                                "Bearer",
                                                mapOf("realm" to "api1")
                                            )
                                        )
                                    )
                                    return@intercept finish()
                                }
                            } catch (e: Exception) {
                                logger.info("THROW REDIS ERROR: " + e.message)
                            }
                        }
                    }
                }
                authenticate("jwt", optional = true) {
                    api6SubscriptionController.installRoutes(this)
                }
            }


            // Version 5
            route("/v5") {
                api5DigitalvirgoController.installRoutes(this)
                api5UserController.installRoutes(this)
                api5CardsController.installRoutes(this)
                api5CafeynController.installRoutes(this)
                api5BackofficeController.installRoutes(this)
                authenticate("jwt") {
                    intercept(ApplicationCallPipeline.Call) {
                        val authHeader = call.request.parseAuthorizationHeader()
                        if (!(authHeader == null || authHeader !is HttpAuthHeader.Single || authHeader.authScheme != "Bearer")) {
                            try {
                                val isTokenPersist =
                                    platformInjector.sharedInjector.cacheDatabase.getUserJwtFromBlackList(authHeader.blob)
                                if (isTokenPersist) {
                                    call.respond(
                                        UnauthorizedResponse(
                                            HttpAuthHeader.Parameterized(
                                                "Bearer",
                                                mapOf("realm" to "api1")
                                            )
                                        )
                                    )
                                    return@intercept finish()
                                }
                            } catch (e: Exception) {
                                logger.info("THROW REDIS ERROR: " + e.message)
                            }
                        }
                    }
                    api5AccountController.installRoutes(this)

                }
                authenticate("jwt", optional = true) {
                    api5SubscriptionController.installRoutes(this)
                    api5CouponController.installRoutes(this)
                    api5TWTController.installRoutes(this)

                }
                authenticate("apikey","basic-auth","jwt") {
                    api5PartnerSubscriptionController.installRoutes(this)
                }


            }
            // Version 4
            route("/v4") {
                get("/logicom/check") { api4LogicomController.check(call) }

                api4SevamediaController.installRoutes(this)
                api4UserLoginController.installRoutes(this)
                api4LicensesController.installRoutes(this)
                api4CountryController.installRoutes(this)
                api4VewdController.installRoutes(this)
                api4ZattooController.installRoutes(this)
                api4NewsLetterController.installRoutes(this)
                api4ForceUpdateController.installRoutes(this)
                api4BackofficeController.installRoutes(this)
                api4DigitalvirgoController.installRoutes(this)
                api4CardsController.installRoutes(this)
                api4OrangeController.installRoutes(this)

                authenticate("jwt") {

                    intercept(ApplicationCallPipeline.Call) {
                        val authHeader = call.request.parseAuthorizationHeader()

                        if (!(authHeader == null || authHeader !is HttpAuthHeader.Single || authHeader.authScheme != "Bearer")) {
                            try {
                                val isTokenPersist =
                                    platformInjector.sharedInjector.cacheDatabase.getUserJwtFromBlackList(authHeader.blob)
                                if (isTokenPersist) {
                                    call.respond(
                                        UnauthorizedResponse(
                                            HttpAuthHeader.Parameterized(
                                                "Bearer",
                                                mapOf("realm" to "api1")
                                            )
                                        )
                                    )
                                    return@intercept finish()
                                }
                            } catch (e: Exception) {
                                logger.info("THROW REDIS ERROR: " + e.message)
                            }
                        }
                    }


                    post("/digitalvirgo-kidjo/unsubscribe") { api4DigitalvirgoController.unsubscribe(call) }

                    api4AccountController.installRoutes(this)
                    api4BraintreeController.installRoutes(this)
                    api4TclController.installRoutes(this)
                    api4DocomoController.installRoutes(this)
                    api4CouponController.installRoutes(this)
                    api4LogicomController.installRoutes(this)
                }

                authenticate("jwt", optional = true) {
                    api4SubscriptionController.installRoutes(this)
                    api4TWTController.installRoutes(this)
                }

                authenticate("apikey") {
                    api4VideoController.installRoutes(this)

                }

            }

            // Version 3
            route("/v3") {
                api3Controller.installRoutes(this)
            }

            // Version 2
            route("/v2") {
                api2Controller.installRoutes(this)
                subscriptionView2Controller.installRoutes(this)
            }

            // Miscellaneous tooling endpoints
            get("/health") {
                logger.info("Health check - ${LocalDateTime.now()}")
                call.respondText("Server is responding!", ContentType.Text.Plain)
            }

            get("/version") {
                val versions = mutableMapOf<String, String>()
                versions["Last Commit"] = VersionReader.getPropertyValueAsString("commitSHA")
                versions["Tag"] = VersionReader.getPropertyValueAsString("latestTag")
                call.respond(versions)
            }

            get("/server/report") {
                val report = JSONObject()
                report.put("serverVersion", platformConfig.version)
                report.put("webServerVersion", platformConfig.webSiteVersion)
                report.put("webApplicationVersion", platformConfig.webAppVersion)
                report.put("iosReviewBuildVersion", platformConfig.iOSReviewBuild)
                call.respondText(report.toString(), ContentType.Application.Json)
            }
        }

        // Old routing
        route("/v2.0") {
            api2Controller.installRoutes(this)
            subscriptionView2Controller.installRoutes(this)
        }

        route("app/api/3") {
            api3Controller.installRoutes(this)
        }

        route("app/api/4") {
            api4AccountController.installRoutes(this)
        }

        get("/checkHealth") {
            call.respondText("pong", ContentType.Text.Html)
        }

        get("/healthCheck") {
            println("HealthCheck - ${LocalDateTime.now()}")
            call.respondText("pong", ContentType.Text.Html)
        }

        get("app/version") {
            call.respondText(platformConfig.version, ContentType.Text.Html)
        }

        get("/serverReport") {
            val text = "Server version: ${platformConfig.version}\n" +
                    "Web Server version: ${platformConfig.webSiteVersion}\n" +
                    "Web App version: ${platformConfig.webAppVersion}\n" +
                    "iOS review build: ${platformConfig.iOSReviewBuild}\n"
            call.respondText(text, ContentType.Text.Html)
        }

        // Static pages
        webserver3Controller.installRoutes(this)
    }

}
