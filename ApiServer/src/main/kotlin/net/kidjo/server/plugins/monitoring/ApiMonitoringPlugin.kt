package net.kidjo.server.plugins.monitoring

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.application.hooks.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.plugins.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.util.*
import net.kidjo.server.shared.models.User
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.util.*

/**
 * API Monitoring Plugin using Aspect-Oriented Programming approach
 * This plugin intercepts all API calls without modifying existing controller code
 */
class ApiMonitoringPlugin(private val config: ApiMonitoringConfig) {
    
    private val storage = ApiMonitoringStorage(config)
    private val logger = LoggerFactory.getLogger("ApiMonitoring")
    
    companion object Plugin : BaseApplicationPlugin<Application, ApiMonitoringConfig, ApiMonitoringPlugin> {
        override val key = AttributeKey<ApiMonitoringPlugin>("ApiMonitoring")
        
        override fun install(pipeline: Application, configure: ApiMonitoringConfig.() -> Unit): ApiMonitoringPlugin {
            val config = ApiMonitoringConfig().apply(configure)
            val plugin = ApiMonitoringPlugin(config)
            
            if (!config.enabled) {
                plugin.logger.info("API Monitoring is disabled")
                return plugin
            }
            
            plugin.logger.info("Installing API Monitoring Plugin with config: $config")
            
            // Install monitoring hooks using AOP approach
            plugin.installMonitoringHooks(pipeline)
            
            return plugin
        }
    }
    
    private fun installMonitoringHooks(application: Application) {
        // Hook into the call pipeline to monitor all requests
        application.intercept(ApplicationCallPipeline.Monitoring) {
            if (shouldMonitorRequest(call)) {
                monitorApiCall(call)
            }
        }
    }
    
    private fun shouldMonitorRequest(call: ApplicationCall): Boolean {
        val path = call.request.path()
        
        // Skip monitoring for excluded paths
        if (config.excludePaths.any { path.startsWith(it) }) {
            return false
        }
        
        // Skip static resources
        if (path.startsWith("/assets/") || path.startsWith("/static/") || 
            path.endsWith(".js") || path.endsWith(".css") || 
            path.endsWith(".png") || path.endsWith(".jpg") || 
            path.endsWith(".ico") || path.endsWith(".svg")) {
            return false
        }
        
        return true
    }
    
    private suspend fun monitorApiCall(call: ApplicationCall) {
        val requestId = UUID.randomUUID().toString()
        val startTime = LocalDateTime.now()
        val startTimeMillis = System.currentTimeMillis()
        
        // Collect request information
        val request = call.request
        val method = request.httpMethod.value
        val path = request.path()
        val fullUrl = request.uri
        val userAgent = request.headers["User-Agent"]
        val remoteAddress = request.origin.remoteAddress
        val remoteHost = request.origin.remoteHost
        
        // Collect relevant headers
        val requestHeaders = mutableMapOf<String, String>()
        config.includeHeaders.forEach { headerName ->
            request.headers[headerName]?.let { headerValue ->
                requestHeaders[headerName] = if (headerName.equals("Authorization", ignoreCase = true)) {
                    // Mask authorization header for security
                    "Bearer ***"
                } else {
                    headerValue
                }
            }
        }
        
        // Estimate request size
        val requestSize = estimateRequestSize(request)
        
        // Extract user/session info if available
        val userId = extractUserId(call)
        val sessionId = extractSessionId(call)
        
        // Create initial request record
        var requestRecord = ApiRequestRecord(
            requestId = requestId,
            method = method,
            path = path,
            fullUrl = fullUrl,
            userAgent = userAgent,
            remoteAddress = remoteAddress,
            remoteHost = remoteHost,
            requestHeaders = requestHeaders,
            requestSize = requestSize,
            startTime = startTime,
            endTime = null,
            responseStatus = null,
            responseHeaders = null,
            responseSize = null,
            durationMs = null,
            errorMessage = null,
            userId = userId,
            sessionId = sessionId
        )
        
        if (config.enableDetailedLogging) {
            logger.info("API Request Started - ID: $requestId, Method: $method, Path: $path, User: $userId")
        }
        
        try {
            // Continue with the request processing
            /*proceed()*/
            
            // Collect response information after processing
            val endTime = LocalDateTime.now()
            val endTimeMillis = System.currentTimeMillis()
            val duration = endTimeMillis - startTimeMillis
            
            val response = call.response
            val responseStatus = response.status()?.value
            val responseHeaders = mutableMapOf<String, String>()
            
            // Collect response headers
            config.includeHeaders.forEach { headerName ->
                response.headers[headerName]?.let { headerValue ->
                    responseHeaders[headerName] = headerValue
                }
            }
            
            // Estimate response size
            val responseSize = estimateResponseSize(response)
            
            // Update request record with response information
            requestRecord = requestRecord.copy(
                endTime = endTime,
                responseStatus = responseStatus,
                responseHeaders = responseHeaders,
                responseSize = responseSize,
                durationMs = duration
            )
            
            // Store the completed request record
            if (config.enableStatisticsCollection) {
                storage.addRequest(requestRecord)
            }
            
            // Log completion
            if (config.enableDetailedLogging) {
                val statusText = if (requestRecord.isError) "ERROR" else "SUCCESS"
                logger.info("API Request Completed - ID: $requestId, Status: $responseStatus ($statusText), Duration: ${duration}ms")
                
                if (requestRecord.isSlowRequest) {
                    logger.warn("SLOW REQUEST DETECTED - ID: $requestId, Method: $method, Path: $path, Duration: ${duration}ms")
                }
            }
            
        } catch (exception: Throwable) {
            // Handle errors during request processing
            val endTime = LocalDateTime.now()
            val endTimeMillis = System.currentTimeMillis()
            val duration = endTimeMillis - startTimeMillis
            
            requestRecord = requestRecord.copy(
                endTime = endTime,
                responseStatus = 500,
                durationMs = duration,
                errorMessage = exception.message ?: exception.javaClass.simpleName
            )
            
            // Store the error request record
            if (config.enableStatisticsCollection) {
                storage.addRequest(requestRecord)
            }
            
            logger.error("API Request Failed - ID: $requestId, Method: $method, Path: $path, Duration: ${duration}ms, Error: ${exception.message}", exception)
            
            // Re-throw the exception to maintain normal error handling
            throw exception
        }
    }
    
    private suspend fun estimateRequestSize(request: ApplicationRequest): Long {
        return try {
            // Try to get content length from headers
            request.headers["Content-Length"]?.toLongOrNull() ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
    
    private fun estimateResponseSize(response: ApplicationResponse): Long {
        return try {
            // Try to get content length from headers
            response.headers["Content-Length"]?.toLongOrNull() ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
    
    private fun extractUserId(call: ApplicationCall): String? {
        return try {
            // Try to extract user ID from JWT token or User principal
            call.principal<JWTPrincipal>()?.payload?.getClaim("id")?.asLong()?.toString()
                ?: call.principal<User>()?.id
                ?: call.principal<UserIdPrincipal>()?.name
                ?: call.request.headers["X-User-ID"]
        } catch (e: Exception) {
            null
        }
    }
    
    private fun extractSessionId(call: ApplicationCall): String? {
        return try {
            call.request.headers["X-Session-ID"] 
                ?: call.request.cookies["JSESSIONID"]
        } catch (e: Exception) {
            null
        }
    }
    
    // Public API for accessing monitoring data
    fun getStatistics(): ApiMonitoringStatistics = storage.getStatistics()
    
    fun getRecentRequests(limit: Int = 100): List<ApiRequestRecord> = storage.getRecentRequests(limit)
    
    fun getEndpointStatistics(method: String, path: String): EndpointStatistics? = 
        storage.getEndpointStatistics(method, path)
    
    fun clearStatistics() = storage.clearStatistics()
    
    fun getConfig(): ApiMonitoringConfig = config
}

// Extension function to easily access the monitoring plugin
val Application.apiMonitoring: ApiMonitoringPlugin
    get() = plugin(ApiMonitoringPlugin)
