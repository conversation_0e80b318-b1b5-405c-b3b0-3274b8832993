package net.kidjo.server.plugins

import io.ktor.server.application.*
import io.ktor.server.plugins.*
import io.ktor.server.plugins.callloging.*
import io.ktor.server.request.*
import org.slf4j.event.Level

fun Application.configureLogging() {
    install(CallLogging) {
        level = Level.INFO
        format { call ->
            val status = call.response.status()
            val httpMethod = call.request.httpMethod.value
            val userAgent = call.request.headers["User-Agent"]
            val host = call.request.origin.remoteHost
            val remoteIP = call.request.origin.remoteAddress
            val requestUrl=call.request.path()
            "Request URL:${requestUrl} Status: $status, HTTP method: $httpMethod, User agent: $userAgent ,host:$host ,remoteIP:$remoteIP"
        }
    }
}
