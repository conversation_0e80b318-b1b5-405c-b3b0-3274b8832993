package net.kidjo.server.plugins

import io.ktor.server.application.*
import io.ktor.server.plugins.*
import io.ktor.server.plugins.callloging.*
import io.ktor.server.plugins.callid.*
import io.ktor.server.request.*
import org.slf4j.event.Level
import kotlin.time.measureTime
import kotlin.time.Duration

fun Application.configureLogging() {
    // Install CallId for request tracking
    install(CallId) {
        header("X-Request-ID")
        generate { "req-${System.currentTimeMillis()}-${(1000..9999).random()}" }
    }

    install(CallLogging) {
        level = Level.INFO
        callIdMdc("request_id")

        format { call ->
            val startTime = call.attributes.getOrNull(RequestTimingKey)
            val duration = if (startTime != null) {
                System.currentTimeMillis() - startTime
            } else null

            val status = call.response.status()
            val httpMethod = call.request.httpMethod.value
            val userAgent = call.request.headers["User-Agent"]
            val host = call.request.origin.remoteHost
            val remoteIP = call.request.origin.remoteAddress
            val requestUrl = call.request.path()
            val callId = call.callId
            val latencyInfo = duration?.let { " | Latency: ${it}ms" } ?: ""

            "[$callId] $httpMethod $requestUrl | Status: $status | Remote: $remoteIP | User-Agent: $userAgent$latencyInfo"
        }

        // Filter out health check and static resource endpoints to reduce noise
        filter { call ->
            val path = call.request.path()
            !path.startsWith("/assets/") &&
            !path.startsWith("/static/") &&
            !path.endsWith(".js") &&
            !path.endsWith(".css") &&
            !path.endsWith(".png") &&
            !path.endsWith(".jpg") &&
            !path.endsWith(".ico") &&
            !path.endsWith(".svg") &&
            path != "/checkHealth" &&
            path != "/healthCheck"
        }
    }

    // Add request timing interceptor
    intercept(ApplicationCallPipeline.Setup) {
        call.attributes.put(RequestTimingKey, System.currentTimeMillis())
    }
}

// Attribute key for storing request start time
private val RequestTimingKey = AttributeKey<Long>("RequestStartTime")
