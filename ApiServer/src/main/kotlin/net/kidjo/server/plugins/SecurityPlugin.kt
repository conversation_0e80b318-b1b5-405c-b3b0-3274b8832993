package net.kidjo.server.plugins

import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.auth.Authentication
import io.ktor.server.auth.basic
import io.ktor.server.auth.jwt.jwt
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.plugins.provider.apiKey
import net.kidjo.server.shared.database.user_getById
import net.kidjo.server.shared.security.JwtKidjoServerManager
import net.kidjo.server.utils.getPartner
import net.kidjo.server.utils.validateUserCredentials

const val API_KEY_HEADER = "X-API-KEY"
fun Application.configureAuthentication(
    platformJwtManager: JwtKidjoServerManager,
    api4AccountController: Api4AccountController,
) {

    install(Authentication) {
        jwt("jwt") {
            verifier(platformJwtManager.verifier)
            realm = "Kidjo Server"
            validate {
                val payload = it.payload
                val claim = payload.getClaim("id")
                val claimString = claim.asLong()
                val user = api4AccountController.databaseController.user_getById(claimString)
                user
            }

        }
        apiKey("apikey") {
            headerName = API_KEY_HEADER
            validate { key ->
                getPartner(key)
            }
        }
        basic("basic-auth")
        {
            validate { credentials ->
                validateUserCredentials(credentials.name, credentials.password)
            }
        }
    }
}
