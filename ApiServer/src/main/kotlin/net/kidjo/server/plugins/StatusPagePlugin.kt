package net.kidjo.server.plugins

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.plugins.*
import io.ktor.server.plugins.statuspages.*
import net.kidjo.exceptions.SubscriptionException
import net.kidjo.server.shared.extensions.respondAppError
import net.kidjo.server.shared.extensions.respondErrorWithLocalMessage

fun Application.configureStatusPage() {
    install(StatusPages) {
        this.exception<SubscriptionException> {
                call,
                cause,
            ->
            call.respondAppError(
                HttpStatusCode.BadRequest,
                HttpStatusCode.BadRequest.value.toString(),
                cause.message
            )

        }
        this.exception<Throwable> {
                call,
                cause,
            ->
            call.respondErrorWithLocalMessage(HttpStatusCode.UnprocessableEntity, cause.message)
            throw cause
        }
        this.exception<BadRequestException> { call, cause ->
            call.respondAppError(
                HttpStatusCode.BadRequest,
                HttpStatusCode.BadRequest.value.toString(),
                cause.message
            )
        }
    }
}
