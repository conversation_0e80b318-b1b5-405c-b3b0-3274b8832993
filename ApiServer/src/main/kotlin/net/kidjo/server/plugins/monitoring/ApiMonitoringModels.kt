package net.kidjo.server.plugins.monitoring

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * Represents a single API request monitoring record
 */
data class ApiRequestRecord(
    val requestId: String,
    val method: String,
    val path: String,
    val fullUrl: String,
    val userAgent: String?,
    val remoteAddress: String?,
    val remoteHost: String?,
    val requestHeaders: Map<String, String>,
    val requestSize: Long,
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    val startTime: LocalDateTime,
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    val endTime: LocalDateTime?,
    val responseStatus: Int?,
    val responseHeaders: Map<String, String>?,
    val responseSize: Long?,
    val durationMs: Long?,
    val errorMessage: String?,
    val userId: String?,
    val sessionId: String?
) {
    val isCompleted: Boolean get() = endTime != null
    val isError: Boolean get() = responseStatus != null && responseStatus >= 400
    val isSlowRequest: Boolean get() = durationMs != null && durationMs > 1000 // > 1 second
}

/**
 * Aggregated statistics for an API endpoint
 */
data class EndpointStatistics(
    val method: String,
    val path: String,
    val totalRequests: Long = 0,
    val successfulRequests: Long = 0,
    val errorRequests: Long = 0,
    val averageResponseTimeMs: Double = 0.0,
    val minResponseTimeMs: Long = Long.MAX_VALUE,
    val maxResponseTimeMs: Long = 0,
    val totalResponseTimeMs: Long = 0,
    val slowRequestsCount: Long = 0, // requests > 1 second
    val last24HourRequests: Long = 0,
    val lastRequestTime: LocalDateTime? = null
) {
    val errorRate: Double get() = if (totalRequests > 0) (errorRequests.toDouble() / totalRequests) * 100 else 0.0
    val slowRequestRate: Double get() = if (totalRequests > 0) (slowRequestsCount.toDouble() / totalRequests) * 100 else 0.0
    val successRate: Double get() = if (totalRequests > 0) (successfulRequests.toDouble() / totalRequests) * 100 else 0.0
}

/**
 * Overall API monitoring statistics
 */
data class ApiMonitoringStatistics(
    val totalRequests: Long = 0,
    val totalSuccessfulRequests: Long = 0,
    val totalErrorRequests: Long = 0,
    val averageResponseTimeMs: Double = 0.0,
    val slowRequestsCount: Long = 0,
    val endpointStatistics: Map<String, EndpointStatistics> = emptyMap(),
    val topSlowEndpoints: List<EndpointStatistics> = emptyList(),
    val topErrorEndpoints: List<EndpointStatistics> = emptyList(),
    val recentRequests: List<ApiRequestRecord> = emptyList(),
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    val lastUpdated: LocalDateTime = LocalDateTime.now()
) {
    val overallErrorRate: Double get() = if (totalRequests > 0) (totalErrorRequests.toDouble() / totalRequests) * 100 else 0.0
    val overallSlowRequestRate: Double get() = if (totalRequests > 0) (slowRequestsCount.toDouble() / totalRequests) * 100 else 0.0
}

/**
 * Configuration for API monitoring
 */
data class ApiMonitoringConfig(
    var enabled: Boolean = true,
    var maxRecentRequests: Int = 1000,
    var slowRequestThresholdMs: Long = 1000,
    var enableRequestBodyLogging: Boolean = false,
    var enableResponseBodyLogging: Boolean = false,
    var excludePaths: Set<String> = setOf("/healthCheck", "/checkHealth", "/server/report"),
    var includeHeaders: Set<String> = setOf("User-Agent", "Content-Type", "Authorization"),
    var enableDetailedLogging: Boolean = true,
    var enableStatisticsCollection: Boolean = true
)

/**
 * Thread-safe in-memory storage for monitoring data
 */
class ApiMonitoringStorage(private val config: ApiMonitoringConfig) {
    private val recentRequests = mutableListOf<ApiRequestRecord>()
    private val endpointStats = ConcurrentHashMap<String, EndpointStatistics>()
    private val totalRequestsCounter = AtomicLong(0)
    private val totalSuccessfulRequestsCounter = AtomicLong(0)
    private val totalErrorRequestsCounter = AtomicLong(0)
    private val totalResponseTimeCounter = AtomicLong(0)
    private val slowRequestsCounter = AtomicLong(0)

    @Synchronized
    fun addRequest(request: ApiRequestRecord) {
        // Add to recent requests (maintain max size)
        recentRequests.add(request)
        if (recentRequests.size > config.maxRecentRequests) {
            recentRequests.removeAt(0)
        }

        // Update counters
        totalRequestsCounter.incrementAndGet()
        
        if (request.isCompleted) {
            if (request.isError) {
                totalErrorRequestsCounter.incrementAndGet()
            } else {
                totalSuccessfulRequestsCounter.incrementAndGet()
            }

            request.durationMs?.let { duration ->
                totalResponseTimeCounter.addAndGet(duration)
                if (duration > config.slowRequestThresholdMs) {
                    slowRequestsCounter.incrementAndGet()
                }
            }

            // Update endpoint statistics
            updateEndpointStatistics(request)
        }
    }

    private fun updateEndpointStatistics(request: ApiRequestRecord) {
        val endpointKey = "${request.method}:${request.path}"
        
        endpointStats.compute(endpointKey) { _, existing ->
            val current = existing ?: EndpointStatistics(request.method, request.path)
            
            val newTotalRequests = current.totalRequests + 1
            val newSuccessfulRequests = if (request.isError) current.successfulRequests else current.successfulRequests + 1
            val newErrorRequests = if (request.isError) current.errorRequests + 1 else current.errorRequests
            
            val duration = request.durationMs ?: 0
            val newTotalResponseTime = current.totalResponseTimeMs + duration
            val newAverageResponseTime = if (newTotalRequests > 0) newTotalResponseTime.toDouble() / newTotalRequests else 0.0
            val newMinResponseTime = if (duration > 0) minOf(current.minResponseTimeMs, duration) else current.minResponseTimeMs
            val newMaxResponseTime = maxOf(current.maxResponseTimeMs, duration)
            val newSlowRequestsCount = if (duration > config.slowRequestThresholdMs) current.slowRequestsCount + 1 else current.slowRequestsCount

            current.copy(
                totalRequests = newTotalRequests,
                successfulRequests = newSuccessfulRequests,
                errorRequests = newErrorRequests,
                averageResponseTimeMs = newAverageResponseTime,
                minResponseTimeMs = newMinResponseTime,
                maxResponseTimeMs = newMaxResponseTime,
                totalResponseTimeMs = newTotalResponseTime,
                slowRequestsCount = newSlowRequestsCount,
                lastRequestTime = request.endTime ?: request.startTime
            )
        }
    }

    fun getStatistics(): ApiMonitoringStatistics {
        val totalRequests = totalRequestsCounter.get()
        val totalSuccessful = totalSuccessfulRequestsCounter.get()
        val totalErrors = totalErrorRequestsCounter.get()
        val totalResponseTime = totalResponseTimeCounter.get()
        val slowRequests = slowRequestsCounter.get()
        
        val averageResponseTime = if (totalRequests > 0) totalResponseTime.toDouble() / totalRequests else 0.0
        
        val sortedByResponseTime = endpointStats.values.sortedByDescending { it.averageResponseTimeMs }.take(10)
        val sortedByErrors = endpointStats.values.sortedByDescending { it.errorRate }.take(10)
        
        return ApiMonitoringStatistics(
            totalRequests = totalRequests,
            totalSuccessfulRequests = totalSuccessful,
            totalErrorRequests = totalErrors,
            averageResponseTimeMs = averageResponseTime,
            slowRequestsCount = slowRequests,
            endpointStatistics = endpointStats.toMap(),
            topSlowEndpoints = sortedByResponseTime,
            topErrorEndpoints = sortedByErrors,
            recentRequests = recentRequests.takeLast(50), // Return last 50 for API response
            lastUpdated = LocalDateTime.now()
        )
    }

    @Synchronized
    fun getRecentRequests(limit: Int = 100): List<ApiRequestRecord> {
        return recentRequests.takeLast(limit)
    }

    fun getEndpointStatistics(method: String, path: String): EndpointStatistics? {
        return endpointStats["$method:$path"]
    }

    @Synchronized
    fun clearStatistics() {
        recentRequests.clear()
        endpointStats.clear()
        totalRequestsCounter.set(0)
        totalSuccessfulRequestsCounter.set(0)
        totalErrorRequestsCounter.set(0)
        totalResponseTimeCounter.set(0)
        slowRequestsCounter.set(0)
    }
}
