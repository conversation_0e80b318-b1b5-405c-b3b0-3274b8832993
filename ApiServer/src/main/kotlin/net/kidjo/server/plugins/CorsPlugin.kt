package net.kidjo.server.plugins

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.plugins.cors.routing.*

fun Application.configureCors() {
    install(CORS) {
        allowHost("localhost:8080")
        allowHost("localhost:8081")
        allowHost("localhost:8082")
        allowHost("localhost:8083")
        allowHost(
            host = "kidjo.tv",
            subDomains = listOf("www", "app", "api", "account", "backend", "website", "static"),
            schemes = listOf("http", "https")
        )
        allowHost("staging.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("staging.kidjo.smartnsoft.com", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("account.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("testaccount.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("devaccount.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("testapp.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("devapp.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("app.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("tv.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("test.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("testbo.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("devbo.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("backoffice.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("stories.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("dev-stories.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("teststories.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("devaccounts.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("dev-games.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("staging-games.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("games.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("dev-app.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("stage-app.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("apibook.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("apigames.kidjo.tv", subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("dev-tvbo-api.kidjo.tv",subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("stage-tvbo-api.kidjo.tv",subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowHost("apitvbackoffice.kidjo.tv",subDomains = listOf("www"), schemes = listOf("http", "https"))
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Delete)

        // Headers are case insensitive
        allowHeader("X-Kidjo-Date")
        allowHeader("X-Kidjo-DeviceId")
        allowHeader("X-Kidjo-Store")
        allowHeader("X-Kidjo-Build")
        allowHeader("X-Kidjo-DeviceType")
        allowHeader("Content-Type")
        allowHeader("Authorization")
        allowHeader("Accept")
        allowHeader("Cookie")
        allowHeader("Set-Cookie")
        allowHeader("X-Requested-With")
        allowCredentials = true
    }
}
