package net.kidjo.server.plugins.monitoring

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory
import java.io.InputStream

/**
 * Controller for API monitoring dashboard endpoints
 */
class MonitoringController(private val application: Application) {
    
    private val logger = LoggerFactory.getLogger("MonitoringController")
    
    fun installRoutes(routing: Routing) {
        routing {
            route("/monitoring") {

                // Serve monitoring dashboard HTML
                get("/dashboard") {
                    try {
                        val htmlStream: InputStream? = this::class.java.classLoader.getResourceAsStream("monitoring-dashboard.html")
                        if (htmlStream != null) {
                            val htmlContent = htmlStream.bufferedReader().use { it.readText() }
                            call.respondText(htmlContent, ContentType.Text.Html)
                        } else {
                            call.respond(HttpStatusCode.NotFound, "Dashboard not found")
                        }
                    } catch (e: Exception) {
                        logger.error("Error serving monitoring dashboard", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to load dashboard")
                    }
                }

                // Redirect root monitoring path to dashboard
                get {
                    call.respondRedirect("/monitoring/dashboard")
                }
                
                // Get overall API statistics
                get("/stats") {
                    try {
                        val stats = application.apiMonitoring.getStatistics()
                        call.respond(HttpStatusCode.OK, stats)
                    } catch (e: Exception) {
                        logger.error("Error getting monitoring statistics", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get statistics"))
                    }
                }
                
                // Get recent API requests
                get("/requests") {
                    try {
                        val limit = call.request.queryParameters["limit"]?.toIntOrNull() ?: 100
                        val requests = application.apiMonitoring.getRecentRequests(limit)
                        call.respond(HttpStatusCode.OK, mapOf(
                            "requests" to requests,
                            "total" to requests.size,
                            "limit" to limit
                        ))
                    } catch (e: Exception) {
                        logger.error("Error getting recent requests", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get recent requests"))
                    }
                }
                
                // Get statistics for a specific endpoint
                get("/endpoint/{method}/{path...}") {
                    try {
                        val method = call.parameters["method"] ?: return@get call.respond(
                            HttpStatusCode.BadRequest, 
                            mapOf("error" to "Method parameter is required")
                        )
                        val path = call.parameters.getAll("path")?.joinToString("/") ?: return@get call.respond(
                            HttpStatusCode.BadRequest, 
                            mapOf("error" to "Path parameter is required")
                        )
                        
                        val stats = application.apiMonitoring.getEndpointStatistics(method.uppercase(), "/$path")
                        if (stats != null) {
                            call.respond(HttpStatusCode.OK, stats)
                        } else {
                            call.respond(HttpStatusCode.NotFound, mapOf("error" to "Endpoint statistics not found"))
                        }
                    } catch (e: Exception) {
                        logger.error("Error getting endpoint statistics", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get endpoint statistics"))
                    }
                }
                
                // Get slow endpoints (endpoints with high average response time)
                get("/slow-endpoints") {
                    try {
                        val limit = call.request.queryParameters["limit"]?.toIntOrNull() ?: 10
                        val threshold = call.request.queryParameters["threshold"]?.toLongOrNull() ?: 1000
                        
                        val stats = application.apiMonitoring.getStatistics()
                        val slowEndpoints = stats.endpointStatistics.values
                            .filter { it.averageResponseTimeMs > threshold }
                            .sortedByDescending { it.averageResponseTimeMs }
                            .take(limit)
                        
                        call.respond(HttpStatusCode.OK, mapOf(
                            "slowEndpoints" to slowEndpoints,
                            "threshold" to threshold,
                            "limit" to limit,
                            "total" to slowEndpoints.size
                        ))
                    } catch (e: Exception) {
                        logger.error("Error getting slow endpoints", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get slow endpoints"))
                    }
                }
                
                // Get error-prone endpoints (endpoints with high error rate)
                get("/error-endpoints") {
                    try {
                        val limit = call.request.queryParameters["limit"]?.toIntOrNull() ?: 10
                        val minRequests = call.request.queryParameters["minRequests"]?.toLongOrNull() ?: 10
                        
                        val stats = application.apiMonitoring.getStatistics()
                        val errorEndpoints = stats.endpointStatistics.values
                            .filter { it.totalRequests >= minRequests && it.errorRate > 0 }
                            .sortedByDescending { it.errorRate }
                            .take(limit)
                        
                        call.respond(HttpStatusCode.OK, mapOf(
                            "errorEndpoints" to errorEndpoints,
                            "minRequests" to minRequests,
                            "limit" to limit,
                            "total" to errorEndpoints.size
                        ))
                    } catch (e: Exception) {
                        logger.error("Error getting error endpoints", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get error endpoints"))
                    }
                }
                
                // Get monitoring configuration
                get("/config") {
                    try {
                        val config = application.apiMonitoring.getConfig()
                        call.respond(HttpStatusCode.OK, config)
                    } catch (e: Exception) {
                        logger.error("Error getting monitoring config", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get config"))
                    }
                }
                
                // Clear monitoring statistics (useful for testing or reset)
                delete("/stats") {
                    try {
                        application.apiMonitoring.clearStatistics()
                        call.respond(HttpStatusCode.OK, mapOf("message" to "Statistics cleared successfully"))
                        logger.info("Monitoring statistics cleared")
                    } catch (e: Exception) {
                        logger.error("Error clearing statistics", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to clear statistics"))
                    }
                }
                
                // Get health check for monitoring system
                get("/health") {
                    try {
                        val stats = application.apiMonitoring.getStatistics()
                        val health = mapOf(
                            "status" to "healthy",
                            "totalRequests" to stats.totalRequests,
                            "lastUpdated" to stats.lastUpdated,
                            "monitoringEnabled" to application.apiMonitoring.getConfig().enabled
                        )
                        call.respond(HttpStatusCode.OK, health)
                    } catch (e: Exception) {
                        logger.error("Error getting monitoring health", e)
                        call.respond(HttpStatusCode.ServiceUnavailable, mapOf(
                            "status" to "unhealthy",
                            "error" to "Monitoring system error"
                        ))
                    }
                }
                
                // Get performance summary
                get("/performance-summary") {
                    try {
                        val stats = application.apiMonitoring.getStatistics()
                        
                        val summary = mapOf(
                            "overview" to mapOf(
                                "totalRequests" to stats.totalRequests,
                                "averageResponseTime" to "%.2f ms".format(stats.averageResponseTimeMs),
                                "errorRate" to "%.2f%%".format(stats.overallErrorRate),
                                "slowRequestRate" to "%.2f%%".format(stats.overallSlowRequestRate)
                            ),
                            "topSlowEndpoints" to stats.topSlowEndpoints.take(5).map { endpoint ->
                                mapOf(
                                    "endpoint" to "${endpoint.method} ${endpoint.path}",
                                    "averageResponseTime" to "%.2f ms".format(endpoint.averageResponseTimeMs),
                                    "totalRequests" to endpoint.totalRequests,
                                    "slowRequestRate" to "%.2f%%".format(endpoint.slowRequestRate)
                                )
                            },
                            "topErrorEndpoints" to stats.topErrorEndpoints.take(5).map { endpoint ->
                                mapOf(
                                    "endpoint" to "${endpoint.method} ${endpoint.path}",
                                    "errorRate" to "%.2f%%".format(endpoint.errorRate),
                                    "totalRequests" to endpoint.totalRequests,
                                    "errorRequests" to endpoint.errorRequests
                                )
                            },
                            "recentSlowRequests" to stats.recentRequests
                                .filter { it.isSlowRequest }
                                .take(10)
                                .map { request ->
                                    mapOf(
                                        "endpoint" to "${request.method} ${request.path}",
                                        "duration" to "${request.durationMs} ms",
                                        "timestamp" to request.startTime,
                                        "status" to request.responseStatus
                                    )
                                }
                        )
                        
                        call.respond(HttpStatusCode.OK, summary)
                    } catch (e: Exception) {
                        logger.error("Error getting performance summary", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Failed to get performance summary"))
                    }
                }
            }
        }
    }
}
