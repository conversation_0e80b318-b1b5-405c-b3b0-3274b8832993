package net.kidjo.server.webserver3

import io.ktor.http.Parameters
import net.kidjo.common.models.Language
import net.kidjo.server.main.PlatformConfig
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import net.kidjo.server.shared.json.v3.user_toJSON
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.EncryptionController
import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.webserver3.coupon.CouponPartnersPage
import org.json.JSONObject
import java.io.StringWriter
import java.text.NumberFormat
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.*

class TemplateEngine(internal val config: Config,
                     internal val platformConfig: PlatformConfig,
                     internal val encryptionController: EncryptionController) {
    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)
    internal val templates = Templates(config)

    internal fun getBaseMap(headerTitle: String, pageDescription: String, language: Language): HashMap<String, Any> {
        val map = HashMap<String, Any>()
        map["g_headerTitle"] = headerTitle
        map["g_pageDescription"] = pageDescription
        map["g_languageShort"] = language.shortName
        map["g_assetRoot"] = config.websiteAssetRoot
        map["g_assetVersion"] = platformConfig.webSiteVersion
        // TODO: change config.websiteAssetRoot
        map["g_cssUrl"] = config.websiteAssetRoot + "css/main.css" + if (config.env == Config.Env.PROD) ".gz" else "?v=" + platformConfig.webSiteVersion
        map["g_brainTreeLibVersion"] = "3.32.1"
        map["g_isProd"] = config.env == Config.Env.PROD
        return map
    }

    private fun getBaseAppMap(language: Language): HashMap<String, Any> {
        val map = HashMap<String, Any>()
        map["g_headerTitle"] = "App"
        map["g_pageDescription"] = ""
        map["g_languageShort"] = language.shortName
        map["g_assetRoot"] = config.websiteAssetRoot + "webApp/"
        map["g_assetVersion"] = platformConfig.webAppVersion
        map["g_cssUrl"] = config.websiteAssetRoot + "webApp/css/main.css" + if (config.env == Config.Env.PROD) ".gz" else "?v=" + platformConfig.webAppVersion
        map["g_brainTreeLibVersion"] = "3.32.1"
        map["g_isProd"] = config.env == Config.Env.PROD
        return map
    }

    fun account_login(coupon: AccountCoupon?, language: Language, parameters: Parameters? = null): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Account page", language)
        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(User.getEmptyUser(), true)};var isCouponPage = false;"
        baseMap["g_hasCoupon"] = coupon != null
        if (coupon != null) {
            baseMap["g_couponBadge"] = coupon_badge_generic(coupon, language)
        }

        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.account_login.execute(languageTerms, baseMap, writer)
        // baseMap["g_autoAuth"] = parameters?.getString("autoAuth", "true") != "false"
        // baseMap["g_sxid"] = parameters?.getString("sxid", "") ?: ""
        // templates.mondiaVodafoneRelogin.execute(baseMap, writer)

        return writer.toString()
    }

    fun account_logout(user: User, language: Language): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Account page", language)
        var isFacebook = user.oAuthType == User.OAuthType.FACEBOOK
        var isGoogle = user.oAuthType == User.OAuthType.GOOGLE

        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(User.getEmptyUser(), true)};var isCouponPage = false;var isLogoutPage = true;var isLoggedInWithGoogle = $isGoogle;var isLoggedInWithFB = $isFacebook;"
        baseMap["g_hasCoupon"] = false
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.account_logout.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun accountForgottenPassword(language: Language): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Account page", language)
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.accountForgottenPassword.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun account_paymentFlow_select(user: User, coupon: AccountCoupon?, inAppPurchase: InAppPurchase, clientToken: String, langLocale: LanguageLocale): String {
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(langLocale.language)
        val isCoupon = coupon != null

        val baseMap = getBaseMap("Account", "Account page", langLocale.language)
        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(user, true)};var isCouponPage = false;var clientToken = '$clientToken';"
        val moneyFormatter = NumberFormat.getCurrencyInstance(langLocale.locale)
        baseMap["g_planCostString"] = languageTerms.format(languageTerms.planMonthlyPriceShort, moneyFormatter.format(inAppPurchase.price))
        baseMap["g_hasCoupon"] = isCoupon
        if (coupon != null) {
            baseMap["g_couponBadge"] = coupon_badge_generic(coupon, langLocale.language)
        }
        templates.header.execute(languageTerms, baseMap, writer)

        templates.account_purchaseFlow_select.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun account_paymentFlow_creditCard(user: User, coupon: AccountCoupon?, inAppPurchase: InAppPurchase, clientToken: String, langLocale: LanguageLocale): String {
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(langLocale.language)
        val isCoupon = coupon != null
        val baseMap = getBaseMap("Account", "Account page", langLocale.language)
        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(user, true)};var isCouponPage = false;var clientToken = '$clientToken';"
        val moneyFormatter = NumberFormat.getCurrencyInstance(langLocale.locale)
        baseMap["g_planCostString"] = languageTerms.format(languageTerms.planMonthlyPriceShort, moneyFormatter.format(inAppPurchase.price))
        baseMap["g_hasCoupon"] = isCoupon
        if (coupon != null) {
            baseMap["g_couponBadge"] = coupon_badge_generic(coupon, langLocale.language)
        }
        templates.header.execute(languageTerms, baseMap, writer)

        templates.account_purchaseFlow_creditCard.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun account_info(user: User, latestSubscription: SubscriptionRoot, mondiaOperator: String?, coupon: AccountCoupon?, langLocale: LanguageLocale): String {
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(langLocale.language)

        val baseMap = getBaseMap("Account", "Account page", langLocale.language)
        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(user, true)};var isCouponPage = false;"

        val formatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).withLocale(langLocale.locale)

        baseMap["g_email"] = user.email
        baseMap["g_planTextFull"] = languageTerms.planMonthlyTitle
        baseMap["g_subIsRenewing"] = latestSubscription.isRenewing
        baseMap["g_isMondia"] = latestSubscription.storeId == Device.StorePlatform.MONDIA_MEDIA
        if (latestSubscription.isRenewing) {
            baseMap["g_paymentIdText"] = if (latestSubscription.paymentType == SubscriptionRoot.PaymentType.PAY_PAL) latestSubscription.paymentId
            else languageTerms.format(languageTerms.accountInfoPaymentTypeCc, latestSubscription.paymentId)
            baseMap["g_paymentTypeIcon"] = if (latestSubscription.paymentType == SubscriptionRoot.PaymentType.PAY_PAL) "paypal-icon-black" else "credit-card-icon-black"
            baseMap["g_paymentBillDateText"] = languageTerms.accountInfoNextBillDate
        } else {
            baseMap["g_paymentBillDateText"] = languageTerms.accountInfoSubscriptionActiveUntil
            baseMap["g_paymentIdText"] = languageTerms.accountInfoCancelledPaymentTitle
            baseMap["g_paymentTypeIcon"] = "hidden"
        }
        baseMap["g_hasCoupon"] = coupon != null
        baseMap["g_nextBillDate"] = formatter.format(latestSubscription.nextBillDate)

        baseMap["g_mondiaOperator"] = mondiaOperator ?: ""

        if (coupon != null) {
            baseMap["g_couponCode"] = coupon.couponId
        }
        templates.header.execute(languageTerms, baseMap, writer)

        templates.account_info.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun account_cancel(user: User, subscription: SubscriptionRoot, langLocale: LanguageLocale): String {
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(langLocale.language)
        val formatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).withLocale(langLocale.locale)

        val baseMap = getBaseMap("Account", "Account page", langLocale.language)
        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(user, true)};var isCouponPage = false;"
        baseMap["g_cancelMessage1"] = languageTerms.format(languageTerms.accountCancelMessage1, languageTerms.emailSupport)
        baseMap["g_cancelMessage2"] = languageTerms.format(languageTerms.accountCancelMessage2, formatter.format(subscription.nextBillDate))

        templates.header.execute(languageTerms, baseMap, writer)
        templates.account_cancel.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun accountCancelSuccess(langLocale: LanguageLocale): String {
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(langLocale.language)

        val baseMap = getBaseMap("Account", "Account page", langLocale.language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.accountCancelSuccess.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    //============
    //COUPON
    //============
    fun coupon_redeemGeneric(user: User, type: CouponPartnersPage, language: Language): String {
        val usingLanguage: Language = when (type) {
            CouponPartnersPage.LOGICOM -> Language.FRENCH
            /*CouponPartnersPage.NORMAL*/ else -> language
        }

        val writer = StringWriter()
        val baseMap = getBaseMap("Coupon", "Coupon page", usingLanguage)
        baseMap["g_embeddedJS"] = "var user = ${jsonCreator.user_toJSON(user, true)};var isCouponPage = true;"
        baseMap["g_couponType"] = type.toString()
        val languageTerms = LanguageManager.getLanguageTerms(usingLanguage)

        when (type) {
            CouponPartnersPage.LOGICOM -> {
                baseMap["g_couponPageClass"] = "logicom"
                baseMap["g_couponGenericTitle"] = "KIDJO &<br/>LOGIKIDS 4"
                baseMap["g_couponGenericSubtitle"] = "Entrez votre code ci-dessous afin de valider vos <span class='kidjo-green'>6 MOIS GRATUITS</span> d’accès complet à Kidjo"
                baseMap["g_couponExtra"] = "<div class='coupon-warning'>Vous devez valider votre code depuis votre tablette Logikids 4</div>"
            }
            /*CouponPartnersPage.NORMAL*/ else -> {
                baseMap["g_couponPageClass"] = "normal"
                baseMap["g_couponGenericTitle"] = languageTerms.couponGenericTitle
                baseMap["g_couponGenericSubtitle"] = languageTerms.couponGenericSubtitle
                baseMap["g_couponExtra"] = ""
            }
        }
        templates.header.execute(languageTerms, baseMap, writer)
        templates.coupon_redeem_generic.execute(languageTerms, baseMap, writer)
        return writer.toString()
    }

    private fun coupon_badge_generic(accountCoupon: AccountCoupon, language: Language): String {
        val map = HashMap<String, String>()
        val languageTerms = LanguageManager.getLanguageTerms(language)
        map["g_couponMessage1"] = languageTerms.format(languageTerms.couponBadgeGenericTop, accountCoupon.couponId)
        map["g_couponMessage2"] = languageTerms.format(if (accountCoupon.freeTrialValue > 1) languageTerms.couponBadgeGenericBottomMany else languageTerms.couponBadgeGenericBottom, accountCoupon.freeTrialValue.toString())
        return templates.coupon_badge_generic.execute(map)
    }

    //============
    // Mondia
    //============

    fun mondiaVodafoneSubscribe(redirectUrl: String, parameters: Parameters? = null): String {
        val languageToUse = Language.SPANISH
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(languageToUse)
        val baseMap = getBaseMap("Kidjo TV", "Kidjo TV", languageToUse)
        baseMap["g_redirectUrl"] = redirectUrl
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaVodafoneSubscribe.execute(languageTerms, baseMap, writer)
        // baseMap["g_autoAuth"] = parameters?.getString("autoAuth", "true") != "false"
        // baseMap["g_sxid"] = parameters?.getString("sxid", "") ?: ""
        // templates.mondiaVodafoneRelogin.execute(baseMap, writer)

        return writer.toString()
    }

    fun mondiaVodafoneError(redirectUrl: String): String {
        val languageToUse = Language.SPANISH
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(languageToUse)
        val baseMap = getBaseMap("Kidjo TV", "Kidjo TV", languageToUse)
        baseMap["g_redirectUrl"] = redirectUrl
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaVodafoneError.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun mondiaVodafoneApp(dynamicLink: String, isUserSignedIn: Boolean, parameters: Parameters? = null): String {
        val languageToUse = Language.SPANISH
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(languageToUse)
        val baseMap = getBaseMap("Kidjo TV", "Kidjo TV", languageToUse)
        baseMap["g_linkUrl"] = dynamicLink
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaVodafoneApp.execute(languageTerms, baseMap, writer)
        // baseMap["g_autoAuth"] = !isUserSignedIn && parameters?.getString("autoAuth", "true") != "false"
        // baseMap["g_sxid"] = parameters?.getString("sxid", "") ?: ""
        // templates.mondiaVodafoneRelogin.execute(baseMap, writer)

        return writer.toString()
    }

    fun mondiaVodafoneWaiting(customerId: String): String {
        val languageToUse = Language.SPANISH
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(languageToUse)
        val baseMap = getBaseMap("Kidjo TV", "Kidjo TV", languageToUse)
        baseMap["g_customerId"] = customerId
        baseMap["g_accountLoadingMessage"] = "Por favor espera..."
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaVodafoneWaiting.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun mondiaSubscribe(): String {
        val languageToUse = Language.FRENCH
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(languageToUse)
        val baseMap = getBaseMap("Mondia", "Mondia", languageToUse)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaSubscribe.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun mondiaWaiting(customerId: String): String {
        val languageToUse = Language.FRENCH
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(languageToUse)
        val baseMap = getBaseMap("Mondia", "Mondia", languageToUse)
        baseMap["g_customerId"] = customerId
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaWaiting.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun mondiaRedeemCredentials(dynamicLink: String, language: Language): String {
        val writer = StringWriter()
        val languageTerms = LanguageManager.getLanguageTerms(language)
        val baseMap = getBaseMap("Mondia", "Mondia", language)
        baseMap["g_dynamicLinkUrl"] = dynamicLink
        baseMap["g_languagePath"] = if (language.id != config.defaultLanguageId) "/${language.shortName}" else ""
        templates.header.execute(languageTerms, baseMap, writer)
        templates.mondiaRedeemCredentials.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    //============
    // Deep links
    //============

    fun deepLinkRedirection(redirectionAndroid: String, redirectionIos: String, redirectionFallback: String, language: Language): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Deep link", "Deep link redirection", language)
        baseMap["g_redirectionAndroid"] = redirectionAndroid
        baseMap["g_redirectionIos"] = redirectionIos
        baseMap["g_redirectionFallback"] = redirectionFallback

        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.deepLinkRedirection.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    //============
    //Web App
    //============
    fun webApp_main(includeShakaPlayer: Boolean, language: Language, data: JSONObject): String {
        val writer = StringWriter()
        val baseMap = getBaseAppMap(language)
        baseMap["g_webAppVersion"] = platformConfig.webAppVersion
        baseMap["g_embeddedData"] = data.toString()
        baseMap["g_includeShakaPlayer"] = includeShakaPlayer
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.webApp_main.execute(languageTerms, baseMap, writer)
        return writer.toString()
    }

    fun webApp_manifest(language: Language): String {
        val writer = StringWriter()
        val baseMap = getBaseAppMap(language)
        baseMap["g_webAppVersion"] = platformConfig.webAppVersion
        baseMap["g_languageShortName"] = language.shortName
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.webApp_manifest.execute(languageTerms, baseMap, writer)
        return writer.toString()
    }

    //=========
    // Vewd TV
    //=========

    fun vewdLogin(language: Language, parameters: Parameters? = null): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Vewd TV login", language)
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.vewdLogin.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun vewdUnsubscribe(language: Language, parameters: Parameters? = null): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Vewd TV unsubscribe", language)
        val languageTerms = LanguageManager.getLanguageTerms(language)
        baseMap["g_accountVewdUnsubscribe"] = languageTerms.accountVewdUnsubscribe.ifBlank { LanguageManager.getDefaultLanguageTerms().accountVewdUnsubscribe }
        templates.header.execute(languageTerms, baseMap, writer)
        templates.vewdUnsubscribe.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    fun vewdError(language: Language, parameters: Parameters? = null): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Vewd TV error", language)
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.vewdError.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

    //======
    // Link
    //======

    fun linkPin(language: Language, linkPin: String = ""): String {
        val writer = StringWriter()
        val baseMap = getBaseMap("Account", "Link Pin", language)
        baseMap["g_linkPin"] = linkPin
        val languageTerms = LanguageManager.getLanguageTerms(language)
        templates.header.execute(languageTerms, baseMap, writer)
        templates.linkPin.execute(languageTerms, baseMap, writer)

        return writer.toString()
    }

}
