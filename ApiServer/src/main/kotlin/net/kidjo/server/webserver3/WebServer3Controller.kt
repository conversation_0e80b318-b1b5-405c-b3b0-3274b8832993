package net.kidjo.server.webserver3

import io.ktor.server.application.call
import io.ktor.server.http.content.files
import io.ktor.server.http.content.resources
import io.ktor.server.http.content.static
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import net.kidjo.common.models.Language
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import net.kidjo.server.webserver3.account.*
import net.kidjo.server.webserver3.attribution.linkAttribution_link
import net.kidjo.server.webserver3.coupon.CouponPartnersPage
import net.kidjo.server.webserver3.coupon.coupon_main
import net.kidjo.server.webserver3.coupon.coupon_redeemGeneric
import net.kidjo.server.webserver3.login.accountForgottenPassword
import net.kidjo.server.webserver3.login.accountSendNewPassword
import net.kidjo.server.webserver3.login.login_logout
import net.kidjo.server.webserver3.login.login_main
import net.kidjo.server.webserver3.mondiapay.*

const val ACCOUNT_ROUTER_INFO = "/account/info"
const val ACCOUNT_ROUTER_CANCEL = "/account/cancel"
const val ACCOUNT_ROUTER_CANCEL_SUCCESS = "/account/cancel/success"

const val ACCOUNT_ROUTER_MAIN = "/account"
const val ACCOUNT_ROUTER_SUCCESS = "/account/success"

const val ACCOUNT_ROUTER_LOGIN_ACCOUNT = "/account/login"
const val ACCOUNT_ROUTER_LOGIN_DEEPLINK = "/login"
const val ACCOUNT_ROUTER_LOGOUT = "/account/logout"
const val ACCOUNT_ROUTER_REGISTER_ACCOUNT = "/account/register"
const val ACCOUNT_ROUTER_REGISTER_DEEPLINK = "/register"
const val ACCOUNT_ROUTER_PAYMENT_SELECT = "/account/payment"
const val ACCOUNT_ROUTER_PAYMENT_CC = "/account/payment/cc"

const val ACCOUNT_EMAIL_CONFIRMATION = "/account/confirmEmail/{code}"

const val ACCOUNT_ROUTER_PAIR_NATIVE = "/account/pair/native"
const val ACCOUNT_ROUTER_PAIR_WEBAPP = "/account/pair/webapp"

const val ACCOUNT_ROUTER_FORGOTTEN_PASSWORD = "/account/password"

const val COUPON_ROUTER_MAIN = "/account/coupon"
const val COUPON_ROUTER_REDEEM = "/account/coupon/redeem"
const val COUPON_ROUTER_LOGIN = "/account/coupon/login"
const val COUPON_ROUTER_REGISTER = "/account/coupon/register"
const val COUPON_ROUTER_PAYMENT_SELECT = "/account/coupon/valid"
const val COUPON_ROUTER_PAYMENT_CC = "/account/coupon/valid/cc"

const val LOGICOM_PARTNER_COUPON_ROUTER = "/logicom"
const val KURIO="/kurio"

const val MONDIA_PAY_SUBSCRIPTION_MODAL_ROUTER = "/mondia"
const val MONDIA_PAY_OPEN_MONDIA_PAYMENT = "/mondia/pay"
const val MONDIA_PAY_REDIRECT_ROUTER = "/mondia/return"
const val MONDIA_PAY_NOTIFICATION = "/mondia/partnerNotifications/ae29ba3b98d259c7"

const val MONDIA_VODAFONE_PAY_SUBSCRIPTION_MODAL_ROUTER = "/mondiaVodafone"
const val MONDIA_VODAFONE_PAY_REDIRECT_ROUTER = "/mondiaVodafone/return"
const val MONDIA_VODAFONE_SUCCESS_ROUTER = "/mondiaVodafone/success"
const val MONDIA_VODAFONE_ERROR_ROUTER = "/mondiaVodafone/error"
const val MONDIA_VODAFONE_APP_ROUTER = "/mondiaVodafone/app"
const val MONDIA_VODAFONE_AUTH_ROUTER = "/mondiaVodafone/auth"

const val DEEP_LINK_REDIRECTION_ORANGE_ROUTER = "/appLink/source/orange"

const val LINK_ATTRIBUTION_LINK_WITH_ID = "/install/{id}"
const val LINK_ATTRIBUTION_LINK = "/install"

const val VEWD_LOGIN = "/vewd/login"
const val VEWD_UNSUBSCRIBE = "/vewd/unsubscribe"

const val LINK_PIN = "/link/pin"

class WebServer3Controller(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)
    internal val templates = TemplateEngine(config, platformConfig, encryptionController)

    override fun installRoutes(route: Route) {
        // Account
        route.get(ACCOUNT_ROUTER_PAYMENT_SELECT) { this@WebServer3Controller.account_payment(false, call) }
        route.get(ACCOUNT_ROUTER_PAYMENT_CC) { this@WebServer3Controller.account_creditCard(false, call) }

        route.get(ACCOUNT_ROUTER_MAIN) { this@WebServer3Controller.account_success(call) }
        route.get(ACCOUNT_ROUTER_SUCCESS) { this@WebServer3Controller.account_success(call) }

        route.get(ACCOUNT_ROUTER_REGISTER_DEEPLINK) { this@WebServer3Controller.login_main(false, true, call) }
        route.get(ACCOUNT_ROUTER_REGISTER_ACCOUNT) { this@WebServer3Controller.login_main(false, false, call) }
        route.get(ACCOUNT_ROUTER_LOGIN_DEEPLINK) { this@WebServer3Controller.login_main(false, true, call) }
        route.get(ACCOUNT_ROUTER_LOGIN_ACCOUNT) { this@WebServer3Controller.login_main(false, false, call) }

        route.get(ACCOUNT_ROUTER_LOGOUT) { this@WebServer3Controller.login_logout(false, call) }

        route.get(ACCOUNT_ROUTER_INFO) { this@WebServer3Controller.account_info(call) }
        route.get(ACCOUNT_ROUTER_CANCEL) { this@WebServer3Controller.account_cancel(call) }
        route.get(ACCOUNT_ROUTER_CANCEL_SUCCESS) { <EMAIL>(call) }

        route.get(ACCOUNT_EMAIL_CONFIRMATION) { this@WebServer3Controller.account_confirmEmail(call) }

        route.get(ACCOUNT_ROUTER_PAIR_NATIVE) { this@WebServer3Controller.account_pairNative(call) }
        route.get(ACCOUNT_ROUTER_PAIR_WEBAPP) { this@WebServer3Controller.account_pairWebApp(call) }

        route.get(ACCOUNT_ROUTER_FORGOTTEN_PASSWORD) { <EMAIL>(call) }
        route.post(ACCOUNT_ROUTER_FORGOTTEN_PASSWORD) { <EMAIL>(call) }

        // Coupon
        route.get(COUPON_ROUTER_MAIN) { this@WebServer3Controller.coupon_main(call) }
        route.get(COUPON_ROUTER_REDEEM) { this@WebServer3Controller.coupon_redeemGeneric(CouponPartnersPage.NORMAL, call) }
        route.get(COUPON_ROUTER_LOGIN) { this@WebServer3Controller.login_main(true, false, call) }
        route.get(COUPON_ROUTER_REGISTER) { this@WebServer3Controller.login_main(true, false, call) }
        route.get(COUPON_ROUTER_PAYMENT_SELECT) { this@WebServer3Controller.account_payment(true, call) }
        route.get(COUPON_ROUTER_PAYMENT_CC) { this@WebServer3Controller.account_creditCard(true, call) }

        // Partners
        route.get(LOGICOM_PARTNER_COUPON_ROUTER) { this@WebServer3Controller.coupon_redeemGeneric(CouponPartnersPage.LOGICOM, call) }

        route.get(KURIO) { this@WebServer3Controller.coupon_redeemGeneric(CouponPartnersPage.KURIO, call) }

        // Link attribution
        route.get(LINK_ATTRIBUTION_LINK_WITH_ID) { this@WebServer3Controller.linkAttribution_link(call) }
        route.get(LINK_ATTRIBUTION_LINK) { this@WebServer3Controller.linkAttribution_link(call) }

        // Mondia
        route.get(MONDIA_PAY_SUBSCRIPTION_MODAL_ROUTER) { <EMAIL>(call) }
        route.get(MONDIA_PAY_OPEN_MONDIA_PAYMENT) { <EMAIL>(call) }
        route.get(MONDIA_PAY_REDIRECT_ROUTER) { <EMAIL>(call) }
        route.post(MONDIA_PAY_NOTIFICATION) { <EMAIL>(call) }

        // Mondia Vodafone
        route.get(MONDIA_VODAFONE_PAY_SUBSCRIPTION_MODAL_ROUTER) { <EMAIL>(call) }
        route.get(MONDIA_VODAFONE_PAY_REDIRECT_ROUTER) { <EMAIL>(call) }
        route.get(MONDIA_VODAFONE_SUCCESS_ROUTER) { <EMAIL>(call, Language.SPANISH) }
        route.post(MONDIA_VODAFONE_SUCCESS_ROUTER) { <EMAIL>(call, Language.SPANISH) }
        route.get(MONDIA_VODAFONE_ERROR_ROUTER) { <EMAIL>(call) }
        route.get(MONDIA_VODAFONE_APP_ROUTER) { <EMAIL>(call) }
        route.get(MONDIA_VODAFONE_AUTH_ROUTER) { <EMAIL>(call) }

        // Deep link redirection
        route.get(DEEP_LINK_REDIRECTION_ORANGE_ROUTER) { <EMAIL>(call) }

        // Vewd TV login
        route.get(VEWD_LOGIN) { <EMAIL>(call) }
        route.get(VEWD_UNSUBSCRIBE) { <EMAIL>(call) }

        // Link
        route.get(LINK_PIN) { <EMAIL>(call) }
        route.post(LINK_PIN) { <EMAIL>(call) }

        // Assets for static API served pages
        route.static("assets") {
            if (config.websiteHotReload) {
                files(config.websiteHotReloadDir + "assets")
            } else {
                resources("assets")
            }
        }
    }
}
