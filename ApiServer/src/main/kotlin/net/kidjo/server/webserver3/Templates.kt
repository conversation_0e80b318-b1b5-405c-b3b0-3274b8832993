package net.kidjo.server.webserver3

import com.samskivert.mustache.Mustache
import net.kidjo.server.shared.models.HotReloadTemplate
import net.kidjo.server.shared.tools.Config

class Templates(config: Config) {
    internal val compiler = Mustache.compiler()

    val header = HotReloadTemplate("templates/base/header.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val account_login = HotReloadTemplate("templates/accounts/login.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val account_logout = HotReloadTemplate("templates/accounts/logout.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val account_purchaseFlow_select = HotReloadTemplate("templates/accounts/purchase_flow_select.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val account_purchaseFlow_creditCard = HotReloadTemplate("templates/accounts/purchase_flow_cc.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val account_info = HotReloadTemplate("templates/accounts/account_info.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val account_cancel = HotReloadTemplate("templates/accounts/cancel.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val accountForgottenPassword = HotReloadTemplate("templates/accounts/forgotten_password.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val accountCancelSuccess = HotReloadTemplate("templates/accounts/cancelSuccess.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val coupon_redeem_generic = HotReloadTemplate("templates/coupon/redeem_generic.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val coupon_badge_generic = HotReloadTemplate("templates/coupon/coupon_badge_generic.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val mondiaWaiting = HotReloadTemplate("templates/mondia/waiting.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val mondiaSubscribe = HotReloadTemplate("templates/mondia/subscribe.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val mondiaRedeemCredentials = HotReloadTemplate("templates/mondia/redeem_credentials.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val mondiaVodafoneSubscribe = HotReloadTemplate("templates/mondia/vodafone/vodafoneSubscribe.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val mondiaVodafoneError = HotReloadTemplate("templates/mondia/vodafone/vodafoneError.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val mondiaVodafoneWaiting = HotReloadTemplate("templates/mondia/vodafone/vodafoneWaiting.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val mondiaVodafoneApp = HotReloadTemplate("templates/mondia/vodafone/vodafoneApp.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val mondiaVodafoneRelogin = HotReloadTemplate("templates/mondia/vodafone/vodafoneRelogin.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val deepLinkRedirection = HotReloadTemplate("templates/deeplink/deepLinkRedirection.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val webApp_main = HotReloadTemplate("templates/app/index.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val webApp_manifest = HotReloadTemplate("templates/app/app_manifest.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val vewdLogin = HotReloadTemplate("templates/vewd/login.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val vewdUnsubscribe = HotReloadTemplate("templates/vewd/unsubscribe.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
    val vewdError = HotReloadTemplate("templates/vewd/error.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)

    val linkPin = HotReloadTemplate("templates/link/pin.mustache", config.websiteHotReloadDir, config.websiteHotReload, compiler)
}
