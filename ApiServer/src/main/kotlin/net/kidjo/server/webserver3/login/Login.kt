package net.kidjo.server.webserver3.login

import io.ktor.server.application.ApplicationCall
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.database.userUpdatePassword
import net.kidjo.server.shared.extensions.getFullQueryString
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.extensions.isEmail
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.shared.models.User
import net.kidjo.server.webserver3.*

suspend fun WebServer3Controller.login_main(isFromCoupon: <PERSON><PERSON><PERSON>, shouldDeepLinkAfterwards: <PERSON><PERSON><PERSON>, call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    val parameters = call.parameters

    val redirectionPostLogin = parameters["redirect"]

    if (user.isSignedIn) {
        val redirectUrl: String
        var usingShouldDeepLinkAfterwards = shouldDeepLinkAfterwards
        var usingShouldLinkWebAfterwards = false
        if (parameters.getString("link") == "true") usingShouldDeepLinkAfterwards = true
        if (parameters.getString("web") == "true" && usingShouldDeepLinkAfterwards) usingShouldLinkWebAfterwards = true

        if (user.isSubscribed) {
            // Override redirection url if setup in parameters
            redirectionPostLogin?.let { return@login_main call.respondRedirect(it, false) }

            if (usingShouldDeepLinkAfterwards) {
                if (usingShouldLinkWebAfterwards) redirectUrl = "https://app.kidjo.tv"
                else redirectUrl = ACCOUNT_ROUTER_PAIR_NATIVE
            } else redirectUrl = ACCOUNT_ROUTER_INFO
        } else {
            val query: String
            if (redirectionPostLogin != null) {
                query = "?redirect=$redirectionPostLogin"
            } else if (shouldDeepLinkAfterwards) {
                query = "?link=true" + if (usingShouldLinkWebAfterwards) "&web=true" else ""
            } else {
                query = ""
            }
            if (isFromCoupon) redirectUrl = COUPON_ROUTER_PAYMENT_SELECT + query
            else redirectUrl = ACCOUNT_ROUTER_PAYMENT_SELECT + query
        }

        call.respondRedirect(redirectUrl, false)
        return
    }

    val accountCoupon = couponManager.getFromCall(call)

    if (accountCoupon != null && !isFromCoupon) {
        call.respondRedirect(COUPON_ROUTER_LOGIN + call.getFullQueryString())
        return
    } else if (accountCoupon == null && isFromCoupon) {
        call.respondRedirect(COUPON_ROUTER_REDEEM + call.getFullQueryString())
        return
    }

    val html = templates.account_login(accountCoupon, languageCache.get(call), call.parameters)
    call.respondHTML(html)
}

suspend fun WebServer3Controller.accountForgottenPassword(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    if (user.isSignedIn) {
        call.respondRedirect(ACCOUNT_ROUTER_INFO, false)
        return
    }

    val html = templates.accountForgottenPassword(languageCache.get(call))
    call.respondHTML(html)
}

suspend fun WebServer3Controller.accountSendNewPassword(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    if (user.isSignedIn) {
        call.respondRedirect(ACCOUNT_ROUTER_INFO, false)
        return
    }

    val email = call.parameters.getString("email")
    return if (email.isBlank()) {
        call.respond(HttpStatusCode.BadRequest, "Empty email")
    } else if (!email.isEmail()) {
        call.respond(HttpStatusCode.BadRequest, "Bad email")
    } else {
        // Generate a new password
        val password = utility.randomString(8, "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray())
        // Pre-hash password for users not from Mondia, Swisscom, etc. (fake email with kidjo.net in it)
        val hashedPassword = if (!email.contains("kidjo.net")) {
            val preHashedPassword = encryptionController.sha256Hash(password + config.clientSideHashV1Salt)
            encryptionController.hashPassword(preHashedPassword)
        } else {
            encryptionController.hashPassword(password)
        }

        // Save the new password for the corresponding user and send an email
        val userFromEmail = userManager.getUserByEmail(call.parameters.getString("email", ""))
        if (userFromEmail.id != User.NO_ID && (!databaseController.userUpdatePassword(userFromEmail.getLongId(), hashedPassword)
                        || !emailManager.sendResetPasswordEmail(userFromEmail.email, password, languageCache.get(call)))) {
            call.respond(HttpStatusCode.InternalServerError, "An error happened")
        } else {
            call.respond("An email has been sent to '${userFromEmail.email}' if it exists")
        }
    }
}
