package net.kidjo.server.webserver3.mondiapay

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respond
import io.ktor.server.response.respondRedirect
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import net.kidjo.common.models.Language
import net.kidjo.server.shared.cachedatabase.setDeivceUserPairingToken
import net.kidjo.server.shared.database.subscriptionGetRecentByCustomerId
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.database.userUpdatePassword
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.DeviceUserPairing
import net.kidjo.server.shared.models.UserSession
import net.kidjo.server.shared.payments.CHARS_TO_USE_FOR_MONDIA_ACCOUNT
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.payments.LENGTH_OF_GENERATED_MONDIA_ACCOUNT_PASSWORD
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_LOGIN_ACCOUNT
import net.kidjo.server.webserver3.COUPON_ROUTER_REGISTER
import net.kidjo.server.webserver3.MONDIA_VODAFONE_ERROR_ROUTER
import net.kidjo.server.webserver3.WebServer3Controller
import okhttp3.OkHttpClient
import okhttp3.Request
import org.slf4j.LoggerFactory

// Mondia main controller

//region Utility

private val logger = LoggerFactory.getLogger("MondiaController")
private val pairingCharacters = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()

/*
 * Generate a dynamic link with a specific token used in the deep link to unlock app
 */
private fun generateSyncDynamicLink(token: String?): String {
    val link = "?link=https://account.kidjo.tv/appLink/pair/${token ?: ""}?operatorName=VF_ES" // Deep link
    val apn = "&apn=net.kidjo.app.android" // Android package name
    val afl = "" // Android fallback link
    val amv = "&amv=158" // Android minimum version code of app to handle the link
    val ibi = "&ibi=net.kidjo.app.ios" // iOS bundle id
    val ifl = "" // iOS fallback link
    val isi = "&isi=**********" // iOS store id
    val imv = "&imv=" // iOS minimum version of app to handle the link
    val ofl = "&ofl=https://app.kidjo.tv" // Other platform fallback link

    return "https://np894.app.goo.gl/$link$apn$afl$amv$ibi$ifl$isi$imv$ofl"
}

//endregion

//region Mondia routes

/*
 * Mondia page for the user to retrieve credentials by email after a successful subscription
 */
suspend fun WebServer3Controller.mondiaRedirectToPayment(call: ApplicationCall) {
    val country = call.getLocaleFromAcceptLanguage()
    val countryId = country?.country ?: "tn"

    val queryParams = call.parameters
    val duration: String? = if (queryParams.getString("duration") != "") queryParams.getString("duration") else null

    val url = when {
        config.env == Config.Env.PROD || config.env == Config.Env.STAGING -> "https://www.kidjo.tv"
        config.env == Config.Env.DEVELOP -> "http://staging.kidjo.smartnsoft.com"
        else -> ""
    }

    val redirectUrl = mondiaPayManager.getRedirectUrlString(countryId, "$url/mondia/return", duration)
    logger.info("mondiaRedirectToPayment() | redirectUrl: $redirectUrl")
    call.respondRedirect(redirectUrl, false)
}

/*
 * Mondia route to handle the notification received after an action on Mondia side
 */
suspend fun WebServer3Controller.mondiaNotification(call: ApplicationCall) {
    // Somehow determine it is coming from Mondia
    mondiaPayManager.handleMondiaNotification(call)
    call.respond("OK")
}

/*
 * Mondia page after a successful purchase
 */
suspend fun WebServer3Controller.mondiaReturnFromPayment(call: ApplicationCall) {
    val query = call.parameters
    val customerId = query.getString("customerId")
    if (customerId == "") {
        call.respond404()
        return
    }

    val html = templates.mondiaWaiting(customerId)
    call.respondHTML(html)
}

/*
 * Mondia page to begin subscription process
 */
suspend fun WebServer3Controller.mondiaSubscriptionPage(call: ApplicationCall) {
    val html = templates.mondiaSubscribe()
    call.respondHTML(html)
}

/*
 * Mondia success redirection to a dynamic link to the different applications
 */
suspend fun WebServer3Controller.mondiaVodafoneSuccessRedirection(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    if (!user.isSignedIn) {
        call.respondRedirect(COUPON_ROUTER_REGISTER, false)
    }

    val token = utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, pairingCharacters)
    val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
    cacheDatabase.setDeivceUserPairingToken(token, pairing)
    databaseController.subscription_getRecentActive(user.getLongId())

    call.respondRedirect(generateSyncDynamicLink(token))
}

/*
 * Mondia page for the user to retrieve credentials by email after a successful subscription
 */
suspend fun WebServer3Controller.mondiaRedeemCredentials(call: ApplicationCall, language: Language = languageCache.get(call)) {
    // Retrieve user
    val user = userManager.getUser(call.request.cookies)

    // Redirect user to login page if he is not logged in, should not happen: session was lost
    if (!user.isSignedIn && (config.env == Config.Env.PROD || config.env == Config.Env.STAGING)) {
        call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT, false)
    }

    // Generate pairing token to unlock content directly after deep link
    val token = utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, pairingCharacters)
    val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
    cacheDatabase.setDeivceUserPairingToken(token, pairing)
    databaseController.subscription_getRecentActive(user.getLongId())

    val html = templates.mondiaRedeemCredentials(generateSyncDynamicLink(token), language)
    call.respondHTML(html)
}

/*
 * Mondia page for the user to retrieve credentials by email after a successful subscription
 */
suspend fun WebServer3Controller.mondiaSendCredentialsEmail(call: ApplicationCall, language: Language = languageCache.get(call)) {
    // Retrieve user and email
    val user = userManager.getUser(call.request.cookies)
    val email = call.parameters["email"]

    // Send bad response if the user is not signed in
    if (!user.isSignedIn || email == null) {
        return call.respondBadUser()
    }

    // Generate a new password for the user, as we cannot retrieve the old password
    val password = utility.randomString(LENGTH_OF_GENERATED_MONDIA_ACCOUNT_PASSWORD, CHARS_TO_USE_FOR_MONDIA_ACCOUNT)
    val hashedPassword = encryptionController.hashPassword(password)

    // Save new password for the user
    databaseController.userUpdatePassword(user.getLongId(), hashedPassword)

    // Send email
    emailManager.sendCredentialsEmail(email, user, password, language)

    // Generate pairing token to unlock content directly after deep link
    val token = utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, pairingCharacters)
    val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
    cacheDatabase.setDeivceUserPairingToken(token, pairing)
    databaseController.subscription_getRecentActive(user.getLongId())

    call.respondOK()
}

//endregion

//region Vodafone specifics

suspend fun WebServer3Controller.mondiaVodafoneSubscriptionPage(call: ApplicationCall) {
    val queryParams = call.parameters
    val sxid = queryParams.getString("sxid", "")
    val utmSource = queryParams.getString("utm_source", "")
    val utmCampaign = queryParams.getString("utm_campaign", "")

    // Check if user has an already running subscription for mondia Vodafone Spain and redirect to app dynamic link if so
    // -- if the auto login is false only, because we want to try auto login first
    val autoLogin = queryParams.getString("autoAuth", "true") != "false"
    val user = userManager.getUser(call.request.cookies)
    if (!autoLogin && user.isSignedIn) {
        val subscription = databaseController.subscription_getRecentActive(user.getLongId())
        if (subscription != null) {
            val token = utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, pairingCharacters)
            val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
            cacheDatabase.setDeivceUserPairingToken(token, pairing)
            databaseController.subscription_getRecentActive(user.getLongId())

            return call.respondRedirect(generateSyncDynamicLink(token))
        }
    }

    val url = when {
        config.env == Config.Env.PROD || config.env == Config.Env.STAGING -> "https://www.kidjo.tv"
        else -> "http://0.0.0.0:8989"
    }

    val redirectUrl = mondiaPayManager.getRedirectUrlStringForVodafone(sxid, utmSource, utmCampaign, "$url/mondiaVodafone/return/?sxid=$sxid&utm_source=$utmSource&utm_campaign=$utmCampaign")
    val html = templates.mondiaVodafoneSubscribe(redirectUrl, queryParams)
    call.respondHTML(html)
}

suspend fun WebServer3Controller.mondiaVodafoneReturnFromPayment(call: ApplicationCall) {
    val query = call.parameters
    val customerId = query.getString("customerId", "")
    val status = query.getString("status", "")
    val errorCode = query.getString("errorCode", "")
    val sxid = query.getString("sxid", "")
    val utmSource = query.getString("utm_source", "")
    val utmCampaign = query.getString("utm_campaign", "")

    val baseUrl = when {
        config.env == Config.Env.PROD || config.env == Config.Env.STAGING -> "https://www.kidjo.tv"
        else -> "http://0.0.0.0:8989"
    }

    if (customerId.isNotEmpty() && status == "SUCCESS") {
        GlobalScope.launch {
            logger.debug("Sending postback request for sxid: '$sxid'")
            val client = OkHttpClient()
            val request = Request.Builder()
                    .url("https://pixel.leadzu.com/pixel.php?service=4433&hash=$sxid")
                    .get().build()
            client.newCall(request).execute()
        }

        call.respondHTML(templates.mondiaVodafoneWaiting(customerId))
    } else {
        if (errorCode == "4004" || errorCode == "4010") {
            call.respondRedirect("https://www.google.com", false)
        } else {
            call.respondRedirect("$baseUrl$MONDIA_VODAFONE_ERROR_ROUTER?sxid=$sxid&utm_source=$utmSource&utm_campaign=$utmCampaign", false)
        }
    }
}

suspend fun WebServer3Controller.mondiaVodafoneErrorPage(call: ApplicationCall) {
    val queryParams = call.parameters
    val sxid = queryParams.getString("sxid", "")
    val utmSource = queryParams.getString("utm_source", "")
    val utmCampaign = queryParams.getString("utm_campaign", "")

    val url = when {
        config.env == Config.Env.PROD || config.env == Config.Env.STAGING -> "https://www.kidjo.tv"
        else -> "http://0.0.0.0:8989"
    }

    val redirectUrl = mondiaPayManager.getRedirectUrlStringForVodafone(sxid, utmSource, utmCampaign, "$url/mondiaVodafone/return/?sxid=$sxid&utm_source=$utmSource&utm_campaign=$utmCampaign")
    val html = templates.mondiaVodafoneError(redirectUrl)
    call.respondHTML(html)
}

suspend fun WebServer3Controller.mondiaVodafoneAppLinks(call: ApplicationCall) {
    // Retrieve user
    val user = userManager.getUser(call.request.cookies)

    // Generate pairing token to unlock content directly after deep link if the user is connected
    val token = if (user.isSignedIn) utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, pairingCharacters) else null
    if (token != null) {
        val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
        cacheDatabase.setDeivceUserPairingToken(token, pairing)
        databaseController.subscription_getRecentActive(user.getLongId())
    }

    // Respond with the app links page
    val html = templates.mondiaVodafoneApp(generateSyncDynamicLink(token), user.isSignedIn, call.parameters)
    call.respondHTML(html)
}

suspend fun WebServer3Controller.mondiaVodafoneAuth(call: ApplicationCall) {
    // Parameters returned from the mondia redirection with either SUCCESS or ERROR
    val parameters = call.request.queryParameters
    val sxid = parameters.getString("sxid", "")
    val utmSource = parameters.getString("utm_source", "")
    val utmCampaign = parameters.getString("utm_campaign", "")
    // Retrieve customerId in case of success
    val customerId = if (parameters["status"] == "SUCCESS") parameters["customerId"] else null
    val operator = if (parameters["status"] == "SUCCESS") parameters["operator"] else null
    logger.info(if (customerId != null) "Retrieve customerId from Mondia VF: '$customerId'" else "Mondia VF customerId not sent, request parameters: '${parameters.entries()}'")
    // Retrieve url to redirect to at the end
    val fromUrl = parameters["from"]
    if (customerId != null && fromUrl != null) {
        // Retrieve paired userId from subscription
        val subscription = databaseController.subscriptionGetRecentByCustomerId(customerId.toLongOrNull() ?: 0)
        val userId = subscription?.userId
        logger.info(if (userId != null) "User linked with id: '$userId' and operator: '$operator'" else "Fail to retrieve proper linked user, maybe not subscribed anymore")
        if (userId != null) {
            val user = userManager.getUser(userId.toString())
            userManager.setUser(user, UserSession.LoginType.LINK, call.response.cookies)
            operator?.let { userManager.setUserMondiaOperatorCookie(it, call.response.cookies) }
            logger.info("Auto login success")
            call.respondRedirect("$fromUrl${if (sxid.isNotEmpty()) "?=$sxid&utm_source=$utmSource&utm_campaign=$utmCampaign&" else "?"}autoAuth=false")
            return
        }
    }

    logger.info("Auto login failed")
    call.respondRedirect("$fromUrl${if (sxid.isNotEmpty()) "?=$sxid&utm_source=$utmSource&utm_campaign=$utmCampaign&" else "?"}autoAuth=false")
}

//endregion
