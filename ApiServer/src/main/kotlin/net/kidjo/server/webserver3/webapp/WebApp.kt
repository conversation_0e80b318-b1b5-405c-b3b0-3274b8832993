package net.kidjo.server.webserver3.webapp

import io.ktor.server.application.ApplicationCall
import io.ktor.http.ContentType
import io.ktor.server.request.accept
import io.ktor.server.request.userAgent
import io.ktor.server.response.respondText
import net.kidjo.common.models.Folder
import net.kidjo.server.shared.database.folders_get
import net.kidjo.server.shared.database.videos_get
import net.kidjo.server.shared.database.videos_getListFromFolder
import net.kidjo.server.shared.extensions.getLong
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.shared.json.v3.cards_folderNormalToJSON
import net.kidjo.server.shared.json.v3.cards_videoToJSON
import net.kidjo.server.shared.json.v3.user_toJSON
import net.kidjo.server.shared.models.Video
import net.kidjo.server.webserver3.WebServer3Controller
import org.json.JSONObject

suspend private fun WebServer3Controller.webApp_all(json: JSONObject, call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    val language = languageCache.get(call)
    json.put("language",language.shortName)
    val userAgent = call.request.userAgent() ?: ""
    val isMobileSafari: Boolean
    val supportsHLS: Boolean
    val supportsDash: Boolean
    val supportsWebp: Boolean

    if (userAgent.contains("iphone",true) || userAgent.contains("ipad",true)) {
        isMobileSafari = true
        supportsHLS = true
        supportsDash = false
        supportsWebp = false
    } else {
        val accepts = call.request.accept() ?: ""
        isMobileSafari = false
        supportsHLS = true
        supportsDash = true
        supportsWebp = accepts.contains("image/webp")
    }

    json.put("isMobileSafari",isMobileSafari)
    json.put("supportsHLS",supportsHLS)
    json.put("supportsDash",supportsDash)
    json.put("supportsWebp",supportsWebp)
    json.put("user",jsonCreator.user_toJSON(user, true))
    val html = templates.webApp_main(!isMobileSafari,language,json)
    call.respondHTML(html)
}

suspend fun WebServer3Controller.webApp_main(call: ApplicationCall) {
    webApp_all(JSONObject(),call)
}
suspend fun WebServer3Controller.webApp_folder(call: ApplicationCall) {
    val args = call.parameters
    val folderId = args.getLong("id", Folder.NO_ID_LONG)
    val json = JSONObject()
    if (folderId != Folder.NO_ID_LONG) {
        val folder = databaseController.folders_get(folderId)
        val videos = databaseController.videos_getListFromFolder(folderId)
        if (folder != null && videos.isNotEmpty()) json.put("folder",jsonCreator.cards_folderNormalToJSON(folder,videos))
    }

    webApp_all(json,call)
}
suspend fun WebServer3Controller.webApp_video(call: ApplicationCall) {
    val args = call.parameters
    val encodedVideoId = args.getString("id", "")
    val videoId = encryptionController.decodeVideoId(encodedVideoId)
    val json = JSONObject()
    if (videoId != Video.NO_SERVER_ID) {
        val video = databaseController.videos_get(videoId)
        if (video != null) json.put("folder",jsonCreator.cards_videoToJSON(video))
    }

    webApp_all(json,call)
}


suspend fun WebServer3Controller.webApp_manifest(call: ApplicationCall) {
    val manifestJsonString = templates.webApp_manifest(languageCache.get(call))
    call.respondText(manifestJsonString, ContentType("application", "manifest+json"))
}
