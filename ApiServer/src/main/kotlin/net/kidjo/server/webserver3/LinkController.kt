package net.kidjo.server.webserver3

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.cachedatabase.linkPinSet
import net.kidjo.server.shared.extensions.getFullQueryString
import net.kidjo.server.shared.extensions.respondHTML
import kotlin.random.Random

// Link main controller

suspend fun WebServer3Controller.displayPin(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)

    if (!user.isSignedIn) {
        val redirectionParam = "redirect=$LINK_PIN"
        val query = call.getFullQueryString().let { if (it.isNotBlank()) "$it&$redirectionParam" else "?$redirectionParam" }
        call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT + query, false)
        return
    }

    val html = templates.linkPin(languageCache.get(call))
    return call.respondHTML(html)
}

suspend fun WebServer3Controller.generatePin(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)

    if (!user.isSignedIn) {
        val redirectionParam = "redirect=$LINK_PIN"
        val query = call.getFullQueryString().let { if (it.isNotBlank()) "$it&$redirectionParam" else "?$redirectionParam" }
        call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT + query, false)
        return
    }

    val linkPin = (Random.Default.nextInt(900000) + 100000).toString()
    cacheDatabase.linkPinSet(linkPin, user.id)
    val html = templates.linkPin(languageCache.get(call), linkPin)
    return call.respondHTML(html)
}
