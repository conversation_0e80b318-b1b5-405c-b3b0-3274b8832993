package net.kidjo.server.webserver3.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.database.accountCoupon_get
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.extensions.getFullQueryString
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_LOGIN_ACCOUNT
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_PAYMENT_SELECT
import net.kidjo.server.webserver3.WebServer3Controller

suspend fun WebServer3Controller.account_info(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    val subscription = databaseController.subscription_getRecentActive(user.getLongId())

    if (!user.isSignedIn) {
        call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT + call.getFullQueryString(), false)
        return
    } else if (!user.isSubscribed || subscription == null) {
        call.respondRedirect(ACCOUNT_ROUTER_PAYMENT_SELECT + call.getFullQueryString(), false)
        return
    }

    val accountCoupon: AccountCoupon?
    if (subscription.accountCouponId > 0L && subscription.stillInFreeTrial) {
        accountCoupon = databaseController.accountCoupon_get(subscription.accountCouponId)
    } else {
        accountCoupon = null
    }

    val mondiaOperator = userManager.getUserMondiaOperatorCookie(call.request.cookies)
    val html = templates.account_info(user, subscription, mondiaOperator, accountCoupon, languageCache.getLanguageLocale(call))
    call.respondHTML(html)
}
