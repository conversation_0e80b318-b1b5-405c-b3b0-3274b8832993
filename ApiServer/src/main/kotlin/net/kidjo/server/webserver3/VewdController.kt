package net.kidjo.server.webserver3

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.userAgent
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.extensions.respondHTML

// Vewd TV main controller

suspend fun WebServer3Controller.vewdLogin(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    val userAgent = call.request.userAgent() ?: ""

    if (user.isSignedIn) {
        return call.respondRedirect("https://app.kidjo.tv", false)
    }

    // Check for Vewd TV user agent
    if (isVewdTv(userAgent)) {
        val html = templates.vewdLogin(languageCache.get(call), call.parameters)
        return call.respondHTML(html)
    }

    // Display error page else
    val html = templates.vewdError(languageCache.get(call), call.parameters)
    call.respondHTML(html)
}

suspend fun WebServer3Controller.vewdUnsubscribe(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    val userAgent = call.request.userAgent() ?: ""

    if (user.isSignedIn) {
        return call.respondRedirect("https://app.kidjo.tv", false)
    }

    // Check for Vewd TV user agent
    if (isVewdTv(userAgent)) {
        val html = templates.vewdUnsubscribe(languageCache.get(call), call.parameters)
        return call.respondHTML(html)
    }

    // Display error page else
    val html = templates.vewdError(languageCache.get(call), call.parameters)
    call.respondHTML(html)
}

fun isVewdTv(userAgent: String): Boolean =
    "^.*(OMI/[^\\s]*).*\$".toRegex().matches(userAgent) && "^.*(OPR/[^\\s]*).*\$".toRegex().matches(userAgent)
