package net.kidjo.server.webserver3.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respond
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.cachedatabase.setDeivceUserPairingToken
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.DeviceUserPairing
import net.kidjo.server.webserver3.COUPON_ROUTER_REGISTER
import net.kidjo.server.webserver3.WebServer3Controller
import okhttp3.OkHttpClient
import okhttp3.Request

private val PAIRING_CHARS = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()

suspend fun WebServer3Controller.account_pairNative(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    if (!user.isSignedIn) {
        call.respondRedirect(COUPON_ROUTER_REGISTER, false)
    }

    val token = utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, PAIRING_CHARS)
    val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
    cacheDatabase.setDeivceUserPairingToken(token, pairing)
    val subscription = databaseController.subscription_getRecentActive(user.getLongId())
    val isMondia = subscription?.storeId == Device.StorePlatform.MONDIA_MEDIA && subscription.operatorName != "VODAFONE SPAIN"

    val link = "?link=https://account.kidjo.tv/appLink/pair/$token" // deeplink
    val apn = if (isMondia) "&apn=net.kidjo.app.android.tunisia" else "&apn=net.kidjo.app.android" // android package name
    val afl = if (isMondia) "&afl=https://app.kidjo.tv" else "" // android fallback link
    val amv = "&amv=158" // android minimum version code of app to handle the link
    val ibi = if (isMondia) "" else "&ibi=net.kidjo.app.ios" // ios bundle id
    val ifl = if (isMondia) "&ifl=https://app.kidjo.tv" else "" // ios fallback link
    val isi = if (isMondia) "" else "&isi=**********" // ios store id
    val imv = "&imv=" // ios minimum version of app to handle the link
    val ofl = "&ofl=https://app.kidjo.tv" // other platform fallback link

    val dynamicLink = "https://np894.app.goo.gl/$link$apn$afl$amv$ibi$ifl$isi$imv$ofl"

    call.respondRedirect(dynamicLink, false)
}

suspend fun WebServer3Controller.account_pairWebApp(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    if (!user.isSignedIn) {
        call.respondRedirect(COUPON_ROUTER_REGISTER, false)
    }

    val token = utility.randomString(DeviceUserPairing.API_2_PAIRING_TOKEN_KEY_LENGTH, PAIRING_CHARS)

    val pairing = DeviceUserPairing(token, DeviceUserPairing.OPEN_REQUEST, user.getLongId(), DeviceUserPairing.PairingStep.ACCOUNT_CREATED)
    cacheDatabase.setDeivceUserPairingToken(token, pairing)

    call.respondRedirect("https://app.kidjo.tv")
}

suspend fun WebServer3Controller.cancelSubscriptionTest(call: ApplicationCall) {
    val client = OkHttpClient()

    val request = Request.Builder().url("http://payment.mondiamediamena.com/billing-gw/subservice/unsubscribe?subid=***********&operatorId=6")
            .header("username", "@kidjo_inc")
            .header("password", "m\$1DMB159x*#^Uaw")
            .build()

    val response = client.newCall(request).execute()

    if (response.isSuccessful) {
        call.respond(response.body()!!.string())
    }

    response.close()

}
