package net.kidjo.server.webserver3

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.shared.extensions.respondHTML

suspend fun WebServer3Controller.deepLinkRedirectionOrange(call: ApplicationCall) {
    val redirectionIos = "https://np894.app.goo.gl/?link=https://account.kidjo.tv/appLink/source/orange&apn=net.kidjo.app.android&isi=**********&ibi=net.kidjo.app.ios"
    val redirectionFallback = "https://www.kidjo.tv"
    val html = templates.deepLinkRedirection(redirectionFallback, redirectionIos, redirectionFallback, languageCache.get(call))
    call.respondHTML(html)
}
