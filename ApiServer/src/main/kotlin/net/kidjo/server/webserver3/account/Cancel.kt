package net.kidjo.server.webserver3.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_INFO
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_LOGIN_ACCOUNT
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_PAYMENT_SELECT
import net.kidjo.server.webserver3.WebServer3Controller

suspend fun WebServer3Controller.account_cancel(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)

    if (!user.isSignedIn) {
        call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT, false)
        return
    } else if (!user.isSubscribed) {
        call.respondRedirect(ACCOUNT_ROUTER_PAYMENT_SELECT, false)
        return
    }

    val subscription = databaseController.subscription_getRecentActive(user.getLongId())
    if (subscription == null) {
        call.respondRedirect(ACCOUNT_ROUTER_PAYMENT_SELECT, false)
        return
    } else if (!subscription.isRenewing) {
        call.respondRedirect(ACCOUNT_ROUTER_INFO, false)
        return
    }

    val html = templates.account_cancel(user, subscription, languageCache.getLanguageLocale(call))
    call.respondHTML(html)
}

suspend fun WebServer3Controller.accountCancelSuccess(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    if (!user.isSignedIn) {
        call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT, false)
        return
    }

    val html = templates.accountCancelSuccess(languageCache.getLanguageLocale(call))
    call.respondHTML(html)
}
