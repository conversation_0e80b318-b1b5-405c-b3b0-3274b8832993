package net.kidjo.server.webserver3.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.database.subscription_getRecentActive
import net.kidjo.server.shared.extensions.getFullQueryString
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.shared.models.Device
import net.kidjo.server.webserver3.*

suspend fun WebServer3Controller.account_payment(isFromCoupon: <PERSON><PERSON><PERSON>, call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)

    val redirection = call.parameters["redirect"]

    if (!user.isSignedIn) {
        if (isFromCoupon)
            call.respondRedirect(COUPON_ROUTER_REGISTER + call.getFullQueryString(), false)
        else
            call.respondRedirect(ACCOUNT_ROUTER_LOGIN_ACCOUNT + call.getFullQueryString(), false)
        return
    }
    if (user.isSubscribed) {
        val mostRecentSub = databaseController.subscription_getRecentActive(user.getLongId())
        if (mostRecentSub != null && mostRecentSub.isRenewing) {
            call.respondRedirect(if (redirection?.isNotBlank() == true) redirection else ACCOUNT_ROUTER_INFO + call.getFullQueryString())
            return
        }
    }

    val accountCoupon = couponManager.getFromCall(call)

    if (accountCoupon != null && !isFromCoupon) {
        call.respondRedirect(COUPON_ROUTER_PAYMENT_SELECT + call.getFullQueryString())
        return
    } else if (accountCoupon == null && isFromCoupon) {
        call.respondRedirect(COUPON_ROUTER_REDEEM + call.getFullQueryString())
        return
    }

    val iap = iapManager.getDefaultIAP(Device.StorePlatform.KIDJO_BRAINTREE, true, true)
    val html = templates.account_paymentFlow_select(user, accountCoupon, iap, paymentManager.createClientToken(user),
            languageCache.getLanguageLocale(call))
    call.respondHTML(html)
}
