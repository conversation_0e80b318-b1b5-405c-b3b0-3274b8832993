package net.kidjo.server.webserver3.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import net.kidjo.server.shared.cachedatabase.email_getConfirmationCode
import net.kidjo.server.shared.cachedatabase.email_removeConfirmationCode
import net.kidjo.server.shared.database.user_emailConfirmed
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.webserver3.ACCOUNT_ROUTER_MAIN
import net.kidjo.server.webserver3.WebServer3Controller
import net.kidjo.server.webserver3.errors.errorPages_404

suspend fun WebServer3Controller.account_confirmEmail(call: ApplicationCall) {
    val urlArgs = call.parameters
    val query = call.request.queryParameters

    val code = urlArgs.getString("code")
    val confirmCode = query.getString("c")
    if (code == ""|| confirmCode == "") {
        errorPages_404(call)
        return
    }

    val pair = cacheDatabase.email_getConfirmationCode(code)
    if (pair == null) {
        errorPages_404(call)
        return
    }
    val userId = pair.first
    val checkedCode = pair.second
    if (checkedCode != confirmCode) {
        errorPages_404(call)
        return
    }

    // Success
    GlobalScope.launch { cacheDatabase.email_removeConfirmationCode(code) }

    val success = databaseController.user_emailConfirmed(userId)
    if (!success) {
        println("Issue confirming email for $userId")
    }
    call.respondRedirect(ACCOUNT_ROUTER_MAIN)
}
