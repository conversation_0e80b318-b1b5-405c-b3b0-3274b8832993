package net.kidjo.server.webserver3.login

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.webserver3.*


suspend fun WebServer3Controller.login_logout(isFromCoupon: <PERSON><PERSON><PERSON>, call: <PERSON>Call) {
    val user = userManager.getUser(call.request.cookies)
    if (!user.isSignedIn) {
        call.respondRedirect(COUPON_ROUTER_REGISTER,false)
        return
    }
    val html = templates.account_logout(user,languageCache.get(call))
    call.respondHTML(html)
}
