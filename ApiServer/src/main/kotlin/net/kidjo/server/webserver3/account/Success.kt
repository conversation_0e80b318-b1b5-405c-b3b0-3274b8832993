package net.kidjo.server.webserver3.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.extensions.getFullQueryString
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.webserver3.*

suspend fun WebServer3Controller.account_success(call: ApplicationCall) {
    val user = userManager.getUser(call.request.cookies)
    val redirectUrl: String
    val parameters = call.parameters
    val shouldDeepLinkAfterwards = parameters.getString("link") == "true"

    if (user.isSignedIn) {
        if (user.isSubscribed) {
            if (shouldDeepLinkAfterwards) {
                if (shouldDeepLinkAfterwards) redirectUrl = "https://app.kidjo.tv"
                else redirectUrl = ACCOUNT_ROUTER_PAIR_NATIVE
            }
            else
                redirectUrl = ACCOUNT_ROUTER_INFO
        } else {
            val accountCoupon = couponManager.getFromCall(call)

            if (accountCoupon != null) redirectUrl = COUPON_ROUTER_PAYMENT_SELECT + call.getFullQueryString()
            else redirectUrl = ACCOUNT_ROUTER_PAYMENT_SELECT + call.getFullQueryString()
        }
    } else {
        val accountCoupon = couponManager.getFromCall(call)

        if (accountCoupon != null)
            redirectUrl = COUPON_ROUTER_REGISTER + call.getFullQueryString()
        else
            redirectUrl = ACCOUNT_ROUTER_LOGIN_ACCOUNT + call.getFullQueryString()
    }
    call.respondRedirect(redirectUrl, false)

}
