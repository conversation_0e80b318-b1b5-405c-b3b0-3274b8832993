package net.kidjo.server.webserver3.coupon

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.extensions.getFullQueryString
import net.kidjo.server.shared.extensions.respondHTML
import net.kidjo.server.webserver3.*

enum class CouponPartnersPage {
    NORMAL, LOGICOM,KURIO
}
suspend fun WebServer3Controller.coupon_main(call: ApplicationCall) {
    val hasCoupon = couponManager.idIsInCall(call)

    val redirection = call.parameters["redirect"]

    if (!hasCoupon) {
        call.respondRedirect(COUPON_ROUTER_REDEEM + call.getFullQueryString())
        return
    }
    val user = userManager.getUser(call.request.cookies)

    if (!user.isSignedIn) {
        call.respondRedirect(COUPON_ROUTER_REGISTER + call.getFullQueryString())
        return
    } else if (user.isSubscribed) {
        call.respondRedirect(if (redirection?.isNotBlank() == true) redirection else ACCOUNT_ROUTER_INFO + call.getFullQueryString())
        return
    }

    call.respondRedirect(COUPON_ROUTER_PAYMENT_SELECT)
}
suspend fun WebServer3Controller.coupon_redeemGeneric(type: CouponPartnersPage, call: ApplicationCall) {
    val hasCoupon = couponManager.idIsInCall(call)

    val redirection = call.parameters["redirect"]

    val user = userManager.getUser(call.request.cookies)
    if (user.isSubscribed) {
        call.respondRedirect(if (redirection?.isNotBlank() == true) redirection else ACCOUNT_ROUTER_INFO + call.getFullQueryString())
        return
    }
    if (hasCoupon) {
        call.respondRedirect(COUPON_ROUTER_PAYMENT_SELECT + call.getFullQueryString())
        return
    }

    val html = templates.coupon_redeemGeneric(user,type, languageCache.get(call))
    call.respondHTML(html)
}
