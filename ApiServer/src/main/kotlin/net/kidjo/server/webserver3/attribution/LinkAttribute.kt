package net.kidjo.server.webserver3.attribution

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respondRedirect
import net.kidjo.server.shared.database.attribution_getLinkAttributionByURLlink
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.webserver3.WebServer3Controller
import net.kidjo.server.webserver3.errors.errorPages_404

suspend fun WebServer3Controller.linkAttribution_link(call: ApplicationCall) {
    val link = call.parameters.getString("id")

    if (link == "") {
        call.respondRedirect("https://np894.app.goo.gl/7wPX",false)
        return
    }

    val attributionLink = databaseController.attribution_getLinkAttributionByURLlink(link)
    if (attributionLink == null || !attributionLink.isActive) {
        call.respondRedirect("https://np894.app.goo.gl/7wPX",false)
        return
    }

    val deepLinkUrl = "https://np894.app.goo.gl/?link=https://account.kidjo.tv/appLink/install/${attributionLink.deepLinkId}&apn=net.kidjo.app.android&ibi=net.kidjo.app.ios&ipbi=net.kidjo.app.io&ius=kidjo://appLink/install/${attributionLink.deepLinkId}&dfl=https://www.kidjo.tv/"
    call.respondRedirect(deepLinkUrl,true)
}
