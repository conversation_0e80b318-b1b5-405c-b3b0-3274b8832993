package net.kidjo.server.api6

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api6.extension.partnerIntegration
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector

class Api6PartnerSubscriptionController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    override fun installRoutes(route: Route) {
        /**
         *  Partner Notification Api for creating or modifying  subscription
         */
        route.post("/notification/subscription") {
            <EMAIL>(call)
        }
    }
}
