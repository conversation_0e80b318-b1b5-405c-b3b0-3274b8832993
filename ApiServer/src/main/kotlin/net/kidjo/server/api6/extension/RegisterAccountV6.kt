package net.kidjo.server.api6.extension

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receive
import net.kidjo.server.api6.partnersController.Api6UserController
import net.kidjo.server.api6.partnersController.RegisterWebDtoIn
import net.kidjo.server.api6.partnersController.UserDtoOut
import net.kidjo.server.shared.database.accountCoupon_consumeCoupon
import net.kidjo.server.shared.database.getLatestUserSubscriptions
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.updateSubscription
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.database.user_register
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondDatabaseIssue
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.AccountCouponProduct
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdateV1
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.payments.publichers.v5.digitalvirgo.user.update.generatePassword

suspend fun Api6UserController.registerAccount(call: ApplicationCall) {

    val registerWebIn = call.receive<RegisterWebDtoIn>()

    if (registerWebIn.email.isNullOrEmpty() || registerWebIn.password.isNullOrEmpty()) {
        return call.respondBadRequest("Bad Params")
    }

    if (!validator.isEmailValid(registerWebIn.email)) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
    }

    val currentUser = databaseController.user_getByEmail(registerWebIn.email)
    if (currentUser != null) {
        return call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, "There is already such a User")
    }
    val user = User.getEmptyUser()

    user.authType = User.AuthType.EMAIL
    user.email = registerWebIn.email
    registerWebIn.displayName?.takeIf { it.isNotBlank() }?.let {
        user.name = it
    }
    val (hashedPassword)= generatePassword(registerWebIn.password,encryptionController, config.clientSideHashV1Salt)
    user.hashedPassword = hashedPassword
    user.countryId = countryCodeByGoogle.getCountryIdByIP(call)

    val couponDto = getUserCouponDto(registerWebIn.coupon)
    if (registerWebIn.coupon!!.isNotBlank()) {
        val coupon = couponManager.getFromDatabase(registerWebIn.coupon)
        if (coupon == null || !coupon.isValid()) {
            logger.error("Failed to register user")
            return call.respondBadRequest(UsersErrors.ERROR_INVALID_COUPON, "This coupon is invalid or expired.")
        } else {
            user.id = createUser(user, call)
            user.authToken = jwtManager.generateAccessToken(user.id)
            braintreeManager.createCustomer(user)
            createFreeAccessSubscription(coupon, user.getLongId(), call)

            return call.respondOK(
                UserDtoOut(
                    user.id,
                    couponDto,
                    false,
                    user.authToken
                )
            )
        }
    } else {
        user.id = createUser(user, call)
        user.authToken = jwtManager.generateAccessToken(user.id)
        braintreeManager.createCustomer(user)
        val hasSubscription = linkExternalUserSubscription(registerWebIn.externalPartner, user.getLongId())

        return call.respondOK(
            UserDtoOut(
                user.id,
                couponDto,
                hasSubscription,
                user.authToken
            )
        )
    }
}

suspend fun Api6UserController.createUser(user: User, call: ApplicationCall): String {

    logger.info("[Api5UserController.createUser] New user creation: $user")
    val id = databaseController.user_register(user).toString()
    if (id == User.NO_ID) {
        logger.error("[Api5UserController.createUser] DB Issue - User creation failed, no ID found: $user")
        call.respondDatabaseIssue()
        return id
    }

    user.id = id
    if (validator.isEmailValid(user.email)) {
        emailManager.sendConfirmationEmail(user.email, user.name, languageCache.get(call))
    }

    return id
}

suspend fun Api6UserController.createFreeAccessSubscription(
    accountCoupon: AccountCoupon,
    userId: Long,
    call: ApplicationCall,
) {
    val FREE_CAMPAING = "Free_Campaign"
    if (accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name ||
        accountCoupon.couponType == AccountCouponType.UNIQUE_ACCESS_COUPON.name
    ) {

        var subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV
        if (accountCoupon.productType == AccountCouponProduct.KIDJO_BOOKS.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_BOOKS
        }
        if (accountCoupon.productType == AccountCouponProduct.KIDJO_GAMES.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_GAMES
        }

        if (accountCoupon.productType == AccountCouponProduct.KIDJO_TV_BOOKS.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS
        }

        if (accountCoupon.productType == AccountCouponProduct.KIDJO_BOOKS_GAMES.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_BOOKS_GAMES
        }

        if (accountCoupon.productType == AccountCouponProduct.KIDJO_TV_GAMES.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV_GAMES
        }

        if (accountCoupon.productType == AccountCouponProduct.KIDJO_TV_BOOKS_GAMES.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS_GAMES
        }

        val nextBillingDate = utility.createNextBillingDateFromCoupon(accountCoupon.durationCode)
        val subscriptionRootInsert = SubscriptionRootInsert(
            userId, 0L, true,
            0F, SubscriptionRoot.PaymentType.FREE, subscriptionType,
            FREE_CAMPAING, FREE_CAMPAING,
            Device.StorePlatform.FREE_ACCESS_COUPON, FREE_CAMPAING, FREE_CAMPAING,
            FREE_CAMPAING, FREE_CAMPAING,
            FREE_CAMPAING, accountCoupon.id,
            false, nextBillingDate, !config.env.isLive
        )

        val existingSubscription = databaseController.getLatestUserSubscriptions(userId)
        if (existingSubscription != null) {
            val updateSubscription = SubscriptionRootUpdateV1(
                userId = userId,
                isRenewing = true,
                nextBillDate = nextBillingDate,
                platformPurchaseId = existingSubscription.platformPurchaseId,
                isFreeTrail = true,
                priceToLogUSD = 0.00f,
                subId = existingSubscription.id,
                paymentStateId = existingSubscription.paymentStateId,
                iapId = existingSubscription.iapId,
                accountCouponId = accountCoupon.id
            )
            databaseController.updateSubscription(updateSubscription, existingSubscription.subscriptionType);
        } else {
            val idSub = databaseController.subscriptionRoot_create(subscriptionRootInsert)
            if (idSub > 0) {
                databaseController.accountCoupon_consumeCoupon(accountCoupon.id)
            } else {
                return call.respondBadRequest("Problems creating Free Campaign Subscription")

            }
        }


    }
}


