package net.kidjo.server.api6.extension

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.response.*
import net.kidjo.exceptions.AccessDenied
import net.kidjo.exceptions.SubscriptionException
import net.kidjo.server.api6.Api6PartnerSubscriptionController
import net.kidjo.server.models.enums.PartnerSubscription
import net.kidjo.server.service.MondiaIntegrationService
import net.kidjo.server.service.SwitchIntegrationService
import net.kidjo.server.service.TwtIntegrationService
import net.kidjo.server.shared.models.Partner


suspend fun Api6PartnerSubscriptionController.partnerIntegration(call: ApplicationCall) {
   //To get partner details
    val partner = call.principal<Partner>() ?: throw AccessDenied("Unknown Partner- Invalid partner key")
    val response = when (PartnerSubscription.valueOf(partner.name)) {
        PartnerSubscription.MONDIA -> {
            val partnerService = MondiaIntegrationService(
                config, utility, encryptionController,
                emailManager, databaseController,
                languageCache
            )
            partnerService.process(call, PartnerSubscription.MONDIA)
        }

        PartnerSubscription.SWITCH -> {
            val partnerService = SwitchIntegrationService(
                config, utility, encryptionController,
                emailManager, databaseController,
                languageCache
            )
            partnerService.process(call, PartnerSubscription.SWITCH)
        }

        PartnerSubscription.TWT -> {
            val partnerService = TwtIntegrationService(
                config, utility, encryptionController,
                emailManager, databaseController,
                languageCache
            )
            partnerService.process(call, PartnerSubscription.TWT)
        }

        PartnerSubscription.DIGITAL_VIRGO -> {

        }
        else -> {
            throw SubscriptionException("Partner is not configured")
        }
    }
    call.respond(HttpStatusCode.OK, response);
}
