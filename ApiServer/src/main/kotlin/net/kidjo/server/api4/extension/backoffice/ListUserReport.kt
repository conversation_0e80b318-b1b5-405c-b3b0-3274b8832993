package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.getUsersSubscriptions
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import java.sql.SQLException

suspend fun Api4BackofficeController.listUserReport(call: ApplicationCall) {

    val cardLimit = call.parameters["limit"]?.toInt() ?: 20
    val cardOffset = call.parameters["offset"]?.toInt() ?: 0
    val storeId = call.parameters["storeId"] ?: ""
    val subscriptionType = call.parameters["subscriptionType"] ?: ""
    val withCoupon = call.parameters["withCoupon"] ?: ""
    val fromDate = call.parameters["fromDate"] ?: ""
    val toDate = call.parameters["toDate"] ?: ""
    val search = call.parameters["search"] ?: ""
    val country = call.parameters["country"] ?: ""
    val registrationDateOrder = call.parameters["registrationDateOrder"] ?: "DESC"
    val isActive = call.parameters["isActive"]?.toBoolean()
    val unsubscribed = call.parameters["unsubscribed"]?.toBoolean()
    val isFreeTrial = call.parameters["isFreeTrial"]?.toBoolean()
    val sortColumns = call.parameters["sortColumns"]?.split(",")?.toList() ?: emptyList()
    val sortColumnOrders = call.parameters["sortColumnOrders"]?.split(",")?.toList() ?: emptyList()

    return try {
        val users = databaseController.getUsersSubscriptions(
            fetchLimit = cardLimit,
            skip = cardOffset,
            storeId = storeId,
            subscriptionType = subscriptionType,
            withCoupon = withCoupon,
            search = search,
            fromDate = fromDate,
            toDate = toDate,
            country = country,
            registrationDateOrder = registrationDateOrder,
            isActive = isActive,
            unsubscribed = unsubscribed,
            isFreeTrial = isFreeTrial,
            sortColumns = sortColumns,
            sortColumnOrders = sortColumnOrders
        )
        call.respondOK(UsersSubscriptionViewDTO(users.size, users))
    } catch (e: SQLException) {
        logger.error("Failed to find users content", e)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding users")
    }
}

data class UsersViewDTO(var size: Int, var users: List<Any>)
data class UsersSubscriptionViewDTO(var size: Int, var users: List<Any>)
