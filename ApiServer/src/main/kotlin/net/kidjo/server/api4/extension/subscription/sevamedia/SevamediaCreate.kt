package net.kidjo.server.api4.extension.subscription.sevamedia

import io.ktor.server.application.*
import io.ktor.http.*
import net.kidjo.server.api4.publichers.Api4SevamediaController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*
import java.time.LocalDateTime


internal const val SEVAMEDIA_KIDJO_PAYMENT_ID = "sevamedia"
internal const val SEVAMEDIA_KIDJO_IAP_ID = "KIDJO-SEVAMEDIA"
internal const val SEVAMEDIA_NIGER_MOBILE_PROVIDER = "Zamani TELECOM"
internal const val SEVAMEDIA_BENIN_MOBILE_PROVIDER = "MTN BENIN"

suspend fun Api4SevamediaController.create(call: ApplicationCall) {

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val phone = requestJSON.optString("phone")
    var name = requestJSON.optString("name")
    val savemdiaCoupon = requestJSON.optString("coupon")

    if (name.isNullOrBlank()) {
        name = ""
    }

    if (phone.isNullOrBlank()) {
        call.respondBadRequest(
            ServerErrors.ERROR_BAD_PARAMS,
            ServerErrors.ERROR_BAD_PARAMS.name,
            "The 'phone' parameter is required"
        )
        return
    }

    if (savemdiaCoupon.isNullOrBlank()) {
        call.respondBadRequest(
            ServerErrors.ERROR_BAD_PARAMS,
            ServerErrors.ERROR_BAD_PARAMS.name,
            "The 'coupon' parameter is required"
        )
        return
    }

    var email = "${phone.replace("\\s".toRegex(), "")}@kidjo.tv"

    val newCoupon = couponManager.getFromDatabase(savemdiaCoupon)

    if (newCoupon == null || !newCoupon.isValid()) {
        logger.error("Failed to register user")
        call.respondBadRequest(
            UsersErrors.ERROR_INVALID_COUPON,
            UsersErrors.ERROR_INVALID_COUPON.name,
            "This coupon is invalid or expired."
        )
        return
    }

    val currentUser = databaseController.user_getByEmail(email)
    val countryId = countryCodeByGoogle.getCountryIdByIP(call) // 24-BENIN, 161-NIGER
    var mobileProvider = "NONE"
    if(countryId == 24){
        mobileProvider = SEVAMEDIA_BENIN_MOBILE_PROVIDER
    }
    if(countryId == 161){
        mobileProvider = SEVAMEDIA_NIGER_MOBILE_PROVIDER
    }
    if (currentUser != null) {

        val sevamediaSubscription = databaseController.subscription_getRecent(currentUser.getLongId())
        val oldCoupon = couponManager.getFromDatabase(sevamediaSubscription!!.accountCouponId)

        if (oldCoupon!!.couponId == newCoupon.couponId) {
            return call.respondBadRequest(
                UsersErrors.ERROR_INVALID_COUPON,
                UsersErrors.ERROR_INVALID_COUPON.name,
                "This coupon is invalid or expired."
            )
        }

        if (sevamediaSubscription.nextBillDate.isAfter(LocalDateTime.now())) {
            return call.respondBadRequest(
                UsersErrors.ERROR_INVALID_COUPON,
                UsersErrors.ERROR_INVALID_COUPON.name,
                "This coupon is invalid or expired."
            )
        }

        val id = createManuallySubscription(currentUser.getLongId(), newCoupon, mobileProvider, call)
        if (id > 0) {
            databaseController.accountCoupon_consumeCoupon(newCoupon.id)

            val preHashedPassword = encryptionController.sha256Hash(newCoupon.couponId + config.clientSideHashV1Salt)
            val hashedPassword = encryptionController.hashPassword(preHashedPassword)
            databaseController.updateUserPasswordAndName(currentUser.getLongId(), name, hashedPassword)

        } else {
            return call.respondBadRequest(
                SubscriptionErrors.ERROR_PROBLEM_UPDATE_SUBSCRIPTION,
                SubscriptionErrors.ERROR_PROBLEM_UPDATE_SUBSCRIPTION.name,
                "Problems update SevaMedia Subscription"
            )

        }

        val jwt =  userManager.generateAndSetUpUserAccessToken(currentUser.getLongId())
        return call.respondOK(
            SevamediaUserDTO(currentUser.email, sevamediaSubscription.id, "UPDATED", jwt)
        )

    } else {
        val insertedUser = createManuallyUser(name, email, newCoupon.couponId, countryId, call)
        if (insertedUser != null) {
            val subId = createManuallySubscription(insertedUser.getLongId(), newCoupon, mobileProvider, call)
            val jwt =  userManager.generateAndSetUpUserAccessToken(insertedUser.getLongId())
            return  call.respondCreated(SevamediaUserDTO(insertedUser.email, subId, "CREATED", jwt))
        }
    }
}

private suspend fun Api4SevamediaController.createManuallySubscription(
    userId: Long,
    coupon: AccountCoupon,
    mobileProvider: String,
    call: ApplicationCall
): Long {

    val exiprationDate = utility.createNextBillingDateFromCoupon(coupon.durationCode)
    val subscriptionRootInsert = SubscriptionRootInsert(
        userId, 0, false, 0F,
        SubscriptionRoot.PaymentType.COUPON, SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS,
        SEVAMEDIA_KIDJO_PAYMENT_ID, SEVAMEDIA_KIDJO_PAYMENT_ID, Device.StorePlatform.FREE_ACCESS_COUPON, null,
        SEVAMEDIA_KIDJO_IAP_ID, "NOT SET", coupon.couponId,
        encryptionController.sha256Hash(coupon.couponId), coupon.id, true, exiprationDate, !config.env.isLive, mobileProvider
    )

    val newSubscriptionId = databaseController.subscriptionRoot_create(subscriptionRootInsert)
    if (newSubscriptionId == SubscriptionRoot.NO_ID) {
        logger.error("subscribe() : user ($userId) subscription failed to create in the database ")
        call.respondBadRequest(
            SubscriptionErrors.ERROR_PROBLEM_CREATE_SUBSCRIPTION,
            SubscriptionErrors.ERROR_PROBLEM_CREATE_SUBSCRIPTION.name,
            "A problem occurred while creating the subscription"
        )
    }
    databaseController.accountCoupon_consumeCoupon(coupon.id)

    return newSubscriptionId
}

private suspend fun Api4SevamediaController.createManuallyUser(
    name: String,
    email: String,
    password: String,
    countryId: Int,
    call: ApplicationCall
): User {

    val preHashedPassword = encryptionController.sha256Hash(password + config.clientSideHashV1Salt)
    val hashedPassword = encryptionController.hashPassword(preHashedPassword)
    // -- Create the user and attach its id to the model object
    val insertingUser = User(User.NO_ID, email, false, name, countryId, hashedPassword, password)
    insertingUser.authType = User.AuthType.FAKE_EMAIL
    val userId = databaseController.user_register(insertingUser)

    if (userId == User.NO_SERVER_ID) {
        logger.error("createVirgo() | failed to create a fake sevamedia user in the database ")
        call.respondError(HttpStatusCode.InternalServerError, "")
    }
    insertingUser.id = userId.toString()

    return insertingUser
}

data class SevamediaUserDTO(
    val email: String,
    val subscriptionId: Long,
    val status: String,
    val token: String
)
