package net.kidjo.server.api4.publichers

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.subscription.digitalvirgo.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.deleteLinkedUserBySubToken
import net.kidjo.server.shared.database.subscription_cancelFull_virgo
import net.kidjo.server.shared.database.subscription_getByToken
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

class Api4DigitalVirgoController (platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4DigitalVirgoController")
    override fun installRoutes(route: Route) {
        route.post("/digitalvirgo-ci/subscription") { <EMAIL>("CI", call = call) }
        route.post("/digitalvirgo-sn/subscription") { <EMAIL>("SN",call =call) }
        route.post("/digitalvirgo/unsubscribe") { <EMAIL>(call) }
        route.post("/digitalvirgo/reload") { <EMAIL>(call) }
    }
fun cancelSubscription(event: String, subscriptionId: String): Int {
    if (event == VIRGO_EVENT_RESILATION || event == VIRGO_EVENT_UNSUBSCRIPTION) {
        val existingSubscription = databaseController.subscription_getByToken(subscriptionId) ?: return -1
        val subToken = existingSubscription.subscriptionToken
        val nextBillDate = LocalDateTime.now().minusDays(1)

        if(!databaseController.subscription_cancelFull_virgo(subToken, nextBillDate)) return -2
        if(!databaseController.deleteLinkedUserBySubToken(subToken)) return -3

        return 1
    }
    return 0
}
}
