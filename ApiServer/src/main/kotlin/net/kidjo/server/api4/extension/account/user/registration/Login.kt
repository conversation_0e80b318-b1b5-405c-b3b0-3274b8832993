package net.kidjo.server.api4.extension.account.user.registration

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api4.LoginDtoIn
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.api4.UserDtoOut
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*

suspend fun Api4UserController.login(setJwt: Boolean? = true, call: ApplicationCall) {

    val userLoginIn = call.receive<LoginDtoIn>()
    config.deviceIdForTwt= call.request.header("X-Kidjo-DeviceId")?:""

    if (userLoginIn.email.isNullOrEmpty() || userLoginIn.password.isNullOrEmpty()) {
        return call.respondBadRequest("Bad Params")
    }

    if (!validator.isEmailValid(userLoginIn.email)) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
    }

    val user = databaseController.user_getByEmail(userLoginIn.email)
        ?: return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "There is no such a User.")

        val error = validateAndLimitThePassword(userLoginIn.password, user)
    if (error == 1) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Wrong password.")
    }
    if (error == 2) {
        return call.respondBadRequest(
            UsersErrors.ERROR_LOGIN_LIMITED,
            "Wrong password was entered too many times. Try again in couple minutes."
        )
    }

    updateUserCountry(user, call)
    val jwt =
        if (setJwt == true){
            userManager.generateAndSetUpUserAccessToken(user.getLongId())
        }
        else jwtManager.generateAccessToken(user.id)

    return call.respondOK(
        UserDtoOut(
            user.id,
            null,
            false,
            jwt
        )
    )
}
