package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.getCouponsByGroupId
import net.kidjo.server.shared.database.getCouponsByGroupIdSize
import net.kidjo.server.shared.database.getUsersCouponsByGroupId
import net.kidjo.server.shared.database.getUsersCouponsByGroupIdSize
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.UserCouponsView

suspend fun Api4BackofficeController.listCouponDetails(call: ApplicationCall) {

    val couponLimit = call.parameters["limit"]?.takeIf { it.isNotEmpty() }?.toInt() ?: 20
    val couponOffset = call.parameters["offset"]?.takeIf { it.isNotEmpty() }?.toInt() ?: 0
    val groupId = call.parameters["groupId"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val redeemed = call.parameters["redeemed"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val search = call.parameters["search"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val groupIdOrder = call.parameters["groupIdOrder"] ?: "DESC"
    val coupons: Collection<UserCouponsView>
    val filteredCoupons: Collection<UserCouponsView>
    val size: Int
    try {
        size = databaseController.getUsersCouponsByGroupIdSize(groupId, redeemed, search) +
                databaseController.getCouponsByGroupIdSize(groupId, redeemed, search)
        coupons = databaseController.getUsersCouponsByGroupId(
            couponLimit, couponOffset, groupId,
            redeemed, search, groupIdOrder
        ) union databaseController.getCouponsByGroupId(
            couponLimit, couponOffset, groupId,
            redeemed, search, groupIdOrder
        )
        filteredCoupons = coupons.filter {
            it.userId != null &&
                    (it.couponType == AccountCouponType.UNIQUE_COUPON.raw
                            || it.couponType == AccountCouponType.UNIQUE_ACCESS_COUPON.raw)
        }.ifEmpty { coupons.distinctBy { it.couponNumber } }
        call.respondOK(CouponDetailDTO(size, filteredCoupons))
    } catch (e: Throwable) {
        logger.error(e.message, e)
        call.respondError(HttpStatusCode.InternalServerError, "Internal error when getting coupon details")
    }
}

data class CouponDetailDTO(var size: Int, var couponDetails: List<UserCouponsView>)
