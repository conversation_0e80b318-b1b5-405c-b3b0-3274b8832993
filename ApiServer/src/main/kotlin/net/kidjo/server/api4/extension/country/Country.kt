package net.kidjo.server.api4.extension.country

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4CountryController
import net.kidjo.server.shared.extensions.respondJSON
import net.kidjo.server.shared.extensions.respondOK
import org.json.JSONObject

suspend fun Api4CountryController.getCountryByIp(call: ApplicationCall) {
    val responseBody = countryCodeByGoogle.getGoogleCountryResponse(call)
    if (responseBody.isNotBlank()) {
        return call.respondJSON(JSONObject(responseBody))
    }
    logger.info("Response from google for country code api -{}", responseBody)
    return call.respondOK(mapOf("geoplugin_countryCode" to "fr"))
}
