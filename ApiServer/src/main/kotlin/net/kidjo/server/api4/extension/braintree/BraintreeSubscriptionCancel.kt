package net.kidjo.server.api4.extension.braintree

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BraintreeController
import net.kidjo.server.api4.extension.subscription.subscriptionsCancel

suspend fun Api4BraintreeController.braintreeSubscriptionsCancel(call: ApplicationCall) {
    return subscriptionsCancel(call,databaseController,braintreeManager,digitalvirgoApiManager,config)

}
