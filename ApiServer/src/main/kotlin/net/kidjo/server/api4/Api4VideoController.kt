package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.videos.getVideos
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector

class Api4VideoController(platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    override fun installRoutes(route: Route) {
        route.get("/videos"){ getVideos(call) }
    }
}

