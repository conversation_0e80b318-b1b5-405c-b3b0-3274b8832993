package net.kidjo.server.api4.extension.subscription.digitalvirgo
import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.request.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import net.kidjo.server.api4.publichers.Api4DigitalVirgoController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.payments.publichers.v5.enums.OrangeOperators
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import java.time.LocalDateTime
import java.util.*

internal const val VIRGO_PARAM_TYPE = "type"
internal const val VIRGO_PARAM_TYPE_VALUE = "welcome"
internal const val VIRGO_PARAM_DESTINATION = "destination"
internal const val VIRGO_PARAM_COUNTRY = "country"
internal const val VIRGO_PARAM_LANG = "lang"
internal const val VIRGO_PARAM_LANG_VALUE = "fr"
internal const val VIRGO_PARAM_OPERATION_ID = "operation_id"
internal const val VIRGO_PARAM_SERVICE_ID = "service_id"
internal const val SITE="site"
internal const val URL_CGV="url_cgv"


internal const val VIRGO_PARAM_MESSAGE = "message"
internal const val VIRGO_SUBSCRIPTION_PRICE = 1.90
internal const val VIRGO_KIDJO_PAYMENT_ID = "virgo"
internal const val VIRGO_KIDJO_IAP_ID = "KIDJO-VIRGO"
internal const val TEMPORARY_VIRGO_EMAIL_SUFFIX = "virgo"
internal const val TEMPORARY_ACCOUNT_PASSWORD_LENGTH = 8
internal const val TEMPORARY_ACCOUNT_EMAIL_PREFIX_LENGTH = 8
internal const val TEMPORARY_ACCOUNT_EMAIL_SUFFIX = "@kidjo.net"
internal val TEMPORARY_ACCOUNT_CHARACTERS_TO_USE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray()

internal const val VIRGO_EVENT_SUBSCRIPTION = "Subscription"
internal const val VIRGO_EVENT_RESILATION = "Resiliation"
internal const val VIRGO_EVENT_UNSUBSCRIPTION = "Unsubscription"
internal const val VIRGO_EVENT_MONITORING = "Monitoring"
internal const val VIRGO_EVENT_RELOAD = "Reload"
suspend fun Api4DigitalVirgoController.create(countryCode: String, call: ApplicationCall) {

    val requestFormParameters = call.receiveParameters()

    val errorCode = requestFormParameters["error_code"]
    val event = requestFormParameters["event"]
    val subscriptionToken = requestFormParameters["subscription_id"]
    var sessionId = requestFormParameters["session_id"]
    val alias = requestFormParameters["alias"]
    var msisdn = requestFormParameters["msisdn"]
    val opeId = requestFormParameters["ope_id"]
    val urlCgv="https://www.kidjo.tv/terms"
    val site="kidjo"

    if (errorCode.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'errorCode' parameter is required")
        return
    }
    if (event.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'event' parameter is required")
        return
    }
    if (
        event != VIRGO_EVENT_SUBSCRIPTION &&
        event != VIRGO_EVENT_RESILATION &&
        event != VIRGO_EVENT_UNSUBSCRIPTION &&
        event != VIRGO_EVENT_MONITORING ) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'event' type:$event is not recognized. The allowed types are: Subscription, Resiliation, Unsubscription, Monitoring. ")
        return
    }
    if (subscriptionToken.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'subscriptionId' parameter is required")
        return
    }
    if (msisdn.isNullOrBlank() && alias.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'msisdn' or 'alias' parameter are required")
        return
    }
    if (opeId.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'opeId' parameter is required")
        return
    }

    if (sessionId.isNullOrBlank()) {
        sessionId = "no session id"
    }

    if (errorCode != "0") {
        return call.respondBadRequest("errorCode : $errorCode - there is a problem calling Virgo IPN API")
    }
    val billingKey = getUniqueUserBillingKey(alias, msisdn)

    if (billingKey.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'alias' or 'msisdn' are invalid!")
        return
    }
    val operator = OrangeOperators.getByCountryShort(countryCode)
    val responseMessageBuilder = StringBuilder()

    if (event == VIRGO_EVENT_SUBSCRIPTION) {
        delay(2000L)


        val virgoLinkedSubscription = databaseController.getDVPairedSubscriptionToken(subscriptionToken)

        if(virgoLinkedSubscription != null ) {
            call.respondError(
                    HttpStatusCode.InternalServerError,
                    "There is already subscription with token: $subscriptionToken ")
            return
        }

        responseMessageBuilder.appendln("There is noting to update")

        val userId: Long
        val email: String
        val pass: String

        val virgoLinkedUser = databaseController.getDVPairedUserByAlias(billingKey)
        val (password, hashedPassword) = createRandomPassword()

        if(virgoLinkedUser == null || virgoLinkedUser.virgoAlias != billingKey) {

            val insertedUser = createManuallyKidjoUser(hashedPassword, password, operator.countryId.toInt(), call)
            userId = insertedUser.getLongId()
            email = insertedUser.email
            pass = insertedUser.password

            val success = createManuallyVirgoUser(opeId, billingKey, subscriptionToken, userId)
            if (!success) {
                logger.error("virgo_user_link() | failed to linked the Alias ($billingKey) in the database for user_id: '${userId}'")
                return call.respondError(
                        HttpStatusCode.InternalServerError,
                        "A problem occurred while updating the Alias ($billingKey) "
                )
            }

        } else {

            userId = virgoLinkedUser.userId
            email = databaseController.getUserEmailById(userId)
            val passUpdated = databaseController.updateUserPasswordAndName(userId, "", hashedPassword)
            if (!passUpdated) {
                logger.error("virgo_user_updated_pass() | failed to updated the user pass wit alias: ($billingKey) in the database for user_id: '${userId}'")
                return call.respondError(
                    HttpStatusCode.InternalServerError,
                    "A problem occurred while updating the user pass with: ($billingKey) "
                )
            }
            pass = password

            val success = createManuallyVirgoUser(opeId.toString(),billingKey, subscriptionToken, userId)

            if (!success) {
                logger.error("virgo_user_link() | failed to linked the Alias ($billingKey) in the database for user_id: '${userId}'")
                return call.respondError(
                        HttpStatusCode.InternalServerError,
                        "A problem occurred while updating the Alias ($billingKey) "
                )
            }
        }

        // Check existing ALIAS registration for the user
        val userAlias: String? = databaseController.getVirgoAliasByUserId(userId)
        if (userAlias == null) {
            logger.error("subscribe() | User id '${userId}' is not registered")
            return call.respondError(HttpStatusCode.BadRequest, "User has not been registered")
        }
        // Check if there is an existing subscription for the  user
        val existingSubscription = databaseController.subscription_getByToken(subscriptionToken)
        if (existingSubscription == null || !existingSubscription.isActive) {
            createManuallyKidjoSubscription(
                    userId,
                    userAlias,
                    subscriptionToken,
                    operator.name,
                    call
            )
            responseMessageBuilder.setLength(0)
            responseMessageBuilder.appendln("Created Kidjo-TV Subscription")
        }

        // Check if there is already sent email
        val isSendEmail = databaseController.getSenditoEmailStatusBySubToken(subscriptionToken)
        if (isSendEmail != 1) {
            return sendSMSVirgoUser(
                    paramDestinationValue = billingKey,
                    paramOperationIdValue = subscriptionToken,
                    paramServiceIdValue = opeId,
                    paramCountryCodeValue = countryCode,
                    uniqueUserAlias = billingKey,
                    userId = userId,
                    userPass = pass,
                    userEmail = email,
                    urlCgv= urlCgv,
                    site= site,
                    databaseController = databaseController,
                    call = call)
        } else {
            call.respondError(HttpStatusCode.BadRequest, "The SMS is already send or Country Code is not valid")
            return
        }

        call.respondOK(Success.SUCCESS, responseMessageBuilder.toString())
        return
    }

    if (event == VIRGO_EVENT_UNSUBSCRIPTION || event == VIRGO_EVENT_RESILATION) {
        val result = cancelSubscription(event, subscriptionToken)

        if(result == -2){
            call.respondError(HttpStatusCode.InternalServerError, "A problem occurred while unsubscribe Kidho-Tv subscription ID: $subscriptionToken")
            return
        }
        if(result == -3){
            call.respondError(HttpStatusCode.InternalServerError, "A problem occurred while delete linked user: $subscriptionToken")
            return
        }
        if(result == 1){
            call.respondOK(Success.SUCCESS, "Successfully unsubscribe kidjo-tv subscription: $subscriptionToken ")
            return
        }
    }

    if (event == VIRGO_EVENT_MONITORING) {
        call.respondOK(Success.SUCCESS, "The Kidjo Server is working. Create $countryCode event")
        return
    }
}

private fun Api4DigitalVirgoController.createManuallyVirgoUser(
    opeId: String,
    billingKey: String,
    subscriptionToken: String,
    userId: Long
) = databaseController.createDVPairedUser(
    operationId = opeId,
    virgoAlias = billingKey,
    subscriptionToken = subscriptionToken,
    userId = userId
)

private suspend fun Api4DigitalVirgoController.sendSMSVirgoUser(
    paramDestinationValue: String,
    paramOperationIdValue: String,
    paramServiceIdValue: String,
    paramCountryCodeValue: String,
    uniqueUserAlias: String,
    userId: Long,
    userPass: String?,
    userEmail: String,
    urlCgv: String,
    site: String,
    databaseController: DatabaseController,
    call: ApplicationCall
) {
    var destinationValue=""
    if (paramDestinationValue != null && !paramDestinationValue.startsWith("+")) {
        // Add "+" to the beginning
        destinationValue = "+$paramDestinationValue"
    }

    val virgoSesameLogin= config.virgo_sesame_login
    val virgoSesamePasswordHashed = config.virgo_sesame_password_hashed
    val virgoSenditoMessageProdApiUrl= config.virgo_sendito_message_prod_api_url

    val okHttpClient = OkHttpClient()

    val message = createVirgoMessage(userEmail, userPass, paramCountryCodeValue, userId, databaseController, call)

    val formBody = FormBody.Builder()
            .addEncoded(VIRGO_PARAM_MESSAGE, message)
            .addEncoded(VIRGO_PARAM_DESTINATION, destinationValue)
            .addEncoded(VIRGO_PARAM_OPERATION_ID, paramOperationIdValue)
            .addEncoded(VIRGO_PARAM_SERVICE_ID, paramServiceIdValue)
            .addEncoded(VIRGO_PARAM_COUNTRY, paramCountryCodeValue)
            .addEncoded(VIRGO_PARAM_LANG, VIRGO_PARAM_LANG_VALUE)
            .addEncoded(VIRGO_PARAM_TYPE, VIRGO_PARAM_TYPE_VALUE)
            .addEncoded(SITE,site)
            .addEncoded(URL_CGV,urlCgv)
            .build()

    val authKey = Base64.getEncoder().encodeToString("${virgoSesameLogin}:${virgoSesamePasswordHashed}".toByteArray())

    val request = Request.Builder()
            .url(virgoSenditoMessageProdApiUrl)
            .addHeader( "Authorization", "Basic $authKey")
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .post(formBody)
            .build()

    val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
    val stringResponse = withContext(Dispatchers.IO) { response.body()?.string() }


    return if (response.code() == 202) {
        databaseController.updateSenditoEmailStatus(uniqueUserAlias)
        logger.info("subscribe() | user ($userId) has successfully subscribed to Kidjo")
        response.close()
        call.respondOK(Success.SUCCESS, stringResponse!!)
    } else {
        response.close()
        call.respondBadRequest(stringResponse!!)
    }

}

private suspend fun Api4DigitalVirgoController.createVirgoMessage(
        userEmail: String, userPass: String?, countryCode: String,
        userId: Long, databaseController: DatabaseController,
        call: ApplicationCall
): String {
    val url = getUrlRedirectKey(userId, databaseController, call)

    val virgoMessageBuilder: StringBuilder = StringBuilder()

    if (countryCode == "SN") {
        virgoMessageBuilder.append("« Votre abonnement journalier à 100F au service Kidjo est validé! \n")
        virgoMessageBuilder.append("Identifiant: $userEmail , \n")
        if (userPass != null) virgoMessageBuilder.append("mdp: $userPass \n")
        virgoMessageBuilder.append("RDV ici: $url . \n")
    }
    if (countryCode == "CI") {
        virgoMessageBuilder.append("« Votre abonnement journalier à 150F au service Kidjo est validé! \n")
        virgoMessageBuilder.append("Identifiant: $userEmail , \n")
        if (userPass != null) virgoMessageBuilder.append("mdp: $userPass \n")
        virgoMessageBuilder.append("RDV ici: $url. \n")
    }

    return virgoMessageBuilder.toString()
}

private fun getUniqueUserBillingKey(alias: String?, msisdn: String?
): String? {
    if (!msisdn.isNullOrBlank()) {
        return msisdn
    }
    if (!alias.isNullOrBlank()) {
        return alias
    }
    return null
}

private suspend fun Api4DigitalVirgoController.getUrlRedirectKey(
        userId: Long, databaseController: DatabaseController,
        call: ApplicationCall
): String {
    val url = config.dv_login_redirect_url
    val generatedKey = utility.randomString(4, TEMPORARY_ACCOUNT_CHARACTERS_TO_USE)
    try {
        databaseController.insert_user_redirect_key(userId, generatedKey)
    } catch (e: Throwable) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems creating redirect key")
    }
    return url + generatedKey
}

private suspend fun Api4DigitalVirgoController.createManuallyKidjoSubscription(
        userId: Long,
        userAlias: String,
        subscriptionId: String,
        operatorName: String,
        call: ApplicationCall
){
    // 5 years expiration date, the user is premium until he unsubscribes himself
    val exiprationDate = LocalDateTime.now().plusYears(5)
    val subscriptionRootInsert = SubscriptionRootInsert(
        userId,
        0,
        false,
        VIRGO_SUBSCRIPTION_PRICE.toFloat(),
        SubscriptionRoot.PaymentType.NATIVE,
        SubscriptionRoot.SubscriptionType.KIDJO_TV,
        VIRGO_KIDJO_PAYMENT_ID,
        userAlias,
        Device.StorePlatform.VIRGO,
        null,
        VIRGO_KIDJO_IAP_ID,
        "NOT SET",
        subscriptionId,
        encryptionController.sha256Hash(subscriptionId),
        0L,
        true,
        exiprationDate,
        !config.env.isLive,
        operatorName
    )

    val newSubscriptionId = databaseController.subscriptionRoot_create(subscriptionRootInsert)
    if (newSubscriptionId == SubscriptionRoot.NO_ID) {
        logger.error("subscribe() : user ($userId) subscription failed to create in the database ")
        call.respondError(HttpStatusCode.InternalServerError, "A problem occurred while creating the subscription")
    }
}

private suspend fun Api4DigitalVirgoController.createManuallyKidjoUser(hashedPassword: String, password: String, countryId: Int, call: ApplicationCall): User {

    var temporaryEmail = ""
    while (temporaryEmail == "") {
        temporaryEmail = utility.randomString(
                TEMPORARY_ACCOUNT_EMAIL_PREFIX_LENGTH,
                TEMPORARY_ACCOUNT_CHARACTERS_TO_USE
        ) + TEMPORARY_ACCOUNT_EMAIL_SUFFIX
        val user = databaseController.user_getByEmail(temporaryEmail)
        if (user != null) temporaryEmail = ""
    }
    // -- Create the user and attach its id to the model object
    val insertingUser = User(
        User.NO_ID,
        temporaryEmail,
        false,
        "",
        countryId,
        hashedPassword,
        password
    )

    insertingUser.authType = User.AuthType.FAKE_EMAIL
    val userId = databaseController.user_register(insertingUser)

    if (userId == User.NO_SERVER_ID) {
        logger.error("createVirgo() | failed to create a fake $TEMPORARY_VIRGO_EMAIL_SUFFIX user in the database ")
        call.respondError(HttpStatusCode.InternalServerError, "")
    }
    insertingUser.id = userId.toString()
    return insertingUser
}

private fun Api4DigitalVirgoController.createRandomPassword(): Pair<String, String> {
    val password = utility.randomString(TEMPORARY_ACCOUNT_PASSWORD_LENGTH, TEMPORARY_ACCOUNT_CHARACTERS_TO_USE)
    val preHashedPassword = encryptionController.sha256Hash(password + config.clientSideHashV1Salt)
    val hashedPassword = encryptionController.hashPassword(preHashedPassword)
    return Pair(password, hashedPassword)
}
