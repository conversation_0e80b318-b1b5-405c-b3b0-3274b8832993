package net.kidjo.server.api4.publichers

import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import net.kidjo.common.models.Language
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.getVideoFromFolder
import net.kidjo.server.shared.database.licensesGetList
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter


class Api4VewdController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    data class PromotedVideo(
        val id: String,
        val title: String,
        val description: String,
        val media_type: String = "clip",
        val genres: String,
        val images: List<PromotedFolderImage>,
        val deep_links: String,
        val aired_date: String,
        val publish_date: String,
        val duration: Int,
        val metadata_language: String,
        val country_availability: List<String>
    )

    data class PromotedFolderImage(val url: String,
                                   val format: String,
                                   val width: Int,
                                   val height: Int)

    override fun installRoutes(route: Route) {
        // Miscellaneous routes to return data
        route.get("/vewd/promoted") { <EMAIL>(call) }
    }

    /**
     * Retrieve the Kidjo promoted content for Vewd to display in the proper section of their services
     */
    private suspend fun getPromotedContent(call: ApplicationCall) {
        val languageParam = call.parameters["language"]
        val language = languageParam?.let { Language.fromShortName(it) } ?: Language.ENGLISH

        val promotedContent = databaseController.licensesGetList(null, language.id,
                null, null, null, getCountry = true, getLanguage = true)
        var list = mutableListOf<PromotedVideo>()
        promotedContent.forEach {
            val images = mutableListOf<PromotedFolderImage>()
            val video = databaseController.getVideoFromFolder(it.folderId)
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/350_525/${it.folderId}.png", "2:3", 350, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/525_525/${it.folderId}.png", "1:1", 525, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/934_525/${it.folderId}.png", "16:9", 934, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/700_525/${it.folderId}.png", "4:3", 700, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/525_350/${it.folderId}.png", "3:2", 525, 350))

            if (video != null) {
                list.add(
                    PromotedVideo(video.userId,
                        video.title,
                        it.description ?: "",
                        genres = it.genre?.capitalize() ?: "",
                        images = images,
                        deep_links = "${config.tvAppSite}/player/${video.userId}?folderId=${it.folderId}?vewd=true",
                        aired_date = formatDate(video.creationDate!!),
                        publish_date = formatDate(video.creationDate!!),
                        duration = video.duration,
                        metadata_language = it.language ?: "",
                        country_availability = it.countries)
                )
            }
        }
        call.respond(list)
    }

    fun formatDate(date: LocalDate): String {
        val dtf: DateTimeFormatter = DateTimeFormatter.ofPattern("uuuu-MM-dd'T'HH:mm:ssX")
        // return date.atStartOfDay().atOffset(ZoneOffset.UTC).toString();
        return date.atStartOfDay().atOffset(ZoneOffset.UTC).format(dtf)
    }
}
