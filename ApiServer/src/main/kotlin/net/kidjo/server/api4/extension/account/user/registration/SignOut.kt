package net.kidjo.server.onboarding.controller.extension.account.user

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.*
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User

suspend fun Api4AccountController.userSignOut(call: ApplicationCall) {

    val currentUser = call.principal<User>()
    if (currentUser == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"Problems finding User")
    }

    val success = userManager.signOutUser(currentUser.getLongId(), call)

    if(!success) {
        return call.respondBadRequest("Problem deleting user token ! ")
    }

    return call.respondOK("Successfully deleted user token! ")
}
