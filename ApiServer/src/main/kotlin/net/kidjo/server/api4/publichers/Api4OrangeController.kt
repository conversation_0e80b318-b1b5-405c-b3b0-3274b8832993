package net.kidjo.server.api4.publichers

import com.fasterxml.jackson.databind.ObjectMapper
import createOrangeUserByTokenRequest
import getOrangeAccessToken
import getUserAuthentication
import io.ktor.server.application.*
import io.ktor.server.routing.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.slf4j.LoggerFactory

class Api4OrangeController (platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4OrangeController")
    internal val okHttpClient = OkHttpClient()
    internal val jsonMediaType = MediaType.parse("application/json; charset=utf-8")

    override fun installRoutes(route: Route) {
        route.get("/authentication/orange-user") { <EMAIL>(call) }
        route.post("/orange-user") { <EMAIL>(call) }
    }

    internal fun getBaseRequestBuilder(url: String, bearerToken: String?): Request.Builder =
        Request.Builder().url(url)
            .header("Authorization", "Bearer $bearerToken")
            .header("Accept", "application/json")
            .header("Content-Type", "application/json")

    suspend fun getOrangeUserInfo(response: Response): OrangeUserInfo? {
        val authResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
        return authResponseString?.let {
            ObjectMapper()
                .readValue(it,OrangeUserInfo::class.java)
        }
    }

    suspend fun getUserProfileResponse(
        tokenRequest: String?
    ): Response {
        val accessToken = getOrangeAccessToken()
        val userInfoUrl = String.format(
            config.orange_user_info_url, tokenRequest
        )
        val request = getBaseRequestBuilder(
            userInfoUrl,
            accessToken?.merchantAuthToken
        ).get().build()

        return withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
    }

     data class OrangeUserInfo(val userAuthAlias: String, val data: OrangeUserData)
     data class OrangeUserData(val operator: String, val subscriptions: List<OrangeUserSubscription>)
     data class OrangeUserSubscription(val status: String, val partnerPricingId: String, val date: String)
}
