package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.licenses.license
import net.kidjo.server.api4.extension.licenses.licenses
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api4LicensesController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4LicensesController")
    override fun installRoutes(route: Route) {
        route.get("/licenses") { <EMAIL>(call) }
        route.get("/license") { <EMAIL>(call) }
    }
}
