package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.account.user.*
import net.kidjo.server.api4.extension.account.user.registration.login
import net.kidjo.server.api4.extension.account.user.registration.registerAccount
import net.kidjo.server.api4.extension.account.user.registration.registerMobile
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.database.v5.getDiscountDetails
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.User
import org.slf4j.LoggerFactory

class Api4UserController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4UserController")
    internal val api4SubscriptionController = Api4SubscriptionController(platformInjector)

    override fun installRoutes(route: Route) {
        //Mobile registration
        route.post("/users/login") { <EMAIL>(call=call) }
        route.post("/users") { <EMAIL>(call) }

        //Account registration
        route.post("/accounts") { <EMAIL>(call) }
        route.post("/accounts/login") { <EMAIL>(false, call) }

        route.get("/users/forgotten-pass") { <EMAIL>(call) }
        route.get("/users/redirect/verify-key") { <EMAIL>(call) }
        route.post("/users/exist") { <EMAIL>(call) }

        route.get("/users/forgotten-pass-problem") { <EMAIL>(call) }
        route.get("/users/braintree-billing-date-problem") { <EMAIL>(call) }
    }

    fun linkExternalUserSubscription(externalPartner: Partner?, userId: Long?): Boolean {
        try {
            if (externalPartner == null || userId == null) return false
            var hasSubscription = true
            when (externalPartner.partnerName) {
                Device.StorePlatform.ORANGE.raw -> {
                    val subscriptions = databaseController.virgo_subscription_geAllByAlias(externalPartner.userAlias)
                    subscriptions.forEach { it ->
                        if (it.userId == User.NO_SERVER_ID) {
                            if (!databaseController.linkUserToKidjoSubscription(userId, it.id)) {
                                hasSubscription = false
                                logger.error("Error linking user to Kidjo sub")
                            }
                            if (!databaseController.linkUserToVirgoSubscription(userId, externalPartner.userAlias)){
                                logger.error("Error linking user to Virgo sub")
                            }
                        }
                    }
                }
            }
            return hasSubscription
        } catch (e: Throwable) {
            logger.error("Failed to link external user ", e)
            throw e
        }
    }

    fun getUserCouponDto(couponId: String?): CouponDTO? {
        try {
            if (couponId == null) return null
            var couponDto: CouponDTO? = null
            val coupon = couponManager.getFromDatabase(couponId)
            if (coupon != null) {
                couponDto = CouponDTO(
                    coupon.id,
                    coupon.couponId,
                    coupon.durationCode,
                    coupon.groupId,
                    coupon.couponType,
                    coupon.productType,
                    coupon.partnerType,
                    when (coupon.couponType) {
                        AccountCouponType.DISCOUNT_COUPON.name, AccountCouponType.UNIQUE_DISCOUNT_COUPON.name ->
                            getDiscountDetails(couponId)
                        else -> null
                    }

                )
            }
            return couponDto
        } catch (e: Throwable) {
            logger.error("Failed to update user country", e)
            throw e
        }
    }

    fun updateUserCountry(
        currentUser: User,
        call: ApplicationCall
    ) {
        try {
            if (currentUser.countryId == 0) {
                val countryId = countryCodeByGoogle.getCountryIdByIP(call)
                if (countryId != -1) {
                    databaseController.userUpdateCountry(currentUser.id.toLong(), countryId)
                    logger.info("Updated user with id=${currentUser.id} with their country [login session]")
                }
            }
        } catch (e: Throwable) {
            logger.error("Failed to update user country", e)
            throw e
        }
    }

    fun validateAndLimitThePassword(
        password: String,
        currentUser: User
    ): Int {
        try {
            val passwordMatch = encryptionController.checkPassword(password, currentUser.hashedPassword)
            if (!passwordMatch) {
                databaseController.incrementWrongPasswordCount(currentUser.id.toLong())
                databaseController.checkLimitLoginThreshold(currentUser.id.toLong())
                return 1
            }
            if (databaseController.isLoginLimited(currentUser.id.toLong())) {
                return 2
            } else {
                databaseController.resetWrongPasswordCountAndLimitLoginAt(currentUser.id.toLong())
            }
        } catch (e: Throwable) {
            logger.error("Failed to validate user password", e)
            throw e
        }
        return 0
    }
}

data class UserDtoOut(
    var userId: String,
    var userCoupon: CouponDTO?,
    var hasSubscription: Boolean,
    var token: String
)
data class LoginDtoIn(
    val email: String?,
    val password: String?,
    val countryCode: String?
)
data class RegisterMobileDtoIn(
    val displayName: String?,
    val email: String?,
    val password: String?,
    val countryCode: String?
)
data class RegisterWebDtoIn(
    val displayName: String?,
    val email: String?,
    val password: String?,
    val coupon: String?,
    val countryCode: String?,
    val env: String?,
    val externalPartner: Partner?
)
data class Partner(
    val partnerName: String,
    val userAlias: String
)
