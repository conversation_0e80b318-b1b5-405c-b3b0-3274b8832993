package net.kidjo.server.api4.extension.cards

import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.content.PartData
import io.ktor.http.content.forEachPart
import io.ktor.http.content.streamProvider
import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveMultipart
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import net.kidjo.server.api4.Api4CardsController
import net.kidjo.server.shared.database.getLanguagesByActiveVideos
import net.kidjo.server.shared.json.v3.set_language
import net.kidjo.server.shared.models.entity.Folders
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import org.json.JSONArray
import org.json.JSONObject
import kotlin.collections.set

suspend fun Api4CardsController.listLanguages(call: ApplicationCall) {
    val json = JSONObject()
    val languages = databaseController.getLanguagesByActiveVideos()

    val languageJSONArray = JSONArray()
    languages.forEach { languageJSONArray.put(jsonCreator.set_language(it)) }
    json.put("languages", languageJSONArray)

    call.respondText(json.toString(), ContentType.Application.Json)
}

suspend fun Api4CardsController.updateFolderDescription(call: ApplicationCall) {
    val multipartData = call.receiveMultipart()
    var folderId: Int = 0
    var description: String = ""
    val descriptionMap = mutableMapOf<String, String>()

    multipartData.forEachPart { part ->
        when (part) {
            is PartData.FormItem -> {
                when (part.name) {
                    "folderId" -> folderId = part.value.toInt()
                    "description" -> description = part.value
                }
            }

            is PartData.FileItem -> {
                val fileBytes = part.streamProvider().readBytes()
                val workbook = WorkbookFactory.create(fileBytes.inputStream())
                val sheet = workbook.getSheetAt(0) // Assuming data is in the first sheet

                // Assuming folderId is in column 0 and description is in column 2
                sheet.rowIterator().next()
                for (i in 1 until sheet.physicalNumberOfRows) {
                    val row = sheet.getRow(i)
                    val folderId = row.getCell(0).numericCellValue
                    val description = row.getCell(2).stringCellValue.trim()

                    transaction {
                        val result = Folders.update({ Folders.id eq folderId.toInt() }) {
                            it[Folders.description] = description
                        }
                        if (result > 0) {
                            logger.info("updated description on folderId : " + folderId)
                        } else {
                            logger.info("unable to update description on folderId : " + folderId)
                            descriptionMap[folderId.toString()] = "false " + folderId
                        }
                    }
                }
            }

            else -> {
                call.respond(HttpStatusCode.BadRequest, "Issue while updating file")
            }
        }
        part.dispose()
    }

    call.respond(HttpStatusCode.OK, "Descriptions updated successfully except "+descriptionMap)
}

