package net.kidjo.server.api4.extension.cards

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.common.models.Folder
import net.kidjo.server.api3.Api3CardsController
import net.kidjo.server.api4.Api4CardsController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.tools.RequestUtil
import org.json.JSONArray
import org.json.JSONObject

suspend fun Api4CardsController.searchCards(call: ApplicationCall) {

    val json = JSONObject()

    val acceptLanguageRaw = call.request.acceptLanguage() ?: "en"
    val queryParameters = call.parameters

    val folderType = Folder.ContentType.FromRaw(queryParameters.getString("contentType"))
    var search = queryParameters.getString("search", "")
    val kidAge = queryParameters.getInt("age")
    val filterFolders = queryParameters.getString("excludeFolderIds") as? String ?: ""
    val countryId: Int = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw) ?: 237
    val languageId = languageCache.getLanguageIdFromAcceptLanguageHeader(acceptLanguageRaw)
    if (search.contains(folderType.raw)) {
        search = search.replace(folderType.raw, "")
    }

    val videosJson = JSONArray()
    val videos = databaseController.videoSearch(search, languageId, countryId, kidAge, folderType, filterFolders, true)
    for (video in videos) {
        videosJson.put(Api3CardsController.createVideoJson(video, true))
    }
    json.put("videos", videosJson)

    call.respondText(json.toString(), ContentType.Application.Json)
}
