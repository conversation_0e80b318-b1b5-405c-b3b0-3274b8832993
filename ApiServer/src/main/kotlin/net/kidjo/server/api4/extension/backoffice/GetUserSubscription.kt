package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.shared.database.getAccountCouponId
import net.kidjo.server.shared.database.getUserViewById
import net.kidjo.server.shared.database.subscription_type_getList
import net.kidjo.server.shared.extensions.*

suspend fun Api4BackofficeController.getUserSubscription(call: ApplicationCall) {

    val userId = call.parameters["userId"]

    if (userId.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'userId' parameter is required")
    }

    try {
        val userSub = databaseController.getUserViewById(userId!!.toLong())

        if (userSub == null) {
            logger.error("Not found user")
            return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Not found user")
        }

        val subscriptions = databaseController.subscription_type_getList(userSub.userId)
        val subscriptionsDto = ArrayList<SubscriptionDTO>()
        var subscriptionDto: SubscriptionDTO
        var couponId = 0L
        var couponNumber = ""
        var groupId = ""
        var durationCode = ""
        var couponType = ""
        var productType = ""
        var partnerType: String? = null
        subscriptions.forEach { it ->
            val iap = iapManager.getIAP(it.storeId, it.iapId)
            val coupon = databaseController.getAccountCouponId(userSub.couponId)
            if (coupon != null) {
                couponId = coupon.id
                couponNumber = coupon.couponId
                groupId = coupon.groupId
                couponType = coupon.couponType
                productType = coupon.productType
                partnerType = coupon.partnerType
                durationCode = coupon.durationCode
            }

            subscriptionDto = SubscriptionDTO(
                it.id.toString(),
                it.createdAt.toString(),
                it.subscriptionType.raw,
                String.format("%.2f", iap?.price ?: 0.0),
                true,
                it.storeId.raw,
                it.nextBillDate.toString(),
                CouponDTO(
                    couponId,
                    couponNumber,
                    durationCode,
                    groupId,
                    couponType,
                    productType,
                    partnerType
                )
            )

            subscriptionsDto.add(subscriptionDto)
        }
        val user = UserDTO(
            userSub.name,
            userSub.email,
            userSub.registeredDate,
            userSub.country,
            subscriptionsDto
        )

        call.respondOK(UserSubscriptionDTO(user))
    } catch (e: Throwable) {
        logger.error("Failed to find user content", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding user")
    }
}

data class UserSubscriptionDTO(var user: UserDTO)
data class UserDTO(
    var firstName: String,
    var email: String,
    var creationDate: String,
    var country: String,
    var subscriptions: List<SubscriptionDTO?>
)

data class SubscriptionDTO(
    val subId: String,
    val subCreatedDate: String,
    val plan: String,
    val price: String,
    val status: Boolean,
    val storeId: String,
    val nextBillDate: String,
    val userCoupon: CouponDTO?
)
