package net.kidjo.server.api4.extension.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.api4.extension.subscription.SubscriptionDTO
import net.kidjo.server.shared.database.accountCoupon_get
import net.kidjo.server.shared.database.subscription_type_getList
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User


suspend fun Api4AccountController.userSubscriptionsInfo(call: ApplicationCall) {

    val user = call.principal<User>()

    try {
        var subscriptions = listOf<SubscriptionRoot>()
        if (user != null) {
            subscriptions = databaseController.subscription_type_getList(user.getLongId())
        }

        val subscriptionsDto = ArrayList<SubscriptionDTO>()

        subscriptions.forEach { it ->
            var coupon: CouponDTO? = null
            val iap = iapManager.getIAP(it.storeId, it.iapId)
            val storeId = it.storeId.raw

            if (it.accountCouponId > 0L && it.stillInFreeTrial) {
                val accCoupon = databaseController.accountCoupon_get(it.accountCouponId)
                coupon = CouponDTO(
                    accCoupon?.id,
                    accCoupon?.couponId,
                    accCoupon?.durationCode,
                    accCoupon?.groupId,
                    accCoupon?.couponType,
                    accCoupon?.productType,
                    accCoupon?.partnerType
                )
            }

            val paymentMethod: String = if ((storeId == Device.StorePlatform.KIDJO_BRAINTREE.raw
                        || storeId == Device.StorePlatform.FREE_ACCESS_COUPON.raw) && user != null
            ) {
                val lastTransaction = braintreeManager.getLastTransactionBraintreeCustomer(user.brainTreeId)
                if (lastTransaction != null) {
                    lastTransaction.creditCard?.cardType + " " + lastTransaction.creditCard?.maskedNumber
                } else {
                    "free"
                }
            } else {
                it.paymentId
            }

            subscriptionsDto.add(
                SubscriptionDTO(
                    id = it.id.toString(),
                    nextBillingDate = it.nextBillDate.toString(),
                    coupon = coupon,
                    durationCode = iap?.durationCode,
                    platformPurchaseId = it.platformPurchaseId,
                    paymentMethod = paymentMethod,
                    paymentPlanId = iap?.id,
                    paymentPlanPrice = iap?.price,
                    paymentStateId = it.paymentStateId,
                    isRenewing = it.isRenewing,
                    storeId = storeId,
                    subscriptionType = it.subscriptionType.raw,
                    isExpired = utility.isBillingDateExpired(it.nextBillDate)
                )
            )
        }
        return call.respondOK(
            UserSubscriptionInfoDTO(
                email = user?.email,
                emailIsConfirmed = user?.emailIsConfirmed,
                isEmailFake = if (user?.authType == User.AuthType.FAKE_EMAIL) true else false,
                name = user?.name,
                subscriptions = subscriptionsDto
            )
        )
    } catch (e: Throwable) {
        logger.error("Failed to find Subscription", e)
        return call.respondBadRequest("Problems finding Subscription")
    }
}

data class UserSubscriptionInfoDTO(
    var email: String?,
    var emailIsConfirmed: Boolean?,
    var isEmailFake: Boolean?,
    var name: String?,
    var subscriptions: ArrayList<SubscriptionDTO>
)
