package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.SubscriptionList
import net.kidjo.server.shared.database.getSubscriptions
import net.kidjo.server.shared.database.getSubscriptionsSize
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.listSubscriptions(call: ApplicationCall) {
    val limit = call.parameters["limit"]?.takeIf { it.isNotEmpty() }?.toInt() ?: 20
    val offset = call.parameters["offset"]?.takeIf { it.isNotEmpty() }?.toInt() ?: 0
    val fromDate = call.parameters["fromDate"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val toDate = call.parameters["toDate"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val storeId = call.parameters["storeId"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val subscriptionType = call.parameters["subscriptionType"]?.takeIf { it.isNotEmpty() }?.toString() ?: ""
    val isActive = call.parameters["isActive"]?.toBoolean()

    val sortColumns =
            call.parameters["sortColumns"]?.takeIf { it.isNotEmpty() }?.split(",")?.toList() ?: emptyList()
    val sortColumnOrders =
            call.parameters["sortColumnOrders"]?.takeIf { it.isNotEmpty() }?.split(",")?.toList() ?: emptyList()
    val createdDateOrder = call.parameters["createdDateOrder"]?.takeIf { it.isNotEmpty() }?.toString() ?: "DESC"

    try {
        val subscriptions = databaseController.getSubscriptions(limit, offset, fromDate, toDate,
                storeId, subscriptionType, isActive, sortColumns, sortColumnOrders, createdDateOrder)
        val size = databaseController.getSubscriptionsSize(fromDate, toDate, storeId, subscriptionType, isActive)
        return call.respondOK(SubscriptionList(size, subscriptions))
    } catch (e: Throwable) {
        logger.error("Failed to find partners", e)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding partners")
    }
}

