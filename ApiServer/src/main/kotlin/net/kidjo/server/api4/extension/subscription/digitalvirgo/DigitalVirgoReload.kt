package net.kidjo.server.api4.extension.subscription.digitalvirgo

import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.*
import net.kidjo.server.api4.publichers.Api4DigitalVirgoController
import net.kidjo.server.shared.extensions.*

suspend fun Api4DigitalVirgoController.reload(call: ApplicationCall) {
    val requestFormParameters = call.receiveParameters()

    val event = requestFormParameters["event"]
    val transactionId = requestFormParameters["transaction_id"]
    val subscriptionId = requestFormParameters["subscription_id"]
    var amount = requestFormParameters["amount"]
    val currency = requestFormParameters["currency"]

    if(event.isNullOrBlank()){
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"The 'event' parameter is required")
    }
    if(subscriptionId.isNullOrBlank()){
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"The 'subscriptionId' parameter is required")
    }

    if(event == VIRGO_EVENT_RELOAD){
        return call.respondOK(Success.SUCCESS, "The Reload event called")

    }

    if(event == VIRGO_EVENT_MONITORING){
        return call.respondOK(Success.SUCCESS, "The Kidjo Server is working. Reload event")
    }
}
