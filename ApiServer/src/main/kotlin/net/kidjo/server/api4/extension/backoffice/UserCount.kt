package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.getQueryUsersSubscriptions
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.getUserCount(call: ApplicationCall) {

    val storeId = call.parameters["storeId"] ?: ""
    val subscriptionType = call.parameters["subscriptionType"] ?: ""
    val withCoupon = call.parameters["withCoupon"] ?: ""
    val fromDate = call.parameters["fromDate"] ?: ""
    val toDate = call.parameters["toDate"] ?: ""
    val search = call.parameters["search"] ?: ""
    val country = call.parameters["country"] ?: ""
    val isActive = call.parameters["isActive"]?.toBoolean()
    val unsubscribed = call.parameters["unsubscribed"]?.toBoolean()
    val isFreeTrial = call.parameters["isFreeTrial"]?.toBoolean()

    return try {
        val size = databaseController.getQueryUsersSubscriptions(
            storeId = storeId,
            subscriptionType = subscriptionType,
            withCoupon = withCoupon,
            search = search,
            country = country,
            fromDate = fromDate,
            toDate = toDate,
            isActive = isActive,
            unsubscribed = unsubscribed,
            isFreeTrial = isFreeTrial
        )

        call.respondOK(mapOf("size" to size))
    } catch (e: Throwable) {
        logger.error("Failed to find users content", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding users")
    }
}
