import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.api4.publichers.Api4OrangeController
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest

/**
 * Retrieve the orange end-user information
 */
suspend fun Api4OrangeController.createOrangeUserByTokenRequest(call: ApplicationCall) {

    val tokenRequest = call.parameters["tokenRequest"]

    if (tokenRequest.isNullOrBlank()) {
        call.respondBadRequest(
            ServerErrors.ERROR_BAD_PARAMS,
            ServerErrors.ERROR_BAD_PARAMS.name,
            "The 'tokenRequest' parameter is required"
        )
        return
    }

    val response = getUserProfileResponse(tokenRequest)

    when (response.code()) {
        HttpStatusCode.OK.value -> {
            val orangeUserInfo = getOrangeUserInfo(response)

            return if (orangeUserInfo != null) {
                createOrUpdateOrangeUserSubscriptions(orangeUserInfo, call)
            } else {
                call.respond(HttpStatusCode.NotFound, "Not Fount an Orange Profile")
            }
        }

        HttpStatusCode.BadRequest.value -> {
            return call.respond(HttpStatusCode.BadRequest, HttpStatusCode.BadRequest.description)
        }
        HttpStatusCode.Unauthorized.value -> {
            return call.respond(HttpStatusCode.Unauthorized, HttpStatusCode.Unauthorized.description)
        }
        HttpStatusCode.Forbidden.value -> {
            return call.respond(HttpStatusCode.Forbidden, HttpStatusCode.Forbidden.description)
        }
        HttpStatusCode.NotFound.value -> {
            return call.respond(HttpStatusCode.NotFound, HttpStatusCode.NotFound.description)
        }
        HttpStatusCode.InternalServerError.value -> {
            return call.respond(HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError.description)
        }

        HttpStatusCode.NotAcceptable.value -> {
            return call.respond(HttpStatusCode.NotAcceptable, HttpStatusCode.NotAcceptable.description)
        }
        498 -> {
            return call.respondBadRequest("Response Token Expired")
        }
    }
}
