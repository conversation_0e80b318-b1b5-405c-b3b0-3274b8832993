package net.kidjo.server.api4.extension.subscription.helper

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4SubscriptionController
import net.kidjo.server.shared.database.accountCoupon_consumeCoupon
import net.kidjo.server.shared.database.getLatestUserSubscriptions
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.updateSubscription
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.*


suspend fun Api4SubscriptionController.createFreeAccessSubscription(
    accountCoupon: AccountCoupon,
    userId: Long,
    call: ApplicationCall,
) {
    val FREE_CAMPAING = "Free_Campaign"
    if (accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name ||
        accountCoupon.couponType == AccountCouponType.UNIQUE_ACCESS_COUPON.name
    ) {

        var subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV
        if (accountCoupon.productType == AccountCouponProduct.KIDJO_BOOKS.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_BOOKS
        }
        if(accountCoupon.productType ==AccountCouponProduct.KIDJO_GAMES.name){
            subscriptionType =SubscriptionRoot.SubscriptionType.KIDJO_GAMES
        }

        if (accountCoupon.productType == AccountCouponProduct.KIDJO_TV_BOOKS.name) {
            subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS
        }

        if(accountCoupon.productType == AccountCouponProduct.KIDJO_BOOKS_GAMES.name){
            subscriptionType =SubscriptionRoot.SubscriptionType.KIDJO_BOOKS_GAMES
        }

        if(accountCoupon.productType == AccountCouponProduct.KIDJO_TV_GAMES.name){
            subscriptionType =SubscriptionRoot.SubscriptionType.KIDJO_TV_GAMES
        }

        if(accountCoupon.productType == AccountCouponProduct.KIDJO_TV_BOOKS_GAMES.name){
            subscriptionType= SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS_GAMES
        }

        val nextBillingDate = utility.createNextBillingDateFromCoupon(accountCoupon.durationCode)
        val subscriptionRootInsert = SubscriptionRootInsert(
            userId, 0L, true,
            0F, SubscriptionRoot.PaymentType.FREE, subscriptionType,
            FREE_CAMPAING, FREE_CAMPAING,
            Device.StorePlatform.FREE_ACCESS_COUPON, FREE_CAMPAING, FREE_CAMPAING,
            FREE_CAMPAING, FREE_CAMPAING,
            FREE_CAMPAING, accountCoupon.id,
            false, nextBillingDate, !config.env.isLive
        )

        val existingSubscription = databaseController.getLatestUserSubscriptions(userId)
        if (existingSubscription != null) {
            val updateSubscription = SubscriptionRootUpdateV1(
                userId = userId,
                isRenewing = true,
                nextBillDate = nextBillingDate,
                platformPurchaseId = existingSubscription.platformPurchaseId,
                isFreeTrail = true,
                priceToLogUSD = 0.00f,
                subId = existingSubscription.id,
                paymentStateId = existingSubscription.paymentStateId,
                iapId = existingSubscription.iapId,
                accountCouponId = accountCoupon.id
            )
            databaseController.updateSubscription(updateSubscription, existingSubscription.subscriptionType);
        } else {
            val idSub = databaseController.subscriptionRoot_create(subscriptionRootInsert)
            if (idSub > 0) {
                databaseController.accountCoupon_consumeCoupon(accountCoupon.id)
            } else {
                return call.respondBadRequest("Problems creating Free Campaign Subscription")

            }
        }


    }
}

