package net.kidjo.server.api4.extension.account

import com.google.api.client.googleapis.notifications.NotificationUtils
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.shared.database.insert_user_redirect_key
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondNotFound
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.User

suspend fun Api4AccountController.generateKey(call: ApplicationCall) {
    val user = call.principal<User>()
    if (user == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Problems finding User")
    }

    val generatedKey = NotificationUtils.randomUuidString()

    return try {

        val key = databaseController.insert_user_redirect_key(user.getLongId(), generatedKey)
        call.respondOK(mapOf("redirectKey" to key, "successMessage" to "Successful created User's redirect key"))

    } catch (e: Throwable) {
        logger.error("Failed to insert redirect key", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems creating redirect key")
    }
}
