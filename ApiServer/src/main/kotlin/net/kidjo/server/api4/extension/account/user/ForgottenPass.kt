package net.kidjo.server.api4.extension.account.user

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import java.time.LocalDateTime

suspend fun Api4UserController.forgottenPass(call: ApplicationCall) {

    val email = call.parameters.getString("email")
    return if (email.isBlank()) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Empty email")
    } else if (!email.isEmail()) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Bad email")
    } else {
        // Generate a new password
        val password = utility.randomString(8, "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray())
        val preHashedPassword = encryptionController.sha256Hash(password + config.clientSideHashV1Salt)
        val hashedPassword = encryptionController.hashPassword(preHashedPassword)

        // Save the new password for the corresponding user and send an email
        val currentUser = databaseController.user_getByEmail(email)

        return if (currentUser != null) {
            return if (!emailManager.sendResetPasswordEmail(currentUser.email, password, languageCache.get(call))
                || (!databaseController.userUpdatePassword(currentUser.getLongId(), hashedPassword))
            ) {
                call.respondBadRequest(UsersErrors.ERROR_INVALID_COUPON, "Problem updating user's password")
            } else {
                call.respondOK(Success.SUCCESS, "An email has been sent to '${currentUser.email}' if it exists")
            }
        } else {
            call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "There is no such a User.")
        }
    }
}

suspend fun Api4UserController.forgottenPassProblem(call: ApplicationCall) {

    val email = call.parameters.getString("email")
    return if (email.isBlank()) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "Empty email")
    } else if (!email.isEmail()) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Bad email")
    } else {
        // Generate a new password
        val password = utility.randomString(8, "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray())
        val preHashedPassword = encryptionController.sha256Hash(password + config.clientSideHashV1Salt)
        val hashedPassword = encryptionController.hashPassword(preHashedPassword)

        // Save the new password for the corresponding user and send an email
        val currentUser = databaseController.user_getByEmail(email)
        if (currentUser != null) {
            return if ((!databaseController.userUpdatePassword(currentUser.getLongId(), hashedPassword))) {
                call.respondBadRequest(UsersErrors.ERROR_INVALID_COUPON, "Problem updating user's password")
            } else {
                call.respondOK(Success.SUCCESS, "An pass has been generated to '${password}'")
            }
        } else {
            call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "There is no such a User.")
        }
    }
}

suspend fun Api4UserController.braintreeBillingDateProblem(call: ApplicationCall) {
    val customerId = call.parameters.getString("customerId") // 5010404487
    val token = call.parameters.getString("token") // 2bsz78
    var nextBillDate: LocalDateTime? = null
    var paidThroughDate: LocalDateTime? = null
    val lastTransaction = braintreeManager.getLastTransactionBraintreeCustomer(customerId)
    val braintreeSub = braintreeManager.getBraintreeSub(token)
    if (braintreeSub != null) {
        if (braintreeSub.nextBillingDate != null) {
            nextBillDate = LocalDateTime.ofInstant(
                braintreeSub.nextBillingDate.toInstant(),
                braintreeSub.nextBillingDate.timeZone.toZoneId()
            )
            nextBillDate.toString()
        }
        if (braintreeSub.paidThroughDate != null) {
            paidThroughDate = LocalDateTime.ofInstant(
                braintreeSub.paidThroughDate.toInstant(),
                braintreeSub.paidThroughDate.timeZone.toZoneId()
            )
            paidThroughDate.toString()
        }
    }
    return call.respondOK(Success.SUCCESS, "nextBillDate: " + nextBillDate + " paidThroughDate: " + paidThroughDate)

}