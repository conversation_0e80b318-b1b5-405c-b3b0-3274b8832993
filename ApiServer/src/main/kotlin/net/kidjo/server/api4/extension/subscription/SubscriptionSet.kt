package net.kidjo.server.api4.extension.subscription

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4SubscriptionController
import net.kidjo.server.shared.database.getHuaweiSubscriptionByOrderId
import net.kidjo.server.shared.database.subscription_getByToken
import net.kidjo.server.shared.extensions.receiveJSON
import net.kidjo.server.shared.extensions.respondBadJSON
import net.kidjo.server.shared.extensions.respondBadParameters
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User

suspend fun Api4SubscriptionController.mobileSubscription(
    subscriptionType: SubscriptionRoot.SubscriptionType,
    call: ApplicationCall
) {
    val requestJSON = call.receiveJSON() ?: return call.respondBadJSON()
    var userId = 0L

    val authenticatedUser = call.principal<User>()
    if (!requestJSON.has("receipt") || !requestJSON.has("iap")) {
        return call.respondBadParameters()

    }
    val receipt = requestJSON.optString("receipt")
    val iapId = requestJSON.optString("iap")
    val sessionId = requestJSON.optString("sessionId", "NONE")
    val forceUpdate = requestJSON.optBoolean("forceUpdate")
    // Only for Huawei
    val subscriptionId = requestJSON.optString("subscriptionId")
    // Only for Huawei
    val orderId = requestJSON.optString("orderId")
    // Only for Amazon
    val amazonUserId = requestJSON.optString("amazonUserId")
    config.isDeviceType=iapId.toString()

    if (authenticatedUser != null) {
        userId = authenticatedUser.getLongId()
    } else {
        val existingSubscription: SubscriptionRoot? = databaseController.subscription_getByToken(receipt)
        if (existingSubscription != null) {
            userId = existingSubscription.userId
        }
    }

    when (requestJSON.optString("deviceType")?.toLowerCase()) {
        Device.StorePlatform.IOS.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.IOS, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.IOS.raw} with iap: $iapId")

            }

            return paymentAppStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.PLAYSTORE.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.PLAYSTORE, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.PLAYSTORE.raw} with iap: $iapId")

            }

            return paymentPlayStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.SAMSUNG.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.SAMSUNG, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.SAMSUNG.raw} with iap: $iapId")

            }

            return paymentSamsungStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.ORANGE.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.ORANGE, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.ORANGE.raw} with iap: $iapId")

            }

            return paymentOrangeStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, null, null, call
            )
        }

        Device.StorePlatform.HUAWEI.raw -> {
            if (subscriptionId.isNullOrEmpty()) {
                return call.respondBadRequest("The 'subscriptionId' is required!")

            }

            val iap = iapManager.getIAP(Device.StorePlatform.HUAWEI, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.HUAWEI.raw} with iap: $iapId")

            }

            if (userId == 0L) {
                val huaweiSubscription: SubscriptionRoot? = databaseController.getHuaweiSubscriptionByOrderId(orderId!!)
                if (huaweiSubscription != null) {
                    userId = huaweiSubscription.userId
                }
            }

            return paymentHuaweiStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, subscriptionId, orderId, call
            )
        }

        Device.StorePlatform.AMAZON.raw -> {
            if (amazonUserId.isNullOrEmpty()) {
                return call.respondBadRequest("The 'amazonUserId' is required!")

            }
            val iap = iapManager.getIAP(Device.StorePlatform.AMAZON, iapId)

            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.AMAZON.raw} with iap: $iapId")

            }

            return paymentAmazonStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, amazonUserId, null, call
            )
        }

        Device.StorePlatform.JIO.raw -> {
            val iap = iapManager.getIAP(Device.StorePlatform.JIO, iapId)
            if (iap == null) {
                return call.respondBadRequest("can't find iap for ${Device.StorePlatform.JIO.raw} with iap: $iapId")

            }

            return paymentJioStoreManager.start(
                userId, iap, receipt, sessionId,
                subscriptionType, forceUpdate, null, null, call
            )
        }

    }
}
