package net.kidjo.server.api4.extension.coupon

import com.fasterxml.jackson.annotation.JsonInclude
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.response.*
import net.kidjo.server.api4.Api4CouponController
import net.kidjo.server.api4.extension.subscription.helper.createFreeAccessSubscription
import net.kidjo.server.shared.database.subscription_type_getList
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.DiscountDetails
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User

suspend fun Api4CouponController.validateCoupon(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val currentUser = call.principal<User>()
    if (currentUser == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Problems finding User")
    }
    val coupon = requestJSON.optString("coupon")
    if (coupon.isNullOrEmpty()) {
        return call.respondBadRequest("This 'coupon' parameter is required.")
    }
    val accountCoupon = couponManager.getFromDatabase(coupon)

    if (accountCoupon == null || !accountCoupon.isValid()) {
        return call.respondBadRequest(
            UsersErrors.ERROR_INVALID_COUPON,
            "This coupon is invalid or expired."
        )
    }

    if (accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name ||
        accountCoupon.couponType == AccountCouponType.UNIQUE_ACCESS_COUPON.name
    ) {

        val subscriptions = databaseController.subscription_type_getList(currentUser.getLongId())
        subscriptions.forEach { it ->
            if (!utility.isBillingDateExpired(it.nextBillDate) &&
                it.subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_TV &&
                accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name
            ) {
                return call.respond(
                    HttpStatusCode.BadRequest,
                    mapOf(
                        "couponProduct" to SubscriptionRoot.SubscriptionType.KIDJO_TV,
                        "code" to SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_TV.code,
                        "errorMessage" to "This user already has a subscription for KIDJO-TV."
                    )
                )
            }
            if (!utility.isBillingDateExpired(it.nextBillDate) &&
                it.subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_BOOKS &&
                accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name
            ) {
                return call.respond(
                    HttpStatusCode.BadRequest,
                    mapOf(
                        "couponProduct" to SubscriptionRoot.SubscriptionType.KIDJO_BOOKS,
                        "code" to SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_BOOKS.code,
                        "errorMessage" to "This user already has a subscription for KIDJO-BOOKS."
                    )
                )
            }
            if (!utility.isBillingDateExpired(it.nextBillDate) &&
                it.subscriptionType == SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS &&
                accountCoupon.couponType == AccountCouponType.FREE_ACCESS_COUPON.name
            ) {
                return call.respond(
                    HttpStatusCode.BadRequest,
                    mapOf(
                        "couponProduct" to SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS,
                        "code" to SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_TV_BOOKS.code,
                        "errorMessage" to "This user already has a subscription for KIDJO-TV AND KIDJO-BOOKS."
                    )
                )
            }
        }

        api4SubscriptionController.createFreeAccessSubscription(accountCoupon, currentUser.getLongId(), call)
    }

    return call.respondCreated(
        CouponDTO(
            accountCoupon.id,
            accountCoupon.couponId,
            accountCoupon.durationCode,
            accountCoupon.groupId,
            accountCoupon.couponType,
            accountCoupon.productType,
            accountCoupon.partnerType
        )
    )

}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CouponDTO(
    var couponId: Long?,
    var couponSerialNumber: String?,
    var couponDurationCode: String?,
    var couponGroupId: String?,
    var couponType: String?,
    var couponProduct: String?,
    var couponPartner: String?,
    var discountDetails: DiscountDetails? = null,
)
