package net.kidjo.server.api4.extension.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.api5.extension.subscription.mondia.MondiaPushEventType
import net.kidjo.server.api5.extension.subscription.mondiaPushEvent
import net.kidjo.server.shared.database.getLastRedirectKeyByUserId
import net.kidjo.server.shared.database.getLastSubscriptionTokenByUserId
import net.kidjo.server.shared.database.get_email
import net.kidjo.server.shared.database.userUpdateEmail
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User
import java.util.*


suspend fun Api4AccountController.changeUserEmail(call: ApplicationCall) {

    var correlationId = call.getCorrelationId() ?: UUID.randomUUID().toString()

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val currentUser = call.principal<User>()
    if (currentUser == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Problems finding User")
    }

    val email = requestJSON.optString("email")

    if (email.isNullOrEmpty()) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "The email is required")
        return
    }

    if (!validator.isEmailValid(email)) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Invalid email.")
    }

    val existingEmail = databaseController.get_email(email, currentUser.getLongId())
    if (existingEmail != null) {
        return call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, " The email has been taken by another account!")
    }

    try {
        databaseController.userUpdateEmail(currentUser.getLongId(), email)
        call.respondOK(mapOf("Old email: " to currentUser.email, "New email: " to email))

        val userId = currentUser.id.toInt()
        val parts = databaseController.getLastSubscriptionTokenByUserId(userId).split("|")
        val userUuid = parts[0]
        val packageId = parts[1].toLong()
        val key = databaseController.getLastRedirectKeyByUserId(userId.toLong())
        mondiaPushEvent(
            logger, config, databaseController, correlationId,
            MondiaPushEventType.CUSTOMER_ONBOARDING_COMPLETE,
            packageId,
            "",// not mandatory for CUSTOMER_ONBOARDING_COMPLETE event
            userUuid,
            key,
            userId
        )

        return
    } catch (e: Throwable) {
        logger.error("Failed to update user's email", e)
        call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, "Problems updating User's email")
        return
    }
}
