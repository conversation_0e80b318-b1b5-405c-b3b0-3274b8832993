package net.kidjo.server.api4.extension.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.shared.database.getUserLastChangedPassword
import net.kidjo.server.shared.database.userUpdateLastChangedPassword
import net.kidjo.server.shared.database.userUpdatePassword
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User


suspend fun Api4AccountController.changeUserPass(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val currentUser = call.principal<User>()
    if (currentUser == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"Problems finding User")
    }

    val oldPass = requestJSON.optString("oldPass")
    val newPass = requestJSON.optString("newPass")

    val passwordMatched = encryptionController.checkPassword(oldPass, currentUser.hashedPassword)
    if (!passwordMatched) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "The password is wrong")
        return
    }
    if (newPass == databaseController.getUserLastChangedPassword(currentUser.getLongId())) {
        return call.respondBadRequest("New password can't match the previous one")
    }
    try {
        val hashedPassword = encryptionController.hashPassword(newPass)
        databaseController.userUpdatePassword(currentUser.getLongId(), hashedPassword)
        databaseController.userUpdateLastChangedPassword(currentUser.getLongId(), oldPass)
        call.respondOK(Success.SUCCESS,"Successful updated User's password")
        return
    } catch (e: Throwable) {
        logger.error("Failed to update user's password", e)
        call.respondBadRequest(UsersErrors.ERROR_EXIST_USER,"Problems updating User's password")
        return
    }
}
