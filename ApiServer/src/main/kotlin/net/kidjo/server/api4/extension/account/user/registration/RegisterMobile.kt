package net.kidjo.server.api4.extension.account.user.registration

import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.api4.RegisterMobileDtoIn
import net.kidjo.server.api4.UserDtoOut
import net.kidjo.server.api4.extension.account.user.helpers.createUser
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.User

suspend fun Api4UserController.registerMobile(call: ApplicationCall) {

    val registerMobileIn = call.receive<RegisterMobileDtoIn>()

    if (registerMobileIn.email.isNullOrEmpty() || registerMobileIn.password.isNullOrEmpty()) {
        return call.respondBadRequest("Bad Params")
    }

    if (!validator.isEmailValid(registerMobileIn.email)) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
    }

    val currentUser = databaseController.user_getByEmail(registerMobileIn.email)
    if (currentUser != null) {
        return call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, "There is already such a User")
    }
    val user = User.getEmptyUser()

    user.authType = User.AuthType.EMAIL
    user.email = registerMobileIn.email
    user.name = registerMobileIn.displayName!!
    user.hashedPassword = encryptionController.hashPassword(registerMobileIn.password)
    user.countryId = countryCodeByGoogle.getCountryIdByIP(call)

    user.id = createUser(user, call)
    user.authToken = userManager.generateAndSetUpUserAccessToken(user.getLongId())

    return call.respondOK(
        UserDtoOut(
            user.id,
            null,
            false,
            user.authToken
        )
    )
}
