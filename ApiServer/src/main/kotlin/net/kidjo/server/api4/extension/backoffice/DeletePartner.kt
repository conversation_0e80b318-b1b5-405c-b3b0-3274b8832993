package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*

suspend fun Api4BackofficeController.deletePartner(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val partnerId = requestJSON.optLong("partnerId")

    if(partnerId == null){
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"The 'partnerId' parameter is required")
    }

    try {

        databaseController.deleteAccountCouponPartner(partnerId)

        call.respondOK("Successful deleted partner id: $partnerId")
    } catch (e: Throwable) {
        logger.error("Failed to deleted partner", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"Problems deleting partner")
    }
}
