package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.subscription.mobileSubscription
import net.kidjo.server.api4.extension.subscription.subscriptionsInfo
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.models.SubscriptionRoot
import org.slf4j.LoggerFactory

class Api4SubscriptionController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4SubscriptionController")
    override fun installRoutes(route: Route) {
        //Subscribe Mobile kidjo-TV
        route.post("/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_TV, call) }
        //Subscribe Mobile kidjo-BOOKS
        route.post("/books/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_BOOKS, call)}
        //Subscribe Mobile kidjo-GAMES
        route.post("/games/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_GAMES, call)}


        //Get Subscription Info without a User
        route.get("/subscriptions") { <EMAIL>(call)}
    }
}
