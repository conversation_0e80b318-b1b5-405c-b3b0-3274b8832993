
import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.api4.publichers.Api4OrangeController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.Device
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.SubscriptionRootInsert
import net.kidjo.server.shared.models.SubscriptionRootUpdate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Create the orange end-user's subscription in Kidjo-Tv
 */
suspend fun Api4OrangeController.createOrUpdateOrangeUserSubscriptions(
    orangeUserInfo: Api4OrangeController.OrangeUserInfo,
    call: ApplicationCall
) {
    val userAlias = orangeUserInfo.userAuthAlias
    val subscriptionOperator = orangeUserInfo.data.operator
    val userSubscriptions = orangeUserInfo.data.subscriptions

    if (userSubscriptions.isEmpty()) {
        return call.respond(HttpStatusCode.NotFound, "The End-User doesn't have an Orange subscription! ")
    }

    val virgoSubscription = orangeUserInfo.data.subscriptions[0]
    val iap = virgoSubscription.partnerPricingId

    if (!iap.contains("tv")) {
        return call.respond(HttpStatusCode.NotFound, "The End-User doesn't have a subscription for the Kidjo TV app! ")
    }

    val isActiveSubscription = virgoSubscription.status == "subscribed"
    val startDate = getFormattedStartDate(virgoSubscription.date)

    val customSubscriptionToken =
        encryptionController.sha256Hash(virgoSubscription.date + userAlias + config.clientSideHashV1Salt)
    var existingSubscription: SubscriptionRoot? =
        databaseController.subscription_getByToken(customSubscriptionToken)
    var newNextBillDate =
        createNewNextBillDate(startDate, isActiveSubscription)
    val responseMessage = StringBuilder()

    //Deactivate subscription if is unsubscribed and is Expired!
    if (startDate.isBefore(LocalDateTime.now()) && !isActiveSubscription) {
        var subscriptionId = databaseController.getSubscriptionIdByOrangeAlias(userAlias)
        val nextBillDateInPast = LocalDateTime.now().minusDays(1)
        if (subscriptionId == null) {
            return call.respond(
                HttpStatusCode.NotFound,
                "The End-User doesn't have a Kidjo subscription! "
            )
        }
        if (!databaseController.subscription_cancelFull_virgo(customSubscriptionToken, nextBillDateInPast)) {
            return call.respondBadRequest("Error Kidjo user's subscription")
        }
        responseMessage.append("Successful DEACTIVATED Kidjo subscription with customSubscriptionToken: $customSubscriptionToken ")

    } else if (existingSubscription == null && newNextBillDate.isAfter(LocalDateTime.now())) {
        //Create new subscription if NOT Exist and Orange subscription nextBillDate is NOT Expired!
        val subId = createOrangeSubscription(
            subscriptionOperator,
            iap,
            customSubscriptionToken,
            newNextBillDate,
            userAlias
        )
        if (subId < 0
        ) return call.respondBadRequest("Problems creating KidjoOrange Subscription")
        responseMessage.append("Successful CREATED Orange subscription with id: $subId ")

    } else if (existingSubscription != null && newNextBillDate.isAfter(existingSubscription.nextBillDate)) {
        //Update subscription if the old IS Expired!
        val subscriptionRootUpdate = SubscriptionRootUpdate(
            existingSubscription.userId,
            true,
            newNextBillDate,
            existingSubscription.platformPurchaseId,
            existingSubscription.stillInFreeTrial,
            0.0F,
            existingSubscription.id,
            existingSubscription.paymentStateId
        )

        if (!databaseController.subscriptionRoot_update(subscriptionRootUpdate)) {
            return call.respondBadRequest("Error updating orange user's subscription")
        }
        responseMessage.append("Successful UPDATED orange subscription with id: ${existingSubscription.id} ")
    } else {
        responseMessage.append("Successful VALIDATED Orange User.")
    }

    return call.respondOK(mapOf("userAlias" to userAlias, "message" to responseMessage))
}

private fun Api4OrangeController.createOrangeSubscription(
    subscriptionOperator: String,
    iap: String,
    hashedSubscriptionToken: String,
    nextBillDate: LocalDateTime,
    userAlias: String
): Long {
    val idSub = createKidjoSubscription(subscriptionOperator, iap, hashedSubscriptionToken, nextBillDate)
    if (idSub < 0) {
        return idSub
    }
    if (!createVirgoLink(userAlias, hashedSubscriptionToken, idSub)) {
        return -1L
    }
    return idSub
}

private fun Api4OrangeController.createVirgoLink(
    userAlias: String,
    hashedSubscriptionToken: String,
    idSub: Long
): Boolean {
    return databaseController.createDVPairedUser(
        operationId = userAlias,
        virgoAlias = userAlias,
        subscriptionToken = hashedSubscriptionToken,
        subscriptionId = idSub
    )
}

private fun Api4OrangeController.createKidjoSubscription(
    subscriptionOperator: String,
    iap: String,
    hashedSubscriptionToken: String,
    nextBillDate: LocalDateTime
): Long {
    val subscriptionRootInsert = SubscriptionRootInsert(
        0L, 0L, false,
        0F, SubscriptionRoot.PaymentType.NATIVE, SubscriptionRoot.SubscriptionType.KIDJO_TV,
        subscriptionOperator, subscriptionOperator,
        Device.StorePlatform.ORANGE, subscriptionOperator, iap,
        subscriptionOperator, hashedSubscriptionToken,
        encryptionController.sha256Hash(hashedSubscriptionToken), 0L,
        true, nextBillDate, !config.env.isLive, SubscriptionRoot.OPERATOR_WEB
    )

    return databaseController.subscriptionRoot_create(subscriptionRootInsert)
}

private fun createNewNextBillDate(
    startDate: LocalDateTime,
    isSubscriptionActive: Boolean
): LocalDateTime {
    var newNextBillDate = startDate.plusDays(7)
    //if subscribed but the start date is expired, add 7 days more
    if (isSubscriptionActive && LocalDateTime.now().isAfter(newNextBillDate)) {
        newNextBillDate = LocalDateTime.now().plusDays(7)
    }
    return newNextBillDate
}

private fun getFormattedStartDate(
    startDate: String
): LocalDateTime {
    return LocalDateTime.parse(
        startDate.substringBefore("+"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
    )
}

