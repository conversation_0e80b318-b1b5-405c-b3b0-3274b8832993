package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.listAllOriginCountries
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK
import java.sql.SQLException

suspend fun Api4BackofficeController.listAllOriginCountries(call: ApplicationCall) {
    return try {
        val countries = databaseController.listAllOriginCountries()
        call.respondOK(countries)
    } catch (e: SQLException) {
        logger.error("Database interaction error ", e)
        call.respondError(HttpStatusCode.InternalServerError, "Database interaction error")
    }
}
