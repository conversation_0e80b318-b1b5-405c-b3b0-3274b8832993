package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.subscription_getStores
import net.kidjo.server.shared.extensions.*

suspend fun Api4BackofficeController.getStores(call: ApplicationCall) {

    return try {
        call.respondOK(databaseController.subscription_getStores())
    } catch (e: Throwable) {
        logger.error("Failed to find users content", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding users")
    }
}
