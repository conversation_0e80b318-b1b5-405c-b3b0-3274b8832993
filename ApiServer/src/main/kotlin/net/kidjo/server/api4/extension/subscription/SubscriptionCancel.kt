package net.kidjo.server.api4.extension.subscription

import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.payments.BraintreeApiManager
import net.kidjo.server.shared.payments.DigitalVirgoApiManager
import net.kidjo.server.shared.tools.Config
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

private val logger = LoggerFactory.getLogger("- subscriptionsCancel- ")
suspend fun subscriptionsCancel(call: ApplicationCall,
                                databaseController: DatabaseController,
                                braintreeManager: BraintreeApiManager,
                                digitalvirgoApiManager: DigitalVirgoApiManager,
                                config: Config
) {
try{
    val user = call.principal<User>() ?: return call.respondError(HttpStatusCode.BadRequest, "There is no such a user")
    val subscriptionId = call.parameters.getLong("id")

    //CANCELING BRAINTREE SUBSCRIPTION
    val braintreeSubscription = databaseController.getRecentActiveBraintreeSubscriptionBySubIdOrUserId(subscriptionId, user.getLongId())

    if(braintreeSubscription != null) {
        if(!braintreeSubscription.isRenewing) return call.respondBadRequest(ServerErrors.ERROR_SUBSCRIPTION_IS_NOT_RENEWING, "The Braintree Subscription is already cancelled")
        val resultBraintreeCancel = braintreeManager.cancelSubscription(user, braintreeSubscription, true)
        if (!resultBraintreeCancel) return call.respondBadRequest("There is a problem cancelling Braintree Subscription: ${braintreeSubscription.id}")

        return call.respondOK("Successful canceled Braintree subscription id: ${braintreeSubscription.id} ")
    }

    //CANCELING DIGITAL VIRGO SUBSCRIPTION
    val digitalVirgoSubscription = databaseController.getDVPairedSubscriptionByIdOrUserId(subscriptionId, user.getLongId())
    if(digitalVirgoSubscription?.correlationId != null) {
        val resultDVCancel =
            digitalvirgoApiManager.subscription.cancelDVPASS(
                digitalVirgoSubscription.subscriptionToken!!,
                digitalVirgoSubscription.correlationId!!,
                LocalDateTime.now()
            )
        if(!resultDVCancel) {
            return call.respondBadRequest("There is problem cancelling Digital Virgo Subscription: ${digitalVirgoSubscription.subscriptionId}")
        }
        return call.respondOK("Successful canceled Digital Virgo subscription id: ${digitalVirgoSubscription.subscriptionId} ")
    } else {
        return call.respondRedirect(config.dv_cancel_url, false)

    }

    //CANCELING OTHER ACTIVE SUBSCRIPTION
    val activeSubscription = databaseController.getSubscriptionRecentActiveById(subscriptionId)
    if(activeSubscription != null ) {
        if(activeSubscription.id == digitalVirgoSubscription?.subscriptionId ||
            activeSubscription.id == braintreeSubscription?.id) return call.respondNoContent()

        val resultActiveCancel =
            databaseController.subscription_cancelFull(activeSubscription.id)
        if(!resultActiveCancel) {
            return call.respondBadRequest("There is problem cancelling Active Kidjo Subscription: ${activeSubscription.id}")
        }
        return call.respondOK("Successful canceled Active Kidjo subscription id: ${activeSubscription.id} ")
    }
    return call.respondNotFound(
        UsersErrors.ERROR_NOT_EXIST_USER,
        errorMessage = "There is NO SUSBCRIPTION with ID: $subscriptionId for the Logged User with ID: ${user.getLongId()}")
    } catch (e : Exception){
        logger.error("Error DV subscriptionInfoResponse, ${e.localizedMessage}")
        return call.respondBadRequest("There is problem cancelling Kidjo Subscription: ${e.localizedMessage}" )
    }
}
