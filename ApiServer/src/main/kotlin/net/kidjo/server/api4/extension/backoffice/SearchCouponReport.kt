package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.CouponSearchType
import net.kidjo.server.shared.database.searchCoupons
import net.kidjo.server.shared.extensions.*

suspend fun Api4BackofficeController.searchCoupons(call: ApplicationCall) {

    val limit = call.getParameterOrDefaultValue("limit", 20)!!.toInt()
    val offset = call.getParameterOrDefaultValue("offset", 0)!!.toInt()
    val fromDate = call.getParameterOrDefaultValue("fromDate")
    val toDate = call.getParameterOrDefaultValue("toDate")
    val search = call.getParameterOrDefaultValue("search")
    var searchType = call.getParameterOrDefaultValue("searchType")
    val country = call.getParameterOrDefaultValue("country")
    val duration = call.getParameterOrDefaultValue("duration")
    val product = call.getParameterOrDefaultValue("product")
    val type = call.getParameterOrDefaultValue("type")
    val partner = call.getParameterOrDefaultValue("partner")
    val couponId = call.getParameterOrDefaultValue("couponId")
    val status = call.getParameterOrDefaultValue("status")
    val sortColumns = call.getParameterOrDefaultValue("sortColumns")?.split(",")?.toList()
    val sortColumnOrders = call.getParameterOrDefaultValue("sortColumnOrders")?.split(",")?.toList()

    if (!search.isNullOrBlank() && (search.length < 3)) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The parameter 'search' must be minimum 3 characters !")
    }
    if (searchType.isNullOrBlank()) {
        searchType = if(couponId == null) CouponSearchType.BATCH_NAME.name else CouponSearchType.USER_EMAIL_COUPON_CODE.name
    }

    try {
        val (size, coupons) = databaseController.searchCoupons(
            limit = limit,
            offset = offset,
            fromDate = fromDate,
            toDate = toDate,
            search = search,
            searchType= searchType,
            duration = duration,
            country = country,
            product = product,
            type = type,
            partner = partner,
            couponId = couponId,
            status = status,
            sortColumns = sortColumns,
            sortColumnOrders = sortColumnOrders
        )
        call.respondOK(mapOf("size" to size, "coupons" to coupons))
    } catch (e: Throwable) {
        call.respondError(
            statusCode = HttpStatusCode.InternalServerError,
            code = ServerErrors.INTERNAL_ERROR.code,
            errorMessage = e.localizedMessage
        )
    }
}
