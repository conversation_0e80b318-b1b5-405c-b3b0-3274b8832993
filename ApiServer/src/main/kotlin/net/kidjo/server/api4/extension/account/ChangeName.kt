package net.kidjo.server.api4.extension.account

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.shared.database.userUpdateName
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User


suspend fun Api4AccountController.changeUserName(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val currentUser = call.principal<User>()
    if (currentUser == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"Problems finding User")
    }

    val name = requestJSON.optString("name")

    if (name.isNullOrEmpty()) {
        call.respondBadRequest(UsersErrors.ERROR_BAD_PASS, "The name is required")
        return
    }
    try {
        databaseController.userUpdateName(currentUser.getLongId(), name)

        call.respondOK(mapOf("Old name: " to currentUser.name, "New name: " to name))
        return
    } catch (e: Throwable) {
        logger.error("Failed to update user's name", e)
        call.respondBadRequest(UsersErrors.ERROR_EXIST_USER,"Problems updating User's name")
        return
    }
}
