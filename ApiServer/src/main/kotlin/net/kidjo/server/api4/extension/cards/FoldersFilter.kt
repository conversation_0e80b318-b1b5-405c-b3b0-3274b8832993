package net.kidjo.server.api4.extension.cards

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.common.models.Folder
import net.kidjo.server.api4.Api4CardsController
import net.kidjo.server.shared.database.getFilterList
import net.kidjo.server.shared.extensions.getInt
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.json.v3.new_filterFolderToJSON
import net.kidjo.server.shared.tools.RequestUtil
import org.json.JSONArray
import org.json.JSONObject

suspend fun Api4CardsController.listFolders(call: ApplicationCall) {

    val acceptLanguageRaw = call.request.acceptLanguage() ?: ""

    val params = call.parameters
    val kidAge = params.getInt("age", 4)
    val folderType = Folder.ContentType.FromRaw(params.getString("contentType"))

    val countryId = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw) ?: 284
    val languageId = languageCache.getLanguageIdFromAcceptLanguageHeader(acceptLanguageRaw)

    val folders = databaseController.getFilterList(kidAge, languageId, countryId, folderType)

    val json = JSONObject()
    json.put("folderImageURL", config.folderThumbnailImageUrl)
    val folderJSONArray = JSONArray()
    folders.forEach { folderJSONArray.put(jsonCreator.new_filterFolderToJSON(it)) }
    json.put("folders", folderJSONArray)

    call.respondText(json.toString(), ContentType.Application.Json)
}
