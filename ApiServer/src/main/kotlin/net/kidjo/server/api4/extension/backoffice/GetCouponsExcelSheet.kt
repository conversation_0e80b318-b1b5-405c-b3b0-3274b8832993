package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.getAccountCoupons
import net.kidjo.server.shared.database.getSumRedeemedCoupons
import org.apache.poi.hssf.usermodel.HSSFWorkbook
import java.io.File
import java.io.FileOutputStream

@Suppress("BlockingMethodInNonBlockingContext")
suspend fun Api4BackofficeController.getCouponsExcelSheet(call: ApplicationCall) {
    val groupId = call.parameters["groupId"]
    try {
        val coupons = databaseController.getAccountCoupons(groupId!!)
        val workbook = HSSFWorkbook()
        val sheet = workbook.createSheet("Coupons")
        val sheetData = mutableMapOf<Int, Array<*>>()
        var counter = 0
        sheetData[counter++] = arrayOf(
            "Group id",
            "Coupon id",
            "Duration code",
            "Redeemed coupons sum",
            "Coupon Type",
            "Product type",
            "Partner type",
            "Expiration date"
        )
        coupons.forEach { coupon ->
            val redeemedCouponsSum = databaseController.getSumRedeemedCoupons(coupon.groupId)
            with(coupon) {
                sheetData[counter++] = arrayOf(
                    groupId,
                    couponId,
                    durationCode,
                    redeemedCouponsSum,
                    couponType,
                    productType,
                    partnerType,
                    expireDate
                )
            }
        }
        var rowNum = 0
        sheetData.keys.forEach { key ->
            val row = sheet.createRow(rowNum++)
            val data = sheetData[key]
            var cellNum = 0
            data?.forEach { dataItem ->
                val cell = row.createCell(cellNum++)
                cell.setCellValue(dataItem.toString())
            }
        }
        val xlsFile = File("coupons.xls")
        val fileOutputStream = FileOutputStream(xlsFile)
        workbook.write(fileOutputStream)
        fileOutputStream.close()
        call.respondFile(xlsFile)
        xlsFile.delete()
    } catch (e: Throwable) {
        logger.error("Error while creating coupons excel file", e)
        call.respond(HttpStatusCode.InternalServerError, "Error while creating coupons excel file")
    }
}
