package net.kidjo.server.api4.extension.subscription.logicom

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.publichers.Api4LogicomController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*
import java.time.LocalDateTime

suspend fun Api4LogicomController.subscriptionSet(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val user = call.principal<User>()
    val serialNumber = requestJSON.optString("serialNumber")
    val macAddress = requestJSON.optString("macAddress")

    // Check last Logicom coupon
    val lastLogicomCoupon = databaseController.logicomGetFirstWithRemaining()
    if (lastLogicomCoupon == null || LocalDateTime.now().isAfter(lastLogicomCoupon.validUntil)) {
        logger.debug("checkLogicom() | there is no Logicom coupon available")
        return call.respondBadRequest(SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION, "There is no valid logicom coupon")
    }

    // Check device serial number or mac address
    val logicomSerialBundle = databaseController.logicomV4Check(serialNumber, macAddress)
    if (!logicomSerialBundle.check) {
        logger.debug("checkLogicom() | device serial number '$serialNumber' or mac address '$macAddress' is not valid")
        return call.respondBadRequest(SubscriptionErrors.ERROR_NOT_EXIST_SUBSCRIPTION, "Invalid device serial number or mac address")
    }

    if(user != null) {

        var checkSubscription: SubscriptionRoot? = databaseController.getLogicomSubscriptionRecentActive(user.getLongId())

        if(checkSubscription == null) {
                val subscriptionRootInsert = SubscriptionRootInsert(user.getLongId(), 0L, false, 0f,
                        SubscriptionRoot.PaymentType.NATIVE, SubscriptionRoot.SubscriptionType.KIDJO_TV, "Logicom", "Logicom", Device.StorePlatform.PLAYSTORE, "Free_Campaign", "Logicom",
                        "none", "none", "none", 0L, false, lastLogicomCoupon.validUntil, !config.env.isLive)
                val id = databaseController.subscriptionRoot_create(subscriptionRootInsert)
                if (id > 0) {
                    if (logicomSerialBundle.check) {
                        logger.debug("checkLogicom() | set coupon with id '$lastLogicomCoupon.id' ")
                        // Enable coupon for device
                        databaseController.couponV4Set(lastLogicomCoupon.id )
                    }
                    return call.respondOK(Success.SUCCESS, "Successful created Subscription to User id: ${subscriptionRootInsert.userId} ")
                } else {
                    logger.error("Error create user's subscription: ${subscriptionRootInsert.userId}")
                    return call.respondBadRequest(SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_TV, "Error create user's subscription: ${subscriptionRootInsert.userId}")
                }
        } else {
            if(LocalDateTime.now().isAfter(checkSubscription.nextBillDate)){
                return call.respondBadRequest(SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_TV_BOOKS,"Expired subscription")
            }else {
                return call.respondBadRequest(SubscriptionErrors.ERROR_EXIST_FOR_KIDJO_BOOKS,"There is already Logicom subscription until: ${checkSubscription.nextBillDate}")
            }
        }

    }
}
