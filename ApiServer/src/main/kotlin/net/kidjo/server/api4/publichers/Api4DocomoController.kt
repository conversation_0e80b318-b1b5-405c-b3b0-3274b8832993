package net.kidjo.server.api4.publichers

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.subscription.docomo.subscriptionSet
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api4DocomoController (platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4DocomoController")
    override fun installRoutes(route: Route) {
        route.post("/docomo/subscriptions") { <EMAIL>(call) }
    }
}
