package net.kidjo.server.api4

import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.put
import net.kidjo.server.api4.extension.cards.listCards
import net.kidjo.server.api4.extension.cards.listFolders
import net.kidjo.server.api4.extension.cards.listLanguages
import net.kidjo.server.api4.extension.cards.searchCards
import net.kidjo.server.api4.extension.cards.updateFolderDescription
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.json.v3.JsonObjectCreatorV3
import org.slf4j.LoggerFactory

class Api4CardsController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4CardsController")
    internal val jsonCreator = JsonObjectCreatorV3(config, encryptionController)
    override fun installRoutes(route: Route) {
        route.get("content/cards/search") { <EMAIL>(call) }
        route.get("content/cards") { <EMAIL>(call) }
        route.get("content/folders") { <EMAIL>(call) }
        route.get("content/languages") { <EMAIL>(call) }
        route.put("update/folders") { <EMAIL>(call) }
    }
}
