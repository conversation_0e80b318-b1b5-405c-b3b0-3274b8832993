package net.kidjo.server.api4.publichers

import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import net.kidjo.common.models.Language
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.licensesGetList

class Api4ZattooController(platformInjector: PlatformInjector)
    : BaseServerController(platformInjector) {

    data class PromotedFolder(
        val id: Long,
        val title: String,
        val description: String,
        val media_type: String = "videos",
        val genres: String,
        val images: List<PromotedFolderImage>,
        val deep_links : String,
        val publish_date: String,
        val duration: String,
        val metadata_language: String,
        val aired_date: String = "",
        val country_availability: List<String>
                              )

    data class PromotedFolderImage(val url: String,
                                   val format: String,
                                   val width: Int,
                                   val height: Int)

    override fun installRoutes(route: Route) {
        // Miscellaneous routes to return data
        route.get("/zattoo/promoted") { <EMAIL>(call) }
    }

    /**
     * Retrieve the Kidjo promoted content for Zattoo to display in the proper section of their services
     */
    private suspend fun getPromotedContent(call: ApplicationCall) {
        val languageParam = call.parameters["language"]
        val language = languageParam?.let { Language.fromShortName(it) } ?: Language.ENGLISH
        val promotedContent = databaseController.licensesGetList( null, language.id,
                null, null, null, getCountry = true, getLanguage = true)

        val list = promotedContent.map {
            val images = mutableListOf<PromotedFolderImage>()
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/350_525/${it.folderId}.png", "2:3", 350, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/525_525/${it.folderId}.png", "1:1", 525, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/934_525/${it.folderId}.png", "16:9", 934, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/700_525/${it.folderId}.png", "4:3", 700, 525))
            images.add(PromotedFolderImage("${config.folderUrl}folderImage/525_350/${it.folderId}.png", "3:2", 525, 350))
            PromotedFolder(it.id, it.title, it.description ?: "",
                    genres = it.genre?.capitalize() ?: "", images = images,
                    deep_links = "https://app.kidjo.tv/folder/${it.folderId}", publish_date = "",
                    duration = "", metadata_language = it.language ?: "",
                    country_availability = it.countries)
        }
        call.respond(list)
    }

}
