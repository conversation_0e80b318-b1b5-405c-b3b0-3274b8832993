package net.kidjo.server.api4.extension.account.user.helpers

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.shared.database.user_register
import net.kidjo.server.shared.extensions.respondDatabaseIssue
import net.kidjo.server.shared.models.User


suspend fun Api4UserController.createUser(user: User, call: ApplicationCall): String {
    val id = databaseController.user_register(user).toString()
    if (id == User.NO_ID) {
        call.respondDatabaseIssue()
        return id
    }

    user.id = id
    if (validator.isEmailValid(user.email)) {
        emailManager.sendConfirmationEmail(user.email, user.name, languageCache.get(call))
    }

    return id
}
