package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.braintree.braintreeSubscriptionsCancel
import net.kidjo.server.api4.extension.braintree.braintreeUpdateSubscription
import net.kidjo.server.api4.extension.braintree.createBraintreeSubscription
import net.kidjo.server.api4.extension.braintree.generateToken
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.models.SubscriptionRoot
import org.slf4j.LoggerFactory

class Api4BraintreeController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4BraintreeController")
    override fun installRoutes(route: Route) {
        route.get("/braintree/generate-token") { <EMAIL>(call) }

        route.post("/braintree/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_TV, call) }
        route.post("/braintree/books/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_BOOKS, call) }
        route.post("/braintree/games/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_GAMES, call) }

        route.post("/braintree/tv-books/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS, call) }
        route.post("/braintree/tv-games/subscriptions") { <EMAIL>(SubscriptionRoot.SubscriptionType.KIDJO_TV_GAMES, call) }

        route.get("/braintree/subscriptions/cancel") { <EMAIL>(call) }
        route.post("/braintree/subscriptions/update") { <EMAIL>( call) }
    }
}
