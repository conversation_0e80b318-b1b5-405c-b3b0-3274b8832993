package net.kidjo.server.api4.extension.braintree

import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.http.*
import net.kidjo.server.api4.Api4BraintreeController
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.getRecentBOOKSubscription
import net.kidjo.server.shared.database.getRecentGAMESubscription
import net.kidjo.server.shared.database.getRecentTVSubscription
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import net.kidjo.server.shared.payments.BraintreeApiManager
import net.kidjo.server.shared.tools.LanguageManager
import net.kidjo.server.shared.tools.LanguageTerms
import net.kidjo.server.shared.tools.Utility
import java.time.LocalDateTime
suspend fun Api4BraintreeController.createBraintreeSubscription(subscriptionType: SubscriptionRoot.SubscriptionType, call: ApplicationCall) {

    val device = call.getDeviceFromHeader(encryptionController)
    val user = call.principal<User>() ?: return call.respondError(HttpStatusCode.BadRequest, "There is no such a user")

    if(isUserSubscribedSubscription(user.getLongId(), utility, subscriptionType, databaseController)) return call.respondBadRequest("The User:${user.getLongId()} is already subscribed! ")

    val requestJSON = call.receiveJSON() ?: return call.respondBadJSON()

    val nonce = requestJSON.optString("nonce")
    val billingName = requestJSON.optString("name")
    val deviceData = requestJSON.optString("deviceData")
    val coupon = couponManager.getFromDatabase(requestJSON.optString("coupon"))
    val iap = iapManager.getKidjoBrainTreeIAP(requestJSON.optString("iap"))

    if(coupon != null && LocalDateTime.now().isAfter(coupon.expireDate)) return call.respondBadRequest("INVALID_COUPON")

    val addingPaymentError = braintreeManager.
            setPaymentDefaultMethod(
                user = user,
                nameOnPaymentMethod = billingName,
                nonce = nonce,
                deviceData = deviceData
            )

    if (!addingPaymentError.noError) return call.respondBadRequest(GetLocalizedErrorMessage(addingPaymentError,LanguageManager.getLanguageTerms(languageCache.get(call))))

    return braintreeManager.
            startBraintreeSubscription(
                device = device,
                user = user,
                sessionId = "NONE",
                inAppPurchase = iap!!,
                forceNoFreeTrial = false,
                subscriptionStartDate = null,
                subscriptionType = subscriptionType,
                withCoupon = coupon,
                call = call
            )
}

private fun isUserSubscribedSubscription(
    userId: Long,
    utility: Utility,
    subscriptionType: SubscriptionRoot.SubscriptionType,
    databaseController: DatabaseController
): Boolean {
    var mostRecentSub: SubscriptionRoot? = null
    if(subscriptionType.raw.contains("tv")){
        mostRecentSub = databaseController.getRecentTVSubscription(userId)
    }
    if(subscriptionType.raw.contains("books")){
        mostRecentSub = databaseController.getRecentBOOKSubscription(userId)
    }
    if(subscriptionType.raw.contains("games")){
        mostRecentSub = databaseController.getRecentGAMESubscription(userId)
    }
    if (mostRecentSub != null && !utility.isBillingDateExpired(mostRecentSub.nextBillDate)) {
        return true
    }
    return false
}

fun GetLocalizedErrorMessage(paymentError: BraintreeApiManager.PaymentError, languageTerms: LanguageTerms): String {
    return when (paymentError) {
        BraintreeApiManager.PaymentError.NONE -> ""
        BraintreeApiManager.PaymentError.ISSUE_ADDING_CARD -> languageTerms.paymentErrorIssueAddingCc
        BraintreeApiManager.PaymentError.ISSUE_CHARGING_CARD -> languageTerms.paymentErrorIssueChargingCc
    }
}
