package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.account.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.onboarding.controller.extension.account.user.userSignOut
import org.slf4j.LoggerFactory

class Api4AccountController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4AccountController")
    override fun installRoutes(route: Route) {
        route.post("/users/change-pass") { <EMAIL>(call) }
        route.post("/users/change-name") { <EMAIL>(call) }
        route.post("/users/change-email") { <EMAIL>(call) }

        //user info
        route.get("/users/info") { <EMAIL>(call) }
        route.get("/books/users/info") { <EMAIL>(call) }
        route.get("/games/users/info") { <EMAIL>(call) }

        // user's subscription info
        route.get("/users/subscriptions") { <EMAIL>(call)}

        route.get("/users/redirect/generate-key") { <EMAIL>(call) }

        route.delete("/users/signout") { <EMAIL>(call) }
    }
}
