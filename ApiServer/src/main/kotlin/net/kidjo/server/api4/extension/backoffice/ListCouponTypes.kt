package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.listCouponTypes(call: ApplicationCall) {
    try {
        val types = databaseController.listAllCouponTypes()
        call.respondOK(types)
    } catch (e: Throwable) {
        logger.error("Failed to find coupon types", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems find coupon types ")
    }
}
