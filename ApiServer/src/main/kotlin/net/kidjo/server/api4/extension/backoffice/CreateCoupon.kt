package net.kidjo.server.api4.extension.backoffice

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.bean.Coupon
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.countAccountCoupon
import net.kidjo.server.shared.database.insertMultipleAccountCoupon
import net.kidjo.server.shared.database.insertSingleAccountCoupon
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.AccountCouponProduct
import net.kidjo.server.shared.models.AccountCouponType
import net.kidjo.server.shared.models.entity.AccountCoupons
import net.kidjo.server.shared.models.entity.Discount
import net.kidjo.server.shared.models.entity.DiscountCountry
import net.kidjo.server.shared.models.entity.DiscountCoupons
import net.kidjo.server.shared.tools.RequestUtil
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import java.math.BigDecimal
import java.sql.SQLDataException
import java.sql.SQLIntegrityConstraintViolationException
import java.util.*
import kotlin.random.Random


suspend fun Api4BackofficeController.createCoupon(call: ApplicationCall) {

    var requestCoupon: Coupon?
    try {
        requestCoupon = call.receive<Coupon>()
    } catch (e: Exception) {
        return call.respondError(
            HttpStatusCode.BadRequest, "Some parameters are missing pass mandatory productId, typeId, " +
                    "expirationDate, duration, groupId, startDate"
        )
    }

    val acceptLanguageRaw = call.request.acceptLanguage()
    val typeId = requestCoupon.typeId
    val productId = requestCoupon.productId
    val groupId = requestCoupon.groupId
    val partnerId = requestCoupon.partnerId
    val numberToGenerate = requestCoupon.numberToGenerate?.toInt() ?: 1
    val duration = requestCoupon.duration
    val startDate = requestCoupon.startDate.takeIf { it.isNotEmpty() } ?: ""
    val expirationDate = requestCoupon.expirationDate.takeIf { it.isNotEmpty() } ?: ""
    val serialNumber = requestCoupon.serialNumber.takeIf { it.isNotEmpty() } ?: ""
    val redeemAmount = requestCoupon.redeemAmount?.toInt() ?: 0
    val discountPrice = requestCoupon.discountPrice ?: BigDecimal.ZERO
    val braintreeId = requestCoupon.braintreeId.takeIf { it.isNotEmpty() } ?: ""
    val countryId = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw) ?: 2

    if (typeId == 0) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'typeId' parameter is required")
    }
    if (productId == 0) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'productId' parameter is required")
    }
    if (groupId.isBlank()) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'groupId' partnerId is required")
    }
    if (duration.isNotBlank()) {
        if (expirationDate.isBlank()) {
            return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'expirationDate' partnerId is required")
        }
        if (startDate.isBlank()) {
            return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'startDate' partnerId is required")
        }
    }
    if (numberToGenerate == 0) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'numberToGenerate' is required")
    }
    val batchName = utility.getFormattedString(groupId)

    if ((typeId == AccountCouponType.STANDARD_COUPON.raw.toInt()) ||
        (typeId == AccountCouponType.FREE_ACCESS_COUPON.raw.toInt()) ||
        (typeId == AccountCouponType.DISCOUNT_COUPON.raw.toInt())
    ) {
        val beforeCount = databaseController.countAccountCoupon(batchName)
        var amountGenerated = 0
        if (numberToGenerate <= 0) {
            call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'numberToGenerate' partnerId is required")
        }

        try {
            generateMultipleCoupons(
                batchName, numberToGenerate,
                startDate, expirationDate, duration, typeId, productId, partnerId,
                braintreeId, countryId, discountPrice
            )


        } catch (e: SQLIntegrityConstraintViolationException) {
            return call.respondBadRequest(
                ServerErrors.ERROR_BAD_PARAMS,
                ServerErrors.ERROR_BAD_PARAMS.name,
                "" + e.message
            )
        } catch (e: SQLDataException) {
            return call.respondBadRequest(
                ServerErrors.ERROR_BAD_PARAMS,
                ServerErrors.ERROR_BAD_PARAMS.name,
                "" + e.message
            )
        }

        val afterCount = databaseController.countAccountCoupon(batchName)
        amountGenerated = afterCount - beforeCount
        return call.respondOK(
            Success.SUCCESS,
            "Successfully generated multiple coupons: $amountGenerated, total in group '$groupId': '$afterCount', " +
                    "for type: '${AccountCouponType.fromRaw(typeId.toString())}', product: ${
                        AccountCouponProduct.fromRaw(
                            productId.toString()
                        )
                    } "
        )
    }

    if (typeId == AccountCouponType.UNIQUE_COUPON.raw.toInt() ||
        typeId == AccountCouponType.UNIQUE_ACCESS_COUPON.raw.toInt()
    ) {

        if (serialNumber.isBlank()) {
            call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'serialNumber' is required")
        }
        if (redeemAmount == 0) {
            call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'redeemAmount' is required")
        }

        val couponName = utility.getFormattedString(serialNumber)

        try {
            databaseController.insertSingleAccountCoupon(
                batchName, couponName, expirationDate,
                duration, typeId, productId, partnerId, redeemAmount, startDate
            )
        } catch (e: SQLIntegrityConstraintViolationException) {
            return call.respondBadRequest(
                ServerErrors.ERROR_BAD_PARAMS,
                ServerErrors.ERROR_BAD_PARAMS.name,
                "" + e.message
            )
        } catch (e: SQLDataException) {
            return call.respondBadRequest(
                ServerErrors.ERROR_BAD_PARAMS,
                ServerErrors.ERROR_BAD_PARAMS.name,
                "" + e.message
            )
        }

        return call.respondOK(
            Success.SUCCESS, "Successfully generated single coupon in group '$groupId' " +
                    "for type: '${AccountCouponType.fromRaw(typeId.toString())}', product: ${
                        AccountCouponProduct.fromRaw(
                            productId.toString()
                        )
                    } "
        )

    }
}

private fun Api4BackofficeController.generateMultipleCoupons(
    groupId: String, numberToGenerate: Int, startDate: String, expirationDate: String,
    duration: String, typeId: Int, productId: Int, partnerId: Int, braintreeId: String,
    countryId: Int, discountPrice: BigDecimal
) {
    val MAX_COUPONS_TO_GENERATE = 200000
    val MAX_INSERT_PER_BATCH = 100
    val numbers = minOf(numberToGenerate, MAX_COUPONS_TO_GENERATE)

    var i = 0
    while (i < numbers) {
        var toAdd = MAX_INSERT_PER_BATCH
        if (i + toAdd >= numbers) toAdd = numbers - i
        var inner = 0
        while (inner < toAdd) {
            val serialNumber = generateCouponNumber()
            databaseController.insertMultipleAccountCoupon(
                groupId, serialNumber,
                expirationDate, duration, typeId, productId, partnerId, startDate
            )
            if (braintreeId.isNotBlank() && typeId != 0) {
                createDiscountCoupons(braintreeId, countryId, serialNumber, discountPrice);
            }

            inner++
        }
        i += toAdd
    }
}

private fun generateCouponNumber(): String {
    val replaceChars = "[=+/]".toRegex()
    var serialNumber = Base64.getEncoder().encodeToString(Random.nextBytes(64))
    serialNumber = replaceChars.replace(serialNumber, "")
    serialNumber = serialNumber.substring(0, 10).uppercase()
    return serialNumber
}

fun createDiscountCoupons(braintreeId: String, countryId: Int, serialNumber: String, discountPrice: BigDecimal) {

    var discountId: Int = 0
    var couponId: Int = 0
    var checkBraintree: String = ""
    var checkDiscountIdExists: Int = 0
    transaction {
        Discount.select { Discount.discountName eq braintreeId }.limit(1)
            .mapNotNull { row -> checkBraintree = row[Discount.discountName] }.singleOrNull()
        if (checkBraintree.isNullOrBlank()) {
            Discount.insert {
                it[active] = true
                it[discountName] = braintreeId
                it[freeTrial] = true
                it[isPriceDiscount] = true
            }
        }
        Discount.select { Discount.discountName eq braintreeId }.limit(1)
            .mapNotNull { row -> discountId = row[Discount.id] }.singleOrNull()

        AccountCoupons.select { AccountCoupons.couponId eq serialNumber }.limit(1)
            .mapNotNull { row -> couponId = row[AccountCoupons.id].toInt() }.singleOrNull()


        DiscountCoupons.insert {
            it[DiscountCoupons.discountId] = discountId
            it[DiscountCoupons.couponId] = couponId
        }

        DiscountCountry.selectAll().andWhere { DiscountCountry.countryId eq countryId }
            .andWhere { DiscountCountry.discountId eq discountId }.mapNotNull { row ->
                checkDiscountIdExists = row[DiscountCountry.discountId]
            }.singleOrNull()

        if (discountId != checkDiscountIdExists) {
            DiscountCountry.insert {
                it[DiscountCountry.countryId] = countryId
                it[DiscountCountry.discountId] = discountId
                it[DiscountCountry.discountPrice] = discountPrice
            }
        }

    }

}
