package net.kidjo.server.api4.extension.subscription.digitalvirgo

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.*
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.api4.publichers.Api4DigitalVirgoController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request

suspend fun Api4DigitalVirgoController.unsubscribe(call: ApplicationCall) {

    val VIRGO_PARAM_SUBSCRIPTION_ID = "subscription_id"
    val VIRGO_PARAM_CLIENT_LOGIN = "login"
    val VIRGO_PARAM_CLIENT_PASS = "password"

    val okHttpClient = OkHttpClient()

    val user = call.principal<User>()

    if (user == null) {
        logger.error("Failed to find user")
        return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Problems finding User")
    }

    val virgoSub = databaseController.virgo_subscription_getUserById(user.getLongId())
    if (virgoSub == null) {
            call.respondBadRequest("The User doesn't have a virgo subscription")
            return
    }
    val formBody = FormBody.Builder()
            .addEncoded(VIRGO_PARAM_CLIENT_LOGIN, config.virgo_sesame_login)
            .addEncoded(VIRGO_PARAM_CLIENT_PASS, config.virgo_sesame_password)
            .addEncoded(VIRGO_PARAM_SUBSCRIPTION_ID, virgoSub)
            .build()

    val request = Request.Builder()
            .url(config.virgo_unsubcribe_prod_api_url)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .post(formBody)
            .build()

    val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }
    val stringResponse = withContext(Dispatchers.IO) { response.body()?.string() }

    response.close()
    if (response.code() == 200) {

        val existingSubscription = databaseController.subscription_getByToken(virgoSub)
        if(existingSubscription == null) {
            return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Not found kidjo-tv subscription")
        }

        val subId = existingSubscription!!.id
        val subToken = existingSubscription!!.subscriptionToken
        val success = databaseController.subscription_cancelFull(subId)

        if(!success){
            call.respondError(HttpStatusCode.InternalServerError, "A problem occurred while unsubscribe Kidjo-Tv subscription: $subToken")
            return
        }
        call.respondOK(Success.SUCCESS, "Successfully unsubscribe kidjo-tv subscription is: $subToken ")
        return
    } else if(response.code() == 4) {
        call.respond404( "The user is already unsubscribed")
        return
    } else {
        call.respond404( "Unsuccessfully unsubscribed")
        return
    }
}
