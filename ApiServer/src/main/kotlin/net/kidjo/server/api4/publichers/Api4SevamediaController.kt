package net.kidjo.server.api4.publichers

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.subscription.sevamedia.create
import net.kidjo.server.api4.extension.subscription.sevamedia.subscriptionInfo
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api4SevamediaController (platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4SevamediaController")

    override fun installRoutes(route: Route) {
        route.post("/sevamedia/subscription") { <EMAIL>(call) }
        route.get("/sevamedia/subscription") { <EMAIL>(call) }
    }
}
