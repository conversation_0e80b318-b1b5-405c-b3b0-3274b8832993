package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.routing.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.getForceUpdateVersion
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.extensions.respondBadParameters
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK
import org.slf4j.LoggerFactory

class Api4ApkForceUpdateController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4ApkForceUpdateController")

    override fun installRoutes(route: Route) {
        route.get("/force-update") { <EMAIL>(call) }
    }

    suspend fun forceUpdate(call: ApplicationCall) {

        val appName = call.parameters.getString("appName")
        val appModule = call.parameters.getString("appModule")
        val appOS = call.parameters.getString("appOS")
        val appVersion = call.parameters.getString("appVersion")

        if(appName.isNullOrBlank() || appModule.isNullOrBlank() ||
                appOS.isNullOrBlank() || appVersion.isNullOrEmpty()){
            return call.respondBadParameters()
        }

        val forceUpdate = databaseController.getForceUpdateVersion(appName, appModule, appOS)
                ?: return call.respondError(HttpStatusCode.BadRequest, "There is no record")

        if (appVersion.toFloat() >= forceUpdate.mandatoryVersion &&
                appVersion.toFloat() < forceUpdate.optionalVersion) {
            return call.respondOK(ForceUpdateDTO(false, false))
        }

        if (appVersion.toFloat() < forceUpdate.mandatoryVersion) {
            return  call.respondOK(ForceUpdateDTO(false, true))
        }

        if (appVersion.toFloat() >= forceUpdate.optionalVersion) {
            return  call.respondOK(ForceUpdateDTO(true, false))
        }
    }
}

data class ForceUpdateDTO(val upToDate: Boolean, val forceUpdate: Boolean)
