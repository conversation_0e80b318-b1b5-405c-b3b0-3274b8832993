package net.kidjo.server.api4.extension.backoffice

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respond
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.extensions.receiveJSON
import net.kidjo.server.shared.extensions.respondBadJSON
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.models.entity.AccountCoupons
import net.kidjo.server.utils.checkDateFormat
import net.kidjo.server.utils.convertStringToDate
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update

suspend fun Api4BackofficeController.updateCoupon(call: ApplicationCall) {

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val productId = requestJSON.getString("productId")?.takeIf { it.isNotEmpty() }?.toInt()
        ?: return call.respondBadRequest("productId is required");
    val groupId = requestJSON.getString("groupId")?.takeIf { it.isNotEmpty() }?.toString()
        ?: return call.respondBadRequest("groupId is required");
    val expirationDate = requestJSON.getString("expirationDate")?.takeIf { it.isNotEmpty() }?.toString()
        ?: return call.respondBadRequest("expirationDate is required");
    val duration = requestJSON.getString("duration")?.takeIf { it.isNotEmpty() }?.toString()
        ?: return call.respondBadRequest("duration is required");
    if (!checkDateFormat(expirationDate))
        return call.respondBadRequest("expirationDate format is wrong: Follow yyyy-MM-dd'T'HH:mm:ss")

    if (updateCoupon(productId, groupId, expirationDate, duration) != 0) {
        call.respond(HttpStatusCode.OK, "Coupon updated successfully!!!")
    } else
        call.respond(HttpStatusCode.BadRequest, "Coupon update failed")
}

fun updateCoupon(productId: Int?, groupId: String?, expirationDate: String?, duration: String?): Int {

    return transaction {
        AccountCoupons.update({ AccountCoupons.groupId eq groupId.toString() }) {
            it[AccountCoupons.duration] = duration.toString()
            if (productId != null) {
                it[product] = productId.toLong()
            }
            it[expireDate] = convertStringToDate(expirationDate.toString())
        }
    }
}