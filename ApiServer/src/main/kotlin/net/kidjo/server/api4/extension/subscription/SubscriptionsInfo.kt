package net.kidjo.server.api4.extension.subscription

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4SubscriptionController
import net.kidjo.server.api4.extension.coupon.CouponDTO
import net.kidjo.server.shared.database.accountCoupon_get
import net.kidjo.server.shared.database.getDVSubscriptionListByIdOrUserAlias
import net.kidjo.server.shared.extensions.getString
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.Device


suspend fun Api4SubscriptionController.subscriptionsInfo(call: ApplicationCall) {

    val dvUserAlias = call.parameters.getString("userAlias")

    try {
        val subscriptions = databaseController.getDVSubscriptionListByIdOrUserAlias(dvUserAlias)
        val subscriptionsDto = ArrayList<SubscriptionDTO>()

        subscriptions.forEach { it ->
            var coupon: CouponDTO? = null
            val iap = iapManager.getIAP(it.storeId, it.iapId)

            if (it.accountCouponId > 0L && it.stillInFreeTrial) {
                val accCoupon = databaseController.accountCoupon_get(it.accountCouponId)
                coupon = CouponDTO(
                    accCoupon?.id,
                    accCoupon?.couponId,
                    accCoupon?.durationCode,
                    accCoupon?.groupId,
                    accCoupon?.couponType,
                    accCoupon?.productType,
                    accCoupon?.partnerType
                )
            }

            subscriptionsDto.add(
                SubscriptionDTO(
                id = it.id.toString(),
                nextBillingDate = it.nextBillDate.toString(),
                coupon = coupon,
                durationCode = iap?.durationCode,
                platformPurchaseId = it.platformPurchaseId,
                paymentMethod = if (it.storeId.raw == Device.StorePlatform.FREE_ACCESS_COUPON.raw ) "free" else it.paymentId,
                paymentPlanId = iap?.id,
                paymentPlanPrice = iap?.price,
                paymentStateId = it.paymentStateId,
                isRenewing = it.isRenewing,
                storeId = it.storeId.raw,
                subscriptionType =  it.subscriptionType.raw,
                isExpired = utility.isBillingDateExpired(it.nextBillDate)
            )
            )
        }
        return call.respondOK(
            SubscriptionInfoDTO(
                subscriptionsDto
            )
        )
    } catch (e: Throwable) {
        logger.error("Failed to find Subscription", e)
        return call.respondBadRequest("Problems finding Subscription")
    }
}

data class SubscriptionInfoDTO(
    var subscriptions: ArrayList<SubscriptionDTO>
)

data class SubscriptionDTO(var id: String?,
                           var nextBillingDate: String?,
                           var coupon: CouponDTO?,
                           var durationCode: String?,
                           val platformPurchaseId: String?,
                           var paymentMethod: String?,
                           var paymentPlanId: String?,
                           var paymentPlanPrice: Double?,
                           var paymentStateId: String?,
                           var isRenewing: Boolean?,
                           var storeId: String?,
                           var subscriptionType: String?,
                           var isExpired: Boolean)
