package net.kidjo.server.api4.extension.subscription.logicom

import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.*
import net.kidjo.server.api4.publichers.Api4LogicomController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import java.time.LocalDateTime

suspend fun Api4LogicomController.check(call: ApplicationCall) {

    val query = call.parameters
    val serialNumber = query.getString("serialNumber")
    val macAddress = query.getString("macAddress")

    // Check last Logicom coupon
    val lastLogicomCoupon = databaseController.logicomGetFirstWithRemaining()
    if (lastLogicomCoupon == null || LocalDateTime.now().isAfter(lastLogicomCoupon.validUntil)) {
        logger.debug("checkLogicom() | there is no Logicom coupon available")
        return call.respondBadRequest(SubscriptionErrors.ERROR_CONFLICT_SUBSCRIPTION, "No logicom coupon available")
    }

    // Check device serial number or mac address
    val logicomSerialBundle = databaseController.logicomV4Check(serialNumber, macAddress)
    if (!logicomSerialBundle.check) {
        logger.debug("checkLogicom() | device serial number '$serialNumber' or mac address '$macAddress' is not valid")
        return call.respondBadRequest(SubscriptionErrors.ERROR_NOT_EXIST_SUBSCRIPTION, "Invalid device serial number or mac address")
    }

    return call.respond(
        Api4LogicomController.LogicomCheckResponse(
            logicomSerialBundle.check,
            lastLogicomCoupon.value
        )
    )
}
