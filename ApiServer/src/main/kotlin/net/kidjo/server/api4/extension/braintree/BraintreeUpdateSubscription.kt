package net.kidjo.server.api4.extension.braintree

import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.http.*
import net.kidjo.server.api4.Api4BraintreeController
import net.kidjo.server.shared.database.subscription_getById
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.InAppPurchase
import net.kidjo.server.shared.models.InAppPurchaseType
import net.kidjo.server.shared.models.User

suspend fun Api4BraintreeController.braintreeUpdateSubscription(call: ApplicationCall) {
    val user = call.principal<User>()
    if (user == null) {
        call.respondError(HttpStatusCode.BadRequest, "There is no such a user")
        return
    }

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val id = requestJSON.optString("id")
    val iap = requestJSON.optString("iap")

    if (!requestJSON.has("id") || !requestJSON.has("iap")) {
        call.respondBadParameters()
        return
    }

    val discountSub = databaseController.subscription_getById(user.getLongId(), id)

    return if (discountSub != null) {
        var iAppPurchasToUpdate = getIapPlan(iap)

        if (iAppPurchasToUpdate == null) {
            return call.respondNotFound(UsersErrors.ERROR_EXIST_USER, "Problem finding IAP")
        }
        braintreeManager.updateBraintreeSubscription(user, discountSub, iAppPurchasToUpdate, call)
    } else {
        return  call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"Problems finding Subscription")
    }
}

private fun Api4BraintreeController.getIapPlan(iap: String?) : InAppPurchase? {
    var iAppPurchase: InAppPurchase? = null
    when(iap) {
        InAppPurchaseType.KIDJO_BOOKS_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.KIDJO_BOOKS_MONTHLY.raw)
        }
        InAppPurchaseType.KIDJO_TV_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.KIDJO_TV_YEARLY.raw)
        }
        InAppPurchaseType.KIDJO_TV_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.KIDJO_TV_MONTHLY.raw)
        }
        InAppPurchaseType.KIDJO_BOOKS_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.KIDJO_BOOKS_YEARLY.raw)
        }

        InAppPurchaseType.KIDJO_GAMES_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.KIDJO_BOOKS_MONTHLY.raw)
        }
        InAppPurchaseType.KIDJO_GAMES_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.KIDJO_GAMES_YEARLY.raw)
        }

        InAppPurchaseType.SWISS_KIDJO_BOOKS_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.SWISS_KIDJO_BOOKS_MONTHLY.raw)
        }
        InAppPurchaseType.SWISS_KIDJO_TV_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.SWISS_KIDJO_TV_YEARLY.raw)
        }
        InAppPurchaseType.SWISS_KIDJO_TV_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.SWISS_KIDJO_TV_MONTHLY.raw)
        }

        InAppPurchaseType.SWISS_KIDJO_GAMES_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.SWISS_KIDJO_GAMES_MONTHLY.raw)
        }
        InAppPurchaseType.SWISS_KIDJO_GAMES_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.SWISS_KIDJO_GAMES_YEARLY.raw)
        }

        InAppPurchaseType.EUROPE_KIDJO_BOOKS_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.EUROPE_KIDJO_BOOKS_MONTHLY.raw)
        }
        InAppPurchaseType.EUROPE_KIDJO_TV_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.EUROPE_KIDJO_TV_YEARLY.raw)
        }
        InAppPurchaseType.EUROPE_KIDJO_TV_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.EUROPE_KIDJO_TV_MONTHLY.raw)
        }
        InAppPurchaseType.EUROPE_KIDJO_BOOKS_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.EUROPE_KIDJO_BOOKS_YEARLY.raw)
        }

        InAppPurchaseType.EUROPE_KIDJO_GAMES_MONTHLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.EUROPE_KIDJO_GAMES_MONTHLY.raw)
        }
        InAppPurchaseType.EUROPE_KIDJO_GAMES_YEARLY_DISCOUNT.raw -> {
            iAppPurchase = iapManager.getKidjoBrainTreeIAP(InAppPurchaseType.EUROPE_KIDJO_GAMES_YEARLY.raw)
        }
    }
    return iAppPurchase
}

