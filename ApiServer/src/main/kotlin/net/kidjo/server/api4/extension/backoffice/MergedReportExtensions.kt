package net.kidjo.server.api4.extension.backoffice

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.respond
import io.ktor.server.response.respondFile
import net.kidjo.common.models.UserReportType
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.getAccountById
import net.kidjo.server.shared.database.getUserById
import net.kidjo.server.shared.database.searchAccounts
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.UsersErrors
import net.kidjo.server.shared.extensions.getParameterOrDefaultValue
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondNotFound
import net.kidjo.server.shared.extensions.respondOK
import java.io.File
import java.io.PrintWriter
import java.sql.SQLException

suspend fun Api4BackofficeController.searchAccounts(call: ApplicationCall) {

    val limit = call.getParameterOrDefaultValue("limit", 20)!!.toInt()
    val offset = call.getParameterOrDefaultValue("offset", 0)!!.toInt()
    val fromDate = call.getParameterOrDefaultValue("fromDate")
    val toDate = call.getParameterOrDefaultValue("toDate")
    val search = call.getParameterOrDefaultValue("search")
    val countryCode = call.getParameterOrDefaultValue("countryCode")
    val subStatus = call.getParameterOrDefaultValue("subStatus")
    val subMethod = call.getParameterOrDefaultValue("subMethod")
    val subType = call.getParameterOrDefaultValue("subType")
    val couponType = call.getParameterOrDefaultValue("couponType") ?: "0"
    val partnerId = call.getParameterOrDefaultValue("partnerId")?.toLong()
    val sortColumns = call.getParameterOrDefaultValue("sortColumns", "createdDate")?.split(",")?.toList()
    val sortColumnOrders = call.getParameterOrDefaultValue("sortColumnOrders", "desc")?.split(",")?.toList()
    val promoContent = call.getParameterOrDefaultValue("promoContent")?.toInt()
    val inCountries = call.getParameterOrDefaultValue("inCountries")
    val reportType = call.getParameterOrDefaultValue("reportType") ?: UserReportType.ALL_USERS.value

    if(UserReportType.entries.map { it.value }.contains(reportType).not())
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Invalid report type!")

    if (inCountries.isNullOrEmpty().not() && inCountries?.split(",")?.firstOrNull { it.length != 2 || it.firstOrNull {char -> !char.isLetter() } != null } != null)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Invalid country code!")

    if (!search.isNullOrBlank() && search.length < 3)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Minimum 3 characters !")

    return try {
        val (size, accounts) = databaseController.searchAccounts(
            limit = limit,
            offset = offset,
            search = search?.trim(),
            countryCode = countryCode,
            fromDate = fromDate,
            toDate = toDate,
            subType = subType,
            subMethod = subMethod,
            subStatus = subStatus,
            couponType = couponType,
            partnerId = partnerId,
            sortColumns = sortColumns,
            sortColumnOrders = sortColumnOrders,
            promoContent = promoContent,
            reportType = reportType,
            inCountries = inCountries?.split(",")?.map { it.uppercase() }
        )
        call.respondOK(mapOf("size" to size, "accounts" to accounts))
    } catch (e: SQLException) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, e.localizedMessage)
    }
}

suspend fun Api4BackofficeController.getAccountDetails(call: ApplicationCall) {
    val accountId = call.parameters["accountId"] ?: return call.respondBadRequest(
        ServerErrors.ERROR_BAD_PARAMS,
        "The 'accountId' parameter is required"
    )
    return try {
        val account = databaseController.getAccountById(accountId)
            ?: return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "Account not found")

        call.respondOK(account)
    } catch (e: Throwable) {
        return call.respond(HttpStatusCode.InternalServerError, "Error while finding account details")
    }
}

suspend fun Api4BackofficeController.getUserDetails(call: ApplicationCall) {
    val userId = call.parameters["userId"] ?: return call.respondBadRequest(
        ServerErrors.ERROR_BAD_PARAMS,
        "The 'userId' parameter is required"
    )
    return try {
        val user = databaseController.getUserById(userId)
            ?: return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER, "User not found")

        call.respondOK(user)
    } catch (e: Throwable) {
        return call.respond(HttpStatusCode.InternalServerError, "Error while finding user details")
    }
}

suspend fun Api4BackofficeController.downloadUserCSV(call: ApplicationCall) {

    val limit = call.getParameterOrDefaultValue("limit", 0)!!.toInt()
    val offset = call.getParameterOrDefaultValue("offset", 0)!!.toInt()
    val fromDate = call.getParameterOrDefaultValue("fromDate")
    val toDate = call.getParameterOrDefaultValue("toDate")
    val search = call.getParameterOrDefaultValue("search")
    val countryCode = call.getParameterOrDefaultValue("countryCode")
    val subStatus = call.getParameterOrDefaultValue("subStatus")
    val subMethod = call.getParameterOrDefaultValue("subMethod")
    val subType = call.getParameterOrDefaultValue("subType")
    val couponType = call.getParameterOrDefaultValue("couponType") ?: "0"
    val partnerId = call.getParameterOrDefaultValue("partnerId")?.toLong()
    val sortColumns = call.getParameterOrDefaultValue("sortColumns", "createdDate")?.split(",")?.toList()
    val sortColumnOrders = call.getParameterOrDefaultValue("sortColumnOrders", "desc")?.split(",")?.toList()
    val promoContent = call.getParameterOrDefaultValue("promoContent")?.toInt()
    val inCountries = call.getParameterOrDefaultValue("inCountries")
    val reportType = call.getParameterOrDefaultValue("reportType") ?: UserReportType.ALL_USERS.value

    if (!search.isNullOrBlank() && search.length < 3)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Minimum 3 characters !")

    return try {
        val (size, accounts) = databaseController.searchAccounts(
            limit = limit,
            offset = offset,
            search = search?.trim(),
            countryCode = countryCode,
            fromDate = fromDate,
            toDate = toDate,
            subType = subType,
            subMethod = subMethod,
            subStatus = subStatus,
            couponType = couponType,
            partnerId = partnerId,
            sortColumns = sortColumns,
            sortColumnOrders = sortColumnOrders,
            promoContent = promoContent,
            reportType = reportType,
            inCountries = inCountries?.split(",")?.toList()
        )
        val csvFile = File("users.csv")
        val writer = PrintWriter(csvFile)
        val builder = StringBuilder()
        builder.append(" First Name, Email, Subscription Date, Coupon Type, Country, Product type, Subscription Method, Promo Accepted, Terms and conditions Accepted")
        builder.append("\n\n")
        accounts.forEach { account ->
            val csvLine =
                "${account.name},${account.email},${account.subStartDate},${account.couponType} ,${account.country},${account.subType},${account.subMethod},${account.isPromoAccepted},${account.isTcAccepted}"
            builder.append(csvLine)
            builder.append("\n")
        }
        writer.write(builder.toString())
        writer.close()
        call.respondFile(csvFile)
        csvFile.delete()
        call.respondOK(mapOf("size" to size, "accounts" to accounts))
    } catch (e: SQLException) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, e.localizedMessage)
    }
}