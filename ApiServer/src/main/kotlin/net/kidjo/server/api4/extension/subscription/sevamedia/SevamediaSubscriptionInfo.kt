package net.kidjo.server.api4.extension.subscription.sevamedia

import io.ktor.server.application.*
import net.kidjo.server.api4.publichers.Api4SevamediaController
import net.kidjo.server.shared.database.subscription_getByCouponId
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4SevamediaController.subscriptionInfo(call: ApplicationCall) {
    val couponId = call.parameters["coupon_code"]
    if(couponId.isNullOrEmpty()){
        return  call.respondBadRequest(
            ServerErrors.ERROR_BAD_PARAMS,
            ServerErrors.ERROR_BAD_PARAMS.name,
            "The 'couponCode' parameter is required"
        )
    }
    val subscriptionInfo = databaseController.subscription_getByCouponId(couponId)
    return call.respondOK(mapOf("subscription" to subscriptionInfo))
}
