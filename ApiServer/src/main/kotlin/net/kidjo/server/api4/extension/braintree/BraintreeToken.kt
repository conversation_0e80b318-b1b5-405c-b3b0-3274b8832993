package net.kidjo.server.api4.extension.braintree

import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.principal
import net.kidjo.server.api4.Api4BraintreeController
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.models.*

suspend fun Api4BraintreeController.generateToken(call: ApplicationCall) {
    var user = call.principal<User>()
    if (user == null) {
        logger.error("Failed to find user")
        return call.respondBadRequest("Problems finding User")
    }
    var token: String? = null
    if(user.brainTreeId == "NOT SET"){
        braintreeManager.createCustomer(user)
        user = databaseController.user_getByEmail(user.email)
        if (user == null) {
            logger.error("Failed to find user")
            return call.respondBadRequest("Problems finding User")
        }
    }
    token = braintreeManager.createClientToken(user)

    if(token.isNullOrBlank() || token == "xxToken") {
       return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problem generate client token.")
    }
    return call.respondOK(token)
}
