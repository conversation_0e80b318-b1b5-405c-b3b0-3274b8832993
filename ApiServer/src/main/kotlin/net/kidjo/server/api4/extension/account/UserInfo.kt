package net.kidjo.server.api4.extension.account

import io.ktor.server.application.*
import io.ktor.server.auth.*
import net.kidjo.server.api4.Api4AccountController
import net.kidjo.server.api4.extension.subscription.SubscriptionDTO
import net.kidjo.server.api4.extension.subscription.digitalvirgo.TEMPORARY_ACCOUNT_EMAIL_SUFFIX
import net.kidjo.server.shared.database.accountCoupon_get
import net.kidjo.server.shared.database.getRecentBOOKSubscription
import net.kidjo.server.shared.database.getRecentGAMESubscription
import net.kidjo.server.shared.database.getRecentTVSubscription
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK
import net.kidjo.server.shared.extensions.toEpochMilli
import net.kidjo.server.shared.models.AccountCoupon
import net.kidjo.server.shared.models.SubscriptionRoot
import net.kidjo.server.shared.models.User
import java.time.LocalDateTime

suspend fun Api4AccountController.tvUserInfo(call: ApplicationCall) {
     val tvUser = call.principal<User>() ?: return call.respondBadRequest("Problems finding TV User PRINCIPAL")
    return try {
        val tvSubscription = databaseController.getRecentTVSubscription(tvUser.getLongId(),true)
        val tvUserDto = getUerDTO(tvUser, tvSubscription)
        call.respondOK(mapOf("user" to tvUserDto))

    } catch (e: Throwable) {
        logger.error("Failed to TV find Subscription", e)
        call.respondBadRequest("Problems finding TV Subscription")
    }
}
suspend fun Api4AccountController.bookUserInfo(call: ApplicationCall) {
    val bookUser = call.principal<User>()?: return call.respondBadRequest("Problems finding BOOK User PRINCIPAL")
    return try {
        val bookSubscription = databaseController.getRecentBOOKSubscription(bookUser.getLongId(),true)
        val bookUserDto = getUerDTO(bookUser, bookSubscription)
        call.respondOK(mapOf("user" to bookUserDto))

    } catch (e: Throwable) {
        logger.error("Failed to BOOK find Subscription", e)
        call.respondBadRequest("Problems finding BOOK Subscription")
    }
}
suspend fun Api4AccountController.gameUserInfo(call: ApplicationCall) {
    val gameUser = call.principal<User>()?:
        return call.respondBadRequest("Problems finding GAME User PRINCIPAL")

    return try {
        val gameSubscription = databaseController.getRecentGAMESubscription(gameUser.getLongId(), true)
        val gameUserDto = getUerDTO(gameUser, gameSubscription)
        call.respondOK(mapOf("user" to gameUserDto))

    } catch (e: Throwable) {
        logger.error("Failed to find GAME Subscription", e)
        call.respondBadRequest("Problems finding GAME Subscription")
    }
}

private fun Api4AccountController.getUerDTO(user: User, subscription: SubscriptionRoot?): UserDTO {
    val durationCode = getAccountCoupon(subscription)?.durationCode
    return createUserDto(
        user = user,
        expirationDateToMilli = getExpirationInMilliseconds(subscription?.nextBillDate),
        subscriptionDto = crateSubscriptionDto(subscription, durationCode),
        durationCode = durationCode
    )
}

private fun createUserDto(
    user: User,
    expirationDateToMilli: Long?,
    subscriptionDto: SubscriptionDTO?,
    durationCode: String?
): UserDTO  = UserDTO(
        id = user.id,
        name = user.name,
        email = user.email,
        expirationDate = expirationDateToMilli,
        subscription = subscriptionDto,
        couponDurationCode = durationCode,
        isEmailFake = user.email.contains(TEMPORARY_ACCOUNT_EMAIL_SUFFIX, ignoreCase = true)
    )
private fun Api4AccountController.crateSubscriptionDto(
    subscription: SubscriptionRoot?,
    durationCode: String?
): SubscriptionDTO? =
    if(subscription != null)
        SubscriptionDTO(
            id = subscription.id.toString(),
            nextBillingDate = subscription.nextBillDate.toString(),
            coupon = null,
            durationCode = durationCode,
            platformPurchaseId = subscription.platformPurchaseId,
            paymentMethod = null,
            paymentPlanId = null,
            paymentPlanPrice = null,
            paymentStateId = subscription.paymentStateId,
            isRenewing = subscription.isRenewing,
            storeId = subscription.storeId.raw,
            subscriptionType = subscription.subscriptionType.raw,
            isExpired = utility.isBillingDateExpired(subscription.nextBillDate)
        )
    else null
private fun Api4AccountController.getAccountCoupon(subscription: SubscriptionRoot?): AccountCoupon? =
    if (subscription !=null &&
        subscription.accountCouponId > 0L &&
        subscription.stillInFreeTrial)
          databaseController.accountCoupon_get(subscription.accountCouponId)
    else null
data class UserDTO(var id: String,
                   var name: String,
                   val email: String,
                   val expirationDate: Long?,
                   val subscription: SubscriptionDTO?,
                   val couponDurationCode: String?,
                   val isEmailFake: Boolean = false)
private fun getExpirationInMilliseconds(nextBillDate: LocalDateTime?): Long? = nextBillDate?.toEpochMilli()
