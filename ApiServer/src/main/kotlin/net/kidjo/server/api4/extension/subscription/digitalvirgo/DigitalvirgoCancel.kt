package net.kidjo.server.api4.extension.subscription.digitalvirgo

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.request.*
import net.kidjo.server.api4.publichers.Api4DigitalVirgoController
import net.kidjo.server.shared.extensions.*

suspend fun Api4DigitalVirgoController.cancel(call: ApplicationCall) {

    val requestFormParameters = call.receiveParameters()

    val event = requestFormParameters["event"]
    val subscriptionId = requestFormParameters["subscription_id"]

    if (event.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'event' parameter is required")
        return
    }

    if (subscriptionId.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'subscriptionId' parameter is required")
        return
    }

    val result = cancelSubscription(event, subscriptionId)

        if(result == -2){
            call.respondError(HttpStatusCode.InternalServerError, "A problem occurred while unsubscribe Kidho-Tv subscription ID: $subscriptionId")
            return
        }
        if(result == -3){
            call.respondError(HttpStatusCode.InternalServerError, "A problem occurred while delete linked user: $subscriptionId")
            return
        }
        if(result == 1){
            call.respondOK(Success.SUCCESS, "Successfully unsubscribe kidjo-tv subscription: $subscriptionId ")
            return
        }

    if (event == VIRGO_EVENT_MONITORING) {
        call.respondOK(Success.SUCCESS, "The Kidjo Server is working. Cancel event")
        return
    }
}
