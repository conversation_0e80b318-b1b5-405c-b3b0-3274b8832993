package net.kidjo.server.api4.publichers

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.subscription.logicom.subscriptionSet
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api4LogicomController (platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    data class LogicomCheckResponse(val check: <PERSON><PERSON><PERSON>, val couponId: String)
    internal val logger = LoggerFactory.getLogger("Api4LogicomController")
    override fun installRoutes(route: Route) {
        route.post("/logicom/subscriptions") { <EMAIL>(call) }
    }
}
