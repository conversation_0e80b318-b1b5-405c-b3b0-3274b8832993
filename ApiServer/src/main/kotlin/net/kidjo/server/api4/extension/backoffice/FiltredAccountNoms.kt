package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.filteredSearchAccount
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.getParameterOrDefaultValue
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.filteredSearchAccount(call: ApplicationCall) {

    //all columns consideration
    /*val fromDate = call.getParameterOrDefaultValue("fromDate")
    val toDate = call.getParameterOrDefaultValue("toDate")
    val couponType = call.getParameterOrDefaultValue("couponType")
    val subMethod = call.getParameterOrDefaultValue("subMethod")
    val subStatus = call.getParameterOrDefaultValue("subStatus")
    val countryCode = call.getParameterOrDefaultValue("countryCode")
    val productType = call.getParameterOrDefaultValue("productType")
    val partnerId = call.getParameterOrDefaultValue("partnerId")?.toLong()
    val promoContent = call.getParameterOrDefaultValue("promoContent")?.toInt()
    val reportType = call.getParameterOrDefaultValue("reportType")*/

    try {
        val filteredSearch = databaseController.filteredSearchAccount() ?: return call.respond(HttpStatusCode.NoContent)

        return call.respondOK(filteredSearch)
    } catch (e: Throwable) {
        print(e.printStackTrace())
        return call.respondError(
            statusCode = HttpStatusCode.InternalServerError,
            code = ServerErrors.INTERNAL_ERROR.code,
            errorMessage = e.localizedMessage
        )
    }
}
