package net.kidjo.server.api4.extension.videos

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.server.api4.Api4VideoController
import net.kidjo.server.main.BaseServerController.Companion.logger
import net.kidjo.server.models.Folder
import net.kidjo.server.models.Format
import net.kidjo.server.models.Video
import net.kidjo.server.shared.models.Kid
import net.kidjo.server.shared.models.Partner
import net.kidjo.server.shared.models.Video.Format.Companion.GetVideoUrlWithFormat
import net.kidjo.server.shared.models.entity.*
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.RequestUtil
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction

const val DEFAULT_LIMIT = 10

data class FilterParams(
    val limit: Int,
    val offset: Int,
    val countryId: Int?,
    val age: Int?,
    val search: String?,
    val language: Int?,
    val category: net.kidjo.common.models.Folder.ContentType?,
)

suspend fun Api4VideoController.getVideos(call: ApplicationCall) {
    val partner = call.principal<Partner>()
    logger.debug("Partner - ${partner?.name}")
    val countryId: Int?

    val limit = call.request.queryParameters["limit"].takeIf { !it.isNullOrEmpty() }?.toInt() ?: DEFAULT_LIMIT
    val offset = call.request.queryParameters["offset"].takeIf { !it.isNullOrEmpty() }?.toInt() ?: 0
    val age = call.request.queryParameters["age"]
        .takeIf { !it.isNullOrEmpty() }
        ?.toInt()
        ?: Kid.DEFAULT_AGE
    val language = call.request.queryParameters["languageCode"]
        .takeIf { !it.isNullOrEmpty() }
        ?.let {
            languageCache.getLanguageIdFromAcceptLanguageHeader(it)
        } ?: RequestUtil.getCountryFromHeader(
        databaseController,
        call.request.acceptLanguage()
    ) ?: 1
    val countryCode = call.request.queryParameters["country"] ?: ""
    countryId = if (countryCode.isEmpty() || countryCode.isBlank()) {
        countryCodeByGoogle.getCountryIdByIP(call)
    } else {
        RequestUtil.getCountryFromCode(databaseController, countryCode)
    }
    val title = call.request.queryParameters["search"].takeIf { !it.isNullOrEmpty() }

    val category = call.request.queryParameters["category"].takeIf { !it.isNullOrEmpty() }


    val filters = FilterParams(
        limit = limit,
        offset = offset,
        age = age,
        language = language,
        countryId = countryId,
        search = title,
        category = category?.lowercase()?.let { net.kidjo.common.models.Folder.ContentType.FromRaw(it) }
    )
    val folders = mutableListOf<Folder>()
    folders.addAll(getContents(filters))
    call.respond(HttpStatusCode.OK, folders)
}

fun Api4VideoController.getContents(filters: FilterParams): List<Folder> {
    var folderUrl=config.folderUrl
    if(config.env == Config.Env.PROD){
        folderUrl=config.prodFolderUrl
    }
    val query = Folders.join(
        FoldersCountries,
        JoinType.INNER, FoldersCountries.folderId, Folders.id
    ).join(FoldersVideos, JoinType.INNER, FoldersVideos.folderId, Folders.id)
        .join(Videos, JoinType.INNER, FoldersVideos.videoId, Videos.id)
        .select {
            Folders.isActive.eq(true) and
                    Folders.mediaType.eq(net.kidjo.common.models.Folder.MediaType.VIDEO)
        }

    filters.countryId?.let {
        query.andWhere {
            FoldersCountries.countryId inList listOf(0, it)
        }
    }

    filters.language?.let {
        query.andWhere {
            Folders.languageId inList listOf(0, it)
        }
    }

    filters.age?.let {
        query.andWhere {
            Folders.ageMin.lessEq(it)
        }

        query.andWhere {
            Folders.ageMax.greaterEq(it)
        }
    }

    filters.search?.let {
        query.andWhere {
            Folders.title.like("$it%") or
                    Videos.title.like("$it%")
        }
    }

    filters.category?.let {
        query.andWhere { Folders.type.eq(filters.category) }
    }

    return transaction {
        var order = 1
        query.groupBy(Folders.id)
        query.limit(filters.limit, if (!filters.search.isNullOrEmpty()) 0 else filters.offset.toLong())
            .orderBy(Folders.order)
            .orderBy(Folders.title)
            .map {
                val videos = mutableListOf<Video>()
                videos.addAll(getVideos(it[Folders.id]))
                Folder(
                    id = it[Folders.id],
                    title = it[Folders.title],
                    languageShortCode = languageCache.getLanguageCodeFromId(it[Folders.languageId]),
                    description = it[Folders.description],
                    order = order++,

                    coverImage = "${folderUrl}alteoxFolder/tablet-l/${it[Folders.id]}.png",
                    category = it[Folders.type],
                    videos = videos
                )
            }.toList()

    }
}

private fun Api4VideoController.getVideos(folderId: Int): List<Video> {
    var folderThumbnailUrl=config.folderThumbnail
    if(config.env==Config.Env.PROD){
        folderThumbnailUrl=config.prodFolderThumbnail
    }
    return transaction {
        var order = 1
        FoldersVideos.join(
            Videos,
            JoinType.INNER,
            FoldersVideos.videoId,
            Videos.id
        ).slice(
            Videos.id,
            Videos.title,
            Videos.ageMax,
            Videos.ageMin,
            Videos.ageMax,
            Videos.duration,
            Videos.compile,
            Videos.isPremium,
            Videos.languageId
        )
            .select {
                FoldersVideos.folderId eq folderId and
                        Videos.isActive
            }
            .orderBy(FoldersVideos.order)
            .map {
                val videoId = encryptionController.encodeVideoId(it[Videos.id].toLong())
                Video(
                    id = videoId,
                    title = it[Videos.title],
                    type = "video",
                    minAge = it[Videos.ageMin],
                    maxAge = it[Videos.ageMax],
                    isPremium = it[Videos.isPremium],
                    thumbnail = "${folderThumbnailUrl}1080/$videoId.png",
                    language = languageCache.getLanguageCodeFromId(it[Videos.languageId]),
                    order = order++,
                    duration = it[Videos.duration],
                    formats = getFormats(it[Videos.id], videoId),
                )
            }.toList()
    }
}

private fun Api4VideoController.getFormats(videoId: Int, encryptedVideoId: String): List<Format> {
    return transaction {
        VideosFormats.select { VideosFormats.videoId.eq(videoId) }
            .filter { it[VideosFormats.fileSize] > 0 }
            .map {
                val format = net.kidjo.server.shared.models.Video.Format.FromId(it[VideosFormats.formatId])
                Format(
                    fileSize = it[VideosFormats.fileSize],
                    href = config.videoUrl.plus(GetVideoUrlWithFormat(encryptedVideoId, format)),
                    height = format.height,
                )
            }.toList()
    }
}
