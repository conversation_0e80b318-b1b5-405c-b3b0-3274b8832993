package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.insertAccountCouponPartner
import net.kidjo.server.shared.extensions.*

suspend fun Api4BackofficeController.createPartner(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val name = requestJSON.optString("name")

    if (name.isNullOrBlank()) {
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'name' parameter is required")
    }

    try {
        databaseController.insertAccountCouponPartner(name)
        call.respondOK("Successful created partner: $name")
    } catch (e: Throwable) {
        logger.error("Failed to create partner", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems creating partner")
    }
}
