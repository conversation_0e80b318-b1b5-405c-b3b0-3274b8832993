package net.kidjo.server.api4.extension.account.user

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.api5.extension.subscription.isMondiaUser
import net.kidjo.server.shared.database.delete_user_redirect_key
import net.kidjo.server.shared.database.getUserRedirectKey
import net.kidjo.server.shared.extensions.*

suspend fun Api4UserController.verifyKey(call: ApplicationCall) {

     val redirectKey = call.parameters["redirectKey"]
     try {
          val userId = databaseController.getUserRedirectKey(redirectKey!!)
          if(userId <= 0){
               return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"There is no such a User")
          }
          if(!isMondiaUser(userId.toInt())) {
               databaseController.delete_user_redirect_key(userId)
          }
          val jwt =  userManager.generateAndSetUpUserAccessToken(userId)

          return call.respondOK(mapOf("token" to jwt,
                  "successMessage" to "Successful generated token."))

     } catch (e: Throwable) {
          logger.error("Failed to delete redirect key", e)
          return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"Problems deleting redirect key")
     }
}
