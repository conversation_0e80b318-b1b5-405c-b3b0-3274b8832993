package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.backoffice.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api4BackofficeController(platformInjector: PlatformInjector) : BaseServerController(platformInjector) {
    internal val logger = LoggerFactory.getLogger("Api4BackofficeController")

    override fun installRoutes(route: Route) {
        route.get("backoffice/report/filter/accounts") { <EMAIL>(call) }
        route.get("backoffice/report/accounts") { <EMAIL>(call) }
        route.get("backoffice/report/account") { <EMAIL>(call) }
        route.get("backoffice/report/user") { <EMAIL>(call) }
        route.get("backoffice/report/users/csv"){<EMAIL>(call)}

        route.get("backoffice/report/coupons") { <EMAIL>(call) }
        route.get("backoffice/report/coupon-batch/details") { <EMAIL>(call) }

        route.delete("backoffice/report/coupons") { <EMAIL>(call) }
        route.get("backoffice/report/coupons/csv") { <EMAIL>(call) }
        route.get("backoffice/report/coupons/excel") { <EMAIL>(call) }

        route.post("backoffice/coupon") { <EMAIL>(call) }
        route.post("backoffice/partner") { <EMAIL>(call) }
        route.post("backoffice/partner/remove") { <EMAIL>(call) }

        route.get("backoffice/partners") { <EMAIL>(call) }
        route.get("backoffice/products") { <EMAIL>(call) }
        route.get("backoffice/stores") { <EMAIL>(call) }
        route.get("backoffice/coupon/types") { <EMAIL>(call) }
        route.get("backoffice/report/users/countries") { <EMAIL>(call) }


        //-------------------------------------------------------------------------------------------------------------
        route.get("backoffice/report/users") { <EMAIL>(call) }
        route.get("backoffice/report/users/count") { <EMAIL>(call) }
        route.get("backoffice/report/coupon/details") { <EMAIL>(call) }
        route.get("backoffice/report/subscriptions") { <EMAIL>(call) }

        route.put("backoffice/report/coupons"){ <EMAIL>(call); }
    }
}
