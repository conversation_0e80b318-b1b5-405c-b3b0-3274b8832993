package net.kidjo.server.api4.extension.cards

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import net.kidjo.common.models.Folder
import net.kidjo.server.api4.Api4CardsController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.json.v3.*
import net.kidjo.server.shared.tools.RequestUtil
import org.json.JSONArray
import org.json.JSONObject


private const val CARDS_PER_SECTION = 20
private const val MAX_CARDS_PER_CALL = 100
private const val DEFAULT_CARDS_PER_CALL = 24

suspend fun Api4CardsController.listCards(call: ApplicationCall) {

    val acceptLanguageRaw = call.request.acceptLanguage() ?: "us"

    val json = JSONObject()

    val query = call.parameters

    val cardLimit = kotlin.math.min(query.getInt("limit", DEFAULT_CARDS_PER_CALL), MAX_CARDS_PER_CALL)
    val cardOffset = query.getInt("offset", 0)
    val folderType = Folder.ContentType.FromRaw(query.getString("contentType"))
    val kidAge = query.getInt("age", 4)
    val filterFolders = query.getString("excludeFolderIds") as? String ?: ""

    val flag_onlyFolders = true

    val languageId = languageCache.getLanguageIdFromAcceptLanguageHeader(acceptLanguageRaw)
    val countryId = RequestUtil.getCountryFromHeader(databaseController, acceptLanguageRaw) ?: 284

    val cardJSON = JSONArray()
    val soundPerSection = 2
    val mixedGamePerSection = 2
    //sound card at position 1 and at 8 if there are two
    //mixedGamePerSection card at position 9 and 4 if there are two
    var requestCardsAmount = cardLimit
    var requestCardOffset = cardOffset

    if (flag_onlyFolders) {
        val numberOfSoundCardsSoFar = numberOfSoundCardsUpTo(cardOffset, soundPerSection)
        val numberOfMixedGameCardsSoFar = numberOfMixedGameCardsUpTo(cardOffset, mixedGamePerSection)
        val extraCardsSoFar = numberOfSoundCardsSoFar + numberOfMixedGameCardsSoFar
        requestCardOffset -= extraCardsSoFar

        val numberOfSoundCardsAdded =
            numberOfSoundCardsUpTo(cardOffset + cardLimit, soundPerSection) - numberOfSoundCardsSoFar
        val numberOfMixedGameCardsAdded =
            numberOfMixedGameCardsUpTo(cardOffset + cardLimit, mixedGamePerSection) - numberOfMixedGameCardsSoFar
        val otherCardsToBeAdded = numberOfSoundCardsAdded + numberOfMixedGameCardsAdded
        requestCardsAmount -= otherCardsToBeAdded
    }

    val folders = databaseController.folders_getList(
        requestCardsAmount,
        requestCardOffset,
        languageId,
        countryId,
        kidAge,
        folderType,
        filterFolders,
        flag_onlyFolders
    )

    if (flag_onlyFolders) {

        val folderGameCard = createFolderGameCardJson(jsonCreator, databaseController)
        var folderIndex = 0
        for (i in 0 until cardLimit) {
            val sectionPosition = (i + cardOffset) % CARDS_PER_SECTION
            val section = (i + cardOffset) / CARDS_PER_SECTION

            if (sectionPosition == 3) {
                cardJSON.put(jsonCreator.new_cards_soundToJSON(soundPerSection * section))
            }else if (sectionPosition == 12 ) {
                cardJSON.put(jsonCreator.new_cards_soundToJSON(soundPerSection * section + 1))
            } else if (sectionPosition == 7 )
                cardJSON.put(folderGameCard)
            else if (sectionPosition == 18 )
                cardJSON.put(folderGameCard)
            else if (folderIndex < folders.size) {
                val folder = folders[folderIndex]
                cardJSON.put(createFolderVideoCardJson(folder, jsonCreator, databaseController))
                folderIndex++
            } else
                break
        }
    }
    json.put("cards", cardJSON)
    call.respondText(json.toString(), ContentType.Application.Json)
}

fun createFolderGameCardJson(
    jsonCreator: JsonObjectCreatorV3,
    databaseController: DatabaseController
): JSONObject {
    val folder = databaseController.getFolderMixedGameId()!!
    val games = databaseController.game_getByFolder(folder.getLongId())
    return jsonCreator.new_cards_gameFolderToJSON(folder, games)
}

fun createFolderVideoCardJson(
    folder: Folder,
    jsonCreator: JsonObjectCreatorV3,
    databaseController: DatabaseController
): JSONObject {
    val videos = databaseController.videos_getListFromFolder(folder.getLongId())
    return jsonCreator.new_cards_folderNormalToJSON(folder, videos)
}
fun numberOfMixedGameCardsUpTo(position: Int, numberOfMixedGameCardsPerSection: Int): Int {
    val sectionIndex = position / CARDS_PER_SECTION
    val relativePosition = position % CARDS_PER_SECTION

    var numberOfMixedGameCardsSoFar = numberOfMixedGameCardsPerSection * sectionIndex

    if (relativePosition > 7) numberOfMixedGameCardsSoFar++
    if (relativePosition > 18) numberOfMixedGameCardsSoFar++
    return numberOfMixedGameCardsSoFar
}
fun numberOfSoundCardsUpTo(position: Int, numberOfSoundCardsPerSection: Int): Int {
    val sectionIndex = position / CARDS_PER_SECTION
    val relativePosition = position % CARDS_PER_SECTION

    var numberOfSoundCardsSoFar = numberOfSoundCardsPerSection * sectionIndex
    if (relativePosition > 3) numberOfSoundCardsSoFar++
    if (relativePosition > 12) numberOfSoundCardsSoFar++

    return numberOfSoundCardsSoFar
}
