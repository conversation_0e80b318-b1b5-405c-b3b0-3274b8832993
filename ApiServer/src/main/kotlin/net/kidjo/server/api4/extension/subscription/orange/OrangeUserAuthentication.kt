import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.api4.publichers.Api4OrangeController
import net.kidjo.server.shared.extensions.respondOK
import okhttp3.RequestBody
import org.json.JSONObject

/**
 * Retrieve the authentication fot an orange end-user
 */
suspend fun Api4OrangeController.getUserAuthentication(call: ApplicationCall) {
    val accessToken = getOrangeAccessToken()
    if (accessToken != null) {
        /**
         * Add authProcessCallbackURI if we want to redirect the orange user somewhere in Kidjo-TV
         * .put("authProcessCallbackURI", config.orange_user_authentication_auth_call_back_url)
         */
        val jsonBody = JSONObject()
            .put("servicePartner", config.orange_user_authentication_service_partner)

        val request = getBaseRequestBuilder(
            config.orange_user_authentication_url,
            accessToken.merchantAuthToken
        ).post(RequestBody.create(jsonMediaType, jsonBody.toString())).build()
        val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }

        when (response.code()) {
            HttpStatusCode.OK.value -> {
                // Retrieve auth object
                val authResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
                val authResponse = authResponseString?.let {
                    ObjectMapper()
                        .readValue(it,OrangeUserAuth::class.java)
                } ?: return call.respond(HttpStatusCode.NotFound, "Not found an orange user token")
                return call.respondOK(authResponse)
            }
            HttpStatusCode.BadRequest.value -> {
                return call.respond(HttpStatusCode.BadRequest, HttpStatusCode.BadRequest.description)
            }
            HttpStatusCode.Unauthorized.value -> {
                return call.respond(HttpStatusCode.Unauthorized, HttpStatusCode.Unauthorized.description)
            }
            HttpStatusCode.Forbidden.value -> {
                return call.respond(HttpStatusCode.Forbidden, HttpStatusCode.Forbidden.description)
            }
            HttpStatusCode.NotFound.value -> {
                return call.respond(HttpStatusCode.NotFound, HttpStatusCode.NotFound.description)
            }
            HttpStatusCode.InternalServerError.value -> {
                return call.respond(HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError.description)
            }
        }
    }
}

internal data class OrangeUserAuth(val authProcessURI: String, val userAuthToken: String)

