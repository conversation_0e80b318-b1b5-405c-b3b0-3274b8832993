package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.server.routing.*
import net.kidjo.server.api4.extension.coupon.validateCoupon
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import org.slf4j.LoggerFactory

class Api4CouponController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4CouponController")
    internal val api4SubscriptionController = Api4SubscriptionController(platformInjector)
    override fun installRoutes(route: Route) {
        route.post("/coupon/validate") { <EMAIL>(call) }
    }
}
