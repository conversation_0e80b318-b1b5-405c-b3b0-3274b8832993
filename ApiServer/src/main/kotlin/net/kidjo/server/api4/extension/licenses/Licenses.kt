package net.kidjo.server.api4.extension.licenses

import io.ktor.server.application.*
import io.ktor.server.response.*
import net.kidjo.common.models.Language
import net.kidjo.server.api4.Api4LicensesController
import net.kidjo.server.shared.database.countryByCode
import net.kidjo.server.shared.database.foldersByCountryAndLanguage
import net.kidjo.server.shared.database.getFolderById
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.tools.Config

data class PromotedContent(
                          val id: String,
                          val name: String,
                          val description: String,
                          val genre: String,
                          val image: String)

suspend fun Api4LicensesController.licenses(call: ApplicationCall) {
    val languageParam = call.parameters["language"]
    val countryCode = call.parameters["countryCode"]
    val pictureSizeCode = call.parameters["pictureSizeCode"]
    val count = call.parameters["count"]
    val language = languageParam?.toLowerCase()?.let { Language.fromShortName(it) } ?: Language.ENGLISH
    var country = countryCode?.toUpperCase()
    var folder = pictureSizeCode
    var limitNumber = count
    if(country.isNullOrBlank()){
        country = "FR"
    }
    if(folder.isNullOrBlank()){
        folder = "tablet-l"
    }
    if(limitNumber.isNullOrBlank()){
        limitNumber = 100.toString()
    }

    val countryId = databaseController.countryByCode(country.toString())
    try {
        val promotedContent = databaseController
                .foldersByCountryAndLanguage(limitNumber.toInt(), language.id, countryId)
        if(promotedContent == null){
            logger.error("There is no promoted content")
            return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"There are no promoted content")
        }
        var folderUrl=config.folderUrl
        if(config.env == Config.Env.PROD){
            folderUrl=config.prodFolderUrl
        }
        val licenses = promotedContent.map {
            val image = "${folderUrl}newFolderCovers/${folder}/${it.id}.png"
            PromotedContent(it.id, it.title,  it.namedId, it.contentType.raw
                    ?: "", image)
        }
        call.respond(mapOf("licenses" to licenses))
    } catch (e: Throwable) {
        logger.error("Failed to find promoted content", e)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"Problems finding promoted content")
    }
}

suspend fun Api4LicensesController.license(call: ApplicationCall) {
    val id = call.parameters["id"]
    var pictureSizeCode = call.parameters["pictureSizeCode"]
    if(pictureSizeCode.isNullOrBlank()){
        pictureSizeCode = "phone-s"
    }
    try {

        val folder = databaseController
                .getFolderById(id!!.toLong())
        if(folder != null){
            val image = "${config.folderUrl}folderImage/${pictureSizeCode}/${folder.id}.png"
            val license = PromotedContent(folder.id, folder.title,  folder.namedId, folder.contentType.raw ?: "", image)
            return call.respondOK(mapOf("license" to license))
        } else {
            logger.error("There is no promoted content")
            return call.respondNotFound(UsersErrors.ERROR_NOT_EXIST_USER,"There are no promoted content")
        }
    } catch (e: Throwable) {
        logger.error("Failed to find promoted content", e)
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS,"Problems finding promoted content")
    }
}
