package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.deleteAccountCouponsForBatch
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondError
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.deleteCoupons(call: ApplicationCall) {
    val id = call.parameters["id"]
    if (id.isNullOrBlank()) {
        return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "The 'id' parameter is required")
    }
    try {
        databaseController.deleteAccountCouponsForBatch(id)

        call.respondOK(mapOf("data" to id.toLong(), "message" to "Deleted all coupons for id: $id with same batch name. "))
    } catch (e: Throwable) {
        logger.error("An error ocurred while deleting id coupons", e)
        call.respondError(
            statusCode = HttpStatusCode.InternalServerError,
            code = ServerErrors.INTERNAL_ERROR.code,
            errorMessage = "An error ocurred while deleting batch coupons"
        )
    }
}
