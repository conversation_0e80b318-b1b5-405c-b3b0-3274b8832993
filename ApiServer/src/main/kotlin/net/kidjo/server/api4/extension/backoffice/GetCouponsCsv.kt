@file:Suppress("BlockingMethodInNonBlockingContext")

package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.getCsvReportAccountCoupons
import java.io.File
import java.io.PrintWriter

suspend fun Api4BackofficeController.getCouponsCsv(call: ApplicationCall) {
    val id = call.parameters["couponId"]
    try {
        val coupons = databaseController.getCsvReportAccountCoupons(id!!)
        val csvFile = File("coupons.csv")
        val writer = PrintWriter(csvFile)
        val builder = StringBuilder()
        builder.append("Group id, Coupon id, Duration code, Redeemed coupons sum, Coupon Type, Product type, Partner type, Expiration date")
        builder.append("\n\n")
        coupons.forEach { coupon ->
            with(coupon) {
                val csvLine =
                    "$groupId,$couponId,$durationCode,$redeemedTimes,$couponType,$productType,$partnerType,$expireDate"
                builder.append(csvLine)
                builder.append("\n")
            }
        }
        writer.write(builder.toString())
        writer.close()
        call.respondFile(csvFile)
        csvFile.delete()
    } catch (e: Throwable) {
        logger.error("Error while creating coupons csv file", e)
        call.respond(HttpStatusCode.InternalServerError, "Error while creating coupons csv file")
    }
}
