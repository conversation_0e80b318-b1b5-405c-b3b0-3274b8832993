package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.listAllCouponPartner
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.listCouponPartners(call: ApplicationCall) {
    try {
        val partners = databaseController.listAllCouponPartner()
        call.respondOK(partners)
    } catch (e: Throwable) {
        logger.error("Failed to find partners", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding partners")
    }
}
