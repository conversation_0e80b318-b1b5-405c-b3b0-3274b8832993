package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.listAllCouponProducts
import net.kidjo.server.shared.extensions.ServerErrors
import net.kidjo.server.shared.extensions.respondBadRequest
import net.kidjo.server.shared.extensions.respondOK

suspend fun Api4BackofficeController.listCouponProducts(call: ApplicationCall) {
    try {
        val partners = databaseController.listAllCouponProducts()
        call.respondOK(partners)
    } catch (e: Throwable) {
        logger.error("Failed to find products", e)
        call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Problems finding products")
    }
}
