package net.kidjo.server.api4.extension.backoffice

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.response.*
import net.kidjo.server.api4.Api4BackofficeController
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*

suspend fun Api4BackofficeController.getCouponBatchDetails(call: ApplicationCall) {

    val couponId = call.parameters["id"] ?: return call.respondBadRequest(
        ServerErrors.ERROR_BAD_PARAMS,
        "The 'id' coupon is required"
    )
    try {
        val details =
            databaseController.getCouponBatchDetails(couponId) ?: return call.respond(HttpStatusCode.NoContent)

        return call.respondOK(details)
    } catch (e: Throwable) {
        return call.respond(HttpStatusCode.InternalServerError, "Error while finding coupon details")
    }
}
