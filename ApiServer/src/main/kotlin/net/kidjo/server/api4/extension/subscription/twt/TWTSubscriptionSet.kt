package net.kidjo.server.api4.extension.subscription.twt

import io.ktor.server.application.*
import io.ktor.server.auth.*
import net.kidjo.server.api4.publichers.Api4TWTController
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.subscriptionRoot_update
import net.kidjo.server.shared.database.subscriptionTWT_getRecentActive
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*

suspend fun Api4TWTController.subscriptionSet(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }

    val user = call.principal<User>()
    val userId = user?.getLongId() ?: User.NO_SERVER_ID

    val subCode = requestJSON.optString("subscriptionCode")
    val customerId = requestJSON.optString("customerId")
    val expire = requestJSON.optLong("expire")
    val isSubscribed = requestJSON.optBoolean("isSubscribed")
    val productName = requestJSON.optString("productName")
    val carrierName = requestJSON.optString("carrierName")
    val deviceType = requestJSON.optString("deviceType")
    val subToken = customerId+carrierName

    val sub = databaseController.subscriptionTWT_getRecentActive(userId, subToken)
    val subId = sub?.id ?: SubscriptionRoot.NO_ID

    val nextBillDate = expire.epochMilliToLocalDateTime()

    var subType = SubscriptionRoot.SubscriptionType.KIDJO_TV
    when (subCode) {
        "1" -> subType = SubscriptionRoot.SubscriptionType.KIDJO_BOOKS
        "2" -> subType = SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS
    }

    if(sub == null) {
            val inserted = SubscriptionRootInsert(
                        userId = userId,
                        deviceId =  Device.NO_SERVER_ID,
                        isFreeTrail = false,
                        priceToLogUSD = 0f,
                        paymentType = SubscriptionRoot.PaymentType.NATIVE,
                        subscriptionType = subType,
                        paymentId = deviceType,
                        platformPurchaseId = carrierName,
                        storeId = Device.StorePlatform.TWT,
                        paymentStateId = null,
                        iapId = productName,
                        purchasingSessionId = "NONE" ,
                        subscriptionToken= subToken,
                        subscriptionTokenHash=  encryptionController.sha256Hash(subToken),
                        accountCouponId = AccountCoupon.NO_SERVER_ID,
                        isRenewing= isSubscribed,
                        nextBillDate = nextBillDate,
                        isTest = !config.env.isLive
                    )

                    if(databaseController.subscriptionRoot_create(inserted) > 0) {
                        call.respondOK(
                            Success.SUCCESS,
                            "Successful created Subscription with id: ${sub?.id}")
                        return
                    } else {
                        call.respondBadRequest(
                            ServerErrors.ERROR_BAD_PARAMS,
                            "Error create user's Subscription with id: ${sub?.id}" )
                        return
                    }
    } else {
            if(sub.nextBillDate.isBefore(nextBillDate)) {
                val updated =
                    SubscriptionRootUpdate(
                        userId= userId,
                        isRenewing = isSubscribed,
                        nextBillDate = nextBillDate,
                        platformPurchaseId = carrierName,
                        isFreeTrail = false,
                        priceToLogUSD = 0f,
                        subId = subId,
                        paymentStateId = null
                    )

                if(!databaseController.subscriptionRoot_update(updated)) {
                    call.respondBadRequest(
                        ServerErrors.ERROR_BAD_JSON,
                        "Error UPDATING subscription with id: ${sub.id}")
                    return

                } else {
                    call.respondOK(
                        Success.SUCCESS,
                        "Successful UPDATED subscription  with id: ${sub.id} till: $nextBillDate ")
                    return
                }

            }else {
                call.respondNoContent()
                return
            }
    }
}
