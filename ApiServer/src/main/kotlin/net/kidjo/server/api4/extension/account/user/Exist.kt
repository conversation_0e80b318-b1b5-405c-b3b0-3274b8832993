package net.kidjo.server.api4.extension.account.user

import io.ktor.server.application.ApplicationCall
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.shared.database.user_getByEmail
import net.kidjo.server.shared.extensions.*


suspend fun Api4UserController.exist(call: ApplicationCall) {
    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val email = requestJSON.optString("email")
    var exist: Boolean = false
    val currentUser = databaseController.user_getByEmail(email)
    if(currentUser != null) {
        exist = true
    }
    return call.respondOK(mapOf("exist" to exist))
}
