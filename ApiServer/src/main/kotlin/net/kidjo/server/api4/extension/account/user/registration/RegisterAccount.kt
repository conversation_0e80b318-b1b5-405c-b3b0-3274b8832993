package net.kidjo.server.api4.extension.account.user.registration

import io.ktor.server.application.*
import io.ktor.server.request.*
import net.kidjo.server.api4.UserDtoOut
import net.kidjo.server.api4.RegisterWebDtoIn
import net.kidjo.server.api4.Api4UserController
import net.kidjo.server.api4.extension.account.user.helpers.createUser
import net.kidjo.server.api4.extension.subscription.helper.createFreeAccessSubscription
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.User

suspend fun Api4UserController.registerAccount(call: ApplicationCall) {

    val registerWebIn = call.receive<RegisterWebDtoIn>()

    if (registerWebIn.email.isNullOrEmpty() || registerWebIn.password.isNullOrEmpty()) {
        return call.respondBadRequest("Bad Params")
    }

    if (!validator.isEmailValid(registerWebIn.email)) {
        return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
    }

    val currentUser = databaseController.user_getByEmail(registerWebIn.email)
    if (currentUser != null) {
        return call.respondBadRequest(UsersErrors.ERROR_EXIST_USER, "There is already such a User")
    }
    val user = User.getEmptyUser()

    user.authType = User.AuthType.EMAIL
    user.email = registerWebIn.email
    user.name = registerWebIn.displayName!!
    user.hashedPassword = encryptionController.hashPassword(registerWebIn.password)
    user.countryId = countryCodeByGoogle.getCountryIdByIP(call)

    val couponDto = getUserCouponDto(registerWebIn.coupon)
    if (registerWebIn.coupon!!.isNotBlank()) {
        val coupon = couponManager.getFromDatabase(registerWebIn.coupon)
        if (coupon == null || !coupon.isValid()) {
            logger.error("Failed to register user")
            return call.respondBadRequest(UsersErrors.ERROR_INVALID_COUPON, "This coupon is invalid or expired.")
        } else {
            user.id = createUser(user, call)
            user.authToken = jwtManager.generateAccessToken(user.id)
            braintreeManager.createCustomer(user)
            api4SubscriptionController.createFreeAccessSubscription(coupon, user.getLongId(), call)

            return call.respondOK(
                UserDtoOut(
                    user.id,
                    couponDto,
                    false,
                    user.authToken
                )
            )
        }
    } else {
        user.id = createUser(user, call)
        user.authToken = jwtManager.generateAccessToken(user.id)
        braintreeManager.createCustomer(user)
        val hasSubscription = linkExternalUserSubscription(registerWebIn.externalPartner, user.getLongId())

        return call.respondOK(
            UserDtoOut(
                user.id,
                couponDto,
                hasSubscription,
                user.authToken
            )
        )
    }
}
