package net.kidjo.server.api4

import io.ktor.server.application.*
import io.ktor.http.*
import io.ktor.server.routing.*
import net.kidjo.server.main.BaseServerController
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.newsletterGetByEmail
import net.kidjo.server.shared.database.newsletterSetEmail
import net.kidjo.server.shared.extensions.*
import org.slf4j.LoggerFactory

class Api4NewsLetterController (platformInjector: PlatformInjector) : BaseServerController(platformInjector){
    internal val logger = LoggerFactory.getLogger("Api4NewsLetterController")
    override fun installRoutes(route: Route) {
        route.post("/newsletters") { setEmail(call) }
    }

    private suspend fun setEmail(call: ApplicationCall) {
        val requestJSON = call.receiveJSON()
        if (requestJSON == null) {
            call.respondBadJSON()
            return
        }
        val email = requestJSON.optString("email")

        if (!requestJSON.has("email")) {
            return call.respondBadRequest( "Bad Params")
        }

        if (!validator.isEmailValid(email)) {
            return call.respondBadRequest(UsersErrors.ERROR_BAD_EMAIL, "Wrong email.")
        }
        val hasEmail = databaseController.newsletterGetByEmail(email)
        if(!hasEmail.isNullOrEmpty()){
            return call.respondError(HttpStatusCode.Conflict, 4, "There is such an email address.")
        }

        val success = databaseController.newsletterSetEmail(email)
        if(!success) {
            return call.respondDatabaseIssue()
        }
        return call.respondOK(Success.SUCCESS, "Successful created email")
    }
}
