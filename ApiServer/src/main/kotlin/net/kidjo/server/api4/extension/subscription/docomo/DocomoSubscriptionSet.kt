package net.kidjo.server.api4.extension.subscription.docomo

import io.ktor.server.application.*
import io.ktor.server.auth.*
import net.kidjo.server.api4.publichers.Api4DocomoController
import net.kidjo.server.shared.database.subscriptionDocomo_getRecentActive
import net.kidjo.server.shared.database.subscriptionRoot_create
import net.kidjo.server.shared.database.subscriptionRoot_update
import net.kidjo.server.shared.extensions.*
import net.kidjo.server.shared.models.*

suspend fun Api4DocomoController.subscriptionSet(call: ApplicationCall) {
    val device = call.getDeviceFromHeader(encryptionController)

    if (!device.isDeviceIdValid) {
        call.respond404()
        return
    }

    val requestJSON = call.receiveJSON()
    if (requestJSON == null) {
        call.respondBadJSON()
        return
    }
    val user = call.principal<User>()

    if (!requestJSON.has("expire")) {
        call.respondBadParameters()
        return
    }

    val expiredDate = requestJSON.optString("expire")
    val isSubscribed = requestJSON.optBoolean("isSubscribed")
    val productName = requestJSON.optString("productName")
    val carrierName = requestJSON.optString("carrierName")
    val deviceType = requestJSON.optString("deviceType")
    // is_empty = kidjo-tv, 0 = kidjo-tv, 1 = kidjo-books, 2 = kidjo-tv and kidjo-books
    val subscriptionCode = requestJSON.optString("subscriptionCode")

    var subscriptionType: SubscriptionRoot.SubscriptionType
    if (subscriptionCode.isNullOrEmpty() || subscriptionCode == "0") {
        subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV
    } else if(subscriptionCode == "1") {
        subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_BOOKS
    } else {
        subscriptionType = SubscriptionRoot.SubscriptionType.KIDJO_TV_BOOKS
    }

    if(user != null) {
        val toEpoch = expiredDate + "000"

        val expiredDate = toEpoch.toLong().epochMilliToLocalDateTime()

        val checkSubscription: SubscriptionRoot? = databaseController.subscriptionDocomo_getRecentActive(user.getLongId())

        if(checkSubscription == null) {
                val subscriptionRootInsert = SubscriptionRootInsert(user.getLongId(), 0L, false, 0f,
                        SubscriptionRoot.PaymentType.NATIVE, subscriptionType, deviceType, carrierName, Device.StorePlatform.DOCOMO, null, productName,
                        "none", "none", "none", 0L, isSubscribed, expiredDate, !config.env.isLive)
                val id = databaseController.subscriptionRoot_create(subscriptionRootInsert)
                if (id > 0) {
                    return call.respondOK(Success.SUCCESS, "Successful created Subscription to User id: ${subscriptionRootInsert.userId} ")
                } else {
                    logger.error("Error create user's subscription: ${subscriptionRootInsert.userId}")
                    return call.respondBadRequest(ServerErrors.ERROR_BAD_PARAMS, "Error create user's subscription: ${subscriptionRootInsert.userId}")
                }
        } else {

            if(checkSubscription.nextBillDate.isBefore(expiredDate)) {
                val subscriptionRootUpdate = SubscriptionRootUpdate(user.getLongId(), isSubscribed,
                        expiredDate, carrierName,
                        false, 0f, checkSubscription.id, null)

                val checkUpdate = databaseController.subscriptionRoot_update(subscriptionRootUpdate);
                if (!checkUpdate) {
                    logger.error("Error update user's subscription: ${subscriptionRootUpdate.userId}")
                    return call.respondBadRequest(ServerErrors.ERROR_BAD_JSON,"Error update user's subscription: ${subscriptionRootUpdate.userId}")
                } else {
                    return call.respondOK(Success.SUCCESS,"Successful updated subscription to user id: ${subscriptionRootUpdate.userId} ")
                }
            }else{
                return call.respondNoContent()
            }
        }

    }
}
