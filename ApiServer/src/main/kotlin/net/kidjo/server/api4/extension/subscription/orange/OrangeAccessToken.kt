
import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.kidjo.server.api4.publichers.Api4OrangeController
import okhttp3.Request
import okhttp3.RequestBody
import org.json.JSONObject

/**
 * Retrieve an JWT for the Orange API
 */
suspend fun Api4OrangeController.getOrangeAccessToken(): OrangeToken? {
    var authResponse: OrangeToken? = null
    val jsonBody = JSONObject()
        .put("email", config.orange_kidjo_authorization_dev_user)
        .put("password", config.orange_kidjo_authorization_dev_password)
    val request = Request.Builder().url(config.orange_kidjo_authorization_url)
        .post(RequestBody.create(jsonMediaType, jsonBody.toString())).build()
    val response = withContext(Dispatchers.IO) { okHttpClient.newCall(request).execute() }

    when (response.code()) {
        HttpStatusCode.OK.value -> {
            val authResponseString = withContext(Dispatchers.IO) { response.body()?.string() }
            authResponse = authResponseString?.let {
                return ObjectMapper().readValue(it,OrangeToken::class.java)
            }
            return authResponse

        }
        HttpStatusCode.BadRequest.value -> {
            logger.info("Response BadRequest")
        }
        HttpStatusCode.Unauthorized.value -> {
            logger.info("Response Unauthorized")
        }
        HttpStatusCode.InternalServerError.value -> {
            logger.info("Response InternalServerError")
        }
    }
    return authResponse
}

data class OrangeToken(val merchantAuthToken: String, val merchantRefreshToken: String)
