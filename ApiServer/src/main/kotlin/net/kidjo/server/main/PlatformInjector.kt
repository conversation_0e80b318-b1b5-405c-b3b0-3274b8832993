package net.kidjo.server.main

import net.kidjo.server.shared.tools.injector.SharedInjector

class PlatformInjector(val sharedInjector: SharedInjector,
                       val platformConfig: PlatformConfig) {
    companion object {
        fun Build(sharedInjector: SharedInjector, platformConfig: PlatformConfig): PlatformInjector {
            return PlatformInjector(sharedInjector, platformConfig)
        }
    }
}