package net.kidjo.server.main

import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.defaultheaders.*
import io.ktor.server.plugins.forwardedheaders.*
import net.kidjo.plugins.FlywayFeature
import net.kidjo.plugins.Migrate
import net.kidjo.plugins.Repair
import net.kidjo.server.plugins.*
import net.kidjo.server.shared.tools.Config
import java.util.*

fun main(args: Array<String>) {
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
    val envString = args[0]
    val config = Config(Config.AppTypeId.SERVER, Config.Env.FromString(envString))
    val port = args.getOrNull(1)?.toIntOrNull() ?: config.port

    embeddedServer(Netty, port) {
        configureLogging()
        install(DefaultHeaders) {
            if (config.websiteHotReload) header("Cache-Control", "no-cache")
        }
        configureCors()
        configureCompression()
        configureSerialization()
        configureRouting(args)
        install(ForwardedHeaders)
        install(XForwardedHeaders) {
            useFirstProxy()
        }
        configureStatusPage()
        install(FlywayFeature) {
            locations = config.flywayLocations
            host = config.db_host
            dbName = config.db_name
            user = config.db_user
            password = config.db_password
            baselineOnMigrate = true
            outOfOrderMigration = !config.env.isLive
            commands(Repair, Migrate)
        }

    }.start(wait = true)
}

