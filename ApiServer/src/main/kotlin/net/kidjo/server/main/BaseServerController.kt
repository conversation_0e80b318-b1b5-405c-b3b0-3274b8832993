package net.kidjo.server.main

import io.ktor.server.routing.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
abstract class BaseServerController(platformInjector: PlatformInjector) {
    companion object{
        val logger: Logger =LoggerFactory.getLogger(this::class.java.name)
    }
    val config = platformInjector.sharedInjector.config
    val platformConfig = platformInjector.platformConfig
    val utility = platformInjector.sharedInjector.utility

    val databaseController = platformInjector.sharedInjector.databaseController
    val cacheDatabase = platformInjector.sharedInjector.cacheDatabase
    val encryptionController = platformInjector.sharedInjector.encryptionController

    val validator = platformInjector.sharedInjector.validator
    val languageCache = platformInjector.sharedInjector.languageCache
    val countryCodeByGoogle = platformInjector.sharedInjector.countryCodeByGoogle

    val googleApiManager = platformInjector.sharedInjector.playStoreApiManager
    val digitalvirgoApiManager = platformInjector.sharedInjector.digitalVirgoApiManager
    val mondiaPayManager = platformInjector.sharedInjector.mondiaPayManager
    val braintreeManager = platformInjector.sharedInjector.braintreeApiManager
    val paymentAppStoreManager = platformInjector.sharedInjector.paymentAppStoreManager
    val paymentPlayStoreManager = platformInjector.sharedInjector.paymentPlayStoreManager
    val paymentSamsungStoreManager = platformInjector.sharedInjector.paymentSamsungStoreManager
    val paymentHuaweiStoreManager = platformInjector.sharedInjector.paymentHuaweiStoreManager
    val paymentOrangeStoreManager = platformInjector.sharedInjector.paymentOrangeStoreManager
    val paymentAmazonStoreManager = platformInjector.sharedInjector.paymentAmazonStoreManager
    val paymentJioStoreManager = platformInjector.sharedInjector.paymentJioStoreManager

    val paymentManager = platformInjector.sharedInjector.paymentManager
    val iapManager = platformInjector.sharedInjector.iapManager
    val ruleManager = platformInjector.sharedInjector.rulesManager

    val emailManager = platformInjector.sharedInjector.emailManager
    val userManager = platformInjector.sharedInjector.userManager

    val jwtManager = platformInjector.sharedInjector.jwtManager

    internal val couponManager = platformInjector.sharedInjector.couponManager

    val cafeynApiManager=platformInjector.sharedInjector.cafeynApiManager

    abstract fun installRoutes(route: Route)
}
