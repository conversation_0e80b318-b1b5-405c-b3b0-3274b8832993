plugins {
    id 'application'
    id "org.jetbrains.kotlin.jvm" version "1.9.0" apply true
    id("io.ktor.plugin") version "2.3.3" apply true
}

group 'ApiServer'
version '1.0-SNAPSHOT'


repositories {
    mavenCentral()
}

configurations {
    closureCompiler
}

ktor {
    fatJar {
        archiveFileName.set("${API_SERVER_JAR_NAME}.jar")
    }
}
dependencies {
    implementation project(":SharedServer")

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"

    implementation "ch.qos.logback:logback-classic:$logback_version"
    implementation "com.papertrailapp:logback-syslog4j:1.0.0"
    implementation "com.samskivert:jmustache:$jmustache_version"
    implementation "com.squareup.okhttp3:okhttp:3.14.0"

    implementation "com.braintreepayments.gateway:braintree-java:$brain_tree_version"
    implementation "com.google.api-client:google-api-client:$google_api_version"

    implementation "com.amazonaws:aws-java-sdk-ses:$aws_library_verion"

    implementation "io.ktor:ktor-server-core:$ktor_version"
    implementation "io.ktor:ktor-server-netty:$ktor_version"
    implementation "io.ktor:ktor-server-content-negotiation:$ktor_version"
    implementation "io.ktor:ktor-serialization-jackson:$ktor_version"
    implementation "io.ktor:ktor-server-auth:$ktor_version"
    implementation "io.ktor:ktor-server-auth-jwt:$ktor_version"
    implementation "io.ktor:ktor-server-cors:$ktor_version"
    implementation "io.ktor:ktor-server-caching-headers:$ktor_version"
    implementation "io.ktor:ktor-server-default-headers:$ktor_version"
    implementation "io.ktor:ktor-server-auto-head-response:$ktor_version"
    implementation "io.ktor:ktor-server-compression:$ktor_version"
    implementation "io.ktor:ktor-server-status-pages:$ktor_version"
    implementation "io.ktor:ktor-server-compression:$ktor_version"
    implementation "io.ktor:ktor-server-forwarded-header:$ktor_version"
    implementation "io.ktor:ktor-server-swagger:$ktor_version"

    implementation "org.flywaydb:flyway-core:$flyway_version"
    implementation 'io.ktor:ktor-server-call-logging-jvm:2.3.3'
    implementation 'io.ktor:ktor-server-call-id-jvm:2.3.3'
    implementation 'io.ktor:ktor-server-auth-jvm:2.3.3'
    implementation 'io.ktor:ktor-server-core-jvm:2.3.3'
    implementation 'io.ktor:ktor-client-cio-jvm:2.3.3'

    implementation 'io.ktor:ktor-client-content-negotiation:2.3.3'

    closureCompiler "com.google.javascript:closure-compiler:$closure_js_compiler_version"

    implementation("com.fasterxml.jackson.core:jackson-databind:$jackson_version")

    implementation("com.fasterxml.jackson.core:jackson-annotations:2.12.4")
    implementation group: 'com.fasterxml.jackson.datatype', name: 'jackson-datatype-jsr310', version: '2.12.5'

    implementation "org.apache.poi:poi:5.0.0"
    implementation("org.apache.poi:poi-ooxml:5.0.0")

    implementation "org.jetbrains.exposed:exposed-core:0.41.1"
    implementation "org.jetbrains.exposed:exposed-dao:0.41.1"
    implementation "org.jetbrains.exposed:exposed-jdbc:0.41.1"
    implementation "org.jetbrains.exposed:exposed-java-time:0.41.1"

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.13.0")
}
application {
    mainClassName = 'net.kidjo.server.main.StartKt'
}

def getLastCommitSHA() {
    def process = "git rev-parse --short HEAD".execute()
    def gitHash = process.text.trim()
    process.waitFor()
    return gitHash ?: "Unknown"
}

tasks.register('run_local', JavaExec) {
    mainClass = 'net.kidjo.server.main.StartKt'
    classpath = sourceSets.main.runtimeClasspath
    args = ["local"]
}


defaultTasks 'run_local'

def getLatestTag() {
    def process = "git describe --tags --abbrev=0".execute()
    def latestTag = process.text.trim()
    process.waitFor()
    return latestTag ?: "Unknown"
}

def generateVersionInfo() {
    def commitSHA = getLastCommitSHA()
    def latestTag = getLatestTag()

    def versionProperties = new File("${projectDir}/src/main/resources/version.properties")
    versionProperties.text = "commitSHA=$commitSHA\nlatestTag=$latestTag"
}

tasks.register('generateVersionInfo') {
    generateVersionInfo()
}
tasks.getByName("shadowJar").dependsOn("generateVersionInfo")
