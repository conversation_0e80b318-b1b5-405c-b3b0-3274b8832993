#eval "$(aws ecr get-login --no-include-email --region us-east-1)"
#
#docker build -t kidjo-background ./background/
#docker tag kidjo-background:latest 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-background:latest
#docker push 775893069523.dkr.ecr.us-east-1.amazonaws.com/kidjo-background:latest
#
#echo "Done pushing into background registry"


scp -i ./Kidjo.pem ./background/backgroundProcess.jar <EMAIL>:./cronjob