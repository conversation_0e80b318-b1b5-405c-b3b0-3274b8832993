package net.kidjo.server.main

import net.kidjo.server.main.tasks.subscriptions.SubscriptionTask
import net.kidjo.server.shared.tools.Config
import net.kidjo.server.shared.tools.injector.SharedInjector
import java.util.*


fun main(args: Array<String>) {
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))

    val envString = args[0]

    val config = Config(Config.AppTypeId.BACKGROUND, Config.Env.FromString(envString))

    val sharedInjector = SharedInjector.Build(config)
    val platformInjector = PlatformInjector.Build(sharedInjector, BackgroundConfig(config))

    val subscriptionTask = SubscriptionTask(platformInjector)

    if (!subscriptionTask.results.isError)
        subscriptionTask.start()

}
