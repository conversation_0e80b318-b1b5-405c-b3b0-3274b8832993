package net.kidjo.server.main.tasks.subscriptions

import net.kidjo.server.main.BaseTask
import net.kidjo.server.main.PlatformInjector
import net.kidjo.server.shared.database.*
import net.kidjo.server.shared.models.*

class SubscriptionTask(platformInjector: PlatformInjector) :
    BaseTask(BackgroundTaskLog.TaskType.PROCESS_SUBSCRIPTION, platformInjector) {
    private val amountOfSubscriptionsPerFetch = 10
    private val amountOfSubscriptionsCheckRoutines = 10

    private var amountChecked = 0
    private var amountUpdated = 0
    override fun start() {

        logger.info("[SubscriptionTask.start] Job Subscription Update Starting...")

        databaseController.subscription_getListSortByDateWithProcess(amountOfSubscriptionsPerFetch) { subscriptions ->
            subscriptions.forEach {
                logger.info("[SubscriptionTask.subscription_getListSortByDateWithProcess] Job Subscription Update for sub: $it")
                amountChecked++
                val operation = SubscriptionOperation(it, databaseController, paymentManager, config)
                val operationResult = operation.runOperation()
                if (operationResult.levelToLog > results.levelToLog) results = operationResult
                logMessageBuffer.append(operation.stringBuilder.toString())
                if (operation.updatedSubscription) {
                    logUpdateMessageBuffer.append(operation.updatedStringBuilder.toString())
                    logCancelMessageBuffer.append(operation.cancelStringBuilder.toString())
                    amountUpdated++
                }
            }
        }

        if (results == BackgroundTaskLog.Results.RUNNING) results = BackgroundTaskLog.Results.SUCCESS

        logShortMessageBuffer.append("Checked: $amountChecked -\n")
        logShortMessageBuffer.append("Updated: $amountUpdated -\n")
        end()
    }
}