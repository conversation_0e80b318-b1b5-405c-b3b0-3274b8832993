package net.kidjo.server.main.tasks.subscriptions

import com.braintreegateway.exceptions.NotFoundException
import com.braintreegateway.exceptions.UnexpectedException
import com.google.api.client.googleapis.json.GoogleJsonResponseException
import net.kidjo.server.shared.database.DatabaseController
import net.kidjo.server.shared.database.subscriptionTransaction_add
import net.kidjo.server.shared.database.subscription_cancelFull
import net.kidjo.server.shared.database.subscription_updateNextBillingDateAndRemoveFreeTrial
import net.kidjo.server.shared.models.*
import net.kidjo.server.shared.tools.payments.PaymentManager
import net.kidjo.server.shared.payments.background.checkSubscriptionStatus
import net.kidjo.server.shared.tools.Config
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

class SubscriptionOperation(
    val subscriptionRoot: SubscriptionRoot,
    val databaseController: DatabaseController,
    val paymentManager: PaymentManager,
    val config: Config
) {
    internal val LOGGER=LoggerFactory.getLogger("SubscriptionOperation");
    var isRunning = false
        private set
    var isFinished = false
        private set
    var updatedSubscription = false
        private set
    val stringBuilder = StringBuilder()
    val updatedStringBuilder = StringBuilder()
    val cancelStringBuilder = StringBuilder()


    fun runOperation(): BackgroundTaskLog.Results {

        val now = LocalDateTime.now()
        if (subscriptionRoot.isActive && !subscriptionRoot.isRenewing) {
            if (now.isAfter(subscriptionRoot.nextBillDate)) {

                LOGGER.info("[SubscriptionOperation.runOperation] Subscription Ends for Sub record: $subscriptionRoot")
                //subscription ends
                //no transaction is stored because transactions are only for renewing subscriptions
                val success = databaseController.subscription_cancelFull(subscriptionRoot.id)
                if (!success) {
                    stringBuilder.appendln("Issue DEACTIVATE Subscription id: ${subscriptionRoot.id}")
                    return BackgroundTaskLog.Results.NON_FATAL_ERROR
                }
                cancelStringBuilder.appendln(
                    "DEACTIVATE BEFORE VALIDATION = ${subscriptionRoot.storeId.raw} SUBSCRIPTION id: ${subscriptionRoot.id}, OLD billing date:${subscriptionRoot.nextBillDate}"
                )
            }

            return BackgroundTaskLog.Results.SUCCESS
        }


        if (now.isAfter(subscriptionRoot.nextBillDate)) {
            //time to process the subscription
            //first check subscription
            val subscriptionCheckResult: SubscriptionCheckResult
            try {
                subscriptionCheckResult =
                    paymentManager.checkSubscriptionStatus(subscriptionRoot, updatedStringBuilder, cancelStringBuilder)
            } catch (e: GoogleJsonResponseException) {
                LOGGER.error("[SubscriptionOperation.runOperation] Subscription id in google exception- ${subscriptionRoot.id} | Error $e")
                stringBuilder.appendln("Issue Android: ${e.details.message} -with Subscription id: ${subscriptionRoot.id}")
                return BackgroundTaskLog.Results.NON_FATAL_ERROR
            } catch (e: SubscriptionCheckException) {
                LOGGER.error("[SubscriptionOperation.runOperation]Issue : $e -with Subscription id: ${subscriptionRoot.id}")
                stringBuilder.appendln("Issue : ${e.message} -with Subscription id: ${subscriptionRoot.id}")
                return BackgroundTaskLog.Results.NON_FATAL_ERROR
            } catch (e: NotFoundException) {
                stringBuilder.appendln("Issue Braintree: Not found Braintree Customer -with Subscription id: ${subscriptionRoot.id}")
                return BackgroundTaskLog.Results.NON_FATAL_ERROR
            } catch (e: UnexpectedException) {
                stringBuilder.appendln("Issue Braintree: Unexpected Exception in Subscription id: ${subscriptionRoot.id}")
                return BackgroundTaskLog.Results.NON_FATAL_ERROR
            } catch (e: SamsungSubscriptionCheckException) {
                stringBuilder.appendln("Issue SAMSUNG: Unexpected Exception in SAMSUNG Subscription id: ${subscriptionRoot.id}")
                return BackgroundTaskLog.Results.NON_FATAL_ERROR
            }catch (e: Exception){
                stringBuilder.append("%n Issue Unknown: Unexpected Exception in Subscription id :${subscriptionRoot.id} with exception ${e.message}")
                return BackgroundTaskLog.Results.NON_FATAL_ERROR
            }

            var results: BackgroundTaskLog.Results = BackgroundTaskLog.Results.SUCCESS
            when (subscriptionCheckResult.status) {
                SubscriptionCheckResult.Status.ACTIVE -> {
                    //successfully charged subscription
                    //create a transaction
                    //if subscription was under free trial or coupon log that transaction
                    if (subscriptionRoot.stillInFreeTrial) {
                        val transactionType =
                            if (subscriptionRoot.accountCouponId > 0L) SubscriptionTransaction.TransactionType.COUPON_END else SubscriptionTransaction.TransactionType.FREE_TRIAL_END
                        val success =
                            databaseController.subscriptionTransaction_add(subscriptionRoot.id, transactionType, 0f)
                        if (!success) {
                            results = BackgroundTaskLog.Results.NON_FATAL_ERROR
                            stringBuilder.appendln("Issue added end of free trial for Subscription id: ${subscriptionRoot.id} --and-- with results $subscriptionCheckResult")
                            stringBuilder.appendln(" --and-- with results $subscriptionCheckResult")
                        }
                    }

                    var success = databaseController.subscriptionTransaction_add(
                        subscriptionRoot.id,
                        SubscriptionTransaction.TransactionType.RENEW,
                        subscriptionCheckResult.priceOfLastSubUSD
                    )
                    if (!success) {
                        results = BackgroundTaskLog.Results.NON_FATAL_ERROR
                        stringBuilder.appendln("Issue adding renew transaction for Subscription id: ${subscriptionRoot.id} --and-- with results $subscriptionCheckResult")
                        stringBuilder.appendln(" --and-- with results $subscriptionCheckResult")
                    }
                    success = databaseController.subscription_updateNextBillingDateAndRemoveFreeTrial(
                        subscriptionRoot.id,
                        //subscriptionCheckResult.nextBillDate
                        subscriptionCheckResult.nextBillDate.withHour(23).withMinute(59),
                        subscriptionCheckResult.paymentStateId
                    )
                    if (!success) {
                        results = BackgroundTaskLog.Results.NON_FATAL_ERROR
                        stringBuilder.appendln("Issue UPDATING Subscription id: ${subscriptionRoot.id}")
                        stringBuilder.appendln(" --and-- with results $subscriptionCheckResult")
                    }
                    updatedSubscription = true
                    //success
                }
                SubscriptionCheckResult.Status.PENDING -> {
                    //Wait for the next day to check if subscription is processed
                }
                SubscriptionCheckResult.Status.CANCEL_FAILED_PAYMENT, SubscriptionCheckResult.Status.CANCEL_USER, SubscriptionCheckResult.Status.CANCEL_ADMIN, SubscriptionCheckResult.Status.CANCEL_EXPIRED -> {
                    //Payment has stopped
                    //cancel subscription
                    val cancelReason: SubscriptionTransaction.TransactionType
                    try {
                        cancelReason = SubscriptionCheckResult.Status.GetCancelReason(subscriptionCheckResult.status)
                    } catch (e: GoogleJsonResponseException) {
                        stringBuilder.appendln("Issue CANCELING Android: ${e.details.message} -with Subscription id: ${subscriptionRoot.id}")
                        return BackgroundTaskLog.Results.NON_FATAL_ERROR
                    } catch (e: SubscriptionCheckException) {
                        stringBuilder.appendln("Issue CANCELING: ${e.message} -with Subscription id: ${subscriptionRoot.id}")
                        return BackgroundTaskLog.Results.NON_FATAL_ERROR
                    } catch (e: NotFoundException) {
                        stringBuilder.appendln("Issue CANCELING Braintree: Not found Braintree Customer -with Subscription id: ${subscriptionRoot.id}")
                        return BackgroundTaskLog.Results.NON_FATAL_ERROR
                    } catch (e: SamsungSubscriptionCheckException) {
                        stringBuilder.appendln("Issue CANCELING SAMSUNG: Not found SAMSUNG Subscription id: ${subscriptionRoot.id}")
                        return BackgroundTaskLog.Results.NON_FATAL_ERROR
                    }

                    databaseController.subscriptionTransaction_add(subscriptionRoot.id, cancelReason, 0f)
                    val success = databaseController.subscription_cancelFull(subscriptionRoot.id)
                    if (!success) {
                        results = BackgroundTaskLog.Results.NON_FATAL_ERROR
                        stringBuilder.appendln("Issue ENDING Subscription id: ${subscriptionRoot.id}")
                    }

                    updatedSubscription = true
                }
            }
            return results
        } else {
            return BackgroundTaskLog.Results.SUCCESS
        }
    }

}
