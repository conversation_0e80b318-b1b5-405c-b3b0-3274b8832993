package net.kidjo.server.main

import net.kidjo.server.shared.tools.injector.SharedInjector


class PlatformInjector(
    val sharedInjector: SharedInjector,
    val backgroundConfig: BackgroundConfig
) {
    companion object {
        fun Build(sharedInjector: SharedInjector, backgroundConfig: BackgroundConfig): PlatformInjector {
            return PlatformInjector(sharedInjector, backgroundConfig)
        }
    }
}