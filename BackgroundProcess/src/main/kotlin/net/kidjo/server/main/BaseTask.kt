package net.kidjo.server.main

import net.kidjo.server.shared.database.backgroundTask_finishLog
import net.kidjo.server.shared.database.backgroundTask_startLog
import net.kidjo.server.shared.models.BackgroundTaskLog
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

abstract class BaseTask(val taskType: BackgroundTaskLog.TaskType, platformInjector: PlatformInjector) {
    internal val config = platformInjector.sharedInjector.config
    internal val backgroundConfig = platformInjector.backgroundConfig

    internal val utility = platformInjector.sharedInjector.utility

    internal val databaseController = platformInjector.sharedInjector.databaseController
    internal val cacheDatabase = platformInjector.sharedInjector.cacheDatabase
    internal val encryptionController = platformInjector.sharedInjector.encryptionController

    internal val validator = platformInjector.sharedInjector.validator
    internal val languageCache = platformInjector.sharedInjector.languageCache

    internal val googleApiManager = platformInjector.sharedInjector.playStoreApiManager
    internal val paymentManager = platformInjector.sharedInjector.paymentManager
    internal val iapManager = platformInjector.sharedInjector.iapManager
    internal val ruleManager = platformInjector.sharedInjector.rulesManager

    internal val emailManager = platformInjector.sharedInjector.emailManager
    internal val userManager = platformInjector.sharedInjector.userManager

    internal val couponManager = platformInjector.sharedInjector.couponManager

    internal val taskId: Long
    var results: BackgroundTaskLog.Results = BackgroundTaskLog.Results.RUNNING
        internal set
    internal val logMessageBuffer = StringBuffer()
    internal val logShortMessageBuffer = StringBuffer()
    internal val logUpdateMessageBuffer = StringBuffer()
    internal val logCancelMessageBuffer = StringBuffer()
    internal val logger: Logger

    init {
        taskId = databaseController.backgroundTask_startLog(backgroundConfig.version, taskType)
        if (taskId != BackgroundTaskLog.NO_ID) {
            logMessageBuffer.appendln("STARTED task ${taskType.raw} - $taskId")
            logShortMessageBuffer.appendln("STARTED task ${taskType.raw} - $taskId")
            logUpdateMessageBuffer.appendln("UPDATED task ${taskType.raw} - $taskId")
            logCancelMessageBuffer.appendln("CANCELLED task ${taskType.raw} - $taskId")
            logger = LoggerFactory.getLogger("BACKGROUND Task ${taskType.raw} - $taskId")
        } else {
            results = BackgroundTaskLog.Results.FATAL_ERROR
            logger = LoggerFactory.getLogger("BACKGROUND Task ${taskType.raw} - $taskId")
            logger.error("Some database error")
            println("Database error creating Background Task ${taskType.raw} - $taskId")
        }
    }

    abstract fun start()
    internal fun end() {
        val longMessage = logMessageBuffer.toString()
        val shortMessage = logShortMessageBuffer.toString()
        val updateMessage = logUpdateMessageBuffer.toString()
        val cancelMessage = logCancelMessageBuffer.toString()

        databaseController.backgroundTask_finishLog(
            taskId,
            results,
            shortMessage,
            longMessage,
            updateMessage,
            cancelMessage,
            LocalDateTime.now()
        )
    }
}
