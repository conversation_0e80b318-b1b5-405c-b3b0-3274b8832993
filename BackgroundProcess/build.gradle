plugins {
    id 'application'
    id "org.jetbrains.kotlin.jvm" version "1.9.0" apply true
    id "io.ktor.plugin" version "2.3.3" apply true
}

group 'BackgroundProcess'
version '1.0-SNAPSHOT'


application {
    mainClassName = 'net.kidjo.server.main.StartKt'
}

ktor {
    fatJar {
        archiveFileName.set("${BACKGROUND_PROCESS_JAR_NAME}.jar")
    }
}

dependencies {
    implementation project(":SharedServer")
    implementation "ch.qos.logback:logback-classic:$logback_version"
    implementation "com.braintreepayments.gateway:braintree-java:$brain_tree_version"
    implementation "com.google.api-client:google-api-client:$google_api_version"
    implementation "com.amazonaws:aws-java-sdk-ses:$aws_library_verion"
}
def getLatestTag() {
    def process = "git describe --tags --abbrev=0".execute()
    def latestTag = process.text.trim()
    process.waitFor()
    return latestTag ?: "Unknown"
}


def getLastCommitSHA() {
    def process = "git rev-parse --short HEAD".execute()
    def gitHash = process.text.trim()
    process.waitFor()
    return gitHash ?: "Unknown"
}

def generateVersionInfo() {
    def commitSHA = getLastCommitSHA()
    def latestTag = getLatestTag()

    def versionProperties = new File("${projectDir}/src/main/resources/version.properties")
    versionProperties.text = "commitSHA=$commitSHA\nlatestTag=$latestTag"
}


tasks.register('generateVersionInfo') {
    generateVersionInfo()
}

tasks.getByName("shadowJar").dependsOn("generateVersionInfo")
